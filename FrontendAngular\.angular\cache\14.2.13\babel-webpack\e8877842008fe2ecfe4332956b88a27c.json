{"ast": null, "code": "export const config = {\n  onUnhandledError: null,\n  onStoppedNotification: null,\n  Promise: undefined,\n  useDeprecatedSynchronousErrorHandling: false,\n  useDeprecatedNextContext: false\n};", "map": {"version": 3, "names": ["config", "onUnhandledError", "onStoppedNotification", "Promise", "undefined", "useDeprecatedSynchronousErrorHandling", "useDeprecatedNextContext"], "sources": ["R:/chateye/FrontendAngular/node_modules/rxjs/dist/esm/internal/config.js"], "sourcesContent": ["export const config = {\n    onUnhandledError: null,\n    onStoppedNotification: null,\n    Promise: undefined,\n    useDeprecatedSynchronousErrorHandling: false,\n    useDeprecatedNextContext: false,\n};\n"], "mappings": "AAAA,OAAO,MAAMA,MAAM,GAAG;EAClBC,gBAAgB,EAAE,IADA;EAElBC,qBAAqB,EAAE,IAFL;EAGlBC,OAAO,EAAEC,SAHS;EAIlBC,qCAAqC,EAAE,KAJrB;EAKlBC,wBAAwB,EAAE;AALR,CAAf", "ignoreList": []}, "metadata": {}, "sourceType": "module"}