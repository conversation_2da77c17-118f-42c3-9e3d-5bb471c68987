{"ast": null, "code": "import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" || typeof Blob !== \"undefined\" && Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\"; // ArrayBuffer.isView method is not defined in IE10\n\nconst isView = obj => {\n  return typeof ArrayBuffer.isView === \"function\" ? ArrayBuffer.isView(obj) : obj && obj.buffer instanceof ArrayBuffer;\n};\n\nconst encodePacket = ({\n  type,\n  data\n}, supportsBinary, callback) => {\n  if (withNativeBlob && data instanceof Blob) {\n    if (supportsBinary) {\n      return callback(data);\n    } else {\n      return encodeBlobAsBase64(data, callback);\n    }\n  } else if (withNativeArrayBuffer && (data instanceof ArrayBuffer || isView(data))) {\n    if (supportsBinary) {\n      return callback(data);\n    } else {\n      return encodeBlobAsBase64(new Blob([data]), callback);\n    }\n  } // plain string\n\n\n  return callback(PACKET_TYPES[type] + (data || \"\"));\n};\n\nconst encodeBlobAsBase64 = (data, callback) => {\n  const fileReader = new FileReader();\n\n  fileReader.onload = function () {\n    const content = fileReader.result.split(\",\")[1];\n    callback(\"b\" + (content || \"\"));\n  };\n\n  return fileReader.readAsDataURL(data);\n};\n\nfunction toArray(data) {\n  if (data instanceof Uint8Array) {\n    return data;\n  } else if (data instanceof ArrayBuffer) {\n    return new Uint8Array(data);\n  } else {\n    return new Uint8Array(data.buffer, data.byteOffset, data.byteLength);\n  }\n}\n\nlet TEXT_ENCODER;\nexport function encodePacketToBinary(packet, callback) {\n  if (withNativeBlob && packet.data instanceof Blob) {\n    return packet.data.arrayBuffer().then(toArray).then(callback);\n  } else if (withNativeArrayBuffer && (packet.data instanceof ArrayBuffer || isView(packet.data))) {\n    return callback(toArray(packet.data));\n  }\n\n  encodePacket(packet, false, encoded => {\n    if (!TEXT_ENCODER) {\n      TEXT_ENCODER = new TextEncoder();\n    }\n\n    callback(TEXT_ENCODER.encode(encoded));\n  });\n}\nexport { encodePacket };", "map": {"version": 3, "names": ["PACKET_TYPES", "withNativeBlob", "Blob", "Object", "prototype", "toString", "call", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "obj", "buffer", "encodePacket", "type", "data", "supportsBinary", "callback", "encodeBlobAsBase64", "fileReader", "FileReader", "onload", "content", "result", "split", "readAsDataURL", "toArray", "Uint8Array", "byteOffset", "byteLength", "TEXT_ENCODER", "encodePacketToBinary", "packet", "arrayBuffer", "then", "encoded", "TextEncoder", "encode"], "sources": ["R:/chateye/FrontendAngular/node_modules/engine.io-parser/build/esm/encodePacket.browser.js"], "sourcesContent": ["import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n    if (withNativeBlob && data instanceof Blob) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(data, callback);\n        }\n    }\n    else if (withNativeArrayBuffer &&\n        (data instanceof ArrayBuffer || isView(data))) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(new Blob([data]), callback);\n        }\n    }\n    // plain string\n    return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n    const fileReader = new FileReader();\n    fileReader.onload = function () {\n        const content = fileReader.result.split(\",\")[1];\n        callback(\"b\" + (content || \"\"));\n    };\n    return fileReader.readAsDataURL(data);\n};\nfunction toArray(data) {\n    if (data instanceof Uint8Array) {\n        return data;\n    }\n    else if (data instanceof ArrayBuffer) {\n        return new Uint8Array(data);\n    }\n    else {\n        return new Uint8Array(data.buffer, data.byteOffset, data.byteLength);\n    }\n}\nlet TEXT_ENCODER;\nexport function encodePacketToBinary(packet, callback) {\n    if (withNativeBlob && packet.data instanceof Blob) {\n        return packet.data.arrayBuffer().then(toArray).then(callback);\n    }\n    else if (withNativeArrayBuffer &&\n        (packet.data instanceof ArrayBuffer || isView(packet.data))) {\n        return callback(toArray(packet.data));\n    }\n    encodePacket(packet, false, (encoded) => {\n        if (!TEXT_ENCODER) {\n            TEXT_ENCODER = new TextEncoder();\n        }\n        callback(TEXT_ENCODER.encode(encoded));\n    });\n}\nexport { encodePacket };\n"], "mappings": "AAAA,SAASA,YAAT,QAA6B,cAA7B;AACA,MAAMC,cAAc,GAAG,OAAOC,IAAP,KAAgB,UAAhB,IAClB,OAAOA,IAAP,KAAgB,WAAhB,IACGC,MAAM,CAACC,SAAP,CAAiBC,QAAjB,CAA0BC,IAA1B,CAA+BJ,IAA/B,MAAyC,0BAFjD;AAGA,MAAMK,qBAAqB,GAAG,OAAOC,WAAP,KAAuB,UAArD,C,CACA;;AACA,MAAMC,MAAM,GAAIC,GAAD,IAAS;EACpB,OAAO,OAAOF,WAAW,CAACC,MAAnB,KAA8B,UAA9B,GACDD,WAAW,CAACC,MAAZ,CAAmBC,GAAnB,CADC,GAEDA,GAAG,IAAIA,GAAG,CAACC,MAAJ,YAAsBH,WAFnC;AAGH,CAJD;;AAKA,MAAMI,YAAY,GAAG,CAAC;EAAEC,IAAF;EAAQC;AAAR,CAAD,EAAiBC,cAAjB,EAAiCC,QAAjC,KAA8C;EAC/D,IAAIf,cAAc,IAAIa,IAAI,YAAYZ,IAAtC,EAA4C;IACxC,IAAIa,cAAJ,EAAoB;MAChB,OAAOC,QAAQ,CAACF,IAAD,CAAf;IACH,CAFD,MAGK;MACD,OAAOG,kBAAkB,CAACH,IAAD,EAAOE,QAAP,CAAzB;IACH;EACJ,CAPD,MAQK,IAAIT,qBAAqB,KACzBO,IAAI,YAAYN,WAAhB,IAA+BC,MAAM,CAACK,IAAD,CADZ,CAAzB,EAC8C;IAC/C,IAAIC,cAAJ,EAAoB;MAChB,OAAOC,QAAQ,CAACF,IAAD,CAAf;IACH,CAFD,MAGK;MACD,OAAOG,kBAAkB,CAAC,IAAIf,IAAJ,CAAS,CAACY,IAAD,CAAT,CAAD,EAAmBE,QAAnB,CAAzB;IACH;EACJ,CAjB8D,CAkB/D;;;EACA,OAAOA,QAAQ,CAAChB,YAAY,CAACa,IAAD,CAAZ,IAAsBC,IAAI,IAAI,EAA9B,CAAD,CAAf;AACH,CApBD;;AAqBA,MAAMG,kBAAkB,GAAG,CAACH,IAAD,EAAOE,QAAP,KAAoB;EAC3C,MAAME,UAAU,GAAG,IAAIC,UAAJ,EAAnB;;EACAD,UAAU,CAACE,MAAX,GAAoB,YAAY;IAC5B,MAAMC,OAAO,GAAGH,UAAU,CAACI,MAAX,CAAkBC,KAAlB,CAAwB,GAAxB,EAA6B,CAA7B,CAAhB;IACAP,QAAQ,CAAC,OAAOK,OAAO,IAAI,EAAlB,CAAD,CAAR;EACH,CAHD;;EAIA,OAAOH,UAAU,CAACM,aAAX,CAAyBV,IAAzB,CAAP;AACH,CAPD;;AAQA,SAASW,OAAT,CAAiBX,IAAjB,EAAuB;EACnB,IAAIA,IAAI,YAAYY,UAApB,EAAgC;IAC5B,OAAOZ,IAAP;EACH,CAFD,MAGK,IAAIA,IAAI,YAAYN,WAApB,EAAiC;IAClC,OAAO,IAAIkB,UAAJ,CAAeZ,IAAf,CAAP;EACH,CAFI,MAGA;IACD,OAAO,IAAIY,UAAJ,CAAeZ,IAAI,CAACH,MAApB,EAA4BG,IAAI,CAACa,UAAjC,EAA6Cb,IAAI,CAACc,UAAlD,CAAP;EACH;AACJ;;AACD,IAAIC,YAAJ;AACA,OAAO,SAASC,oBAAT,CAA8BC,MAA9B,EAAsCf,QAAtC,EAAgD;EACnD,IAAIf,cAAc,IAAI8B,MAAM,CAACjB,IAAP,YAAuBZ,IAA7C,EAAmD;IAC/C,OAAO6B,MAAM,CAACjB,IAAP,CAAYkB,WAAZ,GAA0BC,IAA1B,CAA+BR,OAA/B,EAAwCQ,IAAxC,CAA6CjB,QAA7C,CAAP;EACH,CAFD,MAGK,IAAIT,qBAAqB,KACzBwB,MAAM,CAACjB,IAAP,YAAuBN,WAAvB,IAAsCC,MAAM,CAACsB,MAAM,CAACjB,IAAR,CADnB,CAAzB,EAC4D;IAC7D,OAAOE,QAAQ,CAACS,OAAO,CAACM,MAAM,CAACjB,IAAR,CAAR,CAAf;EACH;;EACDF,YAAY,CAACmB,MAAD,EAAS,KAAT,EAAiBG,OAAD,IAAa;IACrC,IAAI,CAACL,YAAL,EAAmB;MACfA,YAAY,GAAG,IAAIM,WAAJ,EAAf;IACH;;IACDnB,QAAQ,CAACa,YAAY,CAACO,MAAb,CAAoBF,OAApB,CAAD,CAAR;EACH,CALW,CAAZ;AAMH;AACD,SAAStB,YAAT", "ignoreList": []}, "metadata": {}, "sourceType": "module"}