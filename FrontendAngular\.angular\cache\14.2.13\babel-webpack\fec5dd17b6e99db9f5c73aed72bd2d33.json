{"ast": null, "code": "import logger from \"../modules/logger/index.js\";\nvar name = \"webpack-dev-server\"; // default level is set on the client side, so it does not need\n// to be set by the CLI or API\n\nvar defaultLevel = \"info\"; // options new options, merge with old options\n\n/**\n * @param {false | true | \"none\" | \"error\" | \"warn\" | \"info\" | \"log\" | \"verbose\"} level\n * @returns {void}\n */\n\nfunction setLogLevel(level) {\n  logger.configureDefaultLogger({\n    level: level\n  });\n}\n\nsetLogLevel(defaultLevel);\nvar log = logger.getLogger(name);\n\nvar logEnabledFeatures = function logEnabledFeatures(features) {\n  var enabledFeatures = Object.keys(features);\n\n  if (!features || enabledFeatures.length === 0) {\n    return;\n  }\n\n  var logString = \"Server started:\"; // Server started: Hot Module Replacement enabled, Live Reloading enabled, Overlay disabled.\n\n  for (var i = 0; i < enabledFeatures.length; i++) {\n    var key = enabledFeatures[i];\n    logString += \" \".concat(key, \" \").concat(features[key] ? \"enabled\" : \"disabled\", \",\");\n  } // replace last comma with a period\n\n\n  logString = logString.slice(0, -1).concat(\".\");\n  log.info(logString);\n};\n\nexport { log, logEnabledFeatures, setLogLevel };", "map": {"version": 3, "names": ["logger", "name", "defaultLevel", "setLogLevel", "level", "configure<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "log", "<PERSON><PERSON><PERSON><PERSON>", "logEnabledFeatures", "features", "enabledFeatures", "Object", "keys", "length", "logString", "i", "key", "concat", "slice", "info"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/webpack-dev-server/client/utils/log.js"], "sourcesContent": ["import logger from \"../modules/logger/index.js\";\nvar name = \"webpack-dev-server\"; // default level is set on the client side, so it does not need\n// to be set by the CLI or API\n\nvar defaultLevel = \"info\"; // options new options, merge with old options\n\n/**\n * @param {false | true | \"none\" | \"error\" | \"warn\" | \"info\" | \"log\" | \"verbose\"} level\n * @returns {void}\n */\n\nfunction setLogLevel(level) {\n  logger.configureDefaultLogger({\n    level: level\n  });\n}\n\nsetLogLevel(defaultLevel);\nvar log = logger.getLogger(name);\n\nvar logEnabledFeatures = function logEnabledFeatures(features) {\n  var enabledFeatures = Object.keys(features);\n\n  if (!features || enabledFeatures.length === 0) {\n    return;\n  }\n\n  var logString = \"Server started:\"; // Server started: Hot Module Replacement enabled, Live Reloading enabled, Overlay disabled.\n\n  for (var i = 0; i < enabledFeatures.length; i++) {\n    var key = enabledFeatures[i];\n    logString += \" \".concat(key, \" \").concat(features[key] ? \"enabled\" : \"disabled\", \",\");\n  } // replace last comma with a period\n\n\n  logString = logString.slice(0, -1).concat(\".\");\n  log.info(logString);\n};\n\nexport { log, logEnabledFeatures, setLogLevel };"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,4BAAnB;AACA,IAAIC,IAAI,GAAG,oBAAX,C,CAAiC;AACjC;;AAEA,IAAIC,YAAY,GAAG,MAAnB,C,CAA2B;;AAE3B;AACA;AACA;AACA;;AAEA,SAASC,WAAT,CAAqBC,KAArB,EAA4B;EAC1BJ,MAAM,CAACK,sBAAP,CAA8B;IAC5BD,KAAK,EAAEA;EADqB,CAA9B;AAGD;;AAEDD,WAAW,CAACD,YAAD,CAAX;AACA,IAAII,GAAG,GAAGN,MAAM,CAACO,SAAP,CAAiBN,IAAjB,CAAV;;AAEA,IAAIO,kBAAkB,GAAG,SAASA,kBAAT,CAA4BC,QAA5B,EAAsC;EAC7D,IAAIC,eAAe,GAAGC,MAAM,CAACC,IAAP,CAAYH,QAAZ,CAAtB;;EAEA,IAAI,CAACA,QAAD,IAAaC,eAAe,CAACG,MAAhB,KAA2B,CAA5C,EAA+C;IAC7C;EACD;;EAED,IAAIC,SAAS,GAAG,iBAAhB,CAP6D,CAO1B;;EAEnC,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGL,eAAe,CAACG,MAApC,EAA4CE,CAAC,EAA7C,EAAiD;IAC/C,IAAIC,GAAG,GAAGN,eAAe,CAACK,CAAD,CAAzB;IACAD,SAAS,IAAI,IAAIG,MAAJ,CAAWD,GAAX,EAAgB,GAAhB,EAAqBC,MAArB,CAA4BR,QAAQ,CAACO,GAAD,CAAR,GAAgB,SAAhB,GAA4B,UAAxD,EAAoE,GAApE,CAAb;EACD,CAZ4D,CAY3D;;;EAGFF,SAAS,GAAGA,SAAS,CAACI,KAAV,CAAgB,CAAhB,EAAmB,CAAC,CAApB,EAAuBD,MAAvB,CAA8B,GAA9B,CAAZ;EACAX,GAAG,CAACa,IAAJ,CAASL,SAAT;AACD,CAjBD;;AAmBA,SAASR,GAAT,EAAcE,kBAAd,EAAkCL,WAAlC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}