{"ast": null, "code": "import { createErrorClass } from './createErrorClass';\nexport const UnsubscriptionError = createErrorClass(_super => function UnsubscriptionErrorImpl(errors) {\n  _super(this);\n\n  this.message = errors ? `${errors.length} errors occurred during unsubscription:\n${errors.map((err, i) => `${i + 1}) ${err.toString()}`).join('\\n  ')}` : '';\n  this.name = 'UnsubscriptionError';\n  this.errors = errors;\n});", "map": {"version": 3, "names": ["createErrorClass", "UnsubscriptionError", "_super", "UnsubscriptionErrorImpl", "errors", "message", "length", "map", "err", "i", "toString", "join", "name"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/rxjs/dist/esm/internal/util/UnsubscriptionError.js"], "sourcesContent": ["import { createErrorClass } from './createErrorClass';\nexport const UnsubscriptionError = createErrorClass((_super) => function UnsubscriptionErrorImpl(errors) {\n    _super(this);\n    this.message = errors\n        ? `${errors.length} errors occurred during unsubscription:\n${errors.map((err, i) => `${i + 1}) ${err.toString()}`).join('\\n  ')}`\n        : '';\n    this.name = 'UnsubscriptionError';\n    this.errors = errors;\n});\n"], "mappings": "AAAA,SAASA,gBAAT,QAAiC,oBAAjC;AACA,OAAO,MAAMC,mBAAmB,GAAGD,gBAAgB,CAAEE,MAAD,IAAY,SAASC,uBAAT,CAAiCC,MAAjC,EAAyC;EACrGF,MAAM,CAAC,IAAD,CAAN;;EACA,KAAKG,OAAL,GAAeD,MAAM,GACd,GAAEA,MAAM,CAACE,MAAO;AAC3B,EAAEF,MAAM,CAACG,GAAP,CAAW,CAACC,GAAD,EAAMC,CAAN,KAAa,GAAEA,CAAC,GAAG,CAAE,KAAID,GAAG,CAACE,QAAJ,EAAe,EAAnD,EAAsDC,IAAtD,CAA2D,MAA3D,CAAmE,EAF5C,GAGf,EAHN;EAIA,KAAKC,IAAL,GAAY,qBAAZ;EACA,KAAKR,MAAL,GAAcA,MAAd;AACH,CARkD,CAA5C", "ignoreList": []}, "metadata": {}, "sourceType": "module"}