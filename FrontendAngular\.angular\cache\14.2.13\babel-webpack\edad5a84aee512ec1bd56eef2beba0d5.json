{"ast": null, "code": "import { mergeAll } from './mergeAll';\nexport function concatAll() {\n  return mergeAll(1);\n}", "map": {"version": 3, "names": ["mergeAll", "concatAll"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/rxjs/dist/esm/internal/operators/concatAll.js"], "sourcesContent": ["import { mergeAll } from './mergeAll';\nexport function concatAll() {\n    return mergeAll(1);\n}\n"], "mappings": "AAAA,SAASA,QAAT,QAAyB,YAAzB;AACA,OAAO,SAASC,SAAT,GAAqB;EACxB,OAAOD,QAAQ,CAAC,CAAD,CAAf;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}