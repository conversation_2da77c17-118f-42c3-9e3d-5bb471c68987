{"ast": null, "code": "import { Observable } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./services/chat.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"./components/login-form/login-form.component\";\nimport * as i4 from \"./components/sidebar/sidebar.component\";\nimport * as i5 from \"./components/message-list/message-list.component\";\nimport * as i6 from \"./components/message-input/message-input.component\";\nimport * as i7 from \"./components/admin-panel/admin-panel.component\";\nimport * as i8 from \"./components/debug-info/debug-info.component\";\n\nfunction AppComponent_app_login_form_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-login-form\");\n  }\n}\n\nfunction AppComponent_div_2_p_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 26);\n    i0.ɵɵtext(1, \" Please select a group to start chatting \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AppComponent_div_2_p_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (tmp_0_0 = i0.ɵɵpipeBind1(2, 1, ctx_r3.currentGroup$)) == null ? null : tmp_0_0.description, \" \");\n  }\n}\n\nfunction AppComponent_div_2_button_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function AppComponent_div_2_button_29_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.onShowAdminPanel());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 29);\n    i0.ɵɵelement(2, \"path\", 30)(3, \"path\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Admin \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AppComponent_div_2_app_message_input_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"app-message-input\", 32);\n    i0.ɵɵlistener(\"onSendMessage\", function AppComponent_div_2_app_message_input_37_Template_app_message_input_onSendMessage_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.onSendMessage($event));\n    })(\"onCancelReply\", function AppComponent_div_2_app_message_input_37_Template_app_message_input_onCancelReply_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r13.onCancelReply());\n    });\n    i0.ɵɵpipe(1, \"async\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"replyTo\", i0.ɵɵpipeBind1(1, 1, ctx_r5.replyTo$));\n  }\n}\n\nfunction AppComponent_div_2_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"p\");\n    i0.ɵɵtext(2, \"Select a group to start chatting\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AppComponent_div_2_app_admin_panel_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"app-admin-panel\", 34);\n    i0.ɵɵlistener(\"onClose\", function AppComponent_div_2_app_admin_panel_41_Template_app_admin_panel_onClose_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.onHideAdminPanel());\n    });\n    i0.ɵɵpipe(1, \"async\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"currentUser\", i0.ɵɵpipeBind1(1, 1, ctx_r7.user$));\n  }\n}\n\nfunction AppComponent_div_2_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36)(2, \"div\", 37)(3, \"h3\", 38);\n    i0.ɵɵtext(4, \"Change Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function AppComponent_div_2_div_43_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.onClosePasswordChange());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(6, \"svg\", 40);\n    i0.ɵɵelement(7, \"path\", 41);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(8, \"div\", 42);\n    i0.ɵɵelement(9, \"app-password-change\");\n    i0.ɵɵelementEnd()()();\n  }\n}\n\nconst _c0 = function () {\n  return [];\n};\n\nfunction AppComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"app-sidebar\", 4);\n    i0.ɵɵlistener(\"onPasswordChange\", function AppComponent_div_2_Template_app_sidebar_onPasswordChange_2_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.onPasswordChange());\n    });\n    i0.ɵɵpipe(3, \"async\");\n    i0.ɵɵpipe(4, \"async\");\n    i0.ɵɵpipe(5, \"async\");\n    i0.ɵɵpipe(6, \"async\");\n    i0.ɵɵpipe(7, \"async\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 5)(9, \"div\", 6)(10, \"div\", 7)(11, \"div\", 8)(12, \"h1\", 9);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"async\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, AppComponent_div_2_p_15_Template, 2, 0, \"p\", 10);\n    i0.ɵɵpipe(16, \"async\");\n    i0.ɵɵtemplate(17, AppComponent_div_2_p_17_Template, 3, 3, \"p\", 11);\n    i0.ɵɵpipe(18, \"async\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 12)(20, \"div\", 13)(21, \"div\", 14);\n    i0.ɵɵpipe(22, \"async\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\", 15);\n    i0.ɵɵtext(24);\n    i0.ɵɵpipe(25, \"async\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 16);\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"async\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(29, AppComponent_div_2_button_29_Template, 5, 0, \"button\", 17);\n    i0.ɵɵpipe(30, \"async\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"div\", 18)(32, \"app-message-list\", 19);\n    i0.ɵɵlistener(\"onReply\", function AppComponent_div_2_Template_app_message_list_onReply_32_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.onReply($event));\n    })(\"onAddReaction\", function AppComponent_div_2_Template_app_message_list_onAddReaction_32_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.onAddReaction($event));\n    })(\"onRemoveReaction\", function AppComponent_div_2_Template_app_message_list_onRemoveReaction_32_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.onRemoveReaction($event));\n    })(\"onEdit\", function AppComponent_div_2_Template_app_message_list_onEdit_32_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.onEdit($event));\n    })(\"onDelete\", function AppComponent_div_2_Template_app_message_list_onDelete_32_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.onDelete($event));\n    });\n    i0.ɵɵpipe(33, \"async\");\n    i0.ɵɵpipe(34, \"async\");\n    i0.ɵɵpipe(35, \"async\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"div\", 20);\n    i0.ɵɵtemplate(37, AppComponent_div_2_app_message_input_37_Template, 2, 3, \"app-message-input\", 21);\n    i0.ɵɵpipe(38, \"async\");\n    i0.ɵɵtemplate(39, AppComponent_div_2_div_39_Template, 3, 0, \"div\", 22);\n    i0.ɵɵpipe(40, \"async\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(41, AppComponent_div_2_app_admin_panel_41_Template, 2, 3, \"app-admin-panel\", 23);\n    i0.ɵɵpipe(42, \"async\");\n    i0.ɵɵtemplate(43, AppComponent_div_2_div_43_Template, 10, 0, \"div\", 24);\n    i0.ɵɵelement(44, \"app-debug-info\", 25);\n    i0.ɵɵpipe(45, \"async\");\n    i0.ɵɵpipe(46, \"async\");\n    i0.ɵɵpipe(47, \"async\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_6_0;\n    let tmp_8_0;\n    let tmp_11_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"onlineUsers\", i0.ɵɵpipeBind1(3, 24, ctx_r1.onlineUsers$) || i0.ɵɵpureFunction0(66, _c0))(\"currentUser\", i0.ɵɵpipeBind1(4, 26, ctx_r1.user$))(\"isAdmin\", i0.ɵɵpipeBind1(5, 28, ctx_r1.isAdmin$) || false)(\"groups\", i0.ɵɵpipeBind1(6, 30, ctx_r1.groups$) || i0.ɵɵpureFunction0(67, _c0))(\"currentGroup\", i0.ɵɵpipeBind1(7, 32, ctx_r1.currentGroup$))(\"onJoinGroup\", ctx_r1.onJoinGroup.bind(ctx_r1));\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate1(\" \", ((tmp_6_0 = i0.ɵɵpipeBind1(14, 34, ctx_r1.currentGroup$)) == null ? null : tmp_6_0.name) || \"Select a Group\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !i0.ɵɵpipeBind1(16, 36, ctx_r1.currentGroup$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (tmp_8_0 = i0.ɵɵpipeBind1(18, 38, ctx_r1.currentGroup$)) == null ? null : tmp_8_0.description);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"connected\", i0.ɵɵpipeBind1(22, 40, ctx_r1.connected$));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(25, 42, ctx_r1.connected$) ? \"Connected\" : \"Disconnected\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ((tmp_11_0 = i0.ɵɵpipeBind1(28, 44, ctx_r1.onlineUsers$)) == null ? null : tmp_11_0.length) || 0, \" online \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(30, 46, ctx_r1.isAdmin$));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"messages\", i0.ɵɵpipeBind1(33, 48, ctx_r1.messages$) || i0.ɵɵpureFunction0(68, _c0))(\"currentUser\", i0.ɵɵpipeBind1(34, 50, ctx_r1.user$))(\"loading\", i0.ɵɵpipeBind1(35, 52, ctx_r1.loading$) || false);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(38, 54, ctx_r1.currentGroup$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !i0.ɵɵpipeBind1(40, 56, ctx_r1.currentGroup$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(42, 58, ctx_r1.showAdminPanel$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showPasswordChange);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"user\", i0.ɵɵpipeBind1(45, 60, ctx_r1.user$))(\"isAdmin\", i0.ɵɵpipeBind1(46, 62, ctx_r1.isAdmin$) || false)(\"securityInfo\", i0.ɵɵpipeBind1(47, 64, ctx_r1.securityInfo$));\n  }\n}\n\nexport class AppComponent {\n  constructor(chatService) {\n    this.chatService = chatService;\n    this.showPasswordChange = false;\n    this.user$ = this.chatService.user$;\n    this.messages$ = this.chatService.messages$;\n    this.onlineUsers$ = this.chatService.onlineUsers$;\n    this.groups$ = this.chatService.groups$;\n    this.currentGroup$ = this.chatService.currentGroup$;\n    this.replyTo$ = this.chatService.replyTo$;\n    this.loading$ = this.chatService.loading$;\n    this.isAdmin$ = this.chatService.isAdmin$;\n    this.showAdminPanel$ = this.chatService.showAdminPanel$;\n    this.securityInfo$ = this.chatService.securityInfo$;\n    this.connected$ = this.chatService.connected$;\n    this.isLoggedIn$ = this.chatService.isLoggedIn$;\n  }\n\n  ngOnInit() {// Component initialization\n  }\n\n  ngOnDestroy() {\n    this.chatService.logout();\n  }\n\n  onJoinGroup(groupId) {\n    this.chatService.joinGroup(groupId);\n  }\n\n  onSendMessage(event) {\n    this.chatService.sendMessage(event.text, event.replyToId);\n  }\n\n  onReply(message) {\n    this.chatService.replyToMessage(message);\n  }\n\n  onCancelReply() {\n    this.chatService.cancelReply();\n  }\n\n  onAddReaction(event) {\n    this.chatService.addReaction(event.messageId, event.emoji);\n  }\n\n  onRemoveReaction(data) {\n    this.chatService.removeReaction(data);\n  }\n\n  onEdit(message) {\n    // TODO: Implement message editing\n    console.log('Edit message:', message); // For now, just show an alert\n\n    const newText = prompt('Edit message:', message.text);\n\n    if (newText && newText !== message.text) {\n      // TODO: Send edit request to backend\n      console.log('New message text:', newText);\n    }\n  }\n\n  onDelete(message) {\n    // TODO: Implement message deletion\n    console.log('Delete message:', message); // For now, just show confirmation\n\n    if (confirm('Are you sure you want to delete this message?')) {\n      // TODO: Send delete request to backend\n      console.log('Message deleted');\n    }\n  }\n\n  onShowAdminPanel() {\n    this.chatService.showAdminPanel();\n  }\n\n  onHideAdminPanel() {\n    this.chatService.hideAdminPanel();\n  }\n\n  onPasswordChange() {\n    this.showPasswordChange = true;\n  }\n\n  onClosePasswordChange() {\n    this.showPasswordChange = false;\n  }\n\n}\n\nAppComponent.ɵfac = function AppComponent_Factory(t) {\n  return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.ChatService));\n};\n\nAppComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: AppComponent,\n  selectors: [[\"app-root\"]],\n  decls: 4,\n  vars: 6,\n  consts: [[4, \"ngIf\"], [\"class\", \"fluid-chat-container\", 4, \"ngIf\"], [1, \"fluid-chat-container\"], [1, \"fluid-sidebar\"], [3, \"onlineUsers\", \"currentUser\", \"isAdmin\", \"groups\", \"currentGroup\", \"onJoinGroup\", \"onPasswordChange\"], [1, \"fluid-main-chat\"], [1, \"fluid-chat-header\"], [1, \"header-content\"], [1, \"group-info\"], [1, \"group-name\"], [\"class\", \"no-group-message\", 4, \"ngIf\"], [\"class\", \"group-description\", 4, \"ngIf\"], [1, \"header-actions\"], [1, \"connection-status\"], [1, \"status-indicator\"], [1, \"status-text\"], [1, \"online-count\"], [\"class\", \"admin-button\", \"title\", \"Admin Panel\", 3, \"click\", 4, \"ngIf\"], [1, \"fluid-messages-area\"], [3, \"messages\", \"currentUser\", \"loading\", \"onReply\", \"onAddReaction\", \"onRemoveReaction\", \"onEdit\", \"onDelete\"], [1, \"fluid-input-area\"], [3, \"replyTo\", \"onSendMessage\", \"onCancelReply\", 4, \"ngIf\"], [\"class\", \"no-group-input\", 4, \"ngIf\"], [3, \"currentUser\", \"onClose\", 4, \"ngIf\"], [\"class\", \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\", 4, \"ngIf\"], [3, \"user\", \"isAdmin\", \"securityInfo\"], [1, \"no-group-message\"], [1, \"group-description\"], [\"title\", \"Admin Panel\", 1, \"admin-button\", 3, \"click\"], [\"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"stroke\", \"currentColor\", 1, \"admin-icon\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"], [3, \"replyTo\", \"onSendMessage\", \"onCancelReply\"], [1, \"no-group-input\"], [3, \"currentUser\", \"onClose\"], [1, \"fixed\", \"inset-0\", \"bg-black\", \"bg-opacity-50\", \"flex\", \"items-center\", \"justify-center\", \"z-50\"], [1, \"bg-white\", \"rounded-lg\", \"w-full\", \"max-w-md\", \"mx-4\"], [1, \"flex\", \"items-center\", \"justify-between\", \"p-6\", \"border-b\", \"border-gray-200\"], [1, \"text-lg\", \"font-medium\", \"text-gray-900\"], [1, \"text-gray-400\", \"hover:text-gray-600\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M6 18L18 6M6 6l12 12\"], [1, \"p-6\"]],\n  template: function AppComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, AppComponent_app_login_form_0_Template, 1, 0, \"app-login-form\", 0);\n      i0.ɵɵpipe(1, \"async\");\n      i0.ɵɵtemplate(2, AppComponent_div_2_Template, 48, 69, \"div\", 1);\n      i0.ɵɵpipe(3, \"async\");\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", !i0.ɵɵpipeBind1(1, 2, ctx.isLoggedIn$));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(3, 4, ctx.isLoggedIn$));\n    }\n  },\n  dependencies: [i2.NgIf, i3.LoginFormComponent, i4.SidebarComponent, i5.MessageListComponent, i6.MessageInputComponent, i7.AdminPanelComponent, i8.DebugInfoComponent, i2.AsyncPipe],\n  styles: [\".fluid-chat-container[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  height: 100vh;\\r\\n  width: 100vw;\\r\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\r\\n  overflow: hidden;\\r\\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\\r\\n}\\r\\n\\r\\n.fluid-sidebar[_ngcontent-%COMP%] {\\r\\n  width: 280px;\\r\\n  min-width: 280px;\\r\\n  background: rgba(255, 255, 255, 0.95);\\r\\n  backdrop-filter: blur(20px);\\r\\n  border-right: 1px solid rgba(255, 255, 255, 0.2);\\r\\n  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);\\r\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\r\\n  z-index: 10;\\r\\n}\\r\\n\\r\\n.fluid-main-chat[_ngcontent-%COMP%] {\\r\\n  flex: 1;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  background: rgba(255, 255, 255, 0.98);\\r\\n  backdrop-filter: blur(20px);\\r\\n  border-radius: 20px 0 0 20px;\\r\\n  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);\\r\\n  overflow: hidden;\\r\\n  margin: 10px 10px 10px 0;\\r\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\r\\n}\\r\\n\\r\\n.fluid-chat-header[_ngcontent-%COMP%] {\\r\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\r\\n  color: white;\\r\\n  padding: 20px 30px;\\r\\n  border-radius: 20px 0 0 0;\\r\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\r\\n}\\r\\n.header-content[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: space-between;\\r\\n  align-items: center;\\r\\n  max-width: 100%;\\r\\n}\\r\\n.group-info[_ngcontent-%COMP%] {\\r\\n  flex: 1;\\r\\n  min-width: 0;\\r\\n}\\r\\n.group-name[_ngcontent-%COMP%] {\\r\\n  font-size: 1.5rem;\\r\\n  font-weight: 700;\\r\\n  margin: 0 0 8px 0;\\r\\n  background: linear-gradient(45deg, #fff, #f0f9ff);\\r\\n  -webkit-background-clip: text;\\r\\n  -webkit-text-fill-color: transparent;\\r\\n  background-clip: text;\\r\\n  line-height: 1.2;\\r\\n}\\r\\n.no-group-message[_ngcontent-%COMP%] {\\r\\n  color: rgba(255, 255, 255, 0.8);\\r\\n  font-size: 0.9rem;\\r\\n  margin: 0;\\r\\n  font-weight: 500;\\r\\n}\\r\\n.group-description[_ngcontent-%COMP%] {\\r\\n  color: rgba(255, 255, 255, 0.9);\\r\\n  font-size: 0.85rem;\\r\\n  margin: 0;\\r\\n  opacity: 0.9;\\r\\n}\\r\\n.header-actions[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 20px;\\r\\n  flex-shrink: 0;\\r\\n}\\r\\n.connection-status[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 8px;\\r\\n  background: rgba(255, 255, 255, 0.1);\\r\\n  padding: 8px 12px;\\r\\n  border-radius: 20px;\\r\\n  backdrop-filter: blur(10px);\\r\\n}\\r\\n.status-indicator[_ngcontent-%COMP%] {\\r\\n  width: 8px;\\r\\n  height: 8px;\\r\\n  border-radius: 50%;\\r\\n  background: #ef4444;\\r\\n  transition: all 0.3s ease;\\r\\n}\\r\\n.status-indicator.connected[_ngcontent-%COMP%] {\\r\\n  background: #10b981;\\r\\n  box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);\\r\\n}\\r\\n.status-text[_ngcontent-%COMP%] {\\r\\n  font-size: 0.8rem;\\r\\n  font-weight: 500;\\r\\n}\\r\\n.online-count[_ngcontent-%COMP%] {\\r\\n  background: rgba(255, 255, 255, 0.1);\\r\\n  padding: 8px 12px;\\r\\n  border-radius: 20px;\\r\\n  font-size: 0.8rem;\\r\\n  font-weight: 500;\\r\\n  backdrop-filter: blur(10px);\\r\\n}\\r\\n.admin-button[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 8px;\\r\\n  background: rgba(255, 255, 255, 0.15);\\r\\n  color: white;\\r\\n  border: none;\\r\\n  padding: 10px 16px;\\r\\n  border-radius: 20px;\\r\\n  font-size: 0.85rem;\\r\\n  font-weight: 500;\\r\\n  cursor: pointer;\\r\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\r\\n  backdrop-filter: blur(10px);\\r\\n}\\r\\n.admin-button[_ngcontent-%COMP%]:hover {\\r\\n  background: rgba(255, 255, 255, 0.25);\\r\\n  transform: translateY(-2px);\\r\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\r\\n}\\r\\n.admin-icon[_ngcontent-%COMP%] {\\r\\n  width: 16px;\\r\\n  height: 16px;\\r\\n}\\r\\n\\r\\n.fluid-messages-area[_ngcontent-%COMP%] {\\r\\n  flex: 1;\\r\\n  min-height: 0;\\r\\n  background: #f8fafc;\\r\\n  position: relative;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n.fluid-input-area[_ngcontent-%COMP%] {\\r\\n  background: white;\\r\\n  border-top: 1px solid rgba(0, 0, 0, 0.05);\\r\\n  padding: 20px 30px;\\r\\n  border-radius: 0 0 20px 0;\\r\\n}\\r\\n.no-group-input[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  height: 60px;\\r\\n  background: #f1f5f9;\\r\\n  border-radius: 15px;\\r\\n  color: #64748b;\\r\\n  font-size: 0.9rem;\\r\\n  font-weight: 500;\\r\\n}\\r\\n\\r\\n@media (max-width: 1024px) {\\r\\n  .fluid-sidebar[_ngcontent-%COMP%] {\\r\\n    width: 260px;\\r\\n    min-width: 260px;\\r\\n  }\\r\\n  \\r\\n  .fluid-main-chat[_ngcontent-%COMP%] {\\r\\n    margin: 8px 8px 8px 0;\\r\\n    border-radius: 15px 0 0 15px;\\r\\n  }\\r\\n  \\r\\n  .fluid-chat-header[_ngcontent-%COMP%] {\\r\\n    padding: 16px 20px;\\r\\n    border-radius: 15px 0 0 0;\\r\\n  }\\r\\n  \\r\\n  .group-name[_ngcontent-%COMP%] {\\r\\n    font-size: 1.3rem;\\r\\n  }\\r\\n  \\r\\n  .header-actions[_ngcontent-%COMP%] {\\r\\n    gap: 15px;\\r\\n  }\\r\\n  \\r\\n  .fluid-input-area[_ngcontent-%COMP%] {\\r\\n    padding: 16px 20px;\\r\\n    border-radius: 0 0 15px 0;\\r\\n  }\\r\\n}\\r\\n@media (max-width: 768px) {\\r\\n  .fluid-chat-container[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n  }\\r\\n  \\r\\n  .fluid-sidebar[_ngcontent-%COMP%] {\\r\\n    width: 100%;\\r\\n    height: auto;\\r\\n    min-height: 60px;\\r\\n    border-right: none;\\r\\n    border-bottom: 1px solid rgba(255, 255, 255, 0.2);\\r\\n    border-radius: 0;\\r\\n  }\\r\\n  \\r\\n  .fluid-main-chat[_ngcontent-%COMP%] {\\r\\n    margin: 0;\\r\\n    border-radius: 0;\\r\\n    height: calc(100vh - 60px);\\r\\n  }\\r\\n  \\r\\n  .fluid-chat-header[_ngcontent-%COMP%] {\\r\\n    padding: 12px 16px;\\r\\n    border-radius: 0;\\r\\n  }\\r\\n  \\r\\n  .group-name[_ngcontent-%COMP%] {\\r\\n    font-size: 1.2rem;\\r\\n  }\\r\\n  \\r\\n  .header-actions[_ngcontent-%COMP%] {\\r\\n    gap: 10px;\\r\\n  }\\r\\n  \\r\\n  .connection-status[_ngcontent-%COMP%], .online-count[_ngcontent-%COMP%], .admin-button[_ngcontent-%COMP%] {\\r\\n    padding: 6px 10px;\\r\\n    font-size: 0.75rem;\\r\\n  }\\r\\n  \\r\\n  .fluid-input-area[_ngcontent-%COMP%] {\\r\\n    padding: 12px 16px;\\r\\n    border-radius: 0;\\r\\n  }\\r\\n}\\r\\n@media (max-width: 480px) {\\r\\n  .header-content[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n    align-items: flex-start;\\r\\n    gap: 12px;\\r\\n  }\\r\\n  \\r\\n  .header-actions[_ngcontent-%COMP%] {\\r\\n    width: 100%;\\r\\n    justify-content: space-between;\\r\\n  }\\r\\n  \\r\\n  .group-name[_ngcontent-%COMP%] {\\r\\n    font-size: 1.1rem;\\r\\n  }\\r\\n}\\r\\n\\r\\n*[_ngcontent-%COMP%] {\\r\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\r\\n}\\r\\n\\r\\n[_ngcontent-%COMP%]::-webkit-scrollbar {\\r\\n  width: 6px;\\r\\n}\\r\\n[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\r\\n  background: transparent;\\r\\n}\\r\\n[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\r\\n  background: rgba(0, 0, 0, 0.2);\\r\\n  border-radius: 3px;\\r\\n}\\r\\n[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\r\\n  background: rgba(0, 0, 0, 0.3);\\r\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\"]\n});", "map": {"version": 3, "mappings": "AACA,SAASA,UAAT,QAA0C,MAA1C;;;;;;;;;;;;;ICAAC;;;;;;IA0BUA;IACEA;IACFA;;;;;;IACAA;IACEA;;IACFA;;;;;;IADEA;IAAAA;;;;;;;;IAgBFA;IAEEA;MAAAA;MAAA;MAAA,OAASA,yCAAT;IAA2B,CAA3B;IAIAA;IAAAA;IACEA,4BAAqjB,CAArjB,EAAqjB,MAArjB,EAAqjB,EAArjB;IAEFA;IACAA;IACFA;;;;;;;;IAqBJA;IAGEA;MAAAA;MAAA;MAAA,OAAiBA,6CAAjB;IAAsC,CAAtC,EAAuC,eAAvC,EAAuC;MAAAA;MAAA;MAAA,OACtBA,uCADsB;IACP,CADhC;;IAEDA;;;;;IAHCA;;;;;;IAKFA,gCAA6D,CAA7D,EAA6D,GAA7D;IACKA;IAAgCA;;;;;;;;IAMzCA;IAGEA;MAAAA;MAAA;MAAA,OAAWA,0CAAX;IAA6B,CAA7B;;IACDA;;;;;IAFCA;;;;;;;;IAKFA,gCAAmH,CAAnH,EAAmH,KAAnH,EAAmH,EAAnH,EAAmH,CAAnH,EAAmH,KAAnH,EAAmH,EAAnH,EAAmH,CAAnH,EAAmH,IAAnH,EAAmH,EAAnH;IAGoDA;IAAeA;IAC7DA;IACEA;MAAAA;MAAA;MAAA,OAASA,+CAAT;IAAgC,CAAhC;IAGAA;IAAAA;IACEA;IACFA;IAGJA;IAAAA;IACEA;IACFA;;;;;;;;;;;;IA/GNA,+BAA8D,CAA9D,EAA8D,KAA9D,EAA8D,CAA9D,EAA8D,CAA9D,EAA8D,aAA9D,EAA8D,CAA9D;IAUMA;MAAAA;MAAA;MAAA,OAAoBA,0CAApB;IAAsC,CAAtC;;;;;;IACDA;IAIHA,+BAA6B,CAA7B,EAA6B,KAA7B,EAA6B,CAA7B,EAA6B,EAA7B,EAA6B,KAA7B,EAA6B,CAA7B,EAA6B,EAA7B,EAA6B,KAA7B,EAA6B,CAA7B,EAA6B,EAA7B,EAA6B,IAA7B,EAA6B,CAA7B;IAMUA;;IACFA;IACAA;;IAGAA;;IAGFA;IAEAA,iCAA4B,EAA5B,EAA4B,KAA5B,EAA4B,EAA5B,EAA4B,EAA5B,EAA4B,KAA5B,EAA4B,EAA5B;;IAEyEA;IACrEA;IACEA;;IACFA;IAGFA;IACEA;;IACFA;IAEAA;;IAYFA;IAKJA,iCAAiC,EAAjC,EAAiC,kBAAjC,EAAiC,EAAjC;IAKIA;MAAAA;MAAA;MAAA,OAAWA,uCAAX;IAA0B,CAA1B,EAA2B,eAA3B,EAA2B;MAAAA;MAAA;MAAA,OACVA,6CADU;IACW,CADtC,EAA2B,kBAA3B,EAA2B;MAAAA;MAAA;MAAA,OAEPA,gDAFO;IAEiB,CAF5C,EAA2B,QAA3B,EAA2B;MAAAA;MAAA;MAAA,OAGjBA,sCAHiB;IAGH,CAHxB,EAA2B,UAA3B,EAA2B;MAAAA;MAAA;MAAA,OAIfA,wCAJe;IAIC,CAJ5B;;;;IAKDA;IAIHA;IACEA;;IAOAA;;IAGFA;IAIFA;;IAOAA;IAoBAA;;;;IAKFA;;;;;;;;IArHMA;IAAAA,wGAA4C,aAA5C,EAA4CA,mCAA5C,EAA4C,SAA5C,EAA4CA,+CAA5C,EAA4C,QAA5C,EAA4CA,oEAA5C,EAA4C,cAA5C,EAA4CA,2CAA5C,EAA4C,aAA5C,EAA4CC,+BAA5C;IAiBMD;IAAAA;IAEEA;IAAAA;IAGAA;IAAAA;IAO4BA;IAAAA;IAE5BA;IAAAA;IAKFA;IAAAA;IAICA;IAAAA;IAkBLA;IAAAA,mGAAsC,aAAtC,EAAsCA,oCAAtC,EAAsC,SAAtC,EAAsCA,gDAAtC;IAcCA;IAAAA;IAMGA;IAAAA;IAQPA;IAAAA;IAMGA;IAAAA;IAqBJA;IAAAA,4DAAsB,SAAtB,EAAsBA,gDAAtB,EAAsB,cAAtB,EAAsBA,4CAAtB;;;;AD9GJ,OAAM,MAAOE,YAAP,CAAmB;EAevBC,YAAoBC,WAApB,EAA4C;IAAxB;IAFpB,0BAAqB,KAArB;IAGE,KAAKC,KAAL,GAAa,KAAKD,WAAL,CAAiBC,KAA9B;IACA,KAAKC,SAAL,GAAiB,KAAKF,WAAL,CAAiBE,SAAlC;IACA,KAAKC,YAAL,GAAoB,KAAKH,WAAL,CAAiBG,YAArC;IACA,KAAKC,OAAL,GAAe,KAAKJ,WAAL,CAAiBI,OAAhC;IACA,KAAKC,aAAL,GAAqB,KAAKL,WAAL,CAAiBK,aAAtC;IACA,KAAKC,QAAL,GAAgB,KAAKN,WAAL,CAAiBM,QAAjC;IACA,KAAKC,QAAL,GAAgB,KAAKP,WAAL,CAAiBO,QAAjC;IACA,KAAKC,QAAL,GAAgB,KAAKR,WAAL,CAAiBQ,QAAjC;IACA,KAAKC,eAAL,GAAuB,KAAKT,WAAL,CAAiBS,eAAxC;IACA,KAAKC,aAAL,GAAqB,KAAKV,WAAL,CAAiBU,aAAtC;IACA,KAAKC,UAAL,GAAkB,KAAKX,WAAL,CAAiBW,UAAnC;IACA,KAAKC,WAAL,GAAmB,KAAKZ,WAAL,CAAiBY,WAApC;EACD;;EAEDC,QAAQ,IACN;EACD;;EAEDC,WAAW;IACT,KAAKd,WAAL,CAAiBe,MAAjB;EACD;;EAEDC,WAAW,CAACC,OAAD,EAAgB;IACzB,KAAKjB,WAAL,CAAiBkB,SAAjB,CAA2BD,OAA3B;EACD;;EAEDE,aAAa,CAACC,KAAD,EAAkD;IAC7D,KAAKpB,WAAL,CAAiBqB,WAAjB,CAA6BD,KAAK,CAACE,IAAnC,EAAyCF,KAAK,CAACG,SAA/C;EACD;;EAEDC,OAAO,CAACC,OAAD,EAAiB;IACtB,KAAKzB,WAAL,CAAiB0B,cAAjB,CAAgCD,OAAhC;EACD;;EAEDE,aAAa;IACX,KAAK3B,WAAL,CAAiB4B,WAAjB;EACD;;EAEDC,aAAa,CAACT,KAAD,EAA4C;IACvD,KAAKpB,WAAL,CAAiB8B,WAAjB,CAA6BV,KAAK,CAACW,SAAnC,EAA8CX,KAAK,CAACY,KAApD;EACD;;EAEDC,gBAAgB,CAACC,IAAD,EAA2C;IACzD,KAAKlC,WAAL,CAAiBmC,cAAjB,CAAgCD,IAAhC;EACD;;EAEDE,MAAM,CAACX,OAAD,EAAiB;IACrB;IACAY,OAAO,CAACC,GAAR,CAAY,eAAZ,EAA6Bb,OAA7B,EAFqB,CAGrB;;IACA,MAAMc,OAAO,GAAGC,MAAM,CAAC,eAAD,EAAkBf,OAAO,CAACH,IAA1B,CAAtB;;IACA,IAAIiB,OAAO,IAAIA,OAAO,KAAKd,OAAO,CAACH,IAAnC,EAAyC;MACvC;MACAe,OAAO,CAACC,GAAR,CAAY,mBAAZ,EAAiCC,OAAjC;IACD;EACF;;EAEDE,QAAQ,CAAChB,OAAD,EAAiB;IACvB;IACAY,OAAO,CAACC,GAAR,CAAY,iBAAZ,EAA+Bb,OAA/B,EAFuB,CAGvB;;IACA,IAAIiB,OAAO,CAAC,+CAAD,CAAX,EAA8D;MAC5D;MACAL,OAAO,CAACC,GAAR,CAAY,iBAAZ;IACD;EACF;;EAEDK,gBAAgB;IACd,KAAK3C,WAAL,CAAiB4C,cAAjB;EACD;;EAEDC,gBAAgB;IACd,KAAK7C,WAAL,CAAiB8C,cAAjB;EACD;;EAEDC,gBAAgB;IACd,KAAKC,kBAAL,GAA0B,IAA1B;EACD;;EAEDC,qBAAqB;IACnB,KAAKD,kBAAL,GAA0B,KAA1B;EACD;;AAjGsB;;;mBAAZlD,cAAYF;AAAA;;;QAAZE;EAAYoD;EAAAC;EAAAC;EAAAC;EAAAC;IAAA;MCVzB1D;;MAGAA;;;;;MAHiBA;MAGXA;MAAAA", "names": ["Observable", "i0", "ctx_r1", "AppComponent", "constructor", "chatService", "user$", "messages$", "onlineUsers$", "groups$", "currentGroup$", "replyTo$", "loading$", "isAdmin$", "showAdminPanel$", "securityInfo$", "connected$", "isLoggedIn$", "ngOnInit", "ngOnDestroy", "logout", "onJoinGroup", "groupId", "joinGroup", "onSendMessage", "event", "sendMessage", "text", "replyToId", "onReply", "message", "replyToMessage", "onCancelReply", "cancelReply", "onAddReaction", "addReaction", "messageId", "emoji", "onRemoveReaction", "data", "removeReaction", "onEdit", "console", "log", "newText", "prompt", "onDelete", "confirm", "onShowAdminPanel", "showAdminPanel", "onHideAdminPanel", "hideAdminPanel", "onPasswordChange", "showPasswordChange", "onClosePasswordChange", "selectors", "decls", "vars", "consts", "template"], "sourceRoot": "", "sources": ["R:\\chateye\\Frontend\\chateye-angular\\src\\app\\app.component.ts", "R:\\chateye\\Frontend\\chateye-angular\\src\\app\\app.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { Observable, combineLatest } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { ChatService } from './services/chat.service';\nimport { Message, Group, User } from './services/api.service';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.css']\n})\nexport class AppComponent implements OnInit, OnDestroy {\n  user$: Observable<string | null>;\n  messages$: Observable<Message[]>;\n  onlineUsers$: Observable<User[]>;\n  groups$: Observable<Group[]>;\n  currentGroup$: Observable<Group | null>;\n  replyTo$: Observable<Message | null>;\n  loading$: Observable<boolean>;\n  isAdmin$: Observable<boolean>;\n  showAdminPanel$: Observable<boolean>;\n  securityInfo$: Observable<any>;\n  connected$: Observable<boolean>;\n  isLoggedIn$: Observable<boolean>;\n  showPasswordChange = false;\n\n  constructor(private chatService: ChatService) {\n    this.user$ = this.chatService.user$;\n    this.messages$ = this.chatService.messages$;\n    this.onlineUsers$ = this.chatService.onlineUsers$;\n    this.groups$ = this.chatService.groups$;\n    this.currentGroup$ = this.chatService.currentGroup$;\n    this.replyTo$ = this.chatService.replyTo$;\n    this.loading$ = this.chatService.loading$;\n    this.isAdmin$ = this.chatService.isAdmin$;\n    this.showAdminPanel$ = this.chatService.showAdminPanel$;\n    this.securityInfo$ = this.chatService.securityInfo$;\n    this.connected$ = this.chatService.connected$;\n    this.isLoggedIn$ = this.chatService.isLoggedIn$;\n  }\n\n  ngOnInit(): void {\n    // Component initialization\n  }\n\n  ngOnDestroy(): void {\n    this.chatService.logout();\n  }\n\n  onJoinGroup(groupId: string): void {\n    this.chatService.joinGroup(groupId);\n  }\n\n  onSendMessage(event: { text: string; replyToId: string | null }): void {\n    this.chatService.sendMessage(event.text, event.replyToId);\n  }\n\n  onReply(message: Message): void {\n    this.chatService.replyToMessage(message);\n  }\n\n  onCancelReply(): void {\n    this.chatService.cancelReply();\n  }\n\n  onAddReaction(event: { messageId: string; emoji: string }): void {\n    this.chatService.addReaction(event.messageId, event.emoji);\n  }\n\n  onRemoveReaction(data: { messageId: string; emoji: string }): void {\n    this.chatService.removeReaction(data);\n  }\n\n  onEdit(message: Message): void {\n    // TODO: Implement message editing\n    console.log('Edit message:', message);\n    // For now, just show an alert\n    const newText = prompt('Edit message:', message.text);\n    if (newText && newText !== message.text) {\n      // TODO: Send edit request to backend\n      console.log('New message text:', newText);\n    }\n  }\n\n  onDelete(message: Message): void {\n    // TODO: Implement message deletion\n    console.log('Delete message:', message);\n    // For now, just show confirmation\n    if (confirm('Are you sure you want to delete this message?')) {\n      // TODO: Send delete request to backend\n      console.log('Message deleted');\n    }\n  }\n\n  onShowAdminPanel(): void {\n    this.chatService.showAdminPanel();\n  }\n\n  onHideAdminPanel(): void {\n    this.chatService.hideAdminPanel();\n  }\n\n  onPasswordChange(): void {\n    this.showPasswordChange = true;\n  }\n\n  onClosePasswordChange(): void {\n    this.showPasswordChange = false;\n  }\n}\n", "<!-- Login Form -->\n<app-login-form *ngIf=\"!(isLoggedIn$ | async)\"></app-login-form>\n\n<!-- Fluid Chat Interface -->\n<div *ngIf=\"isLoggedIn$ | async\" class=\"fluid-chat-container\">\n  <!-- Sidebar -->\n  <div class=\"fluid-sidebar\">\n    <app-sidebar \n      [onlineUsers]=\"(onlineUsers$ | async) || []\"\n      [currentUser]=\"user$ | async\"\n      [isAdmin]=\"(isAdmin$ | async) || false\"\n      [groups]=\"(groups$ | async) || []\"\n      [currentGroup]=\"currentGroup$ | async\"\n      [onJoinGroup]=\"onJoinGroup.bind(this)\"\n      (onPasswordChange)=\"onPasswordChange()\"\n    ></app-sidebar>\n  </div>\n\n  <!-- Main Chat Area -->\n  <div class=\"fluid-main-chat\">\n    <!-- Chat Header -->\n    <div class=\"fluid-chat-header\">\n      <div class=\"header-content\">\n        <div class=\"group-info\">\n          <h1 class=\"group-name\">\n            {{ (currentGroup$ | async)?.name || 'Select a Group' }}\n          </h1>\n          <p *ngIf=\"!(currentGroup$ | async)\" class=\"no-group-message\">\n            Please select a group to start chatting\n          </p>\n          <p *ngIf=\"(currentGroup$ | async)?.description\" class=\"group-description\">\n            {{ (currentGroup$ | async)?.description }}\n          </p>\n        </div>\n        \n        <div class=\"header-actions\">\n          <div class=\"connection-status\">\n            <div class=\"status-indicator\" [class.connected]=\"connected$ | async\"></div>\n            <span class=\"status-text\">\n              {{ (connected$ | async) ? 'Connected' : 'Disconnected' }}\n            </span>\n          </div>\n          \n          <div class=\"online-count\">\n            {{ (onlineUsers$ | async)?.length || 0 }} online\n          </div>\n          \n          <button\n            *ngIf=\"isAdmin$ | async\"\n            (click)=\"onShowAdminPanel()\"\n            class=\"admin-button\"\n            title=\"Admin Panel\"\n          >\n            <svg class=\"admin-icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"></path>\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"></path>\n            </svg>\n            Admin\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Fluid Messages Area -->\n    <div class=\"fluid-messages-area\">\n      <app-message-list\n        [messages]=\"(messages$ | async) || []\"\n        [currentUser]=\"user$ | async\"\n        [loading]=\"(loading$ | async) || false\"\n        (onReply)=\"onReply($event)\"\n        (onAddReaction)=\"onAddReaction($event)\"\n        (onRemoveReaction)=\"onRemoveReaction($event)\"\n        (onEdit)=\"onEdit($event)\"\n        (onDelete)=\"onDelete($event)\"\n      ></app-message-list>\n    </div>\n\n    <!-- Fluid Message Input -->\n    <div class=\"fluid-input-area\">\n      <app-message-input\n        *ngIf=\"currentGroup$ | async\"\n        [replyTo]=\"replyTo$ | async\"\n        (onSendMessage)=\"onSendMessage($event)\"\n        (onCancelReply)=\"onCancelReply()\"\n      ></app-message-input>\n      \n      <div *ngIf=\"!(currentGroup$ | async)\" class=\"no-group-input\">\n        <p>Select a group to start chatting</p>\n      </div>\n    </div>\n  </div>\n\n  <!-- Admin Panel Modal -->\n  <app-admin-panel\n    *ngIf=\"showAdminPanel$ | async\"\n    [currentUser]=\"user$ | async\"\n    (onClose)=\"onHideAdminPanel()\"\n  ></app-admin-panel>\n\n  <!-- Password Change Modal -->\n  <div *ngIf=\"showPasswordChange\" class=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n    <div class=\"bg-white rounded-lg w-full max-w-md mx-4\">\n      <div class=\"flex items-center justify-between p-6 border-b border-gray-200\">\n        <h3 class=\"text-lg font-medium text-gray-900\">Change Password</h3>\n        <button\n          (click)=\"onClosePasswordChange()\"\n          class=\"text-gray-400 hover:text-gray-600\"\n        >\n          <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\n          </svg>\n        </button>\n      </div>\n      <div class=\"p-6\">\n        <app-password-change></app-password-change>\n      </div>\n    </div>\n  </div>\n\n  <!-- Debug Info -->\n  <app-debug-info \n    [user]=\"user$ | async\"\n    [isAdmin]=\"(isAdmin$ | async) || false\"\n    [securityInfo]=\"securityInfo$ | async\"\n  ></app-debug-info>\n</div>"]}, "metadata": {}, "sourceType": "module"}