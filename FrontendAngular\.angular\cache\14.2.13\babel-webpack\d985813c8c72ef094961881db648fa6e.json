{"ast": null, "code": "import { operate } from '../util/lift';\nimport { noop } from '../util/noop';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function buffer(closingNotifier) {\n  return operate((source, subscriber) => {\n    let currentBuffer = [];\n    source.subscribe(createOperatorSubscriber(subscriber, value => currentBuffer.push(value), () => {\n      subscriber.next(currentBuffer);\n      subscriber.complete();\n    }));\n    closingNotifier.subscribe(createOperatorSubscriber(subscriber, () => {\n      const b = currentBuffer;\n      currentBuffer = [];\n      subscriber.next(b);\n    }, noop));\n    return () => {\n      currentBuffer = null;\n    };\n  });\n}", "map": {"version": 3, "names": ["operate", "noop", "createOperatorSubscriber", "buffer", "closingNotifier", "source", "subscriber", "current<PERSON><PERSON><PERSON>", "subscribe", "value", "push", "next", "complete", "b"], "sources": ["R:/chateye/FrontendAngular/node_modules/rxjs/dist/esm/internal/operators/buffer.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { noop } from '../util/noop';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function buffer(closingNotifier) {\n    return operate((source, subscriber) => {\n        let currentBuffer = [];\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => currentBuffer.push(value), () => {\n            subscriber.next(currentBuffer);\n            subscriber.complete();\n        }));\n        closingNotifier.subscribe(createOperatorSubscriber(subscriber, () => {\n            const b = currentBuffer;\n            currentBuffer = [];\n            subscriber.next(b);\n        }, noop));\n        return () => {\n            currentBuffer = null;\n        };\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,cAAxB;AACA,SAASC,IAAT,QAAqB,cAArB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,OAAO,SAASC,MAAT,CAAgBC,eAAhB,EAAiC;EACpC,OAAOJ,OAAO,CAAC,CAACK,MAAD,EAASC,UAAT,KAAwB;IACnC,IAAIC,aAAa,GAAG,EAApB;IACAF,MAAM,CAACG,SAAP,CAAiBN,wBAAwB,CAACI,UAAD,EAAcG,KAAD,IAAWF,aAAa,CAACG,IAAd,CAAmBD,KAAnB,CAAxB,EAAmD,MAAM;MAC9FH,UAAU,CAACK,IAAX,CAAgBJ,aAAhB;MACAD,UAAU,CAACM,QAAX;IACH,CAHwC,CAAzC;IAIAR,eAAe,CAACI,SAAhB,CAA0BN,wBAAwB,CAACI,UAAD,EAAa,MAAM;MACjE,MAAMO,CAAC,GAAGN,aAAV;MACAA,aAAa,GAAG,EAAhB;MACAD,UAAU,CAACK,IAAX,CAAgBE,CAAhB;IACH,CAJiD,EAI/CZ,IAJ+C,CAAlD;IAKA,OAAO,MAAM;MACTM,aAAa,GAAG,IAAhB;IACH,CAFD;EAGH,CAda,CAAd;AAeH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}