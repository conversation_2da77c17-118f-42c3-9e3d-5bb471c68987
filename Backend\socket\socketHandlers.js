const Message = require('../models/Message');
const User = require('../models/User');
const Group = require('../models/Group');
const ReadReceipt = require('../models/ReadReceipt');
const AuthMiddleware = require('../middleware/auth');

const handleSocketConnection = (io) => {
  io.on('connection', (socket) => {
    // Handle user joining
    socket.on('join', async (data) => {
      try {
        const { username, inviteCode } = data;

        // Check authentication
        const authResult = await AuthMiddleware.checkUserAccess(username, inviteCode);
        if (!authResult.allowed) {
          socket.emit('authError', {
            message: authResult.error || 'Access denied',
            securityInfo: AuthMiddleware.getSecurityInfo()
          });
          socket.disconnect();
          return;
        }
        
        // Find or create user and set online
        const user = await User.findOrCreate(username);
        await User.setOnlineStatus(user.id, true);
        
        // Set admin status if applicable
        if (authResult.isAdmin) {
          await User.setAdminStatus(user.id, true);
        }
        
        // Store user info in socket
        socket.userId = user.id;
        socket.username = username;
        
        // Ensure user has access to the default "General" group
        try {
          const generalGroup = await Group.findByName('General');
          if (generalGroup) {
            await Group.grantUserAccess(user.id, generalGroup.id, null);
          }
        } catch (error) {
          // User already has access to General group or other error
        }
        
        // Get user's accessible groups
        const userGroups = await Group.getGroupsForUser(user.id);
        socket.userGroups = userGroups.map(g => g.id);
        
        // Join user to their accessible groups
        for (const group of userGroups) {
          socket.join(`group_${group.id}`);
        }
        
        // Send user's groups and current group info
        socket.emit('userGroups', userGroups);
        
        // If user has groups, join the first one by default
        if (userGroups.length > 0) {
          const defaultGroup = userGroups[0];
          socket.currentGroupId = defaultGroup.id;
          socket.join(`current_group_${defaultGroup.id}`);
          
          // Send recent messages for the default group
          const recentMessages = await Message.getMessagesWithReactionsAndReadReceipts(defaultGroup.id, 50);
          socket.emit('recentMessages', recentMessages);
        }
        
        // Broadcast user joined to all their groups
        for (const group of userGroups) {
          socket.to(`group_${group.id}`).emit('userJoined', {
            username,
            userId: user.id,
            groupId: group.id
          });
        }
        
        // Send updated online users list for each group
        for (const group of userGroups) {
          const onlineUsers = await User.getOnlineUsers();
          io.to(`group_${group.id}`).emit('onlineUsersUpdate', onlineUsers);
        }
        
      } catch (error) {
        console.error('Error handling join:', error);
        socket.emit('error', { message: 'Failed to join chat' });
      }
    });

    // Handle joining a specific group
    socket.on('joinGroup', async (data) => {
      try {
        const { groupId } = data;
        const { userId, userGroups } = socket;
        
        if (!groupId || !userId) {
          socket.emit('error', { message: 'Invalid group data' });
          return;
        }

        // Check if user has access to this group
        if (!userGroups.includes(parseInt(groupId))) {
          socket.emit('error', { message: 'Access denied to this group' });
          return;
        }

        // Leave previous current group
        if (socket.currentGroupId) {
          socket.leave(`current_group_${socket.currentGroupId}`);
        }

        // Join new group
        socket.currentGroupId = parseInt(groupId);
        socket.join(`current_group_${groupId}`);
        
        // Send recent messages for the new group
        const recentMessages = await Message.getMessagesWithReactionsAndReadReceipts(groupId, 50);
        socket.emit('recentMessages', recentMessages);
        
        // Notify user of successful group join
        socket.emit('groupJoined', { groupId });
        
      } catch (error) {
        console.error('Error handling joinGroup:', error);
        socket.emit('error', { message: 'Failed to join group' });
      }
    });

    // Handle sending messages with acknowledgment
    socket.on('sendMessage', async (data, callback) => {
      try {
        const { text, replyTo, groupId, messageId } = data;
        const { username, userId, userGroups } = socket;

        if (!text || !username || !groupId) {
          const error = { message: 'Invalid message data' };
          socket.emit('error', error);
          if (callback) callback({ error: error.message });
          return;
        }

        // Check if user has access to this group
        if (!userGroups.includes(parseInt(groupId))) {
          const error = { message: 'Access denied to this group' };
          socket.emit('error', error);
          if (callback) callback({ error: error.message });
          return;
        }

        // Create message
        const message = await Message.create(text, userId, groupId, replyTo);
        const messageWithDetails = await Message.getMessageWithDetails(message.id);
        messageWithDetails.reactions = await Message.getReactions(message.id);

        // Broadcast to all users in the group
        io.to(`group_${groupId}`).emit('newMessage', messageWithDetails);

        // Send acknowledgment back to sender
        if (callback) {
          callback({
            success: true,
            message: messageWithDetails,
            originalMessageId: messageId // For replacing optimistic messages
          });
        }

      } catch (error) {
        console.error('Error handling sendMessage:', error);
        const errorMsg = { message: 'Failed to send message' };
        socket.emit('error', errorMsg);
        if (callback) callback({ error: errorMsg.message });
      }
    });

    // Handle adding reactions with acknowledgment
    socket.on('addReaction', async (data, callback) => {
      try {
        const { messageId, emoji } = data;
        const { username, userId } = socket;

        if (!messageId || !emoji) {
          const error = { message: 'Invalid reaction data' };
          socket.emit('error', error);
          if (callback) callback({ error: error.message });
          return;
        }

        // Add reaction
        await Message.addReaction(messageId, userId, emoji);
        const reactions = await Message.getReactions(messageId);

        // Get the group ID from the message
        const message = await Message.getMessageWithDetails(messageId);
        if (message && message.group_id) {
          // Broadcast reaction update to the group
          io.to(`group_${message.group_id}`).emit('reactionUpdate', {
            messageId,
            reactions
          });

          // Send acknowledgment
          if (callback) {
            callback({ success: true, reactions });
          }
        } else {
          const error = { message: 'Message not found' };
          if (callback) callback({ error: error.message });
        }

      } catch (error) {
        console.error('Error handling addReaction:', error);
        const errorMsg = { message: 'Failed to add reaction' };
        socket.emit('error', errorMsg);
        if (callback) callback({ error: errorMsg.message });
      }
    });

    // Handle removing reactions
    socket.on('removeReaction', async (data) => {
      try {
        const { messageId, emoji } = data;
        const { username, userId } = socket;

        if (!messageId) {
          socket.emit('error', { message: 'Invalid reaction data' });
          return;
        }

        // Remove reaction (with specific emoji if provided)
        await Message.removeReaction(messageId, userId, emoji);
        const reactions = await Message.getReactions(messageId);
        
        // Get the group ID from the message
        const message = await Message.getMessageWithDetails(messageId);
        if (message && message.group_id) {
          // Broadcast reaction update to the group
          io.to(`group_${message.group_id}`).emit('reactionUpdate', {
            messageId,
            reactions
          });
        }
        
      } catch (error) {
        console.error('Error handling removeReaction:', error);
        socket.emit('error', { message: 'Failed to remove reaction' });
      }
    });

    // Handle message editing
    socket.on('messageUpdated', async (data) => {
      try {
        const { messageId, newText } = data;
        const { username, userId } = socket;

        if (!messageId || !newText) {
          socket.emit('error', { message: 'Invalid message update data' });
          return;
        }

        // Update message in database
        const updatedMessage = await Message.updateMessage(messageId, newText, userId);

        if (updatedMessage) {
          // Get the group ID from the message
          const message = await Message.getMessageWithDetails(messageId);
          if (message && message.group_id) {
            // Broadcast message update to the group
            io.to(`group_${message.group_id}`).emit('messageUpdated', {
              messageId,
              newText,
              updatedAt: updatedMessage.updated_at
            });
          }
        }

      } catch (error) {
        console.error('Error handling messageUpdated:', error);
        socket.emit('error', { message: 'Failed to update message' });
      }
    });

    // Handle typing indicators
    socket.on('typing', (data) => {
      try {
        const { groupId, isTyping } = data;
        const { username, userGroups } = socket;

        if (!groupId || !username) {
          return;
        }

        // Check if user has access to this group
        if (!userGroups.includes(parseInt(groupId))) {
          return;
        }

        // Broadcast typing status to other users in the group (excluding sender)
        socket.to(`group_${groupId}`).emit('userTyping', {
          username,
          groupId,
          isTyping
        });



      } catch (error) {
        console.error('Error handling typing:', error);
      }
    });

    // Handle message deletion
    socket.on('messageDeleted', async (data) => {
      try {
        const { messageId } = data;
        const { username, userId } = socket;
        
        if (!messageId) {
          socket.emit('error', { message: 'Invalid message delete data' });
          return;
        }

        // Get the group ID before deleting
        const message = await Message.getMessageWithDetails(messageId);
        if (message && message.group_id) {
          // Delete message from database
          await Message.deleteMessage(messageId, userId);
          
          // Broadcast message deletion to the group
          io.to(`group_${message.group_id}`).emit('messageDeleted', {
            messageId
          });
        }
        
      } catch (error) {
        console.error('Error handling messageDeleted:', error);
        socket.emit('error', { message: 'Failed to delete message' });
      }
    });

    // Handle heartbeat for presence tracking
    socket.on('heartbeat', async () => {
      try {
        if (socket.userId) {
          await User.updateLastSeen(socket.userId);
        }
      } catch (error) {
        console.error('Error handling heartbeat:', error);
      }
    });

    // Handle user status updates
    socket.on('updateStatus', async (data) => {
      try {
        const { status } = data;
        const { username, userId, userGroups } = socket;

        if (!userId || !status) {
          return;
        }

        // Update user status in database
        await User.updateLastSeen(userId);

        // Broadcast status update to all user's groups
        if (userGroups) {
          for (const groupId of userGroups) {
            socket.to(`group_${groupId}`).emit('userStatusUpdate', {
              username,
              userId,
              status,
              timestamp: new Date().toISOString()
            });
          }
        }



      } catch (error) {
        console.error('Error handling status update:', error);
      }
    });

    // Handle read receipts
    socket.on('markAsRead', async (data) => {
      try {
        const { messageId } = data;
        const { username, userId, userGroups } = socket;

        if (!messageId || !userId) {
          return;
        }

        // Mark message as read
        await ReadReceipt.markAsRead(messageId, userId);

        // Get the message to find its group
        const message = await Message.getMessageWithDetails(messageId);
        if (message && message.group_id) {
          // Check if user has access to this group
          if (!userGroups.includes(parseInt(message.group_id))) {
            return;
          }

          // Get all read receipts for this message
          const readReceipts = await ReadReceipt.getReadReceipts(messageId);

          // Broadcast read receipt update to the group
          io.to(`group_${message.group_id}`).emit('readReceiptUpdate', {
            messageId,
            readReceipts
          });


        }

      } catch (error) {
        console.error('Error handling markAsRead:', error);
      }
    });

    // Handle bulk read receipts (when user views messages)
    socket.on('markMessagesAsRead', async (data) => {
      try {
        const { messageIds } = data;
        const { username, userId, userGroups } = socket;

        if (!messageIds || !Array.isArray(messageIds) || messageIds.length === 0 || !userId) {
          return;
        }

        // Mark messages as read
        await ReadReceipt.markMessagesAsRead(messageIds, userId);

        // Get read receipts for all messages
        const readReceipts = await ReadReceipt.getReadReceiptsForMessages(messageIds);

        // Find the group for these messages (assuming they're all from the same group)
        if (messageIds.length > 0) {
          const message = await Message.getMessageWithDetails(messageIds[0]);
          if (message && message.group_id) {
            // Check if user has access to this group
            if (userGroups.includes(parseInt(message.group_id))) {
              // Broadcast read receipt updates to the group
              for (const messageId of messageIds) {
                if (readReceipts[messageId]) {
                  io.to(`group_${message.group_id}`).emit('readReceiptUpdate', {
                    messageId,
                    readReceipts: readReceipts[messageId]
                  });
                }
              }


            }
          }
        }

      } catch (error) {
        console.error('Error handling markMessagesAsRead:', error);
      }
    });

    // Handle user disconnect
    socket.on('disconnect', async () => {
      try {
        
        if (socket.userId && socket.username) {
          // Set user offline and update last seen
          await User.setOnlineStatus(socket.userId, false);
          await User.updateLastSeen(socket.userId);

          // Broadcast user left to all their groups
          if (socket.userGroups) {
            for (const groupId of socket.userGroups) {
              socket.to(`group_${groupId}`).emit('userLeft', {
                username: socket.username,
                userId: socket.userId,
                groupId: groupId,
                lastSeen: new Date().toISOString()
              });

              // Send updated online users list for each group
              const onlineUsers = await User.getOnlineUsers();
              socket.to(`group_${groupId}`).emit('onlineUsersUpdate', onlineUsers);
            }
          }
        }
      } catch (error) {
        console.error('Error handling disconnect:', error);
      }
    });
  });
};

module.exports = handleSocketConnection;
