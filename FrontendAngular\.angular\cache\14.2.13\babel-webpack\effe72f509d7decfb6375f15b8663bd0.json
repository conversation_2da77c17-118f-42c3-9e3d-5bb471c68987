{"ast": null, "code": "// The error overlay is inspired (and mostly copied) from Create React App (https://github.com/facebookincubator/create-react-app)\n// They, in turn, got inspired by webpack-hot-middleware (https://github.com/glenjamin/webpack-hot-middleware).\nimport ansiHTML from \"ansi-html-community\";\nimport { encode } from \"html-entities\";\nvar colors = {\n  reset: [\"transparent\", \"transparent\"],\n  black: \"181818\",\n  red: \"E36049\",\n  green: \"B3CB74\",\n  yellow: \"FFD080\",\n  blue: \"7CAFC2\",\n  magenta: \"7FACCA\",\n  cyan: \"C3C2EF\",\n  lightgrey: \"EBE7E3\",\n  darkgrey: \"6D7891\"\n};\n/** @type {HTMLIFrameElement | null | undefined} */\n\nvar iframeContainerElement;\n/** @type {HTMLDivElement | null | undefined} */\n\nvar containerElement;\n/** @type {Array<(element: HTMLDivElement) => void>} */\n\nvar onLoadQueue = [];\n/** @type {TrustedTypePolicy | undefined} */\n\nvar overlayTrustedTypesPolicy;\nansiHTML.setColors(colors);\n/**\n * @param {string | null} trustedTypesPolicyName\n */\n\nfunction createContainer(trustedTypesPolicyName) {\n  // Enable Trusted Types if they are available in the current browser.\n  if (window.trustedTypes) {\n    overlayTrustedTypesPolicy = window.trustedTypes.createPolicy(trustedTypesPolicyName || \"webpack-dev-server#overlay\", {\n      createHTML: function createHTML(value) {\n        return value;\n      }\n    });\n  }\n\n  iframeContainerElement = document.createElement(\"iframe\");\n  iframeContainerElement.id = \"webpack-dev-server-client-overlay\";\n  iframeContainerElement.src = \"about:blank\";\n  iframeContainerElement.style.position = \"fixed\";\n  iframeContainerElement.style.left = 0;\n  iframeContainerElement.style.top = 0;\n  iframeContainerElement.style.right = 0;\n  iframeContainerElement.style.bottom = 0;\n  iframeContainerElement.style.width = \"100vw\";\n  iframeContainerElement.style.height = \"100vh\";\n  iframeContainerElement.style.border = \"none\";\n  iframeContainerElement.style.zIndex = 9999999999;\n\n  iframeContainerElement.onload = function () {\n    containerElement =\n    /** @type {Document} */\n\n    /** @type {HTMLIFrameElement} */\n    iframeContainerElement.contentDocument.createElement(\"div\");\n    containerElement.id = \"webpack-dev-server-client-overlay-div\";\n    containerElement.style.position = \"fixed\";\n    containerElement.style.boxSizing = \"border-box\";\n    containerElement.style.left = 0;\n    containerElement.style.top = 0;\n    containerElement.style.right = 0;\n    containerElement.style.bottom = 0;\n    containerElement.style.width = \"100vw\";\n    containerElement.style.height = \"100vh\";\n    containerElement.style.backgroundColor = \"rgba(0, 0, 0, 0.85)\";\n    containerElement.style.color = \"#E8E8E8\";\n    containerElement.style.fontFamily = \"Menlo, Consolas, monospace\";\n    containerElement.style.fontSize = \"large\";\n    containerElement.style.padding = \"2rem\";\n    containerElement.style.lineHeight = \"1.2\";\n    containerElement.style.whiteSpace = \"pre-wrap\";\n    containerElement.style.overflow = \"auto\";\n    var headerElement = document.createElement(\"span\");\n    headerElement.innerText = \"Compiled with problems:\";\n    var closeButtonElement = document.createElement(\"button\");\n    closeButtonElement.innerText = \"X\";\n    closeButtonElement.style.background = \"transparent\";\n    closeButtonElement.style.border = \"none\";\n    closeButtonElement.style.fontSize = \"20px\";\n    closeButtonElement.style.fontWeight = \"bold\";\n    closeButtonElement.style.color = \"white\";\n    closeButtonElement.style.cursor = \"pointer\";\n    closeButtonElement.style.cssFloat = \"right\"; // @ts-ignore\n\n    closeButtonElement.style.styleFloat = \"right\";\n    closeButtonElement.addEventListener(\"click\", function () {\n      hide();\n    });\n    containerElement.appendChild(headerElement);\n    containerElement.appendChild(closeButtonElement);\n    containerElement.appendChild(document.createElement(\"br\"));\n    containerElement.appendChild(document.createElement(\"br\"));\n    /** @type {Document} */\n\n    /** @type {HTMLIFrameElement} */\n\n    iframeContainerElement.contentDocument.body.appendChild(containerElement);\n    onLoadQueue.forEach(function (onLoad) {\n      onLoad(\n      /** @type {HTMLDivElement} */\n      containerElement);\n    });\n    onLoadQueue = [];\n    /** @type {HTMLIFrameElement} */\n\n    iframeContainerElement.onload = null;\n  };\n\n  document.body.appendChild(iframeContainerElement);\n}\n/**\n * @param {(element: HTMLDivElement) => void} callback\n * @param {string | null} trustedTypesPolicyName\n */\n\n\nfunction ensureOverlayExists(callback, trustedTypesPolicyName) {\n  if (containerElement) {\n    // Everything is ready, call the callback right away.\n    callback(containerElement);\n    return;\n  }\n\n  onLoadQueue.push(callback);\n\n  if (iframeContainerElement) {\n    return;\n  }\n\n  createContainer(trustedTypesPolicyName);\n} // Successful compilation.\n\n\nfunction hide() {\n  if (!iframeContainerElement) {\n    return;\n  } // Clean up and reset internal state.\n\n\n  document.body.removeChild(iframeContainerElement);\n  iframeContainerElement = null;\n  containerElement = null;\n}\n/**\n * @param {string} type\n * @param {string  | { file?: string, moduleName?: string, loc?: string, message?: string }} item\n * @returns {{ header: string, body: string }}\n */\n\n\nfunction formatProblem(type, item) {\n  var header = type === \"warning\" ? \"WARNING\" : \"ERROR\";\n  var body = \"\";\n\n  if (typeof item === \"string\") {\n    body += item;\n  } else {\n    var file = item.file || \"\"; // eslint-disable-next-line no-nested-ternary\n\n    var moduleName = item.moduleName ? item.moduleName.indexOf(\"!\") !== -1 ? \"\".concat(item.moduleName.replace(/^(\\s|\\S)*!/, \"\"), \" (\").concat(item.moduleName, \")\") : \"\".concat(item.moduleName) : \"\";\n    var loc = item.loc;\n    header += \"\".concat(moduleName || file ? \" in \".concat(moduleName ? \"\".concat(moduleName).concat(file ? \" (\".concat(file, \")\") : \"\") : file).concat(loc ? \" \".concat(loc) : \"\") : \"\");\n    body += item.message || \"\";\n  }\n\n  return {\n    header: header,\n    body: body\n  };\n} // Compilation with errors (e.g. syntax error or missing modules).\n\n/**\n * @param {string} type\n * @param {Array<string  | { file?: string, moduleName?: string, loc?: string, message?: string }>} messages\n * @param {string | null} trustedTypesPolicyName\n */\n\n\nfunction show(type, messages, trustedTypesPolicyName) {\n  ensureOverlayExists(function () {\n    messages.forEach(function (message) {\n      var entryElement = document.createElement(\"div\");\n      var typeElement = document.createElement(\"span\");\n\n      var _formatProblem = formatProblem(type, message),\n          header = _formatProblem.header,\n          body = _formatProblem.body;\n\n      typeElement.innerText = header;\n      typeElement.style.color = \"#\".concat(colors.red); // Make it look similar to our terminal.\n\n      var text = ansiHTML(encode(body));\n      var messageTextNode = document.createElement(\"div\");\n      messageTextNode.innerHTML = overlayTrustedTypesPolicy ? overlayTrustedTypesPolicy.createHTML(text) : text;\n      entryElement.appendChild(typeElement);\n      entryElement.appendChild(document.createElement(\"br\"));\n      entryElement.appendChild(document.createElement(\"br\"));\n      entryElement.appendChild(messageTextNode);\n      entryElement.appendChild(document.createElement(\"br\"));\n      entryElement.appendChild(document.createElement(\"br\"));\n      /** @type {HTMLDivElement} */\n\n      containerElement.appendChild(entryElement);\n    });\n  }, trustedTypesPolicyName);\n}\n\nexport { formatProblem, show, hide };", "map": {"version": 3, "names": ["ansiHTML", "encode", "colors", "reset", "black", "red", "green", "yellow", "blue", "magenta", "cyan", "<PERSON><PERSON>rey", "<PERSON><PERSON>rey", "iframeContainerElement", "containerElement", "onLoadQueue", "overlayTrustedTypesPolicy", "setColors", "createContainer", "trustedTypesPolicyName", "window", "trustedTypes", "createPolicy", "createHTML", "value", "document", "createElement", "id", "src", "style", "position", "left", "top", "right", "bottom", "width", "height", "border", "zIndex", "onload", "contentDocument", "boxSizing", "backgroundColor", "color", "fontFamily", "fontSize", "padding", "lineHeight", "whiteSpace", "overflow", "headerElement", "innerText", "closeButtonElement", "background", "fontWeight", "cursor", "cssFloat", "styleFloat", "addEventListener", "hide", "append<PERSON><PERSON><PERSON>", "body", "for<PERSON>ach", "onLoad", "ensureOverlayExists", "callback", "push", "<PERSON><PERSON><PERSON><PERSON>", "formatProblem", "type", "item", "header", "file", "moduleName", "indexOf", "concat", "replace", "loc", "message", "show", "messages", "entryElement", "typeElement", "_formatProblem", "text", "messageTextNode", "innerHTML"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/webpack-dev-server/client/overlay.js"], "sourcesContent": ["// The error overlay is inspired (and mostly copied) from Create React App (https://github.com/facebookincubator/create-react-app)\n// They, in turn, got inspired by webpack-hot-middleware (https://github.com/glenjamin/webpack-hot-middleware).\nimport ansiHTML from \"ansi-html-community\";\nimport { encode } from \"html-entities\";\nvar colors = {\n  reset: [\"transparent\", \"transparent\"],\n  black: \"181818\",\n  red: \"E36049\",\n  green: \"B3CB74\",\n  yellow: \"FFD080\",\n  blue: \"7CAFC2\",\n  magenta: \"7FACCA\",\n  cyan: \"C3C2EF\",\n  lightgrey: \"EBE7E3\",\n  darkgrey: \"6D7891\"\n};\n/** @type {HTMLIFrameElement | null | undefined} */\n\nvar iframeContainerElement;\n/** @type {HTMLDivElement | null | undefined} */\n\nvar containerElement;\n/** @type {Array<(element: HTMLDivElement) => void>} */\n\nvar onLoadQueue = [];\n/** @type {TrustedTypePolicy | undefined} */\n\nvar overlayTrustedTypesPolicy;\nansiHTML.setColors(colors);\n/**\n * @param {string | null} trustedTypesPolicyName\n */\n\nfunction createContainer(trustedTypesPolicyName) {\n  // Enable Trusted Types if they are available in the current browser.\n  if (window.trustedTypes) {\n    overlayTrustedTypesPolicy = window.trustedTypes.createPolicy(trustedTypesPolicyName || \"webpack-dev-server#overlay\", {\n      createHTML: function createHTML(value) {\n        return value;\n      }\n    });\n  }\n\n  iframeContainerElement = document.createElement(\"iframe\");\n  iframeContainerElement.id = \"webpack-dev-server-client-overlay\";\n  iframeContainerElement.src = \"about:blank\";\n  iframeContainerElement.style.position = \"fixed\";\n  iframeContainerElement.style.left = 0;\n  iframeContainerElement.style.top = 0;\n  iframeContainerElement.style.right = 0;\n  iframeContainerElement.style.bottom = 0;\n  iframeContainerElement.style.width = \"100vw\";\n  iframeContainerElement.style.height = \"100vh\";\n  iframeContainerElement.style.border = \"none\";\n  iframeContainerElement.style.zIndex = 9999999999;\n\n  iframeContainerElement.onload = function () {\n    containerElement =\n    /** @type {Document} */\n\n    /** @type {HTMLIFrameElement} */\n    iframeContainerElement.contentDocument.createElement(\"div\");\n    containerElement.id = \"webpack-dev-server-client-overlay-div\";\n    containerElement.style.position = \"fixed\";\n    containerElement.style.boxSizing = \"border-box\";\n    containerElement.style.left = 0;\n    containerElement.style.top = 0;\n    containerElement.style.right = 0;\n    containerElement.style.bottom = 0;\n    containerElement.style.width = \"100vw\";\n    containerElement.style.height = \"100vh\";\n    containerElement.style.backgroundColor = \"rgba(0, 0, 0, 0.85)\";\n    containerElement.style.color = \"#E8E8E8\";\n    containerElement.style.fontFamily = \"Menlo, Consolas, monospace\";\n    containerElement.style.fontSize = \"large\";\n    containerElement.style.padding = \"2rem\";\n    containerElement.style.lineHeight = \"1.2\";\n    containerElement.style.whiteSpace = \"pre-wrap\";\n    containerElement.style.overflow = \"auto\";\n    var headerElement = document.createElement(\"span\");\n    headerElement.innerText = \"Compiled with problems:\";\n    var closeButtonElement = document.createElement(\"button\");\n    closeButtonElement.innerText = \"X\";\n    closeButtonElement.style.background = \"transparent\";\n    closeButtonElement.style.border = \"none\";\n    closeButtonElement.style.fontSize = \"20px\";\n    closeButtonElement.style.fontWeight = \"bold\";\n    closeButtonElement.style.color = \"white\";\n    closeButtonElement.style.cursor = \"pointer\";\n    closeButtonElement.style.cssFloat = \"right\"; // @ts-ignore\n\n    closeButtonElement.style.styleFloat = \"right\";\n    closeButtonElement.addEventListener(\"click\", function () {\n      hide();\n    });\n    containerElement.appendChild(headerElement);\n    containerElement.appendChild(closeButtonElement);\n    containerElement.appendChild(document.createElement(\"br\"));\n    containerElement.appendChild(document.createElement(\"br\"));\n    /** @type {Document} */\n\n    /** @type {HTMLIFrameElement} */\n    iframeContainerElement.contentDocument.body.appendChild(containerElement);\n    onLoadQueue.forEach(function (onLoad) {\n      onLoad(\n      /** @type {HTMLDivElement} */\n      containerElement);\n    });\n    onLoadQueue = [];\n    /** @type {HTMLIFrameElement} */\n\n    iframeContainerElement.onload = null;\n  };\n\n  document.body.appendChild(iframeContainerElement);\n}\n/**\n * @param {(element: HTMLDivElement) => void} callback\n * @param {string | null} trustedTypesPolicyName\n */\n\n\nfunction ensureOverlayExists(callback, trustedTypesPolicyName) {\n  if (containerElement) {\n    // Everything is ready, call the callback right away.\n    callback(containerElement);\n    return;\n  }\n\n  onLoadQueue.push(callback);\n\n  if (iframeContainerElement) {\n    return;\n  }\n\n  createContainer(trustedTypesPolicyName);\n} // Successful compilation.\n\n\nfunction hide() {\n  if (!iframeContainerElement) {\n    return;\n  } // Clean up and reset internal state.\n\n\n  document.body.removeChild(iframeContainerElement);\n  iframeContainerElement = null;\n  containerElement = null;\n}\n/**\n * @param {string} type\n * @param {string  | { file?: string, moduleName?: string, loc?: string, message?: string }} item\n * @returns {{ header: string, body: string }}\n */\n\n\nfunction formatProblem(type, item) {\n  var header = type === \"warning\" ? \"WARNING\" : \"ERROR\";\n  var body = \"\";\n\n  if (typeof item === \"string\") {\n    body += item;\n  } else {\n    var file = item.file || \"\"; // eslint-disable-next-line no-nested-ternary\n\n    var moduleName = item.moduleName ? item.moduleName.indexOf(\"!\") !== -1 ? \"\".concat(item.moduleName.replace(/^(\\s|\\S)*!/, \"\"), \" (\").concat(item.moduleName, \")\") : \"\".concat(item.moduleName) : \"\";\n    var loc = item.loc;\n    header += \"\".concat(moduleName || file ? \" in \".concat(moduleName ? \"\".concat(moduleName).concat(file ? \" (\".concat(file, \")\") : \"\") : file).concat(loc ? \" \".concat(loc) : \"\") : \"\");\n    body += item.message || \"\";\n  }\n\n  return {\n    header: header,\n    body: body\n  };\n} // Compilation with errors (e.g. syntax error or missing modules).\n\n/**\n * @param {string} type\n * @param {Array<string  | { file?: string, moduleName?: string, loc?: string, message?: string }>} messages\n * @param {string | null} trustedTypesPolicyName\n */\n\n\nfunction show(type, messages, trustedTypesPolicyName) {\n  ensureOverlayExists(function () {\n    messages.forEach(function (message) {\n      var entryElement = document.createElement(\"div\");\n      var typeElement = document.createElement(\"span\");\n\n      var _formatProblem = formatProblem(type, message),\n          header = _formatProblem.header,\n          body = _formatProblem.body;\n\n      typeElement.innerText = header;\n      typeElement.style.color = \"#\".concat(colors.red); // Make it look similar to our terminal.\n\n      var text = ansiHTML(encode(body));\n      var messageTextNode = document.createElement(\"div\");\n      messageTextNode.innerHTML = overlayTrustedTypesPolicy ? overlayTrustedTypesPolicy.createHTML(text) : text;\n      entryElement.appendChild(typeElement);\n      entryElement.appendChild(document.createElement(\"br\"));\n      entryElement.appendChild(document.createElement(\"br\"));\n      entryElement.appendChild(messageTextNode);\n      entryElement.appendChild(document.createElement(\"br\"));\n      entryElement.appendChild(document.createElement(\"br\"));\n      /** @type {HTMLDivElement} */\n\n      containerElement.appendChild(entryElement);\n    });\n  }, trustedTypesPolicyName);\n}\n\nexport { formatProblem, show, hide };"], "mappings": "AAAA;AACA;AACA,OAAOA,QAAP,MAAqB,qBAArB;AACA,SAASC,MAAT,QAAuB,eAAvB;AACA,IAAIC,MAAM,GAAG;EACXC,KAAK,EAAE,CAAC,aAAD,EAAgB,aAAhB,CADI;EAEXC,KAAK,EAAE,QAFI;EAGXC,GAAG,EAAE,QAHM;EAIXC,KAAK,EAAE,QAJI;EAKXC,MAAM,EAAE,QALG;EAMXC,IAAI,EAAE,QANK;EAOXC,OAAO,EAAE,QAPE;EAQXC,IAAI,EAAE,QARK;EASXC,SAAS,EAAE,QATA;EAUXC,QAAQ,EAAE;AAVC,CAAb;AAYA;;AAEA,IAAIC,sBAAJ;AACA;;AAEA,IAAIC,gBAAJ;AACA;;AAEA,IAAIC,WAAW,GAAG,EAAlB;AACA;;AAEA,IAAIC,yBAAJ;AACAhB,QAAQ,CAACiB,SAAT,CAAmBf,MAAnB;AACA;AACA;AACA;;AAEA,SAASgB,eAAT,CAAyBC,sBAAzB,EAAiD;EAC/C;EACA,IAAIC,MAAM,CAACC,YAAX,EAAyB;IACvBL,yBAAyB,GAAGI,MAAM,CAACC,YAAP,CAAoBC,YAApB,CAAiCH,sBAAsB,IAAI,4BAA3D,EAAyF;MACnHI,UAAU,EAAE,SAASA,UAAT,CAAoBC,KAApB,EAA2B;QACrC,OAAOA,KAAP;MACD;IAHkH,CAAzF,CAA5B;EAKD;;EAEDX,sBAAsB,GAAGY,QAAQ,CAACC,aAAT,CAAuB,QAAvB,CAAzB;EACAb,sBAAsB,CAACc,EAAvB,GAA4B,mCAA5B;EACAd,sBAAsB,CAACe,GAAvB,GAA6B,aAA7B;EACAf,sBAAsB,CAACgB,KAAvB,CAA6BC,QAA7B,GAAwC,OAAxC;EACAjB,sBAAsB,CAACgB,KAAvB,CAA6BE,IAA7B,GAAoC,CAApC;EACAlB,sBAAsB,CAACgB,KAAvB,CAA6BG,GAA7B,GAAmC,CAAnC;EACAnB,sBAAsB,CAACgB,KAAvB,CAA6BI,KAA7B,GAAqC,CAArC;EACApB,sBAAsB,CAACgB,KAAvB,CAA6BK,MAA7B,GAAsC,CAAtC;EACArB,sBAAsB,CAACgB,KAAvB,CAA6BM,KAA7B,GAAqC,OAArC;EACAtB,sBAAsB,CAACgB,KAAvB,CAA6BO,MAA7B,GAAsC,OAAtC;EACAvB,sBAAsB,CAACgB,KAAvB,CAA6BQ,MAA7B,GAAsC,MAAtC;EACAxB,sBAAsB,CAACgB,KAAvB,CAA6BS,MAA7B,GAAsC,UAAtC;;EAEAzB,sBAAsB,CAAC0B,MAAvB,GAAgC,YAAY;IAC1CzB,gBAAgB;IAChB;;IAEA;IACAD,sBAAsB,CAAC2B,eAAvB,CAAuCd,aAAvC,CAAqD,KAArD,CAJA;IAKAZ,gBAAgB,CAACa,EAAjB,GAAsB,uCAAtB;IACAb,gBAAgB,CAACe,KAAjB,CAAuBC,QAAvB,GAAkC,OAAlC;IACAhB,gBAAgB,CAACe,KAAjB,CAAuBY,SAAvB,GAAmC,YAAnC;IACA3B,gBAAgB,CAACe,KAAjB,CAAuBE,IAAvB,GAA8B,CAA9B;IACAjB,gBAAgB,CAACe,KAAjB,CAAuBG,GAAvB,GAA6B,CAA7B;IACAlB,gBAAgB,CAACe,KAAjB,CAAuBI,KAAvB,GAA+B,CAA/B;IACAnB,gBAAgB,CAACe,KAAjB,CAAuBK,MAAvB,GAAgC,CAAhC;IACApB,gBAAgB,CAACe,KAAjB,CAAuBM,KAAvB,GAA+B,OAA/B;IACArB,gBAAgB,CAACe,KAAjB,CAAuBO,MAAvB,GAAgC,OAAhC;IACAtB,gBAAgB,CAACe,KAAjB,CAAuBa,eAAvB,GAAyC,qBAAzC;IACA5B,gBAAgB,CAACe,KAAjB,CAAuBc,KAAvB,GAA+B,SAA/B;IACA7B,gBAAgB,CAACe,KAAjB,CAAuBe,UAAvB,GAAoC,4BAApC;IACA9B,gBAAgB,CAACe,KAAjB,CAAuBgB,QAAvB,GAAkC,OAAlC;IACA/B,gBAAgB,CAACe,KAAjB,CAAuBiB,OAAvB,GAAiC,MAAjC;IACAhC,gBAAgB,CAACe,KAAjB,CAAuBkB,UAAvB,GAAoC,KAApC;IACAjC,gBAAgB,CAACe,KAAjB,CAAuBmB,UAAvB,GAAoC,UAApC;IACAlC,gBAAgB,CAACe,KAAjB,CAAuBoB,QAAvB,GAAkC,MAAlC;IACA,IAAIC,aAAa,GAAGzB,QAAQ,CAACC,aAAT,CAAuB,MAAvB,CAApB;IACAwB,aAAa,CAACC,SAAd,GAA0B,yBAA1B;IACA,IAAIC,kBAAkB,GAAG3B,QAAQ,CAACC,aAAT,CAAuB,QAAvB,CAAzB;IACA0B,kBAAkB,CAACD,SAAnB,GAA+B,GAA/B;IACAC,kBAAkB,CAACvB,KAAnB,CAAyBwB,UAAzB,GAAsC,aAAtC;IACAD,kBAAkB,CAACvB,KAAnB,CAAyBQ,MAAzB,GAAkC,MAAlC;IACAe,kBAAkB,CAACvB,KAAnB,CAAyBgB,QAAzB,GAAoC,MAApC;IACAO,kBAAkB,CAACvB,KAAnB,CAAyByB,UAAzB,GAAsC,MAAtC;IACAF,kBAAkB,CAACvB,KAAnB,CAAyBc,KAAzB,GAAiC,OAAjC;IACAS,kBAAkB,CAACvB,KAAnB,CAAyB0B,MAAzB,GAAkC,SAAlC;IACAH,kBAAkB,CAACvB,KAAnB,CAAyB2B,QAAzB,GAAoC,OAApC,CAjC0C,CAiCG;;IAE7CJ,kBAAkB,CAACvB,KAAnB,CAAyB4B,UAAzB,GAAsC,OAAtC;IACAL,kBAAkB,CAACM,gBAAnB,CAAoC,OAApC,EAA6C,YAAY;MACvDC,IAAI;IACL,CAFD;IAGA7C,gBAAgB,CAAC8C,WAAjB,CAA6BV,aAA7B;IACApC,gBAAgB,CAAC8C,WAAjB,CAA6BR,kBAA7B;IACAtC,gBAAgB,CAAC8C,WAAjB,CAA6BnC,QAAQ,CAACC,aAAT,CAAuB,IAAvB,CAA7B;IACAZ,gBAAgB,CAAC8C,WAAjB,CAA6BnC,QAAQ,CAACC,aAAT,CAAuB,IAAvB,CAA7B;IACA;;IAEA;;IACAb,sBAAsB,CAAC2B,eAAvB,CAAuCqB,IAAvC,CAA4CD,WAA5C,CAAwD9C,gBAAxD;IACAC,WAAW,CAAC+C,OAAZ,CAAoB,UAAUC,MAAV,EAAkB;MACpCA,MAAM;MACN;MACAjD,gBAFM,CAAN;IAGD,CAJD;IAKAC,WAAW,GAAG,EAAd;IACA;;IAEAF,sBAAsB,CAAC0B,MAAvB,GAAgC,IAAhC;EACD,CAxDD;;EA0DAd,QAAQ,CAACoC,IAAT,CAAcD,WAAd,CAA0B/C,sBAA1B;AACD;AACD;AACA;AACA;AACA;;;AAGA,SAASmD,mBAAT,CAA6BC,QAA7B,EAAuC9C,sBAAvC,EAA+D;EAC7D,IAAIL,gBAAJ,EAAsB;IACpB;IACAmD,QAAQ,CAACnD,gBAAD,CAAR;IACA;EACD;;EAEDC,WAAW,CAACmD,IAAZ,CAAiBD,QAAjB;;EAEA,IAAIpD,sBAAJ,EAA4B;IAC1B;EACD;;EAEDK,eAAe,CAACC,sBAAD,CAAf;AACD,C,CAAC;;;AAGF,SAASwC,IAAT,GAAgB;EACd,IAAI,CAAC9C,sBAAL,EAA6B;IAC3B;EACD,CAHa,CAGZ;;;EAGFY,QAAQ,CAACoC,IAAT,CAAcM,WAAd,CAA0BtD,sBAA1B;EACAA,sBAAsB,GAAG,IAAzB;EACAC,gBAAgB,GAAG,IAAnB;AACD;AACD;AACA;AACA;AACA;AACA;;;AAGA,SAASsD,aAAT,CAAuBC,IAAvB,EAA6BC,IAA7B,EAAmC;EACjC,IAAIC,MAAM,GAAGF,IAAI,KAAK,SAAT,GAAqB,SAArB,GAAiC,OAA9C;EACA,IAAIR,IAAI,GAAG,EAAX;;EAEA,IAAI,OAAOS,IAAP,KAAgB,QAApB,EAA8B;IAC5BT,IAAI,IAAIS,IAAR;EACD,CAFD,MAEO;IACL,IAAIE,IAAI,GAAGF,IAAI,CAACE,IAAL,IAAa,EAAxB,CADK,CACuB;;IAE5B,IAAIC,UAAU,GAAGH,IAAI,CAACG,UAAL,GAAkBH,IAAI,CAACG,UAAL,CAAgBC,OAAhB,CAAwB,GAAxB,MAAiC,CAAC,CAAlC,GAAsC,GAAGC,MAAH,CAAUL,IAAI,CAACG,UAAL,CAAgBG,OAAhB,CAAwB,YAAxB,EAAsC,EAAtC,CAAV,EAAqD,IAArD,EAA2DD,MAA3D,CAAkEL,IAAI,CAACG,UAAvE,EAAmF,GAAnF,CAAtC,GAAgI,GAAGE,MAAH,CAAUL,IAAI,CAACG,UAAf,CAAlJ,GAA+K,EAAhM;IACA,IAAII,GAAG,GAAGP,IAAI,CAACO,GAAf;IACAN,MAAM,IAAI,GAAGI,MAAH,CAAUF,UAAU,IAAID,IAAd,GAAqB,OAAOG,MAAP,CAAcF,UAAU,GAAG,GAAGE,MAAH,CAAUF,UAAV,EAAsBE,MAAtB,CAA6BH,IAAI,GAAG,KAAKG,MAAL,CAAYH,IAAZ,EAAkB,GAAlB,CAAH,GAA4B,EAA7D,CAAH,GAAsEA,IAA9F,EAAoGG,MAApG,CAA2GE,GAAG,GAAG,IAAIF,MAAJ,CAAWE,GAAX,CAAH,GAAqB,EAAnI,CAArB,GAA8J,EAAxK,CAAV;IACAhB,IAAI,IAAIS,IAAI,CAACQ,OAAL,IAAgB,EAAxB;EACD;;EAED,OAAO;IACLP,MAAM,EAAEA,MADH;IAELV,IAAI,EAAEA;EAFD,CAAP;AAID,C,CAAC;;AAEF;AACA;AACA;AACA;AACA;;;AAGA,SAASkB,IAAT,CAAcV,IAAd,EAAoBW,QAApB,EAA8B7D,sBAA9B,EAAsD;EACpD6C,mBAAmB,CAAC,YAAY;IAC9BgB,QAAQ,CAAClB,OAAT,CAAiB,UAAUgB,OAAV,EAAmB;MAClC,IAAIG,YAAY,GAAGxD,QAAQ,CAACC,aAAT,CAAuB,KAAvB,CAAnB;MACA,IAAIwD,WAAW,GAAGzD,QAAQ,CAACC,aAAT,CAAuB,MAAvB,CAAlB;;MAEA,IAAIyD,cAAc,GAAGf,aAAa,CAACC,IAAD,EAAOS,OAAP,CAAlC;MAAA,IACIP,MAAM,GAAGY,cAAc,CAACZ,MAD5B;MAAA,IAEIV,IAAI,GAAGsB,cAAc,CAACtB,IAF1B;;MAIAqB,WAAW,CAAC/B,SAAZ,GAAwBoB,MAAxB;MACAW,WAAW,CAACrD,KAAZ,CAAkBc,KAAlB,GAA0B,IAAIgC,MAAJ,CAAWzE,MAAM,CAACG,GAAlB,CAA1B,CATkC,CASgB;;MAElD,IAAI+E,IAAI,GAAGpF,QAAQ,CAACC,MAAM,CAAC4D,IAAD,CAAP,CAAnB;MACA,IAAIwB,eAAe,GAAG5D,QAAQ,CAACC,aAAT,CAAuB,KAAvB,CAAtB;MACA2D,eAAe,CAACC,SAAhB,GAA4BtE,yBAAyB,GAAGA,yBAAyB,CAACO,UAA1B,CAAqC6D,IAArC,CAAH,GAAgDA,IAArG;MACAH,YAAY,CAACrB,WAAb,CAAyBsB,WAAzB;MACAD,YAAY,CAACrB,WAAb,CAAyBnC,QAAQ,CAACC,aAAT,CAAuB,IAAvB,CAAzB;MACAuD,YAAY,CAACrB,WAAb,CAAyBnC,QAAQ,CAACC,aAAT,CAAuB,IAAvB,CAAzB;MACAuD,YAAY,CAACrB,WAAb,CAAyByB,eAAzB;MACAJ,YAAY,CAACrB,WAAb,CAAyBnC,QAAQ,CAACC,aAAT,CAAuB,IAAvB,CAAzB;MACAuD,YAAY,CAACrB,WAAb,CAAyBnC,QAAQ,CAACC,aAAT,CAAuB,IAAvB,CAAzB;MACA;;MAEAZ,gBAAgB,CAAC8C,WAAjB,CAA6BqB,YAA7B;IACD,CAvBD;EAwBD,CAzBkB,EAyBhB9D,sBAzBgB,CAAnB;AA0BD;;AAED,SAASiD,aAAT,EAAwBW,IAAxB,EAA8BpB,IAA9B", "ignoreList": []}, "metadata": {}, "sourceType": "module"}