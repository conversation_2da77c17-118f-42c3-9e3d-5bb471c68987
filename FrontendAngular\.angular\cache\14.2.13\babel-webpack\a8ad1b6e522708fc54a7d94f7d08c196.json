{"ast": null, "code": "import { Observable } from '../Observable';\nimport { EMPTY } from './empty';\nexport function range(start, count, scheduler) {\n  if (count == null) {\n    count = start;\n    start = 0;\n  }\n\n  if (count <= 0) {\n    return EMPTY;\n  }\n\n  const end = count + start;\n  return new Observable(scheduler ? subscriber => {\n    let n = start;\n    return scheduler.schedule(function () {\n      if (n < end) {\n        subscriber.next(n++);\n        this.schedule();\n      } else {\n        subscriber.complete();\n      }\n    });\n  } : subscriber => {\n    let n = start;\n\n    while (n < end && !subscriber.closed) {\n      subscriber.next(n++);\n    }\n\n    subscriber.complete();\n  });\n}", "map": {"version": 3, "names": ["Observable", "EMPTY", "range", "start", "count", "scheduler", "end", "subscriber", "n", "schedule", "next", "complete", "closed"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/rxjs/dist/esm/internal/observable/range.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { EMPTY } from './empty';\nexport function range(start, count, scheduler) {\n    if (count == null) {\n        count = start;\n        start = 0;\n    }\n    if (count <= 0) {\n        return EMPTY;\n    }\n    const end = count + start;\n    return new Observable(scheduler\n        ?\n            (subscriber) => {\n                let n = start;\n                return scheduler.schedule(function () {\n                    if (n < end) {\n                        subscriber.next(n++);\n                        this.schedule();\n                    }\n                    else {\n                        subscriber.complete();\n                    }\n                });\n            }\n        :\n            (subscriber) => {\n                let n = start;\n                while (n < end && !subscriber.closed) {\n                    subscriber.next(n++);\n                }\n                subscriber.complete();\n            });\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,KAAT,QAAsB,SAAtB;AACA,OAAO,SAASC,KAAT,CAAeC,KAAf,EAAsBC,KAAtB,EAA6BC,SAA7B,EAAwC;EAC3C,IAAID,KAAK,IAAI,IAAb,EAAmB;IACfA,KAAK,GAAGD,KAAR;IACAA,KAAK,GAAG,CAAR;EACH;;EACD,IAAIC,KAAK,IAAI,CAAb,EAAgB;IACZ,OAAOH,KAAP;EACH;;EACD,MAAMK,GAAG,GAAGF,KAAK,GAAGD,KAApB;EACA,OAAO,IAAIH,UAAJ,CAAeK,SAAS,GAEtBE,UAAD,IAAgB;IACZ,IAAIC,CAAC,GAAGL,KAAR;IACA,OAAOE,SAAS,CAACI,QAAV,CAAmB,YAAY;MAClC,IAAID,CAAC,GAAGF,GAAR,EAAa;QACTC,UAAU,CAACG,IAAX,CAAgBF,CAAC,EAAjB;QACA,KAAKC,QAAL;MACH,CAHD,MAIK;QACDF,UAAU,CAACI,QAAX;MACH;IACJ,CARM,CAAP;EASH,CAbsB,GAetBJ,UAAD,IAAgB;IACZ,IAAIC,CAAC,GAAGL,KAAR;;IACA,OAAOK,CAAC,GAAGF,GAAJ,IAAW,CAACC,UAAU,CAACK,MAA9B,EAAsC;MAClCL,UAAU,CAACG,IAAX,CAAgBF,CAAC,EAAjB;IACH;;IACDD,UAAU,CAACI,QAAX;EACH,CArBF,CAAP;AAsBH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}