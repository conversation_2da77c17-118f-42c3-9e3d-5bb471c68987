{"ast": null, "code": "import { EventEmitter, ElementRef } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/card\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/tooltip\";\nconst _c0 = [\"pickerRef\"];\n\nfunction EmojiPickerComponent_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function EmojiPickerComponent_button_10_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r5);\n      const category_r3 = restoredCtx.$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.setActiveCategory(category_r3.key));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const category_r3 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.activeCategory === category_r3.key);\n    i0.ɵɵproperty(\"matTooltip\", category_r3.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r3.icon);\n  }\n}\n\nfunction EmojiPickerComponent_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function EmojiPickerComponent_button_12_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r8);\n      const emoji_r6 = restoredCtx.$implicit;\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onEmojiClick({\n        native: emoji_r6\n      }));\n    });\n    i0.ɵɵelementStart(1, \"span\", 11);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const emoji_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"matTooltip\", emoji_r6);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(emoji_r6);\n  }\n}\n\nexport let EmojiPickerComponent = /*#__PURE__*/(() => {\n  class EmojiPickerComponent {\n    constructor() {\n      this.onEmojiSelect = new EventEmitter();\n      this.onClose = new EventEmitter();\n      this.activeCategory = 'people'; // Emoji categories\n\n      this.emojiCategories = {\n        people: ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣', '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓'],\n        nature: ['🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🐨', '🐯', '🦁', '🐮', '🐷', '🐸', '🐵', '🙈', '🙉', '🙊', '🐒', '🦍', '🦧', '🐕', '🐩', '🦮', '🐕‍🦺', '🐈', '🐓', '🦃', '🦚', '🦜', '🦢', '🦩', '🐦', '🐧', '🕊️', '🦅', '🦆', '🦉', '🦇', '🐺', '🐗', '🐴', '🦄', '🐝', '🐛', '🦋', '🐌', '🐞', '🐜', '🦟', '🦗', '🕷️', '🕸️', '🦂', '🐢', '🐍', '🦎', '🦖', '🦕', '🐙', '🦑', '🦐', '🦞', '🦀', '🐡', '🐠', '🐟', '🐬', '🐳', '🐋', '🦈', '🐊', '🐅', '🐆', '🦓', '🦍', '🐘', '🦛', '🦏', '🐪', '🐫', '🦒', '🦘', '🐃', '🐂', '🐄', '🐎', '🐖', '🐏', '🐑', '🦙', '🐐', '🦌', '🐕', '🐩', '🐈', '🐓', '🦃', '🦚', '🦜', '🦢', '🦩', '🐦', '🐧', '🕊️', '🦅', '🦆', '🦉', '🦇'],\n        food: ['🍎', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓', '🫐', '🍈', '🍒', '🍑', '🥭', '🍍', '🥥', '🥝', '🍅', '🍆', '🥑', '🥦', '🥬', '🥒', '🌶️', '🫑', '🌽', '🥕', '🫒', '🧄', '🧅', '🥔', '🍠', '🥐', '🥖', '🍞', '🥨', '🥯', '🧀', '🥚', '🍳', '🧈', '🥞', '🧇', '🥓', '🥩', '🍗', '🍖', '🦴', '🌭', '🍔', '🍟', '🍕', '🫓', '🥙', '🌮', '🌯', '🫔', '🥗', '🥘', '🫕', '🥫', '🍝', '🍜', '🍲', '🍛', '🍣', '🍱', '🥟', '🦪', '🍤', '🍙', '🍚', '🍘', '🍥', '🥠', '🥮', '🍢', '🍡', '🍧', '🍨', '🍦', '🥧', '🧁', '🍰', '🎂', '🍮', '🍭', '🍬', '🍫', '🍿', '🍩', '🍪', '🌰', '🥜', '🍯'],\n        activity: ['⚽', '🏀', '🏈', '⚾', '🥎', '🎾', '🏐', '🏉', '🎱', '🪀', '🏓', '🏸', '🏒', '🏑', '🥍', '🏏', '🪃', '🥅', '⛳', '🪁', '🏹', '🎣', '🤿', '🥊', '🥋', '🎽', '🛹', '🛷', '⛸️', '🥌', '🎿', '⛷️', '🏂', '🪂', '🏋️‍♀️', '🏋️‍♂️', '🤼‍♀️', '🤼‍♂️', '🤸‍♀️', '🤸‍♂️', '⛹️‍♀️', '⛹️‍♂️', '🤺', '🤾‍♀️', '🤾‍♂️', '🏌️‍♀️', '🏌️‍♂️', '🏇', '🧘‍♀️', '🧘‍♂️', '🏄‍♀️', '🏄‍♂️', '🏊‍♀️', '🏊‍♂️', '🤽‍♀️', '🤽‍♂️', '🚣‍♀️', '🚣‍♂️', '🧗‍♀️', '🧗‍♂️', '🚵‍♀️', '🚵‍♂️', '🚴‍♀️', '🚴‍♂️', '🏆', '🥇', '🥈', '🥉', '🏅', '🎖', '🏵', '🎗', '🎫', '🎟', '🎪', '🤹', '🤹‍♀️', '🤹‍♂️', '🎭', '🩰', '🎨', '🎬', '🎤', '🎧', '🎼', '🎵', '🎶', '🪘', '🥁', '🎷', '🎺', '🎸', '🪕', '🎻', '🎲', '♠️', '♥️', '♦️', '♣️', '♟️', '🃏', '🀄', '🎴', '🎯', '🎳', '🎮', '🕹️', '🎰', '🧩'],\n        objects: ['⌚', '📱', '📲', '💻', '⌨️', '🖥️', '🖨️', '🖱️', '🖲️', '🕹️', '🗜️', '💽', '💾', '💿', '📀', '📼', '📷', '📸', '📹', '🎥', '📽️', '🎞️', '📞', '☎️', '📟', '📠', '📺', '📻', '🎙️', '🎚️', '🎛️', '🧭', '⏱️', '⏲️', '⏰', '🕰️', '⌛', '⏳', '📡', '🔋', '🔌', '💡', '🔦', '🕯️', '🪔', '🧯', '🛢️', '💸', '💵', '💴', '💶', '💷', '💰', '💳', '💎', '⚖️', '🧰', '🔧', '🔨', '⚒️', '🛠️', '⛏️', '🪚', '🔩', '⚙️', '🪤', '🧱', '⛓️', '🧲', '🔫', '💣', '🧨', '🪓', '🔪', '🗡️', '⚔️', '🛡️', '🚬', '⚰️', '🪦', '⚱️', '🏺', '🔮', '📿', '🧿', '💈', '⚗️', '🔭', '🔬', '🕳️', '🩹', '🩺', '💊', '💉', '🧬', '🦠', '🧫', '🧪', '🌡️', '🧹', '🧺', '🧻', '🚽', '🚰', '🚿', '🛁', '🛀', '🧴', '🧷', '🧸', '🧵', '🧶', '🪡', '🪢', '🪣', '🪤', '🪥', '🪦', '🪧'],\n        symbols: ['❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔', '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '☮️', '✝️', '☪️', '🕉️', '☸️', '✡️', '🔯', '🕎', '☯️', '☦️', '🛐', '⛎', '♈', '♉', '♊', '♋', '♌', '♍', '♎', '♏', '♐', '♑', '♒', '♓', '🆔', '⚛️', '🉑', '☢️', '☣️', '📴', '📳', '🈶', '🈚', '🈸', '🈺', '🈷️', '✴️', '🆚', '💮', '🉐', '㊙️', '㊗️', '🈴', '🈵', '🈹', '🈲', '🅰️', '🅱️', '🆎', '🆑', '🅾️', '🆘', '❌', '⭕', '🛑', '⛔', '📛', '🚫', '💯', '💢', '♨️', '🚷', '🚯', '🚳', '🚱', '🔞', '📵', '🚭', '❗', '❕', '❓', '❔', '‼️', '⁉️', '🔅', '🔆', '〽️', '⚠️', '🚸', '🔱', '⚜️', '🔰', '♻️', '✅', '🈯', '💹', '❇️', '✳️', '❎', '🌐', '💠', 'Ⓜ️', '🌀', '💤', '🏧', '🚾', '♿', '🅿️', '🈳', '🈂️', '🛂', '🛃', '🛄', '🛅', '🚹', '🚺', '🚼', '🚻', '🚮', '🎦', '📶', '🈁', '🔣', '🔤', '🔠', '🔡', '🔢', '🔟']\n      };\n    }\n\n    ngAfterViewInit() {\n      // Set up click outside listener with delay to prevent immediate closing\n      setTimeout(() => {\n        this.clickListener = event => {\n          if (this.pickerRef && !this.pickerRef.nativeElement.contains(event.target)) {\n            this.onClose.emit();\n          }\n        };\n\n        document.addEventListener('mousedown', this.clickListener);\n      }, 100);\n    }\n\n    ngOnDestroy() {\n      if (this.clickListener) {\n        document.removeEventListener('mousedown', this.clickListener);\n      }\n    }\n\n    onEmojiClick(emoji) {\n      this.onEmojiSelect.emit(emoji);\n    }\n\n    setActiveCategory(category) {\n      this.activeCategory = category;\n    }\n\n    getCategories() {\n      return [{\n        key: 'people',\n        name: 'People',\n        icon: '😀'\n      }, {\n        key: 'nature',\n        name: 'Nature',\n        icon: '🐶'\n      }, {\n        key: 'food',\n        name: 'Food',\n        icon: '🍎'\n      }, {\n        key: 'activity',\n        name: 'Activity',\n        icon: '⚽'\n      }, {\n        key: 'objects',\n        name: 'Objects',\n        icon: '⌚'\n      }, {\n        key: 'symbols',\n        name: 'Symbols',\n        icon: '❤️'\n      }];\n    }\n\n    getCurrentEmojis() {\n      return this.emojiCategories[this.activeCategory] || [];\n    }\n\n  }\n\n  EmojiPickerComponent.ɵfac = function EmojiPickerComponent_Factory(t) {\n    return new (t || EmojiPickerComponent)();\n  };\n\n  EmojiPickerComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: EmojiPickerComponent,\n    selectors: [[\"app-emoji-picker\"]],\n    viewQuery: function EmojiPickerComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.pickerRef = _t.first);\n      }\n    },\n    outputs: {\n      onEmojiSelect: \"onEmojiSelect\",\n      onClose: \"onClose\"\n    },\n    decls: 13,\n    vars: 2,\n    consts: [[1, \"emoji-picker-container\", 3, \"click\"], [\"pickerRef\", \"\"], [1, \"emoji-picker-header\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Close\", 1, \"close-button\", 3, \"click\"], [1, \"emoji-picker-content\"], [1, \"emoji-categories\"], [\"mat-icon-button\", \"\", \"class\", \"category-button\", 3, \"active\", \"matTooltip\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"emoji-grid\"], [\"mat-icon-button\", \"\", \"class\", \"emoji-button\", 3, \"matTooltip\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"mat-icon-button\", \"\", 1, \"category-button\", 3, \"matTooltip\", \"click\"], [\"mat-icon-button\", \"\", 1, \"emoji-button\", 3, \"matTooltip\", \"click\"], [1, \"emoji-text\"]],\n    template: function EmojiPickerComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"mat-card\", 0, 1);\n        i0.ɵɵlistener(\"click\", function EmojiPickerComponent_Template_mat_card_click_0_listener($event) {\n          return $event.stopPropagation();\n        });\n        i0.ɵɵelementStart(2, \"mat-card-header\", 2)(3, \"mat-card-title\");\n        i0.ɵɵtext(4, \"Choose an emoji\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"button\", 3);\n        i0.ɵɵlistener(\"click\", function EmojiPickerComponent_Template_button_click_5_listener() {\n          return ctx.onClose.emit();\n        });\n        i0.ɵɵelementStart(6, \"mat-icon\");\n        i0.ɵɵtext(7, \"close\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(8, \"mat-card-content\", 4)(9, \"div\", 5);\n        i0.ɵɵtemplate(10, EmojiPickerComponent_button_10_Template, 3, 4, \"button\", 6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"div\", 7);\n        i0.ɵɵtemplate(12, EmojiPickerComponent_button_12_Template, 3, 2, \"button\", 8);\n        i0.ɵɵelementEnd()()();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"ngForOf\", ctx.getCategories());\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.getCurrentEmojis());\n      }\n    },\n    dependencies: [i1.NgForOf, i2.MatCard, i2.MatCardHeader, i2.MatCardContent, i2.MatCardTitle, i3.MatButton, i4.MatIcon, i5.MatTooltip],\n    styles: [\".emoji-picker-container[_ngcontent-%COMP%]{background:white;border:1px solid #e0e0e0;border-radius:12px;box-shadow:0 8px 32px #0000001f;max-width:320px;width:100%;z-index:1000!important;animation:emojiPickerSlideIn .3s cubic-bezier(.4,0,.2,1);position:relative;pointer-events:auto!important}.emoji-picker-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:16px;border-bottom:1px solid #e0e0e0;background:#f8f9fa}.emoji-picker-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]{font-size:1rem;font-weight:600;color:#333;margin:0}.close-button[_ngcontent-%COMP%]{color:#666;transition:all .2s ease}.close-button[_ngcontent-%COMP%]:hover{background:#e0e0e0;color:#333}.emoji-categories[_ngcontent-%COMP%]{display:flex;gap:4px;padding:8px;background:#f8f9fa;border-bottom:1px solid #e0e0e0;overflow-x:auto;scrollbar-width:none;-ms-overflow-style:none}.emoji-categories[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}.category-button[_ngcontent-%COMP%]{min-width:40px;height:40px;transition:all .2s ease;flex-shrink:0}.category-button[_ngcontent-%COMP%]:hover{background:#e0e0e0}.category-button.active[_ngcontent-%COMP%]{background:#3f51b5;color:#fff}.emoji-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(8,1fr);gap:4px;max-height:200px;overflow-y:auto;padding:16px;scrollbar-width:thin;scrollbar-color:#cbd5e1 #f1f5f9}.emoji-grid[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px}.emoji-grid[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#f1f5f9}.emoji-grid[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#cbd5e1;border-radius:3px}.emoji-grid[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#94a3b8}.emoji-button[_ngcontent-%COMP%]{min-width:32px;height:32px;transition:all .2s ease}.emoji-button[_ngcontent-%COMP%]:hover{background:#e0e0e0;transform:scale(1.1)}.emoji-text[_ngcontent-%COMP%]{font-size:1.2rem}@keyframes emojiPickerSlideIn{0%{opacity:0;transform:translateY(-20px) scale(.95)}to{opacity:1;transform:translateY(0) scale(1)}}@media (max-width: 768px){.emoji-picker-container[_ngcontent-%COMP%]{max-width:280px}.emoji-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(7,1fr);gap:3px}.emoji-button[_ngcontent-%COMP%]{min-width:28px;height:28px}.category-button[_ngcontent-%COMP%]{min-width:36px;height:36px}}@media (max-width: 480px){.emoji-picker-container[_ngcontent-%COMP%]{max-width:260px}.emoji-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(6,1fr);gap:2px;max-height:180px}.emoji-button[_ngcontent-%COMP%]{min-width:24px;height:24px}.category-button[_ngcontent-%COMP%]{min-width:32px;height:32px}.emoji-text[_ngcontent-%COMP%]{font-size:1rem}}*[_ngcontent-%COMP%]{transition:all .3s cubic-bezier(.4,0,.2,1)}\"]\n  });\n  return EmojiPickerComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}