<!-- Login Form -->
<app-login-form *ngIf="!isLoggedIn"></app-login-form>

<!-- Material Design Chat Interface -->
<mat-sidenav-container *ngIf="isLoggedIn" class="chat-container">
  <!-- Sidebar -->
  <mat-sidenav #sidenav mode="side" opened="true" class="chat-sidebar">
    <app-sidebar 
      [onlineUsers]="onlineUsers"
      [currentUser]="user"
      [isAdmin]="isAdmin"
      [groups]="groups"
      [currentGroup]="currentGroup"
      [onJoinGroup]="onJoinGroup.bind(this)"
      (onPasswordChange)="onPasswordChange()"
      (onLogout)="onLogout()"
    ></app-sidebar>
  </mat-sidenav>

  <!-- Main Chat Area -->
  <mat-sidenav-content class="chat-main-content">
    <!-- Chat Header -->
    <mat-toolbar class="chat-header">
      <button mat-icon-button (click)="sidenav.toggle()" class="menu-button">
        <mat-icon>menu</mat-icon>
      </button>
      
      <div class="header-content">
        <div class="group-info">
          <h1 class="group-name">
            {{ currentGroup?.name || 'Select a Group' }}
          </h1>
          <p *ngIf="!currentGroup" class="no-group-message">
            Please select a group to start chatting
          </p>
          <p *ngIf="currentGroup?.description" class="group-description">
            {{ currentGroup?.description }}
          </p>
        </div>
        
        <div class="header-actions">
          <div class="connection-status">
            <mat-icon [class.connected]="connected" class="status-icon">
              {{ connected ? 'wifi' : 'wifi_off' }}
            </mat-icon>
            <span class="status-text">
              {{ connected ? 'Connected' : 'Disconnected' }}
            </span>
          </div>
          
          <mat-chip-list>
            <mat-chip class="online-count">
              {{ onlineUsers.length || 0 }} online
            </mat-chip>
          </mat-chip-list>
          
          <button
            *ngIf="isAdmin"
            (click)="onShowAdminPanel()"
            mat-icon-button
            matTooltip="Admin Panel"
          >
            <mat-icon>admin_panel_settings</mat-icon>
          </button>
        </div>
      </div>
    </mat-toolbar>

    <!-- Messages Area -->
    <div class="messages-container">
      <!-- Use virtual scrolling for better performance with large message lists -->
      <app-message-list-virtual
        *ngIf="useVirtualScrolling"
        [messages]="messages"
        [currentUser]="user"
        [loading]="loading"
        [highlightedMessageId]="highlightedMessageId"
        (onReply)="onReply($event)"
        (onAddReaction)="onAddReaction($event)"
        (onRemoveReaction)="onRemoveReaction($event)"
        (onEdit)="onEdit($event)"
        (onDelete)="onDelete($event)"
        (onMessageClick)="onMessageClick($event)"
      ></app-message-list-virtual>
      
      <!-- Fallback to regular message list for smaller lists -->
      <app-message-list
        *ngIf="!useVirtualScrolling"
        [messages]="messages"
        [currentUser]="user"
        [loading]="loading"
        [highlightedMessageId]="highlightedMessageId"
        (onReply)="onReply($event)"
        (onAddReaction)="onAddReaction($event)"
        (onRemoveReaction)="onRemoveReaction($event)"
        (onEdit)="onEdit($event)"
        (onDelete)="onDelete($event)"
        (onMessageClick)="onMessageClick($event)"
      ></app-message-list>
    </div>

    <!-- Typing Indicator -->
    <div *ngIf="typingUsers && typingUsers.length > 0" class="typing-indicator">
      <mat-card class="typing-card">
        <mat-card-content>
          <div class="typing-content">
            <div class="typing-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
            <span class="typing-text">
              {{ getTypingText(typingUsers) }}
            </span>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Message Input Area -->
    <div class="input-container">
      <app-message-input
        *ngIf="currentGroup"
        [replyTo]="replyTo"
        (onSendMessage)="onSendMessage($event)"
        (onCancelReply)="onCancelReply()"
      ></app-message-input>
      
      <div *ngIf="!currentGroup" class="no-group-input">
        <mat-card>
          <mat-card-content>
            <div class="group-selection">
              <h3>Select a group to start chatting</h3>
              <mat-form-field appearance="outline" class="group-selector">
                <mat-label>Choose a group</mat-label>
                <mat-select (selectionChange)="onJoinGroup($event.value)" [value]="null">
                  <mat-option *ngFor="let group of groups" [value]="group.id">
                    {{ group.name }}
                    <span *ngIf="group.description" class="group-description-small"> - {{ group.description }}</span>
                  </mat-option>
                </mat-select>
              </mat-form-field>
              <p *ngIf="groups?.length === 0" class="no-groups-message">
                No groups available. Contact an administrator to get access to groups.
              </p>

            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  </mat-sidenav-content>
</mat-sidenav-container>

<!-- Admin Panel Modal -->
<app-admin-panel
  *ngIf="showAdminPanel"
  [currentUser]="user"
  (onClose)="onHideAdminPanel()"
></app-admin-panel>

<!-- Password Change Modal -->
<div *ngIf="showPasswordChange" class="modal-overlay">
  <mat-card class="modal-card">
    <mat-card-header>
      <mat-card-title>Change Password</mat-card-title>
      <button
        (click)="onClosePasswordChange()"
        mat-icon-button
        class="close-button"
      >
        <mat-icon>close</mat-icon>
      </button>
    </mat-card-header>
    <mat-card-content>
      <app-password-change></app-password-change>
    </mat-card-content>
  </mat-card>
</div>

<!-- Message Edit Modal -->
<app-message-edit
  *ngIf="editingMessage"
  [message]="editingMessage"
  (onSave)="onSaveEdit($event)"
  (onCancel)="onCancelEdit()"
></app-message-edit>