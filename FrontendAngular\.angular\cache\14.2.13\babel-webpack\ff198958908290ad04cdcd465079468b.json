{"ast": null, "code": "import { io } from 'socket.io-client';\nimport { environment } from '../../environments/environment';\nimport { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport let SocketService = /*#__PURE__*/(() => {\n  class SocketService {\n    constructor() {\n      this.socket = null;\n      this.isConnectedSubject = new BehaviorSubject(false);\n      this.isConnected$ = this.isConnectedSubject.asObservable();\n    }\n\n    connect(username, password, inviteCode = null) {\n      if (this.socket) {\n        this.disconnect();\n      }\n\n      this.socket = io(environment.backendUrl, {\n        transports: ['websocket', 'polling'],\n        timeout: 20000,\n        auth: {\n          username,\n          password,\n          inviteCode\n        }\n      });\n      this.socket.on('connect', () => {\n        console.log('Connected to server');\n        this.isConnectedSubject.next(true);\n        this.socket?.emit('join', {\n          username,\n          inviteCode\n        });\n        console.log('Emitted join event for:', username);\n      });\n      this.socket.on('disconnect', () => {\n        console.log('Disconnected from server');\n        this.isConnectedSubject.next(false);\n      });\n      this.socket.on('connect_error', error => {\n        console.error('Socket connection error:', error);\n        this.isConnectedSubject.next(false);\n      });\n      this.socket.on('error', error => {\n        console.error('Socket error:', error);\n      });\n      return this.socket;\n    }\n\n    disconnect() {\n      if (this.socket) {\n        this.socket.disconnect();\n        this.socket = null;\n        this.isConnectedSubject.next(false);\n      }\n    }\n\n    joinGroup(groupId) {\n      if (this.socket && this.isConnectedSubject.value) {\n        this.socket.emit('joinGroup', {\n          groupId\n        });\n      }\n    }\n\n    sendMessage(text, groupId, replyTo = null) {\n      console.log('Attempting to send message:', {\n        text,\n        groupId,\n        replyTo\n      });\n      console.log('Socket connected:', this.isConnectedSubject.value);\n      console.log('Socket exists:', !!this.socket);\n\n      if (this.socket && this.isConnectedSubject.value) {\n        this.socket.emit('sendMessage', {\n          text,\n          groupId,\n          replyTo\n        });\n        console.log('Message sent via socket');\n      } else {\n        console.error('Cannot send message - socket not connected or not available');\n      }\n    }\n\n    addReaction(messageId, emoji) {\n      if (this.socket && this.isConnectedSubject.value) {\n        this.socket.emit('addReaction', {\n          messageId,\n          emoji\n        });\n      }\n    }\n\n    removeReaction(data) {\n      if (this.socket && this.isConnectedSubject.value) {\n        this.socket.emit('removeReaction', data);\n      }\n    }\n\n    on(event, callback) {\n      if (this.socket) {\n        this.socket.on(event, callback);\n      }\n    }\n\n    off(event, callback) {\n      if (this.socket) {\n        this.socket.off(event, callback);\n      }\n    }\n\n    emit(event, data) {\n      if (this.socket && this.isConnectedSubject.value) {\n        this.socket.emit(event, data);\n      }\n    }\n\n  }\n\n  SocketService.ɵfac = function SocketService_Factory(t) {\n    return new (t || SocketService)();\n  };\n\n  SocketService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: SocketService,\n    factory: SocketService.ɵfac,\n    providedIn: 'root'\n  });\n  return SocketService;\n})();", "map": null, "metadata": {}, "sourceType": "module"}