# Chateye Backend

Node.js + Express backend with Socket.IO for the <PERSON><PERSON><PERSON> chat application.

## Quick Start

1. Install dependencies:
   ```bash
   npm install
   ```

2. Set up environment variables:
   ```bash
   cp env.example .env
   ```

3. Update `.env` with your database credentials

4. Run database migration:
   ```bash
   npm run migrate
   ```

5. Start development server:
   ```bash
   npm run dev
   ```

## Environment Variables

- `PORT` - Server port (default: 3001)
- `DATABASE_URL` - PostgreSQL connection string
- `FRONTEND_URL` - Frontend URL for CORS
- `NODE_ENV` - Environment (development/production)

## Scripts

- `npm start` - Start production server
- `npm run dev` - Start development server with nodemon
- `npm run migrate` - Run database migrations

## Deployment on Render

1. Create a new Web Service
2. Connect your repository
3. Set root directory to `Backend`
4. Use build command: `npm install`
5. Use start command: `npm start`
6. Add environment variables
7. Create PostgreSQL database and set DATABASE_URL
