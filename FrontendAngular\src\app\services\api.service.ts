import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

export interface Message {
  id: string;
  text: string;
  username: string;
  timestamp: string;
  groupId: string;
  replyTo?: string;
  replyUsername?: string;
  replyText?: string;
  reactions?: Reaction[];
  updated_at?: string;
}

export interface Reaction {
  id: string;
  emoji: string;
  username: string;
  userId: string;
}

export interface Group {
  id: string;
  name: string;
  description?: string;
  created_by_username?: string;
}

export interface User {
  id: string;
  username: string;
}

export interface SecurityInfo {
  mode: 'open' | 'invite' | 'whitelist';
  requiresInvite: boolean;
  requiresWhitelist: boolean;
}

export interface LoginResponse {
  username: string;
  isAdmin: boolean;
  securityInfo: SecurityInfo;
  requiresPassword?: boolean;
}

export interface AllowedUser {
  username: string;
  added_by: string;
  added_at: string;
  is_active: boolean;
}

export interface InviteCode {
  code: string;
  max_uses: number;
  current_uses: number;
  status: 'active' | 'expired' | 'deactivated';
  created_by: string;
  created_at: string;
  expires_at?: string;
}

export interface GroupUser {
  username: string;
  granted_at: string;
}

export interface UserWithPassword {
  id: string;
  username: string;
  online_status: boolean;
  last_seen: string;
  hasPassword: boolean;
}

export interface PasswordChangeRequest {
  username: string;
  currentPassword: string;
  newPassword: string;
}

export interface PasswordSetRequest {
  username: string;
  targetUsername: string;
  password: string;
}

export interface PasswordResetRequest {
  username: string;
  targetUsername: string;
  newPassword: string;
}

export interface UserCreateRequest {
  username: string;
  targetUsername: string;
  password: string;
}

@Injectable({
  providedIn: 'root'
})
export class ApiService {
  private baseUrl = `${environment.backendUrl}/api`;

  constructor(private http: HttpClient) {}

  // Messages API
  getMessages(groupId: string, limit: number = 50): Observable<Message[]> {
    return this.http.get<Message[]>(`${this.baseUrl}/messages/${groupId}?limit=${limit}`);
  }

  sendMessage(text: string, username: string, groupId: string, replyTo: string | null = null): Observable<any> {
    return this.http.post(`${this.baseUrl}/messages`, { text, username, groupId, replyTo });
  }

  addReaction(messageId: string, emoji: string, username: string): Observable<any> {
    return this.http.post(`${this.baseUrl}/messages/${messageId}/reactions`, { emoji, username });
  }

  removeReaction(messageId: string, username: string): Observable<any> {
    return this.http.delete(`${this.baseUrl}/messages/${messageId}/reactions`, { 
      body: { username } 
    });
  }

  updateMessage(messageId: string, text: string, username: string): Observable<any> {
    return this.http.put(`${this.baseUrl}/messages/${messageId}`, { text, username });
  }

  deleteMessage(messageId: string, username: string): Observable<any> {
    return this.http.delete(`${this.baseUrl}/messages/${messageId}`, { 
      body: { username } 
    });
  }

  getUserGroups(username: string): Observable<Group[]> {
    return this.http.get<Group[]>(`${this.baseUrl}/messages/groups/${username}`);
  }

  // Users API
  getOnlineUsers(): Observable<User[]> {
    return this.http.get<User[]>(`${this.baseUrl}/users/online`);
  }

  loginUser(username: string, password?: string, inviteCode?: string): Observable<LoginResponse> {
    const body: any = { username };
    if (password) {
      body.password = password;
    }
    if (inviteCode) {
      body.inviteCode = inviteCode;
    }
    return this.http.post<LoginResponse>(`${this.baseUrl}/users`, body);
  }

  getSecurityInfo(): Observable<SecurityInfo> {
    return this.http.get<SecurityInfo>(`${this.baseUrl}/admin/security-info`);
  }

  // Admin API
  getAllowedUsers(username: string): Observable<AllowedUser[]> {
    return this.http.get<AllowedUser[]>(`${this.baseUrl}/admin/allowed-users`, {
      params: { username }
    });
  }

  addAllowedUser(username: string, targetUsername: string): Observable<any> {
    return this.http.post(`${this.baseUrl}/admin/allowed-users`, {
      username,
      targetUsername
    });
  }

  removeAllowedUser(username: string, targetUsername: string): Observable<any> {
    return this.http.delete(`${this.baseUrl}/admin/allowed-users/${targetUsername}`, {
      body: { username }
    });
  }

  getInviteCodes(username: string): Observable<InviteCode[]> {
    return this.http.get<InviteCode[]>(`${this.baseUrl}/admin/invite-codes`, {
      params: { username }
    });
  }

  createInviteCode(username: string, config: any): Observable<{ code: string }> {
    return this.http.post<{ code: string }>(`${this.baseUrl}/admin/invite-codes`, {
      username,
      ...config
    });
  }

  deactivateInviteCode(username: string, code: string): Observable<any> {
    return this.http.delete(`${this.baseUrl}/admin/invite-codes/${code}`, {
      body: { username }
    });
  }

  getGroups(username: string): Observable<Group[]> {
    return this.http.get<Group[]>(`${this.baseUrl}/admin/groups`, {
      params: { username }
    });
  }

  createGroup(username: string, name: string, description?: string): Observable<any> {
    return this.http.post(`${this.baseUrl}/admin/groups`, {
      username,
      name,
      description
    });
  }

  deleteGroup(username: string, groupId: string): Observable<any> {
    return this.http.delete(`${this.baseUrl}/admin/groups/${groupId}`, {
      body: { username }
    });
  }

  getGroupUsers(username: string, groupId: string): Observable<GroupUser[]> {
    return this.http.get<GroupUser[]>(`${this.baseUrl}/admin/groups/${groupId}/users`, {
      params: { username }
    });
  }

  grantUserAccess(username: string, groupId: string, targetUsername: string): Observable<any> {
    return this.http.post(`${this.baseUrl}/admin/groups/${groupId}/users`, {
      username,
      targetUsername
    });
  }

  revokeUserAccess(username: string, groupId: string, targetUsername: string): Observable<any> {
    return this.http.delete(`${this.baseUrl}/admin/groups/${groupId}/users/${targetUsername}`, {
      body: { username }
    });
  }

  // Password management methods
  changePassword(request: PasswordChangeRequest): Observable<any> {
    return this.http.post(`${this.baseUrl}/passwords/change`, request);
  }

  setPassword(request: PasswordSetRequest): Observable<any> {
    return this.http.post(`${this.baseUrl}/passwords/set`, request);
  }

  resetPassword(request: PasswordResetRequest): Observable<any> {
    return this.http.post(`${this.baseUrl}/passwords/reset`, request);
  }

  createUserWithPassword(request: UserCreateRequest): Observable<any> {
    return this.http.post(`${this.baseUrl}/passwords/create-user`, request);
  }

  checkPasswordStatus(username: string): Observable<{ hasPassword: boolean }> {
    return this.http.get<{ hasPassword: boolean }>(`${this.baseUrl}/passwords/check/${username}`);
  }

  // Admin user management
  getUsersWithPasswordStatus(username: string): Observable<UserWithPassword[]> {
    return this.http.get<UserWithPassword[]>(`${this.baseUrl}/admin/users`, {
      params: { username }
    });
  }

  createUser(username: string, targetUsername: string, password: string): Observable<any> {
    return this.http.post(`${this.baseUrl}/admin/users`, {
      username,
      targetUsername,
      password
    });
  }

  resetUserPassword(username: string, targetUsername: string, newPassword: string): Observable<any> {
    return this.http.post(`${this.baseUrl}/admin/users/${targetUsername}/reset-password`, {
      username,
      newPassword
    });
  }

  setUserPassword(username: string, targetUsername: string, password: string): Observable<any> {
    return this.http.post(`${this.baseUrl}/admin/users/${targetUsername}/set-password`, {
      username,
      password
    });
  }

  // Security configuration methods
  getSecurityConfig(username: string): Observable<any> {
    return this.http.get(`${this.baseUrl}/admin/security-config`, {
      params: { username }
    });
  }

  updateSecurityConfig(username: string, config: { securityMode: string; adminUsers: string[] }): Observable<any> {
    return this.http.put(`${this.baseUrl}/admin/security-config`, {
      username,
      securityMode: config.securityMode,
      adminUsers: config.adminUsers
    });
  }
}
