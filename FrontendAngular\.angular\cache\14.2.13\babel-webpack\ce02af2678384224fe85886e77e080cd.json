{"ast": null, "code": "import { Observable } from '../Observable';\nimport { isFunction } from '../util/isFunction';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nexport function fromEventPattern(addHandler, removeHandler, resultSelector) {\n  if (resultSelector) {\n    return fromEventPattern(addHand<PERSON>, removeHandler).pipe(mapOneOrManyArgs(resultSelector));\n  }\n\n  return new Observable(subscriber => {\n    const handler = (...e) => subscriber.next(e.length === 1 ? e[0] : e);\n\n    const retValue = addHandler(handler);\n    return isFunction(removeHandler) ? () => removeHandler(handler, retValue) : undefined;\n  });\n}", "map": {"version": 3, "names": ["Observable", "isFunction", "mapOneOrManyArgs", "fromEventPattern", "add<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "resultSelector", "pipe", "subscriber", "handler", "e", "next", "length", "retValue", "undefined"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/rxjs/dist/esm/internal/observable/fromEventPattern.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { isFunction } from '../util/isFunction';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nexport function fromEventPattern(addHandler, removeHandler, resultSelector) {\n    if (resultSelector) {\n        return fromEventPattern(addHandler, removeHandler).pipe(mapOneOrManyArgs(resultSelector));\n    }\n    return new Observable((subscriber) => {\n        const handler = (...e) => subscriber.next(e.length === 1 ? e[0] : e);\n        const retValue = addHandler(handler);\n        return isFunction(removeHandler) ? () => removeHandler(handler, retValue) : undefined;\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,UAAT,QAA2B,oBAA3B;AACA,SAASC,gBAAT,QAAiC,0BAAjC;AACA,OAAO,SAASC,gBAAT,CAA0BC,UAA1B,EAAsCC,aAAtC,EAAqDC,cAArD,EAAqE;EACxE,IAAIA,cAAJ,EAAoB;IAChB,OAAOH,gBAAgB,CAACC,UAAD,EAAaC,aAAb,CAAhB,CAA4CE,IAA5C,CAAiDL,gBAAgB,CAACI,cAAD,CAAjE,CAAP;EACH;;EACD,OAAO,IAAIN,UAAJ,CAAgBQ,UAAD,IAAgB;IAClC,MAAMC,OAAO,GAAG,CAAC,GAAGC,CAAJ,KAAUF,UAAU,CAACG,IAAX,CAAgBD,CAAC,CAACE,MAAF,KAAa,CAAb,GAAiBF,CAAC,CAAC,CAAD,CAAlB,GAAwBA,CAAxC,CAA1B;;IACA,MAAMG,QAAQ,GAAGT,UAAU,CAACK,OAAD,CAA3B;IACA,OAAOR,UAAU,CAACI,aAAD,CAAV,GAA4B,MAAMA,aAAa,CAACI,OAAD,EAAUI,QAAV,CAA/C,GAAqEC,SAA5E;EACH,CAJM,CAAP;AAKH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}