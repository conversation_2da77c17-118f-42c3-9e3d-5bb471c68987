{"ast": null, "code": "// imported from https://github.com/galkn/querystring\n\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n  let str = '';\n\n  for (let i in obj) {\n    if (obj.hasOwnProperty(i)) {\n      if (str.length) str += '&';\n      str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n    }\n  }\n\n  return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\n\nexport function decode(qs) {\n  let qry = {};\n  let pairs = qs.split('&');\n\n  for (let i = 0, l = pairs.length; i < l; i++) {\n    let pair = pairs[i].split('=');\n    qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n  }\n\n  return qry;\n}", "map": {"version": 3, "names": ["encode", "obj", "str", "i", "hasOwnProperty", "length", "encodeURIComponent", "decode", "qs", "qry", "pairs", "split", "l", "pair", "decodeURIComponent"], "sources": ["R:/chateye/FrontendAngular/node_modules/engine.io-client/build/esm/contrib/parseqs.js"], "sourcesContent": ["// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n    let str = '';\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            if (str.length)\n                str += '&';\n            str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\nexport function decode(qs) {\n    let qry = {};\n    let pairs = qs.split('&');\n    for (let i = 0, l = pairs.length; i < l; i++) {\n        let pair = pairs[i].split('=');\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n"], "mappings": "AAAA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,MAAT,CAAgBC,GAAhB,EAAqB;EACxB,IAAIC,GAAG,GAAG,EAAV;;EACA,KAAK,IAAIC,CAAT,IAAcF,GAAd,EAAmB;IACf,IAAIA,GAAG,CAACG,cAAJ,CAAmBD,CAAnB,CAAJ,EAA2B;MACvB,IAAID,GAAG,CAACG,MAAR,EACIH,GAAG,IAAI,GAAP;MACJA,GAAG,IAAII,kBAAkB,CAACH,CAAD,CAAlB,GAAwB,GAAxB,GAA8BG,kBAAkB,CAACL,GAAG,CAACE,CAAD,CAAJ,CAAvD;IACH;EACJ;;EACD,OAAOD,GAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,SAASK,MAAT,CAAgBC,EAAhB,EAAoB;EACvB,IAAIC,GAAG,GAAG,EAAV;EACA,IAAIC,KAAK,GAAGF,EAAE,CAACG,KAAH,CAAS,GAAT,CAAZ;;EACA,KAAK,IAAIR,CAAC,GAAG,CAAR,EAAWS,CAAC,GAAGF,KAAK,CAACL,MAA1B,EAAkCF,CAAC,GAAGS,CAAtC,EAAyCT,CAAC,EAA1C,EAA8C;IAC1C,IAAIU,IAAI,GAAGH,KAAK,CAACP,CAAD,CAAL,CAASQ,KAAT,CAAe,GAAf,CAAX;IACAF,GAAG,CAACK,kBAAkB,CAACD,IAAI,CAAC,CAAD,CAAL,CAAnB,CAAH,GAAmCC,kBAAkB,CAACD,IAAI,CAAC,CAAD,CAAL,CAArD;EACH;;EACD,OAAOJ,GAAP;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}