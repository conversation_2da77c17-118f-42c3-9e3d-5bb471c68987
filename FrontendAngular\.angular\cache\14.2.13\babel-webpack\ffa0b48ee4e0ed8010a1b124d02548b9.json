{"ast": null, "code": "import { map } from './map';\nimport { innerFrom } from '../observable/innerFrom';\nimport { operate } from '../util/lift';\nimport { mergeInternals } from './mergeInternals';\nimport { isFunction } from '../util/isFunction';\nexport function mergeMap(project, resultSelector, concurrent = Infinity) {\n  if (isFunction(resultSelector)) {\n    return mergeMap((a, i) => map((b, ii) => resultSelector(a, b, i, ii))(innerFrom(project(a, i))), concurrent);\n  } else if (typeof resultSelector === 'number') {\n    concurrent = resultSelector;\n  }\n\n  return operate((source, subscriber) => mergeInternals(source, subscriber, project, concurrent));\n}", "map": {"version": 3, "names": ["map", "innerFrom", "operate", "mergeInternals", "isFunction", "mergeMap", "project", "resultSelector", "concurrent", "Infinity", "a", "i", "b", "ii", "source", "subscriber"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/rxjs/dist/esm/internal/operators/mergeMap.js"], "sourcesContent": ["import { map } from './map';\nimport { innerFrom } from '../observable/innerFrom';\nimport { operate } from '../util/lift';\nimport { mergeInternals } from './mergeInternals';\nimport { isFunction } from '../util/isFunction';\nexport function mergeMap(project, resultSelector, concurrent = Infinity) {\n    if (isFunction(resultSelector)) {\n        return mergeMap((a, i) => map((b, ii) => resultSelector(a, b, i, ii))(innerFrom(project(a, i))), concurrent);\n    }\n    else if (typeof resultSelector === 'number') {\n        concurrent = resultSelector;\n    }\n    return operate((source, subscriber) => mergeInternals(source, subscriber, project, concurrent));\n}\n"], "mappings": "AAAA,SAASA,GAAT,QAAoB,OAApB;AACA,SAASC,SAAT,QAA0B,yBAA1B;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,cAAT,QAA+B,kBAA/B;AACA,SAASC,UAAT,QAA2B,oBAA3B;AACA,OAAO,SAASC,QAAT,CAAkBC,OAAlB,EAA2BC,cAA3B,EAA2CC,UAAU,GAAGC,QAAxD,EAAkE;EACrE,IAAIL,UAAU,CAACG,cAAD,CAAd,EAAgC;IAC5B,OAAOF,QAAQ,CAAC,CAACK,CAAD,EAAIC,CAAJ,KAAUX,GAAG,CAAC,CAACY,CAAD,EAAIC,EAAJ,KAAWN,cAAc,CAACG,CAAD,EAAIE,CAAJ,EAAOD,CAAP,EAAUE,EAAV,CAA1B,CAAH,CAA4CZ,SAAS,CAACK,OAAO,CAACI,CAAD,EAAIC,CAAJ,CAAR,CAArD,CAAX,EAAkFH,UAAlF,CAAf;EACH,CAFD,MAGK,IAAI,OAAOD,cAAP,KAA0B,QAA9B,EAAwC;IACzCC,UAAU,GAAGD,cAAb;EACH;;EACD,OAAOL,OAAO,CAAC,CAACY,MAAD,EAASC,UAAT,KAAwBZ,cAAc,CAACW,MAAD,EAASC,UAAT,EAAqBT,OAArB,EAA8BE,UAA9B,CAAvC,CAAd;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}