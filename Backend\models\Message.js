const { query } = require('../database/db');

class Message {
  static async create(text, userId, groupId, replyTo = null) {
    const result = await query(
      'INSERT INTO messages (text, user_id, group_id, reply_to) VALUES ($1, $2, $3, $4) RETURNING *',
      [text, userId, groupId, replyTo]
    );
    return result.rows[0];
  }

  static async getRecentMessages(groupId, limit = 50) {
    const result = await query(`
      SELECT 
        m.id,
        m.text,
        m.timestamp,
        m.reply_to,
        m.group_id,
        u.username,
        u.id as user_id,
        rm.text as reply_text,
        ru.username as reply_username
      FROM messages m
      JOIN users u ON m.user_id = u.id
      LEFT JOIN messages rm ON m.reply_to = rm.id
      LEFT JOIN users ru ON rm.user_id = ru.id
      WHERE m.group_id = $1
      ORDER BY m.timestamp DESC
      LIMIT $2
    `, [groupId, limit]);
    
    return result.rows.reverse(); // Return in chronological order
  }

  static async getMessageWithDetails(messageId) {
    const result = await query(`
      SELECT 
        m.id,
        m.text,
        m.timestamp,
        m.reply_to,
        m.group_id,
        u.username,
        u.id as user_id,
        rm.text as reply_text,
        ru.username as reply_username
      FROM messages m
      JOIN users u ON m.user_id = u.id
      LEFT JOIN messages rm ON m.reply_to = rm.id
      LEFT JOIN users ru ON rm.user_id = ru.id
      WHERE m.id = $1
    `, [messageId]);
    
    return result.rows[0];
  }

  static async addReaction(messageId, userId, emoji) {
    try {
      const result = await query(
        'INSERT INTO reactions (message_id, user_id, emoji) VALUES ($1, $2, $3) RETURNING *',
        [messageId, userId, emoji]
      );
      return result.rows[0];
    } catch (error) {
      if (error.code === '23505') { // Unique constraint violation
        // Remove existing reaction and add new one
        await query(
          'DELETE FROM reactions WHERE message_id = $1 AND user_id = $2',
          [messageId, userId]
        );
        const result = await query(
          'INSERT INTO reactions (message_id, user_id, emoji) VALUES ($1, $2, $3) RETURNING *',
          [messageId, userId, emoji]
        );
        return result.rows[0];
      }
      throw error;
    }
  }

  static async removeReaction(messageId, userId, emoji = null) {
    let queryText, params;

    if (emoji) {
      // Remove specific emoji reaction
      queryText = 'DELETE FROM reactions WHERE message_id = $1 AND user_id = $2 AND emoji = $3 RETURNING *';
      params = [messageId, userId, emoji];
    } else {
      // Remove all reactions from user for this message (fallback)
      queryText = 'DELETE FROM reactions WHERE message_id = $1 AND user_id = $2 RETURNING *';
      params = [messageId, userId];
    }

    const result = await query(queryText, params);
    return result.rows[0];
  }

  static async getReactions(messageId) {
    const result = await query(`
      SELECT 
        r.emoji,
        r.user_id,
        u.username,
        COUNT(*) as count
      FROM reactions r
      JOIN users u ON r.user_id = u.id
      WHERE r.message_id = $1
      GROUP BY r.emoji, r.user_id, u.username
      ORDER BY r.emoji
    `, [messageId]);
    
    return result.rows;
  }

  static async getMessagesWithReactions(groupId, limit = 50) {
    const messages = await this.getRecentMessages(groupId, limit);

    // Get reactions for all messages
    for (let message of messages) {
      message.reactions = await this.getReactions(message.id);
    }

    return messages;
  }

  static async getMessagesWithReactionsAndReadReceipts(groupId, limit = 50) {
    const ReadReceipt = require('./ReadReceipt');
    const messages = await this.getRecentMessages(groupId, limit);

    // Get message IDs
    const messageIds = messages.map(msg => msg.id);

    // Get reactions and read receipts for all messages
    const readReceipts = await ReadReceipt.getReadReceiptsForMessages(messageIds);

    for (let message of messages) {
      message.reactions = await this.getReactions(message.id);
      message.readReceipts = readReceipts[message.id] || [];
    }

    return messages;
  }

  static async updateMessage(messageId, newText, userId) {
    const result = await query(
      'UPDATE messages SET text = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2 AND user_id = $3 RETURNING *',
      [newText, messageId, userId]
    );
    return result.rows[0];
  }

  static async deleteMessage(messageId, userId) {
    const result = await query(
      'DELETE FROM messages WHERE id = $1 AND user_id = $2 RETURNING *',
      [messageId, userId]
    );
    return result.rows[0];
  }

  static async getMessageById(messageId) {
    const result = await query(
      'SELECT * FROM messages WHERE id = $1',
      [messageId]
    );
    return result.rows[0];
  }
}

module.exports = Message;
