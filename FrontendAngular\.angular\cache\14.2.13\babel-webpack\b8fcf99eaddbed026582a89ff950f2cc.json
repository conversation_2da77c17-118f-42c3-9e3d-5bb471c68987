{"ast": null, "code": "import { io } from 'socket.io-client';\nimport { environment } from '../../environments/environment';\nimport { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport let SocketService = /*#__PURE__*/(() => {\n  class SocketService {\n    constructor() {\n      this.socket = null;\n      this.isConnectedSubject = new BehaviorSubject(false);\n      this.isConnected$ = this.isConnectedSubject.asObservable();\n      this.connectionStatusSubject = new BehaviorSubject({\n        connected: false,\n        reconnecting: false\n      });\n      this.connectionStatus$ = this.connectionStatusSubject.asObservable();\n      this.reconnectAttempts = 0;\n      this.maxReconnectAttempts = 3;\n      this.reconnectDelay = 2000;\n      this.heartbeatInterval = null;\n      this.isConnecting = false;\n      this.lastConnectionAttempt = 0;\n      this.connectionCooldown = 5000; // 5 seconds between connection attempts\n    }\n\n    connect(username, password, inviteCode = null) {\n      // If already connected with same credentials, return existing socket\n      if (this.socket && this.socket.connected && this.isConnectedSubject.value) {\n        console.log('Already connected, reusing existing socket');\n        return this.socket;\n      } // Prevent multiple simultaneous connections\n\n\n      if (this.isConnecting) {\n        console.warn('Connection already in progress, skipping...');\n        return this.socket;\n      } // Prevent rapid reconnection attempts\n\n\n      const now = Date.now();\n\n      if (now - this.lastConnectionAttempt < this.connectionCooldown) {\n        console.warn('Connection attempt too soon, waiting for cooldown...');\n        return this.socket;\n      }\n\n      this.lastConnectionAttempt = now; // Always disconnect first to prevent multiple connections\n\n      this.disconnect();\n      this.isConnecting = true;\n\n      try {\n        this.socket = io(environment.backendUrl, {\n          transports: ['websocket', 'polling'],\n          timeout: 30000,\n          reconnection: false,\n          forceNew: true,\n          auth: {\n            username,\n            password,\n            inviteCode\n          }\n        });\n      } catch (error) {\n        console.error('❌ Failed to create socket connection:', error);\n        this.isConnecting = false;\n        this.updateConnectionStatus(false, false, `Failed to create connection: ${error}`);\n        throw error;\n      }\n\n      this.setupSocketEventHandlers(username, inviteCode);\n      this.isConnecting = false;\n      return this.socket;\n    }\n\n    setupSocketEventHandlers(username, inviteCode) {\n      if (!this.socket) return;\n      this.socket.on('connect', () => {\n        console.log('✅ Socket connected successfully');\n        this.reconnectAttempts = 0;\n        this.isConnecting = false;\n        this.updateConnectionStatus(true, false);\n        this.socket?.emit('join', {\n          username,\n          inviteCode\n        }); // Start heartbeat\n\n        this.startHeartbeat();\n      });\n      this.socket.on('disconnect', reason => {\n        console.log('🔌 Socket disconnected:', reason);\n        this.updateConnectionStatus(false, false, `Disconnected: ${reason}`); // Only attempt manual reconnection for certain disconnect reasons\n\n        if (reason === 'io server disconnect' || reason === 'transport close') {\n          // Server initiated disconnect or transport issues - attempt reconnection\n          setTimeout(() => {\n            if (!this.isConnected() && this.reconnectAttempts < this.maxReconnectAttempts) {\n              console.log('🔄 Attempting manual reconnection...');\n              this.reconnect(username, undefined, inviteCode);\n            }\n          }, this.reconnectDelay);\n        }\n      });\n      this.socket.on('connect_error', error => {\n        console.error('❌ Socket connection error:', error);\n        this.reconnectAttempts++;\n        this.updateConnectionStatus(false, false, `Connection error: ${error.message || 'Unknown error'}`); // Don't automatically reconnect on auth errors\n\n        if (error.message && error.message.includes('Authentication')) {\n          console.error('❌ Authentication error - not attempting reconnection');\n          return;\n        } // Attempt manual reconnection with exponential backoff\n\n\n        if (this.reconnectAttempts < this.maxReconnectAttempts) {\n          const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), 10000);\n          setTimeout(() => {\n            if (!this.isConnected()) {\n              console.log(`🔄 Reconnection attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);\n              this.reconnect(username, undefined, inviteCode);\n            }\n          }, delay);\n        }\n      });\n      this.socket.on('error', error => {\n        console.error('❌ Socket error:', error);\n        this.updateConnectionStatus(false, false, `Socket error: ${error.message || error}`);\n      });\n    }\n\n    updateConnectionStatus(connected, reconnecting, error) {\n      this.isConnectedSubject.next(connected);\n      this.connectionStatusSubject.next({\n        connected,\n        reconnecting,\n        error\n      });\n    }\n\n    disconnect() {\n      if (this.socket) {\n        try {\n          console.log('🔌 Disconnecting socket...');\n          this.stopHeartbeat();\n          this.socket.removeAllListeners();\n          this.socket.disconnect();\n        } catch (error) {\n          console.error('Error disconnecting socket:', error);\n        } finally {\n          this.socket = null;\n          this.reconnectAttempts = 0;\n          this.isConnecting = false;\n          this.updateConnectionStatus(false, false);\n        }\n      }\n    } // Reset connection state - useful for clearing error states\n\n\n    resetConnectionState() {\n      this.reconnectAttempts = 0;\n      this.isConnecting = false;\n      this.lastConnectionAttempt = 0;\n      console.log('🔄 Connection state reset');\n    }\n\n    reconnect(username, password, inviteCode = null) {\n      // Check if we should attempt reconnection\n      if (this.reconnectAttempts >= this.maxReconnectAttempts) {\n        console.warn('❌ Maximum reconnection attempts reached, not attempting reconnection');\n        this.updateConnectionStatus(false, false, 'Maximum reconnection attempts reached');\n        return;\n      } // Check if already connected\n\n\n      if (this.isConnected()) {\n        console.log('✅ Already connected, skipping reconnection');\n        return;\n      } // Check cooldown period\n\n\n      const now = Date.now();\n\n      if (now - this.lastConnectionAttempt < this.connectionCooldown) {\n        console.warn('🕐 Reconnection attempt too soon, waiting for cooldown...');\n        return;\n      }\n\n      console.log(`🔄 Attempting reconnection (${this.reconnectAttempts + 1}/${this.maxReconnectAttempts})`);\n      this.updateConnectionStatus(false, true, `Reconnecting... (${this.reconnectAttempts + 1}/${this.maxReconnectAttempts})`);\n      this.disconnect(); // Use exponential backoff but don't delay the first reconnection attempt\n\n      const delay = this.reconnectAttempts === 0 ? 0 : Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), 10000);\n      setTimeout(() => {\n        if (!this.isConnected()) {\n          try {\n            this.connect(username, password, inviteCode);\n          } catch (error) {\n            console.error('❌ Reconnection failed:', error);\n            this.reconnectAttempts++;\n          }\n        }\n      }, delay);\n    }\n\n    getConnectionStatus() {\n      return this.connectionStatusSubject.value;\n    }\n\n    isConnected() {\n      return this.isConnectedSubject.value && this.socket?.connected === true;\n    }\n\n    joinGroup(groupId) {\n      if (this.isConnected()) {\n        this.socket.emit('joinGroup', {\n          groupId\n        });\n      } else {\n        console.error('❌ Cannot join group - socket not connected');\n      }\n    }\n\n    sendMessage(text, groupId, replyTo = null) {\n      if (this.isConnected()) {\n        this.socket.emit('sendMessage', {\n          text,\n          groupId,\n          replyTo\n        });\n      } else {\n        console.error('❌ Cannot send message - socket not connected');\n        throw new Error('Socket not connected');\n      }\n    }\n\n    addReaction(messageId, emoji) {\n      if (this.isConnected()) {\n        this.socket.emit('addReaction', {\n          messageId,\n          emoji\n        });\n      } else {\n        console.error('❌ Cannot add reaction - socket not connected');\n      }\n    }\n\n    removeReaction(data) {\n      if (this.isConnected()) {\n        this.socket.emit('removeReaction', data);\n      } else {\n        console.error('❌ Cannot remove reaction - socket not connected');\n      }\n    }\n\n    on(event, callback) {\n      if (this.socket) {\n        this.socket.on(event, callback);\n      }\n    }\n\n    off(event, callback) {\n      if (this.socket) {\n        this.socket.off(event, callback);\n      }\n    }\n\n    emit(event, data) {\n      if (this.isConnected()) {\n        this.socket.emit(event, data);\n      } else {\n        console.error(`❌ Cannot emit event ${event} - socket not connected`);\n      }\n    } // Utility method to emit with acknowledgment\n\n\n    emitWithAck(event, data) {\n      return new Promise((resolve, reject) => {\n        if (this.isConnected()) {\n          this.socket.emit(event, data, response => {\n            if (response.error) {\n              reject(new Error(response.error));\n            } else {\n              resolve(response);\n            }\n          });\n        } else {\n          reject(new Error('Socket not connected'));\n        }\n      });\n    } // Method to check if socket is in a healthy state\n\n\n    isHealthy() {\n      return this.socket !== null && this.socket.connected === true && this.isConnectedSubject.value === true;\n    } // Typing indicator methods\n\n\n    sendTypingIndicator(groupId, isTyping) {\n      if (this.isConnected()) {\n        this.socket.emit('typing', {\n          groupId,\n          isTyping\n        });\n      }\n    } // Heartbeat methods for presence tracking\n\n\n    startHeartbeat() {\n      this.stopHeartbeat(); // Clear any existing heartbeat\n\n      this.heartbeatInterval = setInterval(() => {\n        if (this.isConnected()) {\n          this.socket.emit('heartbeat');\n        }\n      }, 60000); // Send heartbeat every 60 seconds\n    }\n\n    stopHeartbeat() {\n      if (this.heartbeatInterval) {\n        clearInterval(this.heartbeatInterval);\n        this.heartbeatInterval = null;\n      }\n    } // Status update methods\n\n\n    updateUserStatus(status) {\n      if (this.isConnected()) {\n        this.socket.emit('updateStatus', {\n          status\n        });\n      }\n    } // Read receipt methods\n\n\n    markAsRead(messageId) {\n      if (this.isConnected()) {\n        this.socket.emit('markAsRead', {\n          messageId\n        });\n      }\n    }\n\n    markMessagesAsRead(messageIds) {\n      if (this.isConnected() && messageIds.length > 0) {\n        this.socket.emit('markMessagesAsRead', {\n          messageIds\n        });\n      }\n    } // Message edit and delete methods\n\n\n    updateMessage(messageId, newText) {\n      if (this.isConnected()) {\n        this.socket.emit('messageUpdated', {\n          messageId,\n          newText\n        });\n      } else {\n        console.error('❌ Cannot update message - socket not connected');\n        throw new Error('Socket not connected');\n      }\n    }\n\n    deleteMessage(messageId) {\n      if (this.isConnected()) {\n        this.socket.emit('messageDeleted', {\n          messageId\n        });\n      } else {\n        console.error('❌ Cannot delete message - socket not connected');\n        throw new Error('Socket not connected');\n      }\n    }\n\n  }\n\n  SocketService.ɵfac = function SocketService_Factory(t) {\n    return new (t || SocketService)();\n  };\n\n  SocketService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: SocketService,\n    factory: SocketService.ɵfac,\n    providedIn: 'root'\n  });\n  return SocketService;\n})();", "map": null, "metadata": {}, "sourceType": "module"}