{"ast": null, "code": "import { EventEmitter, ElementRef, ChangeDetectorRef } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/card\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/chips\";\nimport * as i6 from \"@angular/material/tooltip\";\nimport * as i7 from \"../emoji-picker/emoji-picker.component\";\nconst _c0 = [\"emojiPicker\"];\n\nfunction MessageComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵlistener(\"click\", function MessageComponent_div_1_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onReplyIndicatorClick($event));\n    });\n    i0.ɵɵelement(1, \"div\", 14);\n    i0.ɵɵelementStart(2, \"div\", 15)(3, \"div\", 16)(4, \"mat-icon\", 17);\n    i0.ɵɵtext(5, \"reply\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 18);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"span\", 19);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r0.message.replyUsername);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.message.replyText);\n  }\n}\n\nfunction MessageComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 22)(4, \"span\", 23);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function MessageComponent_div_3_Template_button_click_6_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onEmojiClick($event));\n    });\n    i0.ɵɵelementStart(7, \"mat-icon\");\n    i0.ɵɵtext(8, \"emoji_emotions\");\n    i0.ɵɵelementEnd()()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.message.username);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.formatTime(ctx_r1.message.timestamp));\n  }\n}\n\nfunction MessageComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"span\", 23);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function MessageComponent_div_7_Template_button_click_3_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.onEmojiClick($event));\n    });\n    i0.ɵɵelementStart(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"emoji_emotions\");\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.formatTime(ctx_r2.message.timestamp));\n  }\n}\n\nfunction MessageComponent_div_8_mat_chip_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"mat-chip\", 28);\n    i0.ɵɵlistener(\"click\", function MessageComponent_div_8_mat_chip_2_Template_mat_chip_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r16);\n      const reaction_r14 = restoredCtx.$implicit;\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.onReactionClick(reaction_r14));\n    });\n    i0.ɵɵelementStart(1, \"span\", 29);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 30);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const reaction_r14 = ctx.$implicit;\n    i0.ɵɵclassProp(\"user-reacted\", reaction_r14.userReacted);\n    i0.ɵɵproperty(\"matTooltip\", reaction_r14.users.join(\", \") + \" reacted with \" + reaction_r14.emoji);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(reaction_r14.emoji);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(reaction_r14.count);\n  }\n}\n\nfunction MessageComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"mat-chip-list\");\n    i0.ɵɵtemplate(2, MessageComponent_div_8_mat_chip_2_Template, 5, 5, \"mat-chip\", 27);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.getReactionValues());\n  }\n}\n\nfunction MessageComponent_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function MessageComponent_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.onEditClick());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"edit\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction MessageComponent_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function MessageComponent_button_17_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.onDeleteClick());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"delete\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction MessageComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 33, 34);\n    i0.ɵɵlistener(\"click\", function MessageComponent_div_18_Template_div_click_0_listener($event) {\n      return $event.stopPropagation();\n    });\n    i0.ɵɵelementStart(2, \"app-emoji-picker\", 35);\n    i0.ɵɵlistener(\"onEmojiSelect\", function MessageComponent_div_18_Template_app_emoji_picker_onEmojiSelect_2_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.onEmojiSelect($event));\n    })(\"onClose\", function MessageComponent_div_18_Template_app_emoji_picker_onClose_2_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.onEmojiClose());\n    });\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"own-picker\", ctx_r6.isOwnMessage);\n  }\n}\n\nexport let MessageComponent = /*#__PURE__*/(() => {\n  class MessageComponent {\n    constructor(cdr) {\n      this.cdr = cdr;\n      this.currentUser = null;\n      this.highlightedMessageId = null;\n      this.onReply = new EventEmitter();\n      this.onAddReaction = new EventEmitter();\n      this.onRemoveReaction = new EventEmitter();\n      this.onEdit = new EventEmitter();\n      this.onDelete = new EventEmitter();\n      this.onMessageClick = new EventEmitter();\n      this.isOwnMessage = false;\n      this.showActions = false;\n      this.showEmojiPicker = false;\n      this.isHighlighted = false;\n      this.isOptimistic = false;\n      this.timeCache = new Map();\n    }\n\n    ngOnInit() {\n      this.isOwnMessage = this.message.username === this.currentUser;\n      this.isHighlighted = this.highlightedMessageId === this.message.id;\n      this.isOptimistic = typeof this.message.id === 'string' && this.message.id.startsWith('temp_');\n    }\n\n    ngOnChanges() {\n      this.isHighlighted = this.highlightedMessageId === this.message.id;\n      this.isOptimistic = typeof this.message.id === 'string' && this.message.id.startsWith('temp_'); // If emoji picker is open, ensure it stays visible\n\n      if (this.showEmojiPicker && this.emojiPicker) {\n        setTimeout(() => {\n          const picker = this.emojiPicker.nativeElement;\n\n          if (picker && picker.style.position === 'fixed') {\n            // Re-validate positioning to prevent blinking\n            picker.style.pointerEvents = 'auto';\n            picker.style.opacity = '1';\n            picker.style.visibility = 'visible';\n          }\n        }, 10);\n      }\n    }\n\n    ngOnDestroy() {\n      if (this.clickListener) {\n        document.removeEventListener('mousedown', this.clickListener);\n        this.clickListener = undefined;\n      }\n\n      if (this.resizeListener) {\n        window.removeEventListener('resize', this.resizeListener);\n        this.resizeListener = undefined;\n      }\n\n      this.timeCache.clear();\n    }\n\n    formatTime(timestamp) {\n      // Use cache to avoid repeated time calculations\n      if (this.timeCache.has(timestamp)) {\n        return this.timeCache.get(timestamp);\n      }\n\n      const date = new Date(timestamp);\n      const now = new Date();\n      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n      let formattedTime;\n\n      if (diffInMinutes < 1) {\n        formattedTime = 'now';\n      } else if (diffInMinutes < 60) {\n        formattedTime = `${diffInMinutes}m`;\n      } else if (diffInMinutes < 1440) {\n        const hours = Math.floor(diffInMinutes / 60);\n        formattedTime = `${hours}h`;\n      } else {\n        formattedTime = date.toLocaleDateString('en-US', {\n          month: 'short',\n          day: 'numeric',\n          hour: 'numeric',\n          minute: '2-digit',\n          hour12: true\n        });\n      }\n\n      this.timeCache.set(timestamp, formattedTime);\n      return formattedTime;\n    }\n\n    getReactionKeys() {\n      const reactions = this.message.reactions || [];\n      const emojiGroups = this.groupReactionsByEmoji(reactions);\n      return Object.keys(emojiGroups);\n    }\n\n    getReactionValues() {\n      const reactions = this.message.reactions || [];\n      const emojiGroups = this.groupReactionsByEmoji(reactions);\n      return Object.entries(emojiGroups).map(([emoji, data]) => ({\n        emoji,\n        count: data.count,\n        users: data.users || [],\n        userReacted: data.users?.includes(this.currentUser) || false\n      }));\n    }\n\n    groupReactionsByEmoji(reactions) {\n      const groups = {};\n      reactions.forEach(reaction => {\n        if (!groups[reaction.emoji]) {\n          groups[reaction.emoji] = {\n            count: 0,\n            users: []\n          };\n        }\n\n        groups[reaction.emoji].count++;\n        groups[reaction.emoji].users.push(reaction.username);\n      });\n      return groups;\n    }\n\n    onMouseEnter() {\n      this.showActions = true;\n    }\n\n    onMouseLeave() {\n      this.showActions = false; // Don't hide emoji picker on mouse leave to allow interaction\n    }\n\n    onReplyClick() {\n      this.onReply.emit(this.message);\n    }\n\n    onReplyIndicatorClick(event) {\n      event.stopPropagation(); // Emit the original message ID to scroll to it\n\n      this.onMessageClick.emit({ ...this.message,\n        id: this.message.replyTo\n      });\n    }\n\n    onEmojiClick(event) {\n      event.stopPropagation();\n      this.showEmojiPicker = !this.showEmojiPicker;\n\n      if (this.showEmojiPicker) {\n        // Position the emoji picker near the click\n        setTimeout(() => {\n          if (this.emojiPicker) {\n            const rect = event.target.getBoundingClientRect();\n            const picker = this.emojiPicker.nativeElement; // Position above the button with proper spacing\n\n            picker.style.position = 'fixed';\n            picker.style.top = `${rect.top - 320}px`; // Fixed height above button\n\n            picker.style.left = `${rect.left - 160}px`; // Center the picker\n\n            picker.style.right = 'auto';\n            picker.style.transform = 'none';\n            picker.style.zIndex = '1000';\n            picker.style.pointerEvents = 'auto'; // Ensure it's within viewport bounds\n\n            const viewportWidth = window.innerWidth;\n            const viewportHeight = window.innerHeight;\n            const pickerRect = picker.getBoundingClientRect();\n\n            if (pickerRect.left < 10) {\n              picker.style.left = '10px';\n            }\n\n            if (pickerRect.right > viewportWidth - 10) {\n              picker.style.left = `${viewportWidth - pickerRect.width - 10}px`;\n            }\n\n            if (pickerRect.top < 10) {\n              picker.style.top = `${rect.bottom + 10}px`; // Show below if no space above\n            } // Force change detection to update the UI\n\n\n            this.cdr.markForCheck();\n          } // Set up click outside listener with delay\n\n\n          setTimeout(() => {\n            this.clickListener = event => {\n              if (this.emojiPicker && !this.emojiPicker.nativeElement.contains(event.target)) {\n                this.showEmojiPicker = false;\n                this.cdr.markForCheck(); // Force change detection\n\n                if (this.clickListener) {\n                  document.removeEventListener('mousedown', this.clickListener);\n                }\n              }\n            };\n\n            document.addEventListener('mousedown', this.clickListener); // Set up resize listener to reposition emoji picker\n\n            this.resizeListener = () => {\n              if (this.showEmojiPicker && this.emojiPicker) {\n                const picker = this.emojiPicker.nativeElement;\n\n                if (picker && picker.style.position === 'fixed') {\n                  // Ensure emoji picker stays visible on resize\n                  picker.style.pointerEvents = 'auto';\n                  picker.style.opacity = '1';\n                  picker.style.visibility = 'visible';\n                }\n              }\n            };\n\n            window.addEventListener('resize', this.resizeListener);\n          }, 200); // Delay to prevent immediate closing\n        }, 50);\n      } else {\n        if (this.clickListener) {\n          document.removeEventListener('mousedown', this.clickListener);\n        }\n\n        if (this.resizeListener) {\n          window.removeEventListener('resize', this.resizeListener);\n        }\n\n        this.cdr.markForCheck(); // Force change detection\n      }\n    }\n\n    onEmojiClose() {\n      this.showEmojiPicker = false;\n\n      if (this.clickListener) {\n        document.removeEventListener('mousedown', this.clickListener);\n      }\n\n      if (this.resizeListener) {\n        window.removeEventListener('resize', this.resizeListener);\n      }\n\n      this.cdr.markForCheck(); // Force change detection\n    }\n\n    onEmojiSelect(emoji) {\n      this.onAddReaction.emit({\n        messageId: this.message.id,\n        emoji: emoji.native\n      });\n      this.showEmojiPicker = false;\n      this.cdr.markForCheck(); // Force change detection\n    }\n\n    onReactionClick(reaction) {\n      if (reaction.userReacted) {\n        // Remove the specific emoji reaction\n        this.onRemoveReaction.emit({\n          messageId: this.message.id,\n          emoji: reaction.emoji\n        });\n      } else {\n        // Add the emoji reaction\n        this.onAddReaction.emit({\n          messageId: this.message.id,\n          emoji: reaction.emoji\n        });\n      }\n    }\n\n    onRightClick(event) {\n      event.preventDefault(); // Show action buttons on right click\n\n      this.showActions = true; // Hide after 3 seconds\n\n      setTimeout(() => {\n        this.showActions = false;\n      }, 3000);\n    }\n\n    onEditClick() {\n      this.onEdit.emit(this.message);\n    }\n\n    onDeleteClick() {\n      if (confirm('Are you sure you want to delete this message?')) {\n        this.onDelete.emit(this.message);\n      }\n    }\n\n    onMessageClickHandler() {\n      this.onMessageClick.emit(this.message);\n    }\n\n  }\n\n  MessageComponent.ɵfac = function MessageComponent_Factory(t) {\n    return new (t || MessageComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n\n  MessageComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: MessageComponent,\n    selectors: [[\"app-message\"]],\n    viewQuery: function MessageComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.emojiPicker = _t.first);\n      }\n    },\n    inputs: {\n      message: \"message\",\n      currentUser: \"currentUser\",\n      highlightedMessageId: \"highlightedMessageId\"\n    },\n    outputs: {\n      onReply: \"onReply\",\n      onAddReaction: \"onAddReaction\",\n      onRemoveReaction: \"onRemoveReaction\",\n      onEdit: \"onEdit\",\n      onDelete: \"onDelete\",\n      onMessageClick: \"onMessageClick\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 19,\n    vars: 20,\n    consts: [[1, \"message-container\", 3, \"contextmenu\", \"click\", \"mouseenter\", \"mouseleave\"], [\"class\", \"reply-indicator\", 3, \"click\", 4, \"ngIf\"], [1, \"message-content\"], [\"class\", \"message-header\", 4, \"ngIf\"], [1, \"message-text\"], [\"class\", \"message-footer\", 4, \"ngIf\"], [\"class\", \"reactions-container\", 4, \"ngIf\"], [1, \"action-buttons\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Reply to this message\", 1, \"action-button\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"React to this message\", 1, \"action-button\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Edit this message\", \"class\", \"action-button edit-button\", 3, \"click\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Delete this message\", \"class\", \"action-button delete-button\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"emoji-picker\", 3, \"own-picker\", \"click\", 4, \"ngIf\"], [1, \"reply-indicator\", 3, \"click\"], [1, \"reply-thread-line\"], [1, \"reply-content\"], [1, \"reply-header\"], [1, \"reply-icon\"], [1, \"reply-username\"], [1, \"reply-text\"], [1, \"message-header\"], [1, \"username\"], [1, \"header-actions\"], [1, \"timestamp\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Quick react\", 1, \"react-button\", 3, \"click\"], [1, \"message-footer\"], [1, \"reactions-container\"], [\"class\", \"reaction-chip\", 3, \"user-reacted\", \"matTooltip\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"reaction-chip\", 3, \"matTooltip\", \"click\"], [1, \"reaction-emoji\"], [1, \"reaction-count\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Edit this message\", 1, \"action-button\", \"edit-button\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Delete this message\", 1, \"action-button\", \"delete-button\", 3, \"click\"], [1, \"emoji-picker\", 3, \"click\"], [\"emojiPicker\", \"\"], [3, \"onEmojiSelect\", \"onClose\"]],\n    template: function MessageComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"mat-card\", 0);\n        i0.ɵɵlistener(\"contextmenu\", function MessageComponent_Template_mat_card_contextmenu_0_listener($event) {\n          return ctx.onRightClick($event);\n        })(\"click\", function MessageComponent_Template_mat_card_click_0_listener() {\n          return ctx.onMessageClickHandler();\n        })(\"mouseenter\", function MessageComponent_Template_mat_card_mouseenter_0_listener() {\n          return ctx.onMouseEnter();\n        })(\"mouseleave\", function MessageComponent_Template_mat_card_mouseleave_0_listener() {\n          return ctx.onMouseLeave();\n        });\n        i0.ɵɵtemplate(1, MessageComponent_div_1_Template, 10, 2, \"div\", 1);\n        i0.ɵɵelementStart(2, \"mat-card-content\", 2);\n        i0.ɵɵtemplate(3, MessageComponent_div_3_Template, 9, 2, \"div\", 3);\n        i0.ɵɵelementStart(4, \"div\", 4)(5, \"p\");\n        i0.ɵɵtext(6);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(7, MessageComponent_div_7_Template, 6, 1, \"div\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(8, MessageComponent_div_8_Template, 3, 1, \"div\", 6);\n        i0.ɵɵelementStart(9, \"div\", 7)(10, \"button\", 8);\n        i0.ɵɵlistener(\"click\", function MessageComponent_Template_button_click_10_listener() {\n          return ctx.onReplyClick();\n        });\n        i0.ɵɵelementStart(11, \"mat-icon\");\n        i0.ɵɵtext(12, \"reply\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(13, \"button\", 9);\n        i0.ɵɵlistener(\"click\", function MessageComponent_Template_button_click_13_listener($event) {\n          return ctx.onEmojiClick($event);\n        });\n        i0.ɵɵelementStart(14, \"mat-icon\");\n        i0.ɵɵtext(15, \"emoji_emotions\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(16, MessageComponent_button_16_Template, 3, 0, \"button\", 10);\n        i0.ɵɵtemplate(17, MessageComponent_button_17_Template, 3, 0, \"button\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(18, MessageComponent_div_18_Template, 3, 2, \"div\", 12);\n        i0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"own-message\", ctx.isOwnMessage)(\"message-enter\", true)(\"reply-highlight\", ctx.isHighlighted)(\"optimistic\", ctx.isOptimistic);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.message.replyTo);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isOwnMessage);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.message.text);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isOwnMessage);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.getReactionKeys().length > 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵclassProp(\"own-actions\", ctx.isOwnMessage)(\"visible\", ctx.showActions);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngIf\", ctx.isOwnMessage);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isOwnMessage);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.showEmojiPicker);\n      }\n    },\n    dependencies: [i1.NgForOf, i1.NgIf, i2.MatCard, i2.MatCardContent, i3.MatButton, i4.MatIcon, i5.MatChipList, i5.MatChip, i6.MatTooltip, i7.EmojiPickerComponent],\n    styles: [\".message-container[_ngcontent-%COMP%]{max-width:100%;margin-bottom:.75rem;position:relative;transition:all .3s ease;box-shadow:0 2px 8px #0000001a;border-radius:16px;overflow:visible}.message-container.own-message[_ngcontent-%COMP%]{margin-left:auto;max-width:80%;background:linear-gradient(135deg,#3f51b5 0%,#5c6bc0 100%);color:#fff}.message-container[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 16px #00000026}.reply-indicator[_ngcontent-%COMP%]{margin-bottom:.75rem;padding:.75rem;background:rgba(63,81,181,.08);border-radius:8px;border-left:4px solid #3f51b5;transition:all .3s ease;position:relative;cursor:pointer}.reply-indicator[_ngcontent-%COMP%]:hover{background:rgba(63,81,181,.12);transform:translate(4px);border-left-color:#5c6bc0}.reply-thread-line[_ngcontent-%COMP%]{position:absolute;left:-8px;top:0;bottom:0;width:2px;background:linear-gradient(to bottom,#3f51b5,transparent);border-radius:1px}.reply-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.375rem;margin-left:8px}.reply-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.reply-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px;color:#3f51b5}.reply-username[_ngcontent-%COMP%]{font-size:.8rem;font-weight:600;color:#3f51b5;text-transform:capitalize}.reply-text[_ngcontent-%COMP%]{font-size:.8rem;color:#666;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;max-width:250px;line-height:1.4;font-style:italic}.message-content[_ngcontent-%COMP%]{padding:16px}.message-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:.5rem}.header-actions[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.username[_ngcontent-%COMP%]{font-size:.8rem;font-weight:600;color:#3f51b5;text-transform:capitalize}.timestamp[_ngcontent-%COMP%]{font-size:.7rem;color:#999;font-weight:500}.message-container.own-message[_ngcontent-%COMP%]   .timestamp[_ngcontent-%COMP%]{color:#fffc}.react-button[_ngcontent-%COMP%]{width:32px;height:32px;opacity:0;transition:all .3s ease}.message-container[_ngcontent-%COMP%]:hover   .react-button[_ngcontent-%COMP%]{opacity:1}.react-button[_ngcontent-%COMP%]:hover{background:rgba(63,81,181,.1);color:#3f51b5}.message-text[_ngcontent-%COMP%]{margin:0;font-size:.95rem;line-height:1.6;word-wrap:break-word;white-space:pre-wrap;color:#333}.message-container.own-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]{color:#fff}.message-footer[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-top:.5rem}.reactions-container[_ngcontent-%COMP%]{padding:0 16px 16px}.reaction-chip[_ngcontent-%COMP%]{margin-right:8px;margin-bottom:4px;cursor:pointer;transition:all .3s ease}.reaction-chip[_ngcontent-%COMP%]:hover{background:rgba(63,81,181,.1);transform:scale(1.05)}.reaction-chip.user-reacted[_ngcontent-%COMP%]{background:#3f51b5;color:#fff}.reaction-emoji[_ngcontent-%COMP%]{font-size:.8rem;margin-right:4px}.reaction-count[_ngcontent-%COMP%]{font-weight:600;font-size:.75rem}.action-buttons[_ngcontent-%COMP%]{position:absolute;top:-12px;right:0;display:flex;gap:4px;background:white;border:1px solid #e0e0e0;border-radius:20px;padding:4px;box-shadow:0 4px 12px #00000026;opacity:0;transform:translateY(-8px);transition:all .3s ease;z-index:30}.message-container[_ngcontent-%COMP%]:hover   .action-buttons[_ngcontent-%COMP%], .action-buttons.visible[_ngcontent-%COMP%]{opacity:1;transform:translateY(0)}.action-buttons.own-actions[_ngcontent-%COMP%]{right:auto;left:0}.action-button[_ngcontent-%COMP%]{width:32px;height:32px;color:#666;transition:all .3s ease}.action-button[_ngcontent-%COMP%]:hover{background:rgba(63,81,181,.1);color:#3f51b5}.edit-button[_ngcontent-%COMP%]:hover{background:rgba(76,175,80,.1);color:#4caf50}.delete-button[_ngcontent-%COMP%]:hover{background:rgba(244,67,54,.1);color:#f44336}@media (max-width: 768px){.action-buttons[_ngcontent-%COMP%]{position:static;opacity:1;transform:none;margin-top:8px;box-shadow:none;border:none;background:transparent;padding:0;justify-content:flex-start}}.emoji-picker[_ngcontent-%COMP%]{position:fixed!important;z-index:1000!important;transform:none!important;margin:0!important;pointer-events:auto!important;animation:emojiPickerSlideIn .2s ease;opacity:1!important;visibility:visible!important;contain:layout style paint!important}.emoji-picker[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{pointer-events:auto!important;position:relative!important}.emoji-picker.own-picker[_ngcontent-%COMP%]{right:auto;left:0}@keyframes emojiPickerSlideIn{0%{opacity:0;transform:translateY(10px) scale(.95)}to{opacity:1;transform:translateY(0) scale(1)}}@media (max-width: 1024px){.message-container.own-message[_ngcontent-%COMP%]{max-width:85%}.reply-text[_ngcontent-%COMP%]{max-width:200px}}@media (max-width: 768px){.message-container.own-message[_ngcontent-%COMP%]{max-width:90%}.message-text[_ngcontent-%COMP%]{font-size:.9rem}.reply-text[_ngcontent-%COMP%]{max-width:180px}}@media (max-width: 480px){.message-container.own-message[_ngcontent-%COMP%]{max-width:95%}.message-text[_ngcontent-%COMP%]{font-size:.85rem}.reply-text[_ngcontent-%COMP%]{max-width:150px}.username[_ngcontent-%COMP%]{font-size:.75rem}.timestamp[_ngcontent-%COMP%]{font-size:.65rem}}@keyframes messageAppear{0%{opacity:0;transform:translateY(20px) scale(.95)}to{opacity:1;transform:translateY(0) scale(1)}}.message-container[_ngcontent-%COMP%]{animation:messageAppear .4s ease}.message-container.optimistic[_ngcontent-%COMP%]{opacity:.7;position:relative}.message-container.optimistic[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;top:0;right:0;width:8px;height:8px;background:#ff9800;border-radius:50%;animation:optimisticPulse 1.5s infinite}@keyframes optimisticPulse{0%,to{opacity:.5;transform:scale(1)}50%{opacity:1;transform:scale(1.2)}}*[_ngcontent-%COMP%]{transition:all .3s ease}\"],\n    changeDetection: 0\n  });\n  return MessageComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}