{"ast": null, "code": "let nextHandle = 1;\nlet resolved;\nconst activeHandles = {};\n\nfunction findAndClearHandle(handle) {\n  if (handle in activeHandles) {\n    delete activeHandles[handle];\n    return true;\n  }\n\n  return false;\n}\n\nexport const Immediate = {\n  setImmediate(cb) {\n    const handle = nextHandle++;\n    activeHandles[handle] = true;\n\n    if (!resolved) {\n      resolved = Promise.resolve();\n    }\n\n    resolved.then(() => findAndClearHandle(handle) && cb());\n    return handle;\n  },\n\n  clearImmediate(handle) {\n    findAndClearHandle(handle);\n  }\n\n};\nexport const TestTools = {\n  pending() {\n    return Object.keys(activeHandles).length;\n  }\n\n}; //# sourceMappingURL=Immediate.js.map", "map": null, "metadata": {}, "sourceType": "module"}