import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, combineLatest } from 'rxjs';
import { map } from 'rxjs/operators';
import { ApiService, Message, Group, User, SecurityInfo, LoginResponse } from './api.service';
import { SocketService } from './socket.service';
import { NotificationService } from './notification.service';

@Injectable({
  providedIn: 'root'
})
export class ChatService {
  private userSubject = new BehaviorSubject<string | null>(null);
  private messagesSubject = new BehaviorSubject<Message[]>([]);
  private onlineUsersSubject = new BehaviorSubject<User[]>([]);
  private groupsSubject = new BehaviorSubject<Group[]>([]);
  private currentGroupSubject = new BehaviorSubject<Group | null>(null);
  private replyToSubject = new BehaviorSubject<Message | null>(null);
  private loadingSubject = new BehaviorSubject<boolean>(false);
  private isAdminSubject = new BehaviorSubject<boolean>(false);
  private showAdminPanelSubject = new BehaviorSubject<boolean>(false);
  private securityInfoSubject = new BehaviorSubject<SecurityInfo | null>(null);
  private editingMessageSubject = new BehaviorSubject<Message | null>(null);

  // Public observables
  public user$ = this.userSubject.asObservable();
  public messages$ = this.messagesSubject.asObservable();
  public onlineUsers$ = this.onlineUsersSubject.asObservable();
  public groups$ = this.groupsSubject.asObservable();
  public currentGroup$ = this.currentGroupSubject.asObservable();
  public replyTo$ = this.replyToSubject.asObservable();
  public loading$ = this.loadingSubject.asObservable();
  public isAdmin$ = this.isAdminSubject.asObservable();
  public showAdminPanel$ = this.showAdminPanelSubject.asObservable();
  public securityInfo$ = this.securityInfoSubject.asObservable();
  public editingMessage$ = this.editingMessageSubject.asObservable();
  public connected$ = this.socketService.isConnected$;

  // Typing indicators
  private typingUsersSubject = new BehaviorSubject<string[]>([]);
  public typingUsers$ = this.typingUsersSubject.asObservable();
  private typingTimeout: any = null;

  // Computed observables
  public isLoggedIn$ = this.user$.pipe(map(user => !!user))

  private messageRefreshInterval: any;
  private isLoadingGroups = false;
  private isLoadingMessages = false;
  private lastMessageLoadTime = 0;
  private readonly MESSAGE_LOAD_THROTTLE = 1000; // 1 second throttle

  constructor(
    private apiService: ApiService,
    private socketService: SocketService,
    private notificationService: NotificationService
  ) {
    try {
      this.setupSocketListeners();
      this.setupMessageRefresh();
      

    } catch (error) {
      console.error('Error initializing ChatService:', error);
    }
  }

  private setupSocketListeners(): void {
    this.socketService.on('connect', () => {
      // Clear any loading states when connected
      this.loadingSubject.next(false);
    });

    this.socketService.on('disconnect', () => {
      // Don't set loading to true on disconnect to avoid UI freeze
    });

    this.socketService.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      this.loadingSubject.next(false);
    });

    this.socketService.on('userGroups', (userGroups: Group[]) => {
      this.groupsSubject.next(userGroups || []);
      if (userGroups && userGroups.length > 0) {
        this.currentGroupSubject.next(userGroups[0]);
        // Load messages for the selected group
        this.loadRecentMessages(userGroups[0].id);
      } else {
        // Clear current group and messages if no groups
        this.currentGroupSubject.next(null);
        this.messagesSubject.next([]);
      }
    });

    this.socketService.on('recentMessages', (messages: Message[]) => {
      this.messagesSubject.next(messages || []);
    });

    this.socketService.on('groupJoined', ({ groupId }: { groupId: string }) => {
      const groups = this.groupsSubject.value;
      const group = groups.find(g => g.id === groupId);
      if (group) {
        this.currentGroupSubject.next(group);
      }
    });

    this.socketService.on('newMessage', (message: Message) => {
      const currentMessages = this.messagesSubject.value;
      const currentUser = this.userSubject.value;
      const currentGroup = this.currentGroupSubject.value;

      // Only process messages for the current group to avoid confusion
      if (currentGroup && message.groupId !== currentGroup.id) {
        return;
      }

      // Check for duplicate messages to prevent double-display
      const existingMessage = currentMessages.find(msg =>
        msg.id === message.id ||
        (msg.text === message.text &&
         msg.username === message.username &&
         Math.abs(new Date(msg.timestamp).getTime() - new Date(message.timestamp).getTime()) < 5000)
      );

      if (existingMessage && !existingMessage.id.startsWith('temp_')) {
        return;
      }

      // Check if this is our own message (to replace optimistic message)
      if (message.username === currentUser) {
        // Find and replace optimistic message with real message
        const optimisticIndex = currentMessages.findIndex(msg =>
          msg.id.startsWith('temp_') &&
          msg.text === message.text &&
          msg.username === currentUser &&
          msg.groupId === message.groupId
        );

        if (optimisticIndex !== -1) {
          // Replace optimistic message
          const updatedMessages = [...currentMessages];
          updatedMessages[optimisticIndex] = message;
          this.messagesSubject.next(this.sortMessagesByTimestamp(updatedMessages));
        } else {
          // Add new message if no optimistic message found (e.g., from another tab)
          const updatedMessages = [...currentMessages, message];
          this.messagesSubject.next(this.sortMessagesByTimestamp(updatedMessages));
        }
      } else {
        // Add message from other users
        const updatedMessages = [...currentMessages, message];
        this.messagesSubject.next(this.sortMessagesByTimestamp(updatedMessages));
      }

      // Show notification if not current user and window not focused
      if (message.username !== currentUser && document.hidden) {
        this.notificationService.showMessageNotification(message.username, message.text);
      }
    });

    this.socketService.on('reactionUpdate', ({ messageId, reactions }: { messageId: string; reactions: any[] }) => {
      const currentMessages = this.messagesSubject.value;
      this.messagesSubject.next(
        currentMessages.map(msg =>
          msg.id === messageId
            ? { ...msg, reactions }
            : msg
        )
      );
    });

    this.socketService.on('onlineUsersUpdate', (users: User[]) => {
      this.onlineUsersSubject.next(users || []);
    });

    this.socketService.on('userJoined', ({ username }: { username: string }) => {
      // Online users will be updated via onlineUsersUpdate event
    });

    this.socketService.on('userLeft', ({ username }: { username: string }) => {
      // Online users will be updated via onlineUsersUpdate event
    });

    // Handle typing indicators
    this.socketService.on('userTyping', ({ username, groupId, isTyping }: { username: string; groupId: string; isTyping: boolean }) => {


      const currentGroup = this.currentGroupSubject.value;
      if (currentGroup && groupId === currentGroup.id) {
        const currentTypingUsers = this.typingUsersSubject.value;

        if (isTyping) {
          // Add user to typing list if not already there
          if (!currentTypingUsers.includes(username)) {
            this.typingUsersSubject.next([...currentTypingUsers, username]);
          }
        } else {
          // Remove user from typing list
          this.typingUsersSubject.next(currentTypingUsers.filter(user => user !== username));
        }
      }
    });

    this.socketService.on('messageUpdated', ({ messageId, newText, updatedAt }: { messageId: string; newText: string; updatedAt: string }) => {

      const currentMessages = this.messagesSubject.value;
      this.messagesSubject.next(
        currentMessages.map(msg => 
          msg.id === messageId 
            ? { ...msg, text: newText, updated_at: updatedAt }
            : msg
        )
      );
    });

    this.socketService.on('messageDeleted', ({ messageId }: { messageId: string }) => {

      const currentMessages = this.messagesSubject.value;
      this.messagesSubject.next(
        currentMessages.filter(msg => msg.id !== messageId)
      );
    });

    this.socketService.on('error', (error: any) => {
      console.error('Socket error:', error);
    });
  }

  private setupMessageRefresh(): void {
    // Clear any existing interval first
    if (this.messageRefreshInterval) {
      clearInterval(this.messageRefreshInterval);
    }
    
    // Temporarily disable message refresh to prevent browser freezing
    // TODO: Re-enable once socket connection issues are resolved
    /*
    this.messageRefreshInterval = setInterval(() => {
      try {
        if (!this.socketService.isConnectedSubject.value) {
          const currentGroup = this.currentGroupSubject.value;
          if (currentGroup) {
            console.log('Refreshing messages via HTTP API');
            this.loadRecentMessages(currentGroup.id);
          }
        }
      } catch (error) {
        console.error('Error in message refresh interval:', error);
        // Clear the interval if there's an error to prevent crashes
        if (this.messageRefreshInterval) {
          clearInterval(this.messageRefreshInterval);
          this.messageRefreshInterval = null;
        }
      }
    }, 5000);
    */
  }

  async login(username: string, password?: string, inviteCode: string | null = null): Promise<void> {
    try {
      this.loadingSubject.next(true);

      // Reset socket connection state to clear any previous errors
      this.socketService.resetConnectionState();

      const userData = await this.apiService.loginUser(username, password, inviteCode || undefined).toPromise();

      // Check if password is required
      if (userData?.requiresPassword) {
        throw new Error('Password required for this user');
      }

      // Connect to socket with auth data
      this.socketService.connect(username, password, inviteCode);

      this.userSubject.next(username);
      this.isAdminSubject.next(userData?.isAdmin || false);
      this.securityInfoSubject.next(userData?.securityInfo || null);

      // Wait for socket connection before loading groups
      // The socket will emit 'userGroups' event which will handle group loading
      // This prevents race conditions between API and socket calls

      // Add fallback: if socket doesn't load groups within 5 seconds, try API
      setTimeout(() => {
        if (this.groupsSubject.value.length === 0 && !this.socketService.isConnected()) {
          this.loadUserGroups(username);
        }
      }, 5000);

    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    } finally {
      this.loadingSubject.next(false);
    }
  }

  async loadRecentMessages(groupId: string): Promise<void> {
    // Safety guard to prevent infinite loops
    if (this.isLoadingMessages) {
      return;
    }

    // Throttle message loading to prevent excessive API calls
    const now = Date.now();
    if (now - this.lastMessageLoadTime < this.MESSAGE_LOAD_THROTTLE) {
      return;
    }
    this.lastMessageLoadTime = now;

    this.isLoadingMessages = true;
    try {
      const messages = await this.apiService.getMessages(groupId, 50).toPromise();

      // For group switching, replace messages with only the current group's messages
      // This ensures we only show messages for the current group
      this.messagesSubject.next(messages || []);
    } catch (error) {
      console.error('Failed to load messages:', error);
      // Set empty array to prevent UI from showing stale data
      this.messagesSubject.next([]);
    } finally {
      this.isLoadingMessages = false;
    }
  }

  async loadUserGroups(username: string): Promise<void> {
    // Safety guard to prevent infinite loops
    if (this.isLoadingGroups) {
      return;
    }

    this.isLoadingGroups = true;
    try {
      const groups = await this.apiService.getUserGroups(username).toPromise();
      this.groupsSubject.next(groups || []);
      if (groups && groups.length > 0) {
        this.currentGroupSubject.next(groups[0]);
        this.loadRecentMessages(groups[0].id);
      }
    } catch (error) {
      console.error('Failed to load user groups:', error);
    } finally {
      this.isLoadingGroups = false;
    }
  }





  // Helper method to sort messages by timestamp
  private sortMessagesByTimestamp(messages: Message[]): Message[] {
    return messages.sort((a, b) => {
      const timeA = new Date(a.timestamp).getTime();
      const timeB = new Date(b.timestamp).getTime();
      return timeA - timeB;
    });
  }

  // Helper method to replace optimistic message with real message
  private replaceOptimisticMessage(tempId: string, realMessage: Message): void {
    const currentMessages = this.messagesSubject.value;
    const updatedMessages = currentMessages.map(msg =>
      msg.id === tempId ? realMessage : msg
    );
    this.messagesSubject.next(this.sortMessagesByTimestamp(updatedMessages));

  }

  // Helper method to remove optimistic message (on error)
  private removeOptimisticMessage(tempId: string): void {
    const currentMessages = this.messagesSubject.value;
    const updatedMessages = currentMessages.filter(msg => msg.id !== tempId);
    this.messagesSubject.next(updatedMessages);

  }

  // Helper method to check if message already exists
  private messageExists(message: Message, messages: Message[]): boolean {
    return messages.some(msg =>
      msg.id === message.id ||
      (msg.text === message.text &&
       msg.username === message.username &&
       Math.abs(new Date(msg.timestamp).getTime() - new Date(message.timestamp).getTime()) < 5000)
    );
  }

  joinGroup(groupId: string): void {
    const currentUser = this.userSubject.value;
    if (!groupId || !currentUser) {
      console.error('Cannot join group - missing groupId or user');
      return;
    }
    

    
    // Check if socket is connected before joining
    if (!this.socketService.isConnectedSubject.value) {
      console.error('Cannot join group - socket not connected');
      return;
    }
    
    this.socketService.joinGroup(groupId);
    const groups = this.groupsSubject.value;
    const group = groups.find(g => g.id === groupId);
    if (group) {
      this.currentGroupSubject.next(group);
      // Load recent messages for the group
      this.loadRecentMessages(groupId);
    } else {
      console.error('Group not found in user groups:', groupId);
    }
  }

  sendMessage(text: string, replyToId: string | null = null): void {
    const currentUser = this.userSubject.value;
    const currentGroup = this.currentGroupSubject.value;
    
    if (!text.trim()) {
      console.error('Cannot send message - empty text');
      return;
    }
    
    if (!currentUser) {
      console.error('Cannot send message - user not logged in');
      return;
    }
    
    if (!currentGroup) {
      console.error('Cannot send message - no group selected. Available groups:', this.groupsSubject.value);
      // Try to auto-select the first available group
      const groups = this.groupsSubject.value;
      if (groups && groups.length > 0) {

        this.currentGroupSubject.next(groups[0]);
        this.loadRecentMessages(groups[0].id);
        // Retry sending the message - DISABLED to prevent infinite loops
        // setTimeout(() => this.sendMessage(text, replyToId), 100);
        return;
      } else {
        console.error('No groups available for user');
        return;
      }
    }

    // Create optimistic message for immediate display
    const optimisticMessage: Message = {
      id: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`, // Unique temporary ID
      text: text.trim(),
      username: currentUser,
      timestamp: new Date().toISOString(),
      groupId: currentGroup.id,
      replyTo: replyToId || undefined,
      reactions: []
    };

    // Add optimistic message to local state immediately
    const currentMessages = this.messagesSubject.value;
    const updatedMessages = [...currentMessages, optimisticMessage];
    this.messagesSubject.next(updatedMessages);


    // Send message with acknowledgment and retry logic
    this.sendMessageWithRetry(text, currentGroup.id, replyToId, optimisticMessage);
  }

  private async sendMessageWithRetry(
    text: string,
    groupId: string,
    replyToId: string | null,
    optimisticMessage: Message,
    retryCount: number = 0
  ): Promise<void> {
    const maxRetries = 3;
    const retryDelay = 1000 * Math.pow(2, retryCount); // Exponential backoff

    try {
      if (this.socketService.isConnected()) {
        // Try sending via socket with acknowledgment
        const response = await this.socketService.emitWithAck('sendMessage', {
          text,
          groupId,
          replyTo: replyToId,
          messageId: optimisticMessage.id
        });

        if (response.success) {

          // Replace optimistic message with real message
          this.replaceOptimisticMessage(optimisticMessage.id, response.message);
          return;
        } else {
          throw new Error(response.error || 'Unknown error');
        }
      } else {
        throw new Error('Socket not connected');
      }
    } catch (error) {
      console.error(`❌ Failed to send message (attempt ${retryCount + 1}):`, error);

      if (retryCount < maxRetries) {
        // Retry with exponential backoff

        setTimeout(() => {
          this.sendMessageWithRetry(text, groupId, replyToId, optimisticMessage, retryCount + 1);
        }, retryDelay);
      } else {
        // All retries failed, try HTTP API as final fallback

        this.sendMessageViaHttpFallback(text, groupId, replyToId, optimisticMessage);
      }
    }
  }

  private sendMessageViaHttpFallback(
    text: string,
    groupId: string,
    replyToId: string | null,
    optimisticMessage: Message
  ): void {
    const currentUser = this.userSubject.value;
    if (!currentUser) {
      this.removeOptimisticMessage(optimisticMessage.id);
      return;
    }

    this.apiService.sendMessage(text, currentUser, groupId, replyToId).subscribe({
      next: (response) => {

        this.replaceOptimisticMessage(optimisticMessage.id, response);
      },
      error: (error) => {
        console.error('❌ Failed to send message via HTTP API:', error);
        this.removeOptimisticMessage(optimisticMessage.id);
        // Show error notification to user
        this.showMessageError('Failed to send message. Please try again.');
      }
    });
  }

  private showMessageError(message: string): void {
    // You can implement a toast notification service here
    console.error('💬 Message Error:', message);
    // For now, just log the error. In a real app, you'd show a toast/snackbar
  }

  // Typing indicator methods
  startTyping(): void {
    const currentGroup = this.currentGroupSubject.value;
    if (currentGroup && this.socketService.isConnected()) {
      this.socketService.sendTypingIndicator(currentGroup.id, true);

      // Clear existing timeout
      if (this.typingTimeout) {
        clearTimeout(this.typingTimeout);
      }

      // Set timeout to stop typing after 3 seconds of inactivity
      this.typingTimeout = setTimeout(() => {
        this.stopTyping();
      }, 3000);
    }
  }

  stopTyping(): void {
    const currentGroup = this.currentGroupSubject.value;
    if (currentGroup && this.socketService.isConnected()) {
      this.socketService.sendTypingIndicator(currentGroup.id, false);

      // Clear timeout
      if (this.typingTimeout) {
        clearTimeout(this.typingTimeout);
        this.typingTimeout = null;
      }
    }
  }

  replyToMessage(message: Message): void {
    this.replyToSubject.next(message);
  }

  cancelReply(): void {
    this.replyToSubject.next(null);
  }

  async addReaction(messageId: string, emoji: string): Promise<void> {
    try {
      if (this.socketService.isConnected()) {
        // Try with acknowledgment first
        const response = await this.socketService.emitWithAck('addReaction', {
          messageId,
          emoji
        });

        if (response.success) {

        } else {
          throw new Error(response.error || 'Failed to add reaction');
        }
      } else {
        // Fallback to HTTP API
        const currentUser = this.userSubject.value;
        if (currentUser) {
          this.apiService.addReaction(messageId, emoji, currentUser).subscribe({
            next: (response) => {

            },
            error: (error) => {
              console.error('❌ Failed to add reaction via HTTP API:', error);
            }
          });
        }
      }
    } catch (error) {
      console.error('❌ Failed to add reaction:', error);
      // Fallback to old method
      this.socketService.addReaction(messageId, emoji);
    }
  }

  removeReaction(data: { messageId: string; emoji?: string }): void {
    this.socketService.removeReaction(data);
  }

  async updateMessage(messageId: string, newText: string): Promise<void> {
    const currentUser = this.userSubject.value;
    if (!currentUser) {
      console.error('Cannot update message - user not logged in');
      return;
    }

    try {
      // Update the message in the local state immediately for better UX
      const currentMessages = this.messagesSubject.value;
      this.messagesSubject.next(
        currentMessages.map(msg => 
          msg.id === messageId 
            ? { ...msg, text: newText, updated_at: new Date().toISOString() }
            : msg
        )
      );

      // Emit socket event for real-time updates
      if (this.socketService.isConnectedSubject.value) {
        this.socketService.updateMessage(messageId, newText);
      } else {
        // Fallback to HTTP API if socket not connected
        const updatedMessage = await this.apiService.updateMessage(messageId, newText, currentUser).toPromise();
        this.messagesSubject.next(
          currentMessages.map(msg => 
            msg.id === messageId 
              ? { ...msg, text: newText, updated_at: updatedMessage.updated_at }
              : msg
          )
        );
      }
    } catch (error) {
      console.error('Failed to update message:', error);
      throw error;
    }
  }

  async deleteMessage(messageId: string): Promise<void> {
    const currentUser = this.userSubject.value;
    if (!currentUser) {
      console.error('Cannot delete message - user not logged in');
      return;
    }

    try {
      // Remove the message from local state immediately for better UX
      const currentMessages = this.messagesSubject.value;
      this.messagesSubject.next(
        currentMessages.filter(msg => msg.id !== messageId)
      );

      // Emit socket event for real-time updates
      if (this.socketService.isConnectedSubject.value) {
        this.socketService.deleteMessage(messageId);
      } else {
        // Fallback to HTTP API if socket not connected
        await this.apiService.deleteMessage(messageId, currentUser).toPromise();
      }
    } catch (error) {
      console.error('Failed to delete message:', error);
      throw error;
    }
  }

  showAdminPanel(): void {
    this.showAdminPanelSubject.next(true);
  }

  hideAdminPanel(): void {
    this.showAdminPanelSubject.next(false);
  }

  startEditingMessage(message: Message): void {
    this.editingMessageSubject.next(message);
  }

  cancelEditingMessage(): void {
    this.editingMessageSubject.next(null);
  }

  logout(): void {
    try {
      // Disconnect socket first
      this.socketService.disconnect();
      
      // Clear interval
      if (this.messageRefreshInterval) {
        clearInterval(this.messageRefreshInterval);
        this.messageRefreshInterval = null;
      }
      
      // Reset all subjects
      this.userSubject.next(null);
      this.messagesSubject.next([]);
      this.onlineUsersSubject.next([]);
      this.groupsSubject.next([]);
      this.currentGroupSubject.next(null);
      this.replyToSubject.next(null);
      this.loadingSubject.next(false);
      this.isAdminSubject.next(false);
      this.showAdminPanelSubject.next(false);
      this.securityInfoSubject.next(null);
      this.editingMessageSubject.next(null);
      

    } catch (error) {
      console.error('Error during logout:', error);
    }
  }

  // Method to refresh security info for all components
  refreshSecurityInfo(): void {
    this.apiService.getSecurityInfo().subscribe({
      next: (data) => {
        this.securityInfoSubject.next(data);
      },
      error: (error) => {
        console.error('Failed to refresh security info:', error);
      }
    });
  }

  getCurrentUser(): string | null {
    return this.userSubject.value;
  }
}
