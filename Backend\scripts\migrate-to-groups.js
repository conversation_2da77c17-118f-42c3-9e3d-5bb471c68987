const { query } = require('../database/db');
const Group = require('../models/Group');

async function migrateToGroups() {
  try {
    console.log('🔄 Starting migration to groups...');

    // Create groups table
    console.log('Creating groups table...');
    await query(`
      CREATE TABLE IF NOT EXISTS groups (
        id SERIAL PRIMARY KEY,
        name VARCHAR(100) UNIQUE NOT NULL,
        description TEXT,
        created_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT true
      )
    `);

    // Create user group permissions table
    console.log('Creating user_group_permissions table...');
    await query(`
      CREATE TABLE IF NOT EXISTS user_group_permissions (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        group_id INTEGER REFERENCES groups(id) ON DELETE CASCADE,
        granted_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
        granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT true,
        UNIQUE(user_id, group_id)
      )
    `);

    // Add group_id column to messages table
    console.log('Adding group_id column to messages table...');
    try {
      await query(`
        ALTER TABLE messages ADD COLUMN group_id INTEGER REFERENCES groups(id) ON DELETE CASCADE
      `);
    } catch (error) {
      if (error.code !== '42701') { // Column already exists
        throw error;
      }
      console.log('group_id column already exists');
    }

    // Create indexes
    console.log('Creating indexes...');
    await query(`
      CREATE INDEX IF NOT EXISTS idx_messages_group_id ON messages(group_id)
    `);
    await query(`
      CREATE INDEX IF NOT EXISTS idx_user_group_permissions_user_id ON user_group_permissions(user_id)
    `);
    await query(`
      CREATE INDEX IF NOT EXISTS idx_user_group_permissions_group_id ON user_group_permissions(group_id)
    `);
    await query(`
      CREATE INDEX IF NOT EXISTS idx_groups_name ON groups(name)
    `);

    // Create default "General" group
    console.log('Creating default General group...');
    const generalGroup = await Group.create('General', 'Default chat group for all users');
    console.log('Created General group with ID:', generalGroup.id);

    // Get all existing users
    console.log('Getting existing users...');
    const users = await query('SELECT id, username FROM users');
    console.log(`Found ${users.rows.length} existing users`);

    // Grant all users access to the General group
    console.log('Granting all users access to General group...');
    for (const user of users.rows) {
      try {
        await Group.grantUserAccess(user.id, generalGroup.id);
        console.log(`Granted access to ${user.username}`);
      } catch (error) {
        console.log(`User ${user.username} already has access or error:`, error.message);
      }
    }

    // Update existing messages to belong to General group
    console.log('Updating existing messages to belong to General group...');
    const updateResult = await query(`
      UPDATE messages 
      SET group_id = $1 
      WHERE group_id IS NULL
    `, [generalGroup.id]);
    console.log(`Updated ${updateResult.rowCount} messages to belong to General group`);

    console.log('✅ Migration completed successfully!');
    console.log('📝 Summary:');
    console.log(`   - Created groups table`);
    console.log(`   - Created user_group_permissions table`);
    console.log(`   - Added group_id column to messages`);
    console.log(`   - Created default "General" group`);
    console.log(`   - Granted all ${users.rows.length} users access to General group`);
    console.log(`   - Updated ${updateResult.rowCount} existing messages to General group`);

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  migrateToGroups()
    .then(() => {
      console.log('Migration completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}

module.exports = migrateToGroups;
