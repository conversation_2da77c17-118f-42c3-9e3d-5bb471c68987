{"ast": null, "code": "export function on(obj, ev, fn) {\n  obj.on(ev, fn);\n  return function subDestroy() {\n    obj.off(ev, fn);\n  };\n}", "map": {"version": 3, "names": ["on", "obj", "ev", "fn", "subDestroy", "off"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/socket.io-client/build/esm/on.js"], "sourcesContent": ["export function on(obj, ev, fn) {\n    obj.on(ev, fn);\n    return function subDestroy() {\n        obj.off(ev, fn);\n    };\n}\n"], "mappings": "AAAA,OAAO,SAASA,EAAT,CAAYC,GAAZ,EAAiBC,EAAjB,EAAqBC,EAArB,EAAyB;EAC5BF,GAAG,CAACD,EAAJ,CAAOE,EAAP,EAAWC,EAAX;EACA,OAAO,SAASC,UAAT,GAAsB;IACzBH,GAAG,CAACI,GAAJ,CAAQH,EAAR,EAAYC,EAAZ;EACH,CAFD;AAGH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}