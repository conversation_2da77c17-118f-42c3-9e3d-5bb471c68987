{"ast": null, "code": "const {\n  isArray\n} = Array;\nconst {\n  getPrototypeOf,\n  prototype: objectProto,\n  keys: getKeys\n} = Object;\nexport function argsArgArrayOrObject(args) {\n  if (args.length === 1) {\n    const first = args[0];\n\n    if (isArray(first)) {\n      return {\n        args: first,\n        keys: null\n      };\n    }\n\n    if (isPOJO(first)) {\n      const keys = getKeys(first);\n      return {\n        args: keys.map(key => first[key]),\n        keys\n      };\n    }\n  }\n\n  return {\n    args: args,\n    keys: null\n  };\n}\n\nfunction isPOJO(obj) {\n  return obj && typeof obj === 'object' && getPrototypeOf(obj) === objectProto;\n}", "map": {"version": 3, "names": ["isArray", "Array", "getPrototypeOf", "prototype", "objectProto", "keys", "get<PERSON><PERSON><PERSON>", "Object", "argsArgArrayOrObject", "args", "length", "first", "isPOJO", "map", "key", "obj"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/rxjs/dist/esm/internal/util/argsArgArrayOrObject.js"], "sourcesContent": ["const { isArray } = Array;\nconst { getPrototypeOf, prototype: objectProto, keys: getKeys } = Object;\nexport function argsArgArrayOrObject(args) {\n    if (args.length === 1) {\n        const first = args[0];\n        if (isArray(first)) {\n            return { args: first, keys: null };\n        }\n        if (isPOJO(first)) {\n            const keys = getKeys(first);\n            return {\n                args: keys.map((key) => first[key]),\n                keys,\n            };\n        }\n    }\n    return { args: args, keys: null };\n}\nfunction isPOJO(obj) {\n    return obj && typeof obj === 'object' && getPrototypeOf(obj) === objectProto;\n}\n"], "mappings": "AAAA,MAAM;EAAEA;AAAF,IAAcC,KAApB;AACA,MAAM;EAAEC,cAAF;EAAkBC,SAAS,EAAEC,WAA7B;EAA0CC,IAAI,EAAEC;AAAhD,IAA4DC,MAAlE;AACA,OAAO,SAASC,oBAAT,CAA8BC,IAA9B,EAAoC;EACvC,IAAIA,IAAI,CAACC,MAAL,KAAgB,CAApB,EAAuB;IACnB,MAAMC,KAAK,GAAGF,IAAI,CAAC,CAAD,CAAlB;;IACA,IAAIT,OAAO,CAACW,KAAD,CAAX,EAAoB;MAChB,OAAO;QAAEF,IAAI,EAAEE,KAAR;QAAeN,IAAI,EAAE;MAArB,CAAP;IACH;;IACD,IAAIO,MAAM,CAACD,KAAD,CAAV,EAAmB;MACf,MAAMN,IAAI,GAAGC,OAAO,CAACK,KAAD,CAApB;MACA,OAAO;QACHF,IAAI,EAAEJ,IAAI,CAACQ,GAAL,CAAUC,GAAD,IAASH,KAAK,CAACG,GAAD,CAAvB,CADH;QAEHT;MAFG,CAAP;IAIH;EACJ;;EACD,OAAO;IAAEI,IAAI,EAAEA,IAAR;IAAcJ,IAAI,EAAE;EAApB,CAAP;AACH;;AACD,SAASO,MAAT,CAAgBG,GAAhB,EAAqB;EACjB,OAAOA,GAAG,IAAI,OAAOA,GAAP,KAAe,QAAtB,IAAkCb,cAAc,CAACa,GAAD,CAAd,KAAwBX,WAAjE;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}