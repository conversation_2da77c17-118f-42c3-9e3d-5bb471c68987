{"ast": null, "code": "import { operate } from '../util/lift';\nimport { scanInternals } from './scanInternals';\nexport function scan(accumulator, seed) {\n  return operate(scanInternals(accumulator, seed, arguments.length >= 2, true));\n}", "map": {"version": 3, "names": ["operate", "scanInternals", "scan", "accumulator", "seed", "arguments", "length"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/rxjs/dist/esm/internal/operators/scan.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { scanInternals } from './scanInternals';\nexport function scan(accumulator, seed) {\n    return operate(scanInternals(accumulator, seed, arguments.length >= 2, true));\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,cAAxB;AACA,SAASC,aAAT,QAA8B,iBAA9B;AACA,OAAO,SAASC,IAAT,CAAcC,WAAd,EAA2BC,IAA3B,EAAiC;EACpC,OAAOJ,OAAO,CAACC,aAAa,CAACE,WAAD,EAAcC,IAAd,EAAoBC,SAAS,CAACC,MAAV,IAAoB,CAAxC,EAA2C,IAA3C,CAAd,CAAd;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}