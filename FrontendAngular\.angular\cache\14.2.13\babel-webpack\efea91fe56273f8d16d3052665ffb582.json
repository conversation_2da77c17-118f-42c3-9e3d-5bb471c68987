{"ast": null, "code": "import { Subject } from './Subject';\nimport { dateTimestampProvider } from './scheduler/dateTimestampProvider';\nexport class ReplaySubject extends Subject {\n  constructor(_bufferSize = Infinity, _windowTime = Infinity, _timestampProvider = dateTimestampProvider) {\n    super();\n    this._bufferSize = _bufferSize;\n    this._windowTime = _windowTime;\n    this._timestampProvider = _timestampProvider;\n    this._buffer = [];\n    this._infiniteTimeWindow = true;\n    this._infiniteTimeWindow = _windowTime === Infinity;\n    this._bufferSize = Math.max(1, _bufferSize);\n    this._windowTime = Math.max(1, _windowTime);\n  }\n\n  next(value) {\n    const {\n      isStopped,\n      _buffer,\n      _infiniteTimeWindow,\n      _timestampProvider,\n      _windowTime\n    } = this;\n\n    if (!isStopped) {\n      _buffer.push(value);\n\n      !_infiniteTimeWindow && _buffer.push(_timestampProvider.now() + _windowTime);\n    }\n\n    this._trimBuffer();\n\n    super.next(value);\n  }\n\n  _subscribe(subscriber) {\n    this._throwIfClosed();\n\n    this._trimBuffer();\n\n    const subscription = this._innerSubscribe(subscriber);\n\n    const {\n      _infiniteTimeWindow,\n      _buffer\n    } = this;\n\n    const copy = _buffer.slice();\n\n    for (let i = 0; i < copy.length && !subscriber.closed; i += _infiniteTimeWindow ? 1 : 2) {\n      subscriber.next(copy[i]);\n    }\n\n    this._checkFinalizedStatuses(subscriber);\n\n    return subscription;\n  }\n\n  _trimBuffer() {\n    const {\n      _bufferSize,\n      _timestampProvider,\n      _buffer,\n      _infiniteTimeWindow\n    } = this;\n    const adjustedBufferSize = (_infiniteTimeWindow ? 1 : 2) * _bufferSize;\n    _bufferSize < Infinity && adjustedBufferSize < _buffer.length && _buffer.splice(0, _buffer.length - adjustedBufferSize);\n\n    if (!_infiniteTimeWindow) {\n      const now = _timestampProvider.now();\n\n      let last = 0;\n\n      for (let i = 1; i < _buffer.length && _buffer[i] <= now; i += 2) {\n        last = i;\n      }\n\n      last && _buffer.splice(0, last + 1);\n    }\n  }\n\n} //# sourceMappingURL=ReplaySubject.js.map", "map": null, "metadata": {}, "sourceType": "module"}