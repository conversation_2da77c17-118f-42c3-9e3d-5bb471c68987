{"ast": null, "code": "import { Polling } from \"./polling.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globals.node.js\";\nimport { hasCORS } from \"../contrib/has-cors.js\";\n\nfunction empty() {}\n\nexport class BaseXHR extends Polling {\n  /**\n   * XHR Polling constructor.\n   *\n   * @param {Object} opts\n   * @package\n   */\n  constructor(opts) {\n    super(opts);\n\n    if (typeof location !== \"undefined\") {\n      const isSSL = \"https:\" === location.protocol;\n      let port = location.port; // some user agents have empty `location.port`\n\n      if (!port) {\n        port = isSSL ? \"443\" : \"80\";\n      }\n\n      this.xd = typeof location !== \"undefined\" && opts.hostname !== location.hostname || port !== opts.port;\n    }\n  }\n  /**\n   * Sends data.\n   *\n   * @param {String} data to send.\n   * @param {Function} called upon flush.\n   * @private\n   */\n\n\n  doWrite(data, fn) {\n    const req = this.request({\n      method: \"POST\",\n      data: data\n    });\n    req.on(\"success\", fn);\n    req.on(\"error\", (xhrStatus, context) => {\n      this.onError(\"xhr post error\", xhrStatus, context);\n    });\n  }\n  /**\n   * Starts a poll cycle.\n   *\n   * @private\n   */\n\n\n  doPoll() {\n    const req = this.request();\n    req.on(\"data\", this.onData.bind(this));\n    req.on(\"error\", (xhrStatus, context) => {\n      this.onError(\"xhr poll error\", xhrStatus, context);\n    });\n    this.pollXhr = req;\n  }\n\n}\nexport class Request extends Emitter {\n  /**\n   * Request constructor\n   *\n   * @param {Object} options\n   * @package\n   */\n  constructor(createRequest, uri, opts) {\n    super();\n    this.createRequest = createRequest;\n    installTimerFunctions(this, opts);\n    this._opts = opts;\n    this._method = opts.method || \"GET\";\n    this._uri = uri;\n    this._data = undefined !== opts.data ? opts.data : null;\n\n    this._create();\n  }\n  /**\n   * Creates the XHR object and sends the request.\n   *\n   * @private\n   */\n\n\n  _create() {\n    var _a;\n\n    const opts = pick(this._opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n    opts.xdomain = !!this._opts.xd;\n    const xhr = this._xhr = this.createRequest(opts);\n\n    try {\n      xhr.open(this._method, this._uri, true);\n\n      try {\n        if (this._opts.extraHeaders) {\n          // @ts-ignore\n          xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n\n          for (let i in this._opts.extraHeaders) {\n            if (this._opts.extraHeaders.hasOwnProperty(i)) {\n              xhr.setRequestHeader(i, this._opts.extraHeaders[i]);\n            }\n          }\n        }\n      } catch (e) {}\n\n      if (\"POST\" === this._method) {\n        try {\n          xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n        } catch (e) {}\n      }\n\n      try {\n        xhr.setRequestHeader(\"Accept\", \"*/*\");\n      } catch (e) {}\n\n      (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.addCookies(xhr); // ie6 check\n\n      if (\"withCredentials\" in xhr) {\n        xhr.withCredentials = this._opts.withCredentials;\n      }\n\n      if (this._opts.requestTimeout) {\n        xhr.timeout = this._opts.requestTimeout;\n      }\n\n      xhr.onreadystatechange = () => {\n        var _a;\n\n        if (xhr.readyState === 3) {\n          (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies( // @ts-ignore\n          xhr.getResponseHeader(\"set-cookie\"));\n        }\n\n        if (4 !== xhr.readyState) return;\n\n        if (200 === xhr.status || 1223 === xhr.status) {\n          this._onLoad();\n        } else {\n          // make sure the `error` event handler that's user-set\n          // does not throw in the same tick and gets caught here\n          this.setTimeoutFn(() => {\n            this._onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n          }, 0);\n        }\n      };\n\n      xhr.send(this._data);\n    } catch (e) {\n      // Need to defer since .create() is called directly from the constructor\n      // and thus the 'error' event can only be only bound *after* this exception\n      // occurs.  Therefore, also, we cannot throw here at all.\n      this.setTimeoutFn(() => {\n        this._onError(e);\n      }, 0);\n      return;\n    }\n\n    if (typeof document !== \"undefined\") {\n      this._index = Request.requestsCount++;\n      Request.requests[this._index] = this;\n    }\n  }\n  /**\n   * Called upon error.\n   *\n   * @private\n   */\n\n\n  _onError(err) {\n    this.emitReserved(\"error\", err, this._xhr);\n\n    this._cleanup(true);\n  }\n  /**\n   * Cleans up house.\n   *\n   * @private\n   */\n\n\n  _cleanup(fromError) {\n    if (\"undefined\" === typeof this._xhr || null === this._xhr) {\n      return;\n    }\n\n    this._xhr.onreadystatechange = empty;\n\n    if (fromError) {\n      try {\n        this._xhr.abort();\n      } catch (e) {}\n    }\n\n    if (typeof document !== \"undefined\") {\n      delete Request.requests[this._index];\n    }\n\n    this._xhr = null;\n  }\n  /**\n   * Called upon load.\n   *\n   * @private\n   */\n\n\n  _onLoad() {\n    const data = this._xhr.responseText;\n\n    if (data !== null) {\n      this.emitReserved(\"data\", data);\n      this.emitReserved(\"success\");\n\n      this._cleanup();\n    }\n  }\n  /**\n   * Aborts the request.\n   *\n   * @package\n   */\n\n\n  abort() {\n    this._cleanup();\n  }\n\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\n\nif (typeof document !== \"undefined\") {\n  // @ts-ignore\n  if (typeof attachEvent === \"function\") {\n    // @ts-ignore\n    attachEvent(\"onunload\", unloadHandler);\n  } else if (typeof addEventListener === \"function\") {\n    const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n    addEventListener(terminationEvent, unloadHandler, false);\n  }\n}\n\nfunction unloadHandler() {\n  for (let i in Request.requests) {\n    if (Request.requests.hasOwnProperty(i)) {\n      Request.requests[i].abort();\n    }\n  }\n}\n\nconst hasXHR2 = function () {\n  const xhr = newRequest({\n    xdomain: false\n  });\n  return xhr && xhr.responseType !== null;\n}();\n/**\n * HTTP long-polling based on the built-in `XMLHttpRequest` object.\n *\n * Usage: browser\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest\n */\n\n\nexport class XHR extends BaseXHR {\n  constructor(opts) {\n    super(opts);\n    const forceBase64 = opts && opts.forceBase64;\n    this.supportsBinary = hasXHR2 && !forceBase64;\n  }\n\n  request(opts = {}) {\n    Object.assign(opts, {\n      xd: this.xd\n    }, this.opts);\n    return new Request(newRequest, this.uri(), opts);\n  }\n\n}\n\nfunction newRequest(opts) {\n  const xdomain = opts.xdomain; // XMLHttpRequest can be disabled on IE\n\n  try {\n    if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n      return new XMLHttpRequest();\n    }\n  } catch (e) {}\n\n  if (!xdomain) {\n    try {\n      return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n    } catch (e) {}\n  }\n}", "map": {"version": 3, "names": ["Polling", "Emitter", "installTimerFunctions", "pick", "globalThisShim", "globalThis", "hasCORS", "empty", "BaseXHR", "constructor", "opts", "location", "isSSL", "protocol", "port", "xd", "hostname", "doWrite", "data", "fn", "req", "request", "method", "on", "xhrStatus", "context", "onError", "doPoll", "onData", "bind", "pollXhr", "Request", "createRequest", "uri", "_opts", "_method", "_uri", "_data", "undefined", "_create", "_a", "xdomain", "xhr", "_xhr", "open", "extraHeaders", "setDisableHeaderCheck", "i", "hasOwnProperty", "setRequestHeader", "e", "cookieJar", "addCookies", "withCredentials", "requestTimeout", "timeout", "onreadystatechange", "readyState", "parseCookies", "getResponseHeader", "status", "_onLoad", "setTimeoutFn", "_onError", "send", "document", "_index", "requestsCount", "requests", "err", "emit<PERSON><PERSON><PERSON><PERSON>", "_cleanup", "fromError", "abort", "responseText", "attachEvent", "unload<PERSON><PERSON><PERSON>", "addEventListener", "terminationEvent", "hasXHR2", "newRequest", "responseType", "XHR", "forceBase64", "supportsBinary", "Object", "assign", "XMLHttpRequest", "concat", "join"], "sources": ["R:/chateye/FrontendAngular/node_modules/engine.io-client/build/esm/transports/polling-xhr.js"], "sourcesContent": ["import { Polling } from \"./polling.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globals.node.js\";\nimport { hasCORS } from \"../contrib/has-cors.js\";\nfunction empty() { }\nexport class BaseXHR extends Polling {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @package\n     */\n    constructor(opts) {\n        super(opts);\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd =\n                (typeof location !== \"undefined\" &&\n                    opts.hostname !== location.hostname) ||\n                    port !== opts.port;\n        }\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @private\n     */\n    doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data,\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @private\n     */\n    doPoll() {\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nexport class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @package\n     */\n    constructor(createRequest, uri, opts) {\n        super();\n        this.createRequest = createRequest;\n        installTimerFunctions(this, opts);\n        this._opts = opts;\n        this._method = opts.method || \"GET\";\n        this._uri = uri;\n        this._data = undefined !== opts.data ? opts.data : null;\n        this._create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @private\n     */\n    _create() {\n        var _a;\n        const opts = pick(this._opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this._opts.xd;\n        const xhr = (this._xhr = this.createRequest(opts));\n        try {\n            xhr.open(this._method, this._uri, true);\n            try {\n                if (this._opts.extraHeaders) {\n                    // @ts-ignore\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for (let i in this._opts.extraHeaders) {\n                        if (this._opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this._opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            }\n            catch (e) { }\n            if (\"POST\" === this._method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                }\n                catch (e) { }\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            }\n            catch (e) { }\n            (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.addCookies(xhr);\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this._opts.withCredentials;\n            }\n            if (this._opts.requestTimeout) {\n                xhr.timeout = this._opts.requestTimeout;\n            }\n            xhr.onreadystatechange = () => {\n                var _a;\n                if (xhr.readyState === 3) {\n                    (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(\n                    // @ts-ignore\n                    xhr.getResponseHeader(\"set-cookie\"));\n                }\n                if (4 !== xhr.readyState)\n                    return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this._onLoad();\n                }\n                else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(() => {\n                        this._onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            xhr.send(this._data);\n        }\n        catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(() => {\n                this._onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this._index = Request.requestsCount++;\n            Request.requests[this._index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @private\n     */\n    _onError(err) {\n        this.emitReserved(\"error\", err, this._xhr);\n        this._cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @private\n     */\n    _cleanup(fromError) {\n        if (\"undefined\" === typeof this._xhr || null === this._xhr) {\n            return;\n        }\n        this._xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this._xhr.abort();\n            }\n            catch (e) { }\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this._index];\n        }\n        this._xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @private\n     */\n    _onLoad() {\n        const data = this._xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this._cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @package\n     */\n    abort() {\n        this._cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    }\n    else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for (let i in Request.requests) {\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\nconst hasXHR2 = (function () {\n    const xhr = newRequest({\n        xdomain: false,\n    });\n    return xhr && xhr.responseType !== null;\n})();\n/**\n * HTTP long-polling based on the built-in `XMLHttpRequest` object.\n *\n * Usage: browser\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest\n */\nexport class XHR extends BaseXHR {\n    constructor(opts) {\n        super(opts);\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n    }\n    request(opts = {}) {\n        Object.assign(opts, { xd: this.xd }, this.opts);\n        return new Request(newRequest, this.uri(), opts);\n    }\n}\nfunction newRequest(opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    }\n    catch (e) { }\n    if (!xdomain) {\n        try {\n            return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        }\n        catch (e) { }\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,cAAxB;AACA,SAASC,OAAT,QAAwB,8BAAxB;AACA,SAASC,qBAAT,EAAgCC,IAAhC,QAA4C,YAA5C;AACA,SAASC,cAAc,IAAIC,UAA3B,QAA6C,oBAA7C;AACA,SAASC,OAAT,QAAwB,wBAAxB;;AACA,SAASC,KAAT,GAAiB,CAAG;;AACpB,OAAO,MAAMC,OAAN,SAAsBR,OAAtB,CAA8B;EACjC;AACJ;AACA;AACA;AACA;AACA;EACIS,WAAW,CAACC,IAAD,EAAO;IACd,MAAMA,IAAN;;IACA,IAAI,OAAOC,QAAP,KAAoB,WAAxB,EAAqC;MACjC,MAAMC,KAAK,GAAG,aAAaD,QAAQ,CAACE,QAApC;MACA,IAAIC,IAAI,GAAGH,QAAQ,CAACG,IAApB,CAFiC,CAGjC;;MACA,IAAI,CAACA,IAAL,EAAW;QACPA,IAAI,GAAGF,KAAK,GAAG,KAAH,GAAW,IAAvB;MACH;;MACD,KAAKG,EAAL,GACK,OAAOJ,QAAP,KAAoB,WAApB,IACGD,IAAI,CAACM,QAAL,KAAkBL,QAAQ,CAACK,QAD/B,IAEIF,IAAI,KAAKJ,IAAI,CAACI,IAHtB;IAIH;EACJ;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACIG,OAAO,CAACC,IAAD,EAAOC,EAAP,EAAW;IACd,MAAMC,GAAG,GAAG,KAAKC,OAAL,CAAa;MACrBC,MAAM,EAAE,MADa;MAErBJ,IAAI,EAAEA;IAFe,CAAb,CAAZ;IAIAE,GAAG,CAACG,EAAJ,CAAO,SAAP,EAAkBJ,EAAlB;IACAC,GAAG,CAACG,EAAJ,CAAO,OAAP,EAAgB,CAACC,SAAD,EAAYC,OAAZ,KAAwB;MACpC,KAAKC,OAAL,CAAa,gBAAb,EAA+BF,SAA/B,EAA0CC,OAA1C;IACH,CAFD;EAGH;EACD;AACJ;AACA;AACA;AACA;;;EACIE,MAAM,GAAG;IACL,MAAMP,GAAG,GAAG,KAAKC,OAAL,EAAZ;IACAD,GAAG,CAACG,EAAJ,CAAO,MAAP,EAAe,KAAKK,MAAL,CAAYC,IAAZ,CAAiB,IAAjB,CAAf;IACAT,GAAG,CAACG,EAAJ,CAAO,OAAP,EAAgB,CAACC,SAAD,EAAYC,OAAZ,KAAwB;MACpC,KAAKC,OAAL,CAAa,gBAAb,EAA+BF,SAA/B,EAA0CC,OAA1C;IACH,CAFD;IAGA,KAAKK,OAAL,GAAeV,GAAf;EACH;;AAnDgC;AAqDrC,OAAO,MAAMW,OAAN,SAAsB9B,OAAtB,CAA8B;EACjC;AACJ;AACA;AACA;AACA;AACA;EACIQ,WAAW,CAACuB,aAAD,EAAgBC,GAAhB,EAAqBvB,IAArB,EAA2B;IAClC;IACA,KAAKsB,aAAL,GAAqBA,aAArB;IACA9B,qBAAqB,CAAC,IAAD,EAAOQ,IAAP,CAArB;IACA,KAAKwB,KAAL,GAAaxB,IAAb;IACA,KAAKyB,OAAL,GAAezB,IAAI,CAACY,MAAL,IAAe,KAA9B;IACA,KAAKc,IAAL,GAAYH,GAAZ;IACA,KAAKI,KAAL,GAAaC,SAAS,KAAK5B,IAAI,CAACQ,IAAnB,GAA0BR,IAAI,CAACQ,IAA/B,GAAsC,IAAnD;;IACA,KAAKqB,OAAL;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIA,OAAO,GAAG;IACN,IAAIC,EAAJ;;IACA,MAAM9B,IAAI,GAAGP,IAAI,CAAC,KAAK+B,KAAN,EAAa,OAAb,EAAsB,KAAtB,EAA6B,KAA7B,EAAoC,YAApC,EAAkD,MAAlD,EAA0D,IAA1D,EAAgE,SAAhE,EAA2E,oBAA3E,EAAiG,WAAjG,CAAjB;IACAxB,IAAI,CAAC+B,OAAL,GAAe,CAAC,CAAC,KAAKP,KAAL,CAAWnB,EAA5B;IACA,MAAM2B,GAAG,GAAI,KAAKC,IAAL,GAAY,KAAKX,aAAL,CAAmBtB,IAAnB,CAAzB;;IACA,IAAI;MACAgC,GAAG,CAACE,IAAJ,CAAS,KAAKT,OAAd,EAAuB,KAAKC,IAA5B,EAAkC,IAAlC;;MACA,IAAI;QACA,IAAI,KAAKF,KAAL,CAAWW,YAAf,EAA6B;UACzB;UACAH,GAAG,CAACI,qBAAJ,IAA6BJ,GAAG,CAACI,qBAAJ,CAA0B,IAA1B,CAA7B;;UACA,KAAK,IAAIC,CAAT,IAAc,KAAKb,KAAL,CAAWW,YAAzB,EAAuC;YACnC,IAAI,KAAKX,KAAL,CAAWW,YAAX,CAAwBG,cAAxB,CAAuCD,CAAvC,CAAJ,EAA+C;cAC3CL,GAAG,CAACO,gBAAJ,CAAqBF,CAArB,EAAwB,KAAKb,KAAL,CAAWW,YAAX,CAAwBE,CAAxB,CAAxB;YACH;UACJ;QACJ;MACJ,CAVD,CAWA,OAAOG,CAAP,EAAU,CAAG;;MACb,IAAI,WAAW,KAAKf,OAApB,EAA6B;QACzB,IAAI;UACAO,GAAG,CAACO,gBAAJ,CAAqB,cAArB,EAAqC,0BAArC;QACH,CAFD,CAGA,OAAOC,CAAP,EAAU,CAAG;MAChB;;MACD,IAAI;QACAR,GAAG,CAACO,gBAAJ,CAAqB,QAArB,EAA+B,KAA/B;MACH,CAFD,CAGA,OAAOC,CAAP,EAAU,CAAG;;MACb,CAACV,EAAE,GAAG,KAAKN,KAAL,CAAWiB,SAAjB,MAAgC,IAAhC,IAAwCX,EAAE,KAAK,KAAK,CAApD,GAAwD,KAAK,CAA7D,GAAiEA,EAAE,CAACY,UAAH,CAAcV,GAAd,CAAjE,CAxBA,CAyBA;;MACA,IAAI,qBAAqBA,GAAzB,EAA8B;QAC1BA,GAAG,CAACW,eAAJ,GAAsB,KAAKnB,KAAL,CAAWmB,eAAjC;MACH;;MACD,IAAI,KAAKnB,KAAL,CAAWoB,cAAf,EAA+B;QAC3BZ,GAAG,CAACa,OAAJ,GAAc,KAAKrB,KAAL,CAAWoB,cAAzB;MACH;;MACDZ,GAAG,CAACc,kBAAJ,GAAyB,MAAM;QAC3B,IAAIhB,EAAJ;;QACA,IAAIE,GAAG,CAACe,UAAJ,KAAmB,CAAvB,EAA0B;UACtB,CAACjB,EAAE,GAAG,KAAKN,KAAL,CAAWiB,SAAjB,MAAgC,IAAhC,IAAwCX,EAAE,KAAK,KAAK,CAApD,GAAwD,KAAK,CAA7D,GAAiEA,EAAE,CAACkB,YAAH,EACjE;UACAhB,GAAG,CAACiB,iBAAJ,CAAsB,YAAtB,CAFiE,CAAjE;QAGH;;QACD,IAAI,MAAMjB,GAAG,CAACe,UAAd,EACI;;QACJ,IAAI,QAAQf,GAAG,CAACkB,MAAZ,IAAsB,SAASlB,GAAG,CAACkB,MAAvC,EAA+C;UAC3C,KAAKC,OAAL;QACH,CAFD,MAGK;UACD;UACA;UACA,KAAKC,YAAL,CAAkB,MAAM;YACpB,KAAKC,QAAL,CAAc,OAAOrB,GAAG,CAACkB,MAAX,KAAsB,QAAtB,GAAiClB,GAAG,CAACkB,MAArC,GAA8C,CAA5D;UACH,CAFD,EAEG,CAFH;QAGH;MACJ,CAnBD;;MAoBAlB,GAAG,CAACsB,IAAJ,CAAS,KAAK3B,KAAd;IACH,CArDD,CAsDA,OAAOa,CAAP,EAAU;MACN;MACA;MACA;MACA,KAAKY,YAAL,CAAkB,MAAM;QACpB,KAAKC,QAAL,CAAcb,CAAd;MACH,CAFD,EAEG,CAFH;MAGA;IACH;;IACD,IAAI,OAAOe,QAAP,KAAoB,WAAxB,EAAqC;MACjC,KAAKC,MAAL,GAAcnC,OAAO,CAACoC,aAAR,EAAd;MACApC,OAAO,CAACqC,QAAR,CAAiB,KAAKF,MAAtB,IAAgC,IAAhC;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;;;EACIH,QAAQ,CAACM,GAAD,EAAM;IACV,KAAKC,YAAL,CAAkB,OAAlB,EAA2BD,GAA3B,EAAgC,KAAK1B,IAArC;;IACA,KAAK4B,QAAL,CAAc,IAAd;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIA,QAAQ,CAACC,SAAD,EAAY;IAChB,IAAI,gBAAgB,OAAO,KAAK7B,IAA5B,IAAoC,SAAS,KAAKA,IAAtD,EAA4D;MACxD;IACH;;IACD,KAAKA,IAAL,CAAUa,kBAAV,GAA+BjD,KAA/B;;IACA,IAAIiE,SAAJ,EAAe;MACX,IAAI;QACA,KAAK7B,IAAL,CAAU8B,KAAV;MACH,CAFD,CAGA,OAAOvB,CAAP,EAAU,CAAG;IAChB;;IACD,IAAI,OAAOe,QAAP,KAAoB,WAAxB,EAAqC;MACjC,OAAOlC,OAAO,CAACqC,QAAR,CAAiB,KAAKF,MAAtB,CAAP;IACH;;IACD,KAAKvB,IAAL,GAAY,IAAZ;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIkB,OAAO,GAAG;IACN,MAAM3C,IAAI,GAAG,KAAKyB,IAAL,CAAU+B,YAAvB;;IACA,IAAIxD,IAAI,KAAK,IAAb,EAAmB;MACf,KAAKoD,YAAL,CAAkB,MAAlB,EAA0BpD,IAA1B;MACA,KAAKoD,YAAL,CAAkB,SAAlB;;MACA,KAAKC,QAAL;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;;;EACIE,KAAK,GAAG;IACJ,KAAKF,QAAL;EACH;;AAjJgC;AAmJrCxC,OAAO,CAACoC,aAAR,GAAwB,CAAxB;AACApC,OAAO,CAACqC,QAAR,GAAmB,EAAnB;AACA;AACA;AACA;AACA;AACA;;AACA,IAAI,OAAOH,QAAP,KAAoB,WAAxB,EAAqC;EACjC;EACA,IAAI,OAAOU,WAAP,KAAuB,UAA3B,EAAuC;IACnC;IACAA,WAAW,CAAC,UAAD,EAAaC,aAAb,CAAX;EACH,CAHD,MAIK,IAAI,OAAOC,gBAAP,KAA4B,UAAhC,EAA4C;IAC7C,MAAMC,gBAAgB,GAAG,gBAAgBzE,UAAhB,GAA6B,UAA7B,GAA0C,QAAnE;IACAwE,gBAAgB,CAACC,gBAAD,EAAmBF,aAAnB,EAAkC,KAAlC,CAAhB;EACH;AACJ;;AACD,SAASA,aAAT,GAAyB;EACrB,KAAK,IAAI7B,CAAT,IAAchB,OAAO,CAACqC,QAAtB,EAAgC;IAC5B,IAAIrC,OAAO,CAACqC,QAAR,CAAiBpB,cAAjB,CAAgCD,CAAhC,CAAJ,EAAwC;MACpChB,OAAO,CAACqC,QAAR,CAAiBrB,CAAjB,EAAoB0B,KAApB;IACH;EACJ;AACJ;;AACD,MAAMM,OAAO,GAAI,YAAY;EACzB,MAAMrC,GAAG,GAAGsC,UAAU,CAAC;IACnBvC,OAAO,EAAE;EADU,CAAD,CAAtB;EAGA,OAAOC,GAAG,IAAIA,GAAG,CAACuC,YAAJ,KAAqB,IAAnC;AACH,CALe,EAAhB;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,OAAO,MAAMC,GAAN,SAAkB1E,OAAlB,CAA0B;EAC7BC,WAAW,CAACC,IAAD,EAAO;IACd,MAAMA,IAAN;IACA,MAAMyE,WAAW,GAAGzE,IAAI,IAAIA,IAAI,CAACyE,WAAjC;IACA,KAAKC,cAAL,GAAsBL,OAAO,IAAI,CAACI,WAAlC;EACH;;EACD9D,OAAO,CAACX,IAAI,GAAG,EAAR,EAAY;IACf2E,MAAM,CAACC,MAAP,CAAc5E,IAAd,EAAoB;MAAEK,EAAE,EAAE,KAAKA;IAAX,CAApB,EAAqC,KAAKL,IAA1C;IACA,OAAO,IAAIqB,OAAJ,CAAYiD,UAAZ,EAAwB,KAAK/C,GAAL,EAAxB,EAAoCvB,IAApC,CAAP;EACH;;AAT4B;;AAWjC,SAASsE,UAAT,CAAoBtE,IAApB,EAA0B;EACtB,MAAM+B,OAAO,GAAG/B,IAAI,CAAC+B,OAArB,CADsB,CAEtB;;EACA,IAAI;IACA,IAAI,gBAAgB,OAAO8C,cAAvB,KAA0C,CAAC9C,OAAD,IAAYnC,OAAtD,CAAJ,EAAoE;MAChE,OAAO,IAAIiF,cAAJ,EAAP;IACH;EACJ,CAJD,CAKA,OAAOrC,CAAP,EAAU,CAAG;;EACb,IAAI,CAACT,OAAL,EAAc;IACV,IAAI;MACA,OAAO,IAAIpC,UAAU,CAAC,CAAC,QAAD,EAAWmF,MAAX,CAAkB,QAAlB,EAA4BC,IAA5B,CAAiC,GAAjC,CAAD,CAAd,CAAsD,mBAAtD,CAAP;IACH,CAFD,CAGA,OAAOvC,CAAP,EAAU,CAAG;EAChB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}