{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { defaultThrottleConfig, throttle } from './throttle';\nimport { timer } from '../observable/timer';\nexport function throttleTime(duration, scheduler = asyncScheduler, config = defaultThrottleConfig) {\n  const duration$ = timer(duration, scheduler);\n  return throttle(() => duration$, config);\n}", "map": {"version": 3, "names": ["asyncScheduler", "defaultThrottleConfig", "throttle", "timer", "throttleTime", "duration", "scheduler", "config", "duration$"], "sources": ["R:/chateye/FrontendAngular/node_modules/rxjs/dist/esm/internal/operators/throttleTime.js"], "sourcesContent": ["import { asyncScheduler } from '../scheduler/async';\nimport { defaultThrottleConfig, throttle } from './throttle';\nimport { timer } from '../observable/timer';\nexport function throttleTime(duration, scheduler = asyncScheduler, config = defaultThrottleConfig) {\n    const duration$ = timer(duration, scheduler);\n    return throttle(() => duration$, config);\n}\n"], "mappings": "AAAA,SAASA,cAAT,QAA+B,oBAA/B;AACA,SAASC,qBAAT,EAAgCC,QAAhC,QAAgD,YAAhD;AACA,SAASC,KAAT,QAAsB,qBAAtB;AACA,OAAO,SAASC,YAAT,CAAsBC,QAAtB,EAAgCC,SAAS,GAAGN,cAA5C,EAA4DO,MAAM,GAAGN,qBAArE,EAA4F;EAC/F,MAAMO,SAAS,GAAGL,KAAK,CAACE,QAAD,EAAWC,SAAX,CAAvB;EACA,OAAOJ,QAAQ,CAAC,MAAMM,SAAP,EAAkBD,MAAlB,CAAf;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}