{"ast": null, "code": "import _asyncToGenerator from \"R:/chateye/Frontend/chateye-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { ApiService, Message, Group, User, SecurityInfo } from './api.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./api.service\";\nimport * as i2 from \"./socket.service\";\nimport * as i3 from \"./notification.service\";\nexport class ChatService {\n  constructor(apiService, socketService, notificationService) {\n    this.apiService = apiService;\n    this.socketService = socketService;\n    this.notificationService = notificationService;\n    this.userSubject = new BehaviorSubject(null);\n    this.messagesSubject = new BehaviorSubject([]);\n    this.onlineUsersSubject = new BehaviorSubject([]);\n    this.groupsSubject = new BehaviorSubject([]);\n    this.currentGroupSubject = new BehaviorSubject(null);\n    this.replyToSubject = new BehaviorSubject(null);\n    this.loadingSubject = new BehaviorSubject(false);\n    this.isAdminSubject = new BehaviorSubject(false);\n    this.showAdminPanelSubject = new BehaviorSubject(false);\n    this.securityInfoSubject = new BehaviorSubject(null); // Public observables\n\n    this.user$ = this.userSubject.asObservable();\n    this.messages$ = this.messagesSubject.asObservable();\n    this.onlineUsers$ = this.onlineUsersSubject.asObservable();\n    this.groups$ = this.groupsSubject.asObservable();\n    this.currentGroup$ = this.currentGroupSubject.asObservable();\n    this.replyTo$ = this.replyToSubject.asObservable();\n    this.loading$ = this.loadingSubject.asObservable();\n    this.isAdmin$ = this.isAdminSubject.asObservable();\n    this.showAdminPanel$ = this.showAdminPanelSubject.asObservable();\n    this.securityInfo$ = this.securityInfoSubject.asObservable();\n    this.connected$ = this.socketService.isConnected$; // Computed observables\n\n    this.isLoggedIn$ = this.user$.pipe(map(user => !!user));\n    this.setupSocketListeners();\n    this.setupMessageRefresh();\n  }\n\n  setupSocketListeners() {\n    this.socketService.on('connect', () => {\n      console.log('Connected to server');\n    });\n    this.socketService.on('disconnect', () => {\n      console.log('Disconnected from server');\n    });\n    this.socketService.on('userGroups', userGroups => {\n      console.log('Received user groups:', userGroups);\n      this.groupsSubject.next(userGroups || []);\n\n      if (userGroups && userGroups.length > 0) {\n        this.currentGroupSubject.next(userGroups[0]);\n      }\n    });\n    this.socketService.on('recentMessages', messages => {\n      console.log('Received recent messages:', messages.length);\n      this.messagesSubject.next(messages || []);\n    });\n    this.socketService.on('groupJoined', ({\n      groupId\n    }) => {\n      console.log('Joined group:', groupId);\n      const groups = this.groupsSubject.value;\n      const group = groups.find(g => g.id === groupId);\n\n      if (group) {\n        this.currentGroupSubject.next(group);\n      }\n    });\n    this.socketService.on('newMessage', message => {\n      console.log('New message received:', message);\n      const currentMessages = this.messagesSubject.value;\n      this.messagesSubject.next([...currentMessages, message]); // Show notification if not current user and window not focused\n\n      const currentUser = this.userSubject.value;\n\n      if (message.username !== currentUser && document.hidden) {\n        this.notificationService.showMessageNotification(message.username, message.text);\n      }\n    });\n    this.socketService.on('reactionUpdate', ({\n      messageId,\n      reactions\n    }) => {\n      console.log('Reaction update:', messageId, reactions);\n      const currentMessages = this.messagesSubject.value;\n      this.messagesSubject.next(currentMessages.map(msg => msg.id === messageId ? { ...msg,\n        reactions\n      } : msg));\n    });\n    this.socketService.on('onlineUsersUpdate', users => {\n      console.log('Online users updated:', users);\n      this.onlineUsersSubject.next(users || []);\n    });\n    this.socketService.on('userJoined', ({\n      username\n    }) => {\n      console.log('User joined:', username); // Online users will be updated via onlineUsersUpdate event\n    });\n    this.socketService.on('userLeft', ({\n      username\n    }) => {\n      console.log('User left:', username); // Online users will be updated via onlineUsersUpdate event\n    });\n    this.socketService.on('error', error => {\n      console.error('Socket error:', error);\n    });\n  }\n\n  setupMessageRefresh() {\n    // Refresh messages every 5 seconds if socket is not connected\n    this.messageRefreshInterval = setInterval(() => {\n      if (!this.socketService.isConnectedSubject.value) {\n        const currentGroup = this.currentGroupSubject.value;\n\n        if (currentGroup) {\n          console.log('Refreshing messages via HTTP API');\n          this.loadRecentMessages(currentGroup.id);\n        }\n      }\n    }, 5000);\n  }\n\n  login(_x) {\n    var _this = this;\n\n    return _asyncToGenerator(function* (username, inviteCode = null) {\n      try {\n        _this.loadingSubject.next(true);\n\n        console.log('Starting login process for:', username);\n        const userData = yield _this.apiService.loginUser(username, inviteCode || undefined).toPromise();\n        console.log('Login API response:', userData); // Connect to socket with auth data\n\n        console.log('Connecting to socket...');\n\n        _this.socketService.connect(username, inviteCode);\n\n        _this.userSubject.next(username);\n\n        _this.isAdminSubject.next(userData?.isAdmin || false);\n\n        _this.securityInfoSubject.next(userData?.securityInfo || null);\n\n        console.log('Login completed successfully');\n      } catch (error) {\n        console.error('Login failed:', error);\n        throw error;\n      } finally {\n        _this.loadingSubject.next(false);\n      }\n    }).apply(this, arguments);\n  }\n\n  loadRecentMessages(groupId) {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        const messages = yield _this2.apiService.getMessages(groupId, 50).toPromise();\n\n        _this2.messagesSubject.next(messages || []);\n      } catch (error) {\n        console.error('Failed to load messages:', error);\n      }\n    })();\n  }\n\n  joinGroup(groupId) {\n    const currentUser = this.userSubject.value;\n    if (!groupId || !currentUser) return;\n    console.log('Joining group:', groupId);\n    this.socketService.joinGroup(groupId);\n    const groups = this.groupsSubject.value;\n    const group = groups.find(g => g.id === groupId);\n\n    if (group) {\n      this.currentGroupSubject.next(group); // Load recent messages for the group\n\n      this.loadRecentMessages(groupId);\n    }\n  }\n\n  sendMessage(text, replyToId = null) {\n    const currentUser = this.userSubject.value;\n    const currentGroup = this.currentGroupSubject.value;\n\n    if (!text.trim() || !currentUser || !currentGroup) {\n      console.error('Cannot send message - missing required data:', {\n        text,\n        currentUser,\n        currentGroup\n      });\n      return;\n    }\n\n    console.log('Sending message via socket:', {\n      text,\n      groupId: currentGroup.id,\n      replyToId\n    });\n    this.socketService.sendMessage(text, currentGroup.id, replyToId); // Fallback: If socket is not connected, try HTTP API\n\n    if (!this.socketService.isConnectedSubject.value) {\n      console.log('Socket not connected, trying HTTP API fallback');\n      this.apiService.sendMessage(text, currentUser, currentGroup.id, replyToId).subscribe({\n        next: response => {\n          console.log('Message sent via HTTP API:', response);\n        },\n        error: error => {\n          console.error('Failed to send message via HTTP API:', error);\n        }\n      });\n    }\n  }\n\n  replyToMessage(message) {\n    this.replyToSubject.next(message);\n  }\n\n  cancelReply() {\n    this.replyToSubject.next(null);\n  }\n\n  addReaction(messageId, emoji) {\n    this.socketService.addReaction(messageId, emoji);\n  }\n\n  removeReaction(messageId) {\n    this.socketService.removeReaction(messageId);\n  }\n\n  showAdminPanel() {\n    this.showAdminPanelSubject.next(true);\n  }\n\n  hideAdminPanel() {\n    this.showAdminPanelSubject.next(false);\n  }\n\n  logout() {\n    this.socketService.disconnect();\n\n    if (this.messageRefreshInterval) {\n      clearInterval(this.messageRefreshInterval);\n    }\n\n    this.userSubject.next(null);\n    this.messagesSubject.next([]);\n    this.onlineUsersSubject.next([]);\n    this.groupsSubject.next([]);\n    this.currentGroupSubject.next(null);\n    this.replyToSubject.next(null);\n    this.loadingSubject.next(false);\n    this.isAdminSubject.next(false);\n    this.showAdminPanelSubject.next(false);\n    this.securityInfoSubject.next(null);\n  }\n\n}\n\nChatService.ɵfac = function ChatService_Factory(t) {\n  return new (t || ChatService)(i0.ɵɵinject(i1.ApiService), i0.ɵɵinject(i2.SocketService), i0.ɵɵinject(i3.NotificationService));\n};\n\nChatService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: ChatService,\n  factory: ChatService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "mappings": ";AACA,SAASA,eAAT,QAA2D,MAA3D;AACA,SAASC,GAAT,QAAoB,gBAApB;AACA,SAASC,UAAT,EAAqBC,OAArB,EAA8BC,KAA9B,EAAqCC,IAArC,EAA2CC,YAA3C,QAA8E,eAA9E;;;;;AAOA,OAAM,MAAOC,WAAP,CAAkB;EA8BtBC,YACUC,UADV,EAEUC,aAFV,EAGUC,mBAHV,EAGkD;IAFxC;IACA;IACA;IAhCF,mBAAc,IAAIX,eAAJ,CAAmC,IAAnC,CAAd;IACA,uBAAkB,IAAIA,eAAJ,CAA+B,EAA/B,CAAlB;IACA,0BAAqB,IAAIA,eAAJ,CAA4B,EAA5B,CAArB;IACA,qBAAgB,IAAIA,eAAJ,CAA6B,EAA7B,CAAhB;IACA,2BAAsB,IAAIA,eAAJ,CAAkC,IAAlC,CAAtB;IACA,sBAAiB,IAAIA,eAAJ,CAAoC,IAApC,CAAjB;IACA,sBAAiB,IAAIA,eAAJ,CAA6B,KAA7B,CAAjB;IACA,sBAAiB,IAAIA,eAAJ,CAA6B,KAA7B,CAAjB;IACA,6BAAwB,IAAIA,eAAJ,CAA6B,KAA7B,CAAxB;IACA,2BAAsB,IAAIA,eAAJ,CAAyC,IAAzC,CAAtB,CAuB0C,CArBlD;;IACO,aAAQ,KAAKY,WAAL,CAAiBC,YAAjB,EAAR;IACA,iBAAY,KAAKC,eAAL,CAAqBD,YAArB,EAAZ;IACA,oBAAe,KAAKE,kBAAL,CAAwBF,YAAxB,EAAf;IACA,eAAU,KAAKG,aAAL,CAAmBH,YAAnB,EAAV;IACA,qBAAgB,KAAKI,mBAAL,CAAyBJ,YAAzB,EAAhB;IACA,gBAAW,KAAKK,cAAL,CAAoBL,YAApB,EAAX;IACA,gBAAW,KAAKM,cAAL,CAAoBN,YAApB,EAAX;IACA,gBAAW,KAAKO,cAAL,CAAoBP,YAApB,EAAX;IACA,uBAAkB,KAAKQ,qBAAL,CAA2BR,YAA3B,EAAlB;IACA,qBAAgB,KAAKS,mBAAL,CAAyBT,YAAzB,EAAhB;IACA,kBAAa,KAAKH,aAAL,CAAmBa,YAAhC,CAU2C,CARlD;;IACO,mBAAc,KAAKC,KAAL,CAAWC,IAAX,CAAgBxB,GAAG,CAACyB,IAAI,IAAI,CAAC,CAACA,IAAX,CAAnB,CAAd;IASL,KAAKC,oBAAL;IACA,KAAKC,mBAAL;EACD;;EAEOD,oBAAoB;IAC1B,KAAKjB,aAAL,CAAmBmB,EAAnB,CAAsB,SAAtB,EAAiC,MAAK;MACpCC,OAAO,CAACC,GAAR,CAAY,qBAAZ;IACD,CAFD;IAIA,KAAKrB,aAAL,CAAmBmB,EAAnB,CAAsB,YAAtB,EAAoC,MAAK;MACvCC,OAAO,CAACC,GAAR,CAAY,0BAAZ;IACD,CAFD;IAIA,KAAKrB,aAAL,CAAmBmB,EAAnB,CAAsB,YAAtB,EAAqCG,UAAD,IAAwB;MAC1DF,OAAO,CAACC,GAAR,CAAY,uBAAZ,EAAqCC,UAArC;MACA,KAAKhB,aAAL,CAAmBiB,IAAnB,CAAwBD,UAAU,IAAI,EAAtC;;MACA,IAAIA,UAAU,IAAIA,UAAU,CAACE,MAAX,GAAoB,CAAtC,EAAyC;QACvC,KAAKjB,mBAAL,CAAyBgB,IAAzB,CAA8BD,UAAU,CAAC,CAAD,CAAxC;MACD;IACF,CAND;IAQA,KAAKtB,aAAL,CAAmBmB,EAAnB,CAAsB,gBAAtB,EAAyCM,QAAD,IAAwB;MAC9DL,OAAO,CAACC,GAAR,CAAY,2BAAZ,EAAyCI,QAAQ,CAACD,MAAlD;MACA,KAAKpB,eAAL,CAAqBmB,IAArB,CAA0BE,QAAQ,IAAI,EAAtC;IACD,CAHD;IAKA,KAAKzB,aAAL,CAAmBmB,EAAnB,CAAsB,aAAtB,EAAqC,CAAC;MAAEO;IAAF,CAAD,KAAqC;MACxEN,OAAO,CAACC,GAAR,CAAY,eAAZ,EAA6BK,OAA7B;MACA,MAAMC,MAAM,GAAG,KAAKrB,aAAL,CAAmBsB,KAAlC;MACA,MAAMC,KAAK,GAAGF,MAAM,CAACG,IAAP,CAAYC,CAAC,IAAIA,CAAC,CAACC,EAAF,KAASN,OAA1B,CAAd;;MACA,IAAIG,KAAJ,EAAW;QACT,KAAKtB,mBAAL,CAAyBgB,IAAzB,CAA8BM,KAA9B;MACD;IACF,CAPD;IASA,KAAK7B,aAAL,CAAmBmB,EAAnB,CAAsB,YAAtB,EAAqCc,OAAD,IAAqB;MACvDb,OAAO,CAACC,GAAR,CAAY,uBAAZ,EAAqCY,OAArC;MACA,MAAMC,eAAe,GAAG,KAAK9B,eAAL,CAAqBwB,KAA7C;MACA,KAAKxB,eAAL,CAAqBmB,IAArB,CAA0B,CAAC,GAAGW,eAAJ,EAAqBD,OAArB,CAA1B,EAHuD,CAKvD;;MACA,MAAME,WAAW,GAAG,KAAKjC,WAAL,CAAiB0B,KAArC;;MACA,IAAIK,OAAO,CAACG,QAAR,KAAqBD,WAArB,IAAoCE,QAAQ,CAACC,MAAjD,EAAyD;QACvD,KAAKrC,mBAAL,CAAyBsC,uBAAzB,CAAiDN,OAAO,CAACG,QAAzD,EAAmEH,OAAO,CAACO,IAA3E;MACD;IACF,CAVD;IAYA,KAAKxC,aAAL,CAAmBmB,EAAnB,CAAsB,gBAAtB,EAAwC,CAAC;MAAEsB,SAAF;MAAaC;IAAb,CAAD,KAAsE;MAC5GtB,OAAO,CAACC,GAAR,CAAY,kBAAZ,EAAgCoB,SAAhC,EAA2CC,SAA3C;MACA,MAAMR,eAAe,GAAG,KAAK9B,eAAL,CAAqBwB,KAA7C;MACA,KAAKxB,eAAL,CAAqBmB,IAArB,CACEW,eAAe,CAAC3C,GAAhB,CAAoBoD,GAAG,IACrBA,GAAG,CAACX,EAAJ,KAAWS,SAAX,GACI,EAAE,GAAGE,GAAL;QAAUD;MAAV,CADJ,GAEIC,GAHN,CADF;IAOD,CAVD;IAYA,KAAK3C,aAAL,CAAmBmB,EAAnB,CAAsB,mBAAtB,EAA4CyB,KAAD,IAAkB;MAC3DxB,OAAO,CAACC,GAAR,CAAY,uBAAZ,EAAqCuB,KAArC;MACA,KAAKvC,kBAAL,CAAwBkB,IAAxB,CAA6BqB,KAAK,IAAI,EAAtC;IACD,CAHD;IAKA,KAAK5C,aAAL,CAAmBmB,EAAnB,CAAsB,YAAtB,EAAoC,CAAC;MAAEiB;IAAF,CAAD,KAAuC;MACzEhB,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA4Be,QAA5B,EADyE,CAEzE;IACD,CAHD;IAKA,KAAKpC,aAAL,CAAmBmB,EAAnB,CAAsB,UAAtB,EAAkC,CAAC;MAAEiB;IAAF,CAAD,KAAuC;MACvEhB,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0Be,QAA1B,EADuE,CAEvE;IACD,CAHD;IAKA,KAAKpC,aAAL,CAAmBmB,EAAnB,CAAsB,OAAtB,EAAgC0B,KAAD,IAAe;MAC5CzB,OAAO,CAACyB,KAAR,CAAc,eAAd,EAA+BA,KAA/B;IACD,CAFD;EAGD;;EAEO3B,mBAAmB;IACzB;IACA,KAAK4B,sBAAL,GAA8BC,WAAW,CAAC,MAAK;MAC7C,IAAI,CAAC,KAAK/C,aAAL,CAAmBgD,kBAAnB,CAAsCpB,KAA3C,EAAkD;QAChD,MAAMqB,YAAY,GAAG,KAAK1C,mBAAL,CAAyBqB,KAA9C;;QACA,IAAIqB,YAAJ,EAAkB;UAChB7B,OAAO,CAACC,GAAR,CAAY,kCAAZ;UACA,KAAK6B,kBAAL,CAAwBD,YAAY,CAACjB,EAArC;QACD;MACF;IACF,CARwC,EAQtC,IARsC,CAAzC;EASD;;EAEKmB,KAAK,KAAmD;IAAA;;IAAA,oCAAlDf,QAAkD,EAAhCgB,aAA4B,IAAI;MAC5D,IAAI;QACF,KAAI,CAAC3C,cAAL,CAAoBc,IAApB,CAAyB,IAAzB;;QACAH,OAAO,CAACC,GAAR,CAAY,6BAAZ,EAA2Ce,QAA3C;QAEA,MAAMiB,QAAQ,SAAS,KAAI,CAACtD,UAAL,CAAgBuD,SAAhB,CAA0BlB,QAA1B,EAAoCgB,UAAU,IAAIG,SAAlD,EAA6DC,SAA7D,EAAvB;QACApC,OAAO,CAACC,GAAR,CAAY,qBAAZ,EAAmCgC,QAAnC,EALE,CAOF;;QACAjC,OAAO,CAACC,GAAR,CAAY,yBAAZ;;QACA,KAAI,CAACrB,aAAL,CAAmByD,OAAnB,CAA2BrB,QAA3B,EAAqCgB,UAArC;;QAEA,KAAI,CAAClD,WAAL,CAAiBqB,IAAjB,CAAsBa,QAAtB;;QACA,KAAI,CAAC1B,cAAL,CAAoBa,IAApB,CAAyB8B,QAAQ,EAAEK,OAAV,IAAqB,KAA9C;;QACA,KAAI,CAAC9C,mBAAL,CAAyBW,IAAzB,CAA8B8B,QAAQ,EAAEM,YAAV,IAA0B,IAAxD;;QAEAvC,OAAO,CAACC,GAAR,CAAY,8BAAZ;MAED,CAjBD,CAiBE,OAAOwB,KAAP,EAAc;QACdzB,OAAO,CAACyB,KAAR,CAAc,eAAd,EAA+BA,KAA/B;QACA,MAAMA,KAAN;MACD,CApBD,SAoBU;QACR,KAAI,CAACpC,cAAL,CAAoBc,IAApB,CAAyB,KAAzB;MACD;IAvB2D;EAwB7D;;EAEK2B,kBAAkB,CAACxB,OAAD,EAAgB;IAAA;;IAAA;MACtC,IAAI;QACF,MAAMD,QAAQ,SAAS,MAAI,CAAC1B,UAAL,CAAgB6D,WAAhB,CAA4BlC,OAA5B,EAAqC,EAArC,EAAyC8B,SAAzC,EAAvB;;QACA,MAAI,CAACpD,eAAL,CAAqBmB,IAArB,CAA0BE,QAAQ,IAAI,EAAtC;MACD,CAHD,CAGE,OAAOoB,KAAP,EAAc;QACdzB,OAAO,CAACyB,KAAR,CAAc,0BAAd,EAA0CA,KAA1C;MACD;IANqC;EAOvC;;EAEDgB,SAAS,CAACnC,OAAD,EAAgB;IACvB,MAAMS,WAAW,GAAG,KAAKjC,WAAL,CAAiB0B,KAArC;IACA,IAAI,CAACF,OAAD,IAAY,CAACS,WAAjB,EAA8B;IAE9Bf,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA8BK,OAA9B;IACA,KAAK1B,aAAL,CAAmB6D,SAAnB,CAA6BnC,OAA7B;IACA,MAAMC,MAAM,GAAG,KAAKrB,aAAL,CAAmBsB,KAAlC;IACA,MAAMC,KAAK,GAAGF,MAAM,CAACG,IAAP,CAAYC,CAAC,IAAIA,CAAC,CAACC,EAAF,KAASN,OAA1B,CAAd;;IACA,IAAIG,KAAJ,EAAW;MACT,KAAKtB,mBAAL,CAAyBgB,IAAzB,CAA8BM,KAA9B,EADS,CAET;;MACA,KAAKqB,kBAAL,CAAwBxB,OAAxB;IACD;EACF;;EAEDoC,WAAW,CAACtB,IAAD,EAAeuB,YAA2B,IAA1C,EAA8C;IACvD,MAAM5B,WAAW,GAAG,KAAKjC,WAAL,CAAiB0B,KAArC;IACA,MAAMqB,YAAY,GAAG,KAAK1C,mBAAL,CAAyBqB,KAA9C;;IAEA,IAAI,CAACY,IAAI,CAACwB,IAAL,EAAD,IAAgB,CAAC7B,WAAjB,IAAgC,CAACc,YAArC,EAAmD;MACjD7B,OAAO,CAACyB,KAAR,CAAc,8CAAd,EAA8D;QAAEL,IAAF;QAAQL,WAAR;QAAqBc;MAArB,CAA9D;MACA;IACD;;IAED7B,OAAO,CAACC,GAAR,CAAY,6BAAZ,EAA2C;MAAEmB,IAAF;MAAQd,OAAO,EAAEuB,YAAY,CAACjB,EAA9B;MAAkC+B;IAAlC,CAA3C;IACA,KAAK/D,aAAL,CAAmB8D,WAAnB,CAA+BtB,IAA/B,EAAqCS,YAAY,CAACjB,EAAlD,EAAsD+B,SAAtD,EAVuD,CAYvD;;IACA,IAAI,CAAC,KAAK/D,aAAL,CAAmBgD,kBAAnB,CAAsCpB,KAA3C,EAAkD;MAChDR,OAAO,CAACC,GAAR,CAAY,gDAAZ;MACA,KAAKtB,UAAL,CAAgB+D,WAAhB,CAA4BtB,IAA5B,EAAkCL,WAAlC,EAA+Cc,YAAY,CAACjB,EAA5D,EAAgE+B,SAAhE,EAA2EE,SAA3E,CAAqF;QACnF1C,IAAI,EAAG2C,QAAD,IAAa;UACjB9C,OAAO,CAACC,GAAR,CAAY,4BAAZ,EAA0C6C,QAA1C;QACD,CAHkF;QAInFrB,KAAK,EAAGA,KAAD,IAAU;UACfzB,OAAO,CAACyB,KAAR,CAAc,sCAAd,EAAsDA,KAAtD;QACD;MANkF,CAArF;IAQD;EACF;;EAEDsB,cAAc,CAAClC,OAAD,EAAiB;IAC7B,KAAKzB,cAAL,CAAoBe,IAApB,CAAyBU,OAAzB;EACD;;EAEDmC,WAAW;IACT,KAAK5D,cAAL,CAAoBe,IAApB,CAAyB,IAAzB;EACD;;EAED8C,WAAW,CAAC5B,SAAD,EAAoB6B,KAApB,EAAiC;IAC1C,KAAKtE,aAAL,CAAmBqE,WAAnB,CAA+B5B,SAA/B,EAA0C6B,KAA1C;EACD;;EAEDC,cAAc,CAAC9B,SAAD,EAAkB;IAC9B,KAAKzC,aAAL,CAAmBuE,cAAnB,CAAkC9B,SAAlC;EACD;;EAED+B,cAAc;IACZ,KAAK7D,qBAAL,CAA2BY,IAA3B,CAAgC,IAAhC;EACD;;EAEDkD,cAAc;IACZ,KAAK9D,qBAAL,CAA2BY,IAA3B,CAAgC,KAAhC;EACD;;EAEDmD,MAAM;IACJ,KAAK1E,aAAL,CAAmB2E,UAAnB;;IACA,IAAI,KAAK7B,sBAAT,EAAiC;MAC/B8B,aAAa,CAAC,KAAK9B,sBAAN,CAAb;IACD;;IACD,KAAK5C,WAAL,CAAiBqB,IAAjB,CAAsB,IAAtB;IACA,KAAKnB,eAAL,CAAqBmB,IAArB,CAA0B,EAA1B;IACA,KAAKlB,kBAAL,CAAwBkB,IAAxB,CAA6B,EAA7B;IACA,KAAKjB,aAAL,CAAmBiB,IAAnB,CAAwB,EAAxB;IACA,KAAKhB,mBAAL,CAAyBgB,IAAzB,CAA8B,IAA9B;IACA,KAAKf,cAAL,CAAoBe,IAApB,CAAyB,IAAzB;IACA,KAAKd,cAAL,CAAoBc,IAApB,CAAyB,KAAzB;IACA,KAAKb,cAAL,CAAoBa,IAApB,CAAyB,KAAzB;IACA,KAAKZ,qBAAL,CAA2BY,IAA3B,CAAgC,KAAhC;IACA,KAAKX,mBAAL,CAAyBW,IAAzB,CAA8B,IAA9B;EACD;;AAlPqB;;;mBAAX1B,aAAWgF;AAAA;;;SAAXhF;EAAWiF,SAAXjF,WAAW;EAAAkF,YAFV", "names": ["BehaviorSubject", "map", "ApiService", "Message", "Group", "User", "SecurityInfo", "ChatService", "constructor", "apiService", "socketService", "notificationService", "userSubject", "asObservable", "messagesSubject", "onlineUsersSubject", "groupsSubject", "currentGroupSubject", "replyToSubject", "loadingSubject", "isAdminSubject", "showAdminPanelSubject", "securityInfoSubject", "isConnected$", "user$", "pipe", "user", "setupSocketListeners", "setupMessageRefresh", "on", "console", "log", "userGroups", "next", "length", "messages", "groupId", "groups", "value", "group", "find", "g", "id", "message", "currentMessages", "currentUser", "username", "document", "hidden", "showMessageNotification", "text", "messageId", "reactions", "msg", "users", "error", "messageRefreshInterval", "setInterval", "isConnectedSubject", "currentGroup", "loadRecentMessages", "login", "inviteCode", "userData", "loginUser", "undefined", "to<PERSON>romise", "connect", "isAdmin", "securityInfo", "getMessages", "joinGroup", "sendMessage", "replyToId", "trim", "subscribe", "response", "replyToMessage", "cancelReply", "addReaction", "emoji", "removeReaction", "showAdminPanel", "hideAdminPanel", "logout", "disconnect", "clearInterval", "i0", "factory", "providedIn"], "sourceRoot": "", "sources": ["R:\\chateye\\Frontend\\chateye-angular\\src\\app\\services\\chat.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable, combineLatest } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\nimport { ApiService, Message, Group, User, SecurityInfo, LoginResponse } from './api.service';\r\nimport { SocketService } from './socket.service';\r\nimport { NotificationService } from './notification.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ChatService {\r\n  private userSubject = new BehaviorSubject<string | null>(null);\r\n  private messagesSubject = new BehaviorSubject<Message[]>([]);\r\n  private onlineUsersSubject = new BehaviorSubject<User[]>([]);\r\n  private groupsSubject = new BehaviorSubject<Group[]>([]);\r\n  private currentGroupSubject = new BehaviorSubject<Group | null>(null);\r\n  private replyToSubject = new BehaviorSubject<Message | null>(null);\r\n  private loadingSubject = new BehaviorSubject<boolean>(false);\r\n  private isAdminSubject = new BehaviorSubject<boolean>(false);\r\n  private showAdminPanelSubject = new BehaviorSubject<boolean>(false);\r\n  private securityInfoSubject = new BehaviorSubject<SecurityInfo | null>(null);\r\n\r\n  // Public observables\r\n  public user$ = this.userSubject.asObservable();\r\n  public messages$ = this.messagesSubject.asObservable();\r\n  public onlineUsers$ = this.onlineUsersSubject.asObservable();\r\n  public groups$ = this.groupsSubject.asObservable();\r\n  public currentGroup$ = this.currentGroupSubject.asObservable();\r\n  public replyTo$ = this.replyToSubject.asObservable();\r\n  public loading$ = this.loadingSubject.asObservable();\r\n  public isAdmin$ = this.isAdminSubject.asObservable();\r\n  public showAdminPanel$ = this.showAdminPanelSubject.asObservable();\r\n  public securityInfo$ = this.securityInfoSubject.asObservable();\r\n  public connected$ = this.socketService.isConnected$;\r\n\r\n  // Computed observables\r\n  public isLoggedIn$ = this.user$.pipe(map(user => !!user));\r\n\r\n  private messageRefreshInterval: any;\r\n\r\n  constructor(\r\n    private apiService: ApiService,\r\n    private socketService: SocketService,\r\n    private notificationService: NotificationService\r\n  ) {\r\n    this.setupSocketListeners();\r\n    this.setupMessageRefresh();\r\n  }\r\n\r\n  private setupSocketListeners(): void {\r\n    this.socketService.on('connect', () => {\r\n      console.log('Connected to server');\r\n    });\r\n\r\n    this.socketService.on('disconnect', () => {\r\n      console.log('Disconnected from server');\r\n    });\r\n\r\n    this.socketService.on('userGroups', (userGroups: Group[]) => {\r\n      console.log('Received user groups:', userGroups);\r\n      this.groupsSubject.next(userGroups || []);\r\n      if (userGroups && userGroups.length > 0) {\r\n        this.currentGroupSubject.next(userGroups[0]);\r\n      }\r\n    });\r\n\r\n    this.socketService.on('recentMessages', (messages: Message[]) => {\r\n      console.log('Received recent messages:', messages.length);\r\n      this.messagesSubject.next(messages || []);\r\n    });\r\n\r\n    this.socketService.on('groupJoined', ({ groupId }: { groupId: string }) => {\r\n      console.log('Joined group:', groupId);\r\n      const groups = this.groupsSubject.value;\r\n      const group = groups.find(g => g.id === groupId);\r\n      if (group) {\r\n        this.currentGroupSubject.next(group);\r\n      }\r\n    });\r\n\r\n    this.socketService.on('newMessage', (message: Message) => {\r\n      console.log('New message received:', message);\r\n      const currentMessages = this.messagesSubject.value;\r\n      this.messagesSubject.next([...currentMessages, message]);\r\n      \r\n      // Show notification if not current user and window not focused\r\n      const currentUser = this.userSubject.value;\r\n      if (message.username !== currentUser && document.hidden) {\r\n        this.notificationService.showMessageNotification(message.username, message.text);\r\n      }\r\n    });\r\n\r\n    this.socketService.on('reactionUpdate', ({ messageId, reactions }: { messageId: string; reactions: any[] }) => {\r\n      console.log('Reaction update:', messageId, reactions);\r\n      const currentMessages = this.messagesSubject.value;\r\n      this.messagesSubject.next(\r\n        currentMessages.map(msg => \r\n          msg.id === messageId \r\n            ? { ...msg, reactions }\r\n            : msg\r\n        )\r\n      );\r\n    });\r\n\r\n    this.socketService.on('onlineUsersUpdate', (users: User[]) => {\r\n      console.log('Online users updated:', users);\r\n      this.onlineUsersSubject.next(users || []);\r\n    });\r\n\r\n    this.socketService.on('userJoined', ({ username }: { username: string }) => {\r\n      console.log('User joined:', username);\r\n      // Online users will be updated via onlineUsersUpdate event\r\n    });\r\n\r\n    this.socketService.on('userLeft', ({ username }: { username: string }) => {\r\n      console.log('User left:', username);\r\n      // Online users will be updated via onlineUsersUpdate event\r\n    });\r\n\r\n    this.socketService.on('error', (error: any) => {\r\n      console.error('Socket error:', error);\r\n    });\r\n  }\r\n\r\n  private setupMessageRefresh(): void {\r\n    // Refresh messages every 5 seconds if socket is not connected\r\n    this.messageRefreshInterval = setInterval(() => {\r\n      if (!this.socketService.isConnectedSubject.value) {\r\n        const currentGroup = this.currentGroupSubject.value;\r\n        if (currentGroup) {\r\n          console.log('Refreshing messages via HTTP API');\r\n          this.loadRecentMessages(currentGroup.id);\r\n        }\r\n      }\r\n    }, 5000);\r\n  }\r\n\r\n  async login(username: string, inviteCode: string | null = null): Promise<void> {\r\n    try {\r\n      this.loadingSubject.next(true);\r\n      console.log('Starting login process for:', username);\r\n      \r\n      const userData = await this.apiService.loginUser(username, inviteCode || undefined).toPromise();\r\n      console.log('Login API response:', userData);\r\n      \r\n      // Connect to socket with auth data\r\n      console.log('Connecting to socket...');\r\n      this.socketService.connect(username, inviteCode);\r\n      \r\n      this.userSubject.next(username);\r\n      this.isAdminSubject.next(userData?.isAdmin || false);\r\n      this.securityInfoSubject.next(userData?.securityInfo || null);\r\n      \r\n      console.log('Login completed successfully');\r\n      \r\n    } catch (error) {\r\n      console.error('Login failed:', error);\r\n      throw error;\r\n    } finally {\r\n      this.loadingSubject.next(false);\r\n    }\r\n  }\r\n\r\n  async loadRecentMessages(groupId: string): Promise<void> {\r\n    try {\r\n      const messages = await this.apiService.getMessages(groupId, 50).toPromise();\r\n      this.messagesSubject.next(messages || []);\r\n    } catch (error) {\r\n      console.error('Failed to load messages:', error);\r\n    }\r\n  }\r\n\r\n  joinGroup(groupId: string): void {\r\n    const currentUser = this.userSubject.value;\r\n    if (!groupId || !currentUser) return;\r\n    \r\n    console.log('Joining group:', groupId);\r\n    this.socketService.joinGroup(groupId);\r\n    const groups = this.groupsSubject.value;\r\n    const group = groups.find(g => g.id === groupId);\r\n    if (group) {\r\n      this.currentGroupSubject.next(group);\r\n      // Load recent messages for the group\r\n      this.loadRecentMessages(groupId);\r\n    }\r\n  }\r\n\r\n  sendMessage(text: string, replyToId: string | null = null): void {\r\n    const currentUser = this.userSubject.value;\r\n    const currentGroup = this.currentGroupSubject.value;\r\n    \r\n    if (!text.trim() || !currentUser || !currentGroup) {\r\n      console.error('Cannot send message - missing required data:', { text, currentUser, currentGroup });\r\n      return;\r\n    }\r\n\r\n    console.log('Sending message via socket:', { text, groupId: currentGroup.id, replyToId });\r\n    this.socketService.sendMessage(text, currentGroup.id, replyToId);\r\n    \r\n    // Fallback: If socket is not connected, try HTTP API\r\n    if (!this.socketService.isConnectedSubject.value) {\r\n      console.log('Socket not connected, trying HTTP API fallback');\r\n      this.apiService.sendMessage(text, currentUser, currentGroup.id, replyToId).subscribe({\r\n        next: (response) => {\r\n          console.log('Message sent via HTTP API:', response);\r\n        },\r\n        error: (error) => {\r\n          console.error('Failed to send message via HTTP API:', error);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  replyToMessage(message: Message): void {\r\n    this.replyToSubject.next(message);\r\n  }\r\n\r\n  cancelReply(): void {\r\n    this.replyToSubject.next(null);\r\n  }\r\n\r\n  addReaction(messageId: string, emoji: string): void {\r\n    this.socketService.addReaction(messageId, emoji);\r\n  }\r\n\r\n  removeReaction(messageId: string): void {\r\n    this.socketService.removeReaction(messageId);\r\n  }\r\n\r\n  showAdminPanel(): void {\r\n    this.showAdminPanelSubject.next(true);\r\n  }\r\n\r\n  hideAdminPanel(): void {\r\n    this.showAdminPanelSubject.next(false);\r\n  }\r\n\r\n  logout(): void {\r\n    this.socketService.disconnect();\r\n    if (this.messageRefreshInterval) {\r\n      clearInterval(this.messageRefreshInterval);\r\n    }\r\n    this.userSubject.next(null);\r\n    this.messagesSubject.next([]);\r\n    this.onlineUsersSubject.next([]);\r\n    this.groupsSubject.next([]);\r\n    this.currentGroupSubject.next(null);\r\n    this.replyToSubject.next(null);\r\n    this.loadingSubject.next(false);\r\n    this.isAdminSubject.next(false);\r\n    this.showAdminPanelSubject.next(false);\r\n    this.securityInfoSubject.next(null);\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module"}