{"ast": null, "code": "import { SPAC<PERSON>, BACKSPACE, DELE<PERSON>, TAB, hasMod<PERSON><PERSON><PERSON>, ENT<PERSON> } from '@angular/cdk/keycodes';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, EventEmitter, Optional, Inject, Attribute, ContentChild, Input, Output, Component, ViewEncapsulation, ChangeDetectionStrategy, Self, ContentChildren, NgModule } from '@angular/core';\nimport * as i3 from '@angular/material/core';\nimport { mixinTabIndex, mixinColor, mixinDisableRipple, RippleRenderer, MAT_RIPPLE_GLOBAL_OPTIONS, mixinErrorState, MatCommonModule, ErrorStateMatcher } from '@angular/material/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i1 from '@angular/cdk/platform';\nimport { DOCUMENT } from '@angular/common';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { Subject, merge } from 'rxjs';\nimport { take, takeUntil, startWith } from 'rxjs/operators';\nimport { FocusKeyManager } from '@angular/cdk/a11y';\nimport * as i1$1 from '@angular/cdk/bidi';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport * as i2 from '@angular/forms';\nimport { Validators } from '@angular/forms';\nimport { MatFormFieldControl } from '@angular/material/form-field';\n/** Event object emitted by MatChip when selected or deselected. */\n\nconst _c0 = [\"*\"];\n\nclass MatChipSelectionChange {\n  constructor(\n  /** Reference to the chip that emitted the event. */\n  source,\n  /** Whether the chip that emitted the event is selected. */\n  selected,\n  /** Whether the selection change was a result of a user interaction. */\n  isUserInput = false) {\n    this.source = source;\n    this.selected = selected;\n    this.isUserInput = isUserInput;\n  }\n\n}\n/**\n * Injection token that can be used to reference instances of `MatChipRemove`. It serves as\n * alternative token to the actual `MatChipRemove` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\n\n\nconst MAT_CHIP_REMOVE = /*#__PURE__*/new InjectionToken('MatChipRemove');\n/**\n * Injection token that can be used to reference instances of `MatChipAvatar`. It serves as\n * alternative token to the actual `MatChipAvatar` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\n\nconst MAT_CHIP_AVATAR = /*#__PURE__*/new InjectionToken('MatChipAvatar');\n/**\n * Injection token that can be used to reference instances of `MatChipTrailingIcon`. It serves as\n * alternative token to the actual `MatChipTrailingIcon` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\n\nconst MAT_CHIP_TRAILING_ICON = /*#__PURE__*/new InjectionToken('MatChipTrailingIcon'); // Boilerplate for applying mixins to MatChip.\n\n/** @docs-private */\n\nclass MatChipBase {\n  constructor(_elementRef) {\n    this._elementRef = _elementRef;\n  }\n\n}\n\nconst _MatChipMixinBase = /*#__PURE__*/mixinTabIndex( /*#__PURE__*/mixinColor( /*#__PURE__*/mixinDisableRipple(MatChipBase), 'primary'), -1);\n/**\n * Dummy directive to add CSS class to chip avatar.\n * @docs-private\n */\n\n\nlet MatChipAvatar = /*#__PURE__*/(() => {\n  class MatChipAvatar {}\n\n  MatChipAvatar.ɵfac = function MatChipAvatar_Factory(t) {\n    return new (t || MatChipAvatar)();\n  };\n\n  MatChipAvatar.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatChipAvatar,\n    selectors: [[\"mat-chip-avatar\"], [\"\", \"matChipAvatar\", \"\"]],\n    hostAttrs: [1, \"mat-chip-avatar\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_CHIP_AVATAR,\n      useExisting: MatChipAvatar\n    }])]\n  });\n  return MatChipAvatar;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Dummy directive to add CSS class to chip trailing icon.\n * @docs-private\n */\n\n\nlet MatChipTrailingIcon = /*#__PURE__*/(() => {\n  class MatChipTrailingIcon {}\n\n  MatChipTrailingIcon.ɵfac = function MatChipTrailingIcon_Factory(t) {\n    return new (t || MatChipTrailingIcon)();\n  };\n\n  MatChipTrailingIcon.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatChipTrailingIcon,\n    selectors: [[\"mat-chip-trailing-icon\"], [\"\", \"matChipTrailingIcon\", \"\"]],\n    hostAttrs: [1, \"mat-chip-trailing-icon\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_CHIP_TRAILING_ICON,\n      useExisting: MatChipTrailingIcon\n    }])]\n  });\n  return MatChipTrailingIcon;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Material Design styled chip directive. Used inside the MatChipList component. */\n\n\nlet MatChip = /*#__PURE__*/(() => {\n  class MatChip extends _MatChipMixinBase {\n    constructor(elementRef, _ngZone, platform, globalRippleOptions, _changeDetectorRef, _document, animationMode, tabIndex) {\n      super(elementRef);\n      this._ngZone = _ngZone;\n      this._changeDetectorRef = _changeDetectorRef;\n      /** Whether the chip has focus. */\n\n      this._hasFocus = false;\n      /** Whether the chip list is selectable */\n\n      this.chipListSelectable = true;\n      /** Whether the chip list is in multi-selection mode. */\n\n      this._chipListMultiple = false;\n      /** Whether the chip list as a whole is disabled. */\n\n      this._chipListDisabled = false;\n      /** ARIA role that should be applied to the chip. */\n\n      this.role = 'option';\n      this._selected = false;\n      this._selectable = true;\n      this._disabled = false;\n      this._removable = true;\n      /** Emits when the chip is focused. */\n\n      this._onFocus = new Subject();\n      /** Emits when the chip is blurred. */\n\n      this._onBlur = new Subject();\n      /** Emitted when the chip is selected or deselected. */\n\n      this.selectionChange = new EventEmitter();\n      /** Emitted when the chip is destroyed. */\n\n      this.destroyed = new EventEmitter();\n      /** Emitted when a chip is to be removed. */\n\n      this.removed = new EventEmitter();\n\n      this._addHostClassName(); // Dynamically create the ripple target, append it within the chip, and use it as the\n      // chip's ripple target. Adding the class '.mat-chip-ripple' ensures that it will have\n      // the proper styles.\n\n\n      this._chipRippleTarget = _document.createElement('div');\n\n      this._chipRippleTarget.classList.add('mat-chip-ripple');\n\n      this._elementRef.nativeElement.appendChild(this._chipRippleTarget);\n\n      this._chipRipple = new RippleRenderer(this, _ngZone, this._chipRippleTarget, platform);\n\n      this._chipRipple.setupTriggerEvents(elementRef);\n\n      this.rippleConfig = globalRippleOptions || {};\n      this._animationsDisabled = animationMode === 'NoopAnimations';\n      this.tabIndex = tabIndex != null ? parseInt(tabIndex) || -1 : -1;\n    }\n    /**\n     * Whether ripples are disabled on interaction\n     * @docs-private\n     */\n\n\n    get rippleDisabled() {\n      return this.disabled || this.disableRipple || this._animationsDisabled || !!this.rippleConfig.disabled;\n    }\n    /** Whether the chip is selected. */\n\n\n    get selected() {\n      return this._selected;\n    }\n\n    set selected(value) {\n      const coercedValue = coerceBooleanProperty(value);\n\n      if (coercedValue !== this._selected) {\n        this._selected = coercedValue;\n\n        this._dispatchSelectionChange();\n      }\n    }\n    /** The value of the chip. Defaults to the content inside `<mat-chip>` tags. */\n\n\n    get value() {\n      return this._value !== undefined ? this._value : this._elementRef.nativeElement.textContent;\n    }\n\n    set value(value) {\n      this._value = value;\n    }\n    /**\n     * Whether or not the chip is selectable. When a chip is not selectable,\n     * changes to its selected state are always ignored. By default a chip is\n     * selectable, and it becomes non-selectable if its parent chip list is\n     * not selectable.\n     */\n\n\n    get selectable() {\n      return this._selectable && this.chipListSelectable;\n    }\n\n    set selectable(value) {\n      this._selectable = coerceBooleanProperty(value);\n    }\n    /** Whether the chip is disabled. */\n\n\n    get disabled() {\n      return this._chipListDisabled || this._disabled;\n    }\n\n    set disabled(value) {\n      this._disabled = coerceBooleanProperty(value);\n    }\n    /**\n     * Determines whether or not the chip displays the remove styling and emits (removed) events.\n     */\n\n\n    get removable() {\n      return this._removable;\n    }\n\n    set removable(value) {\n      this._removable = coerceBooleanProperty(value);\n    }\n    /** The ARIA selected applied to the chip. */\n\n\n    get ariaSelected() {\n      // Remove the `aria-selected` when the chip is deselected in single-selection mode, because\n      // it adds noise to NVDA users where \"not selected\" will be read out for each chip.\n      return this.selectable && (this._chipListMultiple || this.selected) ? this.selected.toString() : null;\n    }\n\n    _addHostClassName() {\n      const basicChipAttrName = 'mat-basic-chip';\n      const element = this._elementRef.nativeElement;\n\n      if (element.hasAttribute(basicChipAttrName) || element.tagName.toLowerCase() === basicChipAttrName) {\n        element.classList.add(basicChipAttrName);\n        return;\n      } else {\n        element.classList.add('mat-standard-chip');\n      }\n    }\n\n    ngOnDestroy() {\n      this.destroyed.emit({\n        chip: this\n      });\n\n      this._chipRipple._removeTriggerEvents();\n    }\n    /** Selects the chip. */\n\n\n    select() {\n      if (!this._selected) {\n        this._selected = true;\n\n        this._dispatchSelectionChange();\n\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n    /** Deselects the chip. */\n\n\n    deselect() {\n      if (this._selected) {\n        this._selected = false;\n\n        this._dispatchSelectionChange();\n\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n    /** Select this chip and emit selected event */\n\n\n    selectViaInteraction() {\n      if (!this._selected) {\n        this._selected = true;\n\n        this._dispatchSelectionChange(true);\n\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n    /** Toggles the current selected state of this chip. */\n\n\n    toggleSelected(isUserInput = false) {\n      this._selected = !this.selected;\n\n      this._dispatchSelectionChange(isUserInput);\n\n      this._changeDetectorRef.markForCheck();\n\n      return this.selected;\n    }\n    /** Allows for programmatic focusing of the chip. */\n\n\n    focus() {\n      if (!this._hasFocus) {\n        this._elementRef.nativeElement.focus();\n\n        this._onFocus.next({\n          chip: this\n        });\n      }\n\n      this._hasFocus = true;\n    }\n    /**\n     * Allows for programmatic removal of the chip. Called by the MatChipList when the DELETE or\n     * BACKSPACE keys are pressed.\n     *\n     * Informs any listeners of the removal request. Does not remove the chip from the DOM.\n     */\n\n\n    remove() {\n      if (this.removable) {\n        this.removed.emit({\n          chip: this\n        });\n      }\n    }\n    /** Handles click events on the chip. */\n\n\n    _handleClick(event) {\n      if (this.disabled) {\n        event.preventDefault();\n      }\n    }\n    /** Handle custom key presses. */\n\n\n    _handleKeydown(event) {\n      if (this.disabled) {\n        return;\n      }\n\n      switch (event.keyCode) {\n        case DELETE:\n        case BACKSPACE:\n          // If we are removable, remove the focused chip\n          this.remove(); // Always prevent so page navigation does not occur\n\n          event.preventDefault();\n          break;\n\n        case SPACE:\n          // If we are selectable, toggle the focused chip\n          if (this.selectable) {\n            this.toggleSelected(true);\n          } // Always prevent space from scrolling the page since the list has focus\n\n\n          event.preventDefault();\n          break;\n      }\n    }\n\n    _blur() {\n      // When animations are enabled, Angular may end up removing the chip from the DOM a little\n      // earlier than usual, causing it to be blurred and throwing off the logic in the chip list\n      // that moves focus not the next item. To work around the issue, we defer marking the chip\n      // as not focused until the next time the zone stabilizes.\n      this._ngZone.onStable.pipe(take(1)).subscribe(() => {\n        this._ngZone.run(() => {\n          this._hasFocus = false;\n\n          this._onBlur.next({\n            chip: this\n          });\n        });\n      });\n    }\n\n    _dispatchSelectionChange(isUserInput = false) {\n      this.selectionChange.emit({\n        source: this,\n        isUserInput,\n        selected: this._selected\n      });\n    }\n\n  }\n\n  MatChip.ɵfac = function MatChip_Factory(t) {\n    return new (t || MatChip)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1.Platform), i0.ɵɵdirectiveInject(MAT_RIPPLE_GLOBAL_OPTIONS, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵinjectAttribute('tabindex'));\n  };\n\n  MatChip.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatChip,\n    selectors: [[\"mat-basic-chip\"], [\"\", \"mat-basic-chip\", \"\"], [\"mat-chip\"], [\"\", \"mat-chip\", \"\"]],\n    contentQueries: function MatChip_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_AVATAR, 5);\n        i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_TRAILING_ICON, 5);\n        i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_REMOVE, 5);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.avatar = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.trailingIcon = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.removeIcon = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-chip\", \"mat-focus-indicator\"],\n    hostVars: 15,\n    hostBindings: function MatChip_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function MatChip_click_HostBindingHandler($event) {\n          return ctx._handleClick($event);\n        })(\"keydown\", function MatChip_keydown_HostBindingHandler($event) {\n          return ctx._handleKeydown($event);\n        })(\"focus\", function MatChip_focus_HostBindingHandler() {\n          return ctx.focus();\n        })(\"blur\", function MatChip_blur_HostBindingHandler() {\n          return ctx._blur();\n        });\n      }\n\n      if (rf & 2) {\n        i0.ɵɵattribute(\"tabindex\", ctx.disabled ? null : ctx.tabIndex)(\"role\", ctx.role)(\"disabled\", ctx.disabled || null)(\"aria-disabled\", ctx.disabled.toString())(\"aria-selected\", ctx.ariaSelected);\n        i0.ɵɵclassProp(\"mat-chip-selected\", ctx.selected)(\"mat-chip-with-avatar\", ctx.avatar)(\"mat-chip-with-trailing-icon\", ctx.trailingIcon || ctx.removeIcon)(\"mat-chip-disabled\", ctx.disabled)(\"_mat-animation-noopable\", ctx._animationsDisabled);\n      }\n    },\n    inputs: {\n      color: \"color\",\n      disableRipple: \"disableRipple\",\n      tabIndex: \"tabIndex\",\n      role: \"role\",\n      selected: \"selected\",\n      value: \"value\",\n      selectable: \"selectable\",\n      disabled: \"disabled\",\n      removable: \"removable\"\n    },\n    outputs: {\n      selectionChange: \"selectionChange\",\n      destroyed: \"destroyed\",\n      removed: \"removed\"\n    },\n    exportAs: [\"matChip\"],\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return MatChip;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Applies proper (click) support and adds styling for use with the Material Design \"cancel\" icon\n * available at https://material.io/icons/#ic_cancel.\n *\n * Example:\n *\n *     `<mat-chip>\n *       <mat-icon matChipRemove>cancel</mat-icon>\n *     </mat-chip>`\n *\n * You *may* use a custom icon, but you may need to override the `mat-chip-remove` positioning\n * styles to properly center the icon within the chip.\n */\n\n\nlet MatChipRemove = /*#__PURE__*/(() => {\n  class MatChipRemove {\n    constructor(_parentChip, elementRef) {\n      this._parentChip = _parentChip;\n\n      if (elementRef.nativeElement.nodeName === 'BUTTON') {\n        elementRef.nativeElement.setAttribute('type', 'button');\n      }\n    }\n    /** Calls the parent chip's public `remove()` method if applicable. */\n\n\n    _handleClick(event) {\n      const parentChip = this._parentChip;\n\n      if (parentChip.removable && !parentChip.disabled) {\n        parentChip.remove();\n      } // We need to stop event propagation because otherwise the event will bubble up to the\n      // form field and cause the `onContainerClick` method to be invoked. This method would then\n      // reset the focused chip that has been focused after chip removal. Usually the parent\n      // the parent click listener of the `MatChip` would prevent propagation, but it can happen\n      // that the chip is being removed before the event bubbles up.\n\n\n      event.stopPropagation();\n      event.preventDefault();\n    }\n\n  }\n\n  MatChipRemove.ɵfac = function MatChipRemove_Factory(t) {\n    return new (t || MatChipRemove)(i0.ɵɵdirectiveInject(MatChip), i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n\n  MatChipRemove.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatChipRemove,\n    selectors: [[\"\", \"matChipRemove\", \"\"]],\n    hostAttrs: [1, \"mat-chip-remove\", \"mat-chip-trailing-icon\"],\n    hostBindings: function MatChipRemove_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function MatChipRemove_click_HostBindingHandler($event) {\n          return ctx._handleClick($event);\n        });\n      }\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_CHIP_REMOVE,\n      useExisting: MatChipRemove\n    }])]\n  });\n  return MatChipRemove;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Injection token to be used to override the default options for the chips module. */\n\n\nconst MAT_CHIPS_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('mat-chips-default-options');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to MatChipList.\n\n/** @docs-private */\n\nconst _MatChipListBase = /*#__PURE__*/mixinErrorState(class {\n  constructor(_defaultErrorStateMatcher, _parentForm, _parentFormGroup,\n  /**\n   * Form control bound to the component.\n   * Implemented as part of `MatFormFieldControl`.\n   * @docs-private\n   */\n  ngControl) {\n    this._defaultErrorStateMatcher = _defaultErrorStateMatcher;\n    this._parentForm = _parentForm;\n    this._parentFormGroup = _parentFormGroup;\n    this.ngControl = ngControl;\n    /**\n     * Emits whenever the component state changes and should cause the parent\n     * form-field to update. Implemented as part of `MatFormFieldControl`.\n     * @docs-private\n     */\n\n    this.stateChanges = new Subject();\n  }\n\n}); // Increasing integer for generating unique ids for chip-list components.\n\n\nlet nextUniqueId$1 = 0;\n/** Change event object that is emitted when the chip list value has changed. */\n\nclass MatChipListChange {\n  constructor(\n  /** Chip list that emitted the event. */\n  source,\n  /** Value of the chip list when the event was emitted. */\n  value) {\n    this.source = source;\n    this.value = value;\n  }\n\n}\n/**\n * A material design chips component (named ChipList for its similarity to the List component).\n */\n\n\nlet MatChipList = /*#__PURE__*/(() => {\n  class MatChipList extends _MatChipListBase {\n    constructor(_elementRef, _changeDetectorRef, _dir, _parentForm, _parentFormGroup, _defaultErrorStateMatcher, ngControl) {\n      super(_defaultErrorStateMatcher, _parentForm, _parentFormGroup, ngControl);\n      this._elementRef = _elementRef;\n      this._changeDetectorRef = _changeDetectorRef;\n      this._dir = _dir;\n      /**\n       * Implemented as part of MatFormFieldControl.\n       * @docs-private\n       */\n\n      this.controlType = 'mat-chip-list';\n      /**\n       * When a chip is destroyed, we store the index of the destroyed chip until the chips\n       * query list notifies about the update. This is necessary because we cannot determine an\n       * appropriate chip that should receive focus until the array of chips updated completely.\n       */\n\n      this._lastDestroyedChipIndex = null;\n      /** Subject that emits when the component has been destroyed. */\n\n      this._destroyed = new Subject();\n      /** Uid of the chip list */\n\n      this._uid = `mat-chip-list-${nextUniqueId$1++}`;\n      /** Tab index for the chip list. */\n\n      this._tabIndex = 0;\n      /**\n       * User defined tab index.\n       * When it is not null, use user defined tab index. Otherwise use _tabIndex\n       */\n\n      this._userTabIndex = null;\n      /** Function when touched */\n\n      this._onTouched = () => {};\n      /** Function when changed */\n\n\n      this._onChange = () => {};\n\n      this._multiple = false;\n\n      this._compareWith = (o1, o2) => o1 === o2;\n\n      this._disabled = false;\n      /** Orientation of the chip list. */\n\n      this.ariaOrientation = 'horizontal';\n      this._selectable = true;\n      /** Event emitted when the selected chip list value has been changed by the user. */\n\n      this.change = new EventEmitter();\n      /**\n       * Event that emits whenever the raw value of the chip-list changes. This is here primarily\n       * to facilitate the two-way binding for the `value` input.\n       * @docs-private\n       */\n\n      this.valueChange = new EventEmitter();\n\n      if (this.ngControl) {\n        this.ngControl.valueAccessor = this;\n      }\n    }\n    /** The array of selected chips inside chip list. */\n\n\n    get selected() {\n      return this.multiple ? this._selectionModel?.selected || [] : this._selectionModel?.selected[0];\n    }\n    /** The ARIA role applied to the chip list. */\n\n\n    get role() {\n      if (this._explicitRole) {\n        return this._explicitRole;\n      }\n\n      return this.empty ? null : 'listbox';\n    }\n\n    set role(role) {\n      this._explicitRole = role;\n    }\n    /** Whether the user should be allowed to select multiple chips. */\n\n\n    get multiple() {\n      return this._multiple;\n    }\n\n    set multiple(value) {\n      this._multiple = coerceBooleanProperty(value);\n\n      this._syncChipsState();\n    }\n    /**\n     * A function to compare the option values with the selected values. The first argument\n     * is a value from an option. The second is a value from the selection. A boolean\n     * should be returned.\n     */\n\n\n    get compareWith() {\n      return this._compareWith;\n    }\n\n    set compareWith(fn) {\n      this._compareWith = fn;\n\n      if (this._selectionModel) {\n        // A different comparator means the selection could change.\n        this._initializeSelection();\n      }\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n\n\n    get value() {\n      return this._value;\n    }\n\n    set value(value) {\n      this.writeValue(value);\n      this._value = value;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n\n\n    get id() {\n      return this._chipInput ? this._chipInput.id : this._uid;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n\n\n    get required() {\n      return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n    }\n\n    set required(value) {\n      this._required = coerceBooleanProperty(value);\n      this.stateChanges.next();\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n\n\n    get placeholder() {\n      return this._chipInput ? this._chipInput.placeholder : this._placeholder;\n    }\n\n    set placeholder(value) {\n      this._placeholder = value;\n      this.stateChanges.next();\n    }\n    /** Whether any chips or the matChipInput inside of this chip-list has focus. */\n\n\n    get focused() {\n      return this._chipInput && this._chipInput.focused || this._hasFocusedChip();\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n\n\n    get empty() {\n      return (!this._chipInput || this._chipInput.empty) && (!this.chips || this.chips.length === 0);\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n\n\n    get shouldLabelFloat() {\n      return !this.empty || this.focused;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n\n\n    get disabled() {\n      return this.ngControl ? !!this.ngControl.disabled : this._disabled;\n    }\n\n    set disabled(value) {\n      this._disabled = coerceBooleanProperty(value);\n\n      this._syncChipsState();\n    }\n    /**\n     * Whether or not this chip list is selectable. When a chip list is not selectable,\n     * the selected states for all the chips inside the chip list are always ignored.\n     */\n\n\n    get selectable() {\n      return this._selectable;\n    }\n\n    set selectable(value) {\n      this._selectable = coerceBooleanProperty(value);\n\n      this._syncChipsState();\n    }\n\n    set tabIndex(value) {\n      this._userTabIndex = value;\n      this._tabIndex = value;\n    }\n    /** Combined stream of all of the child chips' selection change events. */\n\n\n    get chipSelectionChanges() {\n      return merge(...this.chips.map(chip => chip.selectionChange));\n    }\n    /** Combined stream of all of the child chips' focus change events. */\n\n\n    get chipFocusChanges() {\n      return merge(...this.chips.map(chip => chip._onFocus));\n    }\n    /** Combined stream of all of the child chips' blur change events. */\n\n\n    get chipBlurChanges() {\n      return merge(...this.chips.map(chip => chip._onBlur));\n    }\n    /** Combined stream of all of the child chips' remove change events. */\n\n\n    get chipRemoveChanges() {\n      return merge(...this.chips.map(chip => chip.destroyed));\n    }\n\n    ngAfterContentInit() {\n      this._keyManager = new FocusKeyManager(this.chips).withWrap().withVerticalOrientation().withHomeAndEnd().withHorizontalOrientation(this._dir ? this._dir.value : 'ltr');\n\n      if (this._dir) {\n        this._dir.change.pipe(takeUntil(this._destroyed)).subscribe(dir => this._keyManager.withHorizontalOrientation(dir));\n      }\n\n      this._keyManager.tabOut.pipe(takeUntil(this._destroyed)).subscribe(() => {\n        this._allowFocusEscape();\n      }); // When the list changes, re-subscribe\n\n\n      this.chips.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n        if (this.disabled || !this.selectable) {\n          // Since this happens after the content has been\n          // checked, we need to defer it to the next tick.\n          Promise.resolve().then(() => {\n            this._syncChipsState();\n          });\n        }\n\n        this._resetChips(); // Reset chips selected/deselected status\n\n\n        this._initializeSelection(); // Check to see if we need to update our tab index\n\n\n        this._updateTabIndex(); // Check to see if we have a destroyed chip and need to refocus\n\n\n        this._updateFocusForDestroyedChips();\n\n        this.stateChanges.next();\n      });\n    }\n\n    ngOnInit() {\n      this._selectionModel = new SelectionModel(this.multiple, undefined, false);\n      this.stateChanges.next();\n    }\n\n    ngDoCheck() {\n      if (this.ngControl) {\n        // We need to re-evaluate this on every change detection cycle, because there are some\n        // error triggers that we can't subscribe to (e.g. parent form submissions). This means\n        // that whatever logic is in here has to be super lean or we risk destroying the performance.\n        this.updateErrorState();\n\n        if (this.ngControl.disabled !== this._disabled) {\n          this.disabled = !!this.ngControl.disabled;\n        }\n      }\n    }\n\n    ngOnDestroy() {\n      this._destroyed.next();\n\n      this._destroyed.complete();\n\n      this.stateChanges.complete();\n\n      this._dropSubscriptions();\n    }\n    /** Associates an HTML input element with this chip list. */\n\n\n    registerInput(inputElement) {\n      this._chipInput = inputElement; // We use this attribute to match the chip list to its input in test harnesses.\n      // Set the attribute directly here to avoid \"changed after checked\" errors.\n\n      this._elementRef.nativeElement.setAttribute('data-mat-chip-input', inputElement.id);\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n\n\n    setDescribedByIds(ids) {\n      if (ids.length) {\n        this._elementRef.nativeElement.setAttribute('aria-describedby', ids.join(' '));\n      } else {\n        this._elementRef.nativeElement.removeAttribute('aria-describedby');\n      }\n    } // Implemented as part of ControlValueAccessor.\n\n\n    writeValue(value) {\n      if (this.chips) {\n        this._setSelectionByValue(value, false);\n      }\n    } // Implemented as part of ControlValueAccessor.\n\n\n    registerOnChange(fn) {\n      this._onChange = fn;\n    } // Implemented as part of ControlValueAccessor.\n\n\n    registerOnTouched(fn) {\n      this._onTouched = fn;\n    } // Implemented as part of ControlValueAccessor.\n\n\n    setDisabledState(isDisabled) {\n      this.disabled = isDisabled;\n      this.stateChanges.next();\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n\n\n    onContainerClick(event) {\n      if (!this._originatesFromChip(event)) {\n        this.focus();\n      }\n    }\n    /**\n     * Focuses the first non-disabled chip in this chip list, or the associated input when there\n     * are no eligible chips.\n     */\n\n\n    focus(options) {\n      if (this.disabled) {\n        return;\n      } // TODO: ARIA says this should focus the first `selected` chip if any are selected.\n      // Focus on first element if there's no chipInput inside chip-list\n\n\n      if (this._chipInput && this._chipInput.focused) {// do nothing\n      } else if (this.chips.length > 0) {\n        this._keyManager.setFirstItemActive();\n\n        this.stateChanges.next();\n      } else {\n        this._focusInput(options);\n\n        this.stateChanges.next();\n      }\n    }\n    /** Attempt to focus an input if we have one. */\n\n\n    _focusInput(options) {\n      if (this._chipInput) {\n        this._chipInput.focus(options);\n      }\n    }\n    /**\n     * Pass events to the keyboard manager. Available here for tests.\n     */\n\n\n    _keydown(event) {\n      const target = event.target;\n\n      if (target && target.classList.contains('mat-chip')) {\n        this._keyManager.onKeydown(event);\n\n        this.stateChanges.next();\n      }\n    }\n    /**\n     * Check the tab index as you should not be allowed to focus an empty list.\n     */\n\n\n    _updateTabIndex() {\n      // If we have 0 chips, we should not allow keyboard focus\n      this._tabIndex = this._userTabIndex || (this.chips.length === 0 ? -1 : 0);\n    }\n    /**\n     * If the amount of chips changed, we need to update the\n     * key manager state and focus the next closest chip.\n     */\n\n\n    _updateFocusForDestroyedChips() {\n      // Move focus to the closest chip. If no other chips remain, focus the chip-list itself.\n      if (this._lastDestroyedChipIndex != null) {\n        if (this.chips.length) {\n          const newChipIndex = Math.min(this._lastDestroyedChipIndex, this.chips.length - 1);\n\n          this._keyManager.setActiveItem(newChipIndex);\n        } else {\n          this.focus();\n        }\n      }\n\n      this._lastDestroyedChipIndex = null;\n    }\n    /**\n     * Utility to ensure all indexes are valid.\n     *\n     * @param index The index to be checked.\n     * @returns True if the index is valid for our list of chips.\n     */\n\n\n    _isValidIndex(index) {\n      return index >= 0 && index < this.chips.length;\n    }\n\n    _setSelectionByValue(value, isUserInput = true) {\n      this._clearSelection();\n\n      this.chips.forEach(chip => chip.deselect());\n\n      if (Array.isArray(value)) {\n        value.forEach(currentValue => this._selectValue(currentValue, isUserInput));\n\n        this._sortValues();\n      } else {\n        const correspondingChip = this._selectValue(value, isUserInput); // Shift focus to the active item. Note that we shouldn't do this in multiple\n        // mode, because we don't know what chip the user interacted with last.\n\n\n        if (correspondingChip) {\n          if (isUserInput) {\n            this._keyManager.setActiveItem(correspondingChip);\n          }\n        }\n      }\n    }\n    /**\n     * Finds and selects the chip based on its value.\n     * @returns Chip that has the corresponding value.\n     */\n\n\n    _selectValue(value, isUserInput = true) {\n      const correspondingChip = this.chips.find(chip => {\n        return chip.value != null && this._compareWith(chip.value, value);\n      });\n\n      if (correspondingChip) {\n        isUserInput ? correspondingChip.selectViaInteraction() : correspondingChip.select();\n\n        this._selectionModel.select(correspondingChip);\n      }\n\n      return correspondingChip;\n    }\n\n    _initializeSelection() {\n      // Defer setting the value in order to avoid the \"Expression\n      // has changed after it was checked\" errors from Angular.\n      Promise.resolve().then(() => {\n        if (this.ngControl || this._value) {\n          this._setSelectionByValue(this.ngControl ? this.ngControl.value : this._value, false);\n\n          this.stateChanges.next();\n        }\n      });\n    }\n    /**\n     * Deselects every chip in the list.\n     * @param skip Chip that should not be deselected.\n     */\n\n\n    _clearSelection(skip) {\n      this._selectionModel.clear();\n\n      this.chips.forEach(chip => {\n        if (chip !== skip) {\n          chip.deselect();\n        }\n      });\n      this.stateChanges.next();\n    }\n    /**\n     * Sorts the model values, ensuring that they keep the same\n     * order that they have in the panel.\n     */\n\n\n    _sortValues() {\n      if (this._multiple) {\n        this._selectionModel.clear();\n\n        this.chips.forEach(chip => {\n          if (chip.selected) {\n            this._selectionModel.select(chip);\n          }\n        });\n        this.stateChanges.next();\n      }\n    }\n    /** Emits change event to set the model value. */\n\n\n    _propagateChanges(fallbackValue) {\n      let valueToEmit = null;\n\n      if (Array.isArray(this.selected)) {\n        valueToEmit = this.selected.map(chip => chip.value);\n      } else {\n        valueToEmit = this.selected ? this.selected.value : fallbackValue;\n      }\n\n      this._value = valueToEmit;\n      this.change.emit(new MatChipListChange(this, valueToEmit));\n      this.valueChange.emit(valueToEmit);\n\n      this._onChange(valueToEmit);\n\n      this._changeDetectorRef.markForCheck();\n    }\n    /** When blurred, mark the field as touched when focus moved outside the chip list. */\n\n\n    _blur() {\n      if (!this._hasFocusedChip()) {\n        this._keyManager.setActiveItem(-1);\n      }\n\n      if (!this.disabled) {\n        if (this._chipInput) {\n          // If there's a chip input, we should check whether the focus moved to chip input.\n          // If the focus is not moved to chip input, mark the field as touched. If the focus moved\n          // to chip input, do nothing.\n          // Timeout is needed to wait for the focus() event trigger on chip input.\n          setTimeout(() => {\n            if (!this.focused) {\n              this._markAsTouched();\n            }\n          });\n        } else {\n          // If there's no chip input, then mark the field as touched.\n          this._markAsTouched();\n        }\n      }\n    }\n    /** Mark the field as touched */\n\n\n    _markAsTouched() {\n      this._onTouched();\n\n      this._changeDetectorRef.markForCheck();\n\n      this.stateChanges.next();\n    }\n    /**\n     * Removes the `tabindex` from the chip list and resets it back afterwards, allowing the\n     * user to tab out of it. This prevents the list from capturing focus and redirecting\n     * it back to the first chip, creating a focus trap, if it user tries to tab away.\n     */\n\n\n    _allowFocusEscape() {\n      if (this._tabIndex !== -1) {\n        this._tabIndex = -1;\n        setTimeout(() => {\n          this._tabIndex = this._userTabIndex || 0;\n\n          this._changeDetectorRef.markForCheck();\n        });\n      }\n    }\n\n    _resetChips() {\n      this._dropSubscriptions();\n\n      this._listenToChipsFocus();\n\n      this._listenToChipsSelection();\n\n      this._listenToChipsRemoved();\n    }\n\n    _dropSubscriptions() {\n      if (this._chipFocusSubscription) {\n        this._chipFocusSubscription.unsubscribe();\n\n        this._chipFocusSubscription = null;\n      }\n\n      if (this._chipBlurSubscription) {\n        this._chipBlurSubscription.unsubscribe();\n\n        this._chipBlurSubscription = null;\n      }\n\n      if (this._chipSelectionSubscription) {\n        this._chipSelectionSubscription.unsubscribe();\n\n        this._chipSelectionSubscription = null;\n      }\n\n      if (this._chipRemoveSubscription) {\n        this._chipRemoveSubscription.unsubscribe();\n\n        this._chipRemoveSubscription = null;\n      }\n    }\n    /** Listens to user-generated selection events on each chip. */\n\n\n    _listenToChipsSelection() {\n      this._chipSelectionSubscription = this.chipSelectionChanges.subscribe(event => {\n        event.source.selected ? this._selectionModel.select(event.source) : this._selectionModel.deselect(event.source); // For single selection chip list, make sure the deselected value is unselected.\n\n        if (!this.multiple) {\n          this.chips.forEach(chip => {\n            if (!this._selectionModel.isSelected(chip) && chip.selected) {\n              chip.deselect();\n            }\n          });\n        }\n\n        if (event.isUserInput) {\n          this._propagateChanges();\n        }\n      });\n    }\n    /** Listens to user-generated selection events on each chip. */\n\n\n    _listenToChipsFocus() {\n      this._chipFocusSubscription = this.chipFocusChanges.subscribe(event => {\n        let chipIndex = this.chips.toArray().indexOf(event.chip);\n\n        if (this._isValidIndex(chipIndex)) {\n          this._keyManager.updateActiveItem(chipIndex);\n        }\n\n        this.stateChanges.next();\n      });\n      this._chipBlurSubscription = this.chipBlurChanges.subscribe(() => {\n        this._blur();\n\n        this.stateChanges.next();\n      });\n    }\n\n    _listenToChipsRemoved() {\n      this._chipRemoveSubscription = this.chipRemoveChanges.subscribe(event => {\n        const chip = event.chip;\n        const chipIndex = this.chips.toArray().indexOf(event.chip); // In case the chip that will be removed is currently focused, we temporarily store\n        // the index in order to be able to determine an appropriate sibling chip that will\n        // receive focus.\n\n        if (this._isValidIndex(chipIndex) && chip._hasFocus) {\n          this._lastDestroyedChipIndex = chipIndex;\n        }\n      });\n    }\n    /** Checks whether an event comes from inside a chip element. */\n\n\n    _originatesFromChip(event) {\n      let currentElement = event.target;\n\n      while (currentElement && currentElement !== this._elementRef.nativeElement) {\n        if (currentElement.classList.contains('mat-chip')) {\n          return true;\n        }\n\n        currentElement = currentElement.parentElement;\n      }\n\n      return false;\n    }\n    /** Checks whether any of the chips is focused. */\n\n\n    _hasFocusedChip() {\n      return this.chips && this.chips.some(chip => chip._hasFocus);\n    }\n    /** Syncs the list's state with the individual chips. */\n\n\n    _syncChipsState() {\n      if (this.chips) {\n        this.chips.forEach(chip => {\n          chip._chipListDisabled = this._disabled;\n          chip._chipListMultiple = this.multiple;\n          chip.chipListSelectable = this._selectable;\n        });\n      }\n    }\n\n  }\n\n  MatChipList.ɵfac = function MatChipList_Factory(t) {\n    return new (t || MatChipList)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.Directionality, 8), i0.ɵɵdirectiveInject(i2.NgForm, 8), i0.ɵɵdirectiveInject(i2.FormGroupDirective, 8), i0.ɵɵdirectiveInject(i3.ErrorStateMatcher), i0.ɵɵdirectiveInject(i2.NgControl, 10));\n  };\n\n  MatChipList.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatChipList,\n    selectors: [[\"mat-chip-list\"]],\n    contentQueries: function MatChipList_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatChip, 5);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.chips = _t);\n      }\n    },\n    hostAttrs: [1, \"mat-chip-list\"],\n    hostVars: 14,\n    hostBindings: function MatChipList_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"focus\", function MatChipList_focus_HostBindingHandler() {\n          return ctx.focus();\n        })(\"blur\", function MatChipList_blur_HostBindingHandler() {\n          return ctx._blur();\n        })(\"keydown\", function MatChipList_keydown_HostBindingHandler($event) {\n          return ctx._keydown($event);\n        });\n      }\n\n      if (rf & 2) {\n        i0.ɵɵhostProperty(\"id\", ctx._uid);\n        i0.ɵɵattribute(\"tabindex\", ctx.disabled ? null : ctx._tabIndex)(\"aria-required\", ctx.role ? ctx.required : null)(\"aria-disabled\", ctx.disabled.toString())(\"aria-invalid\", ctx.errorState)(\"aria-multiselectable\", ctx.multiple)(\"role\", ctx.role)(\"aria-orientation\", ctx.ariaOrientation);\n        i0.ɵɵclassProp(\"mat-chip-list-disabled\", ctx.disabled)(\"mat-chip-list-invalid\", ctx.errorState)(\"mat-chip-list-required\", ctx.required);\n      }\n    },\n    inputs: {\n      role: \"role\",\n      userAriaDescribedBy: [\"aria-describedby\", \"userAriaDescribedBy\"],\n      errorStateMatcher: \"errorStateMatcher\",\n      multiple: \"multiple\",\n      compareWith: \"compareWith\",\n      value: \"value\",\n      required: \"required\",\n      placeholder: \"placeholder\",\n      disabled: \"disabled\",\n      ariaOrientation: [\"aria-orientation\", \"ariaOrientation\"],\n      selectable: \"selectable\",\n      tabIndex: \"tabIndex\"\n    },\n    outputs: {\n      change: \"change\",\n      valueChange: \"valueChange\"\n    },\n    exportAs: [\"matChipList\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MatFormFieldControl,\n      useExisting: MatChipList\n    }]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c0,\n    decls: 2,\n    vars: 0,\n    consts: [[1, \"mat-chip-list-wrapper\"]],\n    template: function MatChipList_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelementEnd();\n      }\n    },\n    styles: [\".mat-chip{position:relative;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0);border:none;-webkit-appearance:none;-moz-appearance:none}.mat-chip::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px) * -1)}.mat-standard-chip{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);display:inline-flex;padding:7px 12px;border-radius:16px;align-items:center;cursor:default;min-height:32px;height:1px}.mat-standard-chip._mat-animation-noopable{transition:none !important;animation:none !important}.mat-standard-chip .mat-chip-remove{border:none;-webkit-appearance:none;-moz-appearance:none;padding:0;background:none}.mat-standard-chip .mat-chip-remove.mat-icon,.mat-standard-chip .mat-chip-remove .mat-icon{width:18px;height:18px;font-size:18px}.mat-standard-chip::after{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit;opacity:0;content:\\\"\\\";pointer-events:none;transition:opacity 200ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-standard-chip:hover::after{opacity:.12}.mat-standard-chip:focus{outline:none}.mat-standard-chip:focus::after{opacity:.16}.cdk-high-contrast-active .mat-standard-chip{outline:solid 1px}.cdk-high-contrast-active .mat-standard-chip.mat-chip-selected{outline-width:3px}.mat-standard-chip.mat-chip-disabled::after{opacity:0}.mat-standard-chip.mat-chip-disabled .mat-chip-remove,.mat-standard-chip.mat-chip-disabled .mat-chip-trailing-icon{cursor:default}.mat-standard-chip.mat-chip-with-trailing-icon.mat-chip-with-avatar,.mat-standard-chip.mat-chip-with-avatar{padding-top:0;padding-bottom:0}.mat-standard-chip.mat-chip-with-trailing-icon.mat-chip-with-avatar{padding-right:8px;padding-left:0}[dir=rtl] .mat-standard-chip.mat-chip-with-trailing-icon.mat-chip-with-avatar{padding-left:8px;padding-right:0}.mat-standard-chip.mat-chip-with-trailing-icon{padding-top:7px;padding-bottom:7px;padding-right:8px;padding-left:12px}[dir=rtl] .mat-standard-chip.mat-chip-with-trailing-icon{padding-left:8px;padding-right:12px}.mat-standard-chip.mat-chip-with-avatar{padding-left:0;padding-right:12px}[dir=rtl] .mat-standard-chip.mat-chip-with-avatar{padding-right:0;padding-left:12px}.mat-standard-chip .mat-chip-avatar{width:24px;height:24px;margin-right:8px;margin-left:4px}[dir=rtl] .mat-standard-chip .mat-chip-avatar{margin-left:8px;margin-right:4px}.mat-standard-chip .mat-chip-remove,.mat-standard-chip .mat-chip-trailing-icon{width:18px;height:18px;cursor:pointer}.mat-standard-chip .mat-chip-remove,.mat-standard-chip .mat-chip-trailing-icon{margin-left:8px;margin-right:0}[dir=rtl] .mat-standard-chip .mat-chip-remove,[dir=rtl] .mat-standard-chip .mat-chip-trailing-icon{margin-right:8px;margin-left:0}.mat-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit;overflow:hidden;transform:translateZ(0)}.mat-chip-list-wrapper{display:flex;flex-direction:row;flex-wrap:wrap;align-items:center;margin:-4px}.mat-chip-list-wrapper input.mat-input-element,.mat-chip-list-wrapper .mat-standard-chip{margin:4px}.mat-chip-list-stacked .mat-chip-list-wrapper{flex-direction:column;align-items:flex-start}.mat-chip-list-stacked .mat-chip-list-wrapper .mat-standard-chip{width:100%}.mat-chip-avatar{border-radius:50%;justify-content:center;align-items:center;display:flex;overflow:hidden;object-fit:cover}input.mat-chip-input{width:150px;margin:4px;flex:1 0 150px}\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return MatChipList;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Increasing integer for generating unique ids.\n\n\nlet nextUniqueId = 0;\n/**\n * Directive that adds chip-specific behaviors to an input element inside `<mat-form-field>`.\n * May be placed inside or outside of an `<mat-chip-list>`.\n */\n\nlet MatChipInput = /*#__PURE__*/(() => {\n  class MatChipInput {\n    constructor(_elementRef, _defaultOptions) {\n      this._elementRef = _elementRef;\n      this._defaultOptions = _defaultOptions;\n      /** Whether the control is focused. */\n\n      this.focused = false;\n      this._addOnBlur = false;\n      /**\n       * The list of key codes that will trigger a chipEnd event.\n       *\n       * Defaults to `[ENTER]`.\n       */\n\n      this.separatorKeyCodes = this._defaultOptions.separatorKeyCodes;\n      /** Emitted when a chip is to be added. */\n\n      this.chipEnd = new EventEmitter();\n      /** The input's placeholder text. */\n\n      this.placeholder = '';\n      /** Unique id for the input. */\n\n      this.id = `mat-chip-list-input-${nextUniqueId++}`;\n      this._disabled = false;\n      this.inputElement = this._elementRef.nativeElement;\n    }\n    /** Register input for chip list */\n\n\n    set chipList(value) {\n      if (value) {\n        this._chipList = value;\n\n        this._chipList.registerInput(this);\n      }\n    }\n    /**\n     * Whether or not the chipEnd event will be emitted when the input is blurred.\n     */\n\n\n    get addOnBlur() {\n      return this._addOnBlur;\n    }\n\n    set addOnBlur(value) {\n      this._addOnBlur = coerceBooleanProperty(value);\n    }\n    /** Whether the input is disabled. */\n\n\n    get disabled() {\n      return this._disabled || this._chipList && this._chipList.disabled;\n    }\n\n    set disabled(value) {\n      this._disabled = coerceBooleanProperty(value);\n    }\n    /** Whether the input is empty. */\n\n\n    get empty() {\n      return !this.inputElement.value;\n    }\n\n    ngOnChanges() {\n      this._chipList.stateChanges.next();\n    }\n\n    ngOnDestroy() {\n      this.chipEnd.complete();\n    }\n\n    ngAfterContentInit() {\n      this._focusLastChipOnBackspace = this.empty;\n    }\n    /** Utility method to make host definition/tests more clear. */\n\n\n    _keydown(event) {\n      if (event) {\n        // Allow the user's focus to escape when they're tabbing forward. Note that we don't\n        // want to do this when going backwards, because focus should go back to the first chip.\n        if (event.keyCode === TAB && !hasModifierKey(event, 'shiftKey')) {\n          this._chipList._allowFocusEscape();\n        } // To prevent the user from accidentally deleting chips when pressing BACKSPACE continuously,\n        // We focus the last chip on backspace only after the user has released the backspace button,\n        // and the input is empty (see behaviour in _keyup)\n\n\n        if (event.keyCode === BACKSPACE && this._focusLastChipOnBackspace) {\n          this._chipList._keyManager.setLastItemActive();\n\n          event.preventDefault();\n          return;\n        } else {\n          this._focusLastChipOnBackspace = false;\n        }\n      }\n\n      this._emitChipEnd(event);\n    }\n    /**\n     * Pass events to the keyboard manager. Available here for tests.\n     */\n\n\n    _keyup(event) {\n      // Allow user to move focus to chips next time he presses backspace\n      if (!this._focusLastChipOnBackspace && event.keyCode === BACKSPACE && this.empty) {\n        this._focusLastChipOnBackspace = true;\n        event.preventDefault();\n      }\n    }\n    /** Checks to see if the blur should emit the (chipEnd) event. */\n\n\n    _blur() {\n      if (this.addOnBlur) {\n        this._emitChipEnd();\n      }\n\n      this.focused = false; // Blur the chip list if it is not focused\n\n      if (!this._chipList.focused) {\n        this._chipList._blur();\n      }\n\n      this._chipList.stateChanges.next();\n    }\n\n    _focus() {\n      this.focused = true;\n      this._focusLastChipOnBackspace = this.empty;\n\n      this._chipList.stateChanges.next();\n    }\n    /** Checks to see if the (chipEnd) event needs to be emitted. */\n\n\n    _emitChipEnd(event) {\n      if (!this.inputElement.value && !!event) {\n        this._chipList._keydown(event);\n      }\n\n      if (!event || this._isSeparatorKey(event)) {\n        this.chipEnd.emit({\n          input: this.inputElement,\n          value: this.inputElement.value,\n          chipInput: this\n        });\n        event?.preventDefault();\n      }\n    }\n\n    _onInput() {\n      // Let chip list know whenever the value changes.\n      this._chipList.stateChanges.next();\n    }\n    /** Focuses the input. */\n\n\n    focus(options) {\n      this.inputElement.focus(options);\n    }\n    /** Clears the input */\n\n\n    clear() {\n      this.inputElement.value = '';\n      this._focusLastChipOnBackspace = true;\n    }\n    /** Checks whether a keycode is one of the configured separators. */\n\n\n    _isSeparatorKey(event) {\n      return !hasModifierKey(event) && new Set(this.separatorKeyCodes).has(event.keyCode);\n    }\n\n  }\n\n  MatChipInput.ɵfac = function MatChipInput_Factory(t) {\n    return new (t || MatChipInput)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MAT_CHIPS_DEFAULT_OPTIONS));\n  };\n\n  MatChipInput.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatChipInput,\n    selectors: [[\"input\", \"matChipInputFor\", \"\"]],\n    hostAttrs: [1, \"mat-chip-input\", \"mat-input-element\"],\n    hostVars: 5,\n    hostBindings: function MatChipInput_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"keydown\", function MatChipInput_keydown_HostBindingHandler($event) {\n          return ctx._keydown($event);\n        })(\"keyup\", function MatChipInput_keyup_HostBindingHandler($event) {\n          return ctx._keyup($event);\n        })(\"blur\", function MatChipInput_blur_HostBindingHandler() {\n          return ctx._blur();\n        })(\"focus\", function MatChipInput_focus_HostBindingHandler() {\n          return ctx._focus();\n        })(\"input\", function MatChipInput_input_HostBindingHandler() {\n          return ctx._onInput();\n        });\n      }\n\n      if (rf & 2) {\n        i0.ɵɵhostProperty(\"id\", ctx.id);\n        i0.ɵɵattribute(\"disabled\", ctx.disabled || null)(\"placeholder\", ctx.placeholder || null)(\"aria-invalid\", ctx._chipList && ctx._chipList.ngControl ? ctx._chipList.ngControl.invalid : null)(\"aria-required\", ctx._chipList && ctx._chipList.required || null);\n      }\n    },\n    inputs: {\n      chipList: [\"matChipInputFor\", \"chipList\"],\n      addOnBlur: [\"matChipInputAddOnBlur\", \"addOnBlur\"],\n      separatorKeyCodes: [\"matChipInputSeparatorKeyCodes\", \"separatorKeyCodes\"],\n      placeholder: \"placeholder\",\n      id: \"id\",\n      disabled: \"disabled\"\n    },\n    outputs: {\n      chipEnd: \"matChipInputTokenEnd\"\n    },\n    exportAs: [\"matChipInput\", \"matChipInputFor\"],\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n  return MatChipInput;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst CHIP_DECLARATIONS = [MatChipList, MatChip, MatChipInput, MatChipRemove, MatChipAvatar, MatChipTrailingIcon];\nlet MatChipsModule = /*#__PURE__*/(() => {\n  class MatChipsModule {}\n\n  MatChipsModule.ɵfac = function MatChipsModule_Factory(t) {\n    return new (t || MatChipsModule)();\n  };\n\n  MatChipsModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatChipsModule\n  });\n  MatChipsModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [ErrorStateMatcher, {\n      provide: MAT_CHIPS_DEFAULT_OPTIONS,\n      useValue: {\n        separatorKeyCodes: [ENTER]\n      }\n    }],\n    imports: [MatCommonModule]\n  });\n  return MatChipsModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { MAT_CHIPS_DEFAULT_OPTIONS, MAT_CHIP_AVATAR, MAT_CHIP_REMOVE, MAT_CHIP_TRAILING_ICON, MatChip, MatChipAvatar, MatChipInput, MatChipList, MatChipListChange, MatChipRemove, MatChipSelectionChange, MatChipTrailingIcon, MatChipsModule }; //# sourceMappingURL=chips.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}