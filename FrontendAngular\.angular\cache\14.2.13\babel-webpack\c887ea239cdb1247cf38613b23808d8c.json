{"ast": null, "code": "import { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function retryWhen(notifier) {\n  return operate((source, subscriber) => {\n    let innerSub;\n    let syncResub = false;\n    let errors$;\n\n    const subscribeForRetryWhen = () => {\n      innerSub = source.subscribe(createOperatorSubscriber(subscriber, undefined, undefined, err => {\n        if (!errors$) {\n          errors$ = new Subject();\n          notifier(errors$).subscribe(createOperatorSubscriber(subscriber, () => innerSub ? subscribeForRetryWhen() : syncResub = true));\n        }\n\n        if (errors$) {\n          errors$.next(err);\n        }\n      }));\n\n      if (syncResub) {\n        innerSub.unsubscribe();\n        innerSub = null;\n        syncResub = false;\n        subscribeForRetryWhen();\n      }\n    };\n\n    subscribeForRetryWhen();\n  });\n}", "map": {"version": 3, "names": ["Subject", "operate", "createOperatorSubscriber", "retry<PERSON><PERSON>", "notifier", "source", "subscriber", "innerSub", "syncResub", "errors$", "subscribeForRetryWhen", "subscribe", "undefined", "err", "next", "unsubscribe"], "sources": ["R:/chateye/FrontendAngular/node_modules/rxjs/dist/esm/internal/operators/retryWhen.js"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function retryWhen(notifier) {\n    return operate((source, subscriber) => {\n        let innerSub;\n        let syncResub = false;\n        let errors$;\n        const subscribeForRetryWhen = () => {\n            innerSub = source.subscribe(createOperatorSubscriber(subscriber, undefined, undefined, (err) => {\n                if (!errors$) {\n                    errors$ = new Subject();\n                    notifier(errors$).subscribe(createOperatorSubscriber(subscriber, () => innerSub ? subscribeForRetryWhen() : (syncResub = true)));\n                }\n                if (errors$) {\n                    errors$.next(err);\n                }\n            }));\n            if (syncResub) {\n                innerSub.unsubscribe();\n                innerSub = null;\n                syncResub = false;\n                subscribeForRetryWhen();\n            }\n        };\n        subscribeForRetryWhen();\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,YAAxB;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,OAAO,SAASC,SAAT,CAAmBC,QAAnB,EAA6B;EAChC,OAAOH,OAAO,CAAC,CAACI,MAAD,EAASC,UAAT,KAAwB;IACnC,IAAIC,QAAJ;IACA,IAAIC,SAAS,GAAG,KAAhB;IACA,IAAIC,OAAJ;;IACA,MAAMC,qBAAqB,GAAG,MAAM;MAChCH,QAAQ,GAAGF,MAAM,CAACM,SAAP,CAAiBT,wBAAwB,CAACI,UAAD,EAAaM,SAAb,EAAwBA,SAAxB,EAAoCC,GAAD,IAAS;QAC5F,IAAI,CAACJ,OAAL,EAAc;UACVA,OAAO,GAAG,IAAIT,OAAJ,EAAV;UACAI,QAAQ,CAACK,OAAD,CAAR,CAAkBE,SAAlB,CAA4BT,wBAAwB,CAACI,UAAD,EAAa,MAAMC,QAAQ,GAAGG,qBAAqB,EAAxB,GAA8BF,SAAS,GAAG,IAArE,CAApD;QACH;;QACD,IAAIC,OAAJ,EAAa;UACTA,OAAO,CAACK,IAAR,CAAaD,GAAb;QACH;MACJ,CARmD,CAAzC,CAAX;;MASA,IAAIL,SAAJ,EAAe;QACXD,QAAQ,CAACQ,WAAT;QACAR,QAAQ,GAAG,IAAX;QACAC,SAAS,GAAG,KAAZ;QACAE,qBAAqB;MACxB;IACJ,CAhBD;;IAiBAA,qBAAqB;EACxB,CAtBa,CAAd;AAuBH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}