{"ast": null, "code": "import _asyncToGenerator from \"R:/chateye/Frontend/chateye-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nexport class NotificationService {\n  constructor() {\n    this.permission = 'default';\n    this.requestPermission();\n  }\n\n  requestPermission() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      if ('Notification' in window) {\n        _this.permission = yield Notification.requestPermission();\n      }\n    })();\n  }\n\n  showNotification(title, options = {}) {\n    if (this.permission === 'granted' && document.hidden) {\n      const notification = new Notification(title, {\n        icon: '/favicon.ico',\n        badge: '/favicon.ico',\n        tag: 'chateye-message',\n        ...options\n      }); // Auto close after 5 seconds\n\n      setTimeout(() => notification.close(), 5000); // Focus window when clicked\n\n      notification.onclick = () => {\n        window.focus();\n        notification.close();\n      };\n\n      return notification;\n    }\n\n    return null;\n  }\n\n  showMessageNotification(username, message) {\n    return this.showNotification(`New message from ${username}`, {\n      body: message.length > 50 ? message.substring(0, 50) + '...' : message,\n      tag: 'chateye-message'\n    });\n  }\n\n  static {\n    this.ɵfac = function NotificationService_Factory(t) {\n      return new (t || NotificationService)();\n    };\n\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: NotificationService,\n      factory: NotificationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "mappings": ";;AAKA,OAAM,MAAOA,mBAAP,CAA0B;EAG9BC;IAFQ,kBAAqC,SAArC;IAGN,KAAKC,iBAAL;EACD;;EAEKA,iBAAiB;IAAA;;IAAA;MACrB,IAAI,kBAAkBC,MAAtB,EAA8B;QAC5B,KAAI,CAACC,UAAL,SAAwBC,YAAY,CAACH,iBAAb,EAAxB;MACD;IAHoB;EAItB;;EAEDI,gBAAgB,CAACC,KAAD,EAAgBC,UAA+B,EAA/C,EAAiD;IAC/D,IAAI,KAAKJ,UAAL,KAAoB,SAApB,IAAiCK,QAAQ,CAACC,MAA9C,EAAsD;MACpD,MAAMC,YAAY,GAAG,IAAIN,YAAJ,CAAiBE,KAAjB,EAAwB;QAC3CK,IAAI,EAAE,cADqC;QAE3CC,KAAK,EAAE,cAFoC;QAG3CC,GAAG,EAAE,iBAHsC;QAI3C,GAAGN;MAJwC,CAAxB,CAArB,CADoD,CAQpD;;MACAO,UAAU,CAAC,MAAMJ,YAAY,CAACK,KAAb,EAAP,EAA6B,IAA7B,CAAV,CAToD,CAWpD;;MACAL,YAAY,CAACM,OAAb,GAAuB,MAAK;QAC1Bd,MAAM,CAACe,KAAP;QACAP,YAAY,CAACK,KAAb;MACD,CAHD;;MAKA,OAAOL,YAAP;IACD;;IACD,OAAO,IAAP;EACD;;EAEDQ,uBAAuB,CAACC,QAAD,EAAmBC,OAAnB,EAAkC;IACvD,OAAO,KAAKf,gBAAL,CAAsB,oBAAoBc,QAAQ,EAAlD,EAAsD;MAC3DE,IAAI,EAAED,OAAO,CAACE,MAAR,GAAiB,EAAjB,GAAsBF,OAAO,CAACG,SAAR,CAAkB,CAAlB,EAAqB,EAArB,IAA2B,KAAjD,GAAyDH,OADJ;MAE3DP,GAAG,EAAE;IAFsD,CAAtD,CAAP;EAID;;;;uBAzCUd;IAAmB;;;;;aAAnBA;MAAmByB,SAAnBzB,mBAAmB;MAAA0B,YAFlB;;;AAEkB", "names": ["NotificationService", "constructor", "requestPermission", "window", "permission", "Notification", "showNotification", "title", "options", "document", "hidden", "notification", "icon", "badge", "tag", "setTimeout", "close", "onclick", "focus", "showMessageNotification", "username", "message", "body", "length", "substring", "factory", "providedIn"], "sourceRoot": "", "sources": ["R:\\chateye\\Frontend\\chateye-angular\\src\\app\\services\\notification.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class NotificationService {\r\n  private permission: NotificationPermission = 'default';\r\n\r\n  constructor() {\r\n    this.requestPermission();\r\n  }\r\n\r\n  async requestPermission(): Promise<void> {\r\n    if ('Notification' in window) {\r\n      this.permission = await Notification.requestPermission();\r\n    }\r\n  }\r\n\r\n  showNotification(title: string, options: NotificationOptions = {}): Notification | null {\r\n    if (this.permission === 'granted' && document.hidden) {\r\n      const notification = new Notification(title, {\r\n        icon: '/favicon.ico',\r\n        badge: '/favicon.ico',\r\n        tag: 'chateye-message',\r\n        ...options\r\n      });\r\n\r\n      // Auto close after 5 seconds\r\n      setTimeout(() => notification.close(), 5000);\r\n\r\n      // Focus window when clicked\r\n      notification.onclick = () => {\r\n        window.focus();\r\n        notification.close();\r\n      };\r\n\r\n      return notification;\r\n    }\r\n    return null;\r\n  }\r\n\r\n  showMessageNotification(username: string, message: string): Notification | null {\r\n    return this.showNotification(`New message from ${username}`, {\r\n      body: message.length > 50 ? message.substring(0, 50) + '...' : message,\r\n      tag: 'chateye-message'\r\n    });\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module"}