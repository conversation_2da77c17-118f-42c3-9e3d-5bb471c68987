{"ast": null, "code": "/******/\n(function () {\n  // webpackBootstrap\n\n  /******/\n  \"use strict\";\n  /******/\n\n  var __webpack_modules__ = {\n    /***/\n    \"./client-src/modules/logger/SyncBailHookFake.js\":\n    /*!*******************************************************!*\\\n      !*** ./client-src/modules/logger/SyncBailHookFake.js ***!\n      \\*******************************************************/\n\n    /***/\n    function (module) {\n      /**\n       * Client stub for tapable SyncBailHook\n       */\n      module.exports = function clientTapableSyncBailHook() {\n        return {\n          call: function call() {}\n        };\n      };\n      /***/\n\n    },\n\n    /***/\n    \"./node_modules/webpack/lib/logging/Logger.js\":\n    /*!****************************************************!*\\\n      !*** ./node_modules/webpack/lib/logging/Logger.js ***!\n      \\****************************************************/\n\n    /***/\n    function (__unused_webpack_module, exports) {\n      /*\n      \tMIT License http://www.opensource.org/licenses/mit-license.php\n      \tAuthor Tobias Koppers @sokra\n      */\n      function _toConsumableArray(arr) {\n        return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n      }\n\n      function _nonIterableSpread() {\n        throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n      }\n\n      function _unsupportedIterableToArray(o, minLen) {\n        if (!o) return;\n        if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n        var n = Object.prototype.toString.call(o).slice(8, -1);\n        if (n === \"Object\" && o.constructor) n = o.constructor.name;\n        if (n === \"Map\" || n === \"Set\") return Array.from(o);\n        if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n      }\n\n      function _iterableToArray(iter) {\n        if (typeof (typeof Symbol !== \"undefined\" ? Symbol : function (i) {\n          return i;\n        }) !== \"undefined\" && iter[(typeof Symbol !== \"undefined\" ? Symbol : function (i) {\n          return i;\n        }).iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n      }\n\n      function _arrayWithoutHoles(arr) {\n        if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n      }\n\n      function _arrayLikeToArray(arr, len) {\n        if (len == null || len > arr.length) len = arr.length;\n\n        for (var i = 0, arr2 = new Array(len); i < len; i++) {\n          arr2[i] = arr[i];\n        }\n\n        return arr2;\n      }\n\n      function _classCallCheck(instance, Constructor) {\n        if (!(instance instanceof Constructor)) {\n          throw new TypeError(\"Cannot call a class as a function\");\n        }\n      }\n\n      function _defineProperties(target, props) {\n        for (var i = 0; i < props.length; i++) {\n          var descriptor = props[i];\n          descriptor.enumerable = descriptor.enumerable || false;\n          descriptor.configurable = true;\n          if (\"value\" in descriptor) descriptor.writable = true;\n          Object.defineProperty(target, descriptor.key, descriptor);\n        }\n      }\n\n      function _createClass(Constructor, protoProps, staticProps) {\n        if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n        if (staticProps) _defineProperties(Constructor, staticProps);\n        Object.defineProperty(Constructor, \"prototype\", {\n          writable: false\n        });\n        return Constructor;\n      }\n\n      var LogType = Object.freeze({\n        error:\n        /** @type {\"error\"} */\n        \"error\",\n        // message, c style arguments\n        warn:\n        /** @type {\"warn\"} */\n        \"warn\",\n        // message, c style arguments\n        info:\n        /** @type {\"info\"} */\n        \"info\",\n        // message, c style arguments\n        log:\n        /** @type {\"log\"} */\n        \"log\",\n        // message, c style arguments\n        debug:\n        /** @type {\"debug\"} */\n        \"debug\",\n        // message, c style arguments\n        trace:\n        /** @type {\"trace\"} */\n        \"trace\",\n        // no arguments\n        group:\n        /** @type {\"group\"} */\n        \"group\",\n        // [label]\n        groupCollapsed:\n        /** @type {\"groupCollapsed\"} */\n        \"groupCollapsed\",\n        // [label]\n        groupEnd:\n        /** @type {\"groupEnd\"} */\n        \"groupEnd\",\n        // [label]\n        profile:\n        /** @type {\"profile\"} */\n        \"profile\",\n        // [profileName]\n        profileEnd:\n        /** @type {\"profileEnd\"} */\n        \"profileEnd\",\n        // [profileName]\n        time:\n        /** @type {\"time\"} */\n        \"time\",\n        // name, time as [seconds, nanoseconds]\n        clear:\n        /** @type {\"clear\"} */\n        \"clear\",\n        // no arguments\n        status:\n        /** @type {\"status\"} */\n        \"status\" // message, arguments\n\n      });\n      exports.LogType = LogType;\n      /** @typedef {typeof LogType[keyof typeof LogType]} LogTypeEnum */\n\n      var LOG_SYMBOL = (typeof Symbol !== \"undefined\" ? Symbol : function (i) {\n        return i;\n      })(\"webpack logger raw log method\");\n      var TIMERS_SYMBOL = (typeof Symbol !== \"undefined\" ? Symbol : function (i) {\n        return i;\n      })(\"webpack logger times\");\n      var TIMERS_AGGREGATES_SYMBOL = (typeof Symbol !== \"undefined\" ? Symbol : function (i) {\n        return i;\n      })(\"webpack logger aggregated times\");\n\n      var WebpackLogger = /*#__PURE__*/function () {\n        /**\n         * @param {function(LogTypeEnum, any[]=): void} log log function\n         * @param {function(string | function(): string): WebpackLogger} getChildLogger function to create child logger\n         */\n        function WebpackLogger(log, getChildLogger) {\n          _classCallCheck(this, WebpackLogger);\n\n          this[LOG_SYMBOL] = log;\n          this.getChildLogger = getChildLogger;\n        }\n\n        _createClass(WebpackLogger, [{\n          key: \"error\",\n          value: function error() {\n            for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n              args[_key] = arguments[_key];\n            }\n\n            this[LOG_SYMBOL](LogType.error, args);\n          }\n        }, {\n          key: \"warn\",\n          value: function warn() {\n            for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n              args[_key2] = arguments[_key2];\n            }\n\n            this[LOG_SYMBOL](LogType.warn, args);\n          }\n        }, {\n          key: \"info\",\n          value: function info() {\n            for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n              args[_key3] = arguments[_key3];\n            }\n\n            this[LOG_SYMBOL](LogType.info, args);\n          }\n        }, {\n          key: \"log\",\n          value: function log() {\n            for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n              args[_key4] = arguments[_key4];\n            }\n\n            this[LOG_SYMBOL](LogType.log, args);\n          }\n        }, {\n          key: \"debug\",\n          value: function debug() {\n            for (var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {\n              args[_key5] = arguments[_key5];\n            }\n\n            this[LOG_SYMBOL](LogType.debug, args);\n          }\n        }, {\n          key: \"assert\",\n          value: function assert(assertion) {\n            if (!assertion) {\n              for (var _len6 = arguments.length, args = new Array(_len6 > 1 ? _len6 - 1 : 0), _key6 = 1; _key6 < _len6; _key6++) {\n                args[_key6 - 1] = arguments[_key6];\n              }\n\n              this[LOG_SYMBOL](LogType.error, args);\n            }\n          }\n        }, {\n          key: \"trace\",\n          value: function trace() {\n            this[LOG_SYMBOL](LogType.trace, [\"Trace\"]);\n          }\n        }, {\n          key: \"clear\",\n          value: function clear() {\n            this[LOG_SYMBOL](LogType.clear);\n          }\n        }, {\n          key: \"status\",\n          value: function status() {\n            for (var _len7 = arguments.length, args = new Array(_len7), _key7 = 0; _key7 < _len7; _key7++) {\n              args[_key7] = arguments[_key7];\n            }\n\n            this[LOG_SYMBOL](LogType.status, args);\n          }\n        }, {\n          key: \"group\",\n          value: function group() {\n            for (var _len8 = arguments.length, args = new Array(_len8), _key8 = 0; _key8 < _len8; _key8++) {\n              args[_key8] = arguments[_key8];\n            }\n\n            this[LOG_SYMBOL](LogType.group, args);\n          }\n        }, {\n          key: \"groupCollapsed\",\n          value: function groupCollapsed() {\n            for (var _len9 = arguments.length, args = new Array(_len9), _key9 = 0; _key9 < _len9; _key9++) {\n              args[_key9] = arguments[_key9];\n            }\n\n            this[LOG_SYMBOL](LogType.groupCollapsed, args);\n          }\n        }, {\n          key: \"groupEnd\",\n          value: function groupEnd() {\n            for (var _len10 = arguments.length, args = new Array(_len10), _key10 = 0; _key10 < _len10; _key10++) {\n              args[_key10] = arguments[_key10];\n            }\n\n            this[LOG_SYMBOL](LogType.groupEnd, args);\n          }\n        }, {\n          key: \"profile\",\n          value: function profile(label) {\n            this[LOG_SYMBOL](LogType.profile, [label]);\n          }\n        }, {\n          key: \"profileEnd\",\n          value: function profileEnd(label) {\n            this[LOG_SYMBOL](LogType.profileEnd, [label]);\n          }\n        }, {\n          key: \"time\",\n          value: function time(label) {\n            this[TIMERS_SYMBOL] = this[TIMERS_SYMBOL] || new Map();\n            this[TIMERS_SYMBOL].set(label, process.hrtime());\n          }\n        }, {\n          key: \"timeLog\",\n          value: function timeLog(label) {\n            var prev = this[TIMERS_SYMBOL] && this[TIMERS_SYMBOL].get(label);\n\n            if (!prev) {\n              throw new Error(\"No such label '\".concat(label, \"' for WebpackLogger.timeLog()\"));\n            }\n\n            var time = process.hrtime(prev);\n            this[LOG_SYMBOL](LogType.time, [label].concat(_toConsumableArray(time)));\n          }\n        }, {\n          key: \"timeEnd\",\n          value: function timeEnd(label) {\n            var prev = this[TIMERS_SYMBOL] && this[TIMERS_SYMBOL].get(label);\n\n            if (!prev) {\n              throw new Error(\"No such label '\".concat(label, \"' for WebpackLogger.timeEnd()\"));\n            }\n\n            var time = process.hrtime(prev);\n            this[TIMERS_SYMBOL].delete(label);\n            this[LOG_SYMBOL](LogType.time, [label].concat(_toConsumableArray(time)));\n          }\n        }, {\n          key: \"timeAggregate\",\n          value: function timeAggregate(label) {\n            var prev = this[TIMERS_SYMBOL] && this[TIMERS_SYMBOL].get(label);\n\n            if (!prev) {\n              throw new Error(\"No such label '\".concat(label, \"' for WebpackLogger.timeAggregate()\"));\n            }\n\n            var time = process.hrtime(prev);\n            this[TIMERS_SYMBOL].delete(label);\n            this[TIMERS_AGGREGATES_SYMBOL] = this[TIMERS_AGGREGATES_SYMBOL] || new Map();\n            var current = this[TIMERS_AGGREGATES_SYMBOL].get(label);\n\n            if (current !== undefined) {\n              if (time[1] + current[1] > 1e9) {\n                time[0] += current[0] + 1;\n                time[1] = time[1] - 1e9 + current[1];\n              } else {\n                time[0] += current[0];\n                time[1] += current[1];\n              }\n            }\n\n            this[TIMERS_AGGREGATES_SYMBOL].set(label, time);\n          }\n        }, {\n          key: \"timeAggregateEnd\",\n          value: function timeAggregateEnd(label) {\n            if (this[TIMERS_AGGREGATES_SYMBOL] === undefined) return;\n            var time = this[TIMERS_AGGREGATES_SYMBOL].get(label);\n            if (time === undefined) return;\n            this[TIMERS_AGGREGATES_SYMBOL].delete(label);\n            this[LOG_SYMBOL](LogType.time, [label].concat(_toConsumableArray(time)));\n          }\n        }]);\n\n        return WebpackLogger;\n      }();\n\n      exports.Logger = WebpackLogger;\n      /***/\n    },\n\n    /***/\n    \"./node_modules/webpack/lib/logging/createConsoleLogger.js\":\n    /*!*****************************************************************!*\\\n      !*** ./node_modules/webpack/lib/logging/createConsoleLogger.js ***!\n      \\*****************************************************************/\n\n    /***/\n    function (module, __unused_webpack_exports, __webpack_require__) {\n      /*\n      \tMIT License http://www.opensource.org/licenses/mit-license.php\n      \tAuthor Tobias Koppers @sokra\n      */\n      function _toConsumableArray(arr) {\n        return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n      }\n\n      function _nonIterableSpread() {\n        throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n      }\n\n      function _unsupportedIterableToArray(o, minLen) {\n        if (!o) return;\n        if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n        var n = Object.prototype.toString.call(o).slice(8, -1);\n        if (n === \"Object\" && o.constructor) n = o.constructor.name;\n        if (n === \"Map\" || n === \"Set\") return Array.from(o);\n        if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n      }\n\n      function _iterableToArray(iter) {\n        if (typeof (typeof Symbol !== \"undefined\" ? Symbol : function (i) {\n          return i;\n        }) !== \"undefined\" && iter[(typeof Symbol !== \"undefined\" ? Symbol : function (i) {\n          return i;\n        }).iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n      }\n\n      function _arrayWithoutHoles(arr) {\n        if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n      }\n\n      function _arrayLikeToArray(arr, len) {\n        if (len == null || len > arr.length) len = arr.length;\n\n        for (var i = 0, arr2 = new Array(len); i < len; i++) {\n          arr2[i] = arr[i];\n        }\n\n        return arr2;\n      }\n\n      var _require = __webpack_require__(\n      /*! ./Logger */\n      \"./node_modules/webpack/lib/logging/Logger.js\"),\n          LogType = _require.LogType;\n      /** @typedef {import(\"../../declarations/WebpackOptions\").FilterItemTypes} FilterItemTypes */\n\n      /** @typedef {import(\"../../declarations/WebpackOptions\").FilterTypes} FilterTypes */\n\n      /** @typedef {import(\"./Logger\").LogTypeEnum} LogTypeEnum */\n\n      /** @typedef {function(string): boolean} FilterFunction */\n\n      /**\n       * @typedef {Object} LoggerConsole\n       * @property {function(): void} clear\n       * @property {function(): void} trace\n       * @property {(...args: any[]) => void} info\n       * @property {(...args: any[]) => void} log\n       * @property {(...args: any[]) => void} warn\n       * @property {(...args: any[]) => void} error\n       * @property {(...args: any[]) => void=} debug\n       * @property {(...args: any[]) => void=} group\n       * @property {(...args: any[]) => void=} groupCollapsed\n       * @property {(...args: any[]) => void=} groupEnd\n       * @property {(...args: any[]) => void=} status\n       * @property {(...args: any[]) => void=} profile\n       * @property {(...args: any[]) => void=} profileEnd\n       * @property {(...args: any[]) => void=} logTime\n       */\n\n      /**\n       * @typedef {Object} LoggerOptions\n       * @property {false|true|\"none\"|\"error\"|\"warn\"|\"info\"|\"log\"|\"verbose\"} level loglevel\n       * @property {FilterTypes|boolean} debug filter for debug logging\n       * @property {LoggerConsole} console the console to log to\n       */\n\n      /**\n       * @param {FilterItemTypes} item an input item\n       * @returns {FilterFunction} filter function\n       */\n\n\n      var filterToFunction = function filterToFunction(item) {\n        if (typeof item === \"string\") {\n          var regExp = new RegExp(\"[\\\\\\\\/]\".concat(item.replace( // eslint-disable-next-line no-useless-escape\n          /[-[\\]{}()*+?.\\\\^$|]/g, \"\\\\$&\"), \"([\\\\\\\\/]|$|!|\\\\?)\"));\n          return function (ident) {\n            return regExp.test(ident);\n          };\n        }\n\n        if (item && typeof item === \"object\" && typeof item.test === \"function\") {\n          return function (ident) {\n            return item.test(ident);\n          };\n        }\n\n        if (typeof item === \"function\") {\n          return item;\n        }\n\n        if (typeof item === \"boolean\") {\n          return function () {\n            return item;\n          };\n        }\n      };\n      /**\n       * @enum {number}\n       */\n\n\n      var LogLevel = {\n        none: 6,\n        false: 6,\n        error: 5,\n        warn: 4,\n        info: 3,\n        log: 2,\n        true: 2,\n        verbose: 1\n      };\n      /**\n       * @param {LoggerOptions} options options object\n       * @returns {function(string, LogTypeEnum, any[]): void} logging function\n       */\n\n      module.exports = function (_ref) {\n        var _ref$level = _ref.level,\n            level = _ref$level === void 0 ? \"info\" : _ref$level,\n            _ref$debug = _ref.debug,\n            debug = _ref$debug === void 0 ? false : _ref$debug,\n            console = _ref.console;\n        var debugFilters = typeof debug === \"boolean\" ? [function () {\n          return debug;\n        }] :\n        /** @type {FilterItemTypes[]} */\n        [].concat(debug).map(filterToFunction);\n        /** @type {number} */\n\n        var loglevel = LogLevel[\"\".concat(level)] || 0;\n        /**\n         * @param {string} name name of the logger\n         * @param {LogTypeEnum} type type of the log entry\n         * @param {any[]} args arguments of the log entry\n         * @returns {void}\n         */\n\n        var logger = function logger(name, type, args) {\n          var labeledArgs = function labeledArgs() {\n            if (Array.isArray(args)) {\n              if (args.length > 0 && typeof args[0] === \"string\") {\n                return [\"[\".concat(name, \"] \").concat(args[0])].concat(_toConsumableArray(args.slice(1)));\n              } else {\n                return [\"[\".concat(name, \"]\")].concat(_toConsumableArray(args));\n              }\n            } else {\n              return [];\n            }\n          };\n\n          var debug = debugFilters.some(function (f) {\n            return f(name);\n          });\n\n          switch (type) {\n            case LogType.debug:\n              if (!debug) return; // eslint-disable-next-line node/no-unsupported-features/node-builtins\n\n              if (typeof console.debug === \"function\") {\n                // eslint-disable-next-line node/no-unsupported-features/node-builtins\n                console.debug.apply(console, _toConsumableArray(labeledArgs()));\n              } else {\n                console.log.apply(console, _toConsumableArray(labeledArgs()));\n              }\n\n              break;\n\n            case LogType.log:\n              if (!debug && loglevel > LogLevel.log) return;\n              console.log.apply(console, _toConsumableArray(labeledArgs()));\n              break;\n\n            case LogType.info:\n              if (!debug && loglevel > LogLevel.info) return;\n              console.info.apply(console, _toConsumableArray(labeledArgs()));\n              break;\n\n            case LogType.warn:\n              if (!debug && loglevel > LogLevel.warn) return;\n              console.warn.apply(console, _toConsumableArray(labeledArgs()));\n              break;\n\n            case LogType.error:\n              if (!debug && loglevel > LogLevel.error) return;\n              console.error.apply(console, _toConsumableArray(labeledArgs()));\n              break;\n\n            case LogType.trace:\n              if (!debug) return;\n              console.trace();\n              break;\n\n            case LogType.groupCollapsed:\n              if (!debug && loglevel > LogLevel.log) return;\n\n              if (!debug && loglevel > LogLevel.verbose) {\n                // eslint-disable-next-line node/no-unsupported-features/node-builtins\n                if (typeof console.groupCollapsed === \"function\") {\n                  // eslint-disable-next-line node/no-unsupported-features/node-builtins\n                  console.groupCollapsed.apply(console, _toConsumableArray(labeledArgs()));\n                } else {\n                  console.log.apply(console, _toConsumableArray(labeledArgs()));\n                }\n\n                break;\n              }\n\n            // falls through\n\n            case LogType.group:\n              if (!debug && loglevel > LogLevel.log) return; // eslint-disable-next-line node/no-unsupported-features/node-builtins\n\n              if (typeof console.group === \"function\") {\n                // eslint-disable-next-line node/no-unsupported-features/node-builtins\n                console.group.apply(console, _toConsumableArray(labeledArgs()));\n              } else {\n                console.log.apply(console, _toConsumableArray(labeledArgs()));\n              }\n\n              break;\n\n            case LogType.groupEnd:\n              if (!debug && loglevel > LogLevel.log) return; // eslint-disable-next-line node/no-unsupported-features/node-builtins\n\n              if (typeof console.groupEnd === \"function\") {\n                // eslint-disable-next-line node/no-unsupported-features/node-builtins\n                console.groupEnd();\n              }\n\n              break;\n\n            case LogType.time:\n              {\n                if (!debug && loglevel > LogLevel.log) return;\n                var ms = args[1] * 1000 + args[2] / 1000000;\n                var msg = \"[\".concat(name, \"] \").concat(args[0], \": \").concat(ms, \" ms\");\n\n                if (typeof console.logTime === \"function\") {\n                  console.logTime(msg);\n                } else {\n                  console.log(msg);\n                }\n\n                break;\n              }\n\n            case LogType.profile:\n              // eslint-disable-next-line node/no-unsupported-features/node-builtins\n              if (typeof console.profile === \"function\") {\n                // eslint-disable-next-line node/no-unsupported-features/node-builtins\n                console.profile.apply(console, _toConsumableArray(labeledArgs()));\n              }\n\n              break;\n\n            case LogType.profileEnd:\n              // eslint-disable-next-line node/no-unsupported-features/node-builtins\n              if (typeof console.profileEnd === \"function\") {\n                // eslint-disable-next-line node/no-unsupported-features/node-builtins\n                console.profileEnd.apply(console, _toConsumableArray(labeledArgs()));\n              }\n\n              break;\n\n            case LogType.clear:\n              if (!debug && loglevel > LogLevel.log) return; // eslint-disable-next-line node/no-unsupported-features/node-builtins\n\n              if (typeof console.clear === \"function\") {\n                // eslint-disable-next-line node/no-unsupported-features/node-builtins\n                console.clear();\n              }\n\n              break;\n\n            case LogType.status:\n              if (!debug && loglevel > LogLevel.info) return;\n\n              if (typeof console.status === \"function\") {\n                if (args.length === 0) {\n                  console.status();\n                } else {\n                  console.status.apply(console, _toConsumableArray(labeledArgs()));\n                }\n              } else {\n                if (args.length !== 0) {\n                  console.info.apply(console, _toConsumableArray(labeledArgs()));\n                }\n              }\n\n              break;\n\n            default:\n              throw new Error(\"Unexpected LogType \".concat(type));\n          }\n        };\n\n        return logger;\n      };\n      /***/\n\n    },\n\n    /***/\n    \"./node_modules/webpack/lib/logging/runtime.js\":\n    /*!*****************************************************!*\\\n      !*** ./node_modules/webpack/lib/logging/runtime.js ***!\n      \\*****************************************************/\n\n    /***/\n    function (__unused_webpack_module, exports, __webpack_require__) {\n      /*\n      \tMIT License http://www.opensource.org/licenses/mit-license.php\n      \tAuthor Tobias Koppers @sokra\n      */\n      function _extends() {\n        _extends = Object.assign ? Object.assign.bind() : function (target) {\n          for (var i = 1; i < arguments.length; i++) {\n            var source = arguments[i];\n\n            for (var key in source) {\n              if (Object.prototype.hasOwnProperty.call(source, key)) {\n                target[key] = source[key];\n              }\n            }\n          }\n\n          return target;\n        };\n        return _extends.apply(this, arguments);\n      }\n\n      var SyncBailHook = __webpack_require__(\n      /*! tapable/lib/SyncBailHook */\n      \"./client-src/modules/logger/SyncBailHookFake.js\");\n\n      var _require = __webpack_require__(\n      /*! ./Logger */\n      \"./node_modules/webpack/lib/logging/Logger.js\"),\n          Logger = _require.Logger;\n\n      var createConsoleLogger = __webpack_require__(\n      /*! ./createConsoleLogger */\n      \"./node_modules/webpack/lib/logging/createConsoleLogger.js\");\n      /** @type {createConsoleLogger.LoggerOptions} */\n\n\n      var currentDefaultLoggerOptions = {\n        level: \"info\",\n        debug: false,\n        console: console\n      };\n      var currentDefaultLogger = createConsoleLogger(currentDefaultLoggerOptions);\n      /**\n       * @param {string} name name of the logger\n       * @returns {Logger} a logger\n       */\n\n      exports.getLogger = function (name) {\n        return new Logger(function (type, args) {\n          if (exports.hooks.log.call(name, type, args) === undefined) {\n            currentDefaultLogger(name, type, args);\n          }\n        }, function (childName) {\n          return exports.getLogger(\"\".concat(name, \"/\").concat(childName));\n        });\n      };\n      /**\n       * @param {createConsoleLogger.LoggerOptions} options new options, merge with old options\n       * @returns {void}\n       */\n\n\n      exports.configureDefaultLogger = function (options) {\n        _extends(currentDefaultLoggerOptions, options);\n\n        currentDefaultLogger = createConsoleLogger(currentDefaultLoggerOptions);\n      };\n\n      exports.hooks = {\n        log: new SyncBailHook([\"origin\", \"type\", \"args\"])\n      };\n      /***/\n    }\n    /******/\n\n  };\n  /************************************************************************/\n\n  /******/\n  // The module cache\n\n  /******/\n\n  var __webpack_module_cache__ = {};\n  /******/\n\n  /******/\n  // The require function\n\n  /******/\n\n  function __webpack_require__(moduleId) {\n    /******/\n    // Check if module is in cache\n\n    /******/\n    var cachedModule = __webpack_module_cache__[moduleId];\n    /******/\n\n    if (cachedModule !== undefined) {\n      /******/\n      return cachedModule.exports;\n      /******/\n    }\n    /******/\n    // Create a new module (and put it into the cache)\n\n    /******/\n\n\n    var module = __webpack_module_cache__[moduleId] = {\n      /******/\n      // no module.id needed\n\n      /******/\n      // no module.loaded needed\n\n      /******/\n      exports: {}\n      /******/\n\n    };\n    /******/\n\n    /******/\n    // Execute the module function\n\n    /******/\n\n    __webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n    /******/\n\n    /******/\n    // Return the exports of the module\n\n    /******/\n\n\n    return module.exports;\n    /******/\n  }\n  /******/\n\n  /************************************************************************/\n\n  /******/\n\n  /* webpack/runtime/define property getters */\n\n  /******/\n\n\n  !function () {\n    /******/\n    // define getter functions for harmony exports\n\n    /******/\n    __webpack_require__.d = function (exports, definition) {\n      /******/\n      for (var key in definition) {\n        /******/\n        if (__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n          /******/\n          Object.defineProperty(exports, key, {\n            enumerable: true,\n            get: definition[key]\n          });\n          /******/\n        }\n        /******/\n\n      }\n      /******/\n\n    };\n    /******/\n\n  }();\n  /******/\n\n  /******/\n\n  /* webpack/runtime/hasOwnProperty shorthand */\n\n  /******/\n\n  !function () {\n    /******/\n    __webpack_require__.o = function (obj, prop) {\n      return Object.prototype.hasOwnProperty.call(obj, prop);\n    };\n    /******/\n\n  }();\n  /******/\n\n  /******/\n\n  /* webpack/runtime/make namespace object */\n\n  /******/\n\n  !function () {\n    /******/\n    // define __esModule on exports\n\n    /******/\n    __webpack_require__.r = function (exports) {\n      /******/\n      if (typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n        /******/\n        Object.defineProperty(exports, Symbol.toStringTag, {\n          value: 'Module'\n        });\n        /******/\n      }\n      /******/\n\n\n      Object.defineProperty(exports, '__esModule', {\n        value: true\n      });\n      /******/\n    };\n    /******/\n\n  }();\n  /******/\n\n  /************************************************************************/\n\n  var __webpack_exports__ = {}; // This entry need to be wrapped in an IIFE because it need to be isolated against other modules in the chunk.\n\n  !function () {\n    /*!********************************************!*\\\n      !*** ./client-src/modules/logger/index.js ***!\n      \\********************************************/\n    __webpack_require__.r(__webpack_exports__);\n    /* harmony export */\n\n\n    __webpack_require__.d(__webpack_exports__, {\n      /* harmony export */\n      \"default\": function () {\n        return (\n          /* reexport default export from named module */\n          webpack_lib_logging_runtime_js__WEBPACK_IMPORTED_MODULE_0__\n        );\n      }\n      /* harmony export */\n\n    });\n    /* harmony import */\n\n\n    var webpack_lib_logging_runtime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(\n    /*! webpack/lib/logging/runtime.js */\n    \"./node_modules/webpack/lib/logging/runtime.js\");\n  }();\n  var __webpack_export_target__ = exports;\n\n  for (var i in __webpack_exports__) __webpack_export_target__[i] = __webpack_exports__[i];\n\n  if (__webpack_exports__.__esModule) Object.defineProperty(__webpack_export_target__, \"__esModule\", {\n    value: true\n  });\n  /******/\n})();", "map": {"version": 3, "names": ["__webpack_modules__", "module", "exports", "clientTapableSyncBailHook", "call", "__unused_webpack_module", "_toConsumableArray", "arr", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "TypeError", "o", "minLen", "_arrayLikeToArray", "n", "Object", "prototype", "toString", "slice", "constructor", "name", "Array", "from", "test", "iter", "Symbol", "i", "iterator", "isArray", "len", "length", "arr2", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "_defineProperties", "target", "props", "descriptor", "enumerable", "configurable", "writable", "defineProperty", "key", "_createClass", "protoProps", "staticProps", "LogType", "freeze", "error", "warn", "info", "log", "debug", "trace", "group", "groupCollapsed", "groupEnd", "profile", "profileEnd", "time", "clear", "status", "LOG_SYMBOL", "TIMERS_SYMBOL", "TIMERS_AGGREGATES_SYMBOL", "WebpackLogger", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "_len", "arguments", "args", "_key", "_len2", "_key2", "_len3", "_key3", "_len4", "_key4", "_len5", "_key5", "assert", "assertion", "_len6", "_key6", "_len7", "_key7", "_len8", "_key8", "_len9", "_key9", "_len10", "_key10", "label", "Map", "set", "process", "hrtime", "timeLog", "prev", "get", "Error", "concat", "timeEnd", "delete", "timeAggregate", "current", "undefined", "timeAggregateEnd", "<PERSON><PERSON>", "__unused_webpack_exports", "__webpack_require__", "_require", "filterToFunction", "item", "regExp", "RegExp", "replace", "ident", "LogLevel", "none", "false", "true", "verbose", "_ref", "_ref$level", "level", "_ref$debug", "console", "debugFilters", "map", "loglevel", "logger", "type", "labeledArgs", "some", "f", "apply", "ms", "msg", "logTime", "_extends", "assign", "bind", "source", "hasOwnProperty", "SyncBailHook", "createConsoleLogger", "currentDefaultLoggerOptions", "current<PERSON>efault<PERSON>og<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "hooks", "<PERSON><PERSON><PERSON>", "configure<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options", "__webpack_module_cache__", "moduleId", "cachedModule", "d", "definition", "obj", "prop", "r", "toStringTag", "__webpack_exports__", "webpack_lib_logging_runtime_js__WEBPACK_IMPORTED_MODULE_0__", "__webpack_export_target__", "__esModule"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/webpack-dev-server/client/modules/logger/index.js"], "sourcesContent": ["/******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ \"./client-src/modules/logger/SyncBailHookFake.js\":\n/*!*******************************************************!*\\\n  !*** ./client-src/modules/logger/SyncBailHookFake.js ***!\n  \\*******************************************************/\n/***/ (function(module) {\n\n\n/**\n * Client stub for tapable SyncBailHook\n */\n\nmodule.exports = function clientTapableSyncBailHook() {\n  return {\n    call: function call() {}\n  };\n};\n\n/***/ }),\n\n/***/ \"./node_modules/webpack/lib/logging/Logger.js\":\n/*!****************************************************!*\\\n  !*** ./node_modules/webpack/lib/logging/Logger.js ***!\n  \\****************************************************/\n/***/ (function(__unused_webpack_module, exports) {\n\n/*\n\tMIT License http://www.opensource.org/licenses/mit-license.php\n\tAuthor Tobias Koppers @sokra\n*/\n\n\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\n\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _iterableToArray(iter) {\n  if (typeof (typeof Symbol !== \"undefined\" ? Symbol : function (i) { return i; }) !== \"undefined\" && iter[(typeof Symbol !== \"undefined\" ? Symbol : function (i) { return i; }).iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\n\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n\n  return arr2;\n}\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\n\nvar LogType = Object.freeze({\n  error:\n  /** @type {\"error\"} */\n  \"error\",\n  // message, c style arguments\n  warn:\n  /** @type {\"warn\"} */\n  \"warn\",\n  // message, c style arguments\n  info:\n  /** @type {\"info\"} */\n  \"info\",\n  // message, c style arguments\n  log:\n  /** @type {\"log\"} */\n  \"log\",\n  // message, c style arguments\n  debug:\n  /** @type {\"debug\"} */\n  \"debug\",\n  // message, c style arguments\n  trace:\n  /** @type {\"trace\"} */\n  \"trace\",\n  // no arguments\n  group:\n  /** @type {\"group\"} */\n  \"group\",\n  // [label]\n  groupCollapsed:\n  /** @type {\"groupCollapsed\"} */\n  \"groupCollapsed\",\n  // [label]\n  groupEnd:\n  /** @type {\"groupEnd\"} */\n  \"groupEnd\",\n  // [label]\n  profile:\n  /** @type {\"profile\"} */\n  \"profile\",\n  // [profileName]\n  profileEnd:\n  /** @type {\"profileEnd\"} */\n  \"profileEnd\",\n  // [profileName]\n  time:\n  /** @type {\"time\"} */\n  \"time\",\n  // name, time as [seconds, nanoseconds]\n  clear:\n  /** @type {\"clear\"} */\n  \"clear\",\n  // no arguments\n  status:\n  /** @type {\"status\"} */\n  \"status\" // message, arguments\n\n});\nexports.LogType = LogType;\n/** @typedef {typeof LogType[keyof typeof LogType]} LogTypeEnum */\n\nvar LOG_SYMBOL = (typeof Symbol !== \"undefined\" ? Symbol : function (i) { return i; })(\"webpack logger raw log method\");\nvar TIMERS_SYMBOL = (typeof Symbol !== \"undefined\" ? Symbol : function (i) { return i; })(\"webpack logger times\");\nvar TIMERS_AGGREGATES_SYMBOL = (typeof Symbol !== \"undefined\" ? Symbol : function (i) { return i; })(\"webpack logger aggregated times\");\n\nvar WebpackLogger = /*#__PURE__*/function () {\n  /**\n   * @param {function(LogTypeEnum, any[]=): void} log log function\n   * @param {function(string | function(): string): WebpackLogger} getChildLogger function to create child logger\n   */\n  function WebpackLogger(log, getChildLogger) {\n    _classCallCheck(this, WebpackLogger);\n\n    this[LOG_SYMBOL] = log;\n    this.getChildLogger = getChildLogger;\n  }\n\n  _createClass(WebpackLogger, [{\n    key: \"error\",\n    value: function error() {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n\n      this[LOG_SYMBOL](LogType.error, args);\n    }\n  }, {\n    key: \"warn\",\n    value: function warn() {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n\n      this[LOG_SYMBOL](LogType.warn, args);\n    }\n  }, {\n    key: \"info\",\n    value: function info() {\n      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        args[_key3] = arguments[_key3];\n      }\n\n      this[LOG_SYMBOL](LogType.info, args);\n    }\n  }, {\n    key: \"log\",\n    value: function log() {\n      for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n        args[_key4] = arguments[_key4];\n      }\n\n      this[LOG_SYMBOL](LogType.log, args);\n    }\n  }, {\n    key: \"debug\",\n    value: function debug() {\n      for (var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {\n        args[_key5] = arguments[_key5];\n      }\n\n      this[LOG_SYMBOL](LogType.debug, args);\n    }\n  }, {\n    key: \"assert\",\n    value: function assert(assertion) {\n      if (!assertion) {\n        for (var _len6 = arguments.length, args = new Array(_len6 > 1 ? _len6 - 1 : 0), _key6 = 1; _key6 < _len6; _key6++) {\n          args[_key6 - 1] = arguments[_key6];\n        }\n\n        this[LOG_SYMBOL](LogType.error, args);\n      }\n    }\n  }, {\n    key: \"trace\",\n    value: function trace() {\n      this[LOG_SYMBOL](LogType.trace, [\"Trace\"]);\n    }\n  }, {\n    key: \"clear\",\n    value: function clear() {\n      this[LOG_SYMBOL](LogType.clear);\n    }\n  }, {\n    key: \"status\",\n    value: function status() {\n      for (var _len7 = arguments.length, args = new Array(_len7), _key7 = 0; _key7 < _len7; _key7++) {\n        args[_key7] = arguments[_key7];\n      }\n\n      this[LOG_SYMBOL](LogType.status, args);\n    }\n  }, {\n    key: \"group\",\n    value: function group() {\n      for (var _len8 = arguments.length, args = new Array(_len8), _key8 = 0; _key8 < _len8; _key8++) {\n        args[_key8] = arguments[_key8];\n      }\n\n      this[LOG_SYMBOL](LogType.group, args);\n    }\n  }, {\n    key: \"groupCollapsed\",\n    value: function groupCollapsed() {\n      for (var _len9 = arguments.length, args = new Array(_len9), _key9 = 0; _key9 < _len9; _key9++) {\n        args[_key9] = arguments[_key9];\n      }\n\n      this[LOG_SYMBOL](LogType.groupCollapsed, args);\n    }\n  }, {\n    key: \"groupEnd\",\n    value: function groupEnd() {\n      for (var _len10 = arguments.length, args = new Array(_len10), _key10 = 0; _key10 < _len10; _key10++) {\n        args[_key10] = arguments[_key10];\n      }\n\n      this[LOG_SYMBOL](LogType.groupEnd, args);\n    }\n  }, {\n    key: \"profile\",\n    value: function profile(label) {\n      this[LOG_SYMBOL](LogType.profile, [label]);\n    }\n  }, {\n    key: \"profileEnd\",\n    value: function profileEnd(label) {\n      this[LOG_SYMBOL](LogType.profileEnd, [label]);\n    }\n  }, {\n    key: \"time\",\n    value: function time(label) {\n      this[TIMERS_SYMBOL] = this[TIMERS_SYMBOL] || new Map();\n      this[TIMERS_SYMBOL].set(label, process.hrtime());\n    }\n  }, {\n    key: \"timeLog\",\n    value: function timeLog(label) {\n      var prev = this[TIMERS_SYMBOL] && this[TIMERS_SYMBOL].get(label);\n\n      if (!prev) {\n        throw new Error(\"No such label '\".concat(label, \"' for WebpackLogger.timeLog()\"));\n      }\n\n      var time = process.hrtime(prev);\n      this[LOG_SYMBOL](LogType.time, [label].concat(_toConsumableArray(time)));\n    }\n  }, {\n    key: \"timeEnd\",\n    value: function timeEnd(label) {\n      var prev = this[TIMERS_SYMBOL] && this[TIMERS_SYMBOL].get(label);\n\n      if (!prev) {\n        throw new Error(\"No such label '\".concat(label, \"' for WebpackLogger.timeEnd()\"));\n      }\n\n      var time = process.hrtime(prev);\n      this[TIMERS_SYMBOL].delete(label);\n      this[LOG_SYMBOL](LogType.time, [label].concat(_toConsumableArray(time)));\n    }\n  }, {\n    key: \"timeAggregate\",\n    value: function timeAggregate(label) {\n      var prev = this[TIMERS_SYMBOL] && this[TIMERS_SYMBOL].get(label);\n\n      if (!prev) {\n        throw new Error(\"No such label '\".concat(label, \"' for WebpackLogger.timeAggregate()\"));\n      }\n\n      var time = process.hrtime(prev);\n      this[TIMERS_SYMBOL].delete(label);\n      this[TIMERS_AGGREGATES_SYMBOL] = this[TIMERS_AGGREGATES_SYMBOL] || new Map();\n      var current = this[TIMERS_AGGREGATES_SYMBOL].get(label);\n\n      if (current !== undefined) {\n        if (time[1] + current[1] > 1e9) {\n          time[0] += current[0] + 1;\n          time[1] = time[1] - 1e9 + current[1];\n        } else {\n          time[0] += current[0];\n          time[1] += current[1];\n        }\n      }\n\n      this[TIMERS_AGGREGATES_SYMBOL].set(label, time);\n    }\n  }, {\n    key: \"timeAggregateEnd\",\n    value: function timeAggregateEnd(label) {\n      if (this[TIMERS_AGGREGATES_SYMBOL] === undefined) return;\n      var time = this[TIMERS_AGGREGATES_SYMBOL].get(label);\n      if (time === undefined) return;\n      this[TIMERS_AGGREGATES_SYMBOL].delete(label);\n      this[LOG_SYMBOL](LogType.time, [label].concat(_toConsumableArray(time)));\n    }\n  }]);\n\n  return WebpackLogger;\n}();\n\nexports.Logger = WebpackLogger;\n\n/***/ }),\n\n/***/ \"./node_modules/webpack/lib/logging/createConsoleLogger.js\":\n/*!*****************************************************************!*\\\n  !*** ./node_modules/webpack/lib/logging/createConsoleLogger.js ***!\n  \\*****************************************************************/\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\n/*\n\tMIT License http://www.opensource.org/licenses/mit-license.php\n\tAuthor Tobias Koppers @sokra\n*/\n\n\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\n\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _iterableToArray(iter) {\n  if (typeof (typeof Symbol !== \"undefined\" ? Symbol : function (i) { return i; }) !== \"undefined\" && iter[(typeof Symbol !== \"undefined\" ? Symbol : function (i) { return i; }).iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\n\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n\n  return arr2;\n}\n\nvar _require = __webpack_require__(/*! ./Logger */ \"./node_modules/webpack/lib/logging/Logger.js\"),\n    LogType = _require.LogType;\n/** @typedef {import(\"../../declarations/WebpackOptions\").FilterItemTypes} FilterItemTypes */\n\n/** @typedef {import(\"../../declarations/WebpackOptions\").FilterTypes} FilterTypes */\n\n/** @typedef {import(\"./Logger\").LogTypeEnum} LogTypeEnum */\n\n/** @typedef {function(string): boolean} FilterFunction */\n\n/**\n * @typedef {Object} LoggerConsole\n * @property {function(): void} clear\n * @property {function(): void} trace\n * @property {(...args: any[]) => void} info\n * @property {(...args: any[]) => void} log\n * @property {(...args: any[]) => void} warn\n * @property {(...args: any[]) => void} error\n * @property {(...args: any[]) => void=} debug\n * @property {(...args: any[]) => void=} group\n * @property {(...args: any[]) => void=} groupCollapsed\n * @property {(...args: any[]) => void=} groupEnd\n * @property {(...args: any[]) => void=} status\n * @property {(...args: any[]) => void=} profile\n * @property {(...args: any[]) => void=} profileEnd\n * @property {(...args: any[]) => void=} logTime\n */\n\n/**\n * @typedef {Object} LoggerOptions\n * @property {false|true|\"none\"|\"error\"|\"warn\"|\"info\"|\"log\"|\"verbose\"} level loglevel\n * @property {FilterTypes|boolean} debug filter for debug logging\n * @property {LoggerConsole} console the console to log to\n */\n\n/**\n * @param {FilterItemTypes} item an input item\n * @returns {FilterFunction} filter function\n */\n\n\nvar filterToFunction = function filterToFunction(item) {\n  if (typeof item === \"string\") {\n    var regExp = new RegExp(\"[\\\\\\\\/]\".concat(item.replace( // eslint-disable-next-line no-useless-escape\n    /[-[\\]{}()*+?.\\\\^$|]/g, \"\\\\$&\"), \"([\\\\\\\\/]|$|!|\\\\?)\"));\n    return function (ident) {\n      return regExp.test(ident);\n    };\n  }\n\n  if (item && typeof item === \"object\" && typeof item.test === \"function\") {\n    return function (ident) {\n      return item.test(ident);\n    };\n  }\n\n  if (typeof item === \"function\") {\n    return item;\n  }\n\n  if (typeof item === \"boolean\") {\n    return function () {\n      return item;\n    };\n  }\n};\n/**\n * @enum {number}\n */\n\n\nvar LogLevel = {\n  none: 6,\n  false: 6,\n  error: 5,\n  warn: 4,\n  info: 3,\n  log: 2,\n  true: 2,\n  verbose: 1\n};\n/**\n * @param {LoggerOptions} options options object\n * @returns {function(string, LogTypeEnum, any[]): void} logging function\n */\n\nmodule.exports = function (_ref) {\n  var _ref$level = _ref.level,\n      level = _ref$level === void 0 ? \"info\" : _ref$level,\n      _ref$debug = _ref.debug,\n      debug = _ref$debug === void 0 ? false : _ref$debug,\n      console = _ref.console;\n  var debugFilters = typeof debug === \"boolean\" ? [function () {\n    return debug;\n  }] :\n  /** @type {FilterItemTypes[]} */\n  [].concat(debug).map(filterToFunction);\n  /** @type {number} */\n\n  var loglevel = LogLevel[\"\".concat(level)] || 0;\n  /**\n   * @param {string} name name of the logger\n   * @param {LogTypeEnum} type type of the log entry\n   * @param {any[]} args arguments of the log entry\n   * @returns {void}\n   */\n\n  var logger = function logger(name, type, args) {\n    var labeledArgs = function labeledArgs() {\n      if (Array.isArray(args)) {\n        if (args.length > 0 && typeof args[0] === \"string\") {\n          return [\"[\".concat(name, \"] \").concat(args[0])].concat(_toConsumableArray(args.slice(1)));\n        } else {\n          return [\"[\".concat(name, \"]\")].concat(_toConsumableArray(args));\n        }\n      } else {\n        return [];\n      }\n    };\n\n    var debug = debugFilters.some(function (f) {\n      return f(name);\n    });\n\n    switch (type) {\n      case LogType.debug:\n        if (!debug) return; // eslint-disable-next-line node/no-unsupported-features/node-builtins\n\n        if (typeof console.debug === \"function\") {\n          // eslint-disable-next-line node/no-unsupported-features/node-builtins\n          console.debug.apply(console, _toConsumableArray(labeledArgs()));\n        } else {\n          console.log.apply(console, _toConsumableArray(labeledArgs()));\n        }\n\n        break;\n\n      case LogType.log:\n        if (!debug && loglevel > LogLevel.log) return;\n        console.log.apply(console, _toConsumableArray(labeledArgs()));\n        break;\n\n      case LogType.info:\n        if (!debug && loglevel > LogLevel.info) return;\n        console.info.apply(console, _toConsumableArray(labeledArgs()));\n        break;\n\n      case LogType.warn:\n        if (!debug && loglevel > LogLevel.warn) return;\n        console.warn.apply(console, _toConsumableArray(labeledArgs()));\n        break;\n\n      case LogType.error:\n        if (!debug && loglevel > LogLevel.error) return;\n        console.error.apply(console, _toConsumableArray(labeledArgs()));\n        break;\n\n      case LogType.trace:\n        if (!debug) return;\n        console.trace();\n        break;\n\n      case LogType.groupCollapsed:\n        if (!debug && loglevel > LogLevel.log) return;\n\n        if (!debug && loglevel > LogLevel.verbose) {\n          // eslint-disable-next-line node/no-unsupported-features/node-builtins\n          if (typeof console.groupCollapsed === \"function\") {\n            // eslint-disable-next-line node/no-unsupported-features/node-builtins\n            console.groupCollapsed.apply(console, _toConsumableArray(labeledArgs()));\n          } else {\n            console.log.apply(console, _toConsumableArray(labeledArgs()));\n          }\n\n          break;\n        }\n\n      // falls through\n\n      case LogType.group:\n        if (!debug && loglevel > LogLevel.log) return; // eslint-disable-next-line node/no-unsupported-features/node-builtins\n\n        if (typeof console.group === \"function\") {\n          // eslint-disable-next-line node/no-unsupported-features/node-builtins\n          console.group.apply(console, _toConsumableArray(labeledArgs()));\n        } else {\n          console.log.apply(console, _toConsumableArray(labeledArgs()));\n        }\n\n        break;\n\n      case LogType.groupEnd:\n        if (!debug && loglevel > LogLevel.log) return; // eslint-disable-next-line node/no-unsupported-features/node-builtins\n\n        if (typeof console.groupEnd === \"function\") {\n          // eslint-disable-next-line node/no-unsupported-features/node-builtins\n          console.groupEnd();\n        }\n\n        break;\n\n      case LogType.time:\n        {\n          if (!debug && loglevel > LogLevel.log) return;\n          var ms = args[1] * 1000 + args[2] / 1000000;\n          var msg = \"[\".concat(name, \"] \").concat(args[0], \": \").concat(ms, \" ms\");\n\n          if (typeof console.logTime === \"function\") {\n            console.logTime(msg);\n          } else {\n            console.log(msg);\n          }\n\n          break;\n        }\n\n      case LogType.profile:\n        // eslint-disable-next-line node/no-unsupported-features/node-builtins\n        if (typeof console.profile === \"function\") {\n          // eslint-disable-next-line node/no-unsupported-features/node-builtins\n          console.profile.apply(console, _toConsumableArray(labeledArgs()));\n        }\n\n        break;\n\n      case LogType.profileEnd:\n        // eslint-disable-next-line node/no-unsupported-features/node-builtins\n        if (typeof console.profileEnd === \"function\") {\n          // eslint-disable-next-line node/no-unsupported-features/node-builtins\n          console.profileEnd.apply(console, _toConsumableArray(labeledArgs()));\n        }\n\n        break;\n\n      case LogType.clear:\n        if (!debug && loglevel > LogLevel.log) return; // eslint-disable-next-line node/no-unsupported-features/node-builtins\n\n        if (typeof console.clear === \"function\") {\n          // eslint-disable-next-line node/no-unsupported-features/node-builtins\n          console.clear();\n        }\n\n        break;\n\n      case LogType.status:\n        if (!debug && loglevel > LogLevel.info) return;\n\n        if (typeof console.status === \"function\") {\n          if (args.length === 0) {\n            console.status();\n          } else {\n            console.status.apply(console, _toConsumableArray(labeledArgs()));\n          }\n        } else {\n          if (args.length !== 0) {\n            console.info.apply(console, _toConsumableArray(labeledArgs()));\n          }\n        }\n\n        break;\n\n      default:\n        throw new Error(\"Unexpected LogType \".concat(type));\n    }\n  };\n\n  return logger;\n};\n\n/***/ }),\n\n/***/ \"./node_modules/webpack/lib/logging/runtime.js\":\n/*!*****************************************************!*\\\n  !*** ./node_modules/webpack/lib/logging/runtime.js ***!\n  \\*****************************************************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n/*\n\tMIT License http://www.opensource.org/licenses/mit-license.php\n\tAuthor Tobias Koppers @sokra\n*/\n\n\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\nvar SyncBailHook = __webpack_require__(/*! tapable/lib/SyncBailHook */ \"./client-src/modules/logger/SyncBailHookFake.js\");\n\nvar _require = __webpack_require__(/*! ./Logger */ \"./node_modules/webpack/lib/logging/Logger.js\"),\n    Logger = _require.Logger;\n\nvar createConsoleLogger = __webpack_require__(/*! ./createConsoleLogger */ \"./node_modules/webpack/lib/logging/createConsoleLogger.js\");\n/** @type {createConsoleLogger.LoggerOptions} */\n\n\nvar currentDefaultLoggerOptions = {\n  level: \"info\",\n  debug: false,\n  console: console\n};\nvar currentDefaultLogger = createConsoleLogger(currentDefaultLoggerOptions);\n/**\n * @param {string} name name of the logger\n * @returns {Logger} a logger\n */\n\nexports.getLogger = function (name) {\n  return new Logger(function (type, args) {\n    if (exports.hooks.log.call(name, type, args) === undefined) {\n      currentDefaultLogger(name, type, args);\n    }\n  }, function (childName) {\n    return exports.getLogger(\"\".concat(name, \"/\").concat(childName));\n  });\n};\n/**\n * @param {createConsoleLogger.LoggerOptions} options new options, merge with old options\n * @returns {void}\n */\n\n\nexports.configureDefaultLogger = function (options) {\n  _extends(currentDefaultLoggerOptions, options);\n\n  currentDefaultLogger = createConsoleLogger(currentDefaultLoggerOptions);\n};\n\nexports.hooks = {\n  log: new SyncBailHook([\"origin\", \"type\", \"args\"])\n};\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/make namespace object */\n/******/ \t!function() {\n/******/ \t\t// define __esModule on exports\n/******/ \t\t__webpack_require__.r = function(exports) {\n/******/ \t\t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n/******/ \t\t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n/******/ \t\t\t}\n/******/ \t\t\tObject.defineProperty(exports, '__esModule', { value: true });\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n// This entry need to be wrapped in an IIFE because it need to be isolated against other modules in the chunk.\n!function() {\n/*!********************************************!*\\\n  !*** ./client-src/modules/logger/index.js ***!\n  \\********************************************/\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* reexport default export from named module */ webpack_lib_logging_runtime_js__WEBPACK_IMPORTED_MODULE_0__; }\n/* harmony export */ });\n/* harmony import */ var webpack_lib_logging_runtime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! webpack/lib/logging/runtime.js */ \"./node_modules/webpack/lib/logging/runtime.js\");\n\n}();\nvar __webpack_export_target__ = exports;\nfor(var i in __webpack_exports__) __webpack_export_target__[i] = __webpack_exports__[i];\nif(__webpack_exports__.__esModule) Object.defineProperty(__webpack_export_target__, \"__esModule\", { value: true });\n/******/ })()\n;"], "mappings": "AAAA;AAAS,CAAC,YAAW;EAAE;;EACvB;EAAU;EACV;;EAAU,IAAIA,mBAAmB,GAAI;IAErC;IAAM;IACN;AACA;AACA;;IACA;IAAO,UAASC,MAAT,EAAiB;MAGxB;AACA;AACA;MAEAA,MAAM,CAACC,OAAP,GAAiB,SAASC,yBAAT,GAAqC;QACpD,OAAO;UACLC,IAAI,EAAE,SAASA,IAAT,GAAgB,CAAE;QADnB,CAAP;MAGD,CAJD;MAMA;;IAAO,CAnB8B;;IAqBrC;IAAM;IACN;AACA;AACA;;IACA;IAAO,UAASC,uBAAT,EAAkCH,OAAlC,EAA2C;MAElD;AACA;AACA;AACA;MAGA,SAASI,kBAAT,CAA4BC,GAA5B,EAAiC;QAC/B,OAAOC,kBAAkB,CAACD,GAAD,CAAlB,IAA2BE,gBAAgB,CAACF,GAAD,CAA3C,IAAoDG,2BAA2B,CAACH,GAAD,CAA/E,IAAwFI,kBAAkB,EAAjH;MACD;;MAED,SAASA,kBAAT,GAA8B;QAC5B,MAAM,IAAIC,SAAJ,CAAc,sIAAd,CAAN;MACD;;MAED,SAASF,2BAAT,CAAqCG,CAArC,EAAwCC,MAAxC,EAAgD;QAC9C,IAAI,CAACD,CAAL,EAAQ;QACR,IAAI,OAAOA,CAAP,KAAa,QAAjB,EAA2B,OAAOE,iBAAiB,CAACF,CAAD,EAAIC,MAAJ,CAAxB;QAC3B,IAAIE,CAAC,GAAGC,MAAM,CAACC,SAAP,CAAiBC,QAAjB,CAA0Bf,IAA1B,CAA+BS,CAA/B,EAAkCO,KAAlC,CAAwC,CAAxC,EAA2C,CAAC,CAA5C,CAAR;QACA,IAAIJ,CAAC,KAAK,QAAN,IAAkBH,CAAC,CAACQ,WAAxB,EAAqCL,CAAC,GAAGH,CAAC,CAACQ,WAAF,CAAcC,IAAlB;QACrC,IAAIN,CAAC,KAAK,KAAN,IAAeA,CAAC,KAAK,KAAzB,EAAgC,OAAOO,KAAK,CAACC,IAAN,CAAWX,CAAX,CAAP;QAChC,IAAIG,CAAC,KAAK,WAAN,IAAqB,2CAA2CS,IAA3C,CAAgDT,CAAhD,CAAzB,EAA6E,OAAOD,iBAAiB,CAACF,CAAD,EAAIC,MAAJ,CAAxB;MAC9E;;MAED,SAASL,gBAAT,CAA0BiB,IAA1B,EAAgC;QAC9B,IAAI,QAAQ,OAAOC,MAAP,KAAkB,WAAlB,GAAgCA,MAAhC,GAAyC,UAAUC,CAAV,EAAa;UAAE,OAAOA,CAAP;QAAW,CAA3E,MAAiF,WAAjF,IAAgGF,IAAI,CAAC,CAAC,OAAOC,MAAP,KAAkB,WAAlB,GAAgCA,MAAhC,GAAyC,UAAUC,CAAV,EAAa;UAAE,OAAOA,CAAP;QAAW,CAApE,EAAsEC,QAAvE,CAAJ,IAAwF,IAAxL,IAAgMH,IAAI,CAAC,YAAD,CAAJ,IAAsB,IAA1N,EAAgO,OAAOH,KAAK,CAACC,IAAN,CAAWE,IAAX,CAAP;MACjO;;MAED,SAASlB,kBAAT,CAA4BD,GAA5B,EAAiC;QAC/B,IAAIgB,KAAK,CAACO,OAAN,CAAcvB,GAAd,CAAJ,EAAwB,OAAOQ,iBAAiB,CAACR,GAAD,CAAxB;MACzB;;MAED,SAASQ,iBAAT,CAA2BR,GAA3B,EAAgCwB,GAAhC,EAAqC;QACnC,IAAIA,GAAG,IAAI,IAAP,IAAeA,GAAG,GAAGxB,GAAG,CAACyB,MAA7B,EAAqCD,GAAG,GAAGxB,GAAG,CAACyB,MAAV;;QAErC,KAAK,IAAIJ,CAAC,GAAG,CAAR,EAAWK,IAAI,GAAG,IAAIV,KAAJ,CAAUQ,GAAV,CAAvB,EAAuCH,CAAC,GAAGG,GAA3C,EAAgDH,CAAC,EAAjD,EAAqD;UACnDK,IAAI,CAACL,CAAD,CAAJ,GAAUrB,GAAG,CAACqB,CAAD,CAAb;QACD;;QAED,OAAOK,IAAP;MACD;;MAED,SAASC,eAAT,CAAyBC,QAAzB,EAAmCC,WAAnC,EAAgD;QAC9C,IAAI,EAAED,QAAQ,YAAYC,WAAtB,CAAJ,EAAwC;UACtC,MAAM,IAAIxB,SAAJ,CAAc,mCAAd,CAAN;QACD;MACF;;MAED,SAASyB,iBAAT,CAA2BC,MAA3B,EAAmCC,KAAnC,EAA0C;QACxC,KAAK,IAAIX,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGW,KAAK,CAACP,MAA1B,EAAkCJ,CAAC,EAAnC,EAAuC;UACrC,IAAIY,UAAU,GAAGD,KAAK,CAACX,CAAD,CAAtB;UACAY,UAAU,CAACC,UAAX,GAAwBD,UAAU,CAACC,UAAX,IAAyB,KAAjD;UACAD,UAAU,CAACE,YAAX,GAA0B,IAA1B;UACA,IAAI,WAAWF,UAAf,EAA2BA,UAAU,CAACG,QAAX,GAAsB,IAAtB;UAC3B1B,MAAM,CAAC2B,cAAP,CAAsBN,MAAtB,EAA8BE,UAAU,CAACK,GAAzC,EAA8CL,UAA9C;QACD;MACF;;MAED,SAASM,YAAT,CAAsBV,WAAtB,EAAmCW,UAAnC,EAA+CC,WAA/C,EAA4D;QAC1D,IAAID,UAAJ,EAAgBV,iBAAiB,CAACD,WAAW,CAAClB,SAAb,EAAwB6B,UAAxB,CAAjB;QAChB,IAAIC,WAAJ,EAAiBX,iBAAiB,CAACD,WAAD,EAAcY,WAAd,CAAjB;QACjB/B,MAAM,CAAC2B,cAAP,CAAsBR,WAAtB,EAAmC,WAAnC,EAAgD;UAC9CO,QAAQ,EAAE;QADoC,CAAhD;QAGA,OAAOP,WAAP;MACD;;MAED,IAAIa,OAAO,GAAGhC,MAAM,CAACiC,MAAP,CAAc;QAC1BC,KAAK;QACL;QACA,OAH0B;QAI1B;QACAC,IAAI;QACJ;QACA,MAP0B;QAQ1B;QACAC,IAAI;QACJ;QACA,MAX0B;QAY1B;QACAC,GAAG;QACH;QACA,KAf0B;QAgB1B;QACAC,KAAK;QACL;QACA,OAnB0B;QAoB1B;QACAC,KAAK;QACL;QACA,OAvB0B;QAwB1B;QACAC,KAAK;QACL;QACA,OA3B0B;QA4B1B;QACAC,cAAc;QACd;QACA,gBA/B0B;QAgC1B;QACAC,QAAQ;QACR;QACA,UAnC0B;QAoC1B;QACAC,OAAO;QACP;QACA,SAvC0B;QAwC1B;QACAC,UAAU;QACV;QACA,YA3C0B;QA4C1B;QACAC,IAAI;QACJ;QACA,MA/C0B;QAgD1B;QACAC,KAAK;QACL;QACA,OAnD0B;QAoD1B;QACAC,MAAM;QACN;QACA,QAvD0B,CAuDjB;;MAvDiB,CAAd,CAAd;MA0DA9D,OAAO,CAAC+C,OAAR,GAAkBA,OAAlB;MACA;;MAEA,IAAIgB,UAAU,GAAG,CAAC,OAAOtC,MAAP,KAAkB,WAAlB,GAAgCA,MAAhC,GAAyC,UAAUC,CAAV,EAAa;QAAE,OAAOA,CAAP;MAAW,CAApE,EAAsE,+BAAtE,CAAjB;MACA,IAAIsC,aAAa,GAAG,CAAC,OAAOvC,MAAP,KAAkB,WAAlB,GAAgCA,MAAhC,GAAyC,UAAUC,CAAV,EAAa;QAAE,OAAOA,CAAP;MAAW,CAApE,EAAsE,sBAAtE,CAApB;MACA,IAAIuC,wBAAwB,GAAG,CAAC,OAAOxC,MAAP,KAAkB,WAAlB,GAAgCA,MAAhC,GAAyC,UAAUC,CAAV,EAAa;QAAE,OAAOA,CAAP;MAAW,CAApE,EAAsE,iCAAtE,CAA/B;;MAEA,IAAIwC,aAAa,GAAG,aAAa,YAAY;QAC3C;AACF;AACA;AACA;QACE,SAASA,aAAT,CAAuBd,GAAvB,EAA4Be,cAA5B,EAA4C;UAC1CnC,eAAe,CAAC,IAAD,EAAOkC,aAAP,CAAf;;UAEA,KAAKH,UAAL,IAAmBX,GAAnB;UACA,KAAKe,cAAL,GAAsBA,cAAtB;QACD;;QAEDvB,YAAY,CAACsB,aAAD,EAAgB,CAAC;UAC3BvB,GAAG,EAAE,OADsB;UAE3ByB,KAAK,EAAE,SAASnB,KAAT,GAAiB;YACtB,KAAK,IAAIoB,IAAI,GAAGC,SAAS,CAACxC,MAArB,EAA6ByC,IAAI,GAAG,IAAIlD,KAAJ,CAAUgD,IAAV,CAApC,EAAqDG,IAAI,GAAG,CAAjE,EAAoEA,IAAI,GAAGH,IAA3E,EAAiFG,IAAI,EAArF,EAAyF;cACvFD,IAAI,CAACC,IAAD,CAAJ,GAAaF,SAAS,CAACE,IAAD,CAAtB;YACD;;YAED,KAAKT,UAAL,EAAiBhB,OAAO,CAACE,KAAzB,EAAgCsB,IAAhC;UACD;QAR0B,CAAD,EASzB;UACD5B,GAAG,EAAE,MADJ;UAEDyB,KAAK,EAAE,SAASlB,IAAT,GAAgB;YACrB,KAAK,IAAIuB,KAAK,GAAGH,SAAS,CAACxC,MAAtB,EAA8ByC,IAAI,GAAG,IAAIlD,KAAJ,CAAUoD,KAAV,CAArC,EAAuDC,KAAK,GAAG,CAApE,EAAuEA,KAAK,GAAGD,KAA/E,EAAsFC,KAAK,EAA3F,EAA+F;cAC7FH,IAAI,CAACG,KAAD,CAAJ,GAAcJ,SAAS,CAACI,KAAD,CAAvB;YACD;;YAED,KAAKX,UAAL,EAAiBhB,OAAO,CAACG,IAAzB,EAA+BqB,IAA/B;UACD;QARA,CATyB,EAkBzB;UACD5B,GAAG,EAAE,MADJ;UAEDyB,KAAK,EAAE,SAASjB,IAAT,GAAgB;YACrB,KAAK,IAAIwB,KAAK,GAAGL,SAAS,CAACxC,MAAtB,EAA8ByC,IAAI,GAAG,IAAIlD,KAAJ,CAAUsD,KAAV,CAArC,EAAuDC,KAAK,GAAG,CAApE,EAAuEA,KAAK,GAAGD,KAA/E,EAAsFC,KAAK,EAA3F,EAA+F;cAC7FL,IAAI,CAACK,KAAD,CAAJ,GAAcN,SAAS,CAACM,KAAD,CAAvB;YACD;;YAED,KAAKb,UAAL,EAAiBhB,OAAO,CAACI,IAAzB,EAA+BoB,IAA/B;UACD;QARA,CAlByB,EA2BzB;UACD5B,GAAG,EAAE,KADJ;UAEDyB,KAAK,EAAE,SAAShB,GAAT,GAAe;YACpB,KAAK,IAAIyB,KAAK,GAAGP,SAAS,CAACxC,MAAtB,EAA8ByC,IAAI,GAAG,IAAIlD,KAAJ,CAAUwD,KAAV,CAArC,EAAuDC,KAAK,GAAG,CAApE,EAAuEA,KAAK,GAAGD,KAA/E,EAAsFC,KAAK,EAA3F,EAA+F;cAC7FP,IAAI,CAACO,KAAD,CAAJ,GAAcR,SAAS,CAACQ,KAAD,CAAvB;YACD;;YAED,KAAKf,UAAL,EAAiBhB,OAAO,CAACK,GAAzB,EAA8BmB,IAA9B;UACD;QARA,CA3ByB,EAoCzB;UACD5B,GAAG,EAAE,OADJ;UAEDyB,KAAK,EAAE,SAASf,KAAT,GAAiB;YACtB,KAAK,IAAI0B,KAAK,GAAGT,SAAS,CAACxC,MAAtB,EAA8ByC,IAAI,GAAG,IAAIlD,KAAJ,CAAU0D,KAAV,CAArC,EAAuDC,KAAK,GAAG,CAApE,EAAuEA,KAAK,GAAGD,KAA/E,EAAsFC,KAAK,EAA3F,EAA+F;cAC7FT,IAAI,CAACS,KAAD,CAAJ,GAAcV,SAAS,CAACU,KAAD,CAAvB;YACD;;YAED,KAAKjB,UAAL,EAAiBhB,OAAO,CAACM,KAAzB,EAAgCkB,IAAhC;UACD;QARA,CApCyB,EA6CzB;UACD5B,GAAG,EAAE,QADJ;UAEDyB,KAAK,EAAE,SAASa,MAAT,CAAgBC,SAAhB,EAA2B;YAChC,IAAI,CAACA,SAAL,EAAgB;cACd,KAAK,IAAIC,KAAK,GAAGb,SAAS,CAACxC,MAAtB,EAA8ByC,IAAI,GAAG,IAAIlD,KAAJ,CAAU8D,KAAK,GAAG,CAAR,GAAYA,KAAK,GAAG,CAApB,GAAwB,CAAlC,CAArC,EAA2EC,KAAK,GAAG,CAAxF,EAA2FA,KAAK,GAAGD,KAAnG,EAA0GC,KAAK,EAA/G,EAAmH;gBACjHb,IAAI,CAACa,KAAK,GAAG,CAAT,CAAJ,GAAkBd,SAAS,CAACc,KAAD,CAA3B;cACD;;cAED,KAAKrB,UAAL,EAAiBhB,OAAO,CAACE,KAAzB,EAAgCsB,IAAhC;YACD;UACF;QAVA,CA7CyB,EAwDzB;UACD5B,GAAG,EAAE,OADJ;UAEDyB,KAAK,EAAE,SAASd,KAAT,GAAiB;YACtB,KAAKS,UAAL,EAAiBhB,OAAO,CAACO,KAAzB,EAAgC,CAAC,OAAD,CAAhC;UACD;QAJA,CAxDyB,EA6DzB;UACDX,GAAG,EAAE,OADJ;UAEDyB,KAAK,EAAE,SAASP,KAAT,GAAiB;YACtB,KAAKE,UAAL,EAAiBhB,OAAO,CAACc,KAAzB;UACD;QAJA,CA7DyB,EAkEzB;UACDlB,GAAG,EAAE,QADJ;UAEDyB,KAAK,EAAE,SAASN,MAAT,GAAkB;YACvB,KAAK,IAAIuB,KAAK,GAAGf,SAAS,CAACxC,MAAtB,EAA8ByC,IAAI,GAAG,IAAIlD,KAAJ,CAAUgE,KAAV,CAArC,EAAuDC,KAAK,GAAG,CAApE,EAAuEA,KAAK,GAAGD,KAA/E,EAAsFC,KAAK,EAA3F,EAA+F;cAC7Ff,IAAI,CAACe,KAAD,CAAJ,GAAchB,SAAS,CAACgB,KAAD,CAAvB;YACD;;YAED,KAAKvB,UAAL,EAAiBhB,OAAO,CAACe,MAAzB,EAAiCS,IAAjC;UACD;QARA,CAlEyB,EA2EzB;UACD5B,GAAG,EAAE,OADJ;UAEDyB,KAAK,EAAE,SAASb,KAAT,GAAiB;YACtB,KAAK,IAAIgC,KAAK,GAAGjB,SAAS,CAACxC,MAAtB,EAA8ByC,IAAI,GAAG,IAAIlD,KAAJ,CAAUkE,KAAV,CAArC,EAAuDC,KAAK,GAAG,CAApE,EAAuEA,KAAK,GAAGD,KAA/E,EAAsFC,KAAK,EAA3F,EAA+F;cAC7FjB,IAAI,CAACiB,KAAD,CAAJ,GAAclB,SAAS,CAACkB,KAAD,CAAvB;YACD;;YAED,KAAKzB,UAAL,EAAiBhB,OAAO,CAACQ,KAAzB,EAAgCgB,IAAhC;UACD;QARA,CA3EyB,EAoFzB;UACD5B,GAAG,EAAE,gBADJ;UAEDyB,KAAK,EAAE,SAASZ,cAAT,GAA0B;YAC/B,KAAK,IAAIiC,KAAK,GAAGnB,SAAS,CAACxC,MAAtB,EAA8ByC,IAAI,GAAG,IAAIlD,KAAJ,CAAUoE,KAAV,CAArC,EAAuDC,KAAK,GAAG,CAApE,EAAuEA,KAAK,GAAGD,KAA/E,EAAsFC,KAAK,EAA3F,EAA+F;cAC7FnB,IAAI,CAACmB,KAAD,CAAJ,GAAcpB,SAAS,CAACoB,KAAD,CAAvB;YACD;;YAED,KAAK3B,UAAL,EAAiBhB,OAAO,CAACS,cAAzB,EAAyCe,IAAzC;UACD;QARA,CApFyB,EA6FzB;UACD5B,GAAG,EAAE,UADJ;UAEDyB,KAAK,EAAE,SAASX,QAAT,GAAoB;YACzB,KAAK,IAAIkC,MAAM,GAAGrB,SAAS,CAACxC,MAAvB,EAA+ByC,IAAI,GAAG,IAAIlD,KAAJ,CAAUsE,MAAV,CAAtC,EAAyDC,MAAM,GAAG,CAAvE,EAA0EA,MAAM,GAAGD,MAAnF,EAA2FC,MAAM,EAAjG,EAAqG;cACnGrB,IAAI,CAACqB,MAAD,CAAJ,GAAetB,SAAS,CAACsB,MAAD,CAAxB;YACD;;YAED,KAAK7B,UAAL,EAAiBhB,OAAO,CAACU,QAAzB,EAAmCc,IAAnC;UACD;QARA,CA7FyB,EAsGzB;UACD5B,GAAG,EAAE,SADJ;UAEDyB,KAAK,EAAE,SAASV,OAAT,CAAiBmC,KAAjB,EAAwB;YAC7B,KAAK9B,UAAL,EAAiBhB,OAAO,CAACW,OAAzB,EAAkC,CAACmC,KAAD,CAAlC;UACD;QAJA,CAtGyB,EA2GzB;UACDlD,GAAG,EAAE,YADJ;UAEDyB,KAAK,EAAE,SAAST,UAAT,CAAoBkC,KAApB,EAA2B;YAChC,KAAK9B,UAAL,EAAiBhB,OAAO,CAACY,UAAzB,EAAqC,CAACkC,KAAD,CAArC;UACD;QAJA,CA3GyB,EAgHzB;UACDlD,GAAG,EAAE,MADJ;UAEDyB,KAAK,EAAE,SAASR,IAAT,CAAciC,KAAd,EAAqB;YAC1B,KAAK7B,aAAL,IAAsB,KAAKA,aAAL,KAAuB,IAAI8B,GAAJ,EAA7C;YACA,KAAK9B,aAAL,EAAoB+B,GAApB,CAAwBF,KAAxB,EAA+BG,OAAO,CAACC,MAAR,EAA/B;UACD;QALA,CAhHyB,EAsHzB;UACDtD,GAAG,EAAE,SADJ;UAEDyB,KAAK,EAAE,SAAS8B,OAAT,CAAiBL,KAAjB,EAAwB;YAC7B,IAAIM,IAAI,GAAG,KAAKnC,aAAL,KAAuB,KAAKA,aAAL,EAAoBoC,GAApB,CAAwBP,KAAxB,CAAlC;;YAEA,IAAI,CAACM,IAAL,EAAW;cACT,MAAM,IAAIE,KAAJ,CAAU,kBAAkBC,MAAlB,CAAyBT,KAAzB,EAAgC,+BAAhC,CAAV,CAAN;YACD;;YAED,IAAIjC,IAAI,GAAGoC,OAAO,CAACC,MAAR,CAAeE,IAAf,CAAX;YACA,KAAKpC,UAAL,EAAiBhB,OAAO,CAACa,IAAzB,EAA+B,CAACiC,KAAD,EAAQS,MAAR,CAAelG,kBAAkB,CAACwD,IAAD,CAAjC,CAA/B;UACD;QAXA,CAtHyB,EAkIzB;UACDjB,GAAG,EAAE,SADJ;UAEDyB,KAAK,EAAE,SAASmC,OAAT,CAAiBV,KAAjB,EAAwB;YAC7B,IAAIM,IAAI,GAAG,KAAKnC,aAAL,KAAuB,KAAKA,aAAL,EAAoBoC,GAApB,CAAwBP,KAAxB,CAAlC;;YAEA,IAAI,CAACM,IAAL,EAAW;cACT,MAAM,IAAIE,KAAJ,CAAU,kBAAkBC,MAAlB,CAAyBT,KAAzB,EAAgC,+BAAhC,CAAV,CAAN;YACD;;YAED,IAAIjC,IAAI,GAAGoC,OAAO,CAACC,MAAR,CAAeE,IAAf,CAAX;YACA,KAAKnC,aAAL,EAAoBwC,MAApB,CAA2BX,KAA3B;YACA,KAAK9B,UAAL,EAAiBhB,OAAO,CAACa,IAAzB,EAA+B,CAACiC,KAAD,EAAQS,MAAR,CAAelG,kBAAkB,CAACwD,IAAD,CAAjC,CAA/B;UACD;QAZA,CAlIyB,EA+IzB;UACDjB,GAAG,EAAE,eADJ;UAEDyB,KAAK,EAAE,SAASqC,aAAT,CAAuBZ,KAAvB,EAA8B;YACnC,IAAIM,IAAI,GAAG,KAAKnC,aAAL,KAAuB,KAAKA,aAAL,EAAoBoC,GAApB,CAAwBP,KAAxB,CAAlC;;YAEA,IAAI,CAACM,IAAL,EAAW;cACT,MAAM,IAAIE,KAAJ,CAAU,kBAAkBC,MAAlB,CAAyBT,KAAzB,EAAgC,qCAAhC,CAAV,CAAN;YACD;;YAED,IAAIjC,IAAI,GAAGoC,OAAO,CAACC,MAAR,CAAeE,IAAf,CAAX;YACA,KAAKnC,aAAL,EAAoBwC,MAApB,CAA2BX,KAA3B;YACA,KAAK5B,wBAAL,IAAiC,KAAKA,wBAAL,KAAkC,IAAI6B,GAAJ,EAAnE;YACA,IAAIY,OAAO,GAAG,KAAKzC,wBAAL,EAA+BmC,GAA/B,CAAmCP,KAAnC,CAAd;;YAEA,IAAIa,OAAO,KAAKC,SAAhB,EAA2B;cACzB,IAAI/C,IAAI,CAAC,CAAD,CAAJ,GAAU8C,OAAO,CAAC,CAAD,CAAjB,GAAuB,GAA3B,EAAgC;gBAC9B9C,IAAI,CAAC,CAAD,CAAJ,IAAW8C,OAAO,CAAC,CAAD,CAAP,GAAa,CAAxB;gBACA9C,IAAI,CAAC,CAAD,CAAJ,GAAUA,IAAI,CAAC,CAAD,CAAJ,GAAU,GAAV,GAAgB8C,OAAO,CAAC,CAAD,CAAjC;cACD,CAHD,MAGO;gBACL9C,IAAI,CAAC,CAAD,CAAJ,IAAW8C,OAAO,CAAC,CAAD,CAAlB;gBACA9C,IAAI,CAAC,CAAD,CAAJ,IAAW8C,OAAO,CAAC,CAAD,CAAlB;cACD;YACF;;YAED,KAAKzC,wBAAL,EAA+B8B,GAA/B,CAAmCF,KAAnC,EAA0CjC,IAA1C;UACD;QAzBA,CA/IyB,EAyKzB;UACDjB,GAAG,EAAE,kBADJ;UAEDyB,KAAK,EAAE,SAASwC,gBAAT,CAA0Bf,KAA1B,EAAiC;YACtC,IAAI,KAAK5B,wBAAL,MAAmC0C,SAAvC,EAAkD;YAClD,IAAI/C,IAAI,GAAG,KAAKK,wBAAL,EAA+BmC,GAA/B,CAAmCP,KAAnC,CAAX;YACA,IAAIjC,IAAI,KAAK+C,SAAb,EAAwB;YACxB,KAAK1C,wBAAL,EAA+BuC,MAA/B,CAAsCX,KAAtC;YACA,KAAK9B,UAAL,EAAiBhB,OAAO,CAACa,IAAzB,EAA+B,CAACiC,KAAD,EAAQS,MAAR,CAAelG,kBAAkB,CAACwD,IAAD,CAAjC,CAA/B;UACD;QARA,CAzKyB,CAAhB,CAAZ;;QAoLA,OAAOM,aAAP;MACD,CAjMgC,EAAjC;;MAmMAlE,OAAO,CAAC6G,MAAR,GAAiB3C,aAAjB;MAEA;IAAO,CAnW8B;;IAqWrC;IAAM;IACN;AACA;AACA;;IACA;IAAO,UAASnE,MAAT,EAAiB+G,wBAAjB,EAA2CC,mBAA3C,EAAgE;MAEvE;AACA;AACA;AACA;MAGA,SAAS3G,kBAAT,CAA4BC,GAA5B,EAAiC;QAC/B,OAAOC,kBAAkB,CAACD,GAAD,CAAlB,IAA2BE,gBAAgB,CAACF,GAAD,CAA3C,IAAoDG,2BAA2B,CAACH,GAAD,CAA/E,IAAwFI,kBAAkB,EAAjH;MACD;;MAED,SAASA,kBAAT,GAA8B;QAC5B,MAAM,IAAIC,SAAJ,CAAc,sIAAd,CAAN;MACD;;MAED,SAASF,2BAAT,CAAqCG,CAArC,EAAwCC,MAAxC,EAAgD;QAC9C,IAAI,CAACD,CAAL,EAAQ;QACR,IAAI,OAAOA,CAAP,KAAa,QAAjB,EAA2B,OAAOE,iBAAiB,CAACF,CAAD,EAAIC,MAAJ,CAAxB;QAC3B,IAAIE,CAAC,GAAGC,MAAM,CAACC,SAAP,CAAiBC,QAAjB,CAA0Bf,IAA1B,CAA+BS,CAA/B,EAAkCO,KAAlC,CAAwC,CAAxC,EAA2C,CAAC,CAA5C,CAAR;QACA,IAAIJ,CAAC,KAAK,QAAN,IAAkBH,CAAC,CAACQ,WAAxB,EAAqCL,CAAC,GAAGH,CAAC,CAACQ,WAAF,CAAcC,IAAlB;QACrC,IAAIN,CAAC,KAAK,KAAN,IAAeA,CAAC,KAAK,KAAzB,EAAgC,OAAOO,KAAK,CAACC,IAAN,CAAWX,CAAX,CAAP;QAChC,IAAIG,CAAC,KAAK,WAAN,IAAqB,2CAA2CS,IAA3C,CAAgDT,CAAhD,CAAzB,EAA6E,OAAOD,iBAAiB,CAACF,CAAD,EAAIC,MAAJ,CAAxB;MAC9E;;MAED,SAASL,gBAAT,CAA0BiB,IAA1B,EAAgC;QAC9B,IAAI,QAAQ,OAAOC,MAAP,KAAkB,WAAlB,GAAgCA,MAAhC,GAAyC,UAAUC,CAAV,EAAa;UAAE,OAAOA,CAAP;QAAW,CAA3E,MAAiF,WAAjF,IAAgGF,IAAI,CAAC,CAAC,OAAOC,MAAP,KAAkB,WAAlB,GAAgCA,MAAhC,GAAyC,UAAUC,CAAV,EAAa;UAAE,OAAOA,CAAP;QAAW,CAApE,EAAsEC,QAAvE,CAAJ,IAAwF,IAAxL,IAAgMH,IAAI,CAAC,YAAD,CAAJ,IAAsB,IAA1N,EAAgO,OAAOH,KAAK,CAACC,IAAN,CAAWE,IAAX,CAAP;MACjO;;MAED,SAASlB,kBAAT,CAA4BD,GAA5B,EAAiC;QAC/B,IAAIgB,KAAK,CAACO,OAAN,CAAcvB,GAAd,CAAJ,EAAwB,OAAOQ,iBAAiB,CAACR,GAAD,CAAxB;MACzB;;MAED,SAASQ,iBAAT,CAA2BR,GAA3B,EAAgCwB,GAAhC,EAAqC;QACnC,IAAIA,GAAG,IAAI,IAAP,IAAeA,GAAG,GAAGxB,GAAG,CAACyB,MAA7B,EAAqCD,GAAG,GAAGxB,GAAG,CAACyB,MAAV;;QAErC,KAAK,IAAIJ,CAAC,GAAG,CAAR,EAAWK,IAAI,GAAG,IAAIV,KAAJ,CAAUQ,GAAV,CAAvB,EAAuCH,CAAC,GAAGG,GAA3C,EAAgDH,CAAC,EAAjD,EAAqD;UACnDK,IAAI,CAACL,CAAD,CAAJ,GAAUrB,GAAG,CAACqB,CAAD,CAAb;QACD;;QAED,OAAOK,IAAP;MACD;;MAED,IAAIiF,QAAQ,GAAGD,mBAAmB;MAAC;MAAgB,8CAAjB,CAAlC;MAAA,IACIhE,OAAO,GAAGiE,QAAQ,CAACjE,OADvB;MAEA;;MAEA;;MAEA;;MAEA;;MAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAEA;AACA;AACA;AACA;AACA;AACA;;MAEA;AACA;AACA;AACA;;;MAGA,IAAIkE,gBAAgB,GAAG,SAASA,gBAAT,CAA0BC,IAA1B,EAAgC;QACrD,IAAI,OAAOA,IAAP,KAAgB,QAApB,EAA8B;UAC5B,IAAIC,MAAM,GAAG,IAAIC,MAAJ,CAAW,UAAUd,MAAV,CAAiBY,IAAI,CAACG,OAAL,EAAc;UACvD,sBADyC,EACjB,MADiB,CAAjB,EACS,mBADT,CAAX,CAAb;UAEA,OAAO,UAAUC,KAAV,EAAiB;YACtB,OAAOH,MAAM,CAAC5F,IAAP,CAAY+F,KAAZ,CAAP;UACD,CAFD;QAGD;;QAED,IAAIJ,IAAI,IAAI,OAAOA,IAAP,KAAgB,QAAxB,IAAoC,OAAOA,IAAI,CAAC3F,IAAZ,KAAqB,UAA7D,EAAyE;UACvE,OAAO,UAAU+F,KAAV,EAAiB;YACtB,OAAOJ,IAAI,CAAC3F,IAAL,CAAU+F,KAAV,CAAP;UACD,CAFD;QAGD;;QAED,IAAI,OAAOJ,IAAP,KAAgB,UAApB,EAAgC;UAC9B,OAAOA,IAAP;QACD;;QAED,IAAI,OAAOA,IAAP,KAAgB,SAApB,EAA+B;UAC7B,OAAO,YAAY;YACjB,OAAOA,IAAP;UACD,CAFD;QAGD;MACF,CAxBD;MAyBA;AACA;AACA;;;MAGA,IAAIK,QAAQ,GAAG;QACbC,IAAI,EAAE,CADO;QAEbC,KAAK,EAAE,CAFM;QAGbxE,KAAK,EAAE,CAHM;QAIbC,IAAI,EAAE,CAJO;QAKbC,IAAI,EAAE,CALO;QAMbC,GAAG,EAAE,CANQ;QAObsE,IAAI,EAAE,CAPO;QAQbC,OAAO,EAAE;MARI,CAAf;MAUA;AACA;AACA;AACA;;MAEA5H,MAAM,CAACC,OAAP,GAAiB,UAAU4H,IAAV,EAAgB;QAC/B,IAAIC,UAAU,GAAGD,IAAI,CAACE,KAAtB;QAAA,IACIA,KAAK,GAAGD,UAAU,KAAK,KAAK,CAApB,GAAwB,MAAxB,GAAiCA,UAD7C;QAAA,IAEIE,UAAU,GAAGH,IAAI,CAACvE,KAFtB;QAAA,IAGIA,KAAK,GAAG0E,UAAU,KAAK,KAAK,CAApB,GAAwB,KAAxB,GAAgCA,UAH5C;QAAA,IAIIC,OAAO,GAAGJ,IAAI,CAACI,OAJnB;QAKA,IAAIC,YAAY,GAAG,OAAO5E,KAAP,KAAiB,SAAjB,GAA6B,CAAC,YAAY;UAC3D,OAAOA,KAAP;QACD,CAF+C,CAA7B;QAGnB;QACA,GAAGiD,MAAH,CAAUjD,KAAV,EAAiB6E,GAAjB,CAAqBjB,gBAArB,CAJA;QAKA;;QAEA,IAAIkB,QAAQ,GAAGZ,QAAQ,CAAC,GAAGjB,MAAH,CAAUwB,KAAV,CAAD,CAAR,IAA8B,CAA7C;QACA;AACF;AACA;AACA;AACA;AACA;;QAEE,IAAIM,MAAM,GAAG,SAASA,MAAT,CAAgBhH,IAAhB,EAAsBiH,IAAtB,EAA4B9D,IAA5B,EAAkC;UAC7C,IAAI+D,WAAW,GAAG,SAASA,WAAT,GAAuB;YACvC,IAAIjH,KAAK,CAACO,OAAN,CAAc2C,IAAd,CAAJ,EAAyB;cACvB,IAAIA,IAAI,CAACzC,MAAL,GAAc,CAAd,IAAmB,OAAOyC,IAAI,CAAC,CAAD,CAAX,KAAmB,QAA1C,EAAoD;gBAClD,OAAO,CAAC,IAAI+B,MAAJ,CAAWlF,IAAX,EAAiB,IAAjB,EAAuBkF,MAAvB,CAA8B/B,IAAI,CAAC,CAAD,CAAlC,CAAD,EAAyC+B,MAAzC,CAAgDlG,kBAAkB,CAACmE,IAAI,CAACrD,KAAL,CAAW,CAAX,CAAD,CAAlE,CAAP;cACD,CAFD,MAEO;gBACL,OAAO,CAAC,IAAIoF,MAAJ,CAAWlF,IAAX,EAAiB,GAAjB,CAAD,EAAwBkF,MAAxB,CAA+BlG,kBAAkB,CAACmE,IAAD,CAAjD,CAAP;cACD;YACF,CAND,MAMO;cACL,OAAO,EAAP;YACD;UACF,CAVD;;UAYA,IAAIlB,KAAK,GAAG4E,YAAY,CAACM,IAAb,CAAkB,UAAUC,CAAV,EAAa;YACzC,OAAOA,CAAC,CAACpH,IAAD,CAAR;UACD,CAFW,CAAZ;;UAIA,QAAQiH,IAAR;YACE,KAAKtF,OAAO,CAACM,KAAb;cACE,IAAI,CAACA,KAAL,EAAY,OADd,CACsB;;cAEpB,IAAI,OAAO2E,OAAO,CAAC3E,KAAf,KAAyB,UAA7B,EAAyC;gBACvC;gBACA2E,OAAO,CAAC3E,KAAR,CAAcoF,KAAd,CAAoBT,OAApB,EAA6B5H,kBAAkB,CAACkI,WAAW,EAAZ,CAA/C;cACD,CAHD,MAGO;gBACLN,OAAO,CAAC5E,GAAR,CAAYqF,KAAZ,CAAkBT,OAAlB,EAA2B5H,kBAAkB,CAACkI,WAAW,EAAZ,CAA7C;cACD;;cAED;;YAEF,KAAKvF,OAAO,CAACK,GAAb;cACE,IAAI,CAACC,KAAD,IAAU8E,QAAQ,GAAGZ,QAAQ,CAACnE,GAAlC,EAAuC;cACvC4E,OAAO,CAAC5E,GAAR,CAAYqF,KAAZ,CAAkBT,OAAlB,EAA2B5H,kBAAkB,CAACkI,WAAW,EAAZ,CAA7C;cACA;;YAEF,KAAKvF,OAAO,CAACI,IAAb;cACE,IAAI,CAACE,KAAD,IAAU8E,QAAQ,GAAGZ,QAAQ,CAACpE,IAAlC,EAAwC;cACxC6E,OAAO,CAAC7E,IAAR,CAAasF,KAAb,CAAmBT,OAAnB,EAA4B5H,kBAAkB,CAACkI,WAAW,EAAZ,CAA9C;cACA;;YAEF,KAAKvF,OAAO,CAACG,IAAb;cACE,IAAI,CAACG,KAAD,IAAU8E,QAAQ,GAAGZ,QAAQ,CAACrE,IAAlC,EAAwC;cACxC8E,OAAO,CAAC9E,IAAR,CAAauF,KAAb,CAAmBT,OAAnB,EAA4B5H,kBAAkB,CAACkI,WAAW,EAAZ,CAA9C;cACA;;YAEF,KAAKvF,OAAO,CAACE,KAAb;cACE,IAAI,CAACI,KAAD,IAAU8E,QAAQ,GAAGZ,QAAQ,CAACtE,KAAlC,EAAyC;cACzC+E,OAAO,CAAC/E,KAAR,CAAcwF,KAAd,CAAoBT,OAApB,EAA6B5H,kBAAkB,CAACkI,WAAW,EAAZ,CAA/C;cACA;;YAEF,KAAKvF,OAAO,CAACO,KAAb;cACE,IAAI,CAACD,KAAL,EAAY;cACZ2E,OAAO,CAAC1E,KAAR;cACA;;YAEF,KAAKP,OAAO,CAACS,cAAb;cACE,IAAI,CAACH,KAAD,IAAU8E,QAAQ,GAAGZ,QAAQ,CAACnE,GAAlC,EAAuC;;cAEvC,IAAI,CAACC,KAAD,IAAU8E,QAAQ,GAAGZ,QAAQ,CAACI,OAAlC,EAA2C;gBACzC;gBACA,IAAI,OAAOK,OAAO,CAACxE,cAAf,KAAkC,UAAtC,EAAkD;kBAChD;kBACAwE,OAAO,CAACxE,cAAR,CAAuBiF,KAAvB,CAA6BT,OAA7B,EAAsC5H,kBAAkB,CAACkI,WAAW,EAAZ,CAAxD;gBACD,CAHD,MAGO;kBACLN,OAAO,CAAC5E,GAAR,CAAYqF,KAAZ,CAAkBT,OAAlB,EAA2B5H,kBAAkB,CAACkI,WAAW,EAAZ,CAA7C;gBACD;;gBAED;cACD;;YAEH;;YAEA,KAAKvF,OAAO,CAACQ,KAAb;cACE,IAAI,CAACF,KAAD,IAAU8E,QAAQ,GAAGZ,QAAQ,CAACnE,GAAlC,EAAuC,OADzC,CACiD;;cAE/C,IAAI,OAAO4E,OAAO,CAACzE,KAAf,KAAyB,UAA7B,EAAyC;gBACvC;gBACAyE,OAAO,CAACzE,KAAR,CAAckF,KAAd,CAAoBT,OAApB,EAA6B5H,kBAAkB,CAACkI,WAAW,EAAZ,CAA/C;cACD,CAHD,MAGO;gBACLN,OAAO,CAAC5E,GAAR,CAAYqF,KAAZ,CAAkBT,OAAlB,EAA2B5H,kBAAkB,CAACkI,WAAW,EAAZ,CAA7C;cACD;;cAED;;YAEF,KAAKvF,OAAO,CAACU,QAAb;cACE,IAAI,CAACJ,KAAD,IAAU8E,QAAQ,GAAGZ,QAAQ,CAACnE,GAAlC,EAAuC,OADzC,CACiD;;cAE/C,IAAI,OAAO4E,OAAO,CAACvE,QAAf,KAA4B,UAAhC,EAA4C;gBAC1C;gBACAuE,OAAO,CAACvE,QAAR;cACD;;cAED;;YAEF,KAAKV,OAAO,CAACa,IAAb;cACE;gBACE,IAAI,CAACP,KAAD,IAAU8E,QAAQ,GAAGZ,QAAQ,CAACnE,GAAlC,EAAuC;gBACvC,IAAIsF,EAAE,GAAGnE,IAAI,CAAC,CAAD,CAAJ,GAAU,IAAV,GAAiBA,IAAI,CAAC,CAAD,CAAJ,GAAU,OAApC;gBACA,IAAIoE,GAAG,GAAG,IAAIrC,MAAJ,CAAWlF,IAAX,EAAiB,IAAjB,EAAuBkF,MAAvB,CAA8B/B,IAAI,CAAC,CAAD,CAAlC,EAAuC,IAAvC,EAA6C+B,MAA7C,CAAoDoC,EAApD,EAAwD,KAAxD,CAAV;;gBAEA,IAAI,OAAOV,OAAO,CAACY,OAAf,KAA2B,UAA/B,EAA2C;kBACzCZ,OAAO,CAACY,OAAR,CAAgBD,GAAhB;gBACD,CAFD,MAEO;kBACLX,OAAO,CAAC5E,GAAR,CAAYuF,GAAZ;gBACD;;gBAED;cACD;;YAEH,KAAK5F,OAAO,CAACW,OAAb;cACE;cACA,IAAI,OAAOsE,OAAO,CAACtE,OAAf,KAA2B,UAA/B,EAA2C;gBACzC;gBACAsE,OAAO,CAACtE,OAAR,CAAgB+E,KAAhB,CAAsBT,OAAtB,EAA+B5H,kBAAkB,CAACkI,WAAW,EAAZ,CAAjD;cACD;;cAED;;YAEF,KAAKvF,OAAO,CAACY,UAAb;cACE;cACA,IAAI,OAAOqE,OAAO,CAACrE,UAAf,KAA8B,UAAlC,EAA8C;gBAC5C;gBACAqE,OAAO,CAACrE,UAAR,CAAmB8E,KAAnB,CAAyBT,OAAzB,EAAkC5H,kBAAkB,CAACkI,WAAW,EAAZ,CAApD;cACD;;cAED;;YAEF,KAAKvF,OAAO,CAACc,KAAb;cACE,IAAI,CAACR,KAAD,IAAU8E,QAAQ,GAAGZ,QAAQ,CAACnE,GAAlC,EAAuC,OADzC,CACiD;;cAE/C,IAAI,OAAO4E,OAAO,CAACnE,KAAf,KAAyB,UAA7B,EAAyC;gBACvC;gBACAmE,OAAO,CAACnE,KAAR;cACD;;cAED;;YAEF,KAAKd,OAAO,CAACe,MAAb;cACE,IAAI,CAACT,KAAD,IAAU8E,QAAQ,GAAGZ,QAAQ,CAACpE,IAAlC,EAAwC;;cAExC,IAAI,OAAO6E,OAAO,CAAClE,MAAf,KAA0B,UAA9B,EAA0C;gBACxC,IAAIS,IAAI,CAACzC,MAAL,KAAgB,CAApB,EAAuB;kBACrBkG,OAAO,CAAClE,MAAR;gBACD,CAFD,MAEO;kBACLkE,OAAO,CAAClE,MAAR,CAAe2E,KAAf,CAAqBT,OAArB,EAA8B5H,kBAAkB,CAACkI,WAAW,EAAZ,CAAhD;gBACD;cACF,CAND,MAMO;gBACL,IAAI/D,IAAI,CAACzC,MAAL,KAAgB,CAApB,EAAuB;kBACrBkG,OAAO,CAAC7E,IAAR,CAAasF,KAAb,CAAmBT,OAAnB,EAA4B5H,kBAAkB,CAACkI,WAAW,EAAZ,CAA9C;gBACD;cACF;;cAED;;YAEF;cACE,MAAM,IAAIjC,KAAJ,CAAU,sBAAsBC,MAAtB,CAA6B+B,IAA7B,CAAV,CAAN;UA1IJ;QA4ID,CA7JD;;QA+JA,OAAOD,MAAP;MACD,CArLD;MAuLA;;IAAO,CAjqB8B;;IAmqBrC;IAAM;IACN;AACA;AACA;;IACA;IAAO,UAASjI,uBAAT,EAAkCH,OAAlC,EAA2C+G,mBAA3C,EAAgE;MAEvE;AACA;AACA;AACA;MAGA,SAAS8B,QAAT,GAAoB;QAClBA,QAAQ,GAAG9H,MAAM,CAAC+H,MAAP,GAAgB/H,MAAM,CAAC+H,MAAP,CAAcC,IAAd,EAAhB,GAAuC,UAAU3G,MAAV,EAAkB;UAClE,KAAK,IAAIV,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG4C,SAAS,CAACxC,MAA9B,EAAsCJ,CAAC,EAAvC,EAA2C;YACzC,IAAIsH,MAAM,GAAG1E,SAAS,CAAC5C,CAAD,CAAtB;;YAEA,KAAK,IAAIiB,GAAT,IAAgBqG,MAAhB,EAAwB;cACtB,IAAIjI,MAAM,CAACC,SAAP,CAAiBiI,cAAjB,CAAgC/I,IAAhC,CAAqC8I,MAArC,EAA6CrG,GAA7C,CAAJ,EAAuD;gBACrDP,MAAM,CAACO,GAAD,CAAN,GAAcqG,MAAM,CAACrG,GAAD,CAApB;cACD;YACF;UACF;;UAED,OAAOP,MAAP;QACD,CAZD;QAaA,OAAOyG,QAAQ,CAACJ,KAAT,CAAe,IAAf,EAAqBnE,SAArB,CAAP;MACD;;MAED,IAAI4E,YAAY,GAAGnC,mBAAmB;MAAC;MAAgC,iDAAjC,CAAtC;;MAEA,IAAIC,QAAQ,GAAGD,mBAAmB;MAAC;MAAgB,8CAAjB,CAAlC;MAAA,IACIF,MAAM,GAAGG,QAAQ,CAACH,MADtB;;MAGA,IAAIsC,mBAAmB,GAAGpC,mBAAmB;MAAC;MAA6B,2DAA9B,CAA7C;MACA;;;MAGA,IAAIqC,2BAA2B,GAAG;QAChCtB,KAAK,EAAE,MADyB;QAEhCzE,KAAK,EAAE,KAFyB;QAGhC2E,OAAO,EAAEA;MAHuB,CAAlC;MAKA,IAAIqB,oBAAoB,GAAGF,mBAAmB,CAACC,2BAAD,CAA9C;MACA;AACA;AACA;AACA;;MAEApJ,OAAO,CAACsJ,SAAR,GAAoB,UAAUlI,IAAV,EAAgB;QAClC,OAAO,IAAIyF,MAAJ,CAAW,UAAUwB,IAAV,EAAgB9D,IAAhB,EAAsB;UACtC,IAAIvE,OAAO,CAACuJ,KAAR,CAAcnG,GAAd,CAAkBlD,IAAlB,CAAuBkB,IAAvB,EAA6BiH,IAA7B,EAAmC9D,IAAnC,MAA6CoC,SAAjD,EAA4D;YAC1D0C,oBAAoB,CAACjI,IAAD,EAAOiH,IAAP,EAAa9D,IAAb,CAApB;UACD;QACF,CAJM,EAIJ,UAAUiF,SAAV,EAAqB;UACtB,OAAOxJ,OAAO,CAACsJ,SAAR,CAAkB,GAAGhD,MAAH,CAAUlF,IAAV,EAAgB,GAAhB,EAAqBkF,MAArB,CAA4BkD,SAA5B,CAAlB,CAAP;QACD,CANM,CAAP;MAOD,CARD;MASA;AACA;AACA;AACA;;;MAGAxJ,OAAO,CAACyJ,sBAAR,GAAiC,UAAUC,OAAV,EAAmB;QAClDb,QAAQ,CAACO,2BAAD,EAA8BM,OAA9B,CAAR;;QAEAL,oBAAoB,GAAGF,mBAAmB,CAACC,2BAAD,CAA1C;MACD,CAJD;;MAMApJ,OAAO,CAACuJ,KAAR,GAAgB;QACdnG,GAAG,EAAE,IAAI8F,YAAJ,CAAiB,CAAC,QAAD,EAAW,MAAX,EAAmB,MAAnB,CAAjB;MADS,CAAhB;MAIA;IAAO;IAEP;;EA/uBqC,CAA3B;EAgvBV;;EACA;EAAU;;EACV;;EAAU,IAAIS,wBAAwB,GAAG,EAA/B;EACV;;EACA;EAAU;;EACV;;EAAU,SAAS5C,mBAAT,CAA6B6C,QAA7B,EAAuC;IACjD;IAAW;;IACX;IAAW,IAAIC,YAAY,GAAGF,wBAAwB,CAACC,QAAD,CAA3C;IACX;;IAAW,IAAIC,YAAY,KAAKlD,SAArB,EAAgC;MAC3C;MAAY,OAAOkD,YAAY,CAAC7J,OAApB;MACZ;IAAY;IACZ;IAAW;;IACX;;;IAAW,IAAID,MAAM,GAAG4J,wBAAwB,CAACC,QAAD,CAAxB,GAAqC;MAC7D;MAAY;;MACZ;MAAY;;MACZ;MAAY5J,OAAO,EAAE;MACrB;;IAJ6D,CAAlD;IAKX;;IACA;IAAW;;IACX;;IAAWF,mBAAmB,CAAC8J,QAAD,CAAnB,CAA8B7J,MAA9B,EAAsCA,MAAM,CAACC,OAA7C,EAAsD+G,mBAAtD;IACX;;IACA;IAAW;;IACX;;;IAAW,OAAOhH,MAAM,CAACC,OAAd;IACX;EAAW;EACX;;EACA;;EACA;;EAAU;;EACV;;;EAAU,CAAC,YAAW;IACtB;IAAW;;IACX;IAAW+G,mBAAmB,CAAC+C,CAApB,GAAwB,UAAS9J,OAAT,EAAkB+J,UAAlB,EAA8B;MACjE;MAAY,KAAI,IAAIpH,GAAR,IAAeoH,UAAf,EAA2B;QACvC;QAAa,IAAGhD,mBAAmB,CAACpG,CAApB,CAAsBoJ,UAAtB,EAAkCpH,GAAlC,KAA0C,CAACoE,mBAAmB,CAACpG,CAApB,CAAsBX,OAAtB,EAA+B2C,GAA/B,CAA9C,EAAmF;UAChG;UAAc5B,MAAM,CAAC2B,cAAP,CAAsB1C,OAAtB,EAA+B2C,GAA/B,EAAoC;YAAEJ,UAAU,EAAE,IAAd;YAAoB6D,GAAG,EAAE2D,UAAU,CAACpH,GAAD;UAAnC,CAApC;UACd;QAAc;QACd;;MAAa;MACb;;IAAY,CAND;IAOX;;EAAW,CATA,EAAD;EAUV;;EACA;;EAAU;;EACV;;EAAU,CAAC,YAAW;IACtB;IAAWoE,mBAAmB,CAACpG,CAApB,GAAwB,UAASqJ,GAAT,EAAcC,IAAd,EAAoB;MAAE,OAAOlJ,MAAM,CAACC,SAAP,CAAiBiI,cAAjB,CAAgC/I,IAAhC,CAAqC8J,GAArC,EAA0CC,IAA1C,CAAP;IAAyD,CAAvG;IACX;;EAAW,CAFA,EAAD;EAGV;;EACA;;EAAU;;EACV;;EAAU,CAAC,YAAW;IACtB;IAAW;;IACX;IAAWlD,mBAAmB,CAACmD,CAApB,GAAwB,UAASlK,OAAT,EAAkB;MACrD;MAAY,IAAG,OAAOyB,MAAP,KAAkB,WAAlB,IAAiCA,MAAM,CAAC0I,WAA3C,EAAwD;QACpE;QAAapJ,MAAM,CAAC2B,cAAP,CAAsB1C,OAAtB,EAA+ByB,MAAM,CAAC0I,WAAtC,EAAmD;UAAE/F,KAAK,EAAE;QAAT,CAAnD;QACb;MAAa;MACb;;;MAAYrD,MAAM,CAAC2B,cAAP,CAAsB1C,OAAtB,EAA+B,YAA/B,EAA6C;QAAEoE,KAAK,EAAE;MAAT,CAA7C;MACZ;IAAY,CALD;IAMX;;EAAW,CARA,EAAD;EASV;;EACA;;EACA,IAAIgG,mBAAmB,GAAG,EAA1B,CAzyBqB,CA0yBrB;;EACA,CAAC,YAAW;IACZ;AACA;AACA;IACArD,mBAAmB,CAACmD,CAApB,CAAsBE,mBAAtB;IACA;;;IAAqBrD,mBAAmB,CAAC+C,CAApB,CAAsBM,mBAAtB,EAA2C;MAChE;MAAuB,WAAW,YAAW;QAAE;UAAO;UAAgDC;QAAvD;MAAqH;MACpK;;IAFgE,CAA3C;IAGrB;;;IAAqB,IAAIA,2DAA2D,GAAGtD,mBAAmB;IAAC;IAAsC,+CAAvC,CAArF;EAEpB,CAVA,EAAD;EAWA,IAAIuD,yBAAyB,GAAGtK,OAAhC;;EACA,KAAI,IAAI0B,CAAR,IAAa0I,mBAAb,EAAkCE,yBAAyB,CAAC5I,CAAD,CAAzB,GAA+B0I,mBAAmB,CAAC1I,CAAD,CAAlD;;EAClC,IAAG0I,mBAAmB,CAACG,UAAvB,EAAmCxJ,MAAM,CAAC2B,cAAP,CAAsB4H,yBAAtB,EAAiD,YAAjD,EAA+D;IAAElG,KAAK,EAAE;EAAT,CAA/D;EACnC;AAAU,CAzzBD", "ignoreList": []}, "metadata": {}, "sourceType": "script"}