{"ast": null, "code": "import { Socket as Engine, installTimerFunctions, nextTick } from \"engine.io-client\";\nimport { Socket } from \"./socket.js\";\nimport * as parser from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Backoff } from \"./contrib/backo2.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nexport class Manager extends Emitter {\n  constructor(uri, opts) {\n    var _a;\n\n    super();\n    this.nsps = {};\n    this.subs = [];\n\n    if (uri && \"object\" === typeof uri) {\n      opts = uri;\n      uri = undefined;\n    }\n\n    opts = opts || {};\n    opts.path = opts.path || \"/socket.io\";\n    this.opts = opts;\n    installTimerFunctions(this, opts);\n    this.reconnection(opts.reconnection !== false);\n    this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n    this.reconnectionDelay(opts.reconnectionDelay || 1000);\n    this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n    this.randomizationFactor((_a = opts.randomizationFactor) !== null && _a !== void 0 ? _a : 0.5);\n    this.backoff = new Backoff({\n      min: this.reconnectionDelay(),\n      max: this.reconnectionDelayMax(),\n      jitter: this.randomizationFactor()\n    });\n    this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n    this._readyState = \"closed\";\n    this.uri = uri;\n\n    const _parser = opts.parser || parser;\n\n    this.encoder = new _parser.Encoder();\n    this.decoder = new _parser.Decoder();\n    this._autoConnect = opts.autoConnect !== false;\n    if (this._autoConnect) this.open();\n  }\n\n  reconnection(v) {\n    if (!arguments.length) return this._reconnection;\n    this._reconnection = !!v;\n\n    if (!v) {\n      this.skipReconnect = true;\n    }\n\n    return this;\n  }\n\n  reconnectionAttempts(v) {\n    if (v === undefined) return this._reconnectionAttempts;\n    this._reconnectionAttempts = v;\n    return this;\n  }\n\n  reconnectionDelay(v) {\n    var _a;\n\n    if (v === undefined) return this._reconnectionDelay;\n    this._reconnectionDelay = v;\n    (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n    return this;\n  }\n\n  randomizationFactor(v) {\n    var _a;\n\n    if (v === undefined) return this._randomizationFactor;\n    this._randomizationFactor = v;\n    (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n    return this;\n  }\n\n  reconnectionDelayMax(v) {\n    var _a;\n\n    if (v === undefined) return this._reconnectionDelayMax;\n    this._reconnectionDelayMax = v;\n    (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n    return this;\n  }\n\n  timeout(v) {\n    if (!arguments.length) return this._timeout;\n    this._timeout = v;\n    return this;\n  }\n  /**\n   * Starts trying to reconnect if reconnection is enabled and we have not\n   * started reconnecting yet\n   *\n   * @private\n   */\n\n\n  maybeReconnectOnOpen() {\n    // Only try to reconnect if it's the first time we're connecting\n    if (!this._reconnecting && this._reconnection && this.backoff.attempts === 0) {\n      // keeps reconnection from firing twice for the same reconnection loop\n      this.reconnect();\n    }\n  }\n  /**\n   * Sets the current transport `socket`.\n   *\n   * @param {Function} fn - optional, callback\n   * @return self\n   * @public\n   */\n\n\n  open(fn) {\n    if (~this._readyState.indexOf(\"open\")) return this;\n    this.engine = new Engine(this.uri, this.opts);\n    const socket = this.engine;\n    const self = this;\n    this._readyState = \"opening\";\n    this.skipReconnect = false; // emit `open`\n\n    const openSubDestroy = on(socket, \"open\", function () {\n      self.onopen();\n      fn && fn();\n    });\n\n    const onError = err => {\n      this.cleanup();\n      this._readyState = \"closed\";\n      this.emitReserved(\"error\", err);\n\n      if (fn) {\n        fn(err);\n      } else {\n        // Only do this if there is no fn to handle the error\n        this.maybeReconnectOnOpen();\n      }\n    }; // emit `error`\n\n\n    const errorSub = on(socket, \"error\", onError);\n\n    if (false !== this._timeout) {\n      const timeout = this._timeout; // set timer\n\n      const timer = this.setTimeoutFn(() => {\n        openSubDestroy();\n        onError(new Error(\"timeout\"));\n        socket.close();\n      }, timeout);\n\n      if (this.opts.autoUnref) {\n        timer.unref();\n      }\n\n      this.subs.push(() => {\n        this.clearTimeoutFn(timer);\n      });\n    }\n\n    this.subs.push(openSubDestroy);\n    this.subs.push(errorSub);\n    return this;\n  }\n  /**\n   * Alias for open()\n   *\n   * @return self\n   * @public\n   */\n\n\n  connect(fn) {\n    return this.open(fn);\n  }\n  /**\n   * Called upon transport open.\n   *\n   * @private\n   */\n\n\n  onopen() {\n    // clear old subs\n    this.cleanup(); // mark as open\n\n    this._readyState = \"open\";\n    this.emitReserved(\"open\"); // add new subs\n\n    const socket = this.engine;\n    this.subs.push(on(socket, \"ping\", this.onping.bind(this)), on(socket, \"data\", this.ondata.bind(this)), on(socket, \"error\", this.onerror.bind(this)), on(socket, \"close\", this.onclose.bind(this)), // @ts-ignore\n    on(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n  }\n  /**\n   * Called upon a ping.\n   *\n   * @private\n   */\n\n\n  onping() {\n    this.emitReserved(\"ping\");\n  }\n  /**\n   * Called with data.\n   *\n   * @private\n   */\n\n\n  ondata(data) {\n    try {\n      this.decoder.add(data);\n    } catch (e) {\n      this.onclose(\"parse error\", e);\n    }\n  }\n  /**\n   * Called when parser fully decodes a packet.\n   *\n   * @private\n   */\n\n\n  ondecoded(packet) {\n    // the nextTick call prevents an exception in a user-provided event listener from triggering a disconnection due to a \"parse error\"\n    nextTick(() => {\n      this.emitReserved(\"packet\", packet);\n    }, this.setTimeoutFn);\n  }\n  /**\n   * Called upon socket error.\n   *\n   * @private\n   */\n\n\n  onerror(err) {\n    this.emitReserved(\"error\", err);\n  }\n  /**\n   * Creates a new socket for the given `nsp`.\n   *\n   * @return {Socket}\n   * @public\n   */\n\n\n  socket(nsp, opts) {\n    let socket = this.nsps[nsp];\n\n    if (!socket) {\n      socket = new Socket(this, nsp, opts);\n      this.nsps[nsp] = socket;\n    } else if (this._autoConnect && !socket.active) {\n      socket.connect();\n    }\n\n    return socket;\n  }\n  /**\n   * Called upon a socket close.\n   *\n   * @param socket\n   * @private\n   */\n\n\n  _destroy(socket) {\n    const nsps = Object.keys(this.nsps);\n\n    for (const nsp of nsps) {\n      const socket = this.nsps[nsp];\n\n      if (socket.active) {\n        return;\n      }\n    }\n\n    this._close();\n  }\n  /**\n   * Writes a packet.\n   *\n   * @param packet\n   * @private\n   */\n\n\n  _packet(packet) {\n    const encodedPackets = this.encoder.encode(packet);\n\n    for (let i = 0; i < encodedPackets.length; i++) {\n      this.engine.write(encodedPackets[i], packet.options);\n    }\n  }\n  /**\n   * Clean up transport subscriptions and packet buffer.\n   *\n   * @private\n   */\n\n\n  cleanup() {\n    this.subs.forEach(subDestroy => subDestroy());\n    this.subs.length = 0;\n    this.decoder.destroy();\n  }\n  /**\n   * Close the current socket.\n   *\n   * @private\n   */\n\n\n  _close() {\n    this.skipReconnect = true;\n    this._reconnecting = false;\n    this.onclose(\"forced close\");\n  }\n  /**\n   * Alias for close()\n   *\n   * @private\n   */\n\n\n  disconnect() {\n    return this._close();\n  }\n  /**\n   * Called when:\n   *\n   * - the low-level engine is closed\n   * - the parser encountered a badly formatted packet\n   * - all sockets are disconnected\n   *\n   * @private\n   */\n\n\n  onclose(reason, description) {\n    var _a;\n\n    this.cleanup();\n    (_a = this.engine) === null || _a === void 0 ? void 0 : _a.close();\n    this.backoff.reset();\n    this._readyState = \"closed\";\n    this.emitReserved(\"close\", reason, description);\n\n    if (this._reconnection && !this.skipReconnect) {\n      this.reconnect();\n    }\n  }\n  /**\n   * Attempt a reconnection.\n   *\n   * @private\n   */\n\n\n  reconnect() {\n    if (this._reconnecting || this.skipReconnect) return this;\n    const self = this;\n\n    if (this.backoff.attempts >= this._reconnectionAttempts) {\n      this.backoff.reset();\n      this.emitReserved(\"reconnect_failed\");\n      this._reconnecting = false;\n    } else {\n      const delay = this.backoff.duration();\n      this._reconnecting = true;\n      const timer = this.setTimeoutFn(() => {\n        if (self.skipReconnect) return;\n        this.emitReserved(\"reconnect_attempt\", self.backoff.attempts); // check again for the case socket closed in above events\n\n        if (self.skipReconnect) return;\n        self.open(err => {\n          if (err) {\n            self._reconnecting = false;\n            self.reconnect();\n            this.emitReserved(\"reconnect_error\", err);\n          } else {\n            self.onreconnect();\n          }\n        });\n      }, delay);\n\n      if (this.opts.autoUnref) {\n        timer.unref();\n      }\n\n      this.subs.push(() => {\n        this.clearTimeoutFn(timer);\n      });\n    }\n  }\n  /**\n   * Called upon successful reconnect.\n   *\n   * @private\n   */\n\n\n  onreconnect() {\n    const attempt = this.backoff.attempts;\n    this._reconnecting = false;\n    this.backoff.reset();\n    this.emitReserved(\"reconnect\", attempt);\n  }\n\n}", "map": {"version": 3, "names": ["Socket", "Engine", "installTimerFunctions", "nextTick", "parser", "on", "Backoff", "Emitter", "Manager", "constructor", "uri", "opts", "_a", "nsps", "subs", "undefined", "path", "reconnection", "reconnectionAttempts", "Infinity", "reconnectionDelay", "reconnectionDelayMax", "randomizationFactor", "backoff", "min", "max", "jitter", "timeout", "_readyState", "_parser", "encoder", "Encoder", "decoder", "Decoder", "_autoConnect", "autoConnect", "open", "v", "arguments", "length", "_reconnection", "skipReconnect", "_reconnectionAttempts", "_reconnectionDelay", "setMin", "_randomizationFactor", "setJitter", "_reconnectionDelayMax", "setMax", "_timeout", "maybeReconnectOnOpen", "_reconnecting", "attempts", "reconnect", "fn", "indexOf", "engine", "socket", "self", "openSubDestroy", "onopen", "onError", "err", "cleanup", "emit<PERSON><PERSON><PERSON><PERSON>", "errorSub", "timer", "setTimeoutFn", "Error", "close", "autoUnref", "unref", "push", "clearTimeoutFn", "connect", "onping", "bind", "ondata", "onerror", "onclose", "ondecoded", "data", "add", "e", "packet", "nsp", "active", "_destroy", "Object", "keys", "_close", "_packet", "encodedPackets", "encode", "i", "write", "options", "for<PERSON>ach", "subDestroy", "destroy", "disconnect", "reason", "description", "reset", "delay", "duration", "onreconnect", "attempt"], "sources": ["R:/chateye/FrontendAngular/node_modules/socket.io-client/build/esm/manager.js"], "sourcesContent": ["import { Socket as Engine, installTimerFunctions, nextTick, } from \"engine.io-client\";\nimport { Socket } from \"./socket.js\";\nimport * as parser from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Backoff } from \"./contrib/backo2.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\nexport class Manager extends Emitter {\n    constructor(uri, opts) {\n        var _a;\n        super();\n        this.nsps = {};\n        this.subs = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = undefined;\n        }\n        opts = opts || {};\n        opts.path = opts.path || \"/socket.io\";\n        this.opts = opts;\n        installTimerFunctions(this, opts);\n        this.reconnection(opts.reconnection !== false);\n        this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n        this.reconnectionDelay(opts.reconnectionDelay || 1000);\n        this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n        this.randomizationFactor((_a = opts.randomizationFactor) !== null && _a !== void 0 ? _a : 0.5);\n        this.backoff = new Backoff({\n            min: this.reconnectionDelay(),\n            max: this.reconnectionDelayMax(),\n            jitter: this.randomizationFactor(),\n        });\n        this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n        this._readyState = \"closed\";\n        this.uri = uri;\n        const _parser = opts.parser || parser;\n        this.encoder = new _parser.Encoder();\n        this.decoder = new _parser.Decoder();\n        this._autoConnect = opts.autoConnect !== false;\n        if (this._autoConnect)\n            this.open();\n    }\n    reconnection(v) {\n        if (!arguments.length)\n            return this._reconnection;\n        this._reconnection = !!v;\n        if (!v) {\n            this.skipReconnect = true;\n        }\n        return this;\n    }\n    reconnectionAttempts(v) {\n        if (v === undefined)\n            return this._reconnectionAttempts;\n        this._reconnectionAttempts = v;\n        return this;\n    }\n    reconnectionDelay(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelay;\n        this._reconnectionDelay = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n        return this;\n    }\n    randomizationFactor(v) {\n        var _a;\n        if (v === undefined)\n            return this._randomizationFactor;\n        this._randomizationFactor = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n        return this;\n    }\n    reconnectionDelayMax(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelayMax;\n        this._reconnectionDelayMax = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n        return this;\n    }\n    timeout(v) {\n        if (!arguments.length)\n            return this._timeout;\n        this._timeout = v;\n        return this;\n    }\n    /**\n     * Starts trying to reconnect if reconnection is enabled and we have not\n     * started reconnecting yet\n     *\n     * @private\n     */\n    maybeReconnectOnOpen() {\n        // Only try to reconnect if it's the first time we're connecting\n        if (!this._reconnecting &&\n            this._reconnection &&\n            this.backoff.attempts === 0) {\n            // keeps reconnection from firing twice for the same reconnection loop\n            this.reconnect();\n        }\n    }\n    /**\n     * Sets the current transport `socket`.\n     *\n     * @param {Function} fn - optional, callback\n     * @return self\n     * @public\n     */\n    open(fn) {\n        if (~this._readyState.indexOf(\"open\"))\n            return this;\n        this.engine = new Engine(this.uri, this.opts);\n        const socket = this.engine;\n        const self = this;\n        this._readyState = \"opening\";\n        this.skipReconnect = false;\n        // emit `open`\n        const openSubDestroy = on(socket, \"open\", function () {\n            self.onopen();\n            fn && fn();\n        });\n        const onError = (err) => {\n            this.cleanup();\n            this._readyState = \"closed\";\n            this.emitReserved(\"error\", err);\n            if (fn) {\n                fn(err);\n            }\n            else {\n                // Only do this if there is no fn to handle the error\n                this.maybeReconnectOnOpen();\n            }\n        };\n        // emit `error`\n        const errorSub = on(socket, \"error\", onError);\n        if (false !== this._timeout) {\n            const timeout = this._timeout;\n            // set timer\n            const timer = this.setTimeoutFn(() => {\n                openSubDestroy();\n                onError(new Error(\"timeout\"));\n                socket.close();\n            }, timeout);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(() => {\n                this.clearTimeoutFn(timer);\n            });\n        }\n        this.subs.push(openSubDestroy);\n        this.subs.push(errorSub);\n        return this;\n    }\n    /**\n     * Alias for open()\n     *\n     * @return self\n     * @public\n     */\n    connect(fn) {\n        return this.open(fn);\n    }\n    /**\n     * Called upon transport open.\n     *\n     * @private\n     */\n    onopen() {\n        // clear old subs\n        this.cleanup();\n        // mark as open\n        this._readyState = \"open\";\n        this.emitReserved(\"open\");\n        // add new subs\n        const socket = this.engine;\n        this.subs.push(on(socket, \"ping\", this.onping.bind(this)), on(socket, \"data\", this.ondata.bind(this)), on(socket, \"error\", this.onerror.bind(this)), on(socket, \"close\", this.onclose.bind(this)), \n        // @ts-ignore\n        on(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n    }\n    /**\n     * Called upon a ping.\n     *\n     * @private\n     */\n    onping() {\n        this.emitReserved(\"ping\");\n    }\n    /**\n     * Called with data.\n     *\n     * @private\n     */\n    ondata(data) {\n        try {\n            this.decoder.add(data);\n        }\n        catch (e) {\n            this.onclose(\"parse error\", e);\n        }\n    }\n    /**\n     * Called when parser fully decodes a packet.\n     *\n     * @private\n     */\n    ondecoded(packet) {\n        // the nextTick call prevents an exception in a user-provided event listener from triggering a disconnection due to a \"parse error\"\n        nextTick(() => {\n            this.emitReserved(\"packet\", packet);\n        }, this.setTimeoutFn);\n    }\n    /**\n     * Called upon socket error.\n     *\n     * @private\n     */\n    onerror(err) {\n        this.emitReserved(\"error\", err);\n    }\n    /**\n     * Creates a new socket for the given `nsp`.\n     *\n     * @return {Socket}\n     * @public\n     */\n    socket(nsp, opts) {\n        let socket = this.nsps[nsp];\n        if (!socket) {\n            socket = new Socket(this, nsp, opts);\n            this.nsps[nsp] = socket;\n        }\n        else if (this._autoConnect && !socket.active) {\n            socket.connect();\n        }\n        return socket;\n    }\n    /**\n     * Called upon a socket close.\n     *\n     * @param socket\n     * @private\n     */\n    _destroy(socket) {\n        const nsps = Object.keys(this.nsps);\n        for (const nsp of nsps) {\n            const socket = this.nsps[nsp];\n            if (socket.active) {\n                return;\n            }\n        }\n        this._close();\n    }\n    /**\n     * Writes a packet.\n     *\n     * @param packet\n     * @private\n     */\n    _packet(packet) {\n        const encodedPackets = this.encoder.encode(packet);\n        for (let i = 0; i < encodedPackets.length; i++) {\n            this.engine.write(encodedPackets[i], packet.options);\n        }\n    }\n    /**\n     * Clean up transport subscriptions and packet buffer.\n     *\n     * @private\n     */\n    cleanup() {\n        this.subs.forEach((subDestroy) => subDestroy());\n        this.subs.length = 0;\n        this.decoder.destroy();\n    }\n    /**\n     * Close the current socket.\n     *\n     * @private\n     */\n    _close() {\n        this.skipReconnect = true;\n        this._reconnecting = false;\n        this.onclose(\"forced close\");\n    }\n    /**\n     * Alias for close()\n     *\n     * @private\n     */\n    disconnect() {\n        return this._close();\n    }\n    /**\n     * Called when:\n     *\n     * - the low-level engine is closed\n     * - the parser encountered a badly formatted packet\n     * - all sockets are disconnected\n     *\n     * @private\n     */\n    onclose(reason, description) {\n        var _a;\n        this.cleanup();\n        (_a = this.engine) === null || _a === void 0 ? void 0 : _a.close();\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        this.emitReserved(\"close\", reason, description);\n        if (this._reconnection && !this.skipReconnect) {\n            this.reconnect();\n        }\n    }\n    /**\n     * Attempt a reconnection.\n     *\n     * @private\n     */\n    reconnect() {\n        if (this._reconnecting || this.skipReconnect)\n            return this;\n        const self = this;\n        if (this.backoff.attempts >= this._reconnectionAttempts) {\n            this.backoff.reset();\n            this.emitReserved(\"reconnect_failed\");\n            this._reconnecting = false;\n        }\n        else {\n            const delay = this.backoff.duration();\n            this._reconnecting = true;\n            const timer = this.setTimeoutFn(() => {\n                if (self.skipReconnect)\n                    return;\n                this.emitReserved(\"reconnect_attempt\", self.backoff.attempts);\n                // check again for the case socket closed in above events\n                if (self.skipReconnect)\n                    return;\n                self.open((err) => {\n                    if (err) {\n                        self._reconnecting = false;\n                        self.reconnect();\n                        this.emitReserved(\"reconnect_error\", err);\n                    }\n                    else {\n                        self.onreconnect();\n                    }\n                });\n            }, delay);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(() => {\n                this.clearTimeoutFn(timer);\n            });\n        }\n    }\n    /**\n     * Called upon successful reconnect.\n     *\n     * @private\n     */\n    onreconnect() {\n        const attempt = this.backoff.attempts;\n        this._reconnecting = false;\n        this.backoff.reset();\n        this.emitReserved(\"reconnect\", attempt);\n    }\n}\n"], "mappings": "AAAA,SAASA,MAAM,IAAIC,MAAnB,EAA2BC,qBAA3B,EAAkDC,QAAlD,QAAmE,kBAAnE;AACA,SAASH,MAAT,QAAuB,aAAvB;AACA,OAAO,KAAKI,MAAZ,MAAwB,kBAAxB;AACA,SAASC,EAAT,QAAmB,SAAnB;AACA,SAASC,OAAT,QAAwB,qBAAxB;AACA,SAASC,OAAT,QAAyB,8BAAzB;AACA,OAAO,MAAMC,OAAN,SAAsBD,OAAtB,CAA8B;EACjCE,WAAW,CAACC,GAAD,EAAMC,IAAN,EAAY;IACnB,IAAIC,EAAJ;;IACA;IACA,KAAKC,IAAL,GAAY,EAAZ;IACA,KAAKC,IAAL,GAAY,EAAZ;;IACA,IAAIJ,GAAG,IAAI,aAAa,OAAOA,GAA/B,EAAoC;MAChCC,IAAI,GAAGD,GAAP;MACAA,GAAG,GAAGK,SAAN;IACH;;IACDJ,IAAI,GAAGA,IAAI,IAAI,EAAf;IACAA,IAAI,CAACK,IAAL,GAAYL,IAAI,CAACK,IAAL,IAAa,YAAzB;IACA,KAAKL,IAAL,GAAYA,IAAZ;IACAT,qBAAqB,CAAC,IAAD,EAAOS,IAAP,CAArB;IACA,KAAKM,YAAL,CAAkBN,IAAI,CAACM,YAAL,KAAsB,KAAxC;IACA,KAAKC,oBAAL,CAA0BP,IAAI,CAACO,oBAAL,IAA6BC,QAAvD;IACA,KAAKC,iBAAL,CAAuBT,IAAI,CAACS,iBAAL,IAA0B,IAAjD;IACA,KAAKC,oBAAL,CAA0BV,IAAI,CAACU,oBAAL,IAA6B,IAAvD;IACA,KAAKC,mBAAL,CAAyB,CAACV,EAAE,GAAGD,IAAI,CAACW,mBAAX,MAAoC,IAApC,IAA4CV,EAAE,KAAK,KAAK,CAAxD,GAA4DA,EAA5D,GAAiE,GAA1F;IACA,KAAKW,OAAL,GAAe,IAAIjB,OAAJ,CAAY;MACvBkB,GAAG,EAAE,KAAKJ,iBAAL,EADkB;MAEvBK,GAAG,EAAE,KAAKJ,oBAAL,EAFkB;MAGvBK,MAAM,EAAE,KAAKJ,mBAAL;IAHe,CAAZ,CAAf;IAKA,KAAKK,OAAL,CAAa,QAAQhB,IAAI,CAACgB,OAAb,GAAuB,KAAvB,GAA+BhB,IAAI,CAACgB,OAAjD;IACA,KAAKC,WAAL,GAAmB,QAAnB;IACA,KAAKlB,GAAL,GAAWA,GAAX;;IACA,MAAMmB,OAAO,GAAGlB,IAAI,CAACP,MAAL,IAAeA,MAA/B;;IACA,KAAK0B,OAAL,GAAe,IAAID,OAAO,CAACE,OAAZ,EAAf;IACA,KAAKC,OAAL,GAAe,IAAIH,OAAO,CAACI,OAAZ,EAAf;IACA,KAAKC,YAAL,GAAoBvB,IAAI,CAACwB,WAAL,KAAqB,KAAzC;IACA,IAAI,KAAKD,YAAT,EACI,KAAKE,IAAL;EACP;;EACDnB,YAAY,CAACoB,CAAD,EAAI;IACZ,IAAI,CAACC,SAAS,CAACC,MAAf,EACI,OAAO,KAAKC,aAAZ;IACJ,KAAKA,aAAL,GAAqB,CAAC,CAACH,CAAvB;;IACA,IAAI,CAACA,CAAL,EAAQ;MACJ,KAAKI,aAAL,GAAqB,IAArB;IACH;;IACD,OAAO,IAAP;EACH;;EACDvB,oBAAoB,CAACmB,CAAD,EAAI;IACpB,IAAIA,CAAC,KAAKtB,SAAV,EACI,OAAO,KAAK2B,qBAAZ;IACJ,KAAKA,qBAAL,GAA6BL,CAA7B;IACA,OAAO,IAAP;EACH;;EACDjB,iBAAiB,CAACiB,CAAD,EAAI;IACjB,IAAIzB,EAAJ;;IACA,IAAIyB,CAAC,KAAKtB,SAAV,EACI,OAAO,KAAK4B,kBAAZ;IACJ,KAAKA,kBAAL,GAA0BN,CAA1B;IACA,CAACzB,EAAE,GAAG,KAAKW,OAAX,MAAwB,IAAxB,IAAgCX,EAAE,KAAK,KAAK,CAA5C,GAAgD,KAAK,CAArD,GAAyDA,EAAE,CAACgC,MAAH,CAAUP,CAAV,CAAzD;IACA,OAAO,IAAP;EACH;;EACDf,mBAAmB,CAACe,CAAD,EAAI;IACnB,IAAIzB,EAAJ;;IACA,IAAIyB,CAAC,KAAKtB,SAAV,EACI,OAAO,KAAK8B,oBAAZ;IACJ,KAAKA,oBAAL,GAA4BR,CAA5B;IACA,CAACzB,EAAE,GAAG,KAAKW,OAAX,MAAwB,IAAxB,IAAgCX,EAAE,KAAK,KAAK,CAA5C,GAAgD,KAAK,CAArD,GAAyDA,EAAE,CAACkC,SAAH,CAAaT,CAAb,CAAzD;IACA,OAAO,IAAP;EACH;;EACDhB,oBAAoB,CAACgB,CAAD,EAAI;IACpB,IAAIzB,EAAJ;;IACA,IAAIyB,CAAC,KAAKtB,SAAV,EACI,OAAO,KAAKgC,qBAAZ;IACJ,KAAKA,qBAAL,GAA6BV,CAA7B;IACA,CAACzB,EAAE,GAAG,KAAKW,OAAX,MAAwB,IAAxB,IAAgCX,EAAE,KAAK,KAAK,CAA5C,GAAgD,KAAK,CAArD,GAAyDA,EAAE,CAACoC,MAAH,CAAUX,CAAV,CAAzD;IACA,OAAO,IAAP;EACH;;EACDV,OAAO,CAACU,CAAD,EAAI;IACP,IAAI,CAACC,SAAS,CAACC,MAAf,EACI,OAAO,KAAKU,QAAZ;IACJ,KAAKA,QAAL,GAAgBZ,CAAhB;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIa,oBAAoB,GAAG;IACnB;IACA,IAAI,CAAC,KAAKC,aAAN,IACA,KAAKX,aADL,IAEA,KAAKjB,OAAL,CAAa6B,QAAb,KAA0B,CAF9B,EAEiC;MAC7B;MACA,KAAKC,SAAL;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACIjB,IAAI,CAACkB,EAAD,EAAK;IACL,IAAI,CAAC,KAAK1B,WAAL,CAAiB2B,OAAjB,CAAyB,MAAzB,CAAL,EACI,OAAO,IAAP;IACJ,KAAKC,MAAL,GAAc,IAAIvD,MAAJ,CAAW,KAAKS,GAAhB,EAAqB,KAAKC,IAA1B,CAAd;IACA,MAAM8C,MAAM,GAAG,KAAKD,MAApB;IACA,MAAME,IAAI,GAAG,IAAb;IACA,KAAK9B,WAAL,GAAmB,SAAnB;IACA,KAAKa,aAAL,GAAqB,KAArB,CAPK,CAQL;;IACA,MAAMkB,cAAc,GAAGtD,EAAE,CAACoD,MAAD,EAAS,MAAT,EAAiB,YAAY;MAClDC,IAAI,CAACE,MAAL;MACAN,EAAE,IAAIA,EAAE,EAAR;IACH,CAHwB,CAAzB;;IAIA,MAAMO,OAAO,GAAIC,GAAD,IAAS;MACrB,KAAKC,OAAL;MACA,KAAKnC,WAAL,GAAmB,QAAnB;MACA,KAAKoC,YAAL,CAAkB,OAAlB,EAA2BF,GAA3B;;MACA,IAAIR,EAAJ,EAAQ;QACJA,EAAE,CAACQ,GAAD,CAAF;MACH,CAFD,MAGK;QACD;QACA,KAAKZ,oBAAL;MACH;IACJ,CAXD,CAbK,CAyBL;;;IACA,MAAMe,QAAQ,GAAG5D,EAAE,CAACoD,MAAD,EAAS,OAAT,EAAkBI,OAAlB,CAAnB;;IACA,IAAI,UAAU,KAAKZ,QAAnB,EAA6B;MACzB,MAAMtB,OAAO,GAAG,KAAKsB,QAArB,CADyB,CAEzB;;MACA,MAAMiB,KAAK,GAAG,KAAKC,YAAL,CAAkB,MAAM;QAClCR,cAAc;QACdE,OAAO,CAAC,IAAIO,KAAJ,CAAU,SAAV,CAAD,CAAP;QACAX,MAAM,CAACY,KAAP;MACH,CAJa,EAIX1C,OAJW,CAAd;;MAKA,IAAI,KAAKhB,IAAL,CAAU2D,SAAd,EAAyB;QACrBJ,KAAK,CAACK,KAAN;MACH;;MACD,KAAKzD,IAAL,CAAU0D,IAAV,CAAe,MAAM;QACjB,KAAKC,cAAL,CAAoBP,KAApB;MACH,CAFD;IAGH;;IACD,KAAKpD,IAAL,CAAU0D,IAAV,CAAeb,cAAf;IACA,KAAK7C,IAAL,CAAU0D,IAAV,CAAeP,QAAf;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIS,OAAO,CAACpB,EAAD,EAAK;IACR,OAAO,KAAKlB,IAAL,CAAUkB,EAAV,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIM,MAAM,GAAG;IACL;IACA,KAAKG,OAAL,GAFK,CAGL;;IACA,KAAKnC,WAAL,GAAmB,MAAnB;IACA,KAAKoC,YAAL,CAAkB,MAAlB,EALK,CAML;;IACA,MAAMP,MAAM,GAAG,KAAKD,MAApB;IACA,KAAK1C,IAAL,CAAU0D,IAAV,CAAenE,EAAE,CAACoD,MAAD,EAAS,MAAT,EAAiB,KAAKkB,MAAL,CAAYC,IAAZ,CAAiB,IAAjB,CAAjB,CAAjB,EAA2DvE,EAAE,CAACoD,MAAD,EAAS,MAAT,EAAiB,KAAKoB,MAAL,CAAYD,IAAZ,CAAiB,IAAjB,CAAjB,CAA7D,EAAuGvE,EAAE,CAACoD,MAAD,EAAS,OAAT,EAAkB,KAAKqB,OAAL,CAAaF,IAAb,CAAkB,IAAlB,CAAlB,CAAzG,EAAqJvE,EAAE,CAACoD,MAAD,EAAS,OAAT,EAAkB,KAAKsB,OAAL,CAAaH,IAAb,CAAkB,IAAlB,CAAlB,CAAvJ,EACA;IACAvE,EAAE,CAAC,KAAK2B,OAAN,EAAe,SAAf,EAA0B,KAAKgD,SAAL,CAAeJ,IAAf,CAAoB,IAApB,CAA1B,CAFF;EAGH;EACD;AACJ;AACA;AACA;AACA;;;EACID,MAAM,GAAG;IACL,KAAKX,YAAL,CAAkB,MAAlB;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIa,MAAM,CAACI,IAAD,EAAO;IACT,IAAI;MACA,KAAKjD,OAAL,CAAakD,GAAb,CAAiBD,IAAjB;IACH,CAFD,CAGA,OAAOE,CAAP,EAAU;MACN,KAAKJ,OAAL,CAAa,aAAb,EAA4BI,CAA5B;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;;;EACIH,SAAS,CAACI,MAAD,EAAS;IACd;IACAjF,QAAQ,CAAC,MAAM;MACX,KAAK6D,YAAL,CAAkB,QAAlB,EAA4BoB,MAA5B;IACH,CAFO,EAEL,KAAKjB,YAFA,CAAR;EAGH;EACD;AACJ;AACA;AACA;AACA;;;EACIW,OAAO,CAAChB,GAAD,EAAM;IACT,KAAKE,YAAL,CAAkB,OAAlB,EAA2BF,GAA3B;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIL,MAAM,CAAC4B,GAAD,EAAM1E,IAAN,EAAY;IACd,IAAI8C,MAAM,GAAG,KAAK5C,IAAL,CAAUwE,GAAV,CAAb;;IACA,IAAI,CAAC5B,MAAL,EAAa;MACTA,MAAM,GAAG,IAAIzD,MAAJ,CAAW,IAAX,EAAiBqF,GAAjB,EAAsB1E,IAAtB,CAAT;MACA,KAAKE,IAAL,CAAUwE,GAAV,IAAiB5B,MAAjB;IACH,CAHD,MAIK,IAAI,KAAKvB,YAAL,IAAqB,CAACuB,MAAM,CAAC6B,MAAjC,EAAyC;MAC1C7B,MAAM,CAACiB,OAAP;IACH;;IACD,OAAOjB,MAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACI8B,QAAQ,CAAC9B,MAAD,EAAS;IACb,MAAM5C,IAAI,GAAG2E,MAAM,CAACC,IAAP,CAAY,KAAK5E,IAAjB,CAAb;;IACA,KAAK,MAAMwE,GAAX,IAAkBxE,IAAlB,EAAwB;MACpB,MAAM4C,MAAM,GAAG,KAAK5C,IAAL,CAAUwE,GAAV,CAAf;;MACA,IAAI5B,MAAM,CAAC6B,MAAX,EAAmB;QACf;MACH;IACJ;;IACD,KAAKI,MAAL;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIC,OAAO,CAACP,MAAD,EAAS;IACZ,MAAMQ,cAAc,GAAG,KAAK9D,OAAL,CAAa+D,MAAb,CAAoBT,MAApB,CAAvB;;IACA,KAAK,IAAIU,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,cAAc,CAACrD,MAAnC,EAA2CuD,CAAC,EAA5C,EAAgD;MAC5C,KAAKtC,MAAL,CAAYuC,KAAZ,CAAkBH,cAAc,CAACE,CAAD,CAAhC,EAAqCV,MAAM,CAACY,OAA5C;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;;;EACIjC,OAAO,GAAG;IACN,KAAKjD,IAAL,CAAUmF,OAAV,CAAmBC,UAAD,IAAgBA,UAAU,EAA5C;IACA,KAAKpF,IAAL,CAAUyB,MAAV,GAAmB,CAAnB;IACA,KAAKP,OAAL,CAAamE,OAAb;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIT,MAAM,GAAG;IACL,KAAKjD,aAAL,GAAqB,IAArB;IACA,KAAKU,aAAL,GAAqB,KAArB;IACA,KAAK4B,OAAL,CAAa,cAAb;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIqB,UAAU,GAAG;IACT,OAAO,KAAKV,MAAL,EAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIX,OAAO,CAACsB,MAAD,EAASC,WAAT,EAAsB;IACzB,IAAI1F,EAAJ;;IACA,KAAKmD,OAAL;IACA,CAACnD,EAAE,GAAG,KAAK4C,MAAX,MAAuB,IAAvB,IAA+B5C,EAAE,KAAK,KAAK,CAA3C,GAA+C,KAAK,CAApD,GAAwDA,EAAE,CAACyD,KAAH,EAAxD;IACA,KAAK9C,OAAL,CAAagF,KAAb;IACA,KAAK3E,WAAL,GAAmB,QAAnB;IACA,KAAKoC,YAAL,CAAkB,OAAlB,EAA2BqC,MAA3B,EAAmCC,WAAnC;;IACA,IAAI,KAAK9D,aAAL,IAAsB,CAAC,KAAKC,aAAhC,EAA+C;MAC3C,KAAKY,SAAL;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;;;EACIA,SAAS,GAAG;IACR,IAAI,KAAKF,aAAL,IAAsB,KAAKV,aAA/B,EACI,OAAO,IAAP;IACJ,MAAMiB,IAAI,GAAG,IAAb;;IACA,IAAI,KAAKnC,OAAL,CAAa6B,QAAb,IAAyB,KAAKV,qBAAlC,EAAyD;MACrD,KAAKnB,OAAL,CAAagF,KAAb;MACA,KAAKvC,YAAL,CAAkB,kBAAlB;MACA,KAAKb,aAAL,GAAqB,KAArB;IACH,CAJD,MAKK;MACD,MAAMqD,KAAK,GAAG,KAAKjF,OAAL,CAAakF,QAAb,EAAd;MACA,KAAKtD,aAAL,GAAqB,IAArB;MACA,MAAMe,KAAK,GAAG,KAAKC,YAAL,CAAkB,MAAM;QAClC,IAAIT,IAAI,CAACjB,aAAT,EACI;QACJ,KAAKuB,YAAL,CAAkB,mBAAlB,EAAuCN,IAAI,CAACnC,OAAL,CAAa6B,QAApD,EAHkC,CAIlC;;QACA,IAAIM,IAAI,CAACjB,aAAT,EACI;QACJiB,IAAI,CAACtB,IAAL,CAAW0B,GAAD,IAAS;UACf,IAAIA,GAAJ,EAAS;YACLJ,IAAI,CAACP,aAAL,GAAqB,KAArB;YACAO,IAAI,CAACL,SAAL;YACA,KAAKW,YAAL,CAAkB,iBAAlB,EAAqCF,GAArC;UACH,CAJD,MAKK;YACDJ,IAAI,CAACgD,WAAL;UACH;QACJ,CATD;MAUH,CAjBa,EAiBXF,KAjBW,CAAd;;MAkBA,IAAI,KAAK7F,IAAL,CAAU2D,SAAd,EAAyB;QACrBJ,KAAK,CAACK,KAAN;MACH;;MACD,KAAKzD,IAAL,CAAU0D,IAAV,CAAe,MAAM;QACjB,KAAKC,cAAL,CAAoBP,KAApB;MACH,CAFD;IAGH;EACJ;EACD;AACJ;AACA;AACA;AACA;;;EACIwC,WAAW,GAAG;IACV,MAAMC,OAAO,GAAG,KAAKpF,OAAL,CAAa6B,QAA7B;IACA,KAAKD,aAAL,GAAqB,KAArB;IACA,KAAK5B,OAAL,CAAagF,KAAb;IACA,KAAKvC,YAAL,CAAkB,WAAlB,EAA+B2C,OAA/B;EACH;;AAvWgC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}