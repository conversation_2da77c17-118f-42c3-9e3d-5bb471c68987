{"ast": null, "code": "import _asyncToGenerator from \"R:/chateye/FrontendAngular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/snack-bar\";\nexport class NotificationService {\n  constructor(snackBar) {\n    this.snackBar = snackBar;\n    this.permission = 'default';\n    this.requestPermission();\n  }\n\n  requestPermission() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      if ('Notification' in window) {\n        _this.permission = yield Notification.requestPermission();\n      }\n    })();\n  }\n\n  showNotification(title, options = {}) {\n    if (this.permission === 'granted' && document.hidden) {\n      const notification = new Notification(title, {\n        icon: '/favicon.ico',\n        badge: '/favicon.ico',\n        tag: 'chateye-message',\n        ...options\n      }); // Auto close after 5 seconds\n\n      setTimeout(() => notification.close(), 5000); // Focus window when clicked\n\n      notification.onclick = () => {\n        window.focus();\n        notification.close();\n      };\n\n      return notification;\n    }\n\n    return null;\n  }\n\n  showMessageNotification(username, message) {\n    return this.showNotification(`New message from ${username}`, {\n      body: message.length > 50 ? message.substring(0, 50) + '...' : message,\n      tag: 'chateye-message'\n    });\n  }\n\n  showSnackBar(message, action = 'Close', duration = 3000) {\n    return this.snackBar.open(message, action, {\n      duration,\n      horizontalPosition: 'right',\n      verticalPosition: 'top',\n      panelClass: ['snackbar-notification']\n    });\n  }\n\n  showSuccessSnackBar(message, duration = 3000) {\n    return this.snackBar.open(message, 'Close', {\n      duration,\n      horizontalPosition: 'right',\n      verticalPosition: 'top',\n      panelClass: ['snackbar-success']\n    });\n  }\n\n  showErrorSnackBar(message, duration = 5000) {\n    return this.snackBar.open(message, 'Close', {\n      duration,\n      horizontalPosition: 'right',\n      verticalPosition: 'top',\n      panelClass: ['snackbar-error']\n    });\n  }\n\n}\n\nNotificationService.ɵfac = function NotificationService_Factory(t) {\n  return new (t || NotificationService)(i0.ɵɵinject(i1.MatSnackBar));\n};\n\nNotificationService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: NotificationService,\n  factory: NotificationService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "mappings": ";;;AAMA,OAAM,MAAOA,mBAAP,CAA0B;EAG9BC,YAAoBC,QAApB,EAAyC;IAArB;IAFZ,kBAAqC,SAArC;IAGN,KAAKC,iBAAL;EACD;;EAEKA,iBAAiB;IAAA;;IAAA;MACrB,IAAI,kBAAkBC,MAAtB,EAA8B;QAC5B,KAAI,CAACC,UAAL,SAAwBC,YAAY,CAACH,iBAAb,EAAxB;MACD;IAHoB;EAItB;;EAEDI,gBAAgB,CAACC,KAAD,EAAgBC,UAA+B,EAA/C,EAAiD;IAC/D,IAAI,KAAKJ,UAAL,KAAoB,SAApB,IAAiCK,QAAQ,CAACC,MAA9C,EAAsD;MACpD,MAAMC,YAAY,GAAG,IAAIN,YAAJ,CAAiBE,KAAjB,EAAwB;QAC3CK,IAAI,EAAE,cADqC;QAE3CC,KAAK,EAAE,cAFoC;QAG3CC,GAAG,EAAE,iBAHsC;QAI3C,GAAGN;MAJwC,CAAxB,CAArB,CADoD,CAQpD;;MACAO,UAAU,CAAC,MAAMJ,YAAY,CAACK,KAAb,EAAP,EAA6B,IAA7B,CAAV,CAToD,CAWpD;;MACAL,YAAY,CAACM,OAAb,GAAuB,MAAK;QAC1Bd,MAAM,CAACe,KAAP;QACAP,YAAY,CAACK,KAAb;MACD,CAHD;;MAKA,OAAOL,YAAP;IACD;;IACD,OAAO,IAAP;EACD;;EAEDQ,uBAAuB,CAACC,QAAD,EAAmBC,OAAnB,EAAkC;IACvD,OAAO,KAAKf,gBAAL,CAAsB,oBAAoBc,QAAQ,EAAlD,EAAsD;MAC3DE,IAAI,EAAED,OAAO,CAACE,MAAR,GAAiB,EAAjB,GAAsBF,OAAO,CAACG,SAAR,CAAkB,CAAlB,EAAqB,EAArB,IAA2B,KAAjD,GAAyDH,OADJ;MAE3DP,GAAG,EAAE;IAFsD,CAAtD,CAAP;EAID;;EAEDW,YAAY,CAACJ,OAAD,EAAkBK,SAAiB,OAAnC,EAA4CC,WAAmB,IAA/D,EAAmE;IAC7E,OAAO,KAAK1B,QAAL,CAAc2B,IAAd,CAAmBP,OAAnB,EAA4BK,MAA5B,EAAoC;MACzCC,QADyC;MAEzCE,kBAAkB,EAAE,OAFqB;MAGzCC,gBAAgB,EAAE,KAHuB;MAIzCC,UAAU,EAAE,CAAC,uBAAD;IAJ6B,CAApC,CAAP;EAMD;;EAEDC,mBAAmB,CAACX,OAAD,EAAkBM,WAAmB,IAArC,EAAyC;IAC1D,OAAO,KAAK1B,QAAL,CAAc2B,IAAd,CAAmBP,OAAnB,EAA4B,OAA5B,EAAqC;MAC1CM,QAD0C;MAE1CE,kBAAkB,EAAE,OAFsB;MAG1CC,gBAAgB,EAAE,KAHwB;MAI1CC,UAAU,EAAE,CAAC,kBAAD;IAJ8B,CAArC,CAAP;EAMD;;EAEDE,iBAAiB,CAACZ,OAAD,EAAkBM,WAAmB,IAArC,EAAyC;IACxD,OAAO,KAAK1B,QAAL,CAAc2B,IAAd,CAAmBP,OAAnB,EAA4B,OAA5B,EAAqC;MAC1CM,QAD0C;MAE1CE,kBAAkB,EAAE,OAFsB;MAG1CC,gBAAgB,EAAE,KAHwB;MAI1CC,UAAU,EAAE,CAAC,gBAAD;IAJ8B,CAArC,CAAP;EAMD;;AApE6B;;;mBAAnBhC,qBAAmBmC;AAAA;;;SAAnBnC;EAAmBoC,SAAnBpC,mBAAmB;EAAAqC,YAFlB", "names": ["NotificationService", "constructor", "snackBar", "requestPermission", "window", "permission", "Notification", "showNotification", "title", "options", "document", "hidden", "notification", "icon", "badge", "tag", "setTimeout", "close", "onclick", "focus", "showMessageNotification", "username", "message", "body", "length", "substring", "showSnackBar", "action", "duration", "open", "horizontalPosition", "verticalPosition", "panelClass", "showSuccessSnackBar", "showErrorSnackBar", "i0", "factory", "providedIn"], "sourceRoot": "", "sources": ["R:\\chateye\\FrontendAngular\\src\\app\\services\\notification.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { MatSnackBar, MatSnackBarRef, SimpleSnackBar } from '@angular/material/snack-bar';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class NotificationService {\r\n  private permission: NotificationPermission = 'default';\r\n\r\n  constructor(private snackBar: MatSnackBar) {\r\n    this.requestPermission();\r\n  }\r\n\r\n  async requestPermission(): Promise<void> {\r\n    if ('Notification' in window) {\r\n      this.permission = await Notification.requestPermission();\r\n    }\r\n  }\r\n\r\n  showNotification(title: string, options: NotificationOptions = {}): Notification | null {\r\n    if (this.permission === 'granted' && document.hidden) {\r\n      const notification = new Notification(title, {\r\n        icon: '/favicon.ico',\r\n        badge: '/favicon.ico',\r\n        tag: 'chateye-message',\r\n        ...options\r\n      });\r\n\r\n      // Auto close after 5 seconds\r\n      setTimeout(() => notification.close(), 5000);\r\n\r\n      // Focus window when clicked\r\n      notification.onclick = () => {\r\n        window.focus();\r\n        notification.close();\r\n      };\r\n\r\n      return notification;\r\n    }\r\n    return null;\r\n  }\r\n\r\n  showMessageNotification(username: string, message: string): Notification | null {\r\n    return this.showNotification(`New message from ${username}`, {\r\n      body: message.length > 50 ? message.substring(0, 50) + '...' : message,\r\n      tag: 'chateye-message'\r\n    });\r\n  }\r\n\r\n  showSnackBar(message: string, action: string = 'Close', duration: number = 3000): MatSnackBarRef<SimpleSnackBar> {\r\n    return this.snackBar.open(message, action, {\r\n      duration,\r\n      horizontalPosition: 'right',\r\n      verticalPosition: 'top',\r\n      panelClass: ['snackbar-notification']\r\n    });\r\n  }\r\n\r\n  showSuccessSnackBar(message: string, duration: number = 3000): MatSnackBarRef<SimpleSnackBar> {\r\n    return this.snackBar.open(message, 'Close', {\r\n      duration,\r\n      horizontalPosition: 'right',\r\n      verticalPosition: 'top',\r\n      panelClass: ['snackbar-success']\r\n    });\r\n  }\r\n\r\n  showErrorSnackBar(message: string, duration: number = 5000): MatSnackBarRef<SimpleSnackBar> {\r\n    return this.snackBar.open(message, 'Close', {\r\n      duration,\r\n      horizontalPosition: 'right',\r\n      verticalPosition: 'top',\r\n      panelClass: ['snackbar-error']\r\n    });\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module"}