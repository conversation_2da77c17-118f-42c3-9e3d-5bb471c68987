import { Injectable } from '@angular/core';
import { io, Socket } from 'socket.io-client';
import { environment } from '../../environments/environment';
import { BehaviorSubject, Observable, Subject } from 'rxjs';

export interface ConnectionStatus {
  connected: boolean;
  reconnecting: boolean;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class SocketService {
  private socket: Socket | null = null;
  public isConnectedSubject = new BehaviorSubject<boolean>(false);
  public isConnected$ = this.isConnectedSubject.asObservable();
  public connectionStatusSubject = new BehaviorSubject<ConnectionStatus>({ connected: false, reconnecting: false });
  public connectionStatus$ = this.connectionStatusSubject.asObservable();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 3;
  private reconnectDelay = 2000;
  private heartbeatInterval: any = null;
  private isConnecting = false;
  private lastConnectionAttempt = 0;
  private connectionCooldown = 5000; // 5 seconds between connection attempts

  constructor() {}

  connect(username: string, password?: string, inviteCode: string | null = null): Socket {
    // Prevent multiple simultaneous connections
    if (this.isConnecting) {
      console.warn('Connection already in progress, skipping...');
      return this.socket!;
    }

    // Prevent rapid reconnection attempts
    const now = Date.now();
    if (now - this.lastConnectionAttempt < this.connectionCooldown) {
      console.warn('Connection attempt too soon, waiting for cooldown...');
      return this.socket!;
    }
    this.lastConnectionAttempt = now;

    // Always disconnect first to prevent multiple connections
    this.disconnect();
    this.isConnecting = true;

    try {
      this.socket = io(environment.backendUrl, {
        transports: ['websocket', 'polling'],
        timeout: 30000,
        reconnection: true,
        reconnectionAttempts: 3,
        reconnectionDelay: 2000,
        reconnectionDelayMax: 10000,
        randomizationFactor: 0.5,
        forceNew: true,
        auth: {
          username,
          password,
          inviteCode
        }
      });
    } catch (error) {
      console.error('❌ Failed to create socket connection:', error);
      this.isConnecting = false;
      this.updateConnectionStatus(false, false, `Failed to create connection: ${error}`);
      throw error;
    }

    this.setupSocketEventHandlers(username, inviteCode);
    this.isConnecting = false;
    return this.socket;
  }

  private setupSocketEventHandlers(username: string, inviteCode: string | null): void {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      this.reconnectAttempts = 0;
      this.isConnecting = false;
      this.updateConnectionStatus(true, false);
      this.socket?.emit('join', { username, inviteCode });

      // Start heartbeat
      this.startHeartbeat();
    });

    this.socket.on('disconnect', (reason) => {
      this.updateConnectionStatus(false, false, `Disconnected: ${reason}`);
    });

    this.socket.on('reconnect', (attemptNumber) => {
      this.reconnectAttempts = 0;
      this.updateConnectionStatus(true, false);
    });

    this.socket.on('reconnect_attempt', (attemptNumber) => {
      if (attemptNumber <= this.maxReconnectAttempts) {
        this.updateConnectionStatus(false, true, `Reconnecting... (${attemptNumber}/${this.maxReconnectAttempts})`);
      }
    });

    this.socket.on('reconnect_error', (error) => {
      console.error('❌ Reconnection error:', error);
      // Don't update status as reconnecting if we've exceeded max attempts
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        this.updateConnectionStatus(false, true, `Reconnection failed: ${error.message || 'Unknown error'}`);
      }
    });

    this.socket.on('reconnect_failed', () => {
      console.error('❌ Reconnection failed after maximum attempts');
      this.updateConnectionStatus(false, false, 'Connection failed after maximum retry attempts');
    });

    this.socket.on('connect_error', (error) => {
      console.error('❌ Socket connection error:', error);
      this.reconnectAttempts++;
      this.updateConnectionStatus(false, this.reconnectAttempts < this.maxReconnectAttempts,
        `Connection error: ${error.message || 'Unknown error'}`);
    });

    this.socket.on('error', (error) => {
      console.error('❌ Socket error:', error);
      this.updateConnectionStatus(false, false, `Socket error: ${error.message || error}`);
    });
  }

  private updateConnectionStatus(connected: boolean, reconnecting: boolean, error?: string): void {
    this.isConnectedSubject.next(connected);
    this.connectionStatusSubject.next({ connected, reconnecting, error });
  }

  disconnect(): void {
    if (this.socket) {
      try {
        this.stopHeartbeat();
        this.socket.removeAllListeners();
        this.socket.disconnect();
      } catch (error) {
        console.error('Error disconnecting socket:', error);
      } finally {
        this.socket = null;
        this.reconnectAttempts = 0;
        this.isConnecting = false;
        this.updateConnectionStatus(false, false);
      }
    }
  }

  reconnect(username: string, password?: string, inviteCode: string | null = null): void {
    // Check if we should attempt reconnection
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.warn('Maximum reconnection attempts reached, not attempting reconnection');
      return;
    }

    this.disconnect();
    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts), 10000);

    setTimeout(() => {
      if (!this.isConnected()) {
        this.connect(username, password, inviteCode);
      }
    }, delay);
  }

  getConnectionStatus(): ConnectionStatus {
    return this.connectionStatusSubject.value;
  }

  isConnected(): boolean {
    return this.isConnectedSubject.value && this.socket?.connected === true;
  }

  joinGroup(groupId: string): void {
    if (this.isConnected()) {
      this.socket!.emit('joinGroup', { groupId });
    } else {
      console.error('❌ Cannot join group - socket not connected');
    }
  }

  sendMessage(text: string, groupId: string, replyTo: string | null = null): void {
    if (this.isConnected()) {
      this.socket!.emit('sendMessage', { text, groupId, replyTo });
    } else {
      console.error('❌ Cannot send message - socket not connected');
      throw new Error('Socket not connected');
    }
  }

  addReaction(messageId: string, emoji: string): void {
    if (this.isConnected()) {
      this.socket!.emit('addReaction', { messageId, emoji });
    } else {
      console.error('❌ Cannot add reaction - socket not connected');
    }
  }

  removeReaction(data: { messageId: string; emoji?: string }): void {
    if (this.isConnected()) {
      this.socket!.emit('removeReaction', data);
    } else {
      console.error('❌ Cannot remove reaction - socket not connected');
    }
  }

  on(event: string, callback: (...args: any[]) => void): void {
    if (this.socket) {
      this.socket.on(event, callback);
    }
  }

  off(event: string, callback?: (...args: any[]) => void): void {
    if (this.socket) {
      this.socket.off(event, callback);
    }
  }

  emit(event: string, data?: any): void {
    if (this.isConnected()) {
      this.socket!.emit(event, data);
    } else {
      console.error(`❌ Cannot emit event ${event} - socket not connected`);
    }
  }

  // Utility method to emit with acknowledgment
  emitWithAck(event: string, data?: any): Promise<any> {
    return new Promise((resolve, reject) => {
      if (this.isConnected()) {
        this.socket!.emit(event, data, (response: any) => {
          if (response.error) {
            reject(new Error(response.error));
          } else {
            resolve(response);
          }
        });
      } else {
        reject(new Error('Socket not connected'));
      }
    });
  }

  // Method to check if socket is in a healthy state
  isHealthy(): boolean {
    return this.socket !== null &&
           this.socket.connected === true &&
           this.isConnectedSubject.value === true;
  }

  // Typing indicator methods
  sendTypingIndicator(groupId: string, isTyping: boolean): void {
    if (this.isConnected()) {
      this.socket!.emit('typing', { groupId, isTyping });
    }
  }

  // Heartbeat methods for presence tracking
  private startHeartbeat(): void {
    this.stopHeartbeat(); // Clear any existing heartbeat

    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected()) {
        this.socket!.emit('heartbeat');
      }
    }, 60000); // Send heartbeat every 60 seconds


  }

  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;

    }
  }

  // Status update methods
  updateUserStatus(status: string): void {
    if (this.isConnected()) {
      this.socket!.emit('updateStatus', { status });
    }
  }

  // Read receipt methods
  markAsRead(messageId: string): void {
    if (this.isConnected()) {
      this.socket!.emit('markAsRead', { messageId });
    }
  }

  markMessagesAsRead(messageIds: string[]): void {
    if (this.isConnected() && messageIds.length > 0) {
      this.socket!.emit('markMessagesAsRead', { messageIds });
    }
  }

  // Message edit and delete methods
  updateMessage(messageId: string, newText: string): void {
    if (this.isConnected()) {
      this.socket!.emit('messageUpdated', { messageId, newText });
    } else {
      console.error('❌ Cannot update message - socket not connected');
      throw new Error('Socket not connected');
    }
  }

  deleteMessage(messageId: string): void {
    if (this.isConnected()) {
      this.socket!.emit('messageDeleted', { messageId });
    } else {
      console.error('❌ Cannot delete message - socket not connected');
      throw new Error('Socket not connected');
    }
  }
}
