{"ast": null, "code": "\"use strict\";\n/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author <PERSON> @sokra\n*/\n\nmodule.exports = function (cssWithMappingToString) {\n  var list = []; // return the list of modules as css string\n\n  list.toString = function toString() {\n    return this.map(function (item) {\n      var content = \"\";\n      var needLayer = typeof item[5] !== \"undefined\";\n\n      if (item[4]) {\n        content += \"@supports (\".concat(item[4], \") {\");\n      }\n\n      if (item[2]) {\n        content += \"@media \".concat(item[2], \" {\");\n      }\n\n      if (needLayer) {\n        content += \"@layer\".concat(item[5].length > 0 ? \" \".concat(item[5]) : \"\", \" {\");\n      }\n\n      content += cssWithMappingToString(item);\n\n      if (needLayer) {\n        content += \"}\";\n      }\n\n      if (item[2]) {\n        content += \"}\";\n      }\n\n      if (item[4]) {\n        content += \"}\";\n      }\n\n      return content;\n    }).join(\"\");\n  }; // import a list of modules into the list\n\n\n  list.i = function i(modules, media, dedupe, supports, layer) {\n    if (typeof modules === \"string\") {\n      modules = [[null, modules, undefined]];\n    }\n\n    var alreadyImportedModules = {};\n\n    if (dedupe) {\n      for (var k = 0; k < this.length; k++) {\n        var id = this[k][0];\n\n        if (id != null) {\n          alreadyImportedModules[id] = true;\n        }\n      }\n    }\n\n    for (var _k = 0; _k < modules.length; _k++) {\n      var item = [].concat(modules[_k]);\n\n      if (dedupe && alreadyImportedModules[item[0]]) {\n        continue;\n      }\n\n      if (typeof layer !== \"undefined\") {\n        if (typeof item[5] === \"undefined\") {\n          item[5] = layer;\n        } else {\n          item[1] = \"@layer\".concat(item[5].length > 0 ? \" \".concat(item[5]) : \"\", \" {\").concat(item[1], \"}\");\n          item[5] = layer;\n        }\n      }\n\n      if (media) {\n        if (!item[2]) {\n          item[2] = media;\n        } else {\n          item[1] = \"@media \".concat(item[2], \" {\").concat(item[1], \"}\");\n          item[2] = media;\n        }\n      }\n\n      if (supports) {\n        if (!item[4]) {\n          item[4] = \"\".concat(supports);\n        } else {\n          item[1] = \"@supports (\".concat(item[4], \") {\").concat(item[1], \"}\");\n          item[4] = supports;\n        }\n      }\n\n      list.push(item);\n    }\n  };\n\n  return list;\n};", "map": {"version": 3, "names": ["module", "exports", "cssWithMappingToString", "list", "toString", "map", "item", "content", "<PERSON><PERSON><PERSON>er", "concat", "length", "join", "i", "modules", "media", "dedupe", "supports", "layer", "undefined", "alreadyImportedModules", "k", "id", "_k", "push"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/css-loader/dist/runtime/api.js"], "sourcesContent": ["\"use strict\";\n\n/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author <PERSON> @sokra\n*/\nmodule.exports = function (cssWithMappingToString) {\n  var list = []; // return the list of modules as css string\n\n  list.toString = function toString() {\n    return this.map(function (item) {\n      var content = \"\";\n      var needLayer = typeof item[5] !== \"undefined\";\n\n      if (item[4]) {\n        content += \"@supports (\".concat(item[4], \") {\");\n      }\n\n      if (item[2]) {\n        content += \"@media \".concat(item[2], \" {\");\n      }\n\n      if (needLayer) {\n        content += \"@layer\".concat(item[5].length > 0 ? \" \".concat(item[5]) : \"\", \" {\");\n      }\n\n      content += cssWithMappingToString(item);\n\n      if (needLayer) {\n        content += \"}\";\n      }\n\n      if (item[2]) {\n        content += \"}\";\n      }\n\n      if (item[4]) {\n        content += \"}\";\n      }\n\n      return content;\n    }).join(\"\");\n  }; // import a list of modules into the list\n\n\n  list.i = function i(modules, media, dedupe, supports, layer) {\n    if (typeof modules === \"string\") {\n      modules = [[null, modules, undefined]];\n    }\n\n    var alreadyImportedModules = {};\n\n    if (dedupe) {\n      for (var k = 0; k < this.length; k++) {\n        var id = this[k][0];\n\n        if (id != null) {\n          alreadyImportedModules[id] = true;\n        }\n      }\n    }\n\n    for (var _k = 0; _k < modules.length; _k++) {\n      var item = [].concat(modules[_k]);\n\n      if (dedupe && alreadyImportedModules[item[0]]) {\n        continue;\n      }\n\n      if (typeof layer !== \"undefined\") {\n        if (typeof item[5] === \"undefined\") {\n          item[5] = layer;\n        } else {\n          item[1] = \"@layer\".concat(item[5].length > 0 ? \" \".concat(item[5]) : \"\", \" {\").concat(item[1], \"}\");\n          item[5] = layer;\n        }\n      }\n\n      if (media) {\n        if (!item[2]) {\n          item[2] = media;\n        } else {\n          item[1] = \"@media \".concat(item[2], \" {\").concat(item[1], \"}\");\n          item[2] = media;\n        }\n      }\n\n      if (supports) {\n        if (!item[4]) {\n          item[4] = \"\".concat(supports);\n        } else {\n          item[1] = \"@supports (\".concat(item[4], \") {\").concat(item[1], \"}\");\n          item[4] = supports;\n        }\n      }\n\n      list.push(item);\n    }\n  };\n\n  return list;\n};"], "mappings": "AAAA;AAEA;AACA;AACA;AACA;;AACAA,MAAM,CAACC,OAAP,GAAiB,UAAUC,sBAAV,EAAkC;EACjD,IAAIC,IAAI,GAAG,EAAX,CADiD,CAClC;;EAEfA,IAAI,CAACC,QAAL,GAAgB,SAASA,QAAT,GAAoB;IAClC,OAAO,KAAKC,GAAL,CAAS,UAAUC,IAAV,EAAgB;MAC9B,IAAIC,OAAO,GAAG,EAAd;MACA,IAAIC,SAAS,GAAG,OAAOF,IAAI,CAAC,CAAD,CAAX,KAAmB,WAAnC;;MAEA,IAAIA,IAAI,CAAC,CAAD,CAAR,EAAa;QACXC,OAAO,IAAI,cAAcE,MAAd,CAAqBH,IAAI,CAAC,CAAD,CAAzB,EAA8B,KAA9B,CAAX;MACD;;MAED,IAAIA,IAAI,CAAC,CAAD,CAAR,EAAa;QACXC,OAAO,IAAI,UAAUE,MAAV,CAAiBH,IAAI,CAAC,CAAD,CAArB,EAA0B,IAA1B,CAAX;MACD;;MAED,IAAIE,SAAJ,EAAe;QACbD,OAAO,IAAI,SAASE,MAAT,CAAgBH,IAAI,CAAC,CAAD,CAAJ,CAAQI,MAAR,GAAiB,CAAjB,GAAqB,IAAID,MAAJ,CAAWH,IAAI,CAAC,CAAD,CAAf,CAArB,GAA2C,EAA3D,EAA+D,IAA/D,CAAX;MACD;;MAEDC,OAAO,IAAIL,sBAAsB,CAACI,IAAD,CAAjC;;MAEA,IAAIE,SAAJ,EAAe;QACbD,OAAO,IAAI,GAAX;MACD;;MAED,IAAID,IAAI,CAAC,CAAD,CAAR,EAAa;QACXC,OAAO,IAAI,GAAX;MACD;;MAED,IAAID,IAAI,CAAC,CAAD,CAAR,EAAa;QACXC,OAAO,IAAI,GAAX;MACD;;MAED,OAAOA,OAAP;IACD,CA/BM,EA+BJI,IA/BI,CA+BC,EA/BD,CAAP;EAgCD,CAjCD,CAHiD,CAoC9C;;;EAGHR,IAAI,CAACS,CAAL,GAAS,SAASA,CAAT,CAAWC,OAAX,EAAoBC,KAApB,EAA2BC,MAA3B,EAAmCC,QAAnC,EAA6CC,KAA7C,EAAoD;IAC3D,IAAI,OAAOJ,OAAP,KAAmB,QAAvB,EAAiC;MAC/BA,OAAO,GAAG,CAAC,CAAC,IAAD,EAAOA,OAAP,EAAgBK,SAAhB,CAAD,CAAV;IACD;;IAED,IAAIC,sBAAsB,GAAG,EAA7B;;IAEA,IAAIJ,MAAJ,EAAY;MACV,KAAK,IAAIK,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKV,MAAzB,EAAiCU,CAAC,EAAlC,EAAsC;QACpC,IAAIC,EAAE,GAAG,KAAKD,CAAL,EAAQ,CAAR,CAAT;;QAEA,IAAIC,EAAE,IAAI,IAAV,EAAgB;UACdF,sBAAsB,CAACE,EAAD,CAAtB,GAA6B,IAA7B;QACD;MACF;IACF;;IAED,KAAK,IAAIC,EAAE,GAAG,CAAd,EAAiBA,EAAE,GAAGT,OAAO,CAACH,MAA9B,EAAsCY,EAAE,EAAxC,EAA4C;MAC1C,IAAIhB,IAAI,GAAG,GAAGG,MAAH,CAAUI,OAAO,CAACS,EAAD,CAAjB,CAAX;;MAEA,IAAIP,MAAM,IAAII,sBAAsB,CAACb,IAAI,CAAC,CAAD,CAAL,CAApC,EAA+C;QAC7C;MACD;;MAED,IAAI,OAAOW,KAAP,KAAiB,WAArB,EAAkC;QAChC,IAAI,OAAOX,IAAI,CAAC,CAAD,CAAX,KAAmB,WAAvB,EAAoC;UAClCA,IAAI,CAAC,CAAD,CAAJ,GAAUW,KAAV;QACD,CAFD,MAEO;UACLX,IAAI,CAAC,CAAD,CAAJ,GAAU,SAASG,MAAT,CAAgBH,IAAI,CAAC,CAAD,CAAJ,CAAQI,MAAR,GAAiB,CAAjB,GAAqB,IAAID,MAAJ,CAAWH,IAAI,CAAC,CAAD,CAAf,CAArB,GAA2C,EAA3D,EAA+D,IAA/D,EAAqEG,MAArE,CAA4EH,IAAI,CAAC,CAAD,CAAhF,EAAqF,GAArF,CAAV;UACAA,IAAI,CAAC,CAAD,CAAJ,GAAUW,KAAV;QACD;MACF;;MAED,IAAIH,KAAJ,EAAW;QACT,IAAI,CAACR,IAAI,CAAC,CAAD,CAAT,EAAc;UACZA,IAAI,CAAC,CAAD,CAAJ,GAAUQ,KAAV;QACD,CAFD,MAEO;UACLR,IAAI,CAAC,CAAD,CAAJ,GAAU,UAAUG,MAAV,CAAiBH,IAAI,CAAC,CAAD,CAArB,EAA0B,IAA1B,EAAgCG,MAAhC,CAAuCH,IAAI,CAAC,CAAD,CAA3C,EAAgD,GAAhD,CAAV;UACAA,IAAI,CAAC,CAAD,CAAJ,GAAUQ,KAAV;QACD;MACF;;MAED,IAAIE,QAAJ,EAAc;QACZ,IAAI,CAACV,IAAI,CAAC,CAAD,CAAT,EAAc;UACZA,IAAI,CAAC,CAAD,CAAJ,GAAU,GAAGG,MAAH,CAAUO,QAAV,CAAV;QACD,CAFD,MAEO;UACLV,IAAI,CAAC,CAAD,CAAJ,GAAU,cAAcG,MAAd,CAAqBH,IAAI,CAAC,CAAD,CAAzB,EAA8B,KAA9B,EAAqCG,MAArC,CAA4CH,IAAI,CAAC,CAAD,CAAhD,EAAqD,GAArD,CAAV;UACAA,IAAI,CAAC,CAAD,CAAJ,GAAUU,QAAV;QACD;MACF;;MAEDb,IAAI,CAACoB,IAAL,CAAUjB,IAAV;IACD;EACF,CArDD;;EAuDA,OAAOH,IAAP;AACD,CA/FD", "ignoreList": []}, "metadata": {}, "sourceType": "script"}