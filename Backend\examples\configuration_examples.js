// Example usage of the new database-based configuration system

const Configuration = require('../models/Configuration');

async function configurationExamples() {
  try {
    console.log('=== Configuration System Examples ===\n');

    // 1. Get current security configuration
    console.log('1. Getting current security configuration:');
    const securityConfig = await Configuration.getSecurityConfig();
    console.log(securityConfig);
    console.log('');

    // 2. Check if a user is admin
    console.log('2. Checking admin status:');
    const isAdmin = await Configuration.isUserAdmin('admin');
    console.log(`Is 'admin' an admin? ${isAdmin}`);
    console.log('');

    // 3. Get a specific configuration value
    console.log('3. Getting specific configuration:');
    const maxMessageLength = await Configuration.get('MAX_MESSAGE_LENGTH', '1000');
    console.log(`Max message length: ${maxMessageLength}`);
    console.log('');

    // 4. Update a configuration value
    console.log('4. Updating configuration:');
    const updatedConfig = await Configuration.set(
      'MAX_MESSAGE_LENGTH', 
      '2000', 
      'Increased max message length to 2000 characters',
      'example-user'
    );
    console.log('Updated configuration:', updatedConfig);
    console.log('');

    // 5. Get all configuration
    console.log('5. Getting all configuration:');
    const allConfig = await Configuration.getAll();
    console.log('All configuration settings:');
    allConfig.forEach(config => {
      console.log(`  ${config.key}: ${config.value} (updated by: ${config.updated_by})`);
    });
    console.log('');

    // 6. Update security configuration
    console.log('6. Updating security configuration:');
    const newSecurityConfig = await Configuration.updateSecurityConfig(
      'invite',
      ['admin', 'moderator1', 'moderator2'],
      'example-user'
    );
    console.log('New security configuration:', newSecurityConfig);
    console.log('');

    console.log('=== Examples completed successfully ===');

  } catch (error) {
    console.error('Error running configuration examples:', error);
  }
}

// Run examples if this file is executed directly
if (require.main === module) {
  configurationExamples().then(() => {
    process.exit(0);
  }).catch(error => {
    console.error('Failed to run examples:', error);
    process.exit(1);
  });
}

module.exports = configurationExamples;
