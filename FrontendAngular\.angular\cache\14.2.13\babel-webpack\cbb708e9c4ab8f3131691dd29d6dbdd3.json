{"ast": null, "code": "export const performanceTimestampProvider = {\n  now() {\n    return (performanceTimestampProvider.delegate || performance).now();\n  },\n\n  delegate: undefined\n};", "map": {"version": 3, "names": ["performanceTimestampProvider", "now", "delegate", "performance", "undefined"], "sources": ["R:/chateye/FrontendAngular/node_modules/rxjs/dist/esm/internal/scheduler/performanceTimestampProvider.js"], "sourcesContent": ["export const performanceTimestampProvider = {\n    now() {\n        return (performanceTimestampProvider.delegate || performance).now();\n    },\n    delegate: undefined,\n};\n"], "mappings": "AAAA,OAAO,MAAMA,4BAA4B,GAAG;EACxCC,GAAG,GAAG;IACF,OAAO,CAACD,4BAA4B,CAACE,QAA7B,IAAyCC,WAA1C,EAAuDF,GAAvD,EAAP;EACH,CAHuC;;EAIxCC,QAAQ,EAAEE;AAJ8B,CAArC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}