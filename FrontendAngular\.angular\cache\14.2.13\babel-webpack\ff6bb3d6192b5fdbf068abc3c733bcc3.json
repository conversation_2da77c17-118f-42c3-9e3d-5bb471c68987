{"ast": null, "code": "import { EventEmitter, ElementRef } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nconst _c0 = [\"pickerRef\"];\n\nfunction EmojiPickerComponent_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function EmojiPickerComponent_button_9_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r5);\n      const category_r3 = restoredCtx.$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.setActiveCategory(category_r3.key));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const category_r3 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.activeCategory === category_r3.key);\n    i0.ɵɵproperty(\"title\", category_r3.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", category_r3.icon, \" \");\n  }\n}\n\nfunction EmojiPickerComponent_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function EmojiPickerComponent_button_11_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r8);\n      const emoji_r6 = restoredCtx.$implicit;\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onEmojiClick({\n        native: emoji_r6\n      }));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const emoji_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"title\", emoji_r6);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", emoji_r6, \" \");\n  }\n}\n\nexport let EmojiPickerComponent = /*#__PURE__*/(() => {\n  class EmojiPickerComponent {\n    constructor() {\n      this.onEmojiSelect = new EventEmitter();\n      this.onClose = new EventEmitter();\n      this.activeCategory = 'people'; // Emoji categories\n\n      this.emojiCategories = {\n        people: ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣', '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓'],\n        nature: ['🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🐨', '🐯', '🦁', '🐮', '🐷', '🐸', '🐵', '🙈', '🙉', '🙊', '🐒', '🦍', '🦧', '🐕', '🐩', '🦮', '🐕‍🦺', '🐈', '🐓', '🦃', '🦚', '🦜', '🦢', '🦩', '🐦', '🐧', '🕊️', '🦅', '🦆', '🦉', '🦇', '🐺', '🐗', '🐴', '🦄', '🐝', '🐛', '🦋', '🐌', '🐞', '🐜', '🦟', '🦗', '🕷️', '🕸️', '🦂', '🐢', '🐍', '🦎', '🦖', '🦕', '🐙', '🦑', '🦐', '🦞', '🦀', '🐡', '🐠', '🐟', '🐬', '🐳', '🐋', '🦈', '🐊', '🐅', '🐆', '🦓', '🦍', '🐘', '🦛', '🦏', '🐪', '🐫', '🦒', '🦘', '🐃', '🐂', '🐄', '🐎', '🐖', '🐏', '🐑', '🦙', '🐐', '🦌', '🐕', '🐩', '🐈', '🐓', '🦃', '🦚', '🦜', '🦢', '🦩', '🐦', '🐧', '🕊️', '🦅', '🦆', '🦉', '🦇'],\n        food: ['🍎', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓', '🫐', '🍈', '🍒', '🍑', '🥭', '🍍', '🥥', '🥝', '🍅', '🍆', '🥑', '🥦', '🥬', '🥒', '🌶️', '🫑', '🌽', '🥕', '🫒', '🧄', '🧅', '🥔', '🍠', '🥐', '🥖', '🍞', '🥨', '🥯', '🧀', '🥚', '🍳', '🧈', '🥞', '🧇', '🥓', '🥩', '🍗', '🍖', '🦴', '🌭', '🍔', '🍟', '🍕', '🫓', '🥙', '🌮', '🌯', '🫔', '🥗', '🥘', '🫕', '🥫', '🍝', '🍜', '🍲', '🍛', '🍣', '🍱', '🥟', '🦪', '🍤', '🍙', '🍚', '🍘', '🍥', '🥠', '🥮', '🍢', '🍡', '🍧', '🍨', '🍦', '🥧', '🧁', '🍰', '🎂', '🍮', '🍭', '🍬', '🍫', '🍿', '🍩', '🍪', '🌰', '🥜', '🍯'],\n        activity: ['⚽', '🏀', '🏈', '⚾', '🥎', '🎾', '🏐', '🏉', '🎱', '🪀', '🏓', '🏸', '🏒', '🏑', '🥍', '🏏', '🪃', '🥅', '⛳', '🪁', '🏹', '🎣', '🤿', '🥊', '🥋', '🎽', '🛹', '🛷', '⛸️', '🥌', '🎿', '⛷️', '🏂', '🪂', '🏋️‍♀️', '🏋️‍♂️', '🤼‍♀️', '🤼‍♂️', '🤸‍♀️', '🤸‍♂️', '⛹️‍♀️', '⛹️‍♂️', '🤺', '🤾‍♀️', '🤾‍♂️', '🏌️‍♀️', '🏌️‍♂️', '🏇', '🧘‍♀️', '🧘‍♂️', '🏄‍♀️', '🏄‍♂️', '🏊‍♀️', '🏊‍♂️', '🤽‍♀️', '🤽‍♂️', '🚣‍♀️', '🚣‍♂️', '🧗‍♀️', '🧗‍♂️', '🚵‍♀️', '🚵‍♂️', '🚴‍♀️', '🚴‍♂️', '🏆', '🥇', '🥈', '🥉', '🏅', '🎖', '🏵', '🎗', '🎫', '🎟', '🎪', '🤹', '🤹‍♀️', '🤹‍♂️', '🎭', '🩰', '🎨', '🎬', '🎤', '🎧', '🎼', '🎵', '🎶', '🪘', '🥁', '🎷', '🎺', '🎸', '🪕', '🎻', '🎲', '♠️', '♥️', '♦️', '♣️', '♟️', '🃏', '🀄', '🎴', '🎯', '🎳', '🎮', '🕹️', '🎰', '🧩'],\n        objects: ['⌚', '📱', '📲', '💻', '⌨️', '🖥️', '🖨️', '🖱️', '🖲️', '🕹️', '🗜️', '💽', '💾', '💿', '📀', '📼', '📷', '📸', '📹', '🎥', '📽️', '🎞️', '📞', '☎️', '📟', '📠', '📺', '📻', '🎙️', '🎚️', '🎛️', '🧭', '⏱️', '⏲️', '⏰', '🕰️', '⌛', '⏳', '📡', '🔋', '🔌', '💡', '🔦', '🕯️', '🪔', '🧯', '🛢️', '💸', '💵', '💴', '💶', '💷', '💰', '💳', '💎', '⚖️', '🧰', '🔧', '🔨', '⚒️', '🛠️', '⛏️', '🪚', '🔩', '⚙️', '🪤', '🧱', '⛓️', '🧲', '🔫', '💣', '🧨', '🪓', '🔪', '🗡️', '⚔️', '🛡️', '🚬', '⚰️', '🪦', '⚱️', '🏺', '🔮', '📿', '🧿', '💈', '⚗️', '🔭', '🔬', '🕳️', '🩹', '🩺', '💊', '💉', '🧬', '🦠', '🧫', '🧪', '🌡️', '🧹', '🧺', '🧻', '🚽', '🚰', '🚿', '🛁', '🛀', '🧴', '🧷', '🧸', '🧵', '🧶', '🪡', '🪢', '🪣', '🪤', '🪥', '🪦', '🪧'],\n        symbols: ['❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔', '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '☮️', '✝️', '☪️', '🕉️', '☸️', '✡️', '🔯', '🕎', '☯️', '☦️', '🛐', '⛎', '♈', '♉', '♊', '♋', '♌', '♍', '♎', '♏', '♐', '♑', '♒', '♓', '🆔', '⚛️', '🉑', '☢️', '☣️', '📴', '📳', '🈶', '🈚', '🈸', '🈺', '🈷️', '✴️', '🆚', '💮', '🉐', '㊙️', '㊗️', '🈴', '🈵', '🈹', '🈲', '🅰️', '🅱️', '🆎', '🆑', '🅾️', '🆘', '❌', '⭕', '🛑', '⛔', '📛', '🚫', '💯', '💢', '♨️', '🚷', '🚯', '🚳', '🚱', '🔞', '📵', '🚭', '❗', '❕', '❓', '❔', '‼️', '⁉️', '🔅', '🔆', '〽️', '⚠️', '🚸', '🔱', '⚜️', '🔰', '♻️', '✅', '🈯', '💹', '❇️', '✳️', '❎', '🌐', '💠', 'Ⓜ️', '🌀', '💤', '🏧', '🚾', '♿', '🅿️', '🈳', '🈂️', '🛂', '🛃', '🛄', '🛅', '🚹', '🚺', '🚼', '🚻', '🚮', '🎦', '📶', '🈁', '🔣', '🔤', '🔠', '🔡', '🔢', '🔟']\n      };\n    }\n\n    ngAfterViewInit() {\n      // Set up click outside listener with delay to prevent immediate closing\n      setTimeout(() => {\n        this.clickListener = event => {\n          if (this.pickerRef && !this.pickerRef.nativeElement.contains(event.target)) {\n            this.onClose.emit();\n          }\n        };\n\n        document.addEventListener('mousedown', this.clickListener);\n      }, 100);\n    }\n\n    ngOnDestroy() {\n      if (this.clickListener) {\n        document.removeEventListener('mousedown', this.clickListener);\n      }\n    }\n\n    onEmojiClick(emoji) {\n      this.onEmojiSelect.emit(emoji);\n    }\n\n    setActiveCategory(category) {\n      this.activeCategory = category;\n    }\n\n    getCategories() {\n      return [{\n        key: 'people',\n        name: 'People',\n        icon: '😀'\n      }, {\n        key: 'nature',\n        name: 'Nature',\n        icon: '🐶'\n      }, {\n        key: 'food',\n        name: 'Food',\n        icon: '🍎'\n      }, {\n        key: 'activity',\n        name: 'Activity',\n        icon: '⚽'\n      }, {\n        key: 'objects',\n        name: 'Objects',\n        icon: '⌚'\n      }, {\n        key: 'symbols',\n        name: 'Symbols',\n        icon: '❤️'\n      }];\n    }\n\n    getCurrentEmojis() {\n      return this.emojiCategories[this.activeCategory] || [];\n    }\n\n  }\n\n  EmojiPickerComponent.ɵfac = function EmojiPickerComponent_Factory(t) {\n    return new (t || EmojiPickerComponent)();\n  };\n\n  EmojiPickerComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: EmojiPickerComponent,\n    selectors: [[\"app-emoji-picker\"]],\n    viewQuery: function EmojiPickerComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.pickerRef = _t.first);\n      }\n    },\n    outputs: {\n      onEmojiSelect: \"onEmojiSelect\",\n      onClose: \"onClose\"\n    },\n    decls: 12,\n    vars: 2,\n    consts: [[1, \"fluid-emoji-picker-container\", 3, \"click\"], [\"pickerRef\", \"\"], [1, \"emoji-picker-header\"], [1, \"picker-title\"], [\"title\", \"Close\", 1, \"close-button\", 3, \"click\"], [\"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"stroke\", \"currentColor\", 1, \"close-icon\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M6 18L18 6M6 6l12 12\"], [1, \"emoji-categories\"], [\"class\", \"category-button\", 3, \"active\", \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"emoji-grid\"], [\"class\", \"emoji-button\", 3, \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"category-button\", 3, \"title\", \"click\"], [1, \"emoji-button\", 3, \"title\", \"click\"]],\n    template: function EmojiPickerComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0, 1);\n        i0.ɵɵlistener(\"click\", function EmojiPickerComponent_Template_div_click_0_listener($event) {\n          return $event.stopPropagation();\n        });\n        i0.ɵɵelementStart(2, \"div\", 2)(3, \"h3\", 3);\n        i0.ɵɵtext(4, \"Choose an emoji\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"button\", 4);\n        i0.ɵɵlistener(\"click\", function EmojiPickerComponent_Template_button_click_5_listener() {\n          return ctx.onClose.emit();\n        });\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(6, \"svg\", 5);\n        i0.ɵɵelement(7, \"path\", 6);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵnamespaceHTML();\n        i0.ɵɵelementStart(8, \"div\", 7);\n        i0.ɵɵtemplate(9, EmojiPickerComponent_button_9_Template, 2, 4, \"button\", 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"div\", 9);\n        i0.ɵɵtemplate(11, EmojiPickerComponent_button_11_Template, 2, 2, \"button\", 10);\n        i0.ɵɵelementEnd()();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"ngForOf\", ctx.getCategories());\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.getCurrentEmojis());\n      }\n    },\n    dependencies: [i1.NgForOf],\n    styles: [\".fluid-emoji-picker-container[_ngcontent-%COMP%]{background:rgba(255,255,255,.95);backdrop-filter:blur(20px);border:1px solid rgba(102,126,234,.2);border-radius:20px;box-shadow:0 20px 40px #00000026;padding:1.5rem;max-width:320px;width:100%;z-index:50;animation:fluidEmojiSlideIn .3s cubic-bezier(.4,0,.2,1)}.emoji-picker-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:1rem;padding-bottom:.75rem;border-bottom:1px solid rgba(102,126,234,.1)}.picker-title[_ngcontent-%COMP%]{font-size:1rem;font-weight:700;color:#1e293b;margin:0;background:linear-gradient(135deg,#667eea,#764ba2);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text}.close-button[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:2rem;height:2rem;background:rgba(102,126,234,.1);border:none;border-radius:50%;cursor:pointer;color:#64748b;transition:all .3s cubic-bezier(.4,0,.2,1)}.close-button[_ngcontent-%COMP%]:hover{background:rgba(102,126,234,.2);color:#667eea;transform:scale(1.1)}.close-icon[_ngcontent-%COMP%]{width:1rem;height:1rem}.emoji-categories[_ngcontent-%COMP%]{display:flex;gap:.5rem;margin-bottom:1rem;padding:.5rem;background:rgba(102,126,234,.05);border-radius:15px;overflow-x:auto;scrollbar-width:none;-ms-overflow-style:none}.emoji-categories[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}.category-button[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:2.5rem;height:2.5rem;background:transparent;border:2px solid transparent;border-radius:12px;cursor:pointer;font-size:1.25rem;transition:all .3s cubic-bezier(.4,0,.2,1);flex-shrink:0}.category-button[_ngcontent-%COMP%]:hover{background:rgba(102,126,234,.1);transform:scale(1.05)}.category-button.active[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);border-color:#667eea;color:#fff;box-shadow:0 4px 15px #667eea4d}.emoji-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(8,1fr);gap:.5rem;max-height:200px;overflow-y:auto;padding:.5rem;scrollbar-width:thin;scrollbar-color:rgba(102,126,234,.3) transparent}.emoji-grid[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px}.emoji-grid[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:transparent}.emoji-grid[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:linear-gradient(135deg,#667eea,#764ba2);border-radius:3px}.emoji-grid[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:linear-gradient(135deg,#5a67d8,#6b46c1)}.emoji-button[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:2.5rem;height:2.5rem;background:transparent;border:none;border-radius:10px;cursor:pointer;font-size:1.25rem;transition:all .3s cubic-bezier(.4,0,.2,1);position:relative;overflow:hidden}.emoji-button[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:0;background:linear-gradient(135deg,#667eea,#764ba2);opacity:0;transition:opacity .3s ease;border-radius:10px}.emoji-button[_ngcontent-%COMP%]:hover{transform:scale(1.15);box-shadow:0 4px 15px #667eea4d}.emoji-button[_ngcontent-%COMP%]:hover:before{opacity:.1}.emoji-button[_ngcontent-%COMP%]:active{transform:scale(1.05)}@keyframes fluidEmojiSlideIn{0%{opacity:0;transform:translateY(-20px) scale(.95)}to{opacity:1;transform:translateY(0) scale(1)}}@media (max-width: 768px){.fluid-emoji-picker-container[_ngcontent-%COMP%]{max-width:280px;padding:1.25rem}.emoji-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(7,1fr);gap:.375rem}.emoji-button[_ngcontent-%COMP%], .category-button[_ngcontent-%COMP%]{width:2.25rem;height:2.25rem;font-size:1.125rem}}@media (max-width: 480px){.fluid-emoji-picker-container[_ngcontent-%COMP%]{max-width:260px;padding:1rem}.emoji-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(6,1fr);gap:.25rem;max-height:180px}.emoji-button[_ngcontent-%COMP%], .category-button[_ngcontent-%COMP%]{width:2rem;height:2rem;font-size:1rem}.picker-title[_ngcontent-%COMP%]{font-size:.9rem}}*[_ngcontent-%COMP%]{transition:all .3s cubic-bezier(.4,0,.2,1)}\"]\n  });\n  return EmojiPickerComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}