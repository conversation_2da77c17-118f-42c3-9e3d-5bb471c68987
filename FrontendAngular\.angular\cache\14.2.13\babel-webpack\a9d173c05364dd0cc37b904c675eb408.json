{"ast": null, "code": "import { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\nimport { EMPTY } from './empty';\nexport function using(resourceFactory, observableFactory) {\n  return new Observable(subscriber => {\n    const resource = resourceFactory();\n    const result = observableFactory(resource);\n    const source = result ? innerFrom(result) : EMPTY;\n    source.subscribe(subscriber);\n    return () => {\n      if (resource) {\n        resource.unsubscribe();\n      }\n    };\n  });\n}", "map": {"version": 3, "names": ["Observable", "innerFrom", "EMPTY", "using", "resourceFactory", "observableFactory", "subscriber", "resource", "result", "source", "subscribe", "unsubscribe"], "sources": ["R:/chateye/FrontendAngular/node_modules/rxjs/dist/esm/internal/observable/using.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\nimport { EMPTY } from './empty';\nexport function using(resourceFactory, observableFactory) {\n    return new Observable((subscriber) => {\n        const resource = resourceFactory();\n        const result = observableFactory(resource);\n        const source = result ? innerFrom(result) : EMPTY;\n        source.subscribe(subscriber);\n        return () => {\n            if (resource) {\n                resource.unsubscribe();\n            }\n        };\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,SAAT,QAA0B,aAA1B;AACA,SAASC,KAAT,QAAsB,SAAtB;AACA,OAAO,SAASC,KAAT,CAAeC,eAAf,EAAgCC,iBAAhC,EAAmD;EACtD,OAAO,IAAIL,UAAJ,CAAgBM,UAAD,IAAgB;IAClC,MAAMC,QAAQ,GAAGH,eAAe,EAAhC;IACA,MAAMI,MAAM,GAAGH,iBAAiB,CAACE,QAAD,CAAhC;IACA,MAAME,MAAM,GAAGD,MAAM,GAAGP,SAAS,CAACO,MAAD,CAAZ,GAAuBN,KAA5C;IACAO,MAAM,CAACC,SAAP,CAAiBJ,UAAjB;IACA,OAAO,MAAM;MACT,IAAIC,QAAJ,EAAc;QACVA,QAAQ,CAACI,WAAT;MACH;IACJ,CAJD;EAKH,CAVM,CAAP;AAWH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}