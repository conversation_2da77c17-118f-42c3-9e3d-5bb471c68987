{"ast": null, "code": "import * as i1$1 from '@angular/cdk/overlay';\nimport { OverlayModule, OverlayConfig } from '@angular/cdk/overlay';\nimport * as i3$1 from '@angular/cdk/portal';\nimport { BasePortalOutlet, CdkPortalOutlet, PortalModule, ComponentPortal, TemplatePortal } from '@angular/cdk/portal';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Component, ViewEncapsulation, ChangeDetectionStrategy, Inject, Directive, ViewChild, NgModule, Injector, TemplateRef, Injectable, Optional, SkipSelf } from '@angular/core';\nimport { MatCommonModule } from '@angular/material/core';\nimport * as i3 from '@angular/material/button';\nimport { MatButtonModule } from '@angular/material/button';\nimport { Subject } from 'rxjs';\nimport * as i1 from '@angular/cdk/platform';\nimport { take, takeUntil } from 'rxjs/operators';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i2$1 from '@angular/cdk/a11y';\nimport * as i3$2 from '@angular/cdk/layout';\nimport { Breakpoints } from '@angular/cdk/layout';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Injection token that can be used to access the data that was passed in to a snack bar. */\n\nfunction SimpleSnackBar_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"button\", 3);\n    i0.ɵɵlistener(\"click\", function SimpleSnackBar_div_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.action());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.data.action);\n  }\n}\n\nfunction MatSnackBarContainer_ng_template_1_Template(rf, ctx) {}\n\nconst MAT_SNACK_BAR_DATA = /*#__PURE__*/new InjectionToken('MatSnackBarData');\n/**\n * Configuration used when opening a snack-bar.\n */\n\nclass MatSnackBarConfig {\n  constructor() {\n    /** The politeness level for the MatAriaLiveAnnouncer announcement. */\n    this.politeness = 'assertive';\n    /**\n     * Message to be announced by the LiveAnnouncer. When opening a snackbar without a custom\n     * component or template, the announcement message will default to the specified message.\n     */\n\n    this.announcementMessage = '';\n    /** The length of time in milliseconds to wait before automatically dismissing the snack bar. */\n\n    this.duration = 0;\n    /** Data being injected into the child component. */\n\n    this.data = null;\n    /** The horizontal position to place the snack bar. */\n\n    this.horizontalPosition = 'center';\n    /** The vertical position to place the snack bar. */\n\n    this.verticalPosition = 'bottom';\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Maximum amount of milliseconds that can be passed into setTimeout. */\n\n\nconst MAX_TIMEOUT = /*#__PURE__*/Math.pow(2, 31) - 1;\n/**\n * Reference to a snack bar dispatched from the snack bar service.\n */\n\nclass MatSnackBarRef {\n  constructor(containerInstance, _overlayRef) {\n    this._overlayRef = _overlayRef;\n    /** Subject for notifying the user that the snack bar has been dismissed. */\n\n    this._afterDismissed = new Subject();\n    /** Subject for notifying the user that the snack bar has opened and appeared. */\n\n    this._afterOpened = new Subject();\n    /** Subject for notifying the user that the snack bar action was called. */\n\n    this._onAction = new Subject();\n    /** Whether the snack bar was dismissed using the action button. */\n\n    this._dismissedByAction = false;\n    this.containerInstance = containerInstance;\n\n    containerInstance._onExit.subscribe(() => this._finishDismiss());\n  }\n  /** Dismisses the snack bar. */\n\n\n  dismiss() {\n    if (!this._afterDismissed.closed) {\n      this.containerInstance.exit();\n    }\n\n    clearTimeout(this._durationTimeoutId);\n  }\n  /** Marks the snackbar action clicked. */\n\n\n  dismissWithAction() {\n    if (!this._onAction.closed) {\n      this._dismissedByAction = true;\n\n      this._onAction.next();\n\n      this._onAction.complete();\n\n      this.dismiss();\n    }\n\n    clearTimeout(this._durationTimeoutId);\n  }\n  /**\n   * Marks the snackbar action clicked.\n   * @deprecated Use `dismissWithAction` instead.\n   * @breaking-change 8.0.0\n   */\n\n\n  closeWithAction() {\n    this.dismissWithAction();\n  }\n  /** Dismisses the snack bar after some duration */\n\n\n  _dismissAfter(duration) {\n    // Note that we need to cap the duration to the maximum value for setTimeout, because\n    // it'll revert to 1 if somebody passes in something greater (e.g. `Infinity`). See #17234.\n    this._durationTimeoutId = setTimeout(() => this.dismiss(), Math.min(duration, MAX_TIMEOUT));\n  }\n  /** Marks the snackbar as opened */\n\n\n  _open() {\n    if (!this._afterOpened.closed) {\n      this._afterOpened.next();\n\n      this._afterOpened.complete();\n    }\n  }\n  /** Cleans up the DOM after closing. */\n\n\n  _finishDismiss() {\n    this._overlayRef.dispose();\n\n    if (!this._onAction.closed) {\n      this._onAction.complete();\n    }\n\n    this._afterDismissed.next({\n      dismissedByAction: this._dismissedByAction\n    });\n\n    this._afterDismissed.complete();\n\n    this._dismissedByAction = false;\n  }\n  /** Gets an observable that is notified when the snack bar is finished closing. */\n\n\n  afterDismissed() {\n    return this._afterDismissed;\n  }\n  /** Gets an observable that is notified when the snack bar has opened and appeared. */\n\n\n  afterOpened() {\n    return this.containerInstance._onEnter;\n  }\n  /** Gets an observable that is notified when the snack bar action is called. */\n\n\n  onAction() {\n    return this._onAction;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * A component used to open as the default snack bar, matching material spec.\n * This should only be used internally by the snack bar service.\n */\n\n\nlet SimpleSnackBar = /*#__PURE__*/(() => {\n  class SimpleSnackBar {\n    constructor(snackBarRef, data) {\n      this.snackBarRef = snackBarRef;\n      this.data = data;\n    }\n    /** Performs the action on the snack bar. */\n\n\n    action() {\n      this.snackBarRef.dismissWithAction();\n    }\n    /** If the action button should be shown. */\n\n\n    get hasAction() {\n      return !!this.data.action;\n    }\n\n  }\n\n  SimpleSnackBar.ɵfac = function SimpleSnackBar_Factory(t) {\n    return new (t || SimpleSnackBar)(i0.ɵɵdirectiveInject(MatSnackBarRef), i0.ɵɵdirectiveInject(MAT_SNACK_BAR_DATA));\n  };\n\n  SimpleSnackBar.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: SimpleSnackBar,\n    selectors: [[\"simple-snack-bar\"]],\n    hostAttrs: [1, \"mat-simple-snackbar\"],\n    decls: 3,\n    vars: 2,\n    consts: [[1, \"mat-simple-snack-bar-content\"], [\"class\", \"mat-simple-snackbar-action\", 4, \"ngIf\"], [1, \"mat-simple-snackbar-action\"], [\"mat-button\", \"\", 3, \"click\"]],\n    template: function SimpleSnackBar_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"span\", 0);\n        i0.ɵɵtext(1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(2, SimpleSnackBar_div_2_Template, 3, 1, \"div\", 1);\n      }\n\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate(ctx.data.message);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasAction);\n      }\n    },\n    dependencies: [i2.NgIf, i3.MatButton],\n    styles: [\".mat-simple-snackbar{display:flex;justify-content:space-between;align-items:center;line-height:20px;opacity:1}.mat-simple-snackbar-action{flex-shrink:0;margin:-8px -8px -8px 8px}.mat-simple-snackbar-action button{max-height:36px;min-width:0}[dir=rtl] .mat-simple-snackbar-action{margin-left:-8px;margin-right:8px}.mat-simple-snack-bar-content{overflow:hidden;text-overflow:ellipsis}\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return SimpleSnackBar;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Animations used by the Material snack bar.\n * @docs-private\n */\n\n\nconst matSnackBarAnimations = {\n  /** Animation that shows and hides a snack bar. */\n  snackBarState: /*#__PURE__*/trigger('state', [/*#__PURE__*/state('void, hidden', /*#__PURE__*/style({\n    transform: 'scale(0.8)',\n    opacity: 0\n  })), /*#__PURE__*/state('visible', /*#__PURE__*/style({\n    transform: 'scale(1)',\n    opacity: 1\n  })), /*#__PURE__*/transition('* => visible', /*#__PURE__*/animate('150ms cubic-bezier(0, 0, 0.2, 1)')), /*#__PURE__*/transition('* => void, * => hidden', /*#__PURE__*/animate('75ms cubic-bezier(0.4, 0.0, 1, 1)', /*#__PURE__*/style({\n    opacity: 0\n  })))])\n};\n/**\n * Base class for snack bar containers.\n * @docs-private\n */\n\nlet _MatSnackBarContainerBase = /*#__PURE__*/(() => {\n  class _MatSnackBarContainerBase extends BasePortalOutlet {\n    constructor(_ngZone, _elementRef, _changeDetectorRef, _platform,\n    /** The snack bar configuration. */\n    snackBarConfig) {\n      super();\n      this._ngZone = _ngZone;\n      this._elementRef = _elementRef;\n      this._changeDetectorRef = _changeDetectorRef;\n      this._platform = _platform;\n      this.snackBarConfig = snackBarConfig;\n      /** The number of milliseconds to wait before announcing the snack bar's content. */\n\n      this._announceDelay = 150;\n      /** Whether the component has been destroyed. */\n\n      this._destroyed = false;\n      /** Subject for notifying that the snack bar has announced to screen readers. */\n\n      this._onAnnounce = new Subject();\n      /** Subject for notifying that the snack bar has exited from view. */\n\n      this._onExit = new Subject();\n      /** Subject for notifying that the snack bar has finished entering the view. */\n\n      this._onEnter = new Subject();\n      /** The state of the snack bar animations. */\n\n      this._animationState = 'void';\n      /**\n       * Attaches a DOM portal to the snack bar container.\n       * @deprecated To be turned into a method.\n       * @breaking-change 10.0.0\n       */\n\n      this.attachDomPortal = portal => {\n        this._assertNotAttached();\n\n        const result = this._portalOutlet.attachDomPortal(portal);\n\n        this._afterPortalAttached();\n\n        return result;\n      }; // Use aria-live rather than a live role like 'alert' or 'status'\n      // because NVDA and JAWS have show inconsistent behavior with live roles.\n\n\n      if (snackBarConfig.politeness === 'assertive' && !snackBarConfig.announcementMessage) {\n        this._live = 'assertive';\n      } else if (snackBarConfig.politeness === 'off') {\n        this._live = 'off';\n      } else {\n        this._live = 'polite';\n      } // Only set role for Firefox. Set role based on aria-live because setting role=\"alert\" implies\n      // aria-live=\"assertive\" which may cause issues if aria-live is set to \"polite\" above.\n\n\n      if (this._platform.FIREFOX) {\n        if (this._live === 'polite') {\n          this._role = 'status';\n        }\n\n        if (this._live === 'assertive') {\n          this._role = 'alert';\n        }\n      }\n    }\n    /** Attach a component portal as content to this snack bar container. */\n\n\n    attachComponentPortal(portal) {\n      this._assertNotAttached();\n\n      const result = this._portalOutlet.attachComponentPortal(portal);\n\n      this._afterPortalAttached();\n\n      return result;\n    }\n    /** Attach a template portal as content to this snack bar container. */\n\n\n    attachTemplatePortal(portal) {\n      this._assertNotAttached();\n\n      const result = this._portalOutlet.attachTemplatePortal(portal);\n\n      this._afterPortalAttached();\n\n      return result;\n    }\n    /** Handle end of animations, updating the state of the snackbar. */\n\n\n    onAnimationEnd(event) {\n      const {\n        fromState,\n        toState\n      } = event;\n\n      if (toState === 'void' && fromState !== 'void' || toState === 'hidden') {\n        this._completeExit();\n      }\n\n      if (toState === 'visible') {\n        // Note: we shouldn't use `this` inside the zone callback,\n        // because it can cause a memory leak.\n        const onEnter = this._onEnter;\n\n        this._ngZone.run(() => {\n          onEnter.next();\n          onEnter.complete();\n        });\n      }\n    }\n    /** Begin animation of snack bar entrance into view. */\n\n\n    enter() {\n      if (!this._destroyed) {\n        this._animationState = 'visible';\n\n        this._changeDetectorRef.detectChanges();\n\n        this._screenReaderAnnounce();\n      }\n    }\n    /** Begin animation of the snack bar exiting from view. */\n\n\n    exit() {\n      // It's common for snack bars to be opened by random outside calls like HTTP requests or\n      // errors. Run inside the NgZone to ensure that it functions correctly.\n      this._ngZone.run(() => {\n        // Note: this one transitions to `hidden`, rather than `void`, in order to handle the case\n        // where multiple snack bars are opened in quick succession (e.g. two consecutive calls to\n        // `MatSnackBar.open`).\n        this._animationState = 'hidden'; // Mark this element with an 'exit' attribute to indicate that the snackbar has\n        // been dismissed and will soon be removed from the DOM. This is used by the snackbar\n        // test harness.\n\n        this._elementRef.nativeElement.setAttribute('mat-exit', ''); // If the snack bar hasn't been announced by the time it exits it wouldn't have been open\n        // long enough to visually read it either, so clear the timeout for announcing.\n\n\n        clearTimeout(this._announceTimeoutId);\n      });\n\n      return this._onExit;\n    }\n    /** Makes sure the exit callbacks have been invoked when the element is destroyed. */\n\n\n    ngOnDestroy() {\n      this._destroyed = true;\n\n      this._completeExit();\n    }\n    /**\n     * Waits for the zone to settle before removing the element. Helps prevent\n     * errors where we end up removing an element which is in the middle of an animation.\n     */\n\n\n    _completeExit() {\n      this._ngZone.onMicrotaskEmpty.pipe(take(1)).subscribe(() => {\n        this._ngZone.run(() => {\n          this._onExit.next();\n\n          this._onExit.complete();\n        });\n      });\n    }\n    /**\n     * Called after the portal contents have been attached. Can be\n     * used to modify the DOM once it's guaranteed to be in place.\n     */\n\n\n    _afterPortalAttached() {\n      const element = this._elementRef.nativeElement;\n      const panelClasses = this.snackBarConfig.panelClass;\n\n      if (panelClasses) {\n        if (Array.isArray(panelClasses)) {\n          // Note that we can't use a spread here, because IE doesn't support multiple arguments.\n          panelClasses.forEach(cssClass => element.classList.add(cssClass));\n        } else {\n          element.classList.add(panelClasses);\n        }\n      }\n    }\n    /** Asserts that no content is already attached to the container. */\n\n\n    _assertNotAttached() {\n      if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('Attempting to attach snack bar content after content is already attached');\n      }\n    }\n    /**\n     * Starts a timeout to move the snack bar content to the live region so screen readers will\n     * announce it.\n     */\n\n\n    _screenReaderAnnounce() {\n      if (!this._announceTimeoutId) {\n        this._ngZone.runOutsideAngular(() => {\n          this._announceTimeoutId = setTimeout(() => {\n            const inertElement = this._elementRef.nativeElement.querySelector('[aria-hidden]');\n\n            const liveElement = this._elementRef.nativeElement.querySelector('[aria-live]');\n\n            if (inertElement && liveElement) {\n              // If an element in the snack bar content is focused before being moved\n              // track it and restore focus after moving to the live region.\n              let focusedElement = null;\n\n              if (this._platform.isBrowser && document.activeElement instanceof HTMLElement && inertElement.contains(document.activeElement)) {\n                focusedElement = document.activeElement;\n              }\n\n              inertElement.removeAttribute('aria-hidden');\n              liveElement.appendChild(inertElement);\n              focusedElement?.focus();\n\n              this._onAnnounce.next();\n\n              this._onAnnounce.complete();\n            }\n          }, this._announceDelay);\n        });\n      }\n    }\n\n  }\n\n  _MatSnackBarContainerBase.ɵfac = function _MatSnackBarContainerBase_Factory(t) {\n    return new (t || _MatSnackBarContainerBase)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.Platform), i0.ɵɵdirectiveInject(MatSnackBarConfig));\n  };\n\n  _MatSnackBarContainerBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: _MatSnackBarContainerBase,\n    viewQuery: function _MatSnackBarContainerBase_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(CdkPortalOutlet, 7);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._portalOutlet = _t.first);\n      }\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return _MatSnackBarContainerBase;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Internal component that wraps user-provided snack bar content.\n * @docs-private\n */\n\n\nlet MatSnackBarContainer = /*#__PURE__*/(() => {\n  class MatSnackBarContainer extends _MatSnackBarContainerBase {\n    _afterPortalAttached() {\n      super._afterPortalAttached();\n\n      if (this.snackBarConfig.horizontalPosition === 'center') {\n        this._elementRef.nativeElement.classList.add('mat-snack-bar-center');\n      }\n\n      if (this.snackBarConfig.verticalPosition === 'top') {\n        this._elementRef.nativeElement.classList.add('mat-snack-bar-top');\n      }\n    }\n\n  }\n\n  MatSnackBarContainer.ɵfac = /* @__PURE__ */function () {\n    let ɵMatSnackBarContainer_BaseFactory;\n    return function MatSnackBarContainer_Factory(t) {\n      return (ɵMatSnackBarContainer_BaseFactory || (ɵMatSnackBarContainer_BaseFactory = i0.ɵɵgetInheritedFactory(MatSnackBarContainer)))(t || MatSnackBarContainer);\n    };\n  }();\n\n  MatSnackBarContainer.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatSnackBarContainer,\n    selectors: [[\"snack-bar-container\"]],\n    hostAttrs: [1, \"mat-snack-bar-container\"],\n    hostVars: 1,\n    hostBindings: function MatSnackBarContainer_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵsyntheticHostListener(\"@state.done\", function MatSnackBarContainer_animation_state_done_HostBindingHandler($event) {\n          return ctx.onAnimationEnd($event);\n        });\n      }\n\n      if (rf & 2) {\n        i0.ɵɵsyntheticHostProperty(\"@state\", ctx._animationState);\n      }\n    },\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 3,\n    vars: 2,\n    consts: [[\"aria-hidden\", \"true\"], [\"cdkPortalOutlet\", \"\"]],\n    template: function MatSnackBarContainer_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵtemplate(1, MatSnackBarContainer_ng_template_1_Template, 0, 0, \"ng-template\", 1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(2, \"div\");\n      }\n\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵattribute(\"aria-live\", ctx._live)(\"role\", ctx._role);\n      }\n    },\n    dependencies: [i3$1.CdkPortalOutlet],\n    styles: [\".mat-snack-bar-container{border-radius:4px;box-sizing:border-box;display:block;margin:24px;max-width:33vw;min-width:344px;padding:14px 16px;min-height:48px;transform-origin:center}.cdk-high-contrast-active .mat-snack-bar-container{border:solid 1px}.mat-snack-bar-handset{width:100%}.mat-snack-bar-handset .mat-snack-bar-container{margin:8px;max-width:100%;min-width:0;width:100%}\"],\n    encapsulation: 2,\n    data: {\n      animation: [matSnackBarAnimations.snackBarState]\n    }\n  });\n  return MatSnackBarContainer;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nlet MatSnackBarModule = /*#__PURE__*/(() => {\n  class MatSnackBarModule {}\n\n  MatSnackBarModule.ɵfac = function MatSnackBarModule_Factory(t) {\n    return new (t || MatSnackBarModule)();\n  };\n\n  MatSnackBarModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatSnackBarModule\n  });\n  MatSnackBarModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [OverlayModule, PortalModule, CommonModule, MatButtonModule, MatCommonModule, MatCommonModule]\n  });\n  return MatSnackBarModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Injection token that can be used to specify default snack bar. */\n\n\nconst MAT_SNACK_BAR_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('mat-snack-bar-default-options', {\n  providedIn: 'root',\n  factory: MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY\n});\n/** @docs-private */\n\nfunction MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY() {\n  return new MatSnackBarConfig();\n}\n\nlet _MatSnackBarBase = /*#__PURE__*/(() => {\n  class _MatSnackBarBase {\n    constructor(_overlay, _live, _injector, _breakpointObserver, _parentSnackBar, _defaultConfig) {\n      this._overlay = _overlay;\n      this._live = _live;\n      this._injector = _injector;\n      this._breakpointObserver = _breakpointObserver;\n      this._parentSnackBar = _parentSnackBar;\n      this._defaultConfig = _defaultConfig;\n      /**\n       * Reference to the current snack bar in the view *at this level* (in the Angular injector tree).\n       * If there is a parent snack-bar service, all operations should delegate to that parent\n       * via `_openedSnackBarRef`.\n       */\n\n      this._snackBarRefAtThisLevel = null;\n    }\n    /** Reference to the currently opened snackbar at *any* level. */\n\n\n    get _openedSnackBarRef() {\n      const parent = this._parentSnackBar;\n      return parent ? parent._openedSnackBarRef : this._snackBarRefAtThisLevel;\n    }\n\n    set _openedSnackBarRef(value) {\n      if (this._parentSnackBar) {\n        this._parentSnackBar._openedSnackBarRef = value;\n      } else {\n        this._snackBarRefAtThisLevel = value;\n      }\n    }\n    /**\n     * Creates and dispatches a snack bar with a custom component for the content, removing any\n     * currently opened snack bars.\n     *\n     * @param component Component to be instantiated.\n     * @param config Extra configuration for the snack bar.\n     */\n\n\n    openFromComponent(component, config) {\n      return this._attach(component, config);\n    }\n    /**\n     * Creates and dispatches a snack bar with a custom template for the content, removing any\n     * currently opened snack bars.\n     *\n     * @param template Template to be instantiated.\n     * @param config Extra configuration for the snack bar.\n     */\n\n\n    openFromTemplate(template, config) {\n      return this._attach(template, config);\n    }\n    /**\n     * Opens a snackbar with a message and an optional action.\n     * @param message The message to show in the snackbar.\n     * @param action The label for the snackbar action.\n     * @param config Additional configuration options for the snackbar.\n     */\n\n\n    open(message, action = '', config) {\n      const _config = { ...this._defaultConfig,\n        ...config\n      }; // Since the user doesn't have access to the component, we can\n      // override the data to pass in our own message and action.\n\n      _config.data = {\n        message,\n        action\n      }; // Since the snack bar has `role=\"alert\"`, we don't\n      // want to announce the same message twice.\n\n      if (_config.announcementMessage === message) {\n        _config.announcementMessage = undefined;\n      }\n\n      return this.openFromComponent(this.simpleSnackBarComponent, _config);\n    }\n    /**\n     * Dismisses the currently-visible snack bar.\n     */\n\n\n    dismiss() {\n      if (this._openedSnackBarRef) {\n        this._openedSnackBarRef.dismiss();\n      }\n    }\n\n    ngOnDestroy() {\n      // Only dismiss the snack bar at the current level on destroy.\n      if (this._snackBarRefAtThisLevel) {\n        this._snackBarRefAtThisLevel.dismiss();\n      }\n    }\n    /**\n     * Attaches the snack bar container component to the overlay.\n     */\n\n\n    _attachSnackBarContainer(overlayRef, config) {\n      const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n      const injector = Injector.create({\n        parent: userInjector || this._injector,\n        providers: [{\n          provide: MatSnackBarConfig,\n          useValue: config\n        }]\n      });\n      const containerPortal = new ComponentPortal(this.snackBarContainerComponent, config.viewContainerRef, injector);\n      const containerRef = overlayRef.attach(containerPortal);\n      containerRef.instance.snackBarConfig = config;\n      return containerRef.instance;\n    }\n    /**\n     * Places a new component or a template as the content of the snack bar container.\n     */\n\n\n    _attach(content, userConfig) {\n      const config = { ...new MatSnackBarConfig(),\n        ...this._defaultConfig,\n        ...userConfig\n      };\n\n      const overlayRef = this._createOverlay(config);\n\n      const container = this._attachSnackBarContainer(overlayRef, config);\n\n      const snackBarRef = new MatSnackBarRef(container, overlayRef);\n\n      if (content instanceof TemplateRef) {\n        const portal = new TemplatePortal(content, null, {\n          $implicit: config.data,\n          snackBarRef\n        });\n        snackBarRef.instance = container.attachTemplatePortal(portal);\n      } else {\n        const injector = this._createInjector(config, snackBarRef);\n\n        const portal = new ComponentPortal(content, undefined, injector);\n        const contentRef = container.attachComponentPortal(portal); // We can't pass this via the injector, because the injector is created earlier.\n\n        snackBarRef.instance = contentRef.instance;\n      } // Subscribe to the breakpoint observer and attach the mat-snack-bar-handset class as\n      // appropriate. This class is applied to the overlay element because the overlay must expand to\n      // fill the width of the screen for full width snackbars.\n\n\n      this._breakpointObserver.observe(Breakpoints.HandsetPortrait).pipe(takeUntil(overlayRef.detachments())).subscribe(state => {\n        overlayRef.overlayElement.classList.toggle(this.handsetCssClass, state.matches);\n      });\n\n      if (config.announcementMessage) {\n        // Wait until the snack bar contents have been announced then deliver this message.\n        container._onAnnounce.subscribe(() => {\n          this._live.announce(config.announcementMessage, config.politeness);\n        });\n      }\n\n      this._animateSnackBar(snackBarRef, config);\n\n      this._openedSnackBarRef = snackBarRef;\n      return this._openedSnackBarRef;\n    }\n    /** Animates the old snack bar out and the new one in. */\n\n\n    _animateSnackBar(snackBarRef, config) {\n      // When the snackbar is dismissed, clear the reference to it.\n      snackBarRef.afterDismissed().subscribe(() => {\n        // Clear the snackbar ref if it hasn't already been replaced by a newer snackbar.\n        if (this._openedSnackBarRef == snackBarRef) {\n          this._openedSnackBarRef = null;\n        }\n\n        if (config.announcementMessage) {\n          this._live.clear();\n        }\n      });\n\n      if (this._openedSnackBarRef) {\n        // If a snack bar is already in view, dismiss it and enter the\n        // new snack bar after exit animation is complete.\n        this._openedSnackBarRef.afterDismissed().subscribe(() => {\n          snackBarRef.containerInstance.enter();\n        });\n\n        this._openedSnackBarRef.dismiss();\n      } else {\n        // If no snack bar is in view, enter the new snack bar.\n        snackBarRef.containerInstance.enter();\n      } // If a dismiss timeout is provided, set up dismiss based on after the snackbar is opened.\n\n\n      if (config.duration && config.duration > 0) {\n        snackBarRef.afterOpened().subscribe(() => snackBarRef._dismissAfter(config.duration));\n      }\n    }\n    /**\n     * Creates a new overlay and places it in the correct location.\n     * @param config The user-specified snack bar config.\n     */\n\n\n    _createOverlay(config) {\n      const overlayConfig = new OverlayConfig();\n      overlayConfig.direction = config.direction;\n\n      let positionStrategy = this._overlay.position().global(); // Set horizontal position.\n\n\n      const isRtl = config.direction === 'rtl';\n      const isLeft = config.horizontalPosition === 'left' || config.horizontalPosition === 'start' && !isRtl || config.horizontalPosition === 'end' && isRtl;\n      const isRight = !isLeft && config.horizontalPosition !== 'center';\n\n      if (isLeft) {\n        positionStrategy.left('0');\n      } else if (isRight) {\n        positionStrategy.right('0');\n      } else {\n        positionStrategy.centerHorizontally();\n      } // Set horizontal position.\n\n\n      if (config.verticalPosition === 'top') {\n        positionStrategy.top('0');\n      } else {\n        positionStrategy.bottom('0');\n      }\n\n      overlayConfig.positionStrategy = positionStrategy;\n      return this._overlay.create(overlayConfig);\n    }\n    /**\n     * Creates an injector to be used inside of a snack bar component.\n     * @param config Config that was used to create the snack bar.\n     * @param snackBarRef Reference to the snack bar.\n     */\n\n\n    _createInjector(config, snackBarRef) {\n      const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n      return Injector.create({\n        parent: userInjector || this._injector,\n        providers: [{\n          provide: MatSnackBarRef,\n          useValue: snackBarRef\n        }, {\n          provide: MAT_SNACK_BAR_DATA,\n          useValue: config.data\n        }]\n      });\n    }\n\n  }\n\n  _MatSnackBarBase.ɵfac = function _MatSnackBarBase_Factory(t) {\n    return new (t || _MatSnackBarBase)(i0.ɵɵinject(i1$1.Overlay), i0.ɵɵinject(i2$1.LiveAnnouncer), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i3$2.BreakpointObserver), i0.ɵɵinject(_MatSnackBarBase, 12), i0.ɵɵinject(MAT_SNACK_BAR_DEFAULT_OPTIONS));\n  };\n\n  _MatSnackBarBase.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: _MatSnackBarBase,\n    factory: _MatSnackBarBase.ɵfac\n  });\n  return _MatSnackBarBase;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Service to dispatch Material Design snack bar messages.\n */\n\n\nlet MatSnackBar = /*#__PURE__*/(() => {\n  class MatSnackBar extends _MatSnackBarBase {\n    constructor(overlay, live, injector, breakpointObserver, parentSnackBar, defaultConfig) {\n      super(overlay, live, injector, breakpointObserver, parentSnackBar, defaultConfig);\n      this.simpleSnackBarComponent = SimpleSnackBar;\n      this.snackBarContainerComponent = MatSnackBarContainer;\n      this.handsetCssClass = 'mat-snack-bar-handset';\n    }\n\n  }\n\n  MatSnackBar.ɵfac = function MatSnackBar_Factory(t) {\n    return new (t || MatSnackBar)(i0.ɵɵinject(i1$1.Overlay), i0.ɵɵinject(i2$1.LiveAnnouncer), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i3$2.BreakpointObserver), i0.ɵɵinject(MatSnackBar, 12), i0.ɵɵinject(MAT_SNACK_BAR_DEFAULT_OPTIONS));\n  };\n\n  MatSnackBar.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MatSnackBar,\n    factory: MatSnackBar.ɵfac,\n    providedIn: MatSnackBarModule\n  });\n  return MatSnackBar;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { MAT_SNACK_BAR_DATA, MAT_SNACK_BAR_DEFAULT_OPTIONS, MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY, MatSnackBar, MatSnackBarConfig, MatSnackBarContainer, MatSnackBarModule, MatSnackBarRef, SimpleSnackBar, _MatSnackBarBase, _MatSnackBarContainerBase, matSnackBarAnimations }; //# sourceMappingURL=snack-bar.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}