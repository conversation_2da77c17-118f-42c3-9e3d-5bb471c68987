{"ast": null, "code": "import { Subject } from '../Subject';\nimport { asyncScheduler } from '../scheduler/async';\nimport { Subscription } from '../Subscription';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { arrRemove } from '../util/arrRemove';\nimport { popScheduler } from '../util/args';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function windowTime(windowTimeSpan, ...otherArgs) {\n  var _a, _b;\n\n  const scheduler = (_a = popScheduler(otherArgs)) !== null && _a !== void 0 ? _a : asyncScheduler;\n  const windowCreationInterval = (_b = otherArgs[0]) !== null && _b !== void 0 ? _b : null;\n  const maxWindowSize = otherArgs[1] || Infinity;\n  return operate((source, subscriber) => {\n    let windowRecords = [];\n    let restartOnClose = false;\n\n    const closeWindow = record => {\n      const {\n        window,\n        subs\n      } = record;\n      window.complete();\n      subs.unsubscribe();\n      arrRemove(windowRecords, record);\n      restartOnClose && startWindow();\n    };\n\n    const startWindow = () => {\n      if (windowRecords) {\n        const subs = new Subscription();\n        subscriber.add(subs);\n        const window = new Subject();\n        const record = {\n          window,\n          subs,\n          seen: 0\n        };\n        windowRecords.push(record);\n        subscriber.next(window.asObservable());\n        executeSchedule(subs, scheduler, () => closeWindow(record), windowTimeSpan);\n      }\n    };\n\n    if (windowCreationInterval !== null && windowCreationInterval >= 0) {\n      executeSchedule(subscriber, scheduler, startWindow, windowCreationInterval, true);\n    } else {\n      restartOnClose = true;\n    }\n\n    startWindow();\n\n    const loop = cb => windowRecords.slice().forEach(cb);\n\n    const terminate = cb => {\n      loop(({\n        window\n      }) => cb(window));\n      cb(subscriber);\n      subscriber.unsubscribe();\n    };\n\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      loop(record => {\n        record.window.next(value);\n        maxWindowSize <= ++record.seen && closeWindow(record);\n      });\n    }, () => terminate(consumer => consumer.complete()), err => terminate(consumer => consumer.error(err))));\n    return () => {\n      windowRecords = null;\n    };\n  });\n}", "map": {"version": 3, "names": ["Subject", "asyncScheduler", "Subscription", "operate", "createOperatorSubscriber", "arr<PERSON><PERSON><PERSON>", "popScheduler", "executeSchedule", "windowTime", "windowTimeSpan", "otherArgs", "_a", "_b", "scheduler", "windowCreationInterval", "maxWindowSize", "Infinity", "source", "subscriber", "windowRecords", "restartOnClose", "closeWindow", "record", "window", "subs", "complete", "unsubscribe", "startWindow", "add", "seen", "push", "next", "asObservable", "loop", "cb", "slice", "for<PERSON>ach", "terminate", "subscribe", "value", "consumer", "err", "error"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/rxjs/dist/esm/internal/operators/windowTime.js"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { asyncScheduler } from '../scheduler/async';\nimport { Subscription } from '../Subscription';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { arrRemove } from '../util/arrRemove';\nimport { popScheduler } from '../util/args';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function windowTime(windowTimeSpan, ...otherArgs) {\n    var _a, _b;\n    const scheduler = (_a = popScheduler(otherArgs)) !== null && _a !== void 0 ? _a : asyncScheduler;\n    const windowCreationInterval = (_b = otherArgs[0]) !== null && _b !== void 0 ? _b : null;\n    const maxWindowSize = otherArgs[1] || Infinity;\n    return operate((source, subscriber) => {\n        let windowRecords = [];\n        let restartOnClose = false;\n        const closeWindow = (record) => {\n            const { window, subs } = record;\n            window.complete();\n            subs.unsubscribe();\n            arrRemove(windowRecords, record);\n            restartOnClose && startWindow();\n        };\n        const startWindow = () => {\n            if (windowRecords) {\n                const subs = new Subscription();\n                subscriber.add(subs);\n                const window = new Subject();\n                const record = {\n                    window,\n                    subs,\n                    seen: 0,\n                };\n                windowRecords.push(record);\n                subscriber.next(window.asObservable());\n                executeSchedule(subs, scheduler, () => closeWindow(record), windowTimeSpan);\n            }\n        };\n        if (windowCreationInterval !== null && windowCreationInterval >= 0) {\n            executeSchedule(subscriber, scheduler, startWindow, windowCreationInterval, true);\n        }\n        else {\n            restartOnClose = true;\n        }\n        startWindow();\n        const loop = (cb) => windowRecords.slice().forEach(cb);\n        const terminate = (cb) => {\n            loop(({ window }) => cb(window));\n            cb(subscriber);\n            subscriber.unsubscribe();\n        };\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            loop((record) => {\n                record.window.next(value);\n                maxWindowSize <= ++record.seen && closeWindow(record);\n            });\n        }, () => terminate((consumer) => consumer.complete()), (err) => terminate((consumer) => consumer.error(err))));\n        return () => {\n            windowRecords = null;\n        };\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,YAAxB;AACA,SAASC,cAAT,QAA+B,oBAA/B;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,SAASC,SAAT,QAA0B,mBAA1B;AACA,SAASC,YAAT,QAA6B,cAA7B;AACA,SAASC,eAAT,QAAgC,yBAAhC;AACA,OAAO,SAASC,UAAT,CAAoBC,cAApB,EAAoC,GAAGC,SAAvC,EAAkD;EACrD,IAAIC,EAAJ,EAAQC,EAAR;;EACA,MAAMC,SAAS,GAAG,CAACF,EAAE,GAAGL,YAAY,CAACI,SAAD,CAAlB,MAAmC,IAAnC,IAA2CC,EAAE,KAAK,KAAK,CAAvD,GAA2DA,EAA3D,GAAgEV,cAAlF;EACA,MAAMa,sBAAsB,GAAG,CAACF,EAAE,GAAGF,SAAS,CAAC,CAAD,CAAf,MAAwB,IAAxB,IAAgCE,EAAE,KAAK,KAAK,CAA5C,GAAgDA,EAAhD,GAAqD,IAApF;EACA,MAAMG,aAAa,GAAGL,SAAS,CAAC,CAAD,CAAT,IAAgBM,QAAtC;EACA,OAAOb,OAAO,CAAC,CAACc,MAAD,EAASC,UAAT,KAAwB;IACnC,IAAIC,aAAa,GAAG,EAApB;IACA,IAAIC,cAAc,GAAG,KAArB;;IACA,MAAMC,WAAW,GAAIC,MAAD,IAAY;MAC5B,MAAM;QAAEC,MAAF;QAAUC;MAAV,IAAmBF,MAAzB;MACAC,MAAM,CAACE,QAAP;MACAD,IAAI,CAACE,WAAL;MACArB,SAAS,CAACc,aAAD,EAAgBG,MAAhB,CAAT;MACAF,cAAc,IAAIO,WAAW,EAA7B;IACH,CAND;;IAOA,MAAMA,WAAW,GAAG,MAAM;MACtB,IAAIR,aAAJ,EAAmB;QACf,MAAMK,IAAI,GAAG,IAAItB,YAAJ,EAAb;QACAgB,UAAU,CAACU,GAAX,CAAeJ,IAAf;QACA,MAAMD,MAAM,GAAG,IAAIvB,OAAJ,EAAf;QACA,MAAMsB,MAAM,GAAG;UACXC,MADW;UAEXC,IAFW;UAGXK,IAAI,EAAE;QAHK,CAAf;QAKAV,aAAa,CAACW,IAAd,CAAmBR,MAAnB;QACAJ,UAAU,CAACa,IAAX,CAAgBR,MAAM,CAACS,YAAP,EAAhB;QACAzB,eAAe,CAACiB,IAAD,EAAOX,SAAP,EAAkB,MAAMQ,WAAW,CAACC,MAAD,CAAnC,EAA6Cb,cAA7C,CAAf;MACH;IACJ,CAdD;;IAeA,IAAIK,sBAAsB,KAAK,IAA3B,IAAmCA,sBAAsB,IAAI,CAAjE,EAAoE;MAChEP,eAAe,CAACW,UAAD,EAAaL,SAAb,EAAwBc,WAAxB,EAAqCb,sBAArC,EAA6D,IAA7D,CAAf;IACH,CAFD,MAGK;MACDM,cAAc,GAAG,IAAjB;IACH;;IACDO,WAAW;;IACX,MAAMM,IAAI,GAAIC,EAAD,IAAQf,aAAa,CAACgB,KAAd,GAAsBC,OAAtB,CAA8BF,EAA9B,CAArB;;IACA,MAAMG,SAAS,GAAIH,EAAD,IAAQ;MACtBD,IAAI,CAAC,CAAC;QAAEV;MAAF,CAAD,KAAgBW,EAAE,CAACX,MAAD,CAAnB,CAAJ;MACAW,EAAE,CAAChB,UAAD,CAAF;MACAA,UAAU,CAACQ,WAAX;IACH,CAJD;;IAKAT,MAAM,CAACqB,SAAP,CAAiBlC,wBAAwB,CAACc,UAAD,EAAcqB,KAAD,IAAW;MAC7DN,IAAI,CAAEX,MAAD,IAAY;QACbA,MAAM,CAACC,MAAP,CAAcQ,IAAd,CAAmBQ,KAAnB;QACAxB,aAAa,IAAI,EAAEO,MAAM,CAACO,IAA1B,IAAkCR,WAAW,CAACC,MAAD,CAA7C;MACH,CAHG,CAAJ;IAIH,CALwC,EAKtC,MAAMe,SAAS,CAAEG,QAAD,IAAcA,QAAQ,CAACf,QAAT,EAAf,CALuB,EAKegB,GAAD,IAASJ,SAAS,CAAEG,QAAD,IAAcA,QAAQ,CAACE,KAAT,CAAeD,GAAf,CAAf,CALhC,CAAzC;IAMA,OAAO,MAAM;MACTtB,aAAa,GAAG,IAAhB;IACH,CAFD;EAGH,CA/Ca,CAAd;AAgDH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}