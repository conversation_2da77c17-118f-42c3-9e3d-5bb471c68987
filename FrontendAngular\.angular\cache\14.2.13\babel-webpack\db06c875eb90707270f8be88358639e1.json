{"ast": null, "code": "import { AsyncSubject } from '../AsyncSubject';\nimport { ConnectableObservable } from '../observable/ConnectableObservable';\nexport function publishLast() {\n  return source => {\n    const subject = new AsyncSubject();\n    return new ConnectableObservable(source, () => subject);\n  };\n}", "map": {"version": 3, "names": ["AsyncSubject", "ConnectableObservable", "publishLast", "source", "subject"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/rxjs/dist/esm/internal/operators/publishLast.js"], "sourcesContent": ["import { AsyncSubject } from '../AsyncSubject';\nimport { ConnectableObservable } from '../observable/ConnectableObservable';\nexport function publishLast() {\n    return (source) => {\n        const subject = new AsyncSubject();\n        return new ConnectableObservable(source, () => subject);\n    };\n}\n"], "mappings": "AAAA,SAASA,YAAT,QAA6B,iBAA7B;AACA,SAASC,qBAAT,QAAsC,qCAAtC;AACA,OAAO,SAASC,WAAT,GAAuB;EAC1B,OAAQC,MAAD,IAAY;IACf,MAAMC,OAAO,GAAG,IAAIJ,YAAJ,EAAhB;IACA,OAAO,IAAIC,qBAAJ,CAA0BE,MAA1B,EAAkC,MAAMC,OAAxC,CAAP;EACH,CAHD;AAIH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}