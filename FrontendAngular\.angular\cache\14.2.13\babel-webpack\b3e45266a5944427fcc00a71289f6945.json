{"ast": null, "code": "import { isBinary } from \"./is-binary.js\";\n/**\n * Replaces every Buffer | ArrayBuffer | Blob | File in packet with a numbered placeholder.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @public\n */\n\nexport function deconstructPacket(packet) {\n  const buffers = [];\n  const packetData = packet.data;\n  const pack = packet;\n  pack.data = _deconstructPacket(packetData, buffers);\n  pack.attachments = buffers.length; // number of binary 'attachments'\n\n  return {\n    packet: pack,\n    buffers: buffers\n  };\n}\n\nfunction _deconstructPacket(data, buffers) {\n  if (!data) return data;\n\n  if (isBinary(data)) {\n    const placeholder = {\n      _placeholder: true,\n      num: buffers.length\n    };\n    buffers.push(data);\n    return placeholder;\n  } else if (Array.isArray(data)) {\n    const newData = new Array(data.length);\n\n    for (let i = 0; i < data.length; i++) {\n      newData[i] = _deconstructPacket(data[i], buffers);\n    }\n\n    return newData;\n  } else if (typeof data === \"object\" && !(data instanceof Date)) {\n    const newData = {};\n\n    for (const key in data) {\n      if (Object.prototype.hasOwnProperty.call(data, key)) {\n        newData[key] = _deconstructPacket(data[key], buffers);\n      }\n    }\n\n    return newData;\n  }\n\n  return data;\n}\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @public\n */\n\n\nexport function reconstructPacket(packet, buffers) {\n  packet.data = _reconstructPacket(packet.data, buffers);\n  delete packet.attachments; // no longer useful\n\n  return packet;\n}\n\nfunction _reconstructPacket(data, buffers) {\n  if (!data) return data;\n\n  if (data && data._placeholder === true) {\n    const isIndexValid = typeof data.num === \"number\" && data.num >= 0 && data.num < buffers.length;\n\n    if (isIndexValid) {\n      return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n    } else {\n      throw new Error(\"illegal attachments\");\n    }\n  } else if (Array.isArray(data)) {\n    for (let i = 0; i < data.length; i++) {\n      data[i] = _reconstructPacket(data[i], buffers);\n    }\n  } else if (typeof data === \"object\") {\n    for (const key in data) {\n      if (Object.prototype.hasOwnProperty.call(data, key)) {\n        data[key] = _reconstructPacket(data[key], buffers);\n      }\n    }\n  }\n\n  return data;\n}", "map": {"version": 3, "names": ["isBinary", "deconstructPacket", "packet", "buffers", "packetData", "data", "pack", "_deconstructPacket", "attachments", "length", "placeholder", "_placeholder", "num", "push", "Array", "isArray", "newData", "i", "Date", "key", "Object", "prototype", "hasOwnProperty", "call", "reconstructPacket", "_reconstructPacket", "isIndexValid", "Error"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/socket.io-parser/build/esm/binary.js"], "sourcesContent": ["import { isBinary } from \"./is-binary.js\";\n/**\n * Replaces every Buffer | ArrayBuffer | Blob | File in packet with a numbered placeholder.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @public\n */\nexport function deconstructPacket(packet) {\n    const buffers = [];\n    const packetData = packet.data;\n    const pack = packet;\n    pack.data = _deconstructPacket(packetData, buffers);\n    pack.attachments = buffers.length; // number of binary 'attachments'\n    return { packet: pack, buffers: buffers };\n}\nfunction _deconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (isBinary(data)) {\n        const placeholder = { _placeholder: true, num: buffers.length };\n        buffers.push(data);\n        return placeholder;\n    }\n    else if (Array.isArray(data)) {\n        const newData = new Array(data.length);\n        for (let i = 0; i < data.length; i++) {\n            newData[i] = _deconstructPacket(data[i], buffers);\n        }\n        return newData;\n    }\n    else if (typeof data === \"object\" && !(data instanceof Date)) {\n        const newData = {};\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                newData[key] = _deconstructPacket(data[key], buffers);\n            }\n        }\n        return newData;\n    }\n    return data;\n}\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @public\n */\nexport function reconstructPacket(packet, buffers) {\n    packet.data = _reconstructPacket(packet.data, buffers);\n    delete packet.attachments; // no longer useful\n    return packet;\n}\nfunction _reconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (data && data._placeholder === true) {\n        const isIndexValid = typeof data.num === \"number\" &&\n            data.num >= 0 &&\n            data.num < buffers.length;\n        if (isIndexValid) {\n            return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n        }\n        else {\n            throw new Error(\"illegal attachments\");\n        }\n    }\n    else if (Array.isArray(data)) {\n        for (let i = 0; i < data.length; i++) {\n            data[i] = _reconstructPacket(data[i], buffers);\n        }\n    }\n    else if (typeof data === \"object\") {\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                data[key] = _reconstructPacket(data[key], buffers);\n            }\n        }\n    }\n    return data;\n}\n"], "mappings": "AAAA,SAASA,QAAT,QAAyB,gBAAzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,SAASC,iBAAT,CAA2BC,MAA3B,EAAmC;EACtC,MAAMC,OAAO,GAAG,EAAhB;EACA,MAAMC,UAAU,GAAGF,MAAM,CAACG,IAA1B;EACA,MAAMC,IAAI,GAAGJ,MAAb;EACAI,IAAI,CAACD,IAAL,GAAYE,kBAAkB,CAACH,UAAD,EAAaD,OAAb,CAA9B;EACAG,IAAI,CAACE,WAAL,GAAmBL,OAAO,CAACM,MAA3B,CALsC,CAKH;;EACnC,OAAO;IAAEP,MAAM,EAAEI,IAAV;IAAgBH,OAAO,EAAEA;EAAzB,CAAP;AACH;;AACD,SAASI,kBAAT,CAA4BF,IAA5B,EAAkCF,OAAlC,EAA2C;EACvC,IAAI,CAACE,IAAL,EACI,OAAOA,IAAP;;EACJ,IAAIL,QAAQ,CAACK,IAAD,CAAZ,EAAoB;IAChB,MAAMK,WAAW,GAAG;MAAEC,YAAY,EAAE,IAAhB;MAAsBC,GAAG,EAAET,OAAO,CAACM;IAAnC,CAApB;IACAN,OAAO,CAACU,IAAR,CAAaR,IAAb;IACA,OAAOK,WAAP;EACH,CAJD,MAKK,IAAII,KAAK,CAACC,OAAN,CAAcV,IAAd,CAAJ,EAAyB;IAC1B,MAAMW,OAAO,GAAG,IAAIF,KAAJ,CAAUT,IAAI,CAACI,MAAf,CAAhB;;IACA,KAAK,IAAIQ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGZ,IAAI,CAACI,MAAzB,EAAiCQ,CAAC,EAAlC,EAAsC;MAClCD,OAAO,CAACC,CAAD,CAAP,GAAaV,kBAAkB,CAACF,IAAI,CAACY,CAAD,CAAL,EAAUd,OAAV,CAA/B;IACH;;IACD,OAAOa,OAAP;EACH,CANI,MAOA,IAAI,OAAOX,IAAP,KAAgB,QAAhB,IAA4B,EAAEA,IAAI,YAAYa,IAAlB,CAAhC,EAAyD;IAC1D,MAAMF,OAAO,GAAG,EAAhB;;IACA,KAAK,MAAMG,GAAX,IAAkBd,IAAlB,EAAwB;MACpB,IAAIe,MAAM,CAACC,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqClB,IAArC,EAA2Cc,GAA3C,CAAJ,EAAqD;QACjDH,OAAO,CAACG,GAAD,CAAP,GAAeZ,kBAAkB,CAACF,IAAI,CAACc,GAAD,CAAL,EAAYhB,OAAZ,CAAjC;MACH;IACJ;;IACD,OAAOa,OAAP;EACH;;EACD,OAAOX,IAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,OAAO,SAASmB,iBAAT,CAA2BtB,MAA3B,EAAmCC,OAAnC,EAA4C;EAC/CD,MAAM,CAACG,IAAP,GAAcoB,kBAAkB,CAACvB,MAAM,CAACG,IAAR,EAAcF,OAAd,CAAhC;EACA,OAAOD,MAAM,CAACM,WAAd,CAF+C,CAEpB;;EAC3B,OAAON,MAAP;AACH;;AACD,SAASuB,kBAAT,CAA4BpB,IAA5B,EAAkCF,OAAlC,EAA2C;EACvC,IAAI,CAACE,IAAL,EACI,OAAOA,IAAP;;EACJ,IAAIA,IAAI,IAAIA,IAAI,CAACM,YAAL,KAAsB,IAAlC,EAAwC;IACpC,MAAMe,YAAY,GAAG,OAAOrB,IAAI,CAACO,GAAZ,KAAoB,QAApB,IACjBP,IAAI,CAACO,GAAL,IAAY,CADK,IAEjBP,IAAI,CAACO,GAAL,GAAWT,OAAO,CAACM,MAFvB;;IAGA,IAAIiB,YAAJ,EAAkB;MACd,OAAOvB,OAAO,CAACE,IAAI,CAACO,GAAN,CAAd,CADc,CACY;IAC7B,CAFD,MAGK;MACD,MAAM,IAAIe,KAAJ,CAAU,qBAAV,CAAN;IACH;EACJ,CAVD,MAWK,IAAIb,KAAK,CAACC,OAAN,CAAcV,IAAd,CAAJ,EAAyB;IAC1B,KAAK,IAAIY,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGZ,IAAI,CAACI,MAAzB,EAAiCQ,CAAC,EAAlC,EAAsC;MAClCZ,IAAI,CAACY,CAAD,CAAJ,GAAUQ,kBAAkB,CAACpB,IAAI,CAACY,CAAD,CAAL,EAAUd,OAAV,CAA5B;IACH;EACJ,CAJI,MAKA,IAAI,OAAOE,IAAP,KAAgB,QAApB,EAA8B;IAC/B,KAAK,MAAMc,GAAX,IAAkBd,IAAlB,EAAwB;MACpB,IAAIe,MAAM,CAACC,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqClB,IAArC,EAA2Cc,GAA3C,CAAJ,EAAqD;QACjDd,IAAI,CAACc,GAAD,CAAJ,GAAYM,kBAAkB,CAACpB,IAAI,CAACc,GAAD,CAAL,EAAYhB,OAAZ,CAA9B;MACH;IACJ;EACJ;;EACD,OAAOE,IAAP;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}