{"ast": null, "code": "import { isFunction } from './util/isFunction';\nimport { isSubscription, Subscription } from './Subscription';\nimport { config } from './config';\nimport { reportUnhandledError } from './util/reportUnhandledError';\nimport { noop } from './util/noop';\nimport { nextNotification, errorNotification, COMPLETE_NOTIFICATION } from './NotificationFactories';\nimport { timeoutProvider } from './scheduler/timeoutProvider';\nimport { captureError } from './util/errorContext';\nexport class Subscriber extends Subscription {\n  constructor(destination) {\n    super();\n    this.isStopped = false;\n\n    if (destination) {\n      this.destination = destination;\n\n      if (isSubscription(destination)) {\n        destination.add(this);\n      }\n    } else {\n      this.destination = EMPTY_OBSERVER;\n    }\n  }\n\n  static create(next, error, complete) {\n    return new SafeSubscriber(next, error, complete);\n  }\n\n  next(value) {\n    if (this.isStopped) {\n      handleStoppedNotification(nextNotification(value), this);\n    } else {\n      this._next(value);\n    }\n  }\n\n  error(err) {\n    if (this.isStopped) {\n      handleStoppedNotification(errorNotification(err), this);\n    } else {\n      this.isStopped = true;\n\n      this._error(err);\n    }\n  }\n\n  complete() {\n    if (this.isStopped) {\n      handleStoppedNotification(COMPLETE_NOTIFICATION, this);\n    } else {\n      this.isStopped = true;\n\n      this._complete();\n    }\n  }\n\n  unsubscribe() {\n    if (!this.closed) {\n      this.isStopped = true;\n      super.unsubscribe();\n      this.destination = null;\n    }\n  }\n\n  _next(value) {\n    this.destination.next(value);\n  }\n\n  _error(err) {\n    try {\n      this.destination.error(err);\n    } finally {\n      this.unsubscribe();\n    }\n  }\n\n  _complete() {\n    try {\n      this.destination.complete();\n    } finally {\n      this.unsubscribe();\n    }\n  }\n\n}\nconst _bind = Function.prototype.bind;\n\nfunction bind(fn, thisArg) {\n  return _bind.call(fn, thisArg);\n}\n\nclass ConsumerObserver {\n  constructor(partialObserver) {\n    this.partialObserver = partialObserver;\n  }\n\n  next(value) {\n    const {\n      partialObserver\n    } = this;\n\n    if (partialObserver.next) {\n      try {\n        partialObserver.next(value);\n      } catch (error) {\n        handleUnhandledError(error);\n      }\n    }\n  }\n\n  error(err) {\n    const {\n      partialObserver\n    } = this;\n\n    if (partialObserver.error) {\n      try {\n        partialObserver.error(err);\n      } catch (error) {\n        handleUnhandledError(error);\n      }\n    } else {\n      handleUnhandledError(err);\n    }\n  }\n\n  complete() {\n    const {\n      partialObserver\n    } = this;\n\n    if (partialObserver.complete) {\n      try {\n        partialObserver.complete();\n      } catch (error) {\n        handleUnhandledError(error);\n      }\n    }\n  }\n\n}\n\nexport class SafeSubscriber extends Subscriber {\n  constructor(observerOrNext, error, complete) {\n    super();\n    let partialObserver;\n\n    if (isFunction(observerOrNext) || !observerOrNext) {\n      partialObserver = {\n        next: observerOrNext !== null && observerOrNext !== void 0 ? observerOrNext : undefined,\n        error: error !== null && error !== void 0 ? error : undefined,\n        complete: complete !== null && complete !== void 0 ? complete : undefined\n      };\n    } else {\n      let context;\n\n      if (this && config.useDeprecatedNextContext) {\n        context = Object.create(observerOrNext);\n\n        context.unsubscribe = () => this.unsubscribe();\n\n        partialObserver = {\n          next: observerOrNext.next && bind(observerOrNext.next, context),\n          error: observerOrNext.error && bind(observerOrNext.error, context),\n          complete: observerOrNext.complete && bind(observerOrNext.complete, context)\n        };\n      } else {\n        partialObserver = observerOrNext;\n      }\n    }\n\n    this.destination = new ConsumerObserver(partialObserver);\n  }\n\n}\n\nfunction handleUnhandledError(error) {\n  if (config.useDeprecatedSynchronousErrorHandling) {\n    captureError(error);\n  } else {\n    reportUnhandledError(error);\n  }\n}\n\nfunction defaultErrorHandler(err) {\n  throw err;\n}\n\nfunction handleStoppedNotification(notification, subscriber) {\n  const {\n    onStoppedNotification\n  } = config;\n  onStoppedNotification && timeoutProvider.setTimeout(() => onStoppedNotification(notification, subscriber));\n}\n\nexport const EMPTY_OBSERVER = {\n  closed: true,\n  next: noop,\n  error: defaultErrorHandler,\n  complete: noop\n};", "map": {"version": 3, "names": ["isFunction", "isSubscription", "Subscription", "config", "reportUnhandledError", "noop", "nextNotification", "errorNotification", "COMPLETE_NOTIFICATION", "timeout<PERSON>rovider", "captureError", "Subscriber", "constructor", "destination", "isStopped", "add", "EMPTY_OBSERVER", "create", "next", "error", "complete", "SafeSubscriber", "value", "handleStoppedNotification", "_next", "err", "_error", "_complete", "unsubscribe", "closed", "_bind", "Function", "prototype", "bind", "fn", "thisArg", "call", "ConsumerObserver", "partialObserver", "handleUnhandledError", "observerOrNext", "undefined", "context", "useDeprecatedNextContext", "Object", "useDeprecatedSynchronousErrorHandling", "defaultErrorHandler", "notification", "subscriber", "onStoppedNotification", "setTimeout"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/rxjs/dist/esm/internal/Subscriber.js"], "sourcesContent": ["import { isFunction } from './util/isFunction';\nimport { isSubscription, Subscription } from './Subscription';\nimport { config } from './config';\nimport { reportUnhandledError } from './util/reportUnhandledError';\nimport { noop } from './util/noop';\nimport { nextNotification, errorNotification, COMPLETE_NOTIFICATION } from './NotificationFactories';\nimport { timeoutProvider } from './scheduler/timeoutProvider';\nimport { captureError } from './util/errorContext';\nexport class Subscriber extends Subscription {\n    constructor(destination) {\n        super();\n        this.isStopped = false;\n        if (destination) {\n            this.destination = destination;\n            if (isSubscription(destination)) {\n                destination.add(this);\n            }\n        }\n        else {\n            this.destination = EMPTY_OBSERVER;\n        }\n    }\n    static create(next, error, complete) {\n        return new SafeSubscriber(next, error, complete);\n    }\n    next(value) {\n        if (this.isStopped) {\n            handleStoppedNotification(nextNotification(value), this);\n        }\n        else {\n            this._next(value);\n        }\n    }\n    error(err) {\n        if (this.isStopped) {\n            handleStoppedNotification(errorNotification(err), this);\n        }\n        else {\n            this.isStopped = true;\n            this._error(err);\n        }\n    }\n    complete() {\n        if (this.isStopped) {\n            handleStoppedNotification(COMPLETE_NOTIFICATION, this);\n        }\n        else {\n            this.isStopped = true;\n            this._complete();\n        }\n    }\n    unsubscribe() {\n        if (!this.closed) {\n            this.isStopped = true;\n            super.unsubscribe();\n            this.destination = null;\n        }\n    }\n    _next(value) {\n        this.destination.next(value);\n    }\n    _error(err) {\n        try {\n            this.destination.error(err);\n        }\n        finally {\n            this.unsubscribe();\n        }\n    }\n    _complete() {\n        try {\n            this.destination.complete();\n        }\n        finally {\n            this.unsubscribe();\n        }\n    }\n}\nconst _bind = Function.prototype.bind;\nfunction bind(fn, thisArg) {\n    return _bind.call(fn, thisArg);\n}\nclass ConsumerObserver {\n    constructor(partialObserver) {\n        this.partialObserver = partialObserver;\n    }\n    next(value) {\n        const { partialObserver } = this;\n        if (partialObserver.next) {\n            try {\n                partialObserver.next(value);\n            }\n            catch (error) {\n                handleUnhandledError(error);\n            }\n        }\n    }\n    error(err) {\n        const { partialObserver } = this;\n        if (partialObserver.error) {\n            try {\n                partialObserver.error(err);\n            }\n            catch (error) {\n                handleUnhandledError(error);\n            }\n        }\n        else {\n            handleUnhandledError(err);\n        }\n    }\n    complete() {\n        const { partialObserver } = this;\n        if (partialObserver.complete) {\n            try {\n                partialObserver.complete();\n            }\n            catch (error) {\n                handleUnhandledError(error);\n            }\n        }\n    }\n}\nexport class SafeSubscriber extends Subscriber {\n    constructor(observerOrNext, error, complete) {\n        super();\n        let partialObserver;\n        if (isFunction(observerOrNext) || !observerOrNext) {\n            partialObserver = {\n                next: (observerOrNext !== null && observerOrNext !== void 0 ? observerOrNext : undefined),\n                error: error !== null && error !== void 0 ? error : undefined,\n                complete: complete !== null && complete !== void 0 ? complete : undefined,\n            };\n        }\n        else {\n            let context;\n            if (this && config.useDeprecatedNextContext) {\n                context = Object.create(observerOrNext);\n                context.unsubscribe = () => this.unsubscribe();\n                partialObserver = {\n                    next: observerOrNext.next && bind(observerOrNext.next, context),\n                    error: observerOrNext.error && bind(observerOrNext.error, context),\n                    complete: observerOrNext.complete && bind(observerOrNext.complete, context),\n                };\n            }\n            else {\n                partialObserver = observerOrNext;\n            }\n        }\n        this.destination = new ConsumerObserver(partialObserver);\n    }\n}\nfunction handleUnhandledError(error) {\n    if (config.useDeprecatedSynchronousErrorHandling) {\n        captureError(error);\n    }\n    else {\n        reportUnhandledError(error);\n    }\n}\nfunction defaultErrorHandler(err) {\n    throw err;\n}\nfunction handleStoppedNotification(notification, subscriber) {\n    const { onStoppedNotification } = config;\n    onStoppedNotification && timeoutProvider.setTimeout(() => onStoppedNotification(notification, subscriber));\n}\nexport const EMPTY_OBSERVER = {\n    closed: true,\n    next: noop,\n    error: defaultErrorHandler,\n    complete: noop,\n};\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,mBAA3B;AACA,SAASC,cAAT,EAAyBC,YAAzB,QAA6C,gBAA7C;AACA,SAASC,MAAT,QAAuB,UAAvB;AACA,SAASC,oBAAT,QAAqC,6BAArC;AACA,SAASC,IAAT,QAAqB,aAArB;AACA,SAASC,gBAAT,EAA2BC,iBAA3B,EAA8CC,qBAA9C,QAA2E,yBAA3E;AACA,SAASC,eAAT,QAAgC,6BAAhC;AACA,SAASC,YAAT,QAA6B,qBAA7B;AACA,OAAO,MAAMC,UAAN,SAAyBT,YAAzB,CAAsC;EACzCU,WAAW,CAACC,WAAD,EAAc;IACrB;IACA,KAAKC,SAAL,GAAiB,KAAjB;;IACA,IAAID,WAAJ,EAAiB;MACb,KAAKA,WAAL,GAAmBA,WAAnB;;MACA,IAAIZ,cAAc,CAACY,WAAD,CAAlB,EAAiC;QAC7BA,WAAW,CAACE,GAAZ,CAAgB,IAAhB;MACH;IACJ,CALD,MAMK;MACD,KAAKF,WAAL,GAAmBG,cAAnB;IACH;EACJ;;EACY,OAANC,MAAM,CAACC,IAAD,EAAOC,KAAP,EAAcC,QAAd,EAAwB;IACjC,OAAO,IAAIC,cAAJ,CAAmBH,IAAnB,EAAyBC,KAAzB,EAAgCC,QAAhC,CAAP;EACH;;EACDF,IAAI,CAACI,KAAD,EAAQ;IACR,IAAI,KAAKR,SAAT,EAAoB;MAChBS,yBAAyB,CAACjB,gBAAgB,CAACgB,KAAD,CAAjB,EAA0B,IAA1B,CAAzB;IACH,CAFD,MAGK;MACD,KAAKE,KAAL,CAAWF,KAAX;IACH;EACJ;;EACDH,KAAK,CAACM,GAAD,EAAM;IACP,IAAI,KAAKX,SAAT,EAAoB;MAChBS,yBAAyB,CAAChB,iBAAiB,CAACkB,GAAD,CAAlB,EAAyB,IAAzB,CAAzB;IACH,CAFD,MAGK;MACD,KAAKX,SAAL,GAAiB,IAAjB;;MACA,KAAKY,MAAL,CAAYD,GAAZ;IACH;EACJ;;EACDL,QAAQ,GAAG;IACP,IAAI,KAAKN,SAAT,EAAoB;MAChBS,yBAAyB,CAACf,qBAAD,EAAwB,IAAxB,CAAzB;IACH,CAFD,MAGK;MACD,KAAKM,SAAL,GAAiB,IAAjB;;MACA,KAAKa,SAAL;IACH;EACJ;;EACDC,WAAW,GAAG;IACV,IAAI,CAAC,KAAKC,MAAV,EAAkB;MACd,KAAKf,SAAL,GAAiB,IAAjB;MACA,MAAMc,WAAN;MACA,KAAKf,WAAL,GAAmB,IAAnB;IACH;EACJ;;EACDW,KAAK,CAACF,KAAD,EAAQ;IACT,KAAKT,WAAL,CAAiBK,IAAjB,CAAsBI,KAAtB;EACH;;EACDI,MAAM,CAACD,GAAD,EAAM;IACR,IAAI;MACA,KAAKZ,WAAL,CAAiBM,KAAjB,CAAuBM,GAAvB;IACH,CAFD,SAGQ;MACJ,KAAKG,WAAL;IACH;EACJ;;EACDD,SAAS,GAAG;IACR,IAAI;MACA,KAAKd,WAAL,CAAiBO,QAAjB;IACH,CAFD,SAGQ;MACJ,KAAKQ,WAAL;IACH;EACJ;;AApEwC;AAsE7C,MAAME,KAAK,GAAGC,QAAQ,CAACC,SAAT,CAAmBC,IAAjC;;AACA,SAASA,IAAT,CAAcC,EAAd,EAAkBC,OAAlB,EAA2B;EACvB,OAAOL,KAAK,CAACM,IAAN,CAAWF,EAAX,EAAeC,OAAf,CAAP;AACH;;AACD,MAAME,gBAAN,CAAuB;EACnBzB,WAAW,CAAC0B,eAAD,EAAkB;IACzB,KAAKA,eAAL,GAAuBA,eAAvB;EACH;;EACDpB,IAAI,CAACI,KAAD,EAAQ;IACR,MAAM;MAAEgB;IAAF,IAAsB,IAA5B;;IACA,IAAIA,eAAe,CAACpB,IAApB,EAA0B;MACtB,IAAI;QACAoB,eAAe,CAACpB,IAAhB,CAAqBI,KAArB;MACH,CAFD,CAGA,OAAOH,KAAP,EAAc;QACVoB,oBAAoB,CAACpB,KAAD,CAApB;MACH;IACJ;EACJ;;EACDA,KAAK,CAACM,GAAD,EAAM;IACP,MAAM;MAAEa;IAAF,IAAsB,IAA5B;;IACA,IAAIA,eAAe,CAACnB,KAApB,EAA2B;MACvB,IAAI;QACAmB,eAAe,CAACnB,KAAhB,CAAsBM,GAAtB;MACH,CAFD,CAGA,OAAON,KAAP,EAAc;QACVoB,oBAAoB,CAACpB,KAAD,CAApB;MACH;IACJ,CAPD,MAQK;MACDoB,oBAAoB,CAACd,GAAD,CAApB;IACH;EACJ;;EACDL,QAAQ,GAAG;IACP,MAAM;MAAEkB;IAAF,IAAsB,IAA5B;;IACA,IAAIA,eAAe,CAAClB,QAApB,EAA8B;MAC1B,IAAI;QACAkB,eAAe,CAAClB,QAAhB;MACH,CAFD,CAGA,OAAOD,KAAP,EAAc;QACVoB,oBAAoB,CAACpB,KAAD,CAApB;MACH;IACJ;EACJ;;AAvCkB;;AAyCvB,OAAO,MAAME,cAAN,SAA6BV,UAA7B,CAAwC;EAC3CC,WAAW,CAAC4B,cAAD,EAAiBrB,KAAjB,EAAwBC,QAAxB,EAAkC;IACzC;IACA,IAAIkB,eAAJ;;IACA,IAAItC,UAAU,CAACwC,cAAD,CAAV,IAA8B,CAACA,cAAnC,EAAmD;MAC/CF,eAAe,GAAG;QACdpB,IAAI,EAAGsB,cAAc,KAAK,IAAnB,IAA2BA,cAAc,KAAK,KAAK,CAAnD,GAAuDA,cAAvD,GAAwEC,SADjE;QAEdtB,KAAK,EAAEA,KAAK,KAAK,IAAV,IAAkBA,KAAK,KAAK,KAAK,CAAjC,GAAqCA,KAArC,GAA6CsB,SAFtC;QAGdrB,QAAQ,EAAEA,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,KAAK,KAAK,CAAvC,GAA2CA,QAA3C,GAAsDqB;MAHlD,CAAlB;IAKH,CAND,MAOK;MACD,IAAIC,OAAJ;;MACA,IAAI,QAAQvC,MAAM,CAACwC,wBAAnB,EAA6C;QACzCD,OAAO,GAAGE,MAAM,CAAC3B,MAAP,CAAcuB,cAAd,CAAV;;QACAE,OAAO,CAACd,WAAR,GAAsB,MAAM,KAAKA,WAAL,EAA5B;;QACAU,eAAe,GAAG;UACdpB,IAAI,EAAEsB,cAAc,CAACtB,IAAf,IAAuBe,IAAI,CAACO,cAAc,CAACtB,IAAhB,EAAsBwB,OAAtB,CADnB;UAEdvB,KAAK,EAAEqB,cAAc,CAACrB,KAAf,IAAwBc,IAAI,CAACO,cAAc,CAACrB,KAAhB,EAAuBuB,OAAvB,CAFrB;UAGdtB,QAAQ,EAAEoB,cAAc,CAACpB,QAAf,IAA2Ba,IAAI,CAACO,cAAc,CAACpB,QAAhB,EAA0BsB,OAA1B;QAH3B,CAAlB;MAKH,CARD,MASK;QACDJ,eAAe,GAAGE,cAAlB;MACH;IACJ;;IACD,KAAK3B,WAAL,GAAmB,IAAIwB,gBAAJ,CAAqBC,eAArB,CAAnB;EACH;;AA3B0C;;AA6B/C,SAASC,oBAAT,CAA8BpB,KAA9B,EAAqC;EACjC,IAAIhB,MAAM,CAAC0C,qCAAX,EAAkD;IAC9CnC,YAAY,CAACS,KAAD,CAAZ;EACH,CAFD,MAGK;IACDf,oBAAoB,CAACe,KAAD,CAApB;EACH;AACJ;;AACD,SAAS2B,mBAAT,CAA6BrB,GAA7B,EAAkC;EAC9B,MAAMA,GAAN;AACH;;AACD,SAASF,yBAAT,CAAmCwB,YAAnC,EAAiDC,UAAjD,EAA6D;EACzD,MAAM;IAAEC;EAAF,IAA4B9C,MAAlC;EACA8C,qBAAqB,IAAIxC,eAAe,CAACyC,UAAhB,CAA2B,MAAMD,qBAAqB,CAACF,YAAD,EAAeC,UAAf,CAAtD,CAAzB;AACH;;AACD,OAAO,MAAMhC,cAAc,GAAG;EAC1Ba,MAAM,EAAE,IADkB;EAE1BX,IAAI,EAAEb,IAFoB;EAG1Bc,KAAK,EAAE2B,mBAHmB;EAI1B1B,QAAQ,EAAEf;AAJgB,CAAvB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}