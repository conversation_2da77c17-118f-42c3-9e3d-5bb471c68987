{"ast": null, "code": "import _asyncToGenerator from \"R:/chateye/FrontendAngular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { ChangeDetectorRef } from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { PERFORMANCE_CONFIG } from './config/performance.config';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./services/chat.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/toolbar\";\nimport * as i4 from \"@angular/material/sidenav\";\nimport * as i5 from \"@angular/material/card\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/chips\";\nimport * as i10 from \"@angular/material/tooltip\";\nimport * as i11 from \"@angular/material/select\";\nimport * as i12 from \"@angular/material/core\";\nimport * as i13 from \"./components/login-form/login-form.component\";\nimport * as i14 from \"./components/sidebar/sidebar.component\";\nimport * as i15 from \"./components/message-list/message-list.component\";\nimport * as i16 from \"./components/message-list/message-list-virtual.component\";\nimport * as i17 from \"./components/message-input/message-input.component\";\nimport * as i18 from \"./components/admin-panel/admin-panel.component\";\nimport * as i19 from \"./components/password-change/password-change.component\";\nimport * as i20 from \"./components/message-edit/message-edit.component\";\n\nfunction AppComponent_app_login_form_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-login-form\");\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_1_p_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 29);\n    i0.ɵɵtext(1, \" Please select a group to start chatting \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_1_p_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.currentGroup == null ? null : ctx_r7.currentGroup.description, \" \");\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_1_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function AppComponent_mat_sidenav_container_1_button_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.onShowAdminPanel());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"admin_panel_settings\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_1_app_message_list_virtual_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"app-message-list-virtual\", 32);\n    i0.ɵɵlistener(\"onReply\", function AppComponent_mat_sidenav_container_1_app_message_list_virtual_26_Template_app_message_list_virtual_onReply_0_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.onReply($event));\n    })(\"onAddReaction\", function AppComponent_mat_sidenav_container_1_app_message_list_virtual_26_Template_app_message_list_virtual_onAddReaction_0_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r18 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r18.onAddReaction($event));\n    })(\"onRemoveReaction\", function AppComponent_mat_sidenav_container_1_app_message_list_virtual_26_Template_app_message_list_virtual_onRemoveReaction_0_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r19 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r19.onRemoveReaction($event));\n    })(\"onEdit\", function AppComponent_mat_sidenav_container_1_app_message_list_virtual_26_Template_app_message_list_virtual_onEdit_0_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r20 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r20.onEdit($event));\n    })(\"onDelete\", function AppComponent_mat_sidenav_container_1_app_message_list_virtual_26_Template_app_message_list_virtual_onDelete_0_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r21 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r21.onDelete($event));\n    })(\"onMessageClick\", function AppComponent_mat_sidenav_container_1_app_message_list_virtual_26_Template_app_message_list_virtual_onMessageClick_0_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r22 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r22.onMessageClick($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"messages\", ctx_r9.messages)(\"currentUser\", ctx_r9.user)(\"loading\", ctx_r9.loading)(\"highlightedMessageId\", ctx_r9.highlightedMessageId);\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_1_app_message_list_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"app-message-list\", 32);\n    i0.ɵɵlistener(\"onReply\", function AppComponent_mat_sidenav_container_1_app_message_list_27_Template_app_message_list_onReply_0_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r23.onReply($event));\n    })(\"onAddReaction\", function AppComponent_mat_sidenav_container_1_app_message_list_27_Template_app_message_list_onAddReaction_0_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r25 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r25.onAddReaction($event));\n    })(\"onRemoveReaction\", function AppComponent_mat_sidenav_container_1_app_message_list_27_Template_app_message_list_onRemoveReaction_0_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r26 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r26.onRemoveReaction($event));\n    })(\"onEdit\", function AppComponent_mat_sidenav_container_1_app_message_list_27_Template_app_message_list_onEdit_0_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r27 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r27.onEdit($event));\n    })(\"onDelete\", function AppComponent_mat_sidenav_container_1_app_message_list_27_Template_app_message_list_onDelete_0_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.onDelete($event));\n    })(\"onMessageClick\", function AppComponent_mat_sidenav_container_1_app_message_list_27_Template_app_message_list_onMessageClick_0_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r29 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r29.onMessageClick($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"messages\", ctx_r10.messages)(\"currentUser\", ctx_r10.user)(\"loading\", ctx_r10.loading)(\"highlightedMessageId\", ctx_r10.highlightedMessageId);\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_1_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"mat-card\", 34)(2, \"mat-card-content\")(3, \"div\", 35)(4, \"div\", 36);\n    i0.ɵɵelement(5, \"span\")(6, \"span\")(7, \"span\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 37);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.getTypingText(ctx_r11.typingUsers), \" \");\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_1_app_message_input_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"app-message-input\", 38);\n    i0.ɵɵlistener(\"onSendMessage\", function AppComponent_mat_sidenav_container_1_app_message_input_30_Template_app_message_input_onSendMessage_0_listener($event) {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r30 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r30.onSendMessage($event));\n    })(\"onCancelReply\", function AppComponent_mat_sidenav_container_1_app_message_input_30_Template_app_message_input_onCancelReply_0_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r32 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r32.onCancelReply());\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"replyTo\", ctx_r12.replyTo);\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_1_div_31_mat_option_10_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const group_r36 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" - \", group_r36.description, \"\");\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_1_div_31_mat_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 46);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, AppComponent_mat_sidenav_container_1_div_31_mat_option_10_span_2_Template, 2, 1, \"span\", 47);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const group_r36 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", group_r36.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", group_r36.name, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", group_r36.description);\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_1_div_31_p_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 49);\n    i0.ɵɵtext(1, \" No groups available. Contact an administrator to get access to groups. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_1_div_31_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r40 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function AppComponent_mat_sidenav_container_1_div_31_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r39 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r39.onManualLoadGroups());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Retry Loading Groups \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_1_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r42 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"mat-card\")(2, \"mat-card-content\")(3, \"div\", 40)(4, \"h3\");\n    i0.ɵɵtext(5, \"Select a group to start chatting\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-form-field\", 41)(7, \"mat-label\");\n    i0.ɵɵtext(8, \"Choose a group\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-select\", 42);\n    i0.ɵɵlistener(\"selectionChange\", function AppComponent_mat_sidenav_container_1_div_31_Template_mat_select_selectionChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r42);\n      const ctx_r41 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r41.onJoinGroup($event.value));\n    });\n    i0.ɵɵtemplate(10, AppComponent_mat_sidenav_container_1_div_31_mat_option_10_Template, 3, 3, \"mat-option\", 43);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, AppComponent_mat_sidenav_container_1_div_31_p_11_Template, 2, 0, \"p\", 44);\n    i0.ɵɵtemplate(12, AppComponent_mat_sidenav_container_1_div_31_button_12_Template, 4, 0, \"button\", 45);\n    i0.ɵɵelementEnd()()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"value\", null);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r13.groups);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r13.groups == null ? null : ctx_r13.groups.length) === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r13.groups == null ? null : ctx_r13.groups.length) === 0);\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r44 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"mat-sidenav-container\", 5)(1, \"mat-sidenav\", 6, 7)(3, \"app-sidebar\", 8);\n    i0.ɵɵlistener(\"onPasswordChange\", function AppComponent_mat_sidenav_container_1_Template_app_sidebar_onPasswordChange_3_listener() {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r43 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r43.onPasswordChange());\n    })(\"onLogout\", function AppComponent_mat_sidenav_container_1_Template_app_sidebar_onLogout_3_listener() {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r45 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r45.onLogout());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"mat-sidenav-content\", 9)(5, \"mat-toolbar\", 10)(6, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function AppComponent_mat_sidenav_container_1_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r44);\n\n      const _r5 = i0.ɵɵreference(2);\n\n      return i0.ɵɵresetView(_r5.toggle());\n    });\n    i0.ɵɵelementStart(7, \"mat-icon\");\n    i0.ɵɵtext(8, \"menu\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 12)(10, \"div\", 13)(11, \"h1\", 14);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, AppComponent_mat_sidenav_container_1_p_13_Template, 2, 0, \"p\", 15);\n    i0.ɵɵtemplate(14, AppComponent_mat_sidenav_container_1_p_14_Template, 2, 1, \"p\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 17)(16, \"div\", 18)(17, \"mat-icon\", 19);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\", 20);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"mat-chip-list\")(22, \"mat-chip\", 21);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(24, AppComponent_mat_sidenav_container_1_button_24_Template, 3, 0, \"button\", 22);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 23);\n    i0.ɵɵtemplate(26, AppComponent_mat_sidenav_container_1_app_message_list_virtual_26_Template, 1, 4, \"app-message-list-virtual\", 24);\n    i0.ɵɵtemplate(27, AppComponent_mat_sidenav_container_1_app_message_list_27_Template, 1, 4, \"app-message-list\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(28, AppComponent_mat_sidenav_container_1_div_28_Template, 10, 1, \"div\", 25);\n    i0.ɵɵelementStart(29, \"div\", 26);\n    i0.ɵɵtemplate(30, AppComponent_mat_sidenav_container_1_app_message_input_30_Template, 1, 1, \"app-message-input\", 27);\n    i0.ɵɵtemplate(31, AppComponent_mat_sidenav_container_1_div_31_Template, 13, 4, \"div\", 28);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"onlineUsers\", ctx_r1.onlineUsers)(\"currentUser\", ctx_r1.user)(\"isAdmin\", ctx_r1.isAdmin)(\"groups\", ctx_r1.groups)(\"currentGroup\", ctx_r1.currentGroup)(\"onJoinGroup\", ctx_r1.onJoinGroup.bind(ctx_r1));\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r1.currentGroup == null ? null : ctx_r1.currentGroup.name) || \"Select a Group\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.currentGroup);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentGroup == null ? null : ctx_r1.currentGroup.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"connected\", ctx_r1.connected);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.connected ? \"wifi\" : \"wifi_off\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.connected ? \"Connected\" : \"Disconnected\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r1.onlineUsers == null ? null : ctx_r1.onlineUsers.length) || 0, \" online \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isAdmin);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.useVirtualScrolling);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.useVirtualScrolling);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.typingUsers && ctx_r1.typingUsers.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentGroup);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.currentGroup);\n  }\n}\n\nfunction AppComponent_app_admin_panel_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"app-admin-panel\", 51);\n    i0.ɵɵlistener(\"onClose\", function AppComponent_app_admin_panel_2_Template_app_admin_panel_onClose_0_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r47 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r47.onHideAdminPanel());\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"currentUser\", ctx_r2.user);\n  }\n}\n\nfunction AppComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r50 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"mat-card\", 53)(2, \"mat-card-header\")(3, \"mat-card-title\");\n    i0.ɵɵtext(4, \"Change Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function AppComponent_div_3_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r50);\n      const ctx_r49 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r49.onClosePasswordChange());\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"close\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"mat-card-content\");\n    i0.ɵɵelement(9, \"app-password-change\");\n    i0.ɵɵelementEnd()()();\n  }\n}\n\nfunction AppComponent_app_message_edit_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r52 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"app-message-edit\", 55);\n    i0.ɵɵlistener(\"onSave\", function AppComponent_app_message_edit_4_Template_app_message_edit_onSave_0_listener($event) {\n      i0.ɵɵrestoreView(_r52);\n      const ctx_r51 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r51.onSaveEdit($event));\n    })(\"onCancel\", function AppComponent_app_message_edit_4_Template_app_message_edit_onCancel_0_listener() {\n      i0.ɵɵrestoreView(_r52);\n      const ctx_r53 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r53.onCancelEdit());\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"message\", ctx_r4.editingMessage);\n  }\n}\n\nexport class AppComponent {\n  constructor(chatService, cdr) {\n    this.chatService = chatService;\n    this.cdr = cdr; // Cached values to reduce async pipe usage\n\n    this.user = null;\n    this.messages = [];\n    this.onlineUsers = [];\n    this.groups = [];\n    this.currentGroup = null;\n    this.replyTo = null;\n    this.loading = false;\n    this.isAdmin = false;\n    this.showAdminPanel = false;\n    this.connected = false;\n    this.isLoggedIn = false;\n    this.showPasswordChange = false;\n    this.editingMessage = null;\n    this.highlightedMessageId = null;\n    this.typingUsers = []; // Performance configuration\n\n    this.useVirtualScrolling = PERFORMANCE_CONFIG.enableVirtualScrolling;\n    this.subscriptions = [];\n  }\n\n  ngOnInit() {\n    // Subscribe to all observables and cache values to reduce async pipe usage\n    this.subscriptions.push(this.chatService.user$.subscribe(user => {\n      this.user = user;\n      this.cdr.markForCheck();\n    }), this.chatService.messages$.subscribe(messages => {\n      this.messages = messages;\n      this.cdr.markForCheck();\n    }), this.chatService.onlineUsers$.subscribe(users => {\n      this.onlineUsers = users;\n      this.cdr.markForCheck();\n    }), this.chatService.groups$.subscribe(groups => {\n      this.groups = groups;\n      this.cdr.markForCheck();\n    }), this.chatService.currentGroup$.subscribe(group => {\n      this.currentGroup = group;\n      this.cdr.markForCheck();\n    }), this.chatService.replyTo$.subscribe(replyTo => {\n      this.replyTo = replyTo;\n      this.cdr.markForCheck();\n    }), this.chatService.loading$.subscribe(loading => {\n      this.loading = loading;\n      this.cdr.markForCheck();\n    }), this.chatService.isAdmin$.subscribe(isAdmin => {\n      this.isAdmin = isAdmin;\n      this.cdr.markForCheck();\n    }), this.chatService.showAdminPanel$.subscribe(showPanel => {\n      this.showAdminPanel = showPanel;\n      this.cdr.markForCheck();\n    }), this.chatService.connected$.subscribe(connected => {\n      this.connected = connected;\n      this.cdr.markForCheck();\n    }), this.chatService.isLoggedIn$.subscribe(isLoggedIn => {\n      this.isLoggedIn = isLoggedIn;\n      this.cdr.markForCheck();\n    }), this.chatService.editingMessage$.subscribe(editingMessage => {\n      this.editingMessage = editingMessage;\n      this.cdr.markForCheck();\n    }), this.chatService.typingUsers$.subscribe(typingUsers => {\n      this.typingUsers = typingUsers;\n      this.cdr.markForCheck();\n    }));\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.chatService.logout();\n  }\n\n  onJoinGroup(groupId) {\n    this.chatService.joinGroup(groupId);\n  }\n\n  onSendMessage(event) {\n    this.chatService.sendMessage(event.text, event.replyToId);\n  }\n\n  onReply(message) {\n    this.chatService.replyToMessage(message); // Highlight the replied-to message\n\n    this.highlightedMessageId = message.id; // Clear highlight after 3 seconds\n\n    setTimeout(() => {\n      this.highlightedMessageId = null;\n    }, 3000);\n  }\n\n  onCancelReply() {\n    this.chatService.cancelReply();\n  }\n\n  onAddReaction(event) {\n    this.chatService.addReaction(event.messageId, event.emoji);\n  }\n\n  onRemoveReaction(data) {\n    this.chatService.removeReaction(data);\n  }\n\n  onEdit(message) {\n    this.chatService.startEditingMessage(message);\n  }\n\n  onSaveEdit(newText) {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        if (_this.editingMessage) {\n          yield _this.chatService.updateMessage(_this.editingMessage.id, newText);\n\n          _this.chatService.cancelEditingMessage();\n        }\n      } catch (error) {\n        console.error('Failed to edit message:', error);\n        alert('Failed to edit message. Please try again.');\n      }\n    })();\n  }\n\n  onCancelEdit() {\n    this.chatService.cancelEditingMessage();\n  }\n\n  onDelete(message) {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        if (confirm('Are you sure you want to delete this message?')) {\n          yield _this2.chatService.deleteMessage(message.id);\n        }\n      } catch (error) {\n        console.error('Failed to delete message:', error);\n        alert('Failed to delete message. Please try again.');\n      }\n    })();\n  }\n\n  onShowAdminPanel() {\n    this.chatService.showAdminPanel();\n  }\n\n  onHideAdminPanel() {\n    this.chatService.hideAdminPanel();\n  }\n\n  onPasswordChange() {\n    this.showPasswordChange = true;\n  }\n\n  onClosePasswordChange() {\n    this.showPasswordChange = false;\n  }\n\n  onLogout() {\n    this.chatService.logout();\n  }\n\n  onMessageClick(message) {\n    this.highlightedMessageId = message.id; // Clear highlight after 3 seconds\n\n    setTimeout(() => {\n      this.highlightedMessageId = null;\n    }, 3000);\n  }\n\n  onManualLoadGroups() {\n    if (this.user) {\n      console.log('Manually loading groups for:', this.user);\n      this.chatService.loadUserGroups(this.user);\n    }\n  }\n\n  getTypingText(typingUsers) {\n    if (typingUsers.length === 0) return '';\n\n    if (typingUsers.length === 1) {\n      return `${typingUsers[0]} is typing...`;\n    } else if (typingUsers.length === 2) {\n      return `${typingUsers[0]} and ${typingUsers[1]} are typing...`;\n    } else {\n      return `${typingUsers[0]} and ${typingUsers.length - 1} others are typing...`;\n    }\n  }\n\n}\n\nAppComponent.ɵfac = function AppComponent_Factory(t) {\n  return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.ChatService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n\nAppComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: AppComponent,\n  selectors: [[\"app-root\"]],\n  decls: 5,\n  vars: 5,\n  consts: [[4, \"ngIf\"], [\"class\", \"chat-container\", 4, \"ngIf\"], [3, \"currentUser\", \"onClose\", 4, \"ngIf\"], [\"class\", \"modal-overlay\", 4, \"ngIf\"], [3, \"message\", \"onSave\", \"onCancel\", 4, \"ngIf\"], [1, \"chat-container\"], [\"mode\", \"side\", \"opened\", \"true\", 1, \"chat-sidebar\"], [\"sidenav\", \"\"], [3, \"onlineUsers\", \"currentUser\", \"isAdmin\", \"groups\", \"currentGroup\", \"onJoinGroup\", \"onPasswordChange\", \"onLogout\"], [1, \"chat-main-content\"], [1, \"chat-header\"], [\"mat-icon-button\", \"\", 1, \"menu-button\", 3, \"click\"], [1, \"header-content\"], [1, \"group-info\"], [1, \"group-name\"], [\"class\", \"no-group-message\", 4, \"ngIf\"], [\"class\", \"group-description\", 4, \"ngIf\"], [1, \"header-actions\"], [1, \"connection-status\"], [1, \"status-icon\"], [1, \"status-text\"], [1, \"online-count\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Admin Panel\", 3, \"click\", 4, \"ngIf\"], [1, \"messages-container\"], [3, \"messages\", \"currentUser\", \"loading\", \"highlightedMessageId\", \"onReply\", \"onAddReaction\", \"onRemoveReaction\", \"onEdit\", \"onDelete\", \"onMessageClick\", 4, \"ngIf\"], [\"class\", \"typing-indicator\", 4, \"ngIf\"], [1, \"input-container\"], [3, \"replyTo\", \"onSendMessage\", \"onCancelReply\", 4, \"ngIf\"], [\"class\", \"no-group-input\", 4, \"ngIf\"], [1, \"no-group-message\"], [1, \"group-description\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Admin Panel\", 3, \"click\"], [3, \"messages\", \"currentUser\", \"loading\", \"highlightedMessageId\", \"onReply\", \"onAddReaction\", \"onRemoveReaction\", \"onEdit\", \"onDelete\", \"onMessageClick\"], [1, \"typing-indicator\"], [1, \"typing-card\"], [1, \"typing-content\"], [1, \"typing-dots\"], [1, \"typing-text\"], [3, \"replyTo\", \"onSendMessage\", \"onCancelReply\"], [1, \"no-group-input\"], [1, \"group-selection\"], [\"appearance\", \"outline\", 1, \"group-selector\"], [3, \"value\", \"selectionChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"no-groups-message\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"class\", \"retry-button\", 3, \"click\", 4, \"ngIf\"], [3, \"value\"], [\"class\", \"group-description-small\", 4, \"ngIf\"], [1, \"group-description-small\"], [1, \"no-groups-message\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"retry-button\", 3, \"click\"], [3, \"currentUser\", \"onClose\"], [1, \"modal-overlay\"], [1, \"modal-card\"], [\"mat-icon-button\", \"\", 1, \"close-button\", 3, \"click\"], [3, \"message\", \"onSave\", \"onCancel\"]],\n  template: function AppComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, AppComponent_app_login_form_0_Template, 1, 0, \"app-login-form\", 0);\n      i0.ɵɵtemplate(1, AppComponent_mat_sidenav_container_1_Template, 32, 20, \"mat-sidenav-container\", 1);\n      i0.ɵɵtemplate(2, AppComponent_app_admin_panel_2_Template, 1, 1, \"app-admin-panel\", 2);\n      i0.ɵɵtemplate(3, AppComponent_div_3_Template, 10, 0, \"div\", 3);\n      i0.ɵɵtemplate(4, AppComponent_app_message_edit_4_Template, 1, 1, \"app-message-edit\", 4);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", !ctx.isLoggedIn);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.isLoggedIn);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showAdminPanel);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showPasswordChange);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.editingMessage);\n    }\n  },\n  dependencies: [i2.NgForOf, i2.NgIf, i3.MatToolbar, i4.MatSidenav, i4.MatSidenavContainer, i4.MatSidenavContent, i5.MatCard, i5.MatCardHeader, i5.MatCardContent, i5.MatCardTitle, i6.MatFormField, i6.MatLabel, i7.MatButton, i8.MatIcon, i9.MatChipList, i9.MatChip, i10.MatTooltip, i11.MatSelect, i12.MatOption, i13.LoginFormComponent, i14.SidebarComponent, i15.MessageListComponent, i16.MessageListVirtualComponent, i17.MessageInputComponent, i18.AdminPanelComponent, i19.PasswordChangeComponent, i20.MessageEditComponent],\n  styles: [\".chat-container[_ngcontent-%COMP%] {\\r\\n  height: 100vh;\\r\\n  width: 100vw;\\r\\n  background: #f5f5f5;\\r\\n}\\r\\n.chat-sidebar[_ngcontent-%COMP%] {\\r\\n  width: 280px;\\r\\n  min-width: 280px;\\r\\n  background: white;\\r\\n  border-right: 1px solid #e0e0e0;\\r\\n  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);\\r\\n}\\r\\n.chat-main-content[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  height: 100vh;\\r\\n  background: white;\\r\\n}\\r\\n.chat-header[_ngcontent-%COMP%] {\\r\\n  background: linear-gradient(135deg, #3f51b5 0%, #5c6bc0 100%);\\r\\n  color: white;\\r\\n  padding: 0 16px;\\r\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\r\\n  z-index: 10;\\r\\n}\\r\\n.menu-button[_ngcontent-%COMP%] {\\r\\n  margin-right: 16px;\\r\\n}\\r\\n.header-content[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: space-between;\\r\\n  align-items: center;\\r\\n  flex: 1;\\r\\n  min-width: 0;\\r\\n}\\r\\n.group-info[_ngcontent-%COMP%] {\\r\\n  flex: 1;\\r\\n  min-width: 0;\\r\\n  margin-right: 16px;\\r\\n}\\r\\n.group-name[_ngcontent-%COMP%] {\\r\\n  font-size: 1.25rem;\\r\\n  font-weight: 500;\\r\\n  margin: 0 0 4px 0;\\r\\n  line-height: 1.2;\\r\\n}\\r\\n.no-group-message[_ngcontent-%COMP%] {\\r\\n  color: rgba(255, 255, 255, 0.8);\\r\\n  font-size: 0.875rem;\\r\\n  margin: 0;\\r\\n  font-weight: 400;\\r\\n}\\r\\n.group-description[_ngcontent-%COMP%] {\\r\\n  color: rgba(255, 255, 255, 0.9);\\r\\n  font-size: 0.75rem;\\r\\n  margin: 0;\\r\\n  opacity: 0.9;\\r\\n}\\r\\n.header-actions[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 16px;\\r\\n  flex-shrink: 0;\\r\\n}\\r\\n.connection-status[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 8px;\\r\\n  background: rgba(255, 255, 255, 0.1);\\r\\n  padding: 8px 12px;\\r\\n  border-radius: 16px;\\r\\n  backdrop-filter: blur(10px);\\r\\n}\\r\\n.status-icon[_ngcontent-%COMP%] {\\r\\n  font-size: 18px;\\r\\n  width: 18px;\\r\\n  height: 18px;\\r\\n  color: #ef4444;\\r\\n  transition: all 0.3s ease;\\r\\n}\\r\\n.status-icon.connected[_ngcontent-%COMP%] {\\r\\n  color: #10b981;\\r\\n}\\r\\n.status-text[_ngcontent-%COMP%] {\\r\\n  font-size: 0.75rem;\\r\\n  font-weight: 500;\\r\\n}\\r\\n.online-count[_ngcontent-%COMP%] {\\r\\n  background: rgba(255, 255, 255, 0.1);\\r\\n  color: white;\\r\\n  font-size: 0.75rem;\\r\\n  font-weight: 500;\\r\\n}\\r\\n.messages-container[_ngcontent-%COMP%] {\\r\\n  flex: 1;\\r\\n  min-height: 0;\\r\\n  background: #f8f9fa;\\r\\n  overflow: hidden;\\r\\n  position: relative;\\r\\n}\\r\\n.input-container[_ngcontent-%COMP%] {\\r\\n  background: white;\\r\\n  border-top: 1px solid #e0e0e0;\\r\\n  padding: 16px;\\r\\n}\\r\\n.no-group-input[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  height: 60px;\\r\\n}\\r\\n.group-selection[_ngcontent-%COMP%] {\\r\\n  text-align: center;\\r\\n  padding: 20px;\\r\\n}\\r\\n.group-selection[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\r\\n  margin: 0 0 16px 0;\\r\\n  color: #333;\\r\\n  font-weight: 500;\\r\\n}\\r\\n.group-selector[_ngcontent-%COMP%] {\\r\\n  width: 100%;\\r\\n  max-width: 300px;\\r\\n  margin-bottom: 16px;\\r\\n}\\r\\n.group-description-small[_ngcontent-%COMP%] {\\r\\n  color: #666;\\r\\n  font-size: 0.8rem;\\r\\n}\\r\\n.no-groups-message[_ngcontent-%COMP%] {\\r\\n  color: #666;\\r\\n  font-style: italic;\\r\\n  margin: 0 0 16px 0;\\r\\n}\\r\\n.retry-button[_ngcontent-%COMP%] {\\r\\n  margin-top: 8px;\\r\\n}\\r\\n.modal-overlay[_ngcontent-%COMP%] {\\r\\n  position: fixed;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  right: 0;\\r\\n  bottom: 0;\\r\\n  background: rgba(0, 0, 0, 0.5);\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  z-index: 1000;\\r\\n}\\r\\n.modal-card[_ngcontent-%COMP%] {\\r\\n  width: 90%;\\r\\n  max-width: 500px;\\r\\n  max-height: 90vh;\\r\\n  overflow-y: auto;\\r\\n}\\r\\n.close-button[_ngcontent-%COMP%] {\\r\\n  position: absolute;\\r\\n  top: 8px;\\r\\n  right: 8px;\\r\\n}\\r\\n\\r\\n@media (max-width: 1024px) {\\r\\n  .chat-sidebar[_ngcontent-%COMP%] {\\r\\n    width: 260px;\\r\\n    min-width: 260px;\\r\\n  }\\r\\n  \\r\\n  .group-name[_ngcontent-%COMP%] {\\r\\n    font-size: 1.125rem;\\r\\n  }\\r\\n  \\r\\n  .header-actions[_ngcontent-%COMP%] {\\r\\n    gap: 12px;\\r\\n  }\\r\\n}\\r\\n@media (max-width: 768px) {\\r\\n  .chat-sidebar[_ngcontent-%COMP%] {\\r\\n    width: 100%;\\r\\n    min-width: 100%;\\r\\n  }\\r\\n  \\r\\n  .chat-main-content[_ngcontent-%COMP%] {\\r\\n    height: calc(100vh - 60px);\\r\\n  }\\r\\n  \\r\\n  .group-name[_ngcontent-%COMP%] {\\r\\n    font-size: 1rem;\\r\\n  }\\r\\n  \\r\\n  .header-actions[_ngcontent-%COMP%] {\\r\\n    gap: 8px;\\r\\n  }\\r\\n  \\r\\n  .connection-status[_ngcontent-%COMP%] {\\r\\n    padding: 6px 10px;\\r\\n  }\\r\\n  \\r\\n  .status-text[_ngcontent-%COMP%] {\\r\\n    font-size: 0.7rem;\\r\\n  }\\r\\n  \\r\\n  .online-count[_ngcontent-%COMP%] {\\r\\n    font-size: 0.7rem;\\r\\n  }\\r\\n}\\r\\n@media (max-width: 480px) {\\r\\n  .header-content[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n    align-items: flex-start;\\r\\n    gap: 8px;\\r\\n  }\\r\\n  \\r\\n  .header-actions[_ngcontent-%COMP%] {\\r\\n    width: 100%;\\r\\n    justify-content: space-between;\\r\\n  }\\r\\n  \\r\\n  .group-name[_ngcontent-%COMP%] {\\r\\n    font-size: 0.9rem;\\r\\n  }\\r\\n  \\r\\n  .chat-header[_ngcontent-%COMP%] {\\r\\n    padding: 0 8px;\\r\\n  }\\r\\n  \\r\\n  .input-container[_ngcontent-%COMP%] {\\r\\n    padding: 12px;\\r\\n  }\\r\\n}\\r\\n\\r\\n*[_ngcontent-%COMP%] {\\r\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\r\\n}\\r\\n\\r\\n[_ngcontent-%COMP%]::-webkit-scrollbar {\\r\\n  width: 6px;\\r\\n}\\r\\n[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\r\\n  background: transparent;\\r\\n}\\r\\n[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\r\\n  background: rgba(0, 0, 0, 0.2);\\r\\n  border-radius: 3px;\\r\\n}\\r\\n[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\r\\n  background: rgba(0, 0, 0, 0.3);\\r\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFwcC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLG1DQUFtQztBQUNuQztFQUNFLGFBQWE7RUFDYixZQUFZO0VBQ1osbUJBQW1CO0FBQ3JCO0FBRUE7RUFDRSxZQUFZO0VBQ1osZ0JBQWdCO0VBQ2hCLGlCQUFpQjtFQUNqQiwrQkFBK0I7RUFDL0Isd0NBQXdDO0FBQzFDO0FBRUE7RUFDRSxhQUFhO0VBQ2Isc0JBQXNCO0VBQ3RCLGFBQWE7RUFDYixpQkFBaUI7QUFDbkI7QUFFQTtFQUNFLDZEQUE2RDtFQUM3RCxZQUFZO0VBQ1osZUFBZTtFQUNmLHdDQUF3QztFQUN4QyxXQUFXO0FBQ2I7QUFFQTtFQUNFLGtCQUFrQjtBQUNwQjtBQUVBO0VBQ0UsYUFBYTtFQUNiLDhCQUE4QjtFQUM5QixtQkFBbUI7RUFDbkIsT0FBTztFQUNQLFlBQVk7QUFDZDtBQUVBO0VBQ0UsT0FBTztFQUNQLFlBQVk7RUFDWixrQkFBa0I7QUFDcEI7QUFFQTtFQUNFLGtCQUFrQjtFQUNsQixnQkFBZ0I7RUFDaEIsaUJBQWlCO0VBQ2pCLGdCQUFnQjtBQUNsQjtBQUVBO0VBQ0UsK0JBQStCO0VBQy9CLG1CQUFtQjtFQUNuQixTQUFTO0VBQ1QsZ0JBQWdCO0FBQ2xCO0FBRUE7RUFDRSwrQkFBK0I7RUFDL0Isa0JBQWtCO0VBQ2xCLFNBQVM7RUFDVCxZQUFZO0FBQ2Q7QUFFQTtFQUNFLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsU0FBUztFQUNULGNBQWM7QUFDaEI7QUFFQTtFQUNFLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsUUFBUTtFQUNSLG9DQUFvQztFQUNwQyxpQkFBaUI7RUFDakIsbUJBQW1CO0VBQ25CLDJCQUEyQjtBQUM3QjtBQUVBO0VBQ0UsZUFBZTtFQUNmLFdBQVc7RUFDWCxZQUFZO0VBQ1osY0FBYztFQUNkLHlCQUF5QjtBQUMzQjtBQUVBO0VBQ0UsY0FBYztBQUNoQjtBQUVBO0VBQ0Usa0JBQWtCO0VBQ2xCLGdCQUFnQjtBQUNsQjtBQUVBO0VBQ0Usb0NBQW9DO0VBQ3BDLFlBQVk7RUFDWixrQkFBa0I7RUFDbEIsZ0JBQWdCO0FBQ2xCO0FBRUE7RUFDRSxPQUFPO0VBQ1AsYUFBYTtFQUNiLG1CQUFtQjtFQUNuQixnQkFBZ0I7RUFDaEIsa0JBQWtCO0FBQ3BCO0FBRUE7RUFDRSxpQkFBaUI7RUFDakIsNkJBQTZCO0VBQzdCLGFBQWE7QUFDZjtBQUVBO0VBQ0UsYUFBYTtFQUNiLG1CQUFtQjtFQUNuQix1QkFBdUI7RUFDdkIsWUFBWTtBQUNkO0FBRUE7RUFDRSxrQkFBa0I7RUFDbEIsYUFBYTtBQUNmO0FBRUE7RUFDRSxrQkFBa0I7RUFDbEIsV0FBVztFQUNYLGdCQUFnQjtBQUNsQjtBQUVBO0VBQ0UsV0FBVztFQUNYLGdCQUFnQjtFQUNoQixtQkFBbUI7QUFDckI7QUFFQTtFQUNFLFdBQVc7RUFDWCxpQkFBaUI7QUFDbkI7QUFFQTtFQUNFLFdBQVc7RUFDWCxrQkFBa0I7RUFDbEIsa0JBQWtCO0FBQ3BCO0FBRUE7RUFDRSxlQUFlO0FBQ2pCO0FBRUE7RUFDRSxlQUFlO0VBQ2YsTUFBTTtFQUNOLE9BQU87RUFDUCxRQUFRO0VBQ1IsU0FBUztFQUNULDhCQUE4QjtFQUM5QixhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLHVCQUF1QjtFQUN2QixhQUFhO0FBQ2Y7QUFFQTtFQUNFLFVBQVU7RUFDVixnQkFBZ0I7RUFDaEIsZ0JBQWdCO0VBQ2hCLGdCQUFnQjtBQUNsQjtBQUVBO0VBQ0Usa0JBQWtCO0VBQ2xCLFFBQVE7RUFDUixVQUFVO0FBQ1o7QUFFQSxzQkFBc0I7QUFDdEI7RUFDRTtJQUNFLFlBQVk7SUFDWixnQkFBZ0I7RUFDbEI7O0VBRUE7SUFDRSxtQkFBbUI7RUFDckI7O0VBRUE7SUFDRSxTQUFTO0VBQ1g7QUFDRjtBQUVBO0VBQ0U7SUFDRSxXQUFXO0lBQ1gsZUFBZTtFQUNqQjs7RUFFQTtJQUNFLDBCQUEwQjtFQUM1Qjs7RUFFQTtJQUNFLGVBQWU7RUFDakI7O0VBRUE7SUFDRSxRQUFRO0VBQ1Y7O0VBRUE7SUFDRSxpQkFBaUI7RUFDbkI7O0VBRUE7SUFDRSxpQkFBaUI7RUFDbkI7O0VBRUE7SUFDRSxpQkFBaUI7RUFDbkI7QUFDRjtBQUVBO0VBQ0U7SUFDRSxzQkFBc0I7SUFDdEIsdUJBQXVCO0lBQ3ZCLFFBQVE7RUFDVjs7RUFFQTtJQUNFLFdBQVc7SUFDWCw4QkFBOEI7RUFDaEM7O0VBRUE7SUFDRSxpQkFBaUI7RUFDbkI7O0VBRUE7SUFDRSxjQUFjO0VBQ2hCOztFQUVBO0lBQ0UsYUFBYTtFQUNmO0FBQ0Y7QUFFQSxzQkFBc0I7QUFDdEI7RUFDRSxpREFBaUQ7QUFDbkQ7QUFFQSx5Q0FBeUM7QUFDekM7RUFDRSxVQUFVO0FBQ1o7QUFFQTtFQUNFLHVCQUF1QjtBQUN6QjtBQUVBO0VBQ0UsOEJBQThCO0VBQzlCLGtCQUFrQjtBQUNwQjtBQUVBO0VBQ0UsOEJBQThCO0FBQ2hDIiwiZmlsZSI6ImFwcC5jb21wb25lbnQuY3NzIiwic291cmNlc0NvbnRlbnQiOlsiLyogTWF0ZXJpYWwgRGVzaWduIENoYXQgQ29udGFpbmVyICovXHJcbi5jaGF0LWNvbnRhaW5lciB7XHJcbiAgaGVpZ2h0OiAxMDB2aDtcclxuICB3aWR0aDogMTAwdnc7XHJcbiAgYmFja2dyb3VuZDogI2Y1ZjVmNTtcclxufVxyXG5cclxuLmNoYXQtc2lkZWJhciB7XHJcbiAgd2lkdGg6IDI4MHB4O1xyXG4gIG1pbi13aWR0aDogMjgwcHg7XHJcbiAgYmFja2dyb3VuZDogd2hpdGU7XHJcbiAgYm9yZGVyLXJpZ2h0OiAxcHggc29saWQgI2UwZTBlMDtcclxuICBib3gtc2hhZG93OiAycHggMCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xyXG59XHJcblxyXG4uY2hhdC1tYWluLWNvbnRlbnQge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICBoZWlnaHQ6IDEwMHZoO1xyXG4gIGJhY2tncm91bmQ6IHdoaXRlO1xyXG59XHJcblxyXG4uY2hhdC1oZWFkZXIge1xyXG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMzZjUxYjUgMCUsICM1YzZiYzAgMTAwJSk7XHJcbiAgY29sb3I6IHdoaXRlO1xyXG4gIHBhZGRpbmc6IDAgMTZweDtcclxuICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xyXG4gIHotaW5kZXg6IDEwO1xyXG59XHJcblxyXG4ubWVudS1idXR0b24ge1xyXG4gIG1hcmdpbi1yaWdodDogMTZweDtcclxufVxyXG5cclxuLmhlYWRlci1jb250ZW50IHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIGZsZXg6IDE7XHJcbiAgbWluLXdpZHRoOiAwO1xyXG59XHJcblxyXG4uZ3JvdXAtaW5mbyB7XHJcbiAgZmxleDogMTtcclxuICBtaW4td2lkdGg6IDA7XHJcbiAgbWFyZ2luLXJpZ2h0OiAxNnB4O1xyXG59XHJcblxyXG4uZ3JvdXAtbmFtZSB7XHJcbiAgZm9udC1zaXplOiAxLjI1cmVtO1xyXG4gIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgbWFyZ2luOiAwIDAgNHB4IDA7XHJcbiAgbGluZS1oZWlnaHQ6IDEuMjtcclxufVxyXG5cclxuLm5vLWdyb3VwLW1lc3NhZ2Uge1xyXG4gIGNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOCk7XHJcbiAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICBtYXJnaW46IDA7XHJcbiAgZm9udC13ZWlnaHQ6IDQwMDtcclxufVxyXG5cclxuLmdyb3VwLWRlc2NyaXB0aW9uIHtcclxuICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjkpO1xyXG4gIGZvbnQtc2l6ZTogMC43NXJlbTtcclxuICBtYXJnaW46IDA7XHJcbiAgb3BhY2l0eTogMC45O1xyXG59XHJcblxyXG4uaGVhZGVyLWFjdGlvbnMge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBnYXA6IDE2cHg7XHJcbiAgZmxleC1zaHJpbms6IDA7XHJcbn1cclxuXHJcbi5jb25uZWN0aW9uLXN0YXR1cyB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIGdhcDogOHB4O1xyXG4gIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTtcclxuICBwYWRkaW5nOiA4cHggMTJweDtcclxuICBib3JkZXItcmFkaXVzOiAxNnB4O1xyXG4gIGJhY2tkcm9wLWZpbHRlcjogYmx1cigxMHB4KTtcclxufVxyXG5cclxuLnN0YXR1cy1pY29uIHtcclxuICBmb250LXNpemU6IDE4cHg7XHJcbiAgd2lkdGg6IDE4cHg7XHJcbiAgaGVpZ2h0OiAxOHB4O1xyXG4gIGNvbG9yOiAjZWY0NDQ0O1xyXG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XHJcbn1cclxuXHJcbi5zdGF0dXMtaWNvbi5jb25uZWN0ZWQge1xyXG4gIGNvbG9yOiAjMTBiOTgxO1xyXG59XHJcblxyXG4uc3RhdHVzLXRleHQge1xyXG4gIGZvbnQtc2l6ZTogMC43NXJlbTtcclxuICBmb250LXdlaWdodDogNTAwO1xyXG59XHJcblxyXG4ub25saW5lLWNvdW50IHtcclxuICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XHJcbiAgY29sb3I6IHdoaXRlO1xyXG4gIGZvbnQtc2l6ZTogMC43NXJlbTtcclxuICBmb250LXdlaWdodDogNTAwO1xyXG59XHJcblxyXG4ubWVzc2FnZXMtY29udGFpbmVyIHtcclxuICBmbGV4OiAxO1xyXG4gIG1pbi1oZWlnaHQ6IDA7XHJcbiAgYmFja2dyb3VuZDogI2Y4ZjlmYTtcclxuICBvdmVyZmxvdzogaGlkZGVuO1xyXG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxufVxyXG5cclxuLmlucHV0LWNvbnRhaW5lciB7XHJcbiAgYmFja2dyb3VuZDogd2hpdGU7XHJcbiAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlMGUwZTA7XHJcbiAgcGFkZGluZzogMTZweDtcclxufVxyXG5cclxuLm5vLWdyb3VwLWlucHV0IHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgaGVpZ2h0OiA2MHB4O1xyXG59XHJcblxyXG4uZ3JvdXAtc2VsZWN0aW9uIHtcclxuICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgcGFkZGluZzogMjBweDtcclxufVxyXG5cclxuLmdyb3VwLXNlbGVjdGlvbiBoMyB7XHJcbiAgbWFyZ2luOiAwIDAgMTZweCAwO1xyXG4gIGNvbG9yOiAjMzMzO1xyXG4gIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbn1cclxuXHJcbi5ncm91cC1zZWxlY3RvciB7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbiAgbWF4LXdpZHRoOiAzMDBweDtcclxuICBtYXJnaW4tYm90dG9tOiAxNnB4O1xyXG59XHJcblxyXG4uZ3JvdXAtZGVzY3JpcHRpb24tc21hbGwge1xyXG4gIGNvbG9yOiAjNjY2O1xyXG4gIGZvbnQtc2l6ZTogMC44cmVtO1xyXG59XHJcblxyXG4ubm8tZ3JvdXBzLW1lc3NhZ2Uge1xyXG4gIGNvbG9yOiAjNjY2O1xyXG4gIGZvbnQtc3R5bGU6IGl0YWxpYztcclxuICBtYXJnaW46IDAgMCAxNnB4IDA7XHJcbn1cclxuXHJcbi5yZXRyeS1idXR0b24ge1xyXG4gIG1hcmdpbi10b3A6IDhweDtcclxufVxyXG5cclxuLm1vZGFsLW92ZXJsYXkge1xyXG4gIHBvc2l0aW9uOiBmaXhlZDtcclxuICB0b3A6IDA7XHJcbiAgbGVmdDogMDtcclxuICByaWdodDogMDtcclxuICBib3R0b206IDA7XHJcbiAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjUpO1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICB6LWluZGV4OiAxMDAwO1xyXG59XHJcblxyXG4ubW9kYWwtY2FyZCB7XHJcbiAgd2lkdGg6IDkwJTtcclxuICBtYXgtd2lkdGg6IDUwMHB4O1xyXG4gIG1heC1oZWlnaHQ6IDkwdmg7XHJcbiAgb3ZlcmZsb3cteTogYXV0bztcclxufVxyXG5cclxuLmNsb3NlLWJ1dHRvbiB7XHJcbiAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gIHRvcDogOHB4O1xyXG4gIHJpZ2h0OiA4cHg7XHJcbn1cclxuXHJcbi8qIFJlc3BvbnNpdmUgRGVzaWduICovXHJcbkBtZWRpYSAobWF4LXdpZHRoOiAxMDI0cHgpIHtcclxuICAuY2hhdC1zaWRlYmFyIHtcclxuICAgIHdpZHRoOiAyNjBweDtcclxuICAgIG1pbi13aWR0aDogMjYwcHg7XHJcbiAgfVxyXG4gIFxyXG4gIC5ncm91cC1uYW1lIHtcclxuICAgIGZvbnQtc2l6ZTogMS4xMjVyZW07XHJcbiAgfVxyXG4gIFxyXG4gIC5oZWFkZXItYWN0aW9ucyB7XHJcbiAgICBnYXA6IDEycHg7XHJcbiAgfVxyXG59XHJcblxyXG5AbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcclxuICAuY2hhdC1zaWRlYmFyIHtcclxuICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgbWluLXdpZHRoOiAxMDAlO1xyXG4gIH1cclxuICBcclxuICAuY2hhdC1tYWluLWNvbnRlbnQge1xyXG4gICAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gNjBweCk7XHJcbiAgfVxyXG4gIFxyXG4gIC5ncm91cC1uYW1lIHtcclxuICAgIGZvbnQtc2l6ZTogMXJlbTtcclxuICB9XHJcbiAgXHJcbiAgLmhlYWRlci1hY3Rpb25zIHtcclxuICAgIGdhcDogOHB4O1xyXG4gIH1cclxuICBcclxuICAuY29ubmVjdGlvbi1zdGF0dXMge1xyXG4gICAgcGFkZGluZzogNnB4IDEwcHg7XHJcbiAgfVxyXG4gIFxyXG4gIC5zdGF0dXMtdGV4dCB7XHJcbiAgICBmb250LXNpemU6IDAuN3JlbTtcclxuICB9XHJcbiAgXHJcbiAgLm9ubGluZS1jb3VudCB7XHJcbiAgICBmb250LXNpemU6IDAuN3JlbTtcclxuICB9XHJcbn1cclxuXHJcbkBtZWRpYSAobWF4LXdpZHRoOiA0ODBweCkge1xyXG4gIC5oZWFkZXItY29udGVudCB7XHJcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gICAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7XHJcbiAgICBnYXA6IDhweDtcclxuICB9XHJcbiAgXHJcbiAgLmhlYWRlci1hY3Rpb25zIHtcclxuICAgIHdpZHRoOiAxMDAlO1xyXG4gICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gIH1cclxuICBcclxuICAuZ3JvdXAtbmFtZSB7XHJcbiAgICBmb250LXNpemU6IDAuOXJlbTtcclxuICB9XHJcbiAgXHJcbiAgLmNoYXQtaGVhZGVyIHtcclxuICAgIHBhZGRpbmc6IDAgOHB4O1xyXG4gIH1cclxuICBcclxuICAuaW5wdXQtY29udGFpbmVyIHtcclxuICAgIHBhZGRpbmc6IDEycHg7XHJcbiAgfVxyXG59XHJcblxyXG4vKiBTbW9vdGggYW5pbWF0aW9ucyAqL1xyXG4qIHtcclxuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBjdWJpYy1iZXppZXIoMC40LCAwLCAwLjIsIDEpO1xyXG59XHJcblxyXG4vKiBDdXN0b20gc2Nyb2xsYmFyIGZvciB3ZWJraXQgYnJvd3NlcnMgKi9cclxuOjotd2Via2l0LXNjcm9sbGJhciB7XHJcbiAgd2lkdGg6IDZweDtcclxufVxyXG5cclxuOjotd2Via2l0LXNjcm9sbGJhci10cmFjayB7XHJcbiAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7XHJcbn1cclxuXHJcbjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIge1xyXG4gIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC4yKTtcclxuICBib3JkZXItcmFkaXVzOiAzcHg7XHJcbn1cclxuXHJcbjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWI6aG92ZXIge1xyXG4gIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC4zKTtcclxufSJdfQ== */\"],\n  changeDetection: 0\n});", "map": {"version": 3, "mappings": ";AAAA,SAAgEA,iBAAhE,QAAyF,eAAzF;AACA,SAAoCC,YAApC,QAAwD,MAAxD;AAIA,SAASC,kBAAT,QAAmC,6BAAnC;;;;;;;;;;;;;;;;;;;;;;;;;ICJAC;;;;;;IA+BUA;IACEA;IACFA;;;;;;IACAA;IACEA;IACFA;;;;;IADEA;IAAAA;;;;;;;;IAoBFA;IAEEA;MAAAA;MAAA;MAAA,OAASA,0CAAT;IAA2B,CAA3B;IAIAA;IAAUA;IAAoBA;;;;;;;;IASpCA;IAMEA;MAAAA;MAAA;MAAA,OAAWA,uCAAX;IAA0B,CAA1B,EAA2B,eAA3B,EAA2B;MAAAA;MAAA;MAAA,OACVA,6CADU;IACW,CADtC,EAA2B,kBAA3B,EAA2B;MAAAA;MAAA;MAAA,OAEPA,gDAFO;IAEiB,CAF5C,EAA2B,QAA3B,EAA2B;MAAAA;MAAA;MAAA,OAGjBA,sCAHiB;IAGH,CAHxB,EAA2B,UAA3B,EAA2B;MAAAA;MAAA;MAAA,OAIfA,wCAJe;IAIC,CAJ5B,EAA2B,gBAA3B,EAA2B;MAAAA;MAAA;MAAA,OAKTA,8CALS;IAKa,CALxC;IAMDA;;;;;IAVCA,2CAAqB,aAArB,EAAqBC,WAArB,EAAqB,SAArB,EAAqBA,cAArB,EAAqB,sBAArB,EAAqBA,2BAArB;;;;;;;;IAaFD;IAMEA;MAAAA;MAAA;MAAA,OAAWA,uCAAX;IAA0B,CAA1B,EAA2B,eAA3B,EAA2B;MAAAA;MAAA;MAAA,OACVA,6CADU;IACW,CADtC,EAA2B,kBAA3B,EAA2B;MAAAA;MAAA;MAAA,OAEPA,gDAFO;IAEiB,CAF5C,EAA2B,QAA3B,EAA2B;MAAAA;MAAA;MAAA,OAGjBA,sCAHiB;IAGH,CAHxB,EAA2B,UAA3B,EAA2B;MAAAA;MAAA;MAAA,OAIfA,wCAJe;IAIC,CAJ5B,EAA2B,gBAA3B,EAA2B;MAAAA;MAAA;MAAA,OAKTA,8CALS;IAKa,CALxC;IAMDA;;;;;IAVCA,4CAAqB,aAArB,EAAqBE,YAArB,EAAqB,SAArB,EAAqBA,eAArB,EAAqB,sBAArB,EAAqBA,4BAArB;;;;;;IAcJF,gCAA4E,CAA5E,EAA4E,UAA5E,EAA4E,EAA5E,EAA4E,CAA5E,EAA4E,kBAA5E,EAA4E,CAA5E,EAA4E,KAA5E,EAA4E,EAA5E,EAA4E,CAA5E,EAA4E,KAA5E,EAA4E,EAA5E;IAKUA,wBAAa,CAAb,EAAa,MAAb,EAAa,CAAb,EAAa,MAAb;IAGFA;IACAA;IACEA;IACFA;;;;;IADEA;IAAAA;;;;;;;;IASRA;IAGEA;MAAAA;MAAA;MAAA,OAAiBA,6CAAjB;IAAsC,CAAtC,EAAuC,eAAvC,EAAuC;MAAAA;MAAA;MAAA,OACtBA,uCADsB;IACP,CADhC;IAEDA;;;;;IAHCA;;;;;;IAeYA;IAAiEA;IAAyBA;;;;;IAAzBA;IAAAA;;;;;;IAFnEA;IACEA;IACAA;IACFA;;;;;IAHyCA;IACvCA;IAAAA;IACOA;IAAAA;;;;;;IAIbA;IACEA;IACFA;;;;;;;;IACAA;IAEEA;MAAAA;MAAA;MAAA,OAASA,4CAAT;IAA6B,CAA7B;IAKAA;IAAUA;IAAOA;IACjBA;IACFA;;;;;;;;IA1BRA,gCAAkD,CAAlD,EAAkD,UAAlD,EAAkD,CAAlD,EAAkD,kBAAlD,EAAkD,CAAlD,EAAkD,KAAlD,EAAkD,EAAlD,EAAkD,CAAlD,EAAkD,IAAlD;IAIYA;IAAgCA;IACpCA,2CAA4D,CAA5D,EAA4D,WAA5D;IACaA;IAAcA;IACzBA;IAAYA;MAAAA;MAAA;MAAA,OAAmBA,iDAAnB;IAA4C,CAA5C;IACVA;IAIFA;IAEFA;IAGAA;IAUFA;;;;;IApB8DA;IAAAA;IAC1BA;IAAAA;IAM9BA;IAAAA;IAIDA;IAAAA;;;;;;;;IA9IjBA,iDAAiE,CAAjE,EAAiE,aAAjE,EAAiE,CAAjE,EAAiE,CAAjE,EAAiE,CAAjE,EAAiE,aAAjE,EAAiE,CAAjE;IAUMA;MAAAA;MAAA;MAAA,OAAoBA,0CAApB;IAAsC,CAAtC,EAAuC,UAAvC,EAAuC;MAAAA;MAAA;MAAA,OAC3BA,kCAD2B;IACjB,CADtB;IAEDA;IAIHA,+CAA+C,CAA/C,EAA+C,aAA/C,EAA+C,EAA/C,EAA+C,CAA/C,EAA+C,QAA/C,EAA+C,EAA/C;IAG4BA;MAAAA;;MAAA;;MAAA,OAASA,4BAAT;IAAyB,CAAzB;IACtBA;IAAUA;IAAIA;IAGhBA,gCAA4B,EAA5B,EAA4B,KAA5B,EAA4B,EAA5B,EAA4B,EAA5B,EAA4B,IAA5B,EAA4B,EAA5B;IAGMA;IACFA;IACAA;IAGAA;IAGFA;IAEAA,iCAA4B,EAA5B,EAA4B,KAA5B,EAA4B,EAA5B,EAA4B,EAA5B,EAA4B,UAA5B,EAA4B,EAA5B;IAGMA;IACFA;IACAA;IACEA;IACFA;IAGFA,uCAAe,EAAf,EAAe,UAAf,EAAe,EAAf;IAEIA;IACFA;IAGFA;IAQFA;IAKJA;IAEEA;IAeAA;IAaFA;IAGAA;IAkBAA;IACEA;IAOAA;IA+BFA;;;;;IAvJEA;IAAAA,iDAA2B,aAA3B,EAA2BG,WAA3B,EAA2B,SAA3B,EAA2BA,cAA3B,EAA2B,QAA3B,EAA2BA,aAA3B,EAA2B,cAA3B,EAA2BA,mBAA3B,EAA2B,aAA3B,EAA2BA,+BAA3B;IAsBMH;IAAAA;IAEEA;IAAAA;IAGAA;IAAAA;IAOQA;IAAAA;IACRA;IAAAA;IAGAA;IAAAA;IAMAA;IAAAA;IAKDA;IAAAA;IAeJA;IAAAA;IAeAA;IAAAA;IAeCA;IAAAA;IAoBDA;IAAAA;IAMGA;IAAAA;;;;;;;;IAoCZA;IAGEA;MAAAA;MAAA;MAAA,OAAWA,0CAAX;IAA6B,CAA7B;IACDA;;;;;IAFCA;;;;;;;;IAKFA,gCAAsD,CAAtD,EAAsD,UAAtD,EAAsD,EAAtD,EAAsD,CAAtD,EAAsD,iBAAtD,EAAsD,CAAtD,EAAsD,gBAAtD;IAGsBA;IAAeA;IAC/BA;IACEA;MAAAA;MAAA;MAAA,OAASA,+CAAT;IAAgC,CAAhC;IAIAA;IAAUA;IAAKA;IAGnBA;IACEA;IACFA;;;;;;;;IAKJA;IAGEA;MAAAA;MAAA;MAAA,OAAUA,0CAAV;IAA4B,CAA5B,EAA6B,UAA7B,EAA6B;MAAAA;MAAA;MAAA,OACjBA,sCADiB;IACH,CAD1B;IAEDA;;;;;IAHCA;;;;ADnLF,OAAM,MAAOI,YAAP,CAAmB;EAuBvBC,YAAoBC,WAApB,EAAsDC,GAAtD,EAA4E;IAAxD;IAAkC,eAAsB,CAtB5E;;IACA,YAAsB,IAAtB;IACA,gBAAsB,EAAtB;IACA,mBAAsB,EAAtB;IACA,cAAkB,EAAlB;IACA,oBAA6B,IAA7B;IACA,eAA0B,IAA1B;IACA,eAAU,KAAV;IACA,eAAU,KAAV;IACA,sBAAiB,KAAjB;IACA,iBAAY,KAAZ;IACA,kBAAa,KAAb;IACA,0BAAqB,KAArB;IACA,sBAAiC,IAAjC;IACA,4BAAsC,IAAtC;IACA,mBAAwB,EAAxB,CAO4E,CAL5E;;IACA,2BAAsBR,kBAAkB,CAACS,sBAAzC;IAEQ,qBAAgC,EAAhC;EAEwE;;EAEhFC,QAAQ;IACN;IACA,KAAKC,aAAL,CAAmBC,IAAnB,CACE,KAAKL,WAAL,CAAiBM,KAAjB,CAAuBC,SAAvB,CAAiCC,IAAI,IAAG;MACtC,KAAKA,IAAL,GAAYA,IAAZ;MACA,KAAKP,GAAL,CAASQ,YAAT;IACD,CAHD,CADF,EAKE,KAAKT,WAAL,CAAiBU,SAAjB,CAA2BH,SAA3B,CAAqCI,QAAQ,IAAG;MAC9C,KAAKA,QAAL,GAAgBA,QAAhB;MACA,KAAKV,GAAL,CAASQ,YAAT;IACD,CAHD,CALF,EASE,KAAKT,WAAL,CAAiBY,YAAjB,CAA8BL,SAA9B,CAAwCM,KAAK,IAAG;MAC9C,KAAKC,WAAL,GAAmBD,KAAnB;MACA,KAAKZ,GAAL,CAASQ,YAAT;IACD,CAHD,CATF,EAaE,KAAKT,WAAL,CAAiBe,OAAjB,CAAyBR,SAAzB,CAAmCS,MAAM,IAAG;MAC1C,KAAKA,MAAL,GAAcA,MAAd;MACA,KAAKf,GAAL,CAASQ,YAAT;IACD,CAHD,CAbF,EAiBE,KAAKT,WAAL,CAAiBiB,aAAjB,CAA+BV,SAA/B,CAAyCW,KAAK,IAAG;MAC/C,KAAKC,YAAL,GAAoBD,KAApB;MACA,KAAKjB,GAAL,CAASQ,YAAT;IACD,CAHD,CAjBF,EAqBE,KAAKT,WAAL,CAAiBoB,QAAjB,CAA0Bb,SAA1B,CAAoCc,OAAO,IAAG;MAC5C,KAAKA,OAAL,GAAeA,OAAf;MACA,KAAKpB,GAAL,CAASQ,YAAT;IACD,CAHD,CArBF,EAyBE,KAAKT,WAAL,CAAiBsB,QAAjB,CAA0Bf,SAA1B,CAAoCgB,OAAO,IAAG;MAC5C,KAAKA,OAAL,GAAeA,OAAf;MACA,KAAKtB,GAAL,CAASQ,YAAT;IACD,CAHD,CAzBF,EA6BE,KAAKT,WAAL,CAAiBwB,QAAjB,CAA0BjB,SAA1B,CAAoCkB,OAAO,IAAG;MAC5C,KAAKA,OAAL,GAAeA,OAAf;MACA,KAAKxB,GAAL,CAASQ,YAAT;IACD,CAHD,CA7BF,EAiCE,KAAKT,WAAL,CAAiB0B,eAAjB,CAAiCnB,SAAjC,CAA2CoB,SAAS,IAAG;MACrD,KAAKC,cAAL,GAAsBD,SAAtB;MACA,KAAK1B,GAAL,CAASQ,YAAT;IACD,CAHD,CAjCF,EAqCE,KAAKT,WAAL,CAAiB6B,UAAjB,CAA4BtB,SAA5B,CAAsCuB,SAAS,IAAG;MAChD,KAAKA,SAAL,GAAiBA,SAAjB;MACA,KAAK7B,GAAL,CAASQ,YAAT;IACD,CAHD,CArCF,EAyCE,KAAKT,WAAL,CAAiB+B,WAAjB,CAA6BxB,SAA7B,CAAuCyB,UAAU,IAAG;MAClD,KAAKA,UAAL,GAAkBA,UAAlB;MACA,KAAK/B,GAAL,CAASQ,YAAT;IACD,CAHD,CAzCF,EA6CE,KAAKT,WAAL,CAAiBiC,eAAjB,CAAiC1B,SAAjC,CAA2C2B,cAAc,IAAG;MAC1D,KAAKA,cAAL,GAAsBA,cAAtB;MACA,KAAKjC,GAAL,CAASQ,YAAT;IACD,CAHD,CA7CF,EAiDE,KAAKT,WAAL,CAAiBmC,YAAjB,CAA8B5B,SAA9B,CAAwC6B,WAAW,IAAG;MACpD,KAAKA,WAAL,GAAmBA,WAAnB;MACA,KAAKnC,GAAL,CAASQ,YAAT;IACD,CAHD,CAjDF;EAsDD;;EAED4B,WAAW;IACT,KAAKjC,aAAL,CAAmBkC,OAAnB,CAA2BC,GAAG,IAAIA,GAAG,CAACC,WAAJ,EAAlC;IACA,KAAKxC,WAAL,CAAiByC,MAAjB;EACD;;EAEDC,WAAW,CAACC,OAAD,EAAgB;IACzB,KAAK3C,WAAL,CAAiB4C,SAAjB,CAA2BD,OAA3B;EACD;;EAEDE,aAAa,CAACC,KAAD,EAAkD;IAC7D,KAAK9C,WAAL,CAAiB+C,WAAjB,CAA6BD,KAAK,CAACE,IAAnC,EAAyCF,KAAK,CAACG,SAA/C;EACD;;EAEDC,OAAO,CAACC,OAAD,EAAiB;IACtB,KAAKnD,WAAL,CAAiBoD,cAAjB,CAAgCD,OAAhC,EADsB,CAGtB;;IACA,KAAKE,oBAAL,GAA4BF,OAAO,CAACG,EAApC,CAJsB,CAMtB;;IACAC,UAAU,CAAC,MAAK;MACd,KAAKF,oBAAL,GAA4B,IAA5B;IACD,CAFS,EAEP,IAFO,CAAV;EAGD;;EAEDG,aAAa;IACX,KAAKxD,WAAL,CAAiByD,WAAjB;EACD;;EAEDC,aAAa,CAACZ,KAAD,EAA4C;IACvD,KAAK9C,WAAL,CAAiB2D,WAAjB,CAA6Bb,KAAK,CAACc,SAAnC,EAA8Cd,KAAK,CAACe,KAApD;EACD;;EAEDC,gBAAgB,CAACC,IAAD,EAA2C;IACzD,KAAK/D,WAAL,CAAiBgE,cAAjB,CAAgCD,IAAhC;EACD;;EAEDE,MAAM,CAACd,OAAD,EAAiB;IACrB,KAAKnD,WAAL,CAAiBkE,mBAAjB,CAAqCf,OAArC;EACD;;EAEKgB,UAAU,CAACC,OAAD,EAAgB;IAAA;;IAAA;MAC9B,IAAI;QACF,IAAI,KAAI,CAAClC,cAAT,EAAyB;UACvB,MAAM,KAAI,CAAClC,WAAL,CAAiBqE,aAAjB,CAA+B,KAAI,CAACnC,cAAL,CAAoBoB,EAAnD,EAAuDc,OAAvD,CAAN;;UACA,KAAI,CAACpE,WAAL,CAAiBsE,oBAAjB;QACD;MACF,CALD,CAKE,OAAOC,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,yBAAd,EAAyCA,KAAzC;QACAE,KAAK,CAAC,2CAAD,CAAL;MACD;IAT6B;EAU/B;;EAEDC,YAAY;IACV,KAAK1E,WAAL,CAAiBsE,oBAAjB;EACD;;EAEKK,QAAQ,CAACxB,OAAD,EAAiB;IAAA;;IAAA;MAC7B,IAAI;QACF,IAAIyB,OAAO,CAAC,+CAAD,CAAX,EAA8D;UAC5D,MAAM,MAAI,CAAC5E,WAAL,CAAiB6E,aAAjB,CAA+B1B,OAAO,CAACG,EAAvC,CAAN;QACD;MACF,CAJD,CAIE,OAAOiB,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,2BAAd,EAA2CA,KAA3C;QACAE,KAAK,CAAC,6CAAD,CAAL;MACD;IAR4B;EAS9B;;EAEDK,gBAAgB;IACd,KAAK9E,WAAL,CAAiB4B,cAAjB;EACD;;EAEDmD,gBAAgB;IACd,KAAK/E,WAAL,CAAiBgF,cAAjB;EACD;;EAEDC,gBAAgB;IACd,KAAKC,kBAAL,GAA0B,IAA1B;EACD;;EAEDC,qBAAqB;IACnB,KAAKD,kBAAL,GAA0B,KAA1B;EACD;;EAEDE,QAAQ;IACN,KAAKpF,WAAL,CAAiByC,MAAjB;EACD;;EAED4C,cAAc,CAAClC,OAAD,EAAiB;IAC7B,KAAKE,oBAAL,GAA4BF,OAAO,CAACG,EAApC,CAD6B,CAG7B;;IACAC,UAAU,CAAC,MAAK;MACd,KAAKF,oBAAL,GAA4B,IAA5B;IACD,CAFS,EAEP,IAFO,CAAV;EAGD;;EAEDiC,kBAAkB;IAChB,IAAI,KAAK9E,IAAT,EAAe;MACbgE,OAAO,CAACe,GAAR,CAAY,8BAAZ,EAA4C,KAAK/E,IAAjD;MACA,KAAKR,WAAL,CAAiBwF,cAAjB,CAAgC,KAAKhF,IAArC;IACD;EACF;;EAEDiF,aAAa,CAACrD,WAAD,EAAsB;IACjC,IAAIA,WAAW,CAACsD,MAAZ,KAAuB,CAA3B,EAA8B,OAAO,EAAP;;IAE9B,IAAItD,WAAW,CAACsD,MAAZ,KAAuB,CAA3B,EAA8B;MAC5B,OAAO,GAAGtD,WAAW,CAAC,CAAD,CAAG,eAAxB;IACD,CAFD,MAEO,IAAIA,WAAW,CAACsD,MAAZ,KAAuB,CAA3B,EAA8B;MACnC,OAAO,GAAGtD,WAAW,CAAC,CAAD,CAAG,QAAQA,WAAW,CAAC,CAAD,CAAG,gBAA9C;IACD,CAFM,MAEA;MACL,OAAO,GAAGA,WAAW,CAAC,CAAD,CAAG,QAAQA,WAAW,CAACsD,MAAZ,GAAqB,CAAC,uBAAtD;IACD;EACF;;AArMsB;;;mBAAZ5F,cAAYJ;AAAA;;;QAAZI;EAAY6F;EAAAC;EAAAC;EAAAC;EAAAC;IAAA;MCZzBrG;MAGAA;MAgKAA;MAOAA;MAmBAA;;;;MA7LiBA;MAGOA;MAAAA;MAiKrBA;MAAAA;MAMGA;MAAAA;MAoBHA;MAAAA", "names": ["ChangeDetectorRef", "Subscription", "PERFORMANCE_CONFIG", "i0", "ctx_r9", "ctx_r10", "ctx_r1", "AppComponent", "constructor", "chatService", "cdr", "enableVirtualScrolling", "ngOnInit", "subscriptions", "push", "user$", "subscribe", "user", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "messages$", "messages", "onlineUsers$", "users", "onlineUsers", "groups$", "groups", "currentGroup$", "group", "currentGroup", "replyTo$", "replyTo", "loading$", "loading", "isAdmin$", "isAdmin", "showAdminPanel$", "showPanel", "showAdminPanel", "connected$", "connected", "isLoggedIn$", "isLoggedIn", "editingMessage$", "editingMessage", "typingUsers$", "typingUsers", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "logout", "onJoinGroup", "groupId", "joinGroup", "onSendMessage", "event", "sendMessage", "text", "replyToId", "onReply", "message", "replyToMessage", "highlightedMessageId", "id", "setTimeout", "onCancelReply", "cancelReply", "onAddReaction", "addReaction", "messageId", "emoji", "onRemoveReaction", "data", "removeReaction", "onEdit", "startEditingMessage", "onSaveEdit", "newText", "updateMessage", "cancelEditingMessage", "error", "console", "alert", "onCancelEdit", "onDelete", "confirm", "deleteMessage", "onShowAdminPanel", "onHideAdminPanel", "hideAdminPanel", "onPasswordChange", "showPasswordChange", "onClosePasswordChange", "onLogout", "onMessageClick", "onManualLoadGroups", "log", "loadUserGroups", "getTypingText", "length", "selectors", "decls", "vars", "consts", "template"], "sourceRoot": "", "sources": ["R:\\chateye\\FrontendAngular\\src\\app\\app.component.ts", "R:\\chateye\\FrontendAngular\\src\\app\\app.component.html"], "sourcesContent": ["import { Component, OnInit, On<PERSON><PERSON>roy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';\nimport { Observable, combineLatest, Subscription } from 'rxjs';\nimport { map, take } from 'rxjs/operators';\nimport { ChatService } from './services/chat.service';\nimport { Message, Group, User } from './services/api.service';\nimport { PERFORMANCE_CONFIG } from './config/performance.config';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.css'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class AppComponent implements OnInit, OnDestroy {\n  // Cached values to reduce async pipe usage\n  user: string | null = null;\n  messages: Message[] = [];\n  onlineUsers: User[] = [];\n  groups: Group[] = [];\n  currentGroup: Group | null = null;\n  replyTo: Message | null = null;\n  loading = false;\n  isAdmin = false;\n  showAdminPanel = false;\n  connected = false;\n  isLoggedIn = false;\n  showPasswordChange = false;\n  editingMessage: Message | null = null;\n  highlightedMessageId: string | null = null;\n  typingUsers: string[] = [];\n  \n  // Performance configuration\n  useVirtualScrolling = PERFORMANCE_CONFIG.enableVirtualScrolling;\n  \n  private subscriptions: Subscription[] = [];\n\n  constructor(private chatService: ChatService, private cdr: ChangeDetectorRef) {}\n\n  ngOnInit(): void {\n    // Subscribe to all observables and cache values to reduce async pipe usage\n    this.subscriptions.push(\n      this.chatService.user$.subscribe(user => {\n        this.user = user;\n        this.cdr.markForCheck();\n      }),\n      this.chatService.messages$.subscribe(messages => {\n        this.messages = messages;\n        this.cdr.markForCheck();\n      }),\n      this.chatService.onlineUsers$.subscribe(users => {\n        this.onlineUsers = users;\n        this.cdr.markForCheck();\n      }),\n      this.chatService.groups$.subscribe(groups => {\n        this.groups = groups;\n        this.cdr.markForCheck();\n      }),\n      this.chatService.currentGroup$.subscribe(group => {\n        this.currentGroup = group;\n        this.cdr.markForCheck();\n      }),\n      this.chatService.replyTo$.subscribe(replyTo => {\n        this.replyTo = replyTo;\n        this.cdr.markForCheck();\n      }),\n      this.chatService.loading$.subscribe(loading => {\n        this.loading = loading;\n        this.cdr.markForCheck();\n      }),\n      this.chatService.isAdmin$.subscribe(isAdmin => {\n        this.isAdmin = isAdmin;\n        this.cdr.markForCheck();\n      }),\n      this.chatService.showAdminPanel$.subscribe(showPanel => {\n        this.showAdminPanel = showPanel;\n        this.cdr.markForCheck();\n      }),\n      this.chatService.connected$.subscribe(connected => {\n        this.connected = connected;\n        this.cdr.markForCheck();\n      }),\n      this.chatService.isLoggedIn$.subscribe(isLoggedIn => {\n        this.isLoggedIn = isLoggedIn;\n        this.cdr.markForCheck();\n      }),\n      this.chatService.editingMessage$.subscribe(editingMessage => {\n        this.editingMessage = editingMessage;\n        this.cdr.markForCheck();\n      }),\n      this.chatService.typingUsers$.subscribe(typingUsers => {\n        this.typingUsers = typingUsers;\n        this.cdr.markForCheck();\n      })\n    );\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.chatService.logout();\n  }\n\n  onJoinGroup(groupId: string): void {\n    this.chatService.joinGroup(groupId);\n  }\n\n  onSendMessage(event: { text: string; replyToId: string | null }): void {\n    this.chatService.sendMessage(event.text, event.replyToId);\n  }\n\n  onReply(message: Message): void {\n    this.chatService.replyToMessage(message);\n    \n    // Highlight the replied-to message\n    this.highlightedMessageId = message.id;\n    \n    // Clear highlight after 3 seconds\n    setTimeout(() => {\n      this.highlightedMessageId = null;\n    }, 3000);\n  }\n\n  onCancelReply(): void {\n    this.chatService.cancelReply();\n  }\n\n  onAddReaction(event: { messageId: string; emoji: string }): void {\n    this.chatService.addReaction(event.messageId, event.emoji);\n  }\n\n  onRemoveReaction(data: { messageId: string; emoji: string }): void {\n    this.chatService.removeReaction(data);\n  }\n\n  onEdit(message: Message): void {\n    this.chatService.startEditingMessage(message);\n  }\n\n  async onSaveEdit(newText: string): Promise<void> {\n    try {\n      if (this.editingMessage) {\n        await this.chatService.updateMessage(this.editingMessage.id, newText);\n        this.chatService.cancelEditingMessage();\n      }\n    } catch (error) {\n      console.error('Failed to edit message:', error);\n      alert('Failed to edit message. Please try again.');\n    }\n  }\n\n  onCancelEdit(): void {\n    this.chatService.cancelEditingMessage();\n  }\n\n  async onDelete(message: Message): Promise<void> {\n    try {\n      if (confirm('Are you sure you want to delete this message?')) {\n        await this.chatService.deleteMessage(message.id);\n      }\n    } catch (error) {\n      console.error('Failed to delete message:', error);\n      alert('Failed to delete message. Please try again.');\n    }\n  }\n\n  onShowAdminPanel(): void {\n    this.chatService.showAdminPanel();\n  }\n\n  onHideAdminPanel(): void {\n    this.chatService.hideAdminPanel();\n  }\n\n  onPasswordChange(): void {\n    this.showPasswordChange = true;\n  }\n\n  onClosePasswordChange(): void {\n    this.showPasswordChange = false;\n  }\n\n  onLogout(): void {\n    this.chatService.logout();\n  }\n\n  onMessageClick(message: Message): void {\n    this.highlightedMessageId = message.id;\n    \n    // Clear highlight after 3 seconds\n    setTimeout(() => {\n      this.highlightedMessageId = null;\n    }, 3000);\n  }\n\n  onManualLoadGroups(): void {\n    if (this.user) {\n      console.log('Manually loading groups for:', this.user);\n      this.chatService.loadUserGroups(this.user);\n    }\n  }\n\n  getTypingText(typingUsers: string[]): string {\n    if (typingUsers.length === 0) return '';\n\n    if (typingUsers.length === 1) {\n      return `${typingUsers[0]} is typing...`;\n    } else if (typingUsers.length === 2) {\n      return `${typingUsers[0]} and ${typingUsers[1]} are typing...`;\n    } else {\n      return `${typingUsers[0]} and ${typingUsers.length - 1} others are typing...`;\n    }\n  }\n\n}\n", "<!-- Login Form -->\n<app-login-form *ngIf=\"!isLoggedIn\"></app-login-form>\n\n<!-- Material Design Chat Interface -->\n<mat-sidenav-container *ngIf=\"isLoggedIn\" class=\"chat-container\">\n  <!-- Sidebar -->\n  <mat-sidenav #sidenav mode=\"side\" opened=\"true\" class=\"chat-sidebar\">\n    <app-sidebar \n      [onlineUsers]=\"onlineUsers\"\n      [currentUser]=\"user\"\n      [isAdmin]=\"isAdmin\"\n      [groups]=\"groups\"\n      [currentGroup]=\"currentGroup\"\n      [onJoinGroup]=\"onJoinGroup.bind(this)\"\n      (onPasswordChange)=\"onPasswordChange()\"\n      (onLogout)=\"onLogout()\"\n    ></app-sidebar>\n  </mat-sidenav>\n\n  <!-- Main Chat Area -->\n  <mat-sidenav-content class=\"chat-main-content\">\n    <!-- Chat Header -->\n    <mat-toolbar class=\"chat-header\">\n      <button mat-icon-button (click)=\"sidenav.toggle()\" class=\"menu-button\">\n        <mat-icon>menu</mat-icon>\n      </button>\n      \n      <div class=\"header-content\">\n        <div class=\"group-info\">\n          <h1 class=\"group-name\">\n            {{ currentGroup?.name || 'Select a Group' }}\n          </h1>\n          <p *ngIf=\"!currentGroup\" class=\"no-group-message\">\n            Please select a group to start chatting\n          </p>\n          <p *ngIf=\"currentGroup?.description\" class=\"group-description\">\n            {{ currentGroup?.description }}\n          </p>\n        </div>\n        \n        <div class=\"header-actions\">\n          <div class=\"connection-status\">\n            <mat-icon [class.connected]=\"connected\" class=\"status-icon\">\n              {{ connected ? 'wifi' : 'wifi_off' }}\n            </mat-icon>\n            <span class=\"status-text\">\n              {{ connected ? 'Connected' : 'Disconnected' }}\n            </span>\n          </div>\n          \n          <mat-chip-list>\n            <mat-chip class=\"online-count\">\n              {{ onlineUsers?.length || 0 }} online\n            </mat-chip>\n          </mat-chip-list>\n          \n          <button\n            *ngIf=\"isAdmin\"\n            (click)=\"onShowAdminPanel()\"\n            mat-icon-button\n            matTooltip=\"Admin Panel\"\n          >\n            <mat-icon>admin_panel_settings</mat-icon>\n          </button>\n        </div>\n      </div>\n    </mat-toolbar>\n\n    <!-- Messages Area -->\n    <div class=\"messages-container\">\n      <!-- Use virtual scrolling for better performance with large message lists -->\n      <app-message-list-virtual\n        *ngIf=\"useVirtualScrolling\"\n        [messages]=\"messages\"\n        [currentUser]=\"user\"\n        [loading]=\"loading\"\n        [highlightedMessageId]=\"highlightedMessageId\"\n        (onReply)=\"onReply($event)\"\n        (onAddReaction)=\"onAddReaction($event)\"\n        (onRemoveReaction)=\"onRemoveReaction($event)\"\n        (onEdit)=\"onEdit($event)\"\n        (onDelete)=\"onDelete($event)\"\n        (onMessageClick)=\"onMessageClick($event)\"\n      ></app-message-list-virtual>\n      \n      <!-- Fallback to regular message list for smaller lists -->\n      <app-message-list\n        *ngIf=\"!useVirtualScrolling\"\n        [messages]=\"messages\"\n        [currentUser]=\"user\"\n        [loading]=\"loading\"\n        [highlightedMessageId]=\"highlightedMessageId\"\n        (onReply)=\"onReply($event)\"\n        (onAddReaction)=\"onAddReaction($event)\"\n        (onRemoveReaction)=\"onRemoveReaction($event)\"\n        (onEdit)=\"onEdit($event)\"\n        (onDelete)=\"onDelete($event)\"\n        (onMessageClick)=\"onMessageClick($event)\"\n      ></app-message-list>\n    </div>\n\n    <!-- Typing Indicator -->\n    <div *ngIf=\"typingUsers && typingUsers.length > 0\" class=\"typing-indicator\">\n      <mat-card class=\"typing-card\">\n        <mat-card-content>\n          <div class=\"typing-content\">\n            <div class=\"typing-dots\">\n              <span></span>\n              <span></span>\n              <span></span>\n            </div>\n            <span class=\"typing-text\">\n              {{ getTypingText(typingUsers) }}\n            </span>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n\n    <!-- Message Input Area -->\n    <div class=\"input-container\">\n      <app-message-input\n        *ngIf=\"currentGroup\"\n        [replyTo]=\"replyTo\"\n        (onSendMessage)=\"onSendMessage($event)\"\n        (onCancelReply)=\"onCancelReply()\"\n      ></app-message-input>\n      \n      <div *ngIf=\"!currentGroup\" class=\"no-group-input\">\n        <mat-card>\n          <mat-card-content>\n            <div class=\"group-selection\">\n              <h3>Select a group to start chatting</h3>\n              <mat-form-field appearance=\"outline\" class=\"group-selector\">\n                <mat-label>Choose a group</mat-label>\n                <mat-select (selectionChange)=\"onJoinGroup($event.value)\" [value]=\"null\">\n                  <mat-option *ngFor=\"let group of groups\" [value]=\"group.id\">\n                    {{ group.name }}\n                    <span *ngIf=\"group.description\" class=\"group-description-small\"> - {{ group.description }}</span>\n                  </mat-option>\n                </mat-select>\n              </mat-form-field>\n              <p *ngIf=\"groups?.length === 0\" class=\"no-groups-message\">\n                No groups available. Contact an administrator to get access to groups.\n              </p>\n              <button \n                *ngIf=\"groups?.length === 0\"\n                (click)=\"onManualLoadGroups()\"\n                mat-raised-button\n                color=\"primary\"\n                class=\"retry-button\"\n              >\n                <mat-icon>refresh</mat-icon>\n                Retry Loading Groups\n              </button>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n    </div>\n  </mat-sidenav-content>\n</mat-sidenav-container>\n\n<!-- Admin Panel Modal -->\n<app-admin-panel\n  *ngIf=\"showAdminPanel\"\n  [currentUser]=\"user\"\n  (onClose)=\"onHideAdminPanel()\"\n></app-admin-panel>\n\n<!-- Password Change Modal -->\n<div *ngIf=\"showPasswordChange\" class=\"modal-overlay\">\n  <mat-card class=\"modal-card\">\n    <mat-card-header>\n      <mat-card-title>Change Password</mat-card-title>\n      <button\n        (click)=\"onClosePasswordChange()\"\n        mat-icon-button\n        class=\"close-button\"\n      >\n        <mat-icon>close</mat-icon>\n      </button>\n    </mat-card-header>\n    <mat-card-content>\n      <app-password-change></app-password-change>\n    </mat-card-content>\n  </mat-card>\n</div>\n\n<!-- Message Edit Modal -->\n<app-message-edit\n  *ngIf=\"editingMessage\"\n  [message]=\"editingMessage\"\n  (onSave)=\"onSaveEdit($event)\"\n  (onCancel)=\"onCancelEdit()\"\n></app-message-edit>"]}, "metadata": {}, "sourceType": "module"}