{"ast": null, "code": "import { EventEmitter, ElementRef } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nconst _c0 = [\"messageTextarea\"];\nexport let MessageEditComponent = /*#__PURE__*/(() => {\n  class MessageEditComponent {\n    constructor() {\n      this.onSave = new EventEmitter();\n      this.onCancel = new EventEmitter();\n      this.editedText = '';\n      this.originalText = '';\n    }\n\n    ngOnInit() {\n      this.originalText = this.message.text;\n      this.editedText = this.message.text; // Focus the textarea after view init\n\n      setTimeout(() => {\n        if (this.messageTextarea) {\n          this.messageTextarea.nativeElement.focus();\n          this.messageTextarea.nativeElement.select();\n        }\n      }, 100);\n    }\n\n    onKeyDown(event) {\n      if (event.key === 'Enter' && event.ctrlKey) {\n        event.preventDefault();\n        this.onSave.emit(this.editedText);\n      } else if (event.key === 'Escape') {\n        event.preventDefault();\n        this.onCancel.emit();\n      }\n    }\n\n    saveMessage() {\n      if (this.editedText.trim() && this.editedText !== this.originalText) {\n        this.onSave.emit(this.editedText.trim());\n      }\n    }\n\n    cancelEdit() {\n      this.onCancel.emit();\n    }\n\n  }\n\n  MessageEditComponent.ɵfac = function MessageEditComponent_Factory(t) {\n    return new (t || MessageEditComponent)();\n  };\n\n  MessageEditComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: MessageEditComponent,\n    selectors: [[\"app-message-edit\"]],\n    viewQuery: function MessageEditComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messageTextarea = _t.first);\n      }\n    },\n    inputs: {\n      message: \"message\"\n    },\n    outputs: {\n      onSave: \"onSave\",\n      onCancel: \"onCancel\"\n    },\n    decls: 19,\n    vars: 2,\n    consts: [[1, \"fixed\", \"inset-0\", \"bg-black\", \"bg-opacity-50\", \"flex\", \"items-center\", \"justify-center\", \"z-50\", 3, \"click\"], [1, \"bg-white\", \"rounded-lg\", \"w-full\", \"max-w-md\", \"mx-4\", 3, \"click\"], [1, \"flex\", \"items-center\", \"justify-between\", \"p-6\", \"border-b\", \"border-gray-200\"], [1, \"text-lg\", \"font-medium\", \"text-gray-900\"], [1, \"text-gray-400\", \"hover:text-gray-600\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M6 18L18 6M6 6l12 12\"], [1, \"p-6\"], [1, \"mb-4\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"mb-2\"], [\"rows\", \"4\", \"placeholder\", \"Enter your message...\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"border-gray-300\", \"rounded-md\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-blue-500\", \"focus:border-transparent\", \"resize-none\", 3, \"ngModel\", \"ngModelChange\", \"keydown\"], [\"messageTextarea\", \"\"], [1, \"flex\", \"justify-end\", \"space-x-3\"], [1, \"px-4\", \"py-2\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"bg-gray-100\", \"border\", \"border-gray-300\", \"rounded-md\", \"hover:bg-gray-200\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-gray-500\", 3, \"click\"], [1, \"px-4\", \"py-2\", \"text-sm\", \"font-medium\", \"text-white\", \"bg-blue-600\", \"border\", \"border-transparent\", \"rounded-md\", \"hover:bg-blue-700\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-blue-500\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", 3, \"disabled\", \"click\"]],\n    template: function MessageEditComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵlistener(\"click\", function MessageEditComponent_Template_div_click_0_listener() {\n          return ctx.cancelEdit();\n        });\n        i0.ɵɵelementStart(1, \"div\", 1);\n        i0.ɵɵlistener(\"click\", function MessageEditComponent_Template_div_click_1_listener($event) {\n          return $event.stopPropagation();\n        });\n        i0.ɵɵelementStart(2, \"div\", 2)(3, \"h3\", 3);\n        i0.ɵɵtext(4, \"Edit Message\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"button\", 4);\n        i0.ɵɵlistener(\"click\", function MessageEditComponent_Template_button_click_5_listener() {\n          return ctx.cancelEdit();\n        });\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(6, \"svg\", 5);\n        i0.ɵɵelement(7, \"path\", 6);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵnamespaceHTML();\n        i0.ɵɵelementStart(8, \"div\", 7)(9, \"div\", 8)(10, \"label\", 9);\n        i0.ɵɵtext(11, \" Message Text \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"textarea\", 10, 11);\n        i0.ɵɵlistener(\"ngModelChange\", function MessageEditComponent_Template_textarea_ngModelChange_12_listener($event) {\n          return ctx.editedText = $event;\n        })(\"keydown\", function MessageEditComponent_Template_textarea_keydown_12_listener($event) {\n          return ctx.onKeyDown($event);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(14, \"div\", 12)(15, \"button\", 13);\n        i0.ɵɵlistener(\"click\", function MessageEditComponent_Template_button_click_15_listener() {\n          return ctx.cancelEdit();\n        });\n        i0.ɵɵtext(16, \" Cancel \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"button\", 14);\n        i0.ɵɵlistener(\"click\", function MessageEditComponent_Template_button_click_17_listener() {\n          return ctx.saveMessage();\n        });\n        i0.ɵɵtext(18, \" Save Changes \");\n        i0.ɵɵelementEnd()()()()();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵadvance(12);\n        i0.ɵɵproperty(\"ngModel\", ctx.editedText);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"disabled\", !ctx.editedText.trim() || ctx.editedText === ctx.originalText);\n      }\n    },\n    dependencies: [i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgModel],\n    styles: [\"textarea[_ngcontent-%COMP%]{font-family:inherit;line-height:1.5}textarea[_ngcontent-%COMP%]:focus{box-shadow:0 0 0 3px #3b82f61a}button[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed}button[_ngcontent-%COMP%]:not(:disabled):hover{transform:translateY(-1px);transition:transform .1s ease-in-out}\"]\n  });\n  return MessageEditComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}