{"ast": null, "code": "import { not } from '../util/not';\nimport { filter } from '../operators/filter';\nimport { innerFrom } from './innerFrom';\nexport function partition(source, predicate, thisArg) {\n  return [filter(predicate, thisArg)(innerFrom(source)), filter(not(predicate, thisArg))(innerFrom(source))];\n}", "map": {"version": 3, "names": ["not", "filter", "innerFrom", "partition", "source", "predicate", "thisArg"], "sources": ["R:/chateye/FrontendAngular/node_modules/rxjs/dist/esm/internal/observable/partition.js"], "sourcesContent": ["import { not } from '../util/not';\nimport { filter } from '../operators/filter';\nimport { innerFrom } from './innerFrom';\nexport function partition(source, predicate, thisArg) {\n    return [filter(predicate, thisArg)(innerFrom(source)), filter(not(predicate, thisArg))(innerFrom(source))];\n}\n"], "mappings": "AAAA,SAASA,GAAT,QAAoB,aAApB;AACA,SAASC,MAAT,QAAuB,qBAAvB;AACA,SAASC,SAAT,QAA0B,aAA1B;AACA,OAAO,SAASC,SAAT,CAAmBC,MAAnB,EAA2BC,SAA3B,EAAsCC,OAAtC,EAA+C;EAClD,OAAO,CAACL,MAAM,CAACI,SAAD,EAAYC,OAAZ,CAAN,CAA2BJ,SAAS,CAACE,MAAD,CAApC,CAAD,EAAgDH,MAAM,CAACD,GAAG,CAACK,SAAD,EAAYC,OAAZ,CAAJ,CAAN,CAAgCJ,SAAS,CAACE,MAAD,CAAzC,CAAhD,CAAP;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}