{"ast": null, "code": "import { raceInit } from '../observable/race';\nimport { operate } from '../util/lift';\nimport { identity } from '../util/identity';\nexport function raceWith(...otherSources) {\n  return !otherSources.length ? identity : operate((source, subscriber) => {\n    raceInit([source, ...otherSources])(subscriber);\n  });\n}", "map": {"version": 3, "names": ["raceInit", "operate", "identity", "raceWith", "otherSources", "length", "source", "subscriber"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/rxjs/dist/esm/internal/operators/raceWith.js"], "sourcesContent": ["import { raceInit } from '../observable/race';\nimport { operate } from '../util/lift';\nimport { identity } from '../util/identity';\nexport function raceWith(...otherSources) {\n    return !otherSources.length\n        ? identity\n        : operate((source, subscriber) => {\n            raceInit([source, ...otherSources])(subscriber);\n        });\n}\n"], "mappings": "AAAA,SAASA,QAAT,QAAyB,oBAAzB;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,QAAT,QAAyB,kBAAzB;AACA,OAAO,SAASC,QAAT,CAAkB,GAAGC,YAArB,EAAmC;EACtC,OAAO,CAACA,YAAY,CAACC,MAAd,GACDH,QADC,GAEDD,OAAO,CAAC,CAACK,MAAD,EAASC,UAAT,KAAwB;IAC9BP,QAAQ,CAAC,CAACM,MAAD,EAAS,GAAGF,YAAZ,CAAD,CAAR,CAAoCG,UAApC;EACH,CAFQ,CAFb;AAKH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}