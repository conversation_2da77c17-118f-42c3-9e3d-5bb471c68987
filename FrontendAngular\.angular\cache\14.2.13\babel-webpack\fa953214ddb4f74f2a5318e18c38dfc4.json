{"ast": null, "code": "import hotEmitter from \"webpack/hot/emitter.js\";\nimport { log } from \"./log.js\";\n/** @typedef {import(\"../index\").Options} Options\n/** @typedef {import(\"../index\").Status} Status\n\n/**\n * @param {Options} options\n * @param {Status} status\n */\n\nfunction reloadApp(_ref, status) {\n  var hot = _ref.hot,\n      liveReload = _ref.liveReload;\n\n  if (status.isUnloading) {\n    return;\n  }\n\n  var currentHash = status.currentHash,\n      previousHash = status.previousHash;\n  var isInitial = currentHash.indexOf(\n  /** @type {string} */\n  previousHash) >= 0;\n\n  if (isInitial) {\n    return;\n  }\n  /**\n   * @param {Window} rootWindow\n   * @param {number} intervalId\n   */\n\n\n  function applyReload(rootWindow, intervalId) {\n    clearInterval(intervalId);\n    log.info(\"App updated. Reloading...\");\n    rootWindow.location.reload();\n  }\n\n  var search = self.location.search.toLowerCase();\n  var allowToHot = search.indexOf(\"webpack-dev-server-hot=false\") === -1;\n  var allowToLiveReload = search.indexOf(\"webpack-dev-server-live-reload=false\") === -1;\n\n  if (hot && allowToHot) {\n    log.info(\"App hot update...\");\n    hotEmitter.emit(\"webpackHotUpdate\", status.currentHash);\n\n    if (typeof self !== \"undefined\" && self.window) {\n      // broadcast update to window\n      self.postMessage(\"webpackHotUpdate\".concat(status.currentHash), \"*\");\n    }\n  } // allow refreshing the page only if liveReload isn't disabled\n  else if (liveReload && allowToLiveReload) {\n    var rootWindow = self; // use parent window for reload (in case we're in an iframe with no valid src)\n\n    var intervalId = self.setInterval(function () {\n      if (rootWindow.location.protocol !== \"about:\") {\n        // reload immediately if protocol is valid\n        applyReload(rootWindow, intervalId);\n      } else {\n        rootWindow = rootWindow.parent;\n\n        if (rootWindow.parent === rootWindow) {\n          // if parent equals current window we've reached the root which would continue forever, so trigger a reload anyways\n          applyReload(rootWindow, intervalId);\n        }\n      }\n    });\n  }\n}\n\nexport default reloadApp;", "map": {"version": 3, "names": ["hotEmitter", "log", "reloadApp", "_ref", "status", "hot", "liveReload", "isUnloading", "currentHash", "previousHash", "isInitial", "indexOf", "applyReload", "rootWindow", "intervalId", "clearInterval", "info", "location", "reload", "search", "self", "toLowerCase", "allowToHot", "allowToLiveReload", "emit", "window", "postMessage", "concat", "setInterval", "protocol", "parent"], "sources": ["R:/chateye/FrontendAngular/node_modules/webpack-dev-server/client/utils/reloadApp.js"], "sourcesContent": ["import hotEmitter from \"webpack/hot/emitter.js\";\nimport { log } from \"./log.js\";\n/** @typedef {import(\"../index\").Options} Options\n/** @typedef {import(\"../index\").Status} Status\n\n/**\n * @param {Options} options\n * @param {Status} status\n */\n\nfunction reloadApp(_ref, status) {\n  var hot = _ref.hot,\n      liveReload = _ref.liveReload;\n\n  if (status.isUnloading) {\n    return;\n  }\n\n  var currentHash = status.currentHash,\n      previousHash = status.previousHash;\n  var isInitial = currentHash.indexOf(\n  /** @type {string} */\n  previousHash) >= 0;\n\n  if (isInitial) {\n    return;\n  }\n  /**\n   * @param {Window} rootWindow\n   * @param {number} intervalId\n   */\n\n\n  function applyReload(rootWindow, intervalId) {\n    clearInterval(intervalId);\n    log.info(\"App updated. Reloading...\");\n    rootWindow.location.reload();\n  }\n\n  var search = self.location.search.toLowerCase();\n  var allowToHot = search.indexOf(\"webpack-dev-server-hot=false\") === -1;\n  var allowToLiveReload = search.indexOf(\"webpack-dev-server-live-reload=false\") === -1;\n\n  if (hot && allowToHot) {\n    log.info(\"App hot update...\");\n    hotEmitter.emit(\"webpackHotUpdate\", status.currentHash);\n\n    if (typeof self !== \"undefined\" && self.window) {\n      // broadcast update to window\n      self.postMessage(\"webpackHotUpdate\".concat(status.currentHash), \"*\");\n    }\n  } // allow refreshing the page only if liveReload isn't disabled\n  else if (liveReload && allowToLiveReload) {\n    var rootWindow = self; // use parent window for reload (in case we're in an iframe with no valid src)\n\n    var intervalId = self.setInterval(function () {\n      if (rootWindow.location.protocol !== \"about:\") {\n        // reload immediately if protocol is valid\n        applyReload(rootWindow, intervalId);\n      } else {\n        rootWindow = rootWindow.parent;\n\n        if (rootWindow.parent === rootWindow) {\n          // if parent equals current window we've reached the root which would continue forever, so trigger a reload anyways\n          applyReload(rootWindow, intervalId);\n        }\n      }\n    });\n  }\n}\n\nexport default reloadApp;"], "mappings": "AAAA,OAAOA,UAAP,MAAuB,wBAAvB;AACA,SAASC,GAAT,QAAoB,UAApB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,SAAT,CAAmBC,IAAnB,EAAyBC,MAAzB,EAAiC;EAC/B,IAAIC,GAAG,GAAGF,IAAI,CAACE,GAAf;EAAA,IACIC,UAAU,GAAGH,IAAI,CAACG,UADtB;;EAGA,IAAIF,MAAM,CAACG,WAAX,EAAwB;IACtB;EACD;;EAED,IAAIC,WAAW,GAAGJ,MAAM,CAACI,WAAzB;EAAA,IACIC,YAAY,GAAGL,MAAM,CAACK,YAD1B;EAEA,IAAIC,SAAS,GAAGF,WAAW,CAACG,OAAZ;EAChB;EACAF,YAFgB,KAEC,CAFjB;;EAIA,IAAIC,SAAJ,EAAe;IACb;EACD;EACD;AACF;AACA;AACA;;;EAGE,SAASE,WAAT,CAAqBC,UAArB,EAAiCC,UAAjC,EAA6C;IAC3CC,aAAa,CAACD,UAAD,CAAb;IACAb,GAAG,CAACe,IAAJ,CAAS,2BAAT;IACAH,UAAU,CAACI,QAAX,CAAoBC,MAApB;EACD;;EAED,IAAIC,MAAM,GAAGC,IAAI,CAACH,QAAL,CAAcE,MAAd,CAAqBE,WAArB,EAAb;EACA,IAAIC,UAAU,GAAGH,MAAM,CAACR,OAAP,CAAe,8BAAf,MAAmD,CAAC,CAArE;EACA,IAAIY,iBAAiB,GAAGJ,MAAM,CAACR,OAAP,CAAe,sCAAf,MAA2D,CAAC,CAApF;;EAEA,IAAIN,GAAG,IAAIiB,UAAX,EAAuB;IACrBrB,GAAG,CAACe,IAAJ,CAAS,mBAAT;IACAhB,UAAU,CAACwB,IAAX,CAAgB,kBAAhB,EAAoCpB,MAAM,CAACI,WAA3C;;IAEA,IAAI,OAAOY,IAAP,KAAgB,WAAhB,IAA+BA,IAAI,CAACK,MAAxC,EAAgD;MAC9C;MACAL,IAAI,CAACM,WAAL,CAAiB,mBAAmBC,MAAnB,CAA0BvB,MAAM,CAACI,WAAjC,CAAjB,EAAgE,GAAhE;IACD;EACF,CARD,CAQE;EARF,KASK,IAAIF,UAAU,IAAIiB,iBAAlB,EAAqC;IACxC,IAAIV,UAAU,GAAGO,IAAjB,CADwC,CACjB;;IAEvB,IAAIN,UAAU,GAAGM,IAAI,CAACQ,WAAL,CAAiB,YAAY;MAC5C,IAAIf,UAAU,CAACI,QAAX,CAAoBY,QAApB,KAAiC,QAArC,EAA+C;QAC7C;QACAjB,WAAW,CAACC,UAAD,EAAaC,UAAb,CAAX;MACD,CAHD,MAGO;QACLD,UAAU,GAAGA,UAAU,CAACiB,MAAxB;;QAEA,IAAIjB,UAAU,CAACiB,MAAX,KAAsBjB,UAA1B,EAAsC;UACpC;UACAD,WAAW,CAACC,UAAD,EAAaC,UAAb,CAAX;QACD;MACF;IACF,CAZgB,CAAjB;EAaD;AACF;;AAED,eAAeZ,SAAf", "ignoreList": []}, "metadata": {}, "sourceType": "module"}