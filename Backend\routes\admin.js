const express = require('express');
const router = express.Router();
const AllowedUser = require('../models/AllowedUser');
const InviteCode = require('../models/InviteCode');
const Group = require('../models/Group');
const User = require('../models/User');
const Configuration = require('../models/Configuration');
const AuthMiddleware = require('../middleware/auth');

// Middleware to check admin access
const requireAdmin = async (req, res, next) => {
  try {
    // For GET requests, check query params; for POST requests, check body
    const username = req.method === 'GET' ? req.query.username : req.body.username;
    
    if (!username) {
      return res.status(400).json({ error: 'Username required' });
    }
    
    const isAdmin = await Configuration.isUserAdmin(username);
    
    if (!isAdmin) {
      return res.status(403).json({ error: 'Admin access required' });
    }
    
    next();
  } catch (error) {
    console.error('Admin check error:', error);
    res.status(500).json({ error: 'Admin check failed' });
  }
};

// Get security configuration
router.get('/security-info', async (req, res) => {
  try {
    const securityInfo = await AuthMiddleware.getSecurityInfo();
    res.json(securityInfo);
  } catch (error) {
    console.error('Error getting security info:', error);
    res.status(500).json({ error: 'Failed to get security info' });
  }
});

// === CONFIGURATION MANAGEMENT ===

// Get all configuration settings
router.get('/configuration', requireAdmin, async (req, res) => {
  try {
    const config = await Configuration.getAll();
    res.json(config);
  } catch (error) {
    console.error('Error fetching configuration:', error);
    res.status(500).json({ error: 'Failed to fetch configuration' });
  }
});

// Get specific configuration value
router.get('/configuration/:key', requireAdmin, async (req, res) => {
  try {
    const { key } = req.params;
    const value = await Configuration.get(key);
    res.json({ key, value });
  } catch (error) {
    console.error('Error fetching configuration value:', error);
    res.status(500).json({ error: 'Failed to fetch configuration value' });
  }
});

// Update configuration value
router.put('/configuration/:key', requireAdmin, async (req, res) => {
  try {
    const { key } = req.params;
    const { value, description } = req.body;
    const { username } = req.body;
    
    if (value === undefined) {
      return res.status(400).json({ error: 'Value is required' });
    }

    const config = await Configuration.set(key, value, description, username);
    res.json({ message: 'Configuration updated', config });
  } catch (error) {
    console.error('Error updating configuration:', error);
    res.status(500).json({ error: 'Failed to update configuration' });
  }
});

// Get security configuration
router.get('/security-config', requireAdmin, async (req, res) => {
  try {
    const config = await Configuration.getSecurityConfig();
    res.json(config);
  } catch (error) {
    console.error('Error fetching security configuration:', error);
    res.status(500).json({ error: 'Failed to fetch security configuration' });
  }
});

// Update security configuration
router.put('/security-config', requireAdmin, async (req, res) => {
  try {
    const { username, securityMode, adminUsers } = req.body;
    
    if (!securityMode || !Array.isArray(adminUsers)) {
      return res.status(400).json({ 
        error: 'Security mode and admin users array are required' 
      });
    }

    const validModes = ['open', 'whitelist', 'invite'];
    if (!validModes.includes(securityMode)) {
      return res.status(400).json({ 
        error: 'Invalid security mode. Must be: open, whitelist, or invite' 
      });
    }

    const config = await Configuration.updateSecurityConfig(
      securityMode, 
      adminUsers, 
      username
    );
    
    res.json({ message: 'Security configuration updated', config });
  } catch (error) {
    console.error('Error updating security configuration:', error);
    res.status(500).json({ error: 'Failed to update security configuration' });
  }
});

// Initialize default configuration
router.post('/configuration/initialize', requireAdmin, async (req, res) => {
  try {
    await Configuration.initializeDefaults();
    res.json({ message: 'Default configuration initialized' });
  } catch (error) {
    console.error('Error initializing configuration:', error);
    res.status(500).json({ error: 'Failed to initialize configuration' });
  }
});

// === WHITELIST MANAGEMENT ===

// Get all allowed users
router.get('/allowed-users', requireAdmin, async (req, res) => {
  try {
    const users = await AllowedUser.getAllowedUsers();
    res.json(users);
  } catch (error) {
    console.error('Error fetching allowed users:', error);
    res.status(500).json({ error: 'Failed to fetch allowed users' });
  }
});

// Add user to whitelist
router.post('/allowed-users', requireAdmin, async (req, res) => {
  try {
    const { username: adminUser, targetUsername } = req.body;
    
    if (!targetUsername) {
      return res.status(400).json({ error: 'Target username required' });
    }

    const user = await AllowedUser.addUser(targetUsername, adminUser);
    res.json({ message: 'User added to whitelist', user });
  } catch (error) {
    console.error('Error adding user to whitelist:', error);
    res.status(500).json({ error: 'Failed to add user to whitelist' });
  }
});

// Remove user from whitelist
router.delete('/allowed-users/:targetUsername', requireAdmin, async (req, res) => {
  try {
    const { targetUsername } = req.params;
    
    const user = await AllowedUser.removeUser(targetUsername);
    if (!user) {
      return res.status(404).json({ error: 'User not found in whitelist' });
    }

    res.json({ message: 'User removed from whitelist', user });
  } catch (error) {
    console.error('Error removing user from whitelist:', error);
    res.status(500).json({ error: 'Failed to remove user from whitelist' });
  }
});

// Bulk add users to whitelist
router.post('/allowed-users/bulk', requireAdmin, async (req, res) => {
  try {
    const { username: adminUser, usernames } = req.body;
    
    if (!Array.isArray(usernames) || usernames.length === 0) {
      return res.status(400).json({ error: 'Array of usernames required' });
    }

    const results = await AllowedUser.addMultipleUsers(usernames, adminUser);
    res.json({ message: 'Bulk operation completed', results });
  } catch (error) {
    console.error('Error bulk adding users:', error);
    res.status(500).json({ error: 'Failed to bulk add users' });
  }
});

// === INVITE CODE MANAGEMENT ===

// Get all invite codes
router.get('/invite-codes', requireAdmin, async (req, res) => {
  try {
    const codes = await InviteCode.getAllCodes();
    res.json(codes);
  } catch (error) {
    console.error('Error fetching invite codes:', error);
    res.status(500).json({ error: 'Failed to fetch invite codes' });
  }
});

// Create invite code
router.post('/invite-codes', requireAdmin, async (req, res) => {
  try {
    const { 
      username: adminUser, 
      maxUses = 1, 
      expiresInHours = 24,
      customCode = null 
    } = req.body;

    const expiresIn = expiresInHours * 60 * 60 * 1000; // Convert hours to milliseconds
    
    const code = await InviteCode.createCode(adminUser, {
      maxUses,
      expiresIn,
      customCode
    });

    res.json({ 
      message: 'Invite code created', 
      code: code.code,
      details: code 
    });
  } catch (error) {
    console.error('Error creating invite code:', error);
    res.status(500).json({ error: error.message || 'Failed to create invite code' });
  }
});

// Deactivate invite code
router.delete('/invite-codes/:code', requireAdmin, async (req, res) => {
  try {
    const { code } = req.params;
    
    const result = await InviteCode.deactivateCode(code);
    if (!result) {
      return res.status(404).json({ error: 'Invite code not found' });
    }

    res.json({ message: 'Invite code deactivated', code: result });
  } catch (error) {
    console.error('Error deactivating invite code:', error);
    res.status(500).json({ error: 'Failed to deactivate invite code' });
  }
});

// Create permanent invite code
router.post('/invite-codes/permanent', requireAdmin, async (req, res) => {
  try {
    const { username: adminUser, customCode = null } = req.body;
    
    const code = await InviteCode.createPermanentCode(adminUser, customCode);
    res.json({ 
      message: 'Permanent invite code created', 
      code: code.code,
      details: code 
    });
  } catch (error) {
    console.error('Error creating permanent invite code:', error);
    res.status(500).json({ error: error.message || 'Failed to create permanent invite code' });
  }
});

// Cleanup expired codes
router.post('/invite-codes/cleanup', requireAdmin, async (req, res) => {
  try {
    const cleanedCount = await InviteCode.cleanupExpiredCodes();
    res.json({ message: `Cleaned up ${cleanedCount} expired invite codes` });
  } catch (error) {
    console.error('Error cleaning up invite codes:', error);
    res.status(500).json({ error: 'Failed to cleanup invite codes' });
  }
});

// === GROUP MANAGEMENT ===

// Get all groups
router.get('/groups', requireAdmin, async (req, res) => {
  try {
    const groups = await Group.getAllGroups();
    res.json(groups);
  } catch (error) {
    console.error('Error fetching groups:', error);
    res.status(500).json({ error: 'Failed to fetch groups' });
  }
});

// Create new group
router.post('/groups', requireAdmin, async (req, res) => {
  try {
    const { username: adminUser, name, description } = req.body;
    
    if (!name) {
      return res.status(400).json({ error: 'Group name required' });
    }

    // Get admin user ID
    const admin = await User.findByUsername(adminUser);
    if (!admin) {
      return res.status(404).json({ error: 'Admin user not found' });
    }

    const group = await Group.create(name, description || '', admin.id);
    
    // Grant all existing users access to the new group
    try {
      await Group.grantAllUsersAccessToGroup(group.id);
      console.log(`Granted all users access to new group: ${name}`);
    } catch (error) {
      console.log(`Error granting users access to group ${name}:`, error.message);
    }
    
    res.json({ message: 'Group created', group });
  } catch (error) {
    console.error('Error creating group:', error);
    if (error.code === '23505') {
      res.status(400).json({ error: 'Group name already exists' });
    } else {
      res.status(500).json({ error: 'Failed to create group' });
    }
  }
});

// Update group
router.put('/groups/:groupId', requireAdmin, async (req, res) => {
  try {
    const { groupId } = req.params;
    const { username: adminUser, name, description } = req.body;
    
    const updates = {};
    if (name !== undefined) updates.name = name;
    if (description !== undefined) updates.description = description;

    if (Object.keys(updates).length === 0) {
      return res.status(400).json({ error: 'No updates provided' });
    }

    const group = await Group.updateGroup(groupId, updates);
    if (!group) {
      return res.status(404).json({ error: 'Group not found' });
    }

    res.json({ message: 'Group updated', group });
  } catch (error) {
    console.error('Error updating group:', error);
    if (error.code === '23505') {
      res.status(400).json({ error: 'Group name already exists' });
    } else {
      res.status(500).json({ error: 'Failed to update group' });
    }
  }
});

// Delete group
router.delete('/groups/:groupId', requireAdmin, async (req, res) => {
  try {
    const { groupId } = req.params;
    
    const group = await Group.deactivateGroup(groupId);
    if (!group) {
      return res.status(404).json({ error: 'Group not found' });
    }

    res.json({ message: 'Group deleted', group });
  } catch (error) {
    console.error('Error deleting group:', error);
    res.status(500).json({ error: 'Failed to delete group' });
  }
});

// === USER MANAGEMENT ===

// Get all users with password status
router.get('/users', requireAdmin, async (req, res) => {
  try {
    const users = await User.getAllUsers();
    const usersWithPasswordStatus = await Promise.all(
      users.map(async (user) => {
        const hasPassword = await User.hasPassword(user.username);
        return { ...user, hasPassword };
      })
    );
    res.json(usersWithPasswordStatus);
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ error: 'Failed to fetch users' });
  }
});

// Reset user password
router.post('/users/:username/reset-password', requireAdmin, async (req, res) => {
  try {
    const { username: targetUsername } = req.params;
    const { username: adminUser, newPassword } = req.body;
    
    if (!newPassword) {
      return res.status(400).json({ error: 'New password required' });
    }
    
    if (newPassword.length < 6) {
      return res.status(400).json({ error: 'New password must be at least 6 characters long' });
    }
    
    await User.resetPassword(targetUsername, newPassword);
    
    res.json({ message: 'Password reset successfully' });
  } catch (error) {
    console.error('Error resetting password:', error);
    if (error.message === 'User not found') {
      res.status(404).json({ error: 'User not found' });
    } else {
      res.status(500).json({ error: 'Failed to reset password' });
    }
  }
});

// Set user password
router.post('/users/:username/set-password', requireAdmin, async (req, res) => {
  try {
    const { username: targetUsername } = req.params;
    const { username: adminUser, password } = req.body;
    
    if (!password) {
      return res.status(400).json({ error: 'Password required' });
    }
    
    if (password.length < 6) {
      return res.status(400).json({ error: 'Password must be at least 6 characters long' });
    }
    
    const user = await User.findByUsername(targetUsername);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    await User.setPassword(user.id, password);
    
    res.json({ message: 'Password set successfully' });
  } catch (error) {
    console.error('Error setting password:', error);
    res.status(500).json({ error: 'Failed to set password' });
  }
});

// Create user with password
router.post('/users', requireAdmin, async (req, res) => {
  try {
    const { username: adminUser, targetUsername, password } = req.body;
    
    if (!targetUsername || !password) {
      return res.status(400).json({ error: 'Username and password required' });
    }
    
    if (password.length < 6) {
      return res.status(400).json({ error: 'Password must be at least 6 characters long' });
    }
    
    const user = await User.createUserWithPassword(targetUsername, password);
    
    res.json({ message: 'User created successfully', user: { id: user.id, username: user.username } });
  } catch (error) {
    console.error('Error creating user:', error);
    if (error.message === 'User already exists') {
      res.status(400).json({ error: 'User already exists' });
    } else {
      res.status(500).json({ error: 'Failed to create user' });
    }
  }
});

// === USER GROUP PERMISSIONS ===

// Get user permissions for all groups
router.get('/users/:username/permissions', requireAdmin, async (req, res) => {
  try {
    const { username } = req.params;
    
    const user = await User.findByUsername(username);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    const permissions = await Group.getUserPermissions(user.id);
    res.json(permissions);
  } catch (error) {
    console.error('Error fetching user permissions:', error);
    res.status(500).json({ error: 'Failed to fetch user permissions' });
  }
});

// Grant user access to group
router.post('/groups/:groupId/users', requireAdmin, async (req, res) => {
  try {
    const { groupId } = req.params;
    const { username: adminUser, targetUsername } = req.body;
    
    if (!targetUsername) {
      return res.status(400).json({ error: 'Target username required' });
    }

    // Get admin user ID
    const admin = await User.findByUsername(adminUser);
    if (!admin) {
      return res.status(404).json({ error: 'Admin user not found' });
    }

    // Get target user
    const targetUser = await User.findByUsername(targetUsername);
    if (!targetUser) {
      return res.status(404).json({ error: 'Target user not found' });
    }

    // Check if group exists
    const group = await Group.findById(groupId);
    if (!group) {
      return res.status(404).json({ error: 'Group not found' });
    }

    const permission = await Group.grantUserAccess(targetUser.id, groupId, admin.id);
    res.json({ message: 'User access granted', permission });
  } catch (error) {
    console.error('Error granting user access:', error);
    res.status(500).json({ error: 'Failed to grant user access' });
  }
});

// Revoke user access from group
router.delete('/groups/:groupId/users/:username', requireAdmin, async (req, res) => {
  try {
    const { groupId, username } = req.params;
    
    const user = await User.findByUsername(username);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    const permission = await Group.revokeUserAccess(user.id, groupId);
    if (!permission) {
      return res.status(404).json({ error: 'User access not found' });
    }

    res.json({ message: 'User access revoked', permission });
  } catch (error) {
    console.error('Error revoking user access:', error);
    res.status(500).json({ error: 'Failed to revoke user access' });
  }
});

// Get group users
router.get('/groups/:groupId/users', requireAdmin, async (req, res) => {
  try {
    const { groupId } = req.params;
    
    const users = await Group.getGroupUsers(groupId);
    res.json(users);
  } catch (error) {
    console.error('Error fetching group users:', error);
    res.status(500).json({ error: 'Failed to fetch group users' });
  }
});

module.exports = router;
