{"ast": null, "code": "import _asyncToGenerator from \"R:/chateye/Frontend/chateye-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/api.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\n\nfunction AdminPanelComponent_div_25_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1, \"Loading...\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AdminPanelComponent_div_25_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1, \"No users in whitelist\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AdminPanelComponent_div_25_div_17_div_1_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_25_div_17_div_1_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const user_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r9 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r9.removeUserFromWhitelist(user_r7.username));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 24);\n    i0.ɵɵelement(2, \"path\", 38);\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AdminPanelComponent_div_25_div_17_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\")(2, \"p\", 33);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 34);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 35)(7, \"span\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, AdminPanelComponent_div_25_div_17_div_1_button_9_Template, 3, 0, \"button\", 36);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const user_r7 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(user_r7.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" Added by \", user_r7.added_by, \" on \", ctx_r6.formatDate(user_r7.added_at), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(\"px-2 py-1 text-xs rounded-full \" + (user_r7.is_active ? \"bg-green-100 text-green-800\" : \"bg-red-100 text-red-800\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", user_r7.is_active ? \"Active\" : \"Inactive\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", user_r7.is_active);\n  }\n}\n\nfunction AdminPanelComponent_div_25_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtemplate(1, AdminPanelComponent_div_25_div_17_div_1_Template, 10, 7, \"div\", 31);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.allowedUsers);\n  }\n}\n\nfunction AdminPanelComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19)(2, \"h3\", 20);\n    i0.ɵɵtext(3, \" Add User to Whitelist \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 21)(5, \"input\", 22);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminPanelComponent_div_25_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.newUsername = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_25_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.addUserToWhitelist());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(7, \"svg\", 24);\n    i0.ɵɵelement(8, \"path\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10, \"Add User\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(11, \"div\")(12, \"h3\", 20);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 26);\n    i0.ɵɵtemplate(15, AdminPanelComponent_div_25_div_15_Template, 2, 0, \"div\", 27);\n    i0.ɵɵtemplate(16, AdminPanelComponent_div_25_div_16_Template, 2, 0, \"div\", 27);\n    i0.ɵɵtemplate(17, AdminPanelComponent_div_25_div_17_Template, 2, 1, \"div\", 28);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.newUsername);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.newUsername.trim());\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" Allowed Users (\", ctx_r0.allowedUsers.length, \") \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.loading && ctx_r0.allowedUsers.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.loading && ctx_r0.allowedUsers.length > 0);\n  }\n}\n\nfunction AdminPanelComponent_div_26_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1, \"Loading...\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AdminPanelComponent_div_26_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1, \"No invite codes\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AdminPanelComponent_div_26_div_28_div_1__svg_svg_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 55);\n    i0.ɵɵelement(1, \"path\", 56);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AdminPanelComponent_div_26_div_28_div_1__svg_svg_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 24);\n    i0.ɵɵelement(1, \"path\", 57);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AdminPanelComponent_div_26_div_28_div_1_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_26_div_28_div_1_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const code_r19 = i0.ɵɵnextContext().$implicit;\n      const ctx_r24 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r24.deactivateInviteCode(code_r19.code));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 24);\n    i0.ɵɵelement(2, \"path\", 38);\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AdminPanelComponent_div_26_div_28_div_1_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const code_r19 = i0.ɵɵnextContext().$implicit;\n    const ctx_r23 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"Expires: \", ctx_r23.formatDate(code_r19.expires_at), \"\");\n  }\n}\n\nfunction AdminPanelComponent_div_26_div_28_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47)(2, \"div\", 3)(3, \"code\", 48);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_26_div_28_div_1_Template_button_click_5_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r29);\n      const code_r19 = restoredCtx.$implicit;\n      const ctx_r28 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r28.copyToClipboard(code_r19.code));\n    });\n    i0.ɵɵtemplate(6, AdminPanelComponent_div_26_div_28_div_1__svg_svg_6_Template, 2, 0, \"svg\", 50);\n    i0.ɵɵtemplate(7, AdminPanelComponent_div_26_div_28_div_1__svg_svg_7_Template, 2, 0, \"svg\", 51);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 35)(9, \"span\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, AdminPanelComponent_div_26_div_28_div_1_button_11_Template, 3, 0, \"button\", 36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 52)(13, \"div\", 53)(14, \"span\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 53)(19, \"span\");\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, AdminPanelComponent_div_26_div_28_div_1_span_21_Template, 2, 1, \"span\", 54);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const code_r19 = ctx.$implicit;\n    const ctx_r18 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", code_r19.code, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r18.copiedCode === code_r19.code);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r18.copiedCode !== code_r19.code);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(\"px-2 py-1 text-xs rounded-full \" + (code_r19.status === \"active\" ? \"bg-green-100 text-green-800\" : code_r19.status === \"expired\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", code_r19.status, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", code_r19.status === \"active\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"Uses: \", code_r19.current_uses, \"/\", code_r19.max_uses, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Created by: \", code_r19.created_by, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Created: \", ctx_r18.formatDate(code_r19.created_at), \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", code_r19.expires_at);\n  }\n}\n\nfunction AdminPanelComponent_div_26_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtemplate(1, AdminPanelComponent_div_26_div_28_div_1_Template, 22, 12, \"div\", 45);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r17.inviteCodes);\n  }\n}\n\nfunction AdminPanelComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19)(2, \"h3\", 20);\n    i0.ɵɵtext(3, \" Create Invite Code \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 39)(5, \"div\")(6, \"label\", 40);\n    i0.ɵɵtext(7, \" Max Uses \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"input\", 41);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminPanelComponent_div_26_Template_input_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.newInviteConfig.maxUses = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\")(10, \"label\", 40);\n    i0.ɵɵtext(11, \" Expires In (Hours) \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"input\", 42);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminPanelComponent_div_26_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r32 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r32.newInviteConfig.expiresInHours = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\")(14, \"label\", 40);\n    i0.ɵɵtext(15, \" Custom Code (Optional) \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"input\", 43);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminPanelComponent_div_26_Template_input_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r33 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r33.newInviteConfig.customCode = $event);\n    })(\"input\", function AdminPanelComponent_div_26_Template_input_input_16_listener($event) {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r34.onCustomCodeInput($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_26_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.createInviteCode());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(18, \"svg\", 24);\n    i0.ɵɵelement(19, \"path\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(20, \"span\");\n    i0.ɵɵtext(21, \"Create Invite Code\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\")(23, \"h3\", 20);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 26);\n    i0.ɵɵtemplate(26, AdminPanelComponent_div_26_div_26_Template, 2, 0, \"div\", 27);\n    i0.ɵɵtemplate(27, AdminPanelComponent_div_26_div_27_Template, 2, 0, \"div\", 27);\n    i0.ɵɵtemplate(28, AdminPanelComponent_div_26_div_28_Template, 2, 1, \"div\", 28);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.newInviteConfig.maxUses);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.newInviteConfig.expiresInHours);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.newInviteConfig.customCode);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" Invite Codes (\", ctx_r1.inviteCodes.length, \") \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading && ctx_r1.inviteCodes.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading && ctx_r1.inviteCodes.length > 0);\n  }\n}\n\nfunction AdminPanelComponent_div_27_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1, \"Loading...\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AdminPanelComponent_div_27_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1, \"No groups\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AdminPanelComponent_div_27_div_25_div_1_p_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 67);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const group_r45 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(group_r45.description);\n  }\n}\n\nfunction AdminPanelComponent_div_27_div_25_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r49 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_27_div_25_div_1_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r49);\n      const group_r45 = restoredCtx.$implicit;\n      const ctx_r48 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r48.onGroupSelect(group_r45));\n    });\n    i0.ɵɵelementStart(1, \"div\", 64)(2, \"div\")(3, \"p\", 33);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, AdminPanelComponent_div_27_div_25_div_1_p_5_Template, 2, 1, \"p\", 65);\n    i0.ɵɵelementStart(6, \"p\", 66);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_27_div_25_div_1_Template_button_click_8_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r49);\n      const group_r45 = restoredCtx.$implicit;\n      const ctx_r50 = i0.ɵɵnextContext(3);\n      ctx_r50.deleteGroup(group_r45.id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(9, \"svg\", 24);\n    i0.ɵɵelement(10, \"path\", 38);\n    i0.ɵɵelementEnd()()()();\n  }\n\n  if (rf & 2) {\n    const group_r45 = ctx.$implicit;\n    const ctx_r44 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(\"p-4 cursor-pointer hover:bg-gray-50 \" + ((ctx_r44.selectedGroup == null ? null : ctx_r44.selectedGroup.id) === group_r45.id ? \"bg-primary-50 border-r-4 border-primary-500\" : \"\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(group_r45.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", group_r45.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Created by \", group_r45.created_by_username || \"System\", \" \");\n  }\n}\n\nfunction AdminPanelComponent_div_27_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtemplate(1, AdminPanelComponent_div_27_div_25_div_1_Template, 11, 5, \"div\", 63);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r38 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r38.groups);\n  }\n}\n\nfunction AdminPanelComponent_div_27_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1, \"Select a group to manage users\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AdminPanelComponent_div_27_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1, \"Loading...\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AdminPanelComponent_div_27_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1, \"No users in this group\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AdminPanelComponent_div_27_div_33_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r54 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\")(2, \"p\", 33);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 34);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_27_div_33_div_1_Template_button_click_6_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r54);\n      const user_r52 = restoredCtx.$implicit;\n      const ctx_r53 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r53.revokeUserAccess(user_r52.username));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(7, \"svg\", 24);\n    i0.ɵɵelement(8, \"path\", 38);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const user_r52 = ctx.$implicit;\n    const ctx_r51 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(user_r52.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Added on \", ctx_r51.formatDate(user_r52.granted_at), \" \");\n  }\n}\n\nfunction AdminPanelComponent_div_27_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtemplate(1, AdminPanelComponent_div_27_div_33_div_1_Template, 9, 2, \"div\", 31);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r42 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r42.groupUsers);\n  }\n}\n\nfunction AdminPanelComponent_div_27_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r56 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 68)(1, \"h4\", 69);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 21)(4, \"input\", 70);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminPanelComponent_div_27_div_34_Template_input_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r56);\n      const ctx_r55 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r55.newUsername = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_27_div_34_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r56);\n      const ctx_r57 = i0.ɵɵnextContext(2);\n      ctx_r57.grantUserAccess(ctx_r57.newUsername);\n      return i0.ɵɵresetView(ctx_r57.newUsername = \"\");\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(6, \"svg\", 24);\n    i0.ɵɵelement(7, \"path\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9, \"Add User\");\n    i0.ɵɵelementEnd()()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r43 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Add User to \", ctx_r43.selectedGroup.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r43.newUsername);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", !ctx_r43.newUsername.trim());\n  }\n}\n\nfunction AdminPanelComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r59 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19)(2, \"h3\", 20);\n    i0.ɵɵtext(3, \" Create New Group \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 58)(5, \"div\")(6, \"label\", 40);\n    i0.ɵɵtext(7, \" Group Name \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"input\", 59);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminPanelComponent_div_27_Template_input_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r59);\n      const ctx_r58 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r58.newGroupName = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\")(10, \"label\", 40);\n    i0.ɵɵtext(11, \" Description (Optional) \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"textarea\", 60);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminPanelComponent_div_27_Template_textarea_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r59);\n      const ctx_r60 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r60.newGroupDescription = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_27_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r59);\n      const ctx_r61 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r61.createGroup());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(14, \"svg\", 24);\n    i0.ɵɵelement(15, \"path\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17, \"Create Group\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(18, \"div\", 61)(19, \"div\")(20, \"h3\", 20);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 26);\n    i0.ɵɵtemplate(23, AdminPanelComponent_div_27_div_23_Template, 2, 0, \"div\", 27);\n    i0.ɵɵtemplate(24, AdminPanelComponent_div_27_div_24_Template, 2, 0, \"div\", 27);\n    i0.ɵɵtemplate(25, AdminPanelComponent_div_27_div_25_Template, 2, 1, \"div\", 28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\")(27, \"h3\", 20);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 26);\n    i0.ɵɵtemplate(30, AdminPanelComponent_div_27_div_30_Template, 2, 0, \"div\", 27);\n    i0.ɵɵtemplate(31, AdminPanelComponent_div_27_div_31_Template, 2, 0, \"div\", 27);\n    i0.ɵɵtemplate(32, AdminPanelComponent_div_27_div_32_Template, 2, 0, \"div\", 27);\n    i0.ɵɵtemplate(33, AdminPanelComponent_div_27_div_33_Template, 2, 1, \"div\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(34, AdminPanelComponent_div_27_div_34_Template, 10, 3, \"div\", 62);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.newGroupName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.newGroupDescription);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.newGroupName.trim());\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" Groups (\", ctx_r2.groups.length, \") \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.loading && ctx_r2.groups.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.loading && ctx_r2.groups.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r2.selectedGroup ? ctx_r2.selectedGroup.name + \" Users\" : \"Select a Group\", \" (\", ctx_r2.groupUsers.length, \") \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.selectedGroup);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedGroup && ctx_r2.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedGroup && !ctx_r2.loading && ctx_r2.groupUsers.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedGroup && !ctx_r2.loading && ctx_r2.groupUsers.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedGroup);\n  }\n}\n\nexport class AdminPanelComponent {\n  constructor(apiService) {\n    this.apiService = apiService;\n    this.currentUser = null;\n    this.onClose = new EventEmitter();\n    this.activeTab = 'whitelist';\n    this.allowedUsers = [];\n    this.inviteCodes = [];\n    this.groups = [];\n    this.selectedGroup = null;\n    this.groupUsers = [];\n    this.newUsername = '';\n    this.newGroupName = '';\n    this.newGroupDescription = '';\n    this.newInviteConfig = {\n      maxUses: 1,\n      expiresInHours: 24,\n      customCode: ''\n    };\n    this.loading = false;\n    this.copiedCode = '';\n  }\n\n  ngOnInit() {\n    this.loadData();\n  }\n\n  ngOnDestroy() {// Cleanup if needed\n  }\n\n  loadData() {\n    if (this.activeTab === 'whitelist') {\n      this.fetchAllowedUsers();\n    } else if (this.activeTab === 'invites') {\n      this.fetchInviteCodes();\n    } else if (this.activeTab === 'groups') {\n      this.fetchGroups();\n    }\n  }\n\n  onTabChange(tab) {\n    this.activeTab = tab;\n    this.loadData();\n  }\n\n  onGroupSelect(group) {\n    this.selectedGroup = group;\n    this.fetchGroupUsers(group.id);\n  }\n\n  fetchAllowedUsers() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this.currentUser) return;\n\n      try {\n        _this.loading = true;\n        _this.allowedUsers = (yield _this.apiService.getAllowedUsers(_this.currentUser).toPromise()) || [];\n      } catch (error) {\n        console.error('Failed to fetch allowed users:', error);\n      } finally {\n        _this.loading = false;\n      }\n    })();\n  }\n\n  fetchInviteCodes() {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this2.currentUser) return;\n\n      try {\n        _this2.loading = true;\n        _this2.inviteCodes = (yield _this2.apiService.getInviteCodes(_this2.currentUser).toPromise()) || [];\n      } catch (error) {\n        console.error('Failed to fetch invite codes:', error);\n      } finally {\n        _this2.loading = false;\n      }\n    })();\n  }\n\n  fetchGroups() {\n    var _this3 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this3.currentUser) return;\n\n      try {\n        _this3.loading = true;\n        _this3.groups = (yield _this3.apiService.getGroups(_this3.currentUser).toPromise()) || [];\n      } catch (error) {\n        console.error('Failed to fetch groups:', error);\n      } finally {\n        _this3.loading = false;\n      }\n    })();\n  }\n\n  fetchGroupUsers(groupId) {\n    var _this4 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this4.currentUser) return;\n\n      try {\n        _this4.groupUsers = (yield _this4.apiService.getGroupUsers(_this4.currentUser, groupId).toPromise()) || [];\n      } catch (error) {\n        console.error('Failed to fetch group users:', error);\n      }\n    })();\n  }\n\n  addUserToWhitelist() {\n    var _this5 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this5.newUsername.trim() || !_this5.currentUser) return;\n\n      try {\n        yield _this5.apiService.addAllowedUser(_this5.currentUser, _this5.newUsername.trim()).toPromise();\n        _this5.newUsername = '';\n\n        _this5.fetchAllowedUsers();\n      } catch (error) {\n        alert(error.error?.error || error.message || 'Failed to add user');\n      }\n    })();\n  }\n\n  removeUserFromWhitelist(username) {\n    var _this6 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this6.currentUser) return;\n\n      try {\n        yield _this6.apiService.removeAllowedUser(_this6.currentUser, username).toPromise();\n\n        _this6.fetchAllowedUsers();\n      } catch (error) {\n        alert(error.error?.error || error.message || 'Failed to remove user');\n      }\n    })();\n  }\n\n  createInviteCode() {\n    var _this7 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this7.currentUser) return;\n\n      try {\n        const result = yield _this7.apiService.createInviteCode(_this7.currentUser, _this7.newInviteConfig).toPromise();\n        _this7.newInviteConfig = {\n          maxUses: 1,\n          expiresInHours: 24,\n          customCode: ''\n        };\n\n        _this7.fetchInviteCodes(); // Auto-copy the new code\n\n\n        if (result?.code) {\n          navigator.clipboard.writeText(result.code);\n          _this7.copiedCode = result.code;\n          setTimeout(() => _this7.copiedCode = '', 3000);\n        }\n      } catch (error) {\n        alert(error.error?.error || error.message || 'Failed to create invite code');\n      }\n    })();\n  }\n\n  deactivateInviteCode(code) {\n    var _this8 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this8.currentUser) return;\n\n      try {\n        yield _this8.apiService.deactivateInviteCode(_this8.currentUser, code).toPromise();\n\n        _this8.fetchInviteCodes();\n      } catch (error) {\n        alert(error.error?.error || error.message || 'Failed to deactivate invite code');\n      }\n    })();\n  }\n\n  createGroup() {\n    var _this9 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this9.newGroupName.trim() || !_this9.currentUser) return;\n\n      try {\n        yield _this9.apiService.createGroup(_this9.currentUser, _this9.newGroupName.trim(), _this9.newGroupDescription.trim() || undefined).toPromise();\n        _this9.newGroupName = '';\n        _this9.newGroupDescription = '';\n\n        _this9.fetchGroups();\n      } catch (error) {\n        alert(error.error?.error || error.message || 'Failed to create group');\n      }\n    })();\n  }\n\n  deleteGroup(groupId) {\n    var _this0 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!confirm('Are you sure you want to delete this group?') || !_this0.currentUser) return;\n\n      try {\n        yield _this0.apiService.deleteGroup(_this0.currentUser, groupId).toPromise();\n\n        _this0.fetchGroups();\n\n        if (_this0.selectedGroup && _this0.selectedGroup.id === groupId) {\n          _this0.selectedGroup = null;\n        }\n      } catch (error) {\n        alert(error.error?.error || error.message || 'Failed to delete group');\n      }\n    })();\n  }\n\n  grantUserAccess(username) {\n    var _this1 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this1.selectedGroup || !_this1.currentUser) return;\n\n      try {\n        yield _this1.apiService.grantUserAccess(_this1.currentUser, _this1.selectedGroup.id, username).toPromise();\n\n        _this1.fetchGroupUsers(_this1.selectedGroup.id);\n      } catch (error) {\n        alert(error.error?.error || error.message || 'Failed to grant user access');\n      }\n    })();\n  }\n\n  revokeUserAccess(username) {\n    var _this10 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this10.selectedGroup || !_this10.currentUser) return;\n\n      try {\n        yield _this10.apiService.revokeUserAccess(_this10.currentUser, _this10.selectedGroup.id, username).toPromise();\n\n        _this10.fetchGroupUsers(_this10.selectedGroup.id);\n      } catch (error) {\n        alert(error.error?.error || error.message || 'Failed to revoke user access');\n      }\n    })();\n  }\n\n  copyToClipboard(text) {\n    navigator.clipboard.writeText(text);\n    this.copiedCode = text;\n    setTimeout(() => this.copiedCode = '', 3000);\n  }\n\n  formatDate(dateString) {\n    return new Date(dateString).toLocaleString();\n  }\n\n  onCloseClick() {\n    this.onClose.emit();\n  }\n\n  onCustomCodeInput(event) {\n    const target = event.target;\n    this.newInviteConfig.customCode = target.value.toUpperCase();\n  }\n\n}\n\nAdminPanelComponent.ɵfac = function AdminPanelComponent_Factory(t) {\n  return new (t || AdminPanelComponent)(i0.ɵɵdirectiveInject(i1.ApiService));\n};\n\nAdminPanelComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: AdminPanelComponent,\n  selectors: [[\"app-admin-panel\"]],\n  inputs: {\n    currentUser: \"currentUser\"\n  },\n  outputs: {\n    onClose: \"onClose\"\n  },\n  decls: 28,\n  vars: 9,\n  consts: [[1, \"fixed\", \"inset-0\", \"bg-black\", \"bg-opacity-50\", \"flex\", \"items-center\", \"justify-center\", \"z-50\"], [1, \"bg-white\", \"rounded-lg\", \"w-full\", \"max-w-4xl\", \"h-5/6\", \"flex\", \"flex-col\"], [1, \"flex\", \"items-center\", \"justify-between\", \"p-6\", \"border-b\", \"border-gray-200\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-primary-500\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"], [1, \"text-xl\", \"font-semibold\", \"text-gray-900\"], [1, \"p-2\", \"text-gray-400\", \"hover:text-gray-600\", \"rounded-lg\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M6 18L18 6M6 6l12 12\"], [1, \"flex\", \"border-b\", \"border-gray-200\"], [3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"inline\", \"mr-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"], [1, \"flex-1\", \"p-6\", \"overflow-y-auto\"], [\"class\", \"space-y-6\", 4, \"ngIf\"], [1, \"space-y-6\"], [1, \"bg-gray-50\", \"p-4\", \"rounded-lg\"], [1, \"text-lg\", \"font-medium\", \"text-gray-900\", \"mb-3\"], [1, \"flex\", \"space-x-3\"], [\"type\", \"text\", \"name\", \"newUsername\", \"placeholder\", \"Enter username\", 1, \"flex-1\", \"px-3\", \"py-2\", \"border\", \"border-gray-300\", \"rounded-lg\", \"focus:ring-2\", \"focus:ring-primary-500\", \"focus:border-transparent\", 3, \"ngModel\", \"ngModelChange\"], [1, \"btn-primary\", \"flex\", \"items-center\", \"space-x-2\", 3, \"disabled\", \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 6v6m0 0v6m0-6h6m-6 0H6\"], [1, \"bg-white\", \"border\", \"border-gray-200\", \"rounded-lg\", \"overflow-hidden\"], [\"class\", \"p-4 text-center text-gray-500\", 4, \"ngIf\"], [\"class\", \"divide-y divide-gray-200\", 4, \"ngIf\"], [1, \"p-4\", \"text-center\", \"text-gray-500\"], [1, \"divide-y\", \"divide-gray-200\"], [\"class\", \"p-4 flex items-center justify-between\", 4, \"ngFor\", \"ngForOf\"], [1, \"p-4\", \"flex\", \"items-center\", \"justify-between\"], [1, \"font-medium\", \"text-gray-900\"], [1, \"text-sm\", \"text-gray-500\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [\"class\", \"p-1 text-red-500 hover:text-red-700\", 3, \"click\", 4, \"ngIf\"], [1, \"p-1\", \"text-red-500\", \"hover:text-red-700\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-3\", \"gap-4\", \"mb-4\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"mb-1\"], [\"type\", \"number\", \"name\", \"maxUses\", \"min\", \"1\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"border-gray-300\", \"rounded-lg\", \"focus:ring-2\", \"focus:ring-primary-500\", \"focus:border-transparent\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"number\", \"name\", \"expiresInHours\", \"min\", \"1\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"border-gray-300\", \"rounded-lg\", \"focus:ring-2\", \"focus:ring-primary-500\", \"focus:border-transparent\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"name\", \"customCode\", \"placeholder\", \"Auto-generated\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"border-gray-300\", \"rounded-lg\", \"focus:ring-2\", \"focus:ring-primary-500\", \"focus:border-transparent\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [1, \"btn-primary\", \"flex\", \"items-center\", \"space-x-2\", 3, \"click\"], [\"class\", \"p-4\", 4, \"ngFor\", \"ngForOf\"], [1, \"p-4\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-2\"], [1, \"bg-gray-100\", \"px-2\", \"py-1\", \"rounded\", \"font-mono\", \"text-sm\"], [\"title\", \"Copy code\", 1, \"p-1\", \"text-gray-500\", \"hover:text-gray-700\", 3, \"click\"], [\"class\", \"w-4 h-4 text-green-500\", \"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 4, \"ngIf\"], [\"class\", \"w-4 h-4\", \"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 4, \"ngIf\"], [1, \"text-sm\", \"text-gray-500\", \"space-y-1\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [4, \"ngIf\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-green-500\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M5 13l4 4L19 7\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\"], [1, \"space-y-4\"], [\"type\", \"text\", \"name\", \"newGroupName\", \"placeholder\", \"Enter group name\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"border-gray-300\", \"rounded-lg\", \"focus:ring-2\", \"focus:ring-primary-500\", \"focus:border-transparent\", 3, \"ngModel\", \"ngModelChange\"], [\"name\", \"newGroupDescription\", \"placeholder\", \"Enter group description\", \"rows\", \"3\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"border-gray-300\", \"rounded-lg\", \"focus:ring-2\", \"focus:ring-primary-500\", \"focus:border-transparent\", 3, \"ngModel\", \"ngModelChange\"], [1, \"grid\", \"grid-cols-1\", \"lg:grid-cols-2\", \"gap-6\"], [\"class\", \"mt-4 bg-gray-50 p-4 rounded-lg\", 4, \"ngIf\"], [3, \"class\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"justify-between\"], [\"class\", \"text-sm text-gray-500 mt-1\", 4, \"ngIf\"], [1, \"text-xs\", \"text-gray-400\", \"mt-1\"], [1, \"text-sm\", \"text-gray-500\", \"mt-1\"], [1, \"mt-4\", \"bg-gray-50\", \"p-4\", \"rounded-lg\"], [1, \"text-md\", \"font-medium\", \"text-gray-900\", \"mb-3\"], [\"type\", \"text\", \"placeholder\", \"Enter username\", 1, \"flex-1\", \"px-3\", \"py-2\", \"border\", \"border-gray-300\", \"rounded-lg\", \"focus:ring-2\", \"focus:ring-primary-500\", \"focus:border-transparent\", 3, \"ngModel\", \"ngModelChange\"]],\n  template: function AdminPanelComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n      i0.ɵɵnamespaceSVG();\n      i0.ɵɵelementStart(4, \"svg\", 4);\n      i0.ɵɵelement(5, \"path\", 5);\n      i0.ɵɵelementEnd();\n      i0.ɵɵnamespaceHTML();\n      i0.ɵɵelementStart(6, \"h2\", 6);\n      i0.ɵɵtext(7, \"Admin Panel\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(8, \"button\", 7);\n      i0.ɵɵlistener(\"click\", function AdminPanelComponent_Template_button_click_8_listener() {\n        return ctx.onCloseClick();\n      });\n      i0.ɵɵnamespaceSVG();\n      i0.ɵɵelementStart(9, \"svg\", 8);\n      i0.ɵɵelement(10, \"path\", 9);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵnamespaceHTML();\n      i0.ɵɵelementStart(11, \"div\", 10)(12, \"button\", 11);\n      i0.ɵɵlistener(\"click\", function AdminPanelComponent_Template_button_click_12_listener() {\n        return ctx.onTabChange(\"whitelist\");\n      });\n      i0.ɵɵnamespaceSVG();\n      i0.ɵɵelementStart(13, \"svg\", 12);\n      i0.ɵɵelement(14, \"path\", 13);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtext(15, \" Allowed Users \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵnamespaceHTML();\n      i0.ɵɵelementStart(16, \"button\", 11);\n      i0.ɵɵlistener(\"click\", function AdminPanelComponent_Template_button_click_16_listener() {\n        return ctx.onTabChange(\"invites\");\n      });\n      i0.ɵɵnamespaceSVG();\n      i0.ɵɵelementStart(17, \"svg\", 12);\n      i0.ɵɵelement(18, \"path\", 14);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtext(19, \" Invite Codes \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵnamespaceHTML();\n      i0.ɵɵelementStart(20, \"button\", 11);\n      i0.ɵɵlistener(\"click\", function AdminPanelComponent_Template_button_click_20_listener() {\n        return ctx.onTabChange(\"groups\");\n      });\n      i0.ɵɵnamespaceSVG();\n      i0.ɵɵelementStart(21, \"svg\", 12);\n      i0.ɵɵelement(22, \"path\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtext(23, \" Groups \");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵnamespaceHTML();\n      i0.ɵɵelementStart(24, \"div\", 16);\n      i0.ɵɵtemplate(25, AdminPanelComponent_div_25_Template, 18, 6, \"div\", 17);\n      i0.ɵɵtemplate(26, AdminPanelComponent_div_26_Template, 29, 7, \"div\", 17);\n      i0.ɵɵtemplate(27, AdminPanelComponent_div_27_Template, 35, 14, \"div\", 17);\n      i0.ɵɵelementEnd()()();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(12);\n      i0.ɵɵclassMap(\"px-6 py-3 text-sm font-medium \" + (ctx.activeTab === \"whitelist\" ? \"text-primary-600 border-b-2 border-primary-600\" : \"text-gray-500 hover:text-gray-700\"));\n      i0.ɵɵadvance(4);\n      i0.ɵɵclassMap(\"px-6 py-3 text-sm font-medium \" + (ctx.activeTab === \"invites\" ? \"text-primary-600 border-b-2 border-primary-600\" : \"text-gray-500 hover:text-gray-700\"));\n      i0.ɵɵadvance(4);\n      i0.ɵɵclassMap(\"px-6 py-3 text-sm font-medium \" + (ctx.activeTab === \"groups\" ? \"text-primary-600 border-b-2 border-primary-600\" : \"text-gray-500 hover:text-gray-700\"));\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"whitelist\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"invites\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"groups\");\n    }\n  },\n  dependencies: [i2.NgForOf, i2.NgIf, i3.DefaultValueAccessor, i3.NumberValueAccessor, i3.NgControlStatus, i3.MinValidator, i3.NgModel],\n  styles: [\"\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFkbWluLXBhbmVsLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsd0NBQXdDIiwiZmlsZSI6ImFkbWluLXBhbmVsLmNvbXBvbmVudC5jc3MiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBDb21wb25lbnQtc3BlY2lmaWMgc3R5bGVzIGlmIG5lZWRlZCAqL1xyXG4iXX0= */\"]\n});", "map": {"version": 3, "mappings": ";AAAA,SAAmCA,YAAnC,QAA0E,eAA1E;;;;;;;;ICuFYC;IAA2DA;IAAUA;;;;;;IACrEA;IAAyFA;IAAqBA;;;;;;;;IAaxGA;IAEEA;MAAAA;MAAA;MAAA;MAAA,OAASA,gEAAT;IAA+C,CAA/C;IAGAA;IAAAA;IACEA;IACFA;;;;;;IAlBNA,gCAAqF,CAArF,EAAqF,KAArF,EAAqF,CAArF,EAAqF,GAArF,EAAqF,EAArF;IAEyCA;IAAmBA;IACxDA;IACEA;IACFA;IAEFA,gCAAyC,CAAzC,EAAyC,MAAzC;IAEIA;IACFA;IACAA;IASFA;;;;;;IAlBuCA;IAAAA;IAEnCA;IAAAA;IAIIA;IAAAA;IACJA;IAAAA;IAGCA;IAAAA;;;;;;IAbTA;IACEA;IAsBFA;;;;;IAtBwBA;IAAAA;;;;;;;;IApC9BA,gCAAyD,CAAzD,EAAyD,KAAzD,EAAyD,EAAzD,EAAyD,CAAzD,EAAyD,IAAzD,EAAyD,EAAzD;IAIMA;IACFA;IACAA,gCAA4B,CAA5B,EAA4B,OAA5B,EAA4B,EAA5B;IAIIA;MAAAA;MAAA;MAAA;IAAA;IAHFA;IAOAA;IACEA;MAAAA;MAAA;MAAA,OAASA,4CAAT;IAA6B,CAA7B;IAIAA;IAAAA;IACEA;IACFA;IACAA;IAAAA;IAAMA;IAAQA;IAMpBA,6BAAK,EAAL,EAAK,IAAL,EAAK,EAAL;IAEIA;IACFA;IACAA;IACEA;IACAA;IACAA;IAwBFA;;;;;IAjDIA;IAAAA;IAMAA;IAAAA;IAcFA;IAAAA;IAGMA;IAAAA;IACAA;IAAAA;IACAA;IAAAA;;;;;;IA2FNA;IAA2DA;IAAUA;;;;;;IACrEA;IAAwFA;IAAeA;;;;;;IAa7FA;IAAAA;IACEA;IACFA;;;;;;IACAA;IAAAA;IACEA;IACFA;;;;;;;;IAOFA;IAEEA;MAAAA;MAAA;MAAA;MAAA,OAASA,2DAAT;IAAwC,CAAxC;IAGAA;IAAAA;IACEA;IACFA;;;;;;IAWFA;IAA8BA;IAA0CA;;;;;;IAA1CA;IAAAA;;;;;;;;IAzCpCA,gCAAkD,CAAlD,EAAkD,KAAlD,EAAkD,EAAlD,EAAkD,CAAlD,EAAkD,KAAlD,EAAkD,CAAlD,EAAkD,CAAlD,EAAkD,MAAlD,EAAkD,EAAlD;IAIQA;IACFA;IACAA;IACEA;MAAA;MAAA;MAAA;MAAA,OAASA,sDAAT;IAAmC,CAAnC;IAIAA;IAGAA;IAGFA;IAEFA,gCAAyC,CAAzC,EAAyC,MAAzC;IAEIA;IACFA;IACAA;IASFA;IAEFA,iCAA6C,EAA7C,EAA6C,KAA7C,EAA6C,EAA7C,EAA6C,EAA7C,EAA6C,MAA7C;IAEUA;IAAiDA;IACvDA;IAAMA;IAAiCA;IAEzCA,iCAAyC,EAAzC,EAAyC,MAAzC;IACQA;IAA0CA;IAChDA;IACFA;;;;;;IAtCIA;IAAAA;IAOMA;IAAAA;IAGAA;IAAAA;IAMFA;IAAAA;IACJA;IAAAA;IAGCA;IAAAA;IAYGA;IAAAA;IACAA;IAAAA;IAGAA;IAAAA;IACCA;IAAAA;;;;;;IA1CfA;IACEA;IA6CFA;;;;;IA7CwBA;IAAAA;;;;;;;;IAjE9BA,gCAAuD,CAAvD,EAAuD,KAAvD,EAAuD,EAAvD,EAAuD,CAAvD,EAAuD,IAAvD,EAAuD,EAAvD;IAIMA;IACFA;IACAA,gCAAwD,CAAxD,EAAwD,KAAxD,EAAwD,CAAxD,EAAwD,OAAxD,EAAwD,EAAxD;IAGMA;IACFA;IACAA;IAIEA;MAAAA;MAAA;MAAA,OAAaA,wDAAb;IACX,CADW;IAJFA;IAQFA,4BAAK,EAAL,EAAK,OAAL,EAAK,EAAL;IAEIA;IACFA;IACAA;IAIEA;MAAAA;MAAA;MAAA,OAAaA,+DAAb;IACX,CADW;IAJFA;IAQFA,6BAAK,EAAL,EAAK,OAAL,EAAK,EAAL;IAEIA;IACFA;IACAA;IAGEA;MAAAA;MAAA;MAAA,OAAaA,2DAAb;IACX,CADW,EAAwC,OAAxC,EAAwC;MAAAA;MAAA;MAAA,OAC/BA,iDAD+B;IACN,CADlC;IAHFA;IAUJA;IACEA;MAAAA;MAAA;MAAA,OAASA,0CAAT;IAA2B,CAA3B;IAGAA;IAAAA;IACEA;IACFA;IACAA;IAAAA;IAAMA;IAAkBA;IAK5BA,6BAAK,EAAL,EAAK,IAAL,EAAK,EAAL;IAEIA;IACFA;IACAA;IACEA;IACAA;IACAA;IA+CFA;;;;;IAhGMA;IAAAA;IAYAA;IAAAA;IAWAA;IAAAA;IAqBJA;IAAAA;IAGMA;IAAAA;IACAA;IAAAA;IACAA;IAAAA;;;;;;IAwGJA;IAA2DA;IAAUA;;;;;;IACrEA;IAAmFA;IAASA;;;;;;IAUpFA;IAAgEA;IAAuBA;;;;;IAAvBA;IAAAA;;;;;;;;IARtEA;IAGEA;MAAA;MAAA;MAAA;MAAA,OAASA,gDAAT;IAA6B,CAA7B;IAEAA,gCAA+C,CAA/C,EAA+C,KAA/C,EAA+C,CAA/C,EAA+C,GAA/C,EAA+C,EAA/C;IAEyCA;IAAgBA;IACrDA;IACAA;IACEA;IACFA;IAEFA;IACEA;MAAA;MAAA;MAAA;MAASC;MAAqB,OAAED,wCAAF;IAA0B,CAAxD;IAGAA;IAAAA;IACEA;IACFA;;;;;;IAjBJA;IAKyCA;IAAAA;IACjCA;IAAAA;IAEFA;IAAAA;;;;;;IAXVA;IACEA;IAuBFA;;;;;IAtBsBA;IAAAA;;;;;;IAgCtBA;IAAkEA;IAA8BA;;;;;;IAChGA;IAA4EA;IAAUA;;;;;;IACtFA;IAAwGA;IAAsBA;;;;;;;;IAE5HA,gCAAmF,CAAnF,EAAmF,KAAnF,EAAmF,CAAnF,EAAmF,GAAnF,EAAmF,EAAnF;IAEyCA;IAAmBA;IACxDA;IACEA;IACFA;IAEFA;IACEA;MAAA;MAAA;MAAA;MAAA,OAASA,2DAAT;IAAwC,CAAxC;IAGAA;IAAAA;IACEA;IACFA;;;;;;IAXqCA;IAAAA;IAEnCA;IAAAA;;;;;;IALRA;IACEA;IAgBFA;;;;;IAhBwBA;IAAAA;;;;;;;;IAoB1BA,gCAAkE,CAAlE,EAAkE,IAAlE,EAAkE,EAAlE;IAEIA;IACFA;IACAA,gCAA4B,CAA5B,EAA4B,OAA5B,EAA4B,EAA5B;IAGIA;MAAAA;MAAA;MAAA;IAAA;IAFFA;IAMAA;IACEA;MAAAA;MAAA;MAASE;MAA4B,4CAAgB,EAAhB;IAAkB,CAAvD;IAIAF;IAAAA;IACEA;IACFA;IACAA;IAAAA;IAAMA;IAAQA;;;;;IAjBhBA;IAAAA;IAKEA;IAAAA;IAMAA;IAAAA;;;;;;;;IA7HZA,gCAAsD,CAAtD,EAAsD,KAAtD,EAAsD,EAAtD,EAAsD,CAAtD,EAAsD,IAAtD,EAAsD,EAAtD;IAIMA;IACFA;IACAA,gCAAuB,CAAvB,EAAuB,KAAvB,EAAuB,CAAvB,EAAuB,OAAvB,EAAuB,EAAvB;IAGMA;IACFA;IACAA;IAGEA;MAAAA;MAAA;MAAA;IAAA;IAHFA;IAQFA,4BAAK,EAAL,EAAK,OAAL,EAAK,EAAL;IAEIA;IACFA;IACAA;IAEEA;MAAAA;MAAA;MAAA;IAAA;IAIDA;IAEHA;IACEA;MAAAA;MAAA;MAAA,OAASA,qCAAT;IAAsB,CAAtB;IAIAA;IAAAA;IACEA;IACFA;IACAA;IAAAA;IAAMA;IAAYA;IAMxBA,iCAAmD,EAAnD,EAAmD,KAAnD,EAAmD,EAAnD,EAAmD,IAAnD,EAAmD,EAAnD;IAIMA;IACFA;IACAA;IACEA;IACAA;IACAA;IAyBFA;IAIFA,6BAAK,EAAL,EAAK,IAAL,EAAK,EAAL;IAEIA;IACFA;IACAA;IACEA;IACAA;IACAA;IACAA;IAkBFA;IAGAA;IAuBFA;;;;;IAzHMA;IAAAA;IAWAA;IAAAA;IAQFA;IAAAA;IAgBAA;IAAAA;IAGMA;IAAAA;IACAA;IAAAA;IACAA;IAAAA;IA+BNA;IAAAA;IAGMA;IAAAA;IACAA;IAAAA;IACAA;IAAAA;IACAA;IAAAA;IAqBFA;IAAAA;;;;ADlVlB,OAAM,MAAOG,mBAAP,CAA0B;EAqB9BC,YAAoBC,UAApB,EAA0C;IAAtB;IApBX,mBAA6B,IAA7B;IACC,eAAU,IAAIN,YAAJ,EAAV;IAEV,iBAAY,WAAZ;IACA,oBAA8B,EAA9B;IACA,mBAA4B,EAA5B;IACA,cAAkB,EAAlB;IACA,qBAA8B,IAA9B;IACA,kBAA0B,EAA1B;IACA,mBAAc,EAAd;IACA,oBAAe,EAAf;IACA,2BAAsB,EAAtB;IACA,uBAAkB;MAChBO,OAAO,EAAE,CADO;MAEhBC,cAAc,EAAE,EAFA;MAGhBC,UAAU,EAAE;IAHI,CAAlB;IAKA,eAAU,KAAV;IACA,kBAAa,EAAb;EAE8C;;EAE9CC,QAAQ;IACN,KAAKC,QAAL;EACD;;EAEDC,WAAW,IACT;EACD;;EAEDD,QAAQ;IACN,IAAI,KAAKE,SAAL,KAAmB,WAAvB,EAAoC;MAClC,KAAKC,iBAAL;IACD,CAFD,MAEO,IAAI,KAAKD,SAAL,KAAmB,SAAvB,EAAkC;MACvC,KAAKE,gBAAL;IACD,CAFM,MAEA,IAAI,KAAKF,SAAL,KAAmB,QAAvB,EAAiC;MACtC,KAAKG,WAAL;IACD;EACF;;EAEDC,WAAW,CAACC,GAAD,EAAY;IACrB,KAAKL,SAAL,GAAiBK,GAAjB;IACA,KAAKP,QAAL;EACD;;EAEDQ,aAAa,CAACC,KAAD,EAAa;IACxB,KAAKC,aAAL,GAAqBD,KAArB;IACA,KAAKE,eAAL,CAAqBF,KAAK,CAACG,EAA3B;EACD;;EAEaT,iBAAiB;IAAA;;IAAA;MAC7B,IAAI,CAAC,KAAI,CAACU,WAAV,EAAuB;;MAEvB,IAAI;QACF,KAAI,CAACC,OAAL,GAAe,IAAf;QACA,KAAI,CAACC,YAAL,GAAoB,OAAM,KAAI,CAACpB,UAAL,CAAgBqB,eAAhB,CAAgC,KAAI,CAACH,WAArC,EAAkDI,SAAlD,EAAN,KAAuE,EAA3F;MACD,CAHD,CAGE,OAAOC,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,gCAAd,EAAgDA,KAAhD;MACD,CALD,SAKU;QACR,KAAI,CAACJ,OAAL,GAAe,KAAf;MACD;IAV4B;EAW9B;;EAEaV,gBAAgB;IAAA;;IAAA;MAC5B,IAAI,CAAC,MAAI,CAACS,WAAV,EAAuB;;MAEvB,IAAI;QACF,MAAI,CAACC,OAAL,GAAe,IAAf;QACA,MAAI,CAACM,WAAL,GAAmB,OAAM,MAAI,CAACzB,UAAL,CAAgB0B,cAAhB,CAA+B,MAAI,CAACR,WAApC,EAAiDI,SAAjD,EAAN,KAAsE,EAAzF;MACD,CAHD,CAGE,OAAOC,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,+BAAd,EAA+CA,KAA/C;MACD,CALD,SAKU;QACR,MAAI,CAACJ,OAAL,GAAe,KAAf;MACD;IAV2B;EAW7B;;EAEaT,WAAW;IAAA;;IAAA;MACvB,IAAI,CAAC,MAAI,CAACQ,WAAV,EAAuB;;MAEvB,IAAI;QACF,MAAI,CAACC,OAAL,GAAe,IAAf;QACA,MAAI,CAACQ,MAAL,GAAc,OAAM,MAAI,CAAC3B,UAAL,CAAgB4B,SAAhB,CAA0B,MAAI,CAACV,WAA/B,EAA4CI,SAA5C,EAAN,KAAiE,EAA/E;MACD,CAHD,CAGE,OAAOC,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,yBAAd,EAAyCA,KAAzC;MACD,CALD,SAKU;QACR,MAAI,CAACJ,OAAL,GAAe,KAAf;MACD;IAVsB;EAWxB;;EAEaH,eAAe,CAACa,OAAD,EAAgB;IAAA;;IAAA;MAC3C,IAAI,CAAC,MAAI,CAACX,WAAV,EAAuB;;MAEvB,IAAI;QACF,MAAI,CAACY,UAAL,GAAkB,OAAM,MAAI,CAAC9B,UAAL,CAAgB+B,aAAhB,CAA8B,MAAI,CAACb,WAAnC,EAAgDW,OAAhD,EAAyDP,SAAzD,EAAN,KAA8E,EAAhG;MACD,CAFD,CAEE,OAAOC,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,8BAAd,EAA8CA,KAA9C;MACD;IAP0C;EAQ5C;;EAEKS,kBAAkB;IAAA;;IAAA;MACtB,IAAI,CAAC,MAAI,CAACC,WAAL,CAAiBC,IAAjB,EAAD,IAA4B,CAAC,MAAI,CAAChB,WAAtC,EAAmD;;MAEnD,IAAI;QACF,MAAM,MAAI,CAAClB,UAAL,CAAgBmC,cAAhB,CAA+B,MAAI,CAACjB,WAApC,EAAiD,MAAI,CAACe,WAAL,CAAiBC,IAAjB,EAAjD,EAA0EZ,SAA1E,EAAN;QACA,MAAI,CAACW,WAAL,GAAmB,EAAnB;;QACA,MAAI,CAACzB,iBAAL;MACD,CAJD,CAIE,OAAOe,KAAP,EAAmB;QACnBa,KAAK,CAACb,KAAK,CAACA,KAAN,EAAaA,KAAb,IAAsBA,KAAK,CAACc,OAA5B,IAAuC,oBAAxC,CAAL;MACD;IATqB;EAUvB;;EAEKC,uBAAuB,CAACC,QAAD,EAAiB;IAAA;;IAAA;MAC5C,IAAI,CAAC,MAAI,CAACrB,WAAV,EAAuB;;MAEvB,IAAI;QACF,MAAM,MAAI,CAAClB,UAAL,CAAgBwC,iBAAhB,CAAkC,MAAI,CAACtB,WAAvC,EAAoDqB,QAApD,EAA8DjB,SAA9D,EAAN;;QACA,MAAI,CAACd,iBAAL;MACD,CAHD,CAGE,OAAOe,KAAP,EAAmB;QACnBa,KAAK,CAACb,KAAK,CAACA,KAAN,EAAaA,KAAb,IAAsBA,KAAK,CAACc,OAA5B,IAAuC,uBAAxC,CAAL;MACD;IAR2C;EAS7C;;EAEKI,gBAAgB;IAAA;;IAAA;MACpB,IAAI,CAAC,MAAI,CAACvB,WAAV,EAAuB;;MAEvB,IAAI;QACF,MAAMwB,MAAM,SAAS,MAAI,CAAC1C,UAAL,CAAgByC,gBAAhB,CAAiC,MAAI,CAACvB,WAAtC,EAAmD,MAAI,CAACyB,eAAxD,EAAyErB,SAAzE,EAArB;QACA,MAAI,CAACqB,eAAL,GAAuB;UACrB1C,OAAO,EAAE,CADY;UAErBC,cAAc,EAAE,EAFK;UAGrBC,UAAU,EAAE;QAHS,CAAvB;;QAKA,MAAI,CAACM,gBAAL,GAPE,CASF;;;QACA,IAAIiC,MAAM,EAAEE,IAAZ,EAAkB;UAChBC,SAAS,CAACC,SAAV,CAAoBC,SAApB,CAA8BL,MAAM,CAACE,IAArC;UACA,MAAI,CAACI,UAAL,GAAkBN,MAAM,CAACE,IAAzB;UACAK,UAAU,CAAC,MAAM,MAAI,CAACD,UAAL,GAAkB,EAAzB,EAA6B,IAA7B,CAAV;QACD;MACF,CAfD,CAeE,OAAOzB,KAAP,EAAmB;QACnBa,KAAK,CAACb,KAAK,CAACA,KAAN,EAAaA,KAAb,IAAsBA,KAAK,CAACc,OAA5B,IAAuC,8BAAxC,CAAL;MACD;IApBmB;EAqBrB;;EAEKa,oBAAoB,CAACN,IAAD,EAAa;IAAA;;IAAA;MACrC,IAAI,CAAC,MAAI,CAAC1B,WAAV,EAAuB;;MAEvB,IAAI;QACF,MAAM,MAAI,CAAClB,UAAL,CAAgBkD,oBAAhB,CAAqC,MAAI,CAAChC,WAA1C,EAAuD0B,IAAvD,EAA6DtB,SAA7D,EAAN;;QACA,MAAI,CAACb,gBAAL;MACD,CAHD,CAGE,OAAOc,KAAP,EAAmB;QACnBa,KAAK,CAACb,KAAK,CAACA,KAAN,EAAaA,KAAb,IAAsBA,KAAK,CAACc,OAA5B,IAAuC,kCAAxC,CAAL;MACD;IARoC;EAStC;;EAEKc,WAAW;IAAA;;IAAA;MACf,IAAI,CAAC,MAAI,CAACC,YAAL,CAAkBlB,IAAlB,EAAD,IAA6B,CAAC,MAAI,CAAChB,WAAvC,EAAoD;;MAEpD,IAAI;QACF,MAAM,MAAI,CAAClB,UAAL,CAAgBmD,WAAhB,CACJ,MAAI,CAACjC,WADD,EAEJ,MAAI,CAACkC,YAAL,CAAkBlB,IAAlB,EAFI,EAGJ,MAAI,CAACmB,mBAAL,CAAyBnB,IAAzB,MAAmCoB,SAH/B,EAIJhC,SAJI,EAAN;QAKA,MAAI,CAAC8B,YAAL,GAAoB,EAApB;QACA,MAAI,CAACC,mBAAL,GAA2B,EAA3B;;QACA,MAAI,CAAC3C,WAAL;MACD,CATD,CASE,OAAOa,KAAP,EAAmB;QACnBa,KAAK,CAACb,KAAK,CAACA,KAAN,EAAaA,KAAb,IAAsBA,KAAK,CAACc,OAA5B,IAAuC,wBAAxC,CAAL;MACD;IAdc;EAehB;;EAEKkB,WAAW,CAAC1B,OAAD,EAAgB;IAAA;;IAAA;MAC/B,IAAI,CAAC2B,OAAO,CAAC,6CAAD,CAAR,IAA2D,CAAC,MAAI,CAACtC,WAArE,EAAkF;;MAElF,IAAI;QACF,MAAM,MAAI,CAAClB,UAAL,CAAgBuD,WAAhB,CAA4B,MAAI,CAACrC,WAAjC,EAA8CW,OAA9C,EAAuDP,SAAvD,EAAN;;QACA,MAAI,CAACZ,WAAL;;QACA,IAAI,MAAI,CAACK,aAAL,IAAsB,MAAI,CAACA,aAAL,CAAmBE,EAAnB,KAA0BY,OAApD,EAA6D;UAC3D,MAAI,CAACd,aAAL,GAAqB,IAArB;QACD;MACF,CAND,CAME,OAAOQ,KAAP,EAAmB;QACnBa,KAAK,CAACb,KAAK,CAACA,KAAN,EAAaA,KAAb,IAAsBA,KAAK,CAACc,OAA5B,IAAuC,wBAAxC,CAAL;MACD;IAX8B;EAYhC;;EAEKoB,eAAe,CAAClB,QAAD,EAAiB;IAAA;;IAAA;MACpC,IAAI,CAAC,MAAI,CAACxB,aAAN,IAAuB,CAAC,MAAI,CAACG,WAAjC,EAA8C;;MAE9C,IAAI;QACF,MAAM,MAAI,CAAClB,UAAL,CAAgByD,eAAhB,CAAgC,MAAI,CAACvC,WAArC,EAAkD,MAAI,CAACH,aAAL,CAAmBE,EAArE,EAAyEsB,QAAzE,EAAmFjB,SAAnF,EAAN;;QACA,MAAI,CAACN,eAAL,CAAqB,MAAI,CAACD,aAAL,CAAmBE,EAAxC;MACD,CAHD,CAGE,OAAOM,KAAP,EAAmB;QACnBa,KAAK,CAACb,KAAK,CAACA,KAAN,EAAaA,KAAb,IAAsBA,KAAK,CAACc,OAA5B,IAAuC,6BAAxC,CAAL;MACD;IARmC;EASrC;;EAEKqB,gBAAgB,CAACnB,QAAD,EAAiB;IAAA;;IAAA;MACrC,IAAI,CAAC,OAAI,CAACxB,aAAN,IAAuB,CAAC,OAAI,CAACG,WAAjC,EAA8C;;MAE9C,IAAI;QACF,MAAM,OAAI,CAAClB,UAAL,CAAgB0D,gBAAhB,CAAiC,OAAI,CAACxC,WAAtC,EAAmD,OAAI,CAACH,aAAL,CAAmBE,EAAtE,EAA0EsB,QAA1E,EAAoFjB,SAApF,EAAN;;QACA,OAAI,CAACN,eAAL,CAAqB,OAAI,CAACD,aAAL,CAAmBE,EAAxC;MACD,CAHD,CAGE,OAAOM,KAAP,EAAmB;QACnBa,KAAK,CAACb,KAAK,CAACA,KAAN,EAAaA,KAAb,IAAsBA,KAAK,CAACc,OAA5B,IAAuC,8BAAxC,CAAL;MACD;IARoC;EAStC;;EAEDsB,eAAe,CAACC,IAAD,EAAa;IAC1Bf,SAAS,CAACC,SAAV,CAAoBC,SAApB,CAA8Ba,IAA9B;IACA,KAAKZ,UAAL,GAAkBY,IAAlB;IACAX,UAAU,CAAC,MAAM,KAAKD,UAAL,GAAkB,EAAzB,EAA6B,IAA7B,CAAV;EACD;;EAEDa,UAAU,CAACC,UAAD,EAAmB;IAC3B,OAAO,IAAIC,IAAJ,CAASD,UAAT,EAAqBE,cAArB,EAAP;EACD;;EAEDC,YAAY;IACV,KAAKC,OAAL,CAAaC,IAAb;EACD;;EAEDC,iBAAiB,CAACC,KAAD,EAAa;IAC5B,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAArB;IACA,KAAK3B,eAAL,CAAqBxC,UAArB,GAAkCmE,MAAM,CAACC,KAAP,CAAaC,WAAb,EAAlC;EACD;;AAnO6B;;;mBAAnB1E,qBAAmBH;AAAA;;;QAAnBG;EAAmB2E;EAAAC;IAAAxD;EAAA;EAAAyD;IAAAT;EAAA;EAAAU;EAAAC;EAAAC;EAAAC;IAAA;MCRhCpF,+BAAwF,CAAxF,EAAwF,KAAxF,EAAwF,CAAxF,EAAwF,CAAxF,EAAwF,KAAxF,EAAwF,CAAxF,EAAwF,CAAxF,EAAwF,KAAxF,EAAwF,CAAxF;MAKQA;MAAAA;MACEA;MACFA;MACAA;MAAAA;MAAgDA;MAAWA;MAE7DA;MACEA;QAAA,OAASqF,kBAAT;MAAuB,CAAvB;MAGArF;MAAAA;MACEA;MACFA;MAKJA;MAAAA,iCAA2C,EAA3C,EAA2C,QAA3C,EAA2C,EAA3C;MAEIA;QAAA,OAASqF,gBAAY,WAAZ,CAAT;MAAiC,CAAjC;MAGArF;MAAAA;MACEA;MACFA;MACAA;MACFA;MACAA;MAAAA;MACEA;QAAA,OAASqF,gBAAY,SAAZ,CAAT;MAA+B,CAA/B;MAGArF;MAAAA;MACEA;MACFA;MACAA;MACFA;MACAA;MAAAA;MACEA;QAAA,OAASqF,gBAAY,QAAZ,CAAT;MAA8B,CAA9B;MAGArF;MAAAA;MACEA;MACFA;MACAA;MACFA;MAIFA;MAAAA;MAEEA;MAgEAA;MAoHAA;MA0IFA;;;;MA5VIA;MAAAA;MASAA;MAAAA;MASAA;MAAAA;MAYIA;MAAAA;MAgEAA;MAAAA;MAoHAA;MAAAA", "names": ["EventEmitter", "i0", "ctx_r50", "ctx_r57", "AdminPanelComponent", "constructor", "apiService", "maxUses", "expiresInHours", "customCode", "ngOnInit", "loadData", "ngOnDestroy", "activeTab", "fetchAllowedUsers", "fetchInviteCodes", "fetchGroups", "onTabChange", "tab", "onGroupSelect", "group", "selectedGroup", "fetchGroupUsers", "id", "currentUser", "loading", "allowedUsers", "getAllowedUsers", "to<PERSON>romise", "error", "console", "inviteCodes", "getInviteCodes", "groups", "getGroups", "groupId", "groupUsers", "getGroupUsers", "addUser<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "newUsername", "trim", "addAllowedUser", "alert", "message", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "username", "removeAllowedUser", "createInviteCode", "result", "newInviteConfig", "code", "navigator", "clipboard", "writeText", "copiedCode", "setTimeout", "deactivateInviteCode", "createGroup", "newGroupName", "newGroupDescription", "undefined", "deleteGroup", "confirm", "grantUserAccess", "revokeUserAccess", "copyToClipboard", "text", "formatDate", "dateString", "Date", "toLocaleString", "onCloseClick", "onClose", "emit", "onCustomCodeInput", "event", "target", "value", "toUpperCase", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["R:\\chateye\\Frontend\\chateye-angular\\src\\app\\components\\admin-panel\\admin-panel.component.ts", "R:\\chateye\\Frontend\\chateye-angular\\src\\app\\components\\admin-panel\\admin-panel.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';\r\nimport { ApiService, AllowedUser, InviteCode, Group, GroupUser } from '../../services/api.service';\r\n\r\n@Component({\r\n  selector: 'app-admin-panel',\r\n  templateUrl: './admin-panel.component.html',\r\n  styleUrls: ['./admin-panel.component.css']\r\n})\r\nexport class AdminPanelComponent implements OnInit, OnDestroy {\r\n  @Input() currentUser: string | null = null;\r\n  @Output() onClose = new EventEmitter<void>();\r\n\r\n  activeTab = 'whitelist';\r\n  allowedUsers: AllowedUser[] = [];\r\n  inviteCodes: InviteCode[] = [];\r\n  groups: Group[] = [];\r\n  selectedGroup: Group | null = null;\r\n  groupUsers: GroupUser[] = [];\r\n  newUsername = '';\r\n  newGroupName = '';\r\n  newGroupDescription = '';\r\n  newInviteConfig = {\r\n    maxUses: 1,\r\n    expiresInHours: 24,\r\n    customCode: ''\r\n  };\r\n  loading = false;\r\n  copiedCode = '';\r\n\r\n  constructor(private apiService: ApiService) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loadData();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Cleanup if needed\r\n  }\r\n\r\n  loadData(): void {\r\n    if (this.activeTab === 'whitelist') {\r\n      this.fetchAllowedUsers();\r\n    } else if (this.activeTab === 'invites') {\r\n      this.fetchInviteCodes();\r\n    } else if (this.activeTab === 'groups') {\r\n      this.fetchGroups();\r\n    }\r\n  }\r\n\r\n  onTabChange(tab: string): void {\r\n    this.activeTab = tab;\r\n    this.loadData();\r\n  }\r\n\r\n  onGroupSelect(group: Group): void {\r\n    this.selectedGroup = group;\r\n    this.fetchGroupUsers(group.id);\r\n  }\r\n\r\n  private async fetchAllowedUsers(): Promise<void> {\r\n    if (!this.currentUser) return;\r\n    \r\n    try {\r\n      this.loading = true;\r\n      this.allowedUsers = await this.apiService.getAllowedUsers(this.currentUser).toPromise() || [];\r\n    } catch (error) {\r\n      console.error('Failed to fetch allowed users:', error);\r\n    } finally {\r\n      this.loading = false;\r\n    }\r\n  }\r\n\r\n  private async fetchInviteCodes(): Promise<void> {\r\n    if (!this.currentUser) return;\r\n    \r\n    try {\r\n      this.loading = true;\r\n      this.inviteCodes = await this.apiService.getInviteCodes(this.currentUser).toPromise() || [];\r\n    } catch (error) {\r\n      console.error('Failed to fetch invite codes:', error);\r\n    } finally {\r\n      this.loading = false;\r\n    }\r\n  }\r\n\r\n  private async fetchGroups(): Promise<void> {\r\n    if (!this.currentUser) return;\r\n    \r\n    try {\r\n      this.loading = true;\r\n      this.groups = await this.apiService.getGroups(this.currentUser).toPromise() || [];\r\n    } catch (error) {\r\n      console.error('Failed to fetch groups:', error);\r\n    } finally {\r\n      this.loading = false;\r\n    }\r\n  }\r\n\r\n  private async fetchGroupUsers(groupId: string): Promise<void> {\r\n    if (!this.currentUser) return;\r\n    \r\n    try {\r\n      this.groupUsers = await this.apiService.getGroupUsers(this.currentUser, groupId).toPromise() || [];\r\n    } catch (error) {\r\n      console.error('Failed to fetch group users:', error);\r\n    }\r\n  }\r\n\r\n  async addUserToWhitelist(): Promise<void> {\r\n    if (!this.newUsername.trim() || !this.currentUser) return;\r\n    \r\n    try {\r\n      await this.apiService.addAllowedUser(this.currentUser, this.newUsername.trim()).toPromise();\r\n      this.newUsername = '';\r\n      this.fetchAllowedUsers();\r\n    } catch (error: any) {\r\n      alert(error.error?.error || error.message || 'Failed to add user');\r\n    }\r\n  }\r\n\r\n  async removeUserFromWhitelist(username: string): Promise<void> {\r\n    if (!this.currentUser) return;\r\n    \r\n    try {\r\n      await this.apiService.removeAllowedUser(this.currentUser, username).toPromise();\r\n      this.fetchAllowedUsers();\r\n    } catch (error: any) {\r\n      alert(error.error?.error || error.message || 'Failed to remove user');\r\n    }\r\n  }\r\n\r\n  async createInviteCode(): Promise<void> {\r\n    if (!this.currentUser) return;\r\n    \r\n    try {\r\n      const result = await this.apiService.createInviteCode(this.currentUser, this.newInviteConfig).toPromise();\r\n      this.newInviteConfig = {\r\n        maxUses: 1,\r\n        expiresInHours: 24,\r\n        customCode: ''\r\n      };\r\n      this.fetchInviteCodes();\r\n      \r\n      // Auto-copy the new code\r\n      if (result?.code) {\r\n        navigator.clipboard.writeText(result.code);\r\n        this.copiedCode = result.code;\r\n        setTimeout(() => this.copiedCode = '', 3000);\r\n      }\r\n    } catch (error: any) {\r\n      alert(error.error?.error || error.message || 'Failed to create invite code');\r\n    }\r\n  }\r\n\r\n  async deactivateInviteCode(code: string): Promise<void> {\r\n    if (!this.currentUser) return;\r\n    \r\n    try {\r\n      await this.apiService.deactivateInviteCode(this.currentUser, code).toPromise();\r\n      this.fetchInviteCodes();\r\n    } catch (error: any) {\r\n      alert(error.error?.error || error.message || 'Failed to deactivate invite code');\r\n    }\r\n  }\r\n\r\n  async createGroup(): Promise<void> {\r\n    if (!this.newGroupName.trim() || !this.currentUser) return;\r\n    \r\n    try {\r\n      await this.apiService.createGroup(\r\n        this.currentUser, \r\n        this.newGroupName.trim(), \r\n        this.newGroupDescription.trim() || undefined\r\n      ).toPromise();\r\n      this.newGroupName = '';\r\n      this.newGroupDescription = '';\r\n      this.fetchGroups();\r\n    } catch (error: any) {\r\n      alert(error.error?.error || error.message || 'Failed to create group');\r\n    }\r\n  }\r\n\r\n  async deleteGroup(groupId: string): Promise<void> {\r\n    if (!confirm('Are you sure you want to delete this group?') || !this.currentUser) return;\r\n    \r\n    try {\r\n      await this.apiService.deleteGroup(this.currentUser, groupId).toPromise();\r\n      this.fetchGroups();\r\n      if (this.selectedGroup && this.selectedGroup.id === groupId) {\r\n        this.selectedGroup = null;\r\n      }\r\n    } catch (error: any) {\r\n      alert(error.error?.error || error.message || 'Failed to delete group');\r\n    }\r\n  }\r\n\r\n  async grantUserAccess(username: string): Promise<void> {\r\n    if (!this.selectedGroup || !this.currentUser) return;\r\n    \r\n    try {\r\n      await this.apiService.grantUserAccess(this.currentUser, this.selectedGroup.id, username).toPromise();\r\n      this.fetchGroupUsers(this.selectedGroup.id);\r\n    } catch (error: any) {\r\n      alert(error.error?.error || error.message || 'Failed to grant user access');\r\n    }\r\n  }\r\n\r\n  async revokeUserAccess(username: string): Promise<void> {\r\n    if (!this.selectedGroup || !this.currentUser) return;\r\n    \r\n    try {\r\n      await this.apiService.revokeUserAccess(this.currentUser, this.selectedGroup.id, username).toPromise();\r\n      this.fetchGroupUsers(this.selectedGroup.id);\r\n    } catch (error: any) {\r\n      alert(error.error?.error || error.message || 'Failed to revoke user access');\r\n    }\r\n  }\r\n\r\n  copyToClipboard(text: string): void {\r\n    navigator.clipboard.writeText(text);\r\n    this.copiedCode = text;\r\n    setTimeout(() => this.copiedCode = '', 3000);\r\n  }\r\n\r\n  formatDate(dateString: string): string {\r\n    return new Date(dateString).toLocaleString();\r\n  }\r\n\r\n  onCloseClick(): void {\r\n    this.onClose.emit();\r\n  }\r\n\r\n  onCustomCodeInput(event: Event): void {\r\n    const target = event.target as HTMLInputElement;\r\n    this.newInviteConfig.customCode = target.value.toUpperCase();\r\n  }\r\n}\r\n", "<div class=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n  <div class=\"bg-white rounded-lg w-full max-w-4xl h-5/6 flex flex-col\">\r\n    <!-- Header -->\r\n    <div class=\"flex items-center justify-between p-6 border-b border-gray-200\">\r\n      <div class=\"flex items-center space-x-3\">\r\n        <svg class=\"w-6 h-6 text-primary-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"></path>\r\n        </svg>\r\n        <h2 class=\"text-xl font-semibold text-gray-900\">Admin Panel</h2>\r\n      </div>\r\n      <button\r\n        (click)=\"onCloseClick()\"\r\n        class=\"p-2 text-gray-400 hover:text-gray-600 rounded-lg\"\r\n      >\r\n        <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\r\n        </svg>\r\n      </button>\r\n    </div>\r\n\r\n    <!-- Tabs -->\r\n    <div class=\"flex border-b border-gray-200\">\r\n      <button\r\n        (click)=\"onTabChange('whitelist')\"\r\n        [class]=\"'px-6 py-3 text-sm font-medium ' + (activeTab === 'whitelist' ? 'text-primary-600 border-b-2 border-primary-600' : 'text-gray-500 hover:text-gray-700')\"\r\n      >\r\n        <svg class=\"w-4 h-4 inline mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\"></path>\r\n        </svg>\r\n        Allowed Users\r\n      </button>\r\n      <button\r\n        (click)=\"onTabChange('invites')\"\r\n        [class]=\"'px-6 py-3 text-sm font-medium ' + (activeTab === 'invites' ? 'text-primary-600 border-b-2 border-primary-600' : 'text-gray-500 hover:text-gray-700')\"\r\n      >\r\n        <svg class=\"w-4 h-4 inline mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z\"></path>\r\n        </svg>\r\n        Invite Codes\r\n      </button>\r\n      <button\r\n        (click)=\"onTabChange('groups')\"\r\n        [class]=\"'px-6 py-3 text-sm font-medium ' + (activeTab === 'groups' ? 'text-primary-600 border-b-2 border-primary-600' : 'text-gray-500 hover:text-gray-700')\"\r\n      >\r\n        <svg class=\"w-4 h-4 inline mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"></path>\r\n        </svg>\r\n        Groups\r\n      </button>\r\n    </div>\r\n\r\n    <!-- Content -->\r\n    <div class=\"flex-1 p-6 overflow-y-auto\">\r\n      <!-- Whitelist Tab -->\r\n      <div *ngIf=\"activeTab === 'whitelist'\" class=\"space-y-6\">\r\n        <!-- Add User Form -->\r\n        <div class=\"bg-gray-50 p-4 rounded-lg\">\r\n          <h3 class=\"text-lg font-medium text-gray-900 mb-3\">\r\n            Add User to Whitelist\r\n          </h3>\r\n          <div class=\"flex space-x-3\">\r\n            <input\r\n              type=\"text\"\r\n              name=\"newUsername\"\r\n              [(ngModel)]=\"newUsername\"\r\n              placeholder=\"Enter username\"\r\n              class=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\r\n            />\r\n            <button\r\n              (click)=\"addUserToWhitelist()\"\r\n              [disabled]=\"!newUsername.trim()\"\r\n              class=\"btn-primary flex items-center space-x-2\"\r\n            >\r\n              <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\r\n              </svg>\r\n              <span>Add User</span>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Users List -->\r\n        <div>\r\n          <h3 class=\"text-lg font-medium text-gray-900 mb-3\">\r\n            Allowed Users ({{ allowedUsers.length }})\r\n          </h3>\r\n          <div class=\"bg-white border border-gray-200 rounded-lg overflow-hidden\">\r\n            <div *ngIf=\"loading\" class=\"p-4 text-center text-gray-500\">Loading...</div>\r\n            <div *ngIf=\"!loading && allowedUsers.length === 0\" class=\"p-4 text-center text-gray-500\">No users in whitelist</div>\r\n            <div *ngIf=\"!loading && allowedUsers.length > 0\" class=\"divide-y divide-gray-200\">\r\n              <div *ngFor=\"let user of allowedUsers\" class=\"p-4 flex items-center justify-between\">\r\n                <div>\r\n                  <p class=\"font-medium text-gray-900\">{{ user.username }}</p>\r\n                  <p class=\"text-sm text-gray-500\">\r\n                    Added by {{ user.added_by }} on {{ formatDate(user.added_at) }}\r\n                  </p>\r\n                </div>\r\n                <div class=\"flex items-center space-x-2\">\r\n                  <span [class]=\"'px-2 py-1 text-xs rounded-full ' + (user.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800')\">\r\n                    {{ user.is_active ? 'Active' : 'Inactive' }}\r\n                  </span>\r\n                  <button\r\n                    *ngIf=\"user.is_active\"\r\n                    (click)=\"removeUserFromWhitelist(user.username)\"\r\n                    class=\"p-1 text-red-500 hover:text-red-700\"\r\n                  >\r\n                    <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"></path>\r\n                    </svg>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Invites Tab -->\r\n      <div *ngIf=\"activeTab === 'invites'\" class=\"space-y-6\">\r\n        <!-- Create Invite Form -->\r\n        <div class=\"bg-gray-50 p-4 rounded-lg\">\r\n          <h3 class=\"text-lg font-medium text-gray-900 mb-3\">\r\n            Create Invite Code\r\n          </h3>\r\n          <div class=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\">\r\n            <div>\r\n              <label class=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                Max Uses\r\n              </label>\r\n              <input\r\n                type=\"number\"\r\n                name=\"maxUses\"\r\n                min=\"1\"\r\n                [(ngModel)]=\"newInviteConfig.maxUses\"\r\n                class=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\r\n              />\r\n            </div>\r\n            <div>\r\n              <label class=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                Expires In (Hours)\r\n              </label>\r\n              <input\r\n                type=\"number\"\r\n                name=\"expiresInHours\"\r\n                min=\"1\"\r\n                [(ngModel)]=\"newInviteConfig.expiresInHours\"\r\n                class=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\r\n              />\r\n            </div>\r\n            <div>\r\n              <label class=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                Custom Code (Optional)\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                name=\"customCode\"\r\n                [(ngModel)]=\"newInviteConfig.customCode\"\r\n                (input)=\"onCustomCodeInput($event)\"\r\n                placeholder=\"Auto-generated\"\r\n                class=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\r\n              />\r\n            </div>\r\n          </div>\r\n          <button\r\n            (click)=\"createInviteCode()\"\r\n            class=\"btn-primary flex items-center space-x-2\"\r\n          >\r\n            <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\r\n            </svg>\r\n            <span>Create Invite Code</span>\r\n          </button>\r\n        </div>\r\n\r\n        <!-- Invite Codes List -->\r\n        <div>\r\n          <h3 class=\"text-lg font-medium text-gray-900 mb-3\">\r\n            Invite Codes ({{ inviteCodes.length }})\r\n          </h3>\r\n          <div class=\"bg-white border border-gray-200 rounded-lg overflow-hidden\">\r\n            <div *ngIf=\"loading\" class=\"p-4 text-center text-gray-500\">Loading...</div>\r\n            <div *ngIf=\"!loading && inviteCodes.length === 0\" class=\"p-4 text-center text-gray-500\">No invite codes</div>\r\n            <div *ngIf=\"!loading && inviteCodes.length > 0\" class=\"divide-y divide-gray-200\">\r\n              <div *ngFor=\"let code of inviteCodes\" class=\"p-4\">\r\n                <div class=\"flex items-center justify-between mb-2\">\r\n                  <div class=\"flex items-center space-x-3\">\r\n                    <code class=\"bg-gray-100 px-2 py-1 rounded font-mono text-sm\">\r\n                      {{ code.code }}\r\n                    </code>\r\n                    <button\r\n                      (click)=\"copyToClipboard(code.code)\"\r\n                      class=\"p-1 text-gray-500 hover:text-gray-700\"\r\n                      title=\"Copy code\"\r\n                    >\r\n                      <svg *ngIf=\"copiedCode === code.code\" class=\"w-4 h-4 text-green-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\"></path>\r\n                      </svg>\r\n                      <svg *ngIf=\"copiedCode !== code.code\" class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\"></path>\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                  <div class=\"flex items-center space-x-2\">\r\n                    <span [class]=\"'px-2 py-1 text-xs rounded-full ' + (code.status === 'active' ? 'bg-green-100 text-green-800' : code.status === 'expired' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800')\">\r\n                      {{ code.status }}\r\n                    </span>\r\n                    <button\r\n                      *ngIf=\"code.status === 'active'\"\r\n                      (click)=\"deactivateInviteCode(code.code)\"\r\n                      class=\"p-1 text-red-500 hover:text-red-700\"\r\n                    >\r\n                      <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"></path>\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n                <div class=\"text-sm text-gray-500 space-y-1\">\r\n                  <div class=\"flex items-center space-x-4\">\r\n                    <span>Uses: {{ code.current_uses }}/{{ code.max_uses }}</span>\r\n                    <span>Created by: {{ code.created_by }}</span>\r\n                  </div>\r\n                  <div class=\"flex items-center space-x-4\">\r\n                    <span>Created: {{ formatDate(code.created_at) }}</span>\r\n                    <span *ngIf=\"code.expires_at\">Expires: {{ formatDate(code.expires_at) }}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Groups Tab -->\r\n      <div *ngIf=\"activeTab === 'groups'\" class=\"space-y-6\">\r\n        <!-- Create Group Form -->\r\n        <div class=\"bg-gray-50 p-4 rounded-lg\">\r\n          <h3 class=\"text-lg font-medium text-gray-900 mb-3\">\r\n            Create New Group\r\n          </h3>\r\n          <div class=\"space-y-4\">\r\n            <div>\r\n              <label class=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                Group Name\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                name=\"newGroupName\"\r\n                [(ngModel)]=\"newGroupName\"\r\n                placeholder=\"Enter group name\"\r\n                class=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\r\n              />\r\n            </div>\r\n            <div>\r\n              <label class=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                Description (Optional)\r\n              </label>\r\n              <textarea\r\n                name=\"newGroupDescription\"\r\n                [(ngModel)]=\"newGroupDescription\"\r\n                placeholder=\"Enter group description\"\r\n                rows=\"3\"\r\n                class=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\r\n              ></textarea>\r\n            </div>\r\n            <button\r\n              (click)=\"createGroup()\"\r\n              [disabled]=\"!newGroupName.trim()\"\r\n              class=\"btn-primary flex items-center space-x-2\"\r\n            >\r\n              <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\r\n              </svg>\r\n              <span>Create Group</span>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Groups List -->\r\n        <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n          <!-- Groups -->\r\n          <div>\r\n            <h3 class=\"text-lg font-medium text-gray-900 mb-3\">\r\n              Groups ({{ groups.length }})\r\n            </h3>\r\n            <div class=\"bg-white border border-gray-200 rounded-lg overflow-hidden\">\r\n              <div *ngIf=\"loading\" class=\"p-4 text-center text-gray-500\">Loading...</div>\r\n              <div *ngIf=\"!loading && groups.length === 0\" class=\"p-4 text-center text-gray-500\">No groups</div>\r\n              <div *ngIf=\"!loading && groups.length > 0\" class=\"divide-y divide-gray-200\">\r\n                <div\r\n                  *ngFor=\"let group of groups\"\r\n                  [class]=\"'p-4 cursor-pointer hover:bg-gray-50 ' + (selectedGroup?.id === group.id ? 'bg-primary-50 border-r-4 border-primary-500' : '')\"\r\n                  (click)=\"onGroupSelect(group)\"\r\n                >\r\n                  <div class=\"flex items-center justify-between\">\r\n                    <div>\r\n                      <p class=\"font-medium text-gray-900\">{{ group.name }}</p>\r\n                      <p *ngIf=\"group.description\" class=\"text-sm text-gray-500 mt-1\">{{ group.description }}</p>\r\n                      <p class=\"text-xs text-gray-400 mt-1\">\r\n                        Created by {{ group.created_by_username || 'System' }}\r\n                      </p>\r\n                    </div>\r\n                    <button\r\n                      (click)=\"deleteGroup(group.id); $event.stopPropagation()\"\r\n                      class=\"p-1 text-red-500 hover:text-red-700\"\r\n                    >\r\n                      <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"></path>\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Group Users -->\r\n          <div>\r\n            <h3 class=\"text-lg font-medium text-gray-900 mb-3\">\r\n              {{ selectedGroup ? selectedGroup.name + ' Users' : 'Select a Group' }} ({{ groupUsers.length }})\r\n            </h3>\r\n            <div class=\"bg-white border border-gray-200 rounded-lg overflow-hidden\">\r\n              <div *ngIf=\"!selectedGroup\" class=\"p-4 text-center text-gray-500\">Select a group to manage users</div>\r\n              <div *ngIf=\"selectedGroup && loading\" class=\"p-4 text-center text-gray-500\">Loading...</div>\r\n              <div *ngIf=\"selectedGroup && !loading && groupUsers.length === 0\" class=\"p-4 text-center text-gray-500\">No users in this group</div>\r\n              <div *ngIf=\"selectedGroup && !loading && groupUsers.length > 0\" class=\"divide-y divide-gray-200\">\r\n                <div *ngFor=\"let user of groupUsers\" class=\"p-4 flex items-center justify-between\">\r\n                  <div>\r\n                    <p class=\"font-medium text-gray-900\">{{ user.username }}</p>\r\n                    <p class=\"text-sm text-gray-500\">\r\n                      Added on {{ formatDate(user.granted_at) }}\r\n                    </p>\r\n                  </div>\r\n                  <button\r\n                    (click)=\"revokeUserAccess(user.username)\"\r\n                    class=\"p-1 text-red-500 hover:text-red-700\"\r\n                  >\r\n                    <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"></path>\r\n                    </svg>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Add User to Group -->\r\n            <div *ngIf=\"selectedGroup\" class=\"mt-4 bg-gray-50 p-4 rounded-lg\">\r\n              <h4 class=\"text-md font-medium text-gray-900 mb-3\">\r\n                Add User to {{ selectedGroup.name }}\r\n              </h4>\r\n              <div class=\"flex space-x-3\">\r\n                <input\r\n                  type=\"text\"\r\n                  [(ngModel)]=\"newUsername\"\r\n                  placeholder=\"Enter username\"\r\n                  class=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\r\n                />\r\n                <button\r\n                  (click)=\"grantUserAccess(newUsername); newUsername = ''\"\r\n                  [disabled]=\"!newUsername.trim()\"\r\n                  class=\"btn-primary flex items-center space-x-2\"\r\n                >\r\n                  <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\r\n                  </svg>\r\n                  <span>Add User</span>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module"}