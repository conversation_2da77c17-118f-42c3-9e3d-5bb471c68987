{"ast": null, "code": "import { scheduleObservable } from './scheduleObservable';\nimport { schedulePromise } from './schedulePromise';\nimport { scheduleArray } from './scheduleArray';\nimport { scheduleIterable } from './scheduleIterable';\nimport { scheduleAsyncIterable } from './scheduleAsyncIterable';\nimport { isInteropObservable } from '../util/isInteropObservable';\nimport { isPromise } from '../util/isPromise';\nimport { isArrayLike } from '../util/isArrayLike';\nimport { isIterable } from '../util/isIterable';\nimport { isAsyncIterable } from '../util/isAsyncIterable';\nimport { createInvalidObservableTypeError } from '../util/throwUnobservableError';\nimport { isReadableStreamLike } from '../util/isReadableStreamLike';\nimport { scheduleReadableStreamLike } from './scheduleReadableStreamLike';\nexport function scheduled(input, scheduler) {\n  if (input != null) {\n    if (isInteropObservable(input)) {\n      return scheduleObservable(input, scheduler);\n    }\n\n    if (isArrayLike(input)) {\n      return scheduleArray(input, scheduler);\n    }\n\n    if (isPromise(input)) {\n      return schedulePromise(input, scheduler);\n    }\n\n    if (isAsyncIterable(input)) {\n      return scheduleAsyncIterable(input, scheduler);\n    }\n\n    if (isIterable(input)) {\n      return scheduleIterable(input, scheduler);\n    }\n\n    if (isReadableStreamLike(input)) {\n      return scheduleReadableStreamLike(input, scheduler);\n    }\n  }\n\n  throw createInvalidObservableTypeError(input);\n} //# sourceMappingURL=scheduled.js.map", "map": null, "metadata": {}, "sourceType": "module"}