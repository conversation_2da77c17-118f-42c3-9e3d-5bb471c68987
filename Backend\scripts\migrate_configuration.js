const fs = require('fs');
const path = require('path');
const { pool } = require('../database/db');
const Configuration = require('../models/Configuration');

async function migrateConfiguration() {
  try {
    console.log('Adding configuration table...');
    
    // Read and execute the configuration table migration
    const migrationPath = path.join(__dirname, 'add_configuration_table.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    await pool.query(migrationSQL);
    console.log('Configuration table migration completed successfully!');
    
    // Initialize configuration with environment variables if they exist
    const envSecurityMode = process.env.SECURITY_MODE || 'whitelist';
    const envAdminUsers = process.env.ADMIN_USERS || 'admin';
    
    // Update configuration with environment values
    await Configuration.set('SECURITY_MODE', envSecurityMode, 'Security mode: open, whitelist, or invite', 'migration');
    await Configuration.set('ADMIN_USERS', envAdminUsers, 'Comma-separated list of admin usernames', 'migration');
    
    console.log('Configuration initialized with environment values:');
    console.log(`- SECURITY_MODE: ${envSecurityMode}`);
    console.log(`- ADMIN_USERS: ${envAdminUsers}`);
    
    // Set admin status for configured users
    const adminUsers = envAdminUsers.split(',').map(user => user.trim());
    for (const adminUser of adminUsers) {
      try {
        const result = await pool.query(
          'UPDATE users SET is_admin = true WHERE username = $1',
          [adminUser.toLowerCase()]
        );
        if (result.rowCount > 0) {
          console.log(`Set admin status for: ${adminUser}`);
        } else {
          console.log(`User not found: ${adminUser}`);
        }
      } catch (error) {
        console.log(`Error updating ${adminUser}:`, error.message);
      }
    }
    
    console.log('Configuration migration completed!');
    process.exit(0);
  } catch (error) {
    console.error('Configuration migration failed:', error);
    process.exit(1);
  }
}

migrateConfiguration();
