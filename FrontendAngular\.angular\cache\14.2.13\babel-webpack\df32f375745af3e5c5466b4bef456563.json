{"ast": null, "code": "import { Observable } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./services/chat.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"./components/login-form/login-form.component\";\nimport * as i4 from \"./components/sidebar/sidebar.component\";\nimport * as i5 from \"./components/message-list/message-list.component\";\nimport * as i6 from \"./components/message-input/message-input.component\";\nimport * as i7 from \"./components/admin-panel/admin-panel.component\";\nimport * as i8 from \"./components/debug-info/debug-info.component\";\n\nfunction AppComponent_app_login_form_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-login-form\");\n  }\n}\n\nfunction AppComponent_div_2_p_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 22);\n    i0.ɵɵtext(1, \" Please select a group from the sidebar to start chatting \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AppComponent_div_2_p_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (tmp_0_0 = i0.ɵɵpipeBind1(2, 1, ctx_r3.currentGroup$)) == null ? null : tmp_0_0.description, \" \");\n  }\n}\n\nfunction AppComponent_div_2_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 24);\n    i0.ɵɵelement(1, \"span\", 25);\n    i0.ɵɵtext(2, \" Connected \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AppComponent_div_2_ng_template_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 24);\n    i0.ɵɵelement(1, \"span\", 26);\n    i0.ɵɵtext(2, \" Disconnected - Check console for errors \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AppComponent_div_2_button_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function AppComponent_div_2_button_27_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.onShowAdminPanel());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 28);\n    i0.ɵɵelement(2, \"path\", 29)(3, \"path\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Admin\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AppComponent_div_2_app_message_input_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"app-message-input\", 31);\n    i0.ɵɵlistener(\"onSendMessage\", function AppComponent_div_2_app_message_input_34_Template_app_message_input_onSendMessage_0_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r13.onSendMessage($event));\n    })(\"onCancelReply\", function AppComponent_div_2_app_message_input_34_Template_app_message_input_onCancelReply_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.onCancelReply());\n    });\n    i0.ɵɵpipe(1, \"async\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"replyTo\", i0.ɵɵpipeBind1(1, 1, ctx_r8.replyTo$));\n  }\n}\n\nfunction AppComponent_div_2_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"p\", 33);\n    i0.ɵɵtext(2, \"Select a group from the sidebar to start chatting\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AppComponent_div_2_app_admin_panel_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"app-admin-panel\", 34);\n    i0.ɵɵlistener(\"onClose\", function AppComponent_div_2_app_admin_panel_38_Template_app_admin_panel_onClose_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.onHideAdminPanel());\n    });\n    i0.ɵɵpipe(1, \"async\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"currentUser\", i0.ɵɵpipeBind1(1, 1, ctx_r10.user$));\n  }\n}\n\nconst _c0 = function () {\n  return [];\n};\n\nfunction AppComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵelement(1, \"app-sidebar\", 3);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵpipe(3, \"async\");\n    i0.ɵɵpipe(4, \"async\");\n    i0.ɵɵpipe(5, \"async\");\n    i0.ɵɵpipe(6, \"async\");\n    i0.ɵɵelementStart(7, \"div\", 4)(8, \"div\", 5)(9, \"div\", 6)(10, \"div\")(11, \"h1\", 7);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"async\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, AppComponent_div_2_p_14_Template, 2, 0, \"p\", 8);\n    i0.ɵɵpipe(15, \"async\");\n    i0.ɵɵtemplate(16, AppComponent_div_2_p_16_Template, 3, 3, \"p\", 9);\n    i0.ɵɵpipe(17, \"async\");\n    i0.ɵɵelementStart(18, \"p\", 10);\n    i0.ɵɵtemplate(19, AppComponent_div_2_span_19_Template, 3, 0, \"span\", 11);\n    i0.ɵɵpipe(20, \"async\");\n    i0.ɵɵtemplate(21, AppComponent_div_2_ng_template_21_Template, 3, 0, \"ng-template\", null, 12, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 13)(24, \"span\", 10);\n    i0.ɵɵtext(25);\n    i0.ɵɵpipe(26, \"async\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(27, AppComponent_div_2_button_27_Template, 6, 0, \"button\", 14);\n    i0.ɵɵpipe(28, \"async\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"div\", 15)(30, \"app-message-list\", 16);\n    i0.ɵɵlistener(\"onReply\", function AppComponent_div_2_Template_app_message_list_onReply_30_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.onReply($event));\n    })(\"onAddReaction\", function AppComponent_div_2_Template_app_message_list_onAddReaction_30_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.onAddReaction($event));\n    })(\"onRemoveReaction\", function AppComponent_div_2_Template_app_message_list_onRemoveReaction_30_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.onRemoveReaction($event));\n    });\n    i0.ɵɵpipe(31, \"async\");\n    i0.ɵɵpipe(32, \"async\");\n    i0.ɵɵpipe(33, \"async\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(34, AppComponent_div_2_app_message_input_34_Template, 2, 3, \"app-message-input\", 17);\n    i0.ɵɵpipe(35, \"async\");\n    i0.ɵɵtemplate(36, AppComponent_div_2_div_36_Template, 3, 0, \"div\", 18);\n    i0.ɵɵpipe(37, \"async\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(38, AppComponent_div_2_app_admin_panel_38_Template, 2, 3, \"app-admin-panel\", 19);\n    i0.ɵɵpipe(39, \"async\");\n    i0.ɵɵelement(40, \"app-debug-info\", 20);\n    i0.ɵɵpipe(41, \"async\");\n    i0.ɵɵpipe(42, \"async\");\n    i0.ɵɵpipe(43, \"async\");\n    i0.ɵɵelementStart(44, \"div\", 21)(45, \"div\")(46, \"strong\");\n    i0.ɵɵtext(47, \"Debug Info:\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\");\n    i0.ɵɵtext(49);\n    i0.ɵɵpipe(50, \"async\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"div\");\n    i0.ɵɵtext(52);\n    i0.ɵɵpipe(53, \"async\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"div\");\n    i0.ɵɵtext(55);\n    i0.ɵɵpipe(56, \"async\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"div\");\n    i0.ɵɵtext(58);\n    i0.ɵɵpipe(59, \"async\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(60, \"div\");\n    i0.ɵɵtext(61);\n    i0.ɵɵpipe(62, \"async\");\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const _r5 = i0.ɵɵreference(22);\n\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_6_0;\n    let tmp_8_0;\n    let tmp_11_0;\n    let tmp_24_0;\n    let tmp_25_0;\n    let tmp_26_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"onlineUsers\", i0.ɵɵpipeBind1(2, 27, ctx_r1.onlineUsers$) || i0.ɵɵpureFunction0(77, _c0))(\"currentUser\", i0.ɵɵpipeBind1(3, 29, ctx_r1.user$))(\"isAdmin\", i0.ɵɵpipeBind1(4, 31, ctx_r1.isAdmin$) || false)(\"groups\", i0.ɵɵpipeBind1(5, 33, ctx_r1.groups$) || i0.ɵɵpureFunction0(78, _c0))(\"currentGroup\", i0.ɵɵpipeBind1(6, 35, ctx_r1.currentGroup$))(\"onJoinGroup\", ctx_r1.onJoinGroup.bind(ctx_r1));\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate1(\" \", ((tmp_6_0 = i0.ɵɵpipeBind1(13, 37, ctx_r1.currentGroup$)) == null ? null : tmp_6_0.name) || \"No Group Selected\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !i0.ɵɵpipeBind1(15, 39, ctx_r1.currentGroup$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (tmp_8_0 = i0.ɵɵpipeBind1(17, 41, ctx_r1.currentGroup$)) == null ? null : tmp_8_0.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(20, 43, ctx_r1.connected$))(\"ngIfElse\", _r5);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ((tmp_11_0 = i0.ɵɵpipeBind1(26, 45, ctx_r1.onlineUsers$)) == null ? null : tmp_11_0.length) || 0, \" online \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(28, 47, ctx_r1.isAdmin$));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"messages\", i0.ɵɵpipeBind1(31, 49, ctx_r1.messages$) || i0.ɵɵpureFunction0(79, _c0))(\"currentUser\", i0.ɵɵpipeBind1(32, 51, ctx_r1.user$))(\"loading\", i0.ɵɵpipeBind1(33, 53, ctx_r1.loading$) || false);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(35, 55, ctx_r1.currentGroup$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !i0.ɵɵpipeBind1(37, 57, ctx_r1.currentGroup$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(39, 59, ctx_r1.showAdminPanel$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"user\", i0.ɵɵpipeBind1(41, 61, ctx_r1.user$))(\"isAdmin\", i0.ɵɵpipeBind1(42, 63, ctx_r1.isAdmin$) || false)(\"securityInfo\", i0.ɵɵpipeBind1(43, 65, ctx_r1.securityInfo$));\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\"User: \", i0.ɵɵpipeBind1(50, 67, ctx_r1.user$) || \"Not logged in\", \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Connected: \", i0.ɵɵpipeBind1(53, 69, ctx_r1.connected$) ? \"Yes\" : \"No\", \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Groups: \", ((tmp_24_0 = i0.ɵɵpipeBind1(56, 71, ctx_r1.groups$)) == null ? null : tmp_24_0.length) || 0, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Online Users: \", ((tmp_25_0 = i0.ɵɵpipeBind1(59, 73, ctx_r1.onlineUsers$)) == null ? null : tmp_25_0.length) || 0, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Messages: \", ((tmp_26_0 = i0.ɵɵpipeBind1(62, 75, ctx_r1.messages$)) == null ? null : tmp_26_0.length) || 0, \"\");\n  }\n}\n\nexport class AppComponent {\n  constructor(chatService) {\n    this.chatService = chatService;\n    this.user$ = this.chatService.user$;\n    this.messages$ = this.chatService.messages$;\n    this.onlineUsers$ = this.chatService.onlineUsers$;\n    this.groups$ = this.chatService.groups$;\n    this.currentGroup$ = this.chatService.currentGroup$;\n    this.replyTo$ = this.chatService.replyTo$;\n    this.loading$ = this.chatService.loading$;\n    this.isAdmin$ = this.chatService.isAdmin$;\n    this.showAdminPanel$ = this.chatService.showAdminPanel$;\n    this.securityInfo$ = this.chatService.securityInfo$;\n    this.connected$ = this.chatService.connected$;\n    this.isLoggedIn$ = this.chatService.isLoggedIn$;\n  }\n\n  ngOnInit() {// Component initialization\n  }\n\n  ngOnDestroy() {\n    this.chatService.logout();\n  }\n\n  onJoinGroup(groupId) {\n    this.chatService.joinGroup(groupId);\n  }\n\n  onSendMessage(event) {\n    this.chatService.sendMessage(event.text, event.replyToId);\n  }\n\n  onReply(message) {\n    this.chatService.replyToMessage(message);\n  }\n\n  onCancelReply() {\n    this.chatService.cancelReply();\n  }\n\n  onAddReaction(event) {\n    this.chatService.addReaction(event.messageId, event.emoji);\n  }\n\n  onRemoveReaction(messageId) {\n    this.chatService.removeReaction(messageId);\n  }\n\n  onShowAdminPanel() {\n    this.chatService.showAdminPanel();\n  }\n\n  onHideAdminPanel() {\n    this.chatService.hideAdminPanel();\n  }\n\n}\n\nAppComponent.ɵfac = function AppComponent_Factory(t) {\n  return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.ChatService));\n};\n\nAppComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: AppComponent,\n  selectors: [[\"app-root\"]],\n  decls: 4,\n  vars: 6,\n  consts: [[4, \"ngIf\"], [\"class\", \"h-screen flex bg-gray-50\", 4, \"ngIf\"], [1, \"h-screen\", \"flex\", \"bg-gray-50\"], [3, \"onlineUsers\", \"currentUser\", \"isAdmin\", \"groups\", \"currentGroup\", \"onJoinGroup\"], [1, \"flex-1\", \"flex\", \"flex-col\", \"min-h-0\"], [1, \"bg-white\", \"border-b\", \"border-gray-200\", \"px-6\", \"py-4\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"text-lg\", \"font-semibold\", \"text-gray-900\"], [\"class\", \"text-sm text-red-500 mt-1\", 4, \"ngIf\"], [\"class\", \"text-sm text-gray-500 mt-1\", 4, \"ngIf\"], [1, \"text-sm\", \"text-gray-500\"], [\"class\", \"inline-flex items-center\", 4, \"ngIf\", \"ngIfElse\"], [\"disconnected\", \"\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [\"class\", \"flex items-center space-x-2 px-3 py-2 text-sm font-medium text-primary-600 bg-primary-50 hover:bg-primary-100 rounded-lg transition-colors duration-200\", \"title\", \"Admin Panel\", 3, \"click\", 4, \"ngIf\"], [1, \"chat-area\", \"flex-1\"], [3, \"messages\", \"currentUser\", \"loading\", \"onReply\", \"onAddReaction\", \"onRemoveReaction\"], [3, \"replyTo\", \"onSendMessage\", \"onCancelReply\", 4, \"ngIf\"], [\"class\", \"border-t border-gray-200 bg-white p-4 text-center\", 4, \"ngIf\"], [3, \"currentUser\", \"onClose\", 4, \"ngIf\"], [3, \"user\", \"isAdmin\", \"securityInfo\"], [1, \"fixed\", \"bottom-4\", \"right-4\", \"bg-black\", \"bg-opacity-75\", \"text-white\", \"p-3\", \"rounded-lg\", \"text-xs\", \"max-w-sm\"], [1, \"text-sm\", \"text-red-500\", \"mt-1\"], [1, \"text-sm\", \"text-gray-500\", \"mt-1\"], [1, \"inline-flex\", \"items-center\"], [1, \"inline-block\", \"w-2\", \"h-2\", \"bg-green-500\", \"rounded-full\", \"mr-2\"], [1, \"inline-block\", \"w-2\", \"h-2\", \"bg-red-500\", \"rounded-full\", \"mr-2\"], [\"title\", \"Admin Panel\", 1, \"flex\", \"items-center\", \"space-x-2\", \"px-3\", \"py-2\", \"text-sm\", \"font-medium\", \"text-primary-600\", \"bg-primary-50\", \"hover:bg-primary-100\", \"rounded-lg\", \"transition-colors\", \"duration-200\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"], [3, \"replyTo\", \"onSendMessage\", \"onCancelReply\"], [1, \"border-t\", \"border-gray-200\", \"bg-white\", \"p-4\", \"text-center\"], [1, \"text-gray-500\"], [3, \"currentUser\", \"onClose\"]],\n  template: function AppComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, AppComponent_app_login_form_0_Template, 1, 0, \"app-login-form\", 0);\n      i0.ɵɵpipe(1, \"async\");\n      i0.ɵɵtemplate(2, AppComponent_div_2_Template, 63, 80, \"div\", 1);\n      i0.ɵɵpipe(3, \"async\");\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", !i0.ɵɵpipeBind1(1, 2, ctx.isLoggedIn$));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(3, 4, ctx.isLoggedIn$));\n    }\n  },\n  dependencies: [i2.NgIf, i3.LoginFormComponent, i4.SidebarComponent, i5.MessageListComponent, i6.MessageInputComponent, i7.AdminPanelComponent, i8.DebugInfoComponent, i2.AsyncPipe],\n  styles: [\".h-screen[_ngcontent-%COMP%] {\\r\\n  height: 100vh;\\r\\n  max-height: 100vh;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n.chat-area[_ngcontent-%COMP%] {\\r\\n  height: 100%;\\r\\n  min-height: 0;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  background: #f8fafc;\\r\\n}\\r\\n\\r\\n.flex-1[_ngcontent-%COMP%] {\\r\\n  flex: 1 1 0%;\\r\\n  min-height: 0;\\r\\n}\\r\\n\\r\\n.main-chat[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  height: 100%;\\r\\n  background: white;\\r\\n  border-radius: 0.5rem;\\r\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\r\\n  overflow: hidden;\\r\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFwcC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLGdCQUFnQjtBQUNoQjtFQUNFLGFBQWE7RUFDYixpQkFBaUI7RUFDakIsZ0JBQWdCO0FBQ2xCO0FBRUEsY0FBYztBQUNkO0VBQ0UsWUFBWTtFQUNaLGFBQWE7RUFDYixhQUFhO0VBQ2Isc0JBQXNCO0VBQ3RCLG1CQUFtQjtBQUNyQjtBQUVBLG1CQUFtQjtBQUNuQjtFQUNFLFlBQVk7RUFDWixhQUFhO0FBQ2Y7QUFFQSx3QkFBd0I7QUFDeEI7RUFDRSxhQUFhO0VBQ2Isc0JBQXNCO0VBQ3RCLFlBQVk7RUFDWixpQkFBaUI7RUFDakIscUJBQXFCO0VBQ3JCLHdDQUF3QztFQUN4QyxnQkFBZ0I7QUFDbEIiLCJmaWxlIjoiYXBwLmNvbXBvbmVudC5jc3MiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBDaGF0IExheW91dCAqL1xyXG4uaC1zY3JlZW4ge1xyXG4gIGhlaWdodDogMTAwdmg7XHJcbiAgbWF4LWhlaWdodDogMTAwdmg7XHJcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcclxufVxyXG5cclxuLyogQ2hhdCBBcmVhICovXHJcbi5jaGF0LWFyZWEge1xyXG4gIGhlaWdodDogMTAwJTtcclxuICBtaW4taGVpZ2h0OiAwO1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICBiYWNrZ3JvdW5kOiAjZjhmYWZjO1xyXG59XHJcblxyXG4vKiBGbGV4IHV0aWxpdGllcyAqL1xyXG4uZmxleC0xIHtcclxuICBmbGV4OiAxIDEgMCU7XHJcbiAgbWluLWhlaWdodDogMDtcclxufVxyXG5cclxuLyogTWFpbiBjaGF0IGludGVyZmFjZSAqL1xyXG4ubWFpbi1jaGF0IHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgaGVpZ2h0OiAxMDAlO1xyXG4gIGJhY2tncm91bmQ6IHdoaXRlO1xyXG4gIGJvcmRlci1yYWRpdXM6IDAuNXJlbTtcclxuICBib3gtc2hhZG93OiAwIDFweCAzcHggcmdiYSgwLCAwLCAwLCAwLjEpO1xyXG4gIG92ZXJmbG93OiBoaWRkZW47XHJcbn1cclxuIl19 */\"]\n});", "map": {"version": 3, "mappings": "AACA,SAASA,UAAT,QAA0C,MAA1C;;;;;;;;;;;;;ICAAC;;;;;;IAuBUA;IACEA;IACFA;;;;;;IACAA;IACEA;;IACFA;;;;;;IADEA;IAAAA;;;;;;IAGAA;IACEA;IACAA;IACFA;;;;;;IAEEA;IACEA;IACAA;IACFA;;;;;;;;IAUJA;IAEEA;MAAAA;MAAA;MAAA,OAASA,0CAAT;IAA2B,CAA3B;IAIAA;IAAAA;IACEA,4BAAqjB,CAArjB,EAAqjB,MAArjB,EAAqjB,EAArjB;IAEVA;IACQA;IAAAA;IAAMA;IAAKA;;;;;;;;IAmBnBA;IAGEA;MAAAA;MAAA;MAAA,OAAiBA,6CAAjB;IAAsC,CAAtC,EAAuC,eAAvC,EAAuC;MAAAA;MAAA;MAAA,OACtBA,uCADsB;IACP,CADhC;;IAEDA;;;;;IAHCA;;;;;;IAMFA,gCAAgG,CAAhG,EAAgG,GAAhG,EAAgG,EAAhG;IAC2BA;IAAiDA;;;;;;;;IAK9EA;IAGEA;MAAAA;MAAA;MAAA,OAAWA,0CAAX;IAA6B,CAA7B;;IACDA;;;;;IAFCA;;;;;;;;;;;;IA1FJA;IAEEA;;;;;;IAUAA,+BAA0C,CAA1C,EAA0C,KAA1C,EAA0C,CAA1C,EAA0C,CAA1C,EAA0C,KAA1C,EAA0C,CAA1C,EAA0C,EAA1C,EAA0C,KAA1C,EAA0C,EAA1C,EAA0C,IAA1C,EAA0C,CAA1C;IAMUA;;IACFA;IACAA;;IAGAA;;IAGAA;IACEA;;IAIAA;IAMFA;IAGFA,iCAAyC,EAAzC,EAAyC,MAAzC,EAAyC,EAAzC;IAEIA;;IACFA;IAEAA;;IAYRA;IAKEA,iCAA8B,EAA9B,EAA8B,kBAA9B,EAA8B,EAA9B;IAKIA;MAAAA;MAAA;MAAA,OAAWA,uCAAX;IAA0B,CAA1B,EAA2B,eAA3B,EAA2B;MAAAA;MAAA;MAAA,OACVA,6CADU;IACW,CADtC,EAA2B,kBAA3B,EAA2B;MAAAA;MAAA;MAAA,OAEPA,gDAFO;IAEiB,CAF5C;;;;IAGDA;IAIHA;;IAQAA;;IAGFA;IAGAA;;IAOAA;;;;IAOAA,iCAAsG,EAAtG,EAAsG,KAAtG,EAAsG,EAAtG,EAAsG,QAAtG;IACeA;IAAWA;IACxBA;IAAKA;;IAA8CA;IACnDA;IAAKA;;IAAoDA;IACzDA;IAAKA;;IAA4CA;IACjDA;IAAKA;;IAAuDA;IAC5DA;IAAKA;;IAAgDA;;;;;;;;;;;;;IAzGrDA;IAAAA,wGAA4C,aAA5C,EAA4CA,mCAA5C,EAA4C,SAA5C,EAA4CA,+CAA5C,EAA4C,QAA5C,EAA4CA,oEAA5C,EAA4C,cAA5C,EAA4CA,2CAA5C,EAA4C,aAA5C,EAA4CC,+BAA5C;IAeQD;IAAAA;IAEEA;IAAAA;IAGAA;IAAAA;IAIKA;IAAAA,iEAA0B,UAA1B,EAA0BE,GAA1B;IAePF;IAAAA;IAICA;IAAAA;IAkBLA;IAAAA,mGAAsC,aAAtC,EAAsCA,oCAAtC,EAAsC,SAAtC,EAAsCA,gDAAtC;IAWDA;IAAAA;IAOGA;IAAAA;IAOLA;IAAAA;IAODA;IAAAA,4DAAsB,SAAtB,EAAsBA,gDAAtB,EAAsB,cAAtB,EAAsBA,4CAAtB;IAQKA;IAAAA;IACAA;IAAAA;IACAA;IAAAA;IACAA;IAAAA;IACAA;IAAAA;;;;ADrGT,OAAM,MAAOG,YAAP,CAAmB;EAcvBC,YAAoBC,WAApB,EAA4C;IAAxB;IAClB,KAAKC,KAAL,GAAa,KAAKD,WAAL,CAAiBC,KAA9B;IACA,KAAKC,SAAL,GAAiB,KAAKF,WAAL,CAAiBE,SAAlC;IACA,KAAKC,YAAL,GAAoB,KAAKH,WAAL,CAAiBG,YAArC;IACA,KAAKC,OAAL,GAAe,KAAKJ,WAAL,CAAiBI,OAAhC;IACA,KAAKC,aAAL,GAAqB,KAAKL,WAAL,CAAiBK,aAAtC;IACA,KAAKC,QAAL,GAAgB,KAAKN,WAAL,CAAiBM,QAAjC;IACA,KAAKC,QAAL,GAAgB,KAAKP,WAAL,CAAiBO,QAAjC;IACA,KAAKC,QAAL,GAAgB,KAAKR,WAAL,CAAiBQ,QAAjC;IACA,KAAKC,eAAL,GAAuB,KAAKT,WAAL,CAAiBS,eAAxC;IACA,KAAKC,aAAL,GAAqB,KAAKV,WAAL,CAAiBU,aAAtC;IACA,KAAKC,UAAL,GAAkB,KAAKX,WAAL,CAAiBW,UAAnC;IACA,KAAKC,WAAL,GAAmB,KAAKZ,WAAL,CAAiBY,WAApC;EACD;;EAEDC,QAAQ,IACN;EACD;;EAEDC,WAAW;IACT,KAAKd,WAAL,CAAiBe,MAAjB;EACD;;EAEDC,WAAW,CAACC,OAAD,EAAgB;IACzB,KAAKjB,WAAL,CAAiBkB,SAAjB,CAA2BD,OAA3B;EACD;;EAEDE,aAAa,CAACC,KAAD,EAAkD;IAC7D,KAAKpB,WAAL,CAAiBqB,WAAjB,CAA6BD,KAAK,CAACE,IAAnC,EAAyCF,KAAK,CAACG,SAA/C;EACD;;EAEDC,OAAO,CAACC,OAAD,EAAiB;IACtB,KAAKzB,WAAL,CAAiB0B,cAAjB,CAAgCD,OAAhC;EACD;;EAEDE,aAAa;IACX,KAAK3B,WAAL,CAAiB4B,WAAjB;EACD;;EAEDC,aAAa,CAACT,KAAD,EAA4C;IACvD,KAAKpB,WAAL,CAAiB8B,WAAjB,CAA6BV,KAAK,CAACW,SAAnC,EAA8CX,KAAK,CAACY,KAApD;EACD;;EAEDC,gBAAgB,CAACF,SAAD,EAAkB;IAChC,KAAK/B,WAAL,CAAiBkC,cAAjB,CAAgCH,SAAhC;EACD;;EAEDI,gBAAgB;IACd,KAAKnC,WAAL,CAAiBoC,cAAjB;EACD;;EAEDC,gBAAgB;IACd,KAAKrC,WAAL,CAAiBsC,cAAjB;EACD;;AAnEsB;;;mBAAZxC,cAAYH;AAAA;;;QAAZG;EAAYyC;EAAAC;EAAAC;EAAAC;EAAAC;IAAA;MCVzBhD;;MAGAA;;;;;MAHiBA;MAGXA;MAAAA", "names": ["Observable", "i0", "ctx_r1", "_r5", "AppComponent", "constructor", "chatService", "user$", "messages$", "onlineUsers$", "groups$", "currentGroup$", "replyTo$", "loading$", "isAdmin$", "showAdminPanel$", "securityInfo$", "connected$", "isLoggedIn$", "ngOnInit", "ngOnDestroy", "logout", "onJoinGroup", "groupId", "joinGroup", "onSendMessage", "event", "sendMessage", "text", "replyToId", "onReply", "message", "replyToMessage", "onCancelReply", "cancelReply", "onAddReaction", "addReaction", "messageId", "emoji", "onRemoveReaction", "removeReaction", "onShowAdminPanel", "showAdminPanel", "onHideAdminPanel", "hideAdminPanel", "selectors", "decls", "vars", "consts", "template"], "sourceRoot": "", "sources": ["R:\\chateye\\Frontend\\chateye-angular\\src\\app\\app.component.ts", "R:\\chateye\\Frontend\\chateye-angular\\src\\app\\app.component.html"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\nimport { Observable, combineLatest } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { ChatService } from './services/chat.service';\nimport { Message, Group, User } from './services/api.service';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.css']\n})\nexport class AppComponent implements OnInit, OnDestroy {\n  user$: Observable<string | null>;\n  messages$: Observable<Message[]>;\n  onlineUsers$: Observable<User[]>;\n  groups$: Observable<Group[]>;\n  currentGroup$: Observable<Group | null>;\n  replyTo$: Observable<Message | null>;\n  loading$: Observable<boolean>;\n  isAdmin$: Observable<boolean>;\n  showAdminPanel$: Observable<boolean>;\n  securityInfo$: Observable<any>;\n  connected$: Observable<boolean>;\n  isLoggedIn$: Observable<boolean>;\n\n  constructor(private chatService: ChatService) {\n    this.user$ = this.chatService.user$;\n    this.messages$ = this.chatService.messages$;\n    this.onlineUsers$ = this.chatService.onlineUsers$;\n    this.groups$ = this.chatService.groups$;\n    this.currentGroup$ = this.chatService.currentGroup$;\n    this.replyTo$ = this.chatService.replyTo$;\n    this.loading$ = this.chatService.loading$;\n    this.isAdmin$ = this.chatService.isAdmin$;\n    this.showAdminPanel$ = this.chatService.showAdminPanel$;\n    this.securityInfo$ = this.chatService.securityInfo$;\n    this.connected$ = this.chatService.connected$;\n    this.isLoggedIn$ = this.chatService.isLoggedIn$;\n  }\n\n  ngOnInit(): void {\n    // Component initialization\n  }\n\n  ngOnDestroy(): void {\n    this.chatService.logout();\n  }\n\n  onJoinGroup(groupId: string): void {\n    this.chatService.joinGroup(groupId);\n  }\n\n  onSendMessage(event: { text: string; replyToId: string | null }): void {\n    this.chatService.sendMessage(event.text, event.replyToId);\n  }\n\n  onReply(message: Message): void {\n    this.chatService.replyToMessage(message);\n  }\n\n  onCancelReply(): void {\n    this.chatService.cancelReply();\n  }\n\n  onAddReaction(event: { messageId: string; emoji: string }): void {\n    this.chatService.addReaction(event.messageId, event.emoji);\n  }\n\n  onRemoveReaction(messageId: string): void {\n    this.chatService.removeReaction(messageId);\n  }\n\n  onShowAdminPanel(): void {\n    this.chatService.showAdminPanel();\n  }\n\n  onHideAdminPanel(): void {\n    this.chatService.hideAdminPanel();\n  }\n}\n", "<!-- Login Form -->\n<app-login-form *ngIf=\"!(isLoggedIn$ | async)\"></app-login-form>\n\n<!-- Main Chat Interface -->\n<div *ngIf=\"isLoggedIn$ | async\" class=\"h-screen flex bg-gray-50\">\n  <!-- Sidebar -->\n  <app-sidebar \n    [onlineUsers]=\"(onlineUsers$ | async) || []\"\n    [currentUser]=\"user$ | async\"\n    [isAdmin]=\"(isAdmin$ | async) || false\"\n    [groups]=\"(groups$ | async) || []\"\n    [currentGroup]=\"currentGroup$ | async\"\n    [onJoinGroup]=\"onJoinGroup.bind(this)\"\n  ></app-sidebar>\n\n  <!-- Main chat area -->\n  <div class=\"flex-1 flex flex-col min-h-0\">\n    <!-- Header -->\n    <div class=\"bg-white border-b border-gray-200 px-6 py-4\">\n      <div class=\"flex items-center justify-between\">\n        <div>\n          <h1 class=\"text-lg font-semibold text-gray-900\">\n            {{ (currentGroup$ | async)?.name || 'No Group Selected' }}\n          </h1>\n          <p *ngIf=\"!(currentGroup$ | async)\" class=\"text-sm text-red-500 mt-1\">\n            Please select a group from the sidebar to start chatting\n          </p>\n          <p *ngIf=\"(currentGroup$ | async)?.description\" class=\"text-sm text-gray-500 mt-1\">\n            {{ (currentGroup$ | async)?.description }}\n          </p>\n          <p class=\"text-sm text-gray-500\">\n            <span *ngIf=\"connected$ | async; else disconnected\" class=\"inline-flex items-center\">\n              <span class=\"inline-block w-2 h-2 bg-green-500 rounded-full mr-2\"></span>\n              Connected\n            </span>\n            <ng-template #disconnected>\n              <span class=\"inline-flex items-center\">\n                <span class=\"inline-block w-2 h-2 bg-red-500 rounded-full mr-2\"></span>\n                Disconnected - Check console for errors\n              </span>\n            </ng-template>\n          </p>\n</div>\n\n        <div class=\"flex items-center space-x-4\">\n          <span class=\"text-sm text-gray-500\">\n            {{ (onlineUsers$ | async)?.length || 0 }} online\n          </span>\n          \n          <button\n            *ngIf=\"isAdmin$ | async\"\n            (click)=\"onShowAdminPanel()\"\n            class=\"flex items-center space-x-2 px-3 py-2 text-sm font-medium text-primary-600 bg-primary-50 hover:bg-primary-100 rounded-lg transition-colors duration-200\"\n            title=\"Admin Panel\"\n          >\n            <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"></path>\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"></path>\n    </svg>\n            <span>Admin</span>\n    </button>\n  </div>\n      </div>\n  </div>\n\n    <!-- Messages -->\n    <div class=\"chat-area flex-1\">\n      <app-message-list\n        [messages]=\"(messages$ | async) || []\"\n        [currentUser]=\"user$ | async\"\n        [loading]=\"(loading$ | async) || false\"\n        (onReply)=\"onReply($event)\"\n        (onAddReaction)=\"onAddReaction($event)\"\n        (onRemoveReaction)=\"onRemoveReaction($event)\"\n      ></app-message-list>\n    </div>\n\n    <!-- Message input -->\n    <app-message-input\n      *ngIf=\"currentGroup$ | async\"\n      [replyTo]=\"replyTo$ | async\"\n      (onSendMessage)=\"onSendMessage($event)\"\n      (onCancelReply)=\"onCancelReply()\"\n    ></app-message-input>\n    \n    <!-- No group selected message -->\n    <div *ngIf=\"!(currentGroup$ | async)\" class=\"border-t border-gray-200 bg-white p-4 text-center\">\n      <p class=\"text-gray-500\">Select a group from the sidebar to start chatting</p>\n    </div>\n  </div>\n\n  <!-- Admin Panel Modal -->\n  <app-admin-panel\n    *ngIf=\"showAdminPanel$ | async\"\n    [currentUser]=\"user$ | async\"\n    (onClose)=\"onHideAdminPanel()\"\n  ></app-admin-panel>\n\n  <!-- Debug Info (Development Only) -->\n  <app-debug-info \n    [user]=\"user$ | async\"\n    [isAdmin]=\"(isAdmin$ | async) || false\"\n    [securityInfo]=\"securityInfo$ | async\"\n  ></app-debug-info>\n\n  <!-- Connection Debug Info -->\n  <div class=\"fixed bottom-4 right-4 bg-black bg-opacity-75 text-white p-3 rounded-lg text-xs max-w-sm\">\n    <div><strong>Debug Info:</strong></div>\n    <div>User: {{ (user$ | async) || 'Not logged in' }}</div>\n    <div>Connected: {{ (connected$ | async) ? 'Yes' : 'No' }}</div>\n    <div>Groups: {{ (groups$ | async)?.length || 0 }}</div>\n    <div>Online Users: {{ (onlineUsers$ | async)?.length || 0 }}</div>\n    <div>Messages: {{ (messages$ | async)?.length || 0 }}</div>\n  </div>\n</div>"]}, "metadata": {}, "sourceType": "module"}