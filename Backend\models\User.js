const { query } = require('../database/db');
const bcrypt = require('bcrypt');

class User {
  static async create(username, password = null) {
    let passwordHash = null;
    if (password) {
      passwordHash = await bcrypt.hash(password, 10);
    }
    
    const result = await query(
      'INSERT INTO users (username, password_hash, online_status) VALUES ($1, $2, true) RETURNING *',
      [username, passwordHash]
    );
    return result.rows[0];
  }

  static async findByUsername(username) {
    const result = await query(
      'SELECT * FROM users WHERE username = $1',
      [username]
    );
    return result.rows[0];
  }

  static async findOrCreate(username, password = null) {
    let user = await this.findByUsername(username);
    if (!user) {
      user = await this.create(username, password);
    }
    return user;
  }

  static async setOnlineStatus(userId, isOnline) {
    const result = await query(
      'UPDATE users SET online_status = $1, last_seen = CURRENT_TIMESTAMP WHERE id = $2 RETURNING *',
      [isOnline, userId]
    );
    return result.rows[0];
  }

  static async updateLastSeen(userId) {
    const result = await query(
      'UPDATE users SET last_seen = CURRENT_TIMESTAMP WHERE id = $1 RETURNING *',
      [userId]
    );
    return result.rows[0];
  }

  static async setAdminStatus(userId, isAdmin) {
    const result = await query(
      'UPDATE users SET is_admin = $1 WHERE id = $2 RETURNING *',
      [isAdmin, userId]
    );
    return result.rows[0];
  }

  static async setAllowedStatus(userId, isAllowed) {
    const result = await query(
      'UPDATE users SET is_allowed = $1 WHERE id = $2 RETURNING *',
      [isAllowed, userId]
    );
    return result.rows[0];
  }

  static async getOnlineUsers() {
    const result = await query(
      'SELECT id, username, online_status, last_seen FROM users WHERE online_status = true ORDER BY username'
    );
    return result.rows;
  }

  static async getAllUsers() {
    const result = await query(
      'SELECT id, username, online_status, last_seen FROM users ORDER BY username'
    );
    return result.rows;
  }

  // Password-related methods
  static async setPassword(userId, password) {
    const passwordHash = await bcrypt.hash(password, 10);
    const result = await query(
      'UPDATE users SET password_hash = $1 WHERE id = $2 RETURNING *',
      [passwordHash, userId]
    );
    return result.rows[0];
  }

  static async verifyPassword(username, password) {
    const user = await this.findByUsername(username);
    if (!user || !user.password_hash) {
      return false;
    }
    return await bcrypt.compare(password, user.password_hash);
  }

  static async hasPassword(username) {
    const user = await this.findByUsername(username);
    return user && user.password_hash !== null;
  }

  static async resetPassword(username, newPassword) {
    const user = await this.findByUsername(username);
    if (!user) {
      throw new Error('User not found');
    }
    return await this.setPassword(user.id, newPassword);
  }

  static async createUserWithPassword(username, password) {
    const existingUser = await this.findByUsername(username);
    if (existingUser) {
      throw new Error('User already exists');
    }
    return await this.create(username, password);
  }
}

module.exports = User;
