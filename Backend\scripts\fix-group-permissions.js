const { query } = require('../database/db');
const Group = require('../models/Group');

async function fixGroupPermissions() {
  try {
    console.log('🔧 Fixing group permissions...');
    
    // Get the General group
    const generalGroup = await Group.findByName('General');
    if (!generalGroup) {
      console.log('❌ General group not found. Creating it...');
      await Group.initializeDefaultGroups();
      return;
    }
    
    console.log(`✅ Found General group with ID: ${generalGroup.id}`);
    
    // Get all users
    const users = await query('SELECT id, username FROM users');
    console.log(`📋 Found ${users.rows.length} users`);
    
    // Grant all users access to the General group
    let grantedCount = 0;
    for (const user of users.rows) {
      try {
        await Group.grantUserAccess(user.id, generalGroup.id, null);
        console.log(`✅ Granted ${user.username} access to General group`);
        grantedCount++;
      } catch (error) {
        if (error.code === '23505') {
          console.log(`ℹ️  ${user.username} already has access to General group`);
        } else {
          console.error(`❌ Error granting access to ${user.username}:`, error.message);
        }
      }
    }
    
    console.log(`🎉 Successfully granted ${grantedCount} users access to General group`);
    
    // Check all groups and ensure they have users
    const allGroups = await Group.getAllGroups();
    console.log(`📊 Total groups: ${allGroups.length}`);
    
    for (const group of allGroups) {
      const groupUsers = await Group.getGroupUsers(group.id);
      console.log(`👥 Group "${group.name}" has ${groupUsers.length} users`);
    }
    
  } catch (error) {
    console.error('❌ Error fixing group permissions:', error);
  } finally {
    process.exit(0);
  }
}

fixGroupPermissions();
