{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nexport const defaultThrottleConfig = {\n  leading: true,\n  trailing: false\n};\nexport function throttle(durationSelector, config = defaultThrottleConfig) {\n  return operate((source, subscriber) => {\n    const {\n      leading,\n      trailing\n    } = config;\n    let hasValue = false;\n    let sendValue = null;\n    let throttled = null;\n    let isComplete = false;\n\n    const endThrottling = () => {\n      throttled === null || throttled === void 0 ? void 0 : throttled.unsubscribe();\n      throttled = null;\n\n      if (trailing) {\n        send();\n        isComplete && subscriber.complete();\n      }\n    };\n\n    const cleanupThrottling = () => {\n      throttled = null;\n      isComplete && subscriber.complete();\n    };\n\n    const startThrottle = value => throttled = innerFrom(durationSelector(value)).subscribe(createOperatorSubscriber(subscriber, endThrottling, cleanupThrottling));\n\n    const send = () => {\n      if (hasValue) {\n        hasValue = false;\n        const value = sendValue;\n        sendValue = null;\n        subscriber.next(value);\n        !isComplete && startThrottle(value);\n      }\n    };\n\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      hasValue = true;\n      sendValue = value;\n      !(throttled && !throttled.closed) && (leading ? send() : startThrottle(value));\n    }, () => {\n      isComplete = true;\n      !(trailing && hasValue && throttled && !throttled.closed) && subscriber.complete();\n    }));\n  });\n} //# sourceMappingURL=throttle.js.map", "map": null, "metadata": {}, "sourceType": "module"}