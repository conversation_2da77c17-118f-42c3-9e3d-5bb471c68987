{"ast": null, "code": "import { isScheduler } from '../util/isScheduler';\nimport { Observable } from '../Observable';\nimport { subscribeOn } from '../operators/subscribeOn';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { observeOn } from '../operators/observeOn';\nimport { AsyncSubject } from '../AsyncSubject';\nexport function bindCallbackInternals(isNodeStyle, callbackFunc, resultSelector, scheduler) {\n  if (resultSelector) {\n    if (isScheduler(resultSelector)) {\n      scheduler = resultSelector;\n    } else {\n      return function (...args) {\n        return bindCallbackInternals(isNodeStyle, callbackFunc, scheduler).apply(this, args).pipe(mapOneOrManyArgs(resultSelector));\n      };\n    }\n  }\n\n  if (scheduler) {\n    return function (...args) {\n      return bindCallbackInternals(isNodeStyle, callbackFunc).apply(this, args).pipe(subscribeOn(scheduler), observeOn(scheduler));\n    };\n  }\n\n  return function (...args) {\n    const subject = new AsyncSubject();\n    let uninitialized = true;\n    return new Observable(subscriber => {\n      const subs = subject.subscribe(subscriber);\n\n      if (uninitialized) {\n        uninitialized = false;\n        let isAsync = false;\n        let isComplete = false;\n        callbackFunc.apply(this, [...args, (...results) => {\n          if (isNodeStyle) {\n            const err = results.shift();\n\n            if (err != null) {\n              subject.error(err);\n              return;\n            }\n          }\n\n          subject.next(1 < results.length ? results : results[0]);\n          isComplete = true;\n\n          if (isAsync) {\n            subject.complete();\n          }\n        }]);\n\n        if (isComplete) {\n          subject.complete();\n        }\n\n        isAsync = true;\n      }\n\n      return subs;\n    });\n  };\n}", "map": {"version": 3, "names": ["isScheduler", "Observable", "subscribeOn", "mapOneOrManyArgs", "observeOn", "AsyncSubject", "bindCallbackInternals", "isNodeStyle", "callback<PERSON><PERSON><PERSON>", "resultSelector", "scheduler", "args", "apply", "pipe", "subject", "uninitialized", "subscriber", "subs", "subscribe", "isAsync", "isComplete", "results", "err", "shift", "error", "next", "length", "complete"], "sources": ["R:/chateye/FrontendAngular/node_modules/rxjs/dist/esm/internal/observable/bindCallbackInternals.js"], "sourcesContent": ["import { isScheduler } from '../util/isScheduler';\nimport { Observable } from '../Observable';\nimport { subscribeOn } from '../operators/subscribeOn';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { observeOn } from '../operators/observeOn';\nimport { AsyncSubject } from '../AsyncSubject';\nexport function bindCallbackInternals(isNodeStyle, callbackFunc, resultSelector, scheduler) {\n    if (resultSelector) {\n        if (isScheduler(resultSelector)) {\n            scheduler = resultSelector;\n        }\n        else {\n            return function (...args) {\n                return bindCallbackInternals(isNodeStyle, callbackFunc, scheduler)\n                    .apply(this, args)\n                    .pipe(mapOneOrManyArgs(resultSelector));\n            };\n        }\n    }\n    if (scheduler) {\n        return function (...args) {\n            return bindCallbackInternals(isNodeStyle, callbackFunc)\n                .apply(this, args)\n                .pipe(subscribeOn(scheduler), observeOn(scheduler));\n        };\n    }\n    return function (...args) {\n        const subject = new AsyncSubject();\n        let uninitialized = true;\n        return new Observable((subscriber) => {\n            const subs = subject.subscribe(subscriber);\n            if (uninitialized) {\n                uninitialized = false;\n                let isAsync = false;\n                let isComplete = false;\n                callbackFunc.apply(this, [\n                    ...args,\n                    (...results) => {\n                        if (isNodeStyle) {\n                            const err = results.shift();\n                            if (err != null) {\n                                subject.error(err);\n                                return;\n                            }\n                        }\n                        subject.next(1 < results.length ? results : results[0]);\n                        isComplete = true;\n                        if (isAsync) {\n                            subject.complete();\n                        }\n                    },\n                ]);\n                if (isComplete) {\n                    subject.complete();\n                }\n                isAsync = true;\n            }\n            return subs;\n        });\n    };\n}\n"], "mappings": "AAAA,SAASA,WAAT,QAA4B,qBAA5B;AACA,SAASC,UAAT,QAA2B,eAA3B;AACA,SAASC,WAAT,QAA4B,0BAA5B;AACA,SAASC,gBAAT,QAAiC,0BAAjC;AACA,SAASC,SAAT,QAA0B,wBAA1B;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,OAAO,SAASC,qBAAT,CAA+BC,WAA/B,EAA4CC,YAA5C,EAA0DC,cAA1D,EAA0EC,SAA1E,EAAqF;EACxF,IAAID,cAAJ,EAAoB;IAChB,IAAIT,WAAW,CAACS,cAAD,CAAf,EAAiC;MAC7BC,SAAS,GAAGD,cAAZ;IACH,CAFD,MAGK;MACD,OAAO,UAAU,GAAGE,IAAb,EAAmB;QACtB,OAAOL,qBAAqB,CAACC,WAAD,EAAcC,YAAd,EAA4BE,SAA5B,CAArB,CACFE,KADE,CACI,IADJ,EACUD,IADV,EAEFE,IAFE,CAEGV,gBAAgB,CAACM,cAAD,CAFnB,CAAP;MAGH,CAJD;IAKH;EACJ;;EACD,IAAIC,SAAJ,EAAe;IACX,OAAO,UAAU,GAAGC,IAAb,EAAmB;MACtB,OAAOL,qBAAqB,CAACC,WAAD,EAAcC,YAAd,CAArB,CACFI,KADE,CACI,IADJ,EACUD,IADV,EAEFE,IAFE,CAEGX,WAAW,CAACQ,SAAD,CAFd,EAE2BN,SAAS,CAACM,SAAD,CAFpC,CAAP;IAGH,CAJD;EAKH;;EACD,OAAO,UAAU,GAAGC,IAAb,EAAmB;IACtB,MAAMG,OAAO,GAAG,IAAIT,YAAJ,EAAhB;IACA,IAAIU,aAAa,GAAG,IAApB;IACA,OAAO,IAAId,UAAJ,CAAgBe,UAAD,IAAgB;MAClC,MAAMC,IAAI,GAAGH,OAAO,CAACI,SAAR,CAAkBF,UAAlB,CAAb;;MACA,IAAID,aAAJ,EAAmB;QACfA,aAAa,GAAG,KAAhB;QACA,IAAII,OAAO,GAAG,KAAd;QACA,IAAIC,UAAU,GAAG,KAAjB;QACAZ,YAAY,CAACI,KAAb,CAAmB,IAAnB,EAAyB,CACrB,GAAGD,IADkB,EAErB,CAAC,GAAGU,OAAJ,KAAgB;UACZ,IAAId,WAAJ,EAAiB;YACb,MAAMe,GAAG,GAAGD,OAAO,CAACE,KAAR,EAAZ;;YACA,IAAID,GAAG,IAAI,IAAX,EAAiB;cACbR,OAAO,CAACU,KAAR,CAAcF,GAAd;cACA;YACH;UACJ;;UACDR,OAAO,CAACW,IAAR,CAAa,IAAIJ,OAAO,CAACK,MAAZ,GAAqBL,OAArB,GAA+BA,OAAO,CAAC,CAAD,CAAnD;UACAD,UAAU,GAAG,IAAb;;UACA,IAAID,OAAJ,EAAa;YACTL,OAAO,CAACa,QAAR;UACH;QACJ,CAfoB,CAAzB;;QAiBA,IAAIP,UAAJ,EAAgB;UACZN,OAAO,CAACa,QAAR;QACH;;QACDR,OAAO,GAAG,IAAV;MACH;;MACD,OAAOF,IAAP;IACH,CA7BM,CAAP;EA8BH,CAjCD;AAkCH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}