const { query } = require('../database/db');
const crypto = require('crypto');

class InviteCode {
  static generateCode(length = 8) {
    return crypto.randomBytes(length).toString('hex').toUpperCase();
  }

  static async createCode(createdBy, options = {}) {
    const {
      maxUses = 1,
      expiresIn = 24 * 60 * 60 * 1000, // 24 hours in milliseconds
      customCode = null
    } = options;

    const code = customCode || this.generateCode();
    const expiresAt = new Date(Date.now() + expiresIn);

    try {
      const result = await query(
        'INSERT INTO invite_codes (code, created_by, expires_at, max_uses) VALUES ($1, $2, $3, $4) RETURNING *',
        [code, createdBy, expiresAt, maxUses]
      );
      return result.rows[0];
    } catch (error) {
      if (error.code === '23505') { // Unique constraint violation
        throw new Error('Invite code already exists');
      }
      throw error;
    }
  }

  static async validateCode(code) {
    const result = await query(`
      SELECT * FROM invite_codes 
      WHERE code = $1 
        AND is_active = true 
        AND (expires_at IS NULL OR expires_at > NOW())
        AND current_uses < max_uses
    `, [code]);
    
    return result.rows[0];
  }

  static async useCode(code) {
    const result = await query(
      'UPDATE invite_codes SET current_uses = current_uses + 1 WHERE code = $1 RETURNING *',
      [code]
    );
    return result.rows[0];
  }

  static async validateAndUseCode(code) {
    try {
      // Start transaction
      await query('BEGIN');
      
      const inviteCode = await this.validateCode(code);
      if (!inviteCode) {
        await query('ROLLBACK');
        return false;
      }

      // Use the code
      const updatedCode = await this.useCode(code);
      
      // Deactivate if max uses reached
      if (updatedCode.current_uses >= updatedCode.max_uses) {
        await query('UPDATE invite_codes SET is_active = false WHERE code = $1', [code]);
      }

      await query('COMMIT');
      return true;
    } catch (error) {
      await query('ROLLBACK');
      console.error('Error using invite code:', error);
      return false;
    }
  }

  static async deactivateCode(code) {
    const result = await query(
      'UPDATE invite_codes SET is_active = false WHERE code = $1 RETURNING *',
      [code]
    );
    return result.rows[0];
  }

  static async getAllCodes() {
    const result = await query(`
      SELECT 
        code, 
        created_by, 
        created_at, 
        expires_at,
        max_uses,
        current_uses,
        is_active,
        CASE 
          WHEN expires_at < NOW() THEN 'expired'
          WHEN current_uses >= max_uses THEN 'exhausted'
          WHEN is_active = false THEN 'deactivated'
          ELSE 'active'
        END as status
      FROM invite_codes 
      ORDER BY created_at DESC
    `);
    return result.rows;
  }

  static async getActiveCodes() {
    const result = await query(`
      SELECT * FROM invite_codes 
      WHERE is_active = true 
        AND (expires_at IS NULL OR expires_at > NOW())
        AND current_uses < max_uses
      ORDER BY created_at DESC
    `);
    return result.rows;
  }

  static async cleanupExpiredCodes() {
    const result = await query(
      'UPDATE invite_codes SET is_active = false WHERE expires_at < NOW() AND is_active = true'
    );
    return result.rowCount;
  }

  // Create a permanent invite code for admins
  static async createPermanentCode(createdBy, customCode = null) {
    return await this.createCode(createdBy, {
      maxUses: 999999,
      expiresIn: 365 * 24 * 60 * 60 * 1000, // 1 year
      customCode
    });
  }
}

module.exports = InviteCode;
