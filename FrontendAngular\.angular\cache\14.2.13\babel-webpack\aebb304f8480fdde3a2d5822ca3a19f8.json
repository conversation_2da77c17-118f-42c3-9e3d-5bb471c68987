{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { isValidDate } from '../util/isDate';\nimport { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { createErrorClass } from '../util/createErrorClass';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { executeSchedule } from '../util/executeSchedule';\nexport const TimeoutError = createErrorClass(_super => function TimeoutErrorImpl(info = null) {\n  _super(this);\n\n  this.message = 'Timeout has occurred';\n  this.name = 'TimeoutError';\n  this.info = info;\n});\nexport function timeout(config, schedulerArg) {\n  const {\n    first,\n    each,\n    with: _with = timeoutErrorFactory,\n    scheduler = schedulerArg !== null && schedulerArg !== void 0 ? schedulerArg : asyncScheduler,\n    meta = null\n  } = isValidDate(config) ? {\n    first: config\n  } : typeof config === 'number' ? {\n    each: config\n  } : config;\n\n  if (first == null && each == null) {\n    throw new TypeError('No timeout provided.');\n  }\n\n  return operate((source, subscriber) => {\n    let originalSourceSubscription;\n    let timerSubscription;\n    let lastValue = null;\n    let seen = 0;\n\n    const startTimer = delay => {\n      timerSubscription = executeSchedule(subscriber, scheduler, () => {\n        try {\n          originalSourceSubscription.unsubscribe();\n          innerFrom(_with({\n            meta,\n            lastValue,\n            seen\n          })).subscribe(subscriber);\n        } catch (err) {\n          subscriber.error(err);\n        }\n      }, delay);\n    };\n\n    originalSourceSubscription = source.subscribe(createOperatorSubscriber(subscriber, value => {\n      timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.unsubscribe();\n      seen++;\n      subscriber.next(lastValue = value);\n      each > 0 && startTimer(each);\n    }, undefined, undefined, () => {\n      if (!(timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.closed)) {\n        timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.unsubscribe();\n      }\n\n      lastValue = null;\n    }));\n    !seen && startTimer(first != null ? typeof first === 'number' ? first : +first - scheduler.now() : each);\n  });\n}\n\nfunction timeoutErrorFactory(info) {\n  throw new TimeoutError(info);\n}", "map": {"version": 3, "names": ["asyncScheduler", "isValidDate", "operate", "innerFrom", "createErrorClass", "createOperatorSubscriber", "executeSchedule", "TimeoutError", "_super", "TimeoutErrorImpl", "info", "message", "name", "timeout", "config", "schedulerArg", "first", "each", "with", "_with", "timeoutErrorFactory", "scheduler", "meta", "TypeError", "source", "subscriber", "originalSourceSubscription", "timerSubscription", "lastValue", "seen", "startTimer", "delay", "unsubscribe", "subscribe", "err", "error", "value", "next", "undefined", "closed", "now"], "sources": ["R:/chateye/FrontendAngular/node_modules/rxjs/dist/esm/internal/operators/timeout.js"], "sourcesContent": ["import { asyncScheduler } from '../scheduler/async';\nimport { isValidDate } from '../util/isDate';\nimport { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { createErrorClass } from '../util/createErrorClass';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { executeSchedule } from '../util/executeSchedule';\nexport const TimeoutError = createErrorClass((_super) => function TimeoutErrorImpl(info = null) {\n    _super(this);\n    this.message = 'Timeout has occurred';\n    this.name = 'TimeoutError';\n    this.info = info;\n});\nexport function timeout(config, schedulerArg) {\n    const { first, each, with: _with = timeoutErrorFactory, scheduler = schedulerArg !== null && schedulerArg !== void 0 ? schedulerArg : asyncScheduler, meta = null, } = (isValidDate(config) ? { first: config } : typeof config === 'number' ? { each: config } : config);\n    if (first == null && each == null) {\n        throw new TypeError('No timeout provided.');\n    }\n    return operate((source, subscriber) => {\n        let originalSourceSubscription;\n        let timerSubscription;\n        let lastValue = null;\n        let seen = 0;\n        const startTimer = (delay) => {\n            timerSubscription = executeSchedule(subscriber, scheduler, () => {\n                try {\n                    originalSourceSubscription.unsubscribe();\n                    innerFrom(_with({\n                        meta,\n                        lastValue,\n                        seen,\n                    })).subscribe(subscriber);\n                }\n                catch (err) {\n                    subscriber.error(err);\n                }\n            }, delay);\n        };\n        originalSourceSubscription = source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.unsubscribe();\n            seen++;\n            subscriber.next((lastValue = value));\n            each > 0 && startTimer(each);\n        }, undefined, undefined, () => {\n            if (!(timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.closed)) {\n                timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.unsubscribe();\n            }\n            lastValue = null;\n        }));\n        !seen && startTimer(first != null ? (typeof first === 'number' ? first : +first - scheduler.now()) : each);\n    });\n}\nfunction timeoutErrorFactory(info) {\n    throw new TimeoutError(info);\n}\n"], "mappings": "AAAA,SAASA,cAAT,QAA+B,oBAA/B;AACA,SAASC,WAAT,QAA4B,gBAA5B;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,SAAT,QAA0B,yBAA1B;AACA,SAASC,gBAAT,QAAiC,0BAAjC;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,SAASC,eAAT,QAAgC,yBAAhC;AACA,OAAO,MAAMC,YAAY,GAAGH,gBAAgB,CAAEI,MAAD,IAAY,SAASC,gBAAT,CAA0BC,IAAI,GAAG,IAAjC,EAAuC;EAC5FF,MAAM,CAAC,IAAD,CAAN;;EACA,KAAKG,OAAL,GAAe,sBAAf;EACA,KAAKC,IAAL,GAAY,cAAZ;EACA,KAAKF,IAAL,GAAYA,IAAZ;AACH,CAL2C,CAArC;AAMP,OAAO,SAASG,OAAT,CAAiBC,MAAjB,EAAyBC,YAAzB,EAAuC;EAC1C,MAAM;IAAEC,KAAF;IAASC,IAAT;IAAeC,IAAI,EAAEC,KAAK,GAAGC,mBAA7B;IAAkDC,SAAS,GAAGN,YAAY,KAAK,IAAjB,IAAyBA,YAAY,KAAK,KAAK,CAA/C,GAAmDA,YAAnD,GAAkEf,cAAhI;IAAgJsB,IAAI,GAAG;EAAvJ,IAAkKrB,WAAW,CAACa,MAAD,CAAX,GAAsB;IAAEE,KAAK,EAAEF;EAAT,CAAtB,GAA0C,OAAOA,MAAP,KAAkB,QAAlB,GAA6B;IAAEG,IAAI,EAAEH;EAAR,CAA7B,GAAgDA,MAAlQ;;EACA,IAAIE,KAAK,IAAI,IAAT,IAAiBC,IAAI,IAAI,IAA7B,EAAmC;IAC/B,MAAM,IAAIM,SAAJ,CAAc,sBAAd,CAAN;EACH;;EACD,OAAOrB,OAAO,CAAC,CAACsB,MAAD,EAASC,UAAT,KAAwB;IACnC,IAAIC,0BAAJ;IACA,IAAIC,iBAAJ;IACA,IAAIC,SAAS,GAAG,IAAhB;IACA,IAAIC,IAAI,GAAG,CAAX;;IACA,MAAMC,UAAU,GAAIC,KAAD,IAAW;MAC1BJ,iBAAiB,GAAGrB,eAAe,CAACmB,UAAD,EAAaJ,SAAb,EAAwB,MAAM;QAC7D,IAAI;UACAK,0BAA0B,CAACM,WAA3B;UACA7B,SAAS,CAACgB,KAAK,CAAC;YACZG,IADY;YAEZM,SAFY;YAGZC;UAHY,CAAD,CAAN,CAAT,CAIII,SAJJ,CAIcR,UAJd;QAKH,CAPD,CAQA,OAAOS,GAAP,EAAY;UACRT,UAAU,CAACU,KAAX,CAAiBD,GAAjB;QACH;MACJ,CAZkC,EAYhCH,KAZgC,CAAnC;IAaH,CAdD;;IAeAL,0BAA0B,GAAGF,MAAM,CAACS,SAAP,CAAiB5B,wBAAwB,CAACoB,UAAD,EAAcW,KAAD,IAAW;MAC1FT,iBAAiB,KAAK,IAAtB,IAA8BA,iBAAiB,KAAK,KAAK,CAAzD,GAA6D,KAAK,CAAlE,GAAsEA,iBAAiB,CAACK,WAAlB,EAAtE;MACAH,IAAI;MACJJ,UAAU,CAACY,IAAX,CAAiBT,SAAS,GAAGQ,KAA7B;MACAnB,IAAI,GAAG,CAAP,IAAYa,UAAU,CAACb,IAAD,CAAtB;IACH,CALqE,EAKnEqB,SALmE,EAKxDA,SALwD,EAK7C,MAAM;MAC3B,IAAI,EAAEX,iBAAiB,KAAK,IAAtB,IAA8BA,iBAAiB,KAAK,KAAK,CAAzD,GAA6D,KAAK,CAAlE,GAAsEA,iBAAiB,CAACY,MAA1F,CAAJ,EAAuG;QACnGZ,iBAAiB,KAAK,IAAtB,IAA8BA,iBAAiB,KAAK,KAAK,CAAzD,GAA6D,KAAK,CAAlE,GAAsEA,iBAAiB,CAACK,WAAlB,EAAtE;MACH;;MACDJ,SAAS,GAAG,IAAZ;IACH,CAVqE,CAAzC,CAA7B;IAWA,CAACC,IAAD,IAASC,UAAU,CAACd,KAAK,IAAI,IAAT,GAAiB,OAAOA,KAAP,KAAiB,QAAjB,GAA4BA,KAA5B,GAAoC,CAACA,KAAD,GAASK,SAAS,CAACmB,GAAV,EAA9D,GAAiFvB,IAAlF,CAAnB;EACH,CAhCa,CAAd;AAiCH;;AACD,SAASG,mBAAT,CAA6BV,IAA7B,EAAmC;EAC/B,MAAM,IAAIH,YAAJ,CAAiBG,IAAjB,CAAN;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}