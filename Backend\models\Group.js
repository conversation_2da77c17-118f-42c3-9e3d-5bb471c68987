const { query } = require('../database/db');

class Group {
  static async create(name, description = '', createdBy = null) {
    const result = await query(
      'INSERT INTO groups (name, description, created_by) VALUES ($1, $2, $3) RETURNING *',
      [name, description, createdBy]
    );
    return result.rows[0];
  }

  static async findById(id) {
    const result = await query(
      'SELECT * FROM groups WHERE id = $1 AND is_active = true',
      [id]
    );
    return result.rows[0];
  }

  static async findByName(name) {
    const result = await query(
      'SELECT * FROM groups WHERE name = $1 AND is_active = true',
      [name]
    );
    return result.rows[0];
  }

  static async getAllGroups() {
    const result = await query(
      'SELECT g.*, u.username as created_by_username FROM groups g LEFT JOIN users u ON g.created_by = u.id WHERE g.is_active = true ORDER BY g.name'
    );
    return result.rows;
  }

  static async getGroupsForUser(userId) {
    const result = await query(`
      SELECT DISTINCT g.*, u.username as created_by_username
      FROM groups g
      LEFT JOIN users u ON g.created_by = u.id
      LEFT JOIN user_group_permissions ugp ON g.id = ugp.group_id
      WHERE g.is_active = true 
      AND (ugp.user_id = $1 AND ugp.is_active = true)
      ORDER BY g.name
    `, [userId]);
    return result.rows;
  }

  static async updateGroup(id, updates) {
    const fields = [];
    const values = [];
    let paramCount = 1;

    if (updates.name !== undefined) {
      fields.push(`name = $${paramCount++}`);
      values.push(updates.name);
    }
    if (updates.description !== undefined) {
      fields.push(`description = $${paramCount++}`);
      values.push(updates.description);
    }

    if (fields.length === 0) {
      throw new Error('No fields to update');
    }

    values.push(id);
    const result = await query(
      `UPDATE groups SET ${fields.join(', ')} WHERE id = $${paramCount} RETURNING *`,
      values
    );
    return result.rows[0];
  }

  static async deactivateGroup(id) {
    const result = await query(
      'UPDATE groups SET is_active = false WHERE id = $1 RETURNING *',
      [id]
    );
    return result.rows[0];
  }

  static async grantUserAccess(userId, groupId, grantedBy = null) {
    try {
      const result = await query(
        'INSERT INTO user_group_permissions (user_id, group_id, granted_by) VALUES ($1, $2, $3) RETURNING *',
        [userId, groupId, grantedBy]
      );
      return result.rows[0];
    } catch (error) {
      if (error.code === '23505') { // Unique constraint violation
        // Update existing permission to active
        const result = await query(
          'UPDATE user_group_permissions SET is_active = true, granted_by = $3, granted_at = CURRENT_TIMESTAMP WHERE user_id = $1 AND group_id = $2 RETURNING *',
          [userId, groupId, grantedBy]
        );
        return result.rows[0];
      }
      throw error;
    }
  }

  static async revokeUserAccess(userId, groupId) {
    const result = await query(
      'UPDATE user_group_permissions SET is_active = false WHERE user_id = $1 AND group_id = $2 RETURNING *',
      [userId, groupId]
    );
    return result.rows[0];
  }

  static async getUserPermissions(userId) {
    const result = await query(`
      SELECT g.*, ugp.granted_at, ugp.is_active as has_access
      FROM groups g
      LEFT JOIN user_group_permissions ugp ON g.id = ugp.group_id AND ugp.user_id = $1
      WHERE g.is_active = true
      ORDER BY g.name
    `, [userId]);
    return result.rows;
  }

  static async getGroupUsers(groupId) {
    const result = await query(`
      SELECT u.id, u.username, ugp.granted_at, ugp.granted_by, ugp.is_active
      FROM user_group_permissions ugp
      JOIN users u ON ugp.user_id = u.id
      WHERE ugp.group_id = $1 AND ugp.is_active = true
      ORDER BY u.username
    `, [groupId]);
    return result.rows;
  }

  static async checkUserAccess(userId, groupId) {
    const result = await query(
      'SELECT * FROM user_group_permissions WHERE user_id = $1 AND group_id = $2 AND is_active = true',
      [userId, groupId]
    );
    return result.rows.length > 0;
  }

  static async initializeDefaultGroups() {
    // Create a default "General" group
    try {
      const generalGroup = await this.findByName('General');
      if (!generalGroup) {
        const newGroup = await this.create('General', 'Default chat group for all users');
        console.log('Created default General group');
        
        // Grant all existing users access to the General group
        await this.grantAllUsersAccessToGroup(newGroup.id);
      } else {
        // Ensure all users have access to the existing General group
        await this.grantAllUsersAccessToGroup(generalGroup.id);
      }
    } catch (error) {
      console.error('Error initializing default groups:', error);
    }
  }

  static async grantAllUsersAccessToGroup(groupId) {
    try {
      // Get all users
      const users = await query('SELECT id FROM users');
      
      // Grant access to each user
      for (const user of users.rows) {
        try {
          await this.grantUserAccess(user.id, groupId, null);
        } catch (error) {
          // Ignore duplicate permission errors
          if (error.code !== '23505') {
            console.error(`Error granting access to user ${user.id}:`, error);
          }
        }
      }
      
      console.log(`Granted all users access to group ${groupId}`);
    } catch (error) {
      console.error('Error granting all users access to group:', error);
    }
  }
}

module.exports = Group;
