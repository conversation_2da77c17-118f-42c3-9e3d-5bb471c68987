{"ast": null, "code": "const PACKET_TYPES = Object.create(null); // no Map = no polyfill\n\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach(key => {\n  PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = {\n  type: \"error\",\n  data: \"parser error\"\n};\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };", "map": {"version": 3, "names": ["PACKET_TYPES", "Object", "create", "PACKET_TYPES_REVERSE", "keys", "for<PERSON>ach", "key", "ERROR_PACKET", "type", "data"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/engine.io-parser/build/esm/commons.js"], "sourcesContent": ["const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach((key) => {\n    PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };\n"], "mappings": "AAAA,MAAMA,YAAY,GAAGC,MAAM,CAACC,MAAP,CAAc,IAAd,CAArB,C,CAA0C;;AAC1CF,YAAY,CAAC,MAAD,CAAZ,GAAuB,GAAvB;AACAA,YAAY,CAAC,OAAD,CAAZ,GAAwB,GAAxB;AACAA,YAAY,CAAC,MAAD,CAAZ,GAAuB,GAAvB;AACAA,YAAY,CAAC,MAAD,CAAZ,GAAuB,GAAvB;AACAA,YAAY,CAAC,SAAD,CAAZ,GAA0B,GAA1B;AACAA,YAAY,CAAC,SAAD,CAAZ,GAA0B,GAA1B;AACAA,YAAY,CAAC,MAAD,CAAZ,GAAuB,GAAvB;AACA,MAAMG,oBAAoB,GAAGF,MAAM,CAACC,MAAP,CAAc,IAAd,CAA7B;AACAD,MAAM,CAACG,IAAP,CAAYJ,YAAZ,EAA0BK,OAA1B,CAAmCC,GAAD,IAAS;EACvCH,oBAAoB,CAACH,YAAY,CAACM,GAAD,CAAb,CAApB,GAA0CA,GAA1C;AACH,CAFD;AAGA,MAAMC,YAAY,GAAG;EAAEC,IAAI,EAAE,OAAR;EAAiBC,IAAI,EAAE;AAAvB,CAArB;AACA,SAAST,YAAT,EAAuBG,oBAAvB,EAA6CI,YAA7C", "ignoreList": []}, "metadata": {}, "sourceType": "module"}