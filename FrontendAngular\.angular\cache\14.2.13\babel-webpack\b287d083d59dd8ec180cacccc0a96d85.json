{"ast": null, "code": "import { Subject } from '../Subject';\nimport { Observable } from '../Observable';\nimport { defer } from './defer';\nconst DEFAULT_CONFIG = {\n  connector: () => new Subject(),\n  resetOnDisconnect: true\n};\nexport function connectable(source, config = DEFAULT_CONFIG) {\n  let connection = null;\n  const {\n    connector,\n    resetOnDisconnect = true\n  } = config;\n  let subject = connector();\n  const result = new Observable(subscriber => {\n    return subject.subscribe(subscriber);\n  });\n\n  result.connect = () => {\n    if (!connection || connection.closed) {\n      connection = defer(() => source).subscribe(subject);\n\n      if (resetOnDisconnect) {\n        connection.add(() => subject = connector());\n      }\n    }\n\n    return connection;\n  };\n\n  return result;\n} //# sourceMappingURL=connectable.js.map", "map": null, "metadata": {}, "sourceType": "module"}