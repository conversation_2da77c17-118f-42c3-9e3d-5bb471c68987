{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { NgModule, Injectable } from '@angular/core';\nimport { coerceArray } from '@angular/cdk/coercion';\nimport { Subject, combineLatest, concat, Observable } from 'rxjs';\nimport { take, skip, debounceTime, map, startWith, takeUntil } from 'rxjs/operators';\nimport * as i1 from '@angular/cdk/platform';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nclass LayoutModule {}\n\nLayoutModule.ɵfac = function LayoutModule_Factory(t) {\n  return new (t || LayoutModule)();\n};\n\nLayoutModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: LayoutModule\n});\nLayoutModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LayoutModule, [{\n    type: NgModule,\n    args: [{}]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Global registry for all dynamically-created, injected media queries. */\n\n\nconst mediaQueriesForWebkitCompatibility = new Set();\n/** Style tag that holds all of the dynamically-created media queries. */\n\nlet mediaQueryStyleNode;\n/** A utility for calling matchMedia queries. */\n\nclass MediaMatcher {\n  constructor(_platform) {\n    this._platform = _platform;\n    this._matchMedia = this._platform.isBrowser && window.matchMedia ? // matchMedia is bound to the window scope intentionally as it is an illegal invocation to\n    // call it from a different scope.\n    window.matchMedia.bind(window) : noopMatchMedia;\n  }\n  /**\n   * Evaluates the given media query and returns the native MediaQueryList from which results\n   * can be retrieved.\n   * Confirms the layout engine will trigger for the selector query provided and returns the\n   * MediaQueryList for the query provided.\n   */\n\n\n  matchMedia(query) {\n    if (this._platform.WEBKIT || this._platform.BLINK) {\n      createEmptyStyleRule(query);\n    }\n\n    return this._matchMedia(query);\n  }\n\n}\n\nMediaMatcher.ɵfac = function MediaMatcher_Factory(t) {\n  return new (t || MediaMatcher)(i0.ɵɵinject(i1.Platform));\n};\n\nMediaMatcher.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: MediaMatcher,\n  factory: MediaMatcher.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MediaMatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i1.Platform\n    }];\n  }, null);\n})();\n/**\n * Creates an empty stylesheet that is used to work around browser inconsistencies related to\n * `matchMedia`. At the time of writing, it handles the following cases:\n * 1. On WebKit browsers, a media query has to have at least one rule in order for `matchMedia`\n * to fire. We work around it by declaring a dummy stylesheet with a `@media` declaration.\n * 2. In some cases Blink browsers will stop firing the `matchMedia` listener if none of the rules\n * inside the `@media` match existing elements on the page. We work around it by having one rule\n * targeting the `body`. See https://github.com/angular/components/issues/23546.\n */\n\n\nfunction createEmptyStyleRule(query) {\n  if (mediaQueriesForWebkitCompatibility.has(query)) {\n    return;\n  }\n\n  try {\n    if (!mediaQueryStyleNode) {\n      mediaQueryStyleNode = document.createElement('style');\n      mediaQueryStyleNode.setAttribute('type', 'text/css');\n      document.head.appendChild(mediaQueryStyleNode);\n    }\n\n    if (mediaQueryStyleNode.sheet) {\n      mediaQueryStyleNode.sheet.insertRule(`@media ${query} {body{ }}`, 0);\n      mediaQueriesForWebkitCompatibility.add(query);\n    }\n  } catch (e) {\n    console.error(e);\n  }\n}\n/** No-op matchMedia replacement for non-browser platforms. */\n\n\nfunction noopMatchMedia(query) {\n  // Use `as any` here to avoid adding additional necessary properties for\n  // the noop matcher.\n  return {\n    matches: query === 'all' || query === '',\n    media: query,\n    addListener: () => {},\n    removeListener: () => {}\n  };\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Utility for checking the matching state of @media queries. */\n\n\nclass BreakpointObserver {\n  constructor(_mediaMatcher, _zone) {\n    this._mediaMatcher = _mediaMatcher;\n    this._zone = _zone;\n    /**  A map of all media queries currently being listened for. */\n\n    this._queries = new Map();\n    /** A subject for all other observables to takeUntil based on. */\n\n    this._destroySubject = new Subject();\n  }\n  /** Completes the active subject, signalling to all other observables to complete. */\n\n\n  ngOnDestroy() {\n    this._destroySubject.next();\n\n    this._destroySubject.complete();\n  }\n  /**\n   * Whether one or more media queries match the current viewport size.\n   * @param value One or more media queries to check.\n   * @returns Whether any of the media queries match.\n   */\n\n\n  isMatched(value) {\n    const queries = splitQueries(coerceArray(value));\n    return queries.some(mediaQuery => this._registerQuery(mediaQuery).mql.matches);\n  }\n  /**\n   * Gets an observable of results for the given queries that will emit new results for any changes\n   * in matching of the given queries.\n   * @param value One or more media queries to check.\n   * @returns A stream of matches for the given queries.\n   */\n\n\n  observe(value) {\n    const queries = splitQueries(coerceArray(value));\n    const observables = queries.map(query => this._registerQuery(query).observable);\n    let stateObservable = combineLatest(observables); // Emit the first state immediately, and then debounce the subsequent emissions.\n\n    stateObservable = concat(stateObservable.pipe(take(1)), stateObservable.pipe(skip(1), debounceTime(0)));\n    return stateObservable.pipe(map(breakpointStates => {\n      const response = {\n        matches: false,\n        breakpoints: {}\n      };\n      breakpointStates.forEach(({\n        matches,\n        query\n      }) => {\n        response.matches = response.matches || matches;\n        response.breakpoints[query] = matches;\n      });\n      return response;\n    }));\n  }\n  /** Registers a specific query to be listened for. */\n\n\n  _registerQuery(query) {\n    // Only set up a new MediaQueryList if it is not already being listened for.\n    if (this._queries.has(query)) {\n      return this._queries.get(query);\n    }\n\n    const mql = this._mediaMatcher.matchMedia(query); // Create callback for match changes and add it is as a listener.\n\n\n    const queryObservable = new Observable(observer => {\n      // Listener callback methods are wrapped to be placed back in ngZone. Callbacks must be placed\n      // back into the zone because matchMedia is only included in Zone.js by loading the\n      // webapis-media-query.js file alongside the zone.js file.  Additionally, some browsers do not\n      // have MediaQueryList inherit from EventTarget, which causes inconsistencies in how Zone.js\n      // patches it.\n      const handler = e => this._zone.run(() => observer.next(e));\n\n      mql.addListener(handler);\n      return () => {\n        mql.removeListener(handler);\n      };\n    }).pipe(startWith(mql), map(({\n      matches\n    }) => ({\n      query,\n      matches\n    })), takeUntil(this._destroySubject)); // Add the MediaQueryList to the set of queries.\n\n    const output = {\n      observable: queryObservable,\n      mql\n    };\n\n    this._queries.set(query, output);\n\n    return output;\n  }\n\n}\n\nBreakpointObserver.ɵfac = function BreakpointObserver_Factory(t) {\n  return new (t || BreakpointObserver)(i0.ɵɵinject(MediaMatcher), i0.ɵɵinject(i0.NgZone));\n};\n\nBreakpointObserver.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: BreakpointObserver,\n  factory: BreakpointObserver.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BreakpointObserver, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: MediaMatcher\n    }, {\n      type: i0.NgZone\n    }];\n  }, null);\n})();\n/**\n * Split each query string into separate query strings if two queries are provided as comma\n * separated.\n */\n\n\nfunction splitQueries(queries) {\n  return queries.map(query => query.split(',')).reduce((a1, a2) => a1.concat(a2)).map(query => query.trim());\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// PascalCase is being used as Breakpoints is used like an enum.\n// tslint:disable-next-line:variable-name\n\n\nconst Breakpoints = {\n  XSmall: '(max-width: 599.98px)',\n  Small: '(min-width: 600px) and (max-width: 959.98px)',\n  Medium: '(min-width: 960px) and (max-width: 1279.98px)',\n  Large: '(min-width: 1280px) and (max-width: 1919.98px)',\n  XLarge: '(min-width: 1920px)',\n  Handset: '(max-width: 599.98px) and (orientation: portrait), ' + '(max-width: 959.98px) and (orientation: landscape)',\n  Tablet: '(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait), ' + '(min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)',\n  Web: '(min-width: 840px) and (orientation: portrait), ' + '(min-width: 1280px) and (orientation: landscape)',\n  HandsetPortrait: '(max-width: 599.98px) and (orientation: portrait)',\n  TabletPortrait: '(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait)',\n  WebPortrait: '(min-width: 840px) and (orientation: portrait)',\n  HandsetLandscape: '(max-width: 959.98px) and (orientation: landscape)',\n  TabletLandscape: '(min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)',\n  WebLandscape: '(min-width: 1280px) and (orientation: landscape)'\n};\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BreakpointObserver, Breakpoints, LayoutModule, MediaMatcher };", "map": {"version": 3, "names": ["i0", "NgModule", "Injectable", "coerce<PERSON><PERSON><PERSON>", "Subject", "combineLatest", "concat", "Observable", "take", "skip", "debounceTime", "map", "startWith", "takeUntil", "i1", "LayoutModule", "ɵfac", "ɵmod", "ɵinj", "type", "args", "mediaQueriesForWebkitCompatibility", "Set", "mediaQueryStyleNode", "MediaMatcher", "constructor", "_platform", "_matchMedia", "<PERSON><PERSON><PERSON><PERSON>", "window", "matchMedia", "bind", "noopMatchMedia", "query", "WEBKIT", "BLINK", "createEmptyStyleRule", "Platform", "ɵprov", "providedIn", "has", "document", "createElement", "setAttribute", "head", "append<PERSON><PERSON><PERSON>", "sheet", "insertRule", "add", "e", "console", "error", "matches", "media", "addListener", "removeListener", "BreakpointObserver", "_mediaMatcher", "_zone", "_queries", "Map", "_destroySubject", "ngOnDestroy", "next", "complete", "isMatched", "value", "queries", "splitQueries", "some", "mediaQuery", "_registerQuery", "mql", "observe", "observables", "observable", "stateObservable", "pipe", "breakpointStates", "response", "breakpoints", "for<PERSON>ach", "get", "queryObservable", "observer", "handler", "run", "output", "set", "NgZone", "split", "reduce", "a1", "a2", "trim", "Breakpoints", "XSmall", "Small", "Medium", "Large", "<PERSON>L<PERSON>ge", "Handset", "Tablet", "Web", "HandsetPortrait", "TabletPortrait", "WebPortrait", "HandsetLandscape", "TabletLandscape", "WebLandscape"], "sources": ["R:/chateye/FrontendAngular/node_modules/@angular/cdk/fesm2020/layout.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { NgModule, Injectable } from '@angular/core';\nimport { coerceArray } from '@angular/cdk/coercion';\nimport { Subject, combineLatest, concat, Observable } from 'rxjs';\nimport { take, skip, debounceTime, map, startWith, takeUntil } from 'rxjs/operators';\nimport * as i1 from '@angular/cdk/platform';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass LayoutModule {\n}\nLayoutModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: LayoutModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nLayoutModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.2.0\", ngImport: i0, type: LayoutModule });\nLayoutModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: LayoutModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: LayoutModule, decorators: [{\n            type: NgModule,\n            args: [{}]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Global registry for all dynamically-created, injected media queries. */\nconst mediaQueriesForWebkitCompatibility = new Set();\n/** Style tag that holds all of the dynamically-created media queries. */\nlet mediaQueryStyleNode;\n/** A utility for calling matchMedia queries. */\nclass MediaMatcher {\n    constructor(_platform) {\n        this._platform = _platform;\n        this._matchMedia =\n            this._platform.isBrowser && window.matchMedia\n                ? // matchMedia is bound to the window scope intentionally as it is an illegal invocation to\n                    // call it from a different scope.\n                    window.matchMedia.bind(window)\n                : noopMatchMedia;\n    }\n    /**\n     * Evaluates the given media query and returns the native MediaQueryList from which results\n     * can be retrieved.\n     * Confirms the layout engine will trigger for the selector query provided and returns the\n     * MediaQueryList for the query provided.\n     */\n    matchMedia(query) {\n        if (this._platform.WEBKIT || this._platform.BLINK) {\n            createEmptyStyleRule(query);\n        }\n        return this._matchMedia(query);\n    }\n}\nMediaMatcher.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MediaMatcher, deps: [{ token: i1.Platform }], target: i0.ɵɵFactoryTarget.Injectable });\nMediaMatcher.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MediaMatcher, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MediaMatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i1.Platform }]; } });\n/**\n * Creates an empty stylesheet that is used to work around browser inconsistencies related to\n * `matchMedia`. At the time of writing, it handles the following cases:\n * 1. On WebKit browsers, a media query has to have at least one rule in order for `matchMedia`\n * to fire. We work around it by declaring a dummy stylesheet with a `@media` declaration.\n * 2. In some cases Blink browsers will stop firing the `matchMedia` listener if none of the rules\n * inside the `@media` match existing elements on the page. We work around it by having one rule\n * targeting the `body`. See https://github.com/angular/components/issues/23546.\n */\nfunction createEmptyStyleRule(query) {\n    if (mediaQueriesForWebkitCompatibility.has(query)) {\n        return;\n    }\n    try {\n        if (!mediaQueryStyleNode) {\n            mediaQueryStyleNode = document.createElement('style');\n            mediaQueryStyleNode.setAttribute('type', 'text/css');\n            document.head.appendChild(mediaQueryStyleNode);\n        }\n        if (mediaQueryStyleNode.sheet) {\n            mediaQueryStyleNode.sheet.insertRule(`@media ${query} {body{ }}`, 0);\n            mediaQueriesForWebkitCompatibility.add(query);\n        }\n    }\n    catch (e) {\n        console.error(e);\n    }\n}\n/** No-op matchMedia replacement for non-browser platforms. */\nfunction noopMatchMedia(query) {\n    // Use `as any` here to avoid adding additional necessary properties for\n    // the noop matcher.\n    return {\n        matches: query === 'all' || query === '',\n        media: query,\n        addListener: () => { },\n        removeListener: () => { },\n    };\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Utility for checking the matching state of @media queries. */\nclass BreakpointObserver {\n    constructor(_mediaMatcher, _zone) {\n        this._mediaMatcher = _mediaMatcher;\n        this._zone = _zone;\n        /**  A map of all media queries currently being listened for. */\n        this._queries = new Map();\n        /** A subject for all other observables to takeUntil based on. */\n        this._destroySubject = new Subject();\n    }\n    /** Completes the active subject, signalling to all other observables to complete. */\n    ngOnDestroy() {\n        this._destroySubject.next();\n        this._destroySubject.complete();\n    }\n    /**\n     * Whether one or more media queries match the current viewport size.\n     * @param value One or more media queries to check.\n     * @returns Whether any of the media queries match.\n     */\n    isMatched(value) {\n        const queries = splitQueries(coerceArray(value));\n        return queries.some(mediaQuery => this._registerQuery(mediaQuery).mql.matches);\n    }\n    /**\n     * Gets an observable of results for the given queries that will emit new results for any changes\n     * in matching of the given queries.\n     * @param value One or more media queries to check.\n     * @returns A stream of matches for the given queries.\n     */\n    observe(value) {\n        const queries = splitQueries(coerceArray(value));\n        const observables = queries.map(query => this._registerQuery(query).observable);\n        let stateObservable = combineLatest(observables);\n        // Emit the first state immediately, and then debounce the subsequent emissions.\n        stateObservable = concat(stateObservable.pipe(take(1)), stateObservable.pipe(skip(1), debounceTime(0)));\n        return stateObservable.pipe(map(breakpointStates => {\n            const response = {\n                matches: false,\n                breakpoints: {},\n            };\n            breakpointStates.forEach(({ matches, query }) => {\n                response.matches = response.matches || matches;\n                response.breakpoints[query] = matches;\n            });\n            return response;\n        }));\n    }\n    /** Registers a specific query to be listened for. */\n    _registerQuery(query) {\n        // Only set up a new MediaQueryList if it is not already being listened for.\n        if (this._queries.has(query)) {\n            return this._queries.get(query);\n        }\n        const mql = this._mediaMatcher.matchMedia(query);\n        // Create callback for match changes and add it is as a listener.\n        const queryObservable = new Observable((observer) => {\n            // Listener callback methods are wrapped to be placed back in ngZone. Callbacks must be placed\n            // back into the zone because matchMedia is only included in Zone.js by loading the\n            // webapis-media-query.js file alongside the zone.js file.  Additionally, some browsers do not\n            // have MediaQueryList inherit from EventTarget, which causes inconsistencies in how Zone.js\n            // patches it.\n            const handler = (e) => this._zone.run(() => observer.next(e));\n            mql.addListener(handler);\n            return () => {\n                mql.removeListener(handler);\n            };\n        }).pipe(startWith(mql), map(({ matches }) => ({ query, matches })), takeUntil(this._destroySubject));\n        // Add the MediaQueryList to the set of queries.\n        const output = { observable: queryObservable, mql };\n        this._queries.set(query, output);\n        return output;\n    }\n}\nBreakpointObserver.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: BreakpointObserver, deps: [{ token: MediaMatcher }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable });\nBreakpointObserver.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: BreakpointObserver, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: BreakpointObserver, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: MediaMatcher }, { type: i0.NgZone }]; } });\n/**\n * Split each query string into separate query strings if two queries are provided as comma\n * separated.\n */\nfunction splitQueries(queries) {\n    return queries\n        .map(query => query.split(','))\n        .reduce((a1, a2) => a1.concat(a2))\n        .map(query => query.trim());\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// PascalCase is being used as Breakpoints is used like an enum.\n// tslint:disable-next-line:variable-name\nconst Breakpoints = {\n    XSmall: '(max-width: 599.98px)',\n    Small: '(min-width: 600px) and (max-width: 959.98px)',\n    Medium: '(min-width: 960px) and (max-width: 1279.98px)',\n    Large: '(min-width: 1280px) and (max-width: 1919.98px)',\n    XLarge: '(min-width: 1920px)',\n    Handset: '(max-width: 599.98px) and (orientation: portrait), ' +\n        '(max-width: 959.98px) and (orientation: landscape)',\n    Tablet: '(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait), ' +\n        '(min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)',\n    Web: '(min-width: 840px) and (orientation: portrait), ' +\n        '(min-width: 1280px) and (orientation: landscape)',\n    HandsetPortrait: '(max-width: 599.98px) and (orientation: portrait)',\n    TabletPortrait: '(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait)',\n    WebPortrait: '(min-width: 840px) and (orientation: portrait)',\n    HandsetLandscape: '(max-width: 959.98px) and (orientation: landscape)',\n    TabletLandscape: '(min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)',\n    WebLandscape: '(min-width: 1280px) and (orientation: landscape)',\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BreakpointObserver, Breakpoints, LayoutModule, MediaMatcher };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,QAAT,EAAmBC,UAAnB,QAAqC,eAArC;AACA,SAASC,WAAT,QAA4B,uBAA5B;AACA,SAASC,OAAT,EAAkBC,aAAlB,EAAiCC,MAAjC,EAAyCC,UAAzC,QAA2D,MAA3D;AACA,SAASC,IAAT,EAAeC,IAAf,EAAqBC,YAArB,EAAmCC,GAAnC,EAAwCC,SAAxC,EAAmDC,SAAnD,QAAoE,gBAApE;AACA,OAAO,KAAKC,EAAZ,MAAoB,uBAApB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,YAAN,CAAmB;;AAEnBA,YAAY,CAACC,IAAb;EAAA,iBAAyGD,YAAzG;AAAA;;AACAA,YAAY,CAACE,IAAb,kBAD+FjB,EAC/F;EAAA,MAA0Ge;AAA1G;AACAA,YAAY,CAACG,IAAb,kBAF+FlB,EAE/F;;AACA;EAAA,mDAH+FA,EAG/F,mBAA2Fe,YAA3F,EAAqH,CAAC;IAC1GI,IAAI,EAAElB,QADoG;IAE1GmB,IAAI,EAAE,CAAC,EAAD;EAFoG,CAAD,CAArH;AAAA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMC,kCAAkC,GAAG,IAAIC,GAAJ,EAA3C;AACA;;AACA,IAAIC,mBAAJ;AACA;;AACA,MAAMC,YAAN,CAAmB;EACfC,WAAW,CAACC,SAAD,EAAY;IACnB,KAAKA,SAAL,GAAiBA,SAAjB;IACA,KAAKC,WAAL,GACI,KAAKD,SAAL,CAAeE,SAAf,IAA4BC,MAAM,CAACC,UAAnC,GACM;IACE;IACAD,MAAM,CAACC,UAAP,CAAkBC,IAAlB,CAAuBF,MAAvB,CAHR,GAIMG,cALV;EAMH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIF,UAAU,CAACG,KAAD,EAAQ;IACd,IAAI,KAAKP,SAAL,CAAeQ,MAAf,IAAyB,KAAKR,SAAL,CAAeS,KAA5C,EAAmD;MAC/CC,oBAAoB,CAACH,KAAD,CAApB;IACH;;IACD,OAAO,KAAKN,WAAL,CAAiBM,KAAjB,CAAP;EACH;;AArBc;;AAuBnBT,YAAY,CAACR,IAAb;EAAA,iBAAyGQ,YAAzG,EA3C+FxB,EA2C/F,UAAuIc,EAAE,CAACuB,QAA1I;AAAA;;AACAb,YAAY,CAACc,KAAb,kBA5C+FtC,EA4C/F;EAAA,OAA6GwB,YAA7G;EAAA,SAA6GA,YAA7G;EAAA,YAAuI;AAAvI;;AACA;EAAA,mDA7C+FxB,EA6C/F,mBAA2FwB,YAA3F,EAAqH,CAAC;IAC1GL,IAAI,EAAEjB,UADoG;IAE1GkB,IAAI,EAAE,CAAC;MAAEmB,UAAU,EAAE;IAAd,CAAD;EAFoG,CAAD,CAArH,EAG4B,YAAY;IAAE,OAAO,CAAC;MAAEpB,IAAI,EAAEL,EAAE,CAACuB;IAAX,CAAD,CAAP;EAAiC,CAH3E;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASD,oBAAT,CAA8BH,KAA9B,EAAqC;EACjC,IAAIZ,kCAAkC,CAACmB,GAAnC,CAAuCP,KAAvC,CAAJ,EAAmD;IAC/C;EACH;;EACD,IAAI;IACA,IAAI,CAACV,mBAAL,EAA0B;MACtBA,mBAAmB,GAAGkB,QAAQ,CAACC,aAAT,CAAuB,OAAvB,CAAtB;MACAnB,mBAAmB,CAACoB,YAApB,CAAiC,MAAjC,EAAyC,UAAzC;MACAF,QAAQ,CAACG,IAAT,CAAcC,WAAd,CAA0BtB,mBAA1B;IACH;;IACD,IAAIA,mBAAmB,CAACuB,KAAxB,EAA+B;MAC3BvB,mBAAmB,CAACuB,KAApB,CAA0BC,UAA1B,CAAsC,UAASd,KAAM,YAArD,EAAkE,CAAlE;MACAZ,kCAAkC,CAAC2B,GAAnC,CAAuCf,KAAvC;IACH;EACJ,CAVD,CAWA,OAAOgB,CAAP,EAAU;IACNC,OAAO,CAACC,KAAR,CAAcF,CAAd;EACH;AACJ;AACD;;;AACA,SAASjB,cAAT,CAAwBC,KAAxB,EAA+B;EAC3B;EACA;EACA,OAAO;IACHmB,OAAO,EAAEnB,KAAK,KAAK,KAAV,IAAmBA,KAAK,KAAK,EADnC;IAEHoB,KAAK,EAAEpB,KAFJ;IAGHqB,WAAW,EAAE,MAAM,CAAG,CAHnB;IAIHC,cAAc,EAAE,MAAM,CAAG;EAJtB,CAAP;AAMH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMC,kBAAN,CAAyB;EACrB/B,WAAW,CAACgC,aAAD,EAAgBC,KAAhB,EAAuB;IAC9B,KAAKD,aAAL,GAAqBA,aAArB;IACA,KAAKC,KAAL,GAAaA,KAAb;IACA;;IACA,KAAKC,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;IACA;;IACA,KAAKC,eAAL,GAAuB,IAAIzD,OAAJ,EAAvB;EACH;EACD;;;EACA0D,WAAW,GAAG;IACV,KAAKD,eAAL,CAAqBE,IAArB;;IACA,KAAKF,eAAL,CAAqBG,QAArB;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIC,SAAS,CAACC,KAAD,EAAQ;IACb,MAAMC,OAAO,GAAGC,YAAY,CAACjE,WAAW,CAAC+D,KAAD,CAAZ,CAA5B;IACA,OAAOC,OAAO,CAACE,IAAR,CAAaC,UAAU,IAAI,KAAKC,cAAL,CAAoBD,UAApB,EAAgCE,GAAhC,CAAoCpB,OAA/D,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIqB,OAAO,CAACP,KAAD,EAAQ;IACX,MAAMC,OAAO,GAAGC,YAAY,CAACjE,WAAW,CAAC+D,KAAD,CAAZ,CAA5B;IACA,MAAMQ,WAAW,GAAGP,OAAO,CAACxD,GAAR,CAAYsB,KAAK,IAAI,KAAKsC,cAAL,CAAoBtC,KAApB,EAA2B0C,UAAhD,CAApB;IACA,IAAIC,eAAe,GAAGvE,aAAa,CAACqE,WAAD,CAAnC,CAHW,CAIX;;IACAE,eAAe,GAAGtE,MAAM,CAACsE,eAAe,CAACC,IAAhB,CAAqBrE,IAAI,CAAC,CAAD,CAAzB,CAAD,EAAgCoE,eAAe,CAACC,IAAhB,CAAqBpE,IAAI,CAAC,CAAD,CAAzB,EAA8BC,YAAY,CAAC,CAAD,CAA1C,CAAhC,CAAxB;IACA,OAAOkE,eAAe,CAACC,IAAhB,CAAqBlE,GAAG,CAACmE,gBAAgB,IAAI;MAChD,MAAMC,QAAQ,GAAG;QACb3B,OAAO,EAAE,KADI;QAEb4B,WAAW,EAAE;MAFA,CAAjB;MAIAF,gBAAgB,CAACG,OAAjB,CAAyB,CAAC;QAAE7B,OAAF;QAAWnB;MAAX,CAAD,KAAwB;QAC7C8C,QAAQ,CAAC3B,OAAT,GAAmB2B,QAAQ,CAAC3B,OAAT,IAAoBA,OAAvC;QACA2B,QAAQ,CAACC,WAAT,CAAqB/C,KAArB,IAA8BmB,OAA9B;MACH,CAHD;MAIA,OAAO2B,QAAP;IACH,CAV8B,CAAxB,CAAP;EAWH;EACD;;;EACAR,cAAc,CAACtC,KAAD,EAAQ;IAClB;IACA,IAAI,KAAK0B,QAAL,CAAcnB,GAAd,CAAkBP,KAAlB,CAAJ,EAA8B;MAC1B,OAAO,KAAK0B,QAAL,CAAcuB,GAAd,CAAkBjD,KAAlB,CAAP;IACH;;IACD,MAAMuC,GAAG,GAAG,KAAKf,aAAL,CAAmB3B,UAAnB,CAA8BG,KAA9B,CAAZ,CALkB,CAMlB;;;IACA,MAAMkD,eAAe,GAAG,IAAI5E,UAAJ,CAAgB6E,QAAD,IAAc;MACjD;MACA;MACA;MACA;MACA;MACA,MAAMC,OAAO,GAAIpC,CAAD,IAAO,KAAKS,KAAL,CAAW4B,GAAX,CAAe,MAAMF,QAAQ,CAACrB,IAAT,CAAcd,CAAd,CAArB,CAAvB;;MACAuB,GAAG,CAAClB,WAAJ,CAAgB+B,OAAhB;MACA,OAAO,MAAM;QACTb,GAAG,CAACjB,cAAJ,CAAmB8B,OAAnB;MACH,CAFD;IAGH,CAXuB,EAWrBR,IAXqB,CAWhBjE,SAAS,CAAC4D,GAAD,CAXO,EAWA7D,GAAG,CAAC,CAAC;MAAEyC;IAAF,CAAD,MAAkB;MAAEnB,KAAF;MAASmB;IAAT,CAAlB,CAAD,CAXH,EAW4CvC,SAAS,CAAC,KAAKgD,eAAN,CAXrD,CAAxB,CAPkB,CAmBlB;;IACA,MAAM0B,MAAM,GAAG;MAAEZ,UAAU,EAAEQ,eAAd;MAA+BX;IAA/B,CAAf;;IACA,KAAKb,QAAL,CAAc6B,GAAd,CAAkBvD,KAAlB,EAAyBsD,MAAzB;;IACA,OAAOA,MAAP;EACH;;AAvEoB;;AAyEzB/B,kBAAkB,CAACxC,IAAnB;EAAA,iBAA+GwC,kBAA/G,EA1K+FxD,EA0K/F,UAAmJwB,YAAnJ,GA1K+FxB,EA0K/F,UAA4KA,EAAE,CAACyF,MAA/K;AAAA;;AACAjC,kBAAkB,CAAClB,KAAnB,kBA3K+FtC,EA2K/F;EAAA,OAAmHwD,kBAAnH;EAAA,SAAmHA,kBAAnH;EAAA,YAAmJ;AAAnJ;;AACA;EAAA,mDA5K+FxD,EA4K/F,mBAA2FwD,kBAA3F,EAA2H,CAAC;IAChHrC,IAAI,EAAEjB,UAD0G;IAEhHkB,IAAI,EAAE,CAAC;MAAEmB,UAAU,EAAE;IAAd,CAAD;EAF0G,CAAD,CAA3H,EAG4B,YAAY;IAAE,OAAO,CAAC;MAAEpB,IAAI,EAAEK;IAAR,CAAD,EAAyB;MAAEL,IAAI,EAAEnB,EAAE,CAACyF;IAAX,CAAzB,CAAP;EAAuD,CAHjG;AAAA;AAIA;AACA;AACA;AACA;;;AACA,SAASrB,YAAT,CAAsBD,OAAtB,EAA+B;EAC3B,OAAOA,OAAO,CACTxD,GADE,CACEsB,KAAK,IAAIA,KAAK,CAACyD,KAAN,CAAY,GAAZ,CADX,EAEFC,MAFE,CAEK,CAACC,EAAD,EAAKC,EAAL,KAAYD,EAAE,CAACtF,MAAH,CAAUuF,EAAV,CAFjB,EAGFlF,GAHE,CAGEsB,KAAK,IAAIA,KAAK,CAAC6D,IAAN,EAHX,CAAP;AAIH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,WAAW,GAAG;EAChBC,MAAM,EAAE,uBADQ;EAEhBC,KAAK,EAAE,8CAFS;EAGhBC,MAAM,EAAE,+CAHQ;EAIhBC,KAAK,EAAE,gDAJS;EAKhBC,MAAM,EAAE,qBALQ;EAMhBC,OAAO,EAAE,wDACL,oDAPY;EAQhBC,MAAM,EAAE,+EACJ,4EATY;EAUhBC,GAAG,EAAE,qDACD,kDAXY;EAYhBC,eAAe,EAAE,mDAZD;EAahBC,cAAc,EAAE,0EAbA;EAchBC,WAAW,EAAE,gDAdG;EAehBC,gBAAgB,EAAE,oDAfF;EAgBhBC,eAAe,EAAE,4EAhBD;EAiBhBC,YAAY,EAAE;AAjBE,CAApB;AAoBA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASrD,kBAAT,EAA6BuC,WAA7B,EAA0ChF,YAA1C,EAAwDS,YAAxD", "ignoreList": []}, "metadata": {}, "sourceType": "module"}