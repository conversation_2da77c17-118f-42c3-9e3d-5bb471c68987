{"ast": null, "code": "import { EMPTY } from '../observable/empty';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nimport { timer } from '../observable/timer';\nexport function repeat(countOrConfig) {\n  let count = Infinity;\n  let delay;\n\n  if (countOrConfig != null) {\n    if (typeof countOrConfig === 'object') {\n      ({\n        count = Infinity,\n        delay\n      } = countOrConfig);\n    } else {\n      count = countOrConfig;\n    }\n  }\n\n  return count <= 0 ? () => EMPTY : operate((source, subscriber) => {\n    let soFar = 0;\n    let sourceSub;\n\n    const resubscribe = () => {\n      sourceSub === null || sourceSub === void 0 ? void 0 : sourceSub.unsubscribe();\n      sourceSub = null;\n\n      if (delay != null) {\n        const notifier = typeof delay === 'number' ? timer(delay) : innerFrom(delay(soFar));\n        const notifierSubscriber = createOperatorSubscriber(subscriber, () => {\n          notifierSubscriber.unsubscribe();\n          subscribeToSource();\n        });\n        notifier.subscribe(notifierSubscriber);\n      } else {\n        subscribeToSource();\n      }\n    };\n\n    const subscribeToSource = () => {\n      let syncUnsub = false;\n      sourceSub = source.subscribe(createOperatorSubscriber(subscriber, undefined, () => {\n        if (++soFar < count) {\n          if (sourceSub) {\n            resubscribe();\n          } else {\n            syncUnsub = true;\n          }\n        } else {\n          subscriber.complete();\n        }\n      }));\n\n      if (syncUnsub) {\n        resubscribe();\n      }\n    };\n\n    subscribeToSource();\n  });\n}", "map": {"version": 3, "names": ["EMPTY", "operate", "createOperatorSubscriber", "innerFrom", "timer", "repeat", "countOrConfig", "count", "Infinity", "delay", "source", "subscriber", "soFar", "sourceSub", "resubscribe", "unsubscribe", "notifier", "notifierSubscriber", "subscribeToSource", "subscribe", "syncUnsub", "undefined", "complete"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/rxjs/dist/esm/internal/operators/repeat.js"], "sourcesContent": ["import { EMPTY } from '../observable/empty';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nimport { timer } from '../observable/timer';\nexport function repeat(countOrConfig) {\n    let count = Infinity;\n    let delay;\n    if (countOrConfig != null) {\n        if (typeof countOrConfig === 'object') {\n            ({ count = Infinity, delay } = countOrConfig);\n        }\n        else {\n            count = countOrConfig;\n        }\n    }\n    return count <= 0\n        ? () => EMPTY\n        : operate((source, subscriber) => {\n            let soFar = 0;\n            let sourceSub;\n            const resubscribe = () => {\n                sourceSub === null || sourceSub === void 0 ? void 0 : sourceSub.unsubscribe();\n                sourceSub = null;\n                if (delay != null) {\n                    const notifier = typeof delay === 'number' ? timer(delay) : innerFrom(delay(soFar));\n                    const notifierSubscriber = createOperatorSubscriber(subscriber, () => {\n                        notifierSubscriber.unsubscribe();\n                        subscribeToSource();\n                    });\n                    notifier.subscribe(notifierSubscriber);\n                }\n                else {\n                    subscribeToSource();\n                }\n            };\n            const subscribeToSource = () => {\n                let syncUnsub = false;\n                sourceSub = source.subscribe(createOperatorSubscriber(subscriber, undefined, () => {\n                    if (++soFar < count) {\n                        if (sourceSub) {\n                            resubscribe();\n                        }\n                        else {\n                            syncUnsub = true;\n                        }\n                    }\n                    else {\n                        subscriber.complete();\n                    }\n                }));\n                if (syncUnsub) {\n                    resubscribe();\n                }\n            };\n            subscribeToSource();\n        });\n}\n"], "mappings": "AAAA,SAASA,KAAT,QAAsB,qBAAtB;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,SAASC,SAAT,QAA0B,yBAA1B;AACA,SAASC,KAAT,QAAsB,qBAAtB;AACA,OAAO,SAASC,MAAT,CAAgBC,aAAhB,EAA+B;EAClC,IAAIC,KAAK,GAAGC,QAAZ;EACA,IAAIC,KAAJ;;EACA,IAAIH,aAAa,IAAI,IAArB,EAA2B;IACvB,IAAI,OAAOA,aAAP,KAAyB,QAA7B,EAAuC;MACnC,CAAC;QAAEC,KAAK,GAAGC,QAAV;QAAoBC;MAApB,IAA8BH,aAA/B;IACH,CAFD,MAGK;MACDC,KAAK,GAAGD,aAAR;IACH;EACJ;;EACD,OAAOC,KAAK,IAAI,CAAT,GACD,MAAMP,KADL,GAEDC,OAAO,CAAC,CAACS,MAAD,EAASC,UAAT,KAAwB;IAC9B,IAAIC,KAAK,GAAG,CAAZ;IACA,IAAIC,SAAJ;;IACA,MAAMC,WAAW,GAAG,MAAM;MACtBD,SAAS,KAAK,IAAd,IAAsBA,SAAS,KAAK,KAAK,CAAzC,GAA6C,KAAK,CAAlD,GAAsDA,SAAS,CAACE,WAAV,EAAtD;MACAF,SAAS,GAAG,IAAZ;;MACA,IAAIJ,KAAK,IAAI,IAAb,EAAmB;QACf,MAAMO,QAAQ,GAAG,OAAOP,KAAP,KAAiB,QAAjB,GAA4BL,KAAK,CAACK,KAAD,CAAjC,GAA2CN,SAAS,CAACM,KAAK,CAACG,KAAD,CAAN,CAArE;QACA,MAAMK,kBAAkB,GAAGf,wBAAwB,CAACS,UAAD,EAAa,MAAM;UAClEM,kBAAkB,CAACF,WAAnB;UACAG,iBAAiB;QACpB,CAHkD,CAAnD;QAIAF,QAAQ,CAACG,SAAT,CAAmBF,kBAAnB;MACH,CAPD,MAQK;QACDC,iBAAiB;MACpB;IACJ,CAdD;;IAeA,MAAMA,iBAAiB,GAAG,MAAM;MAC5B,IAAIE,SAAS,GAAG,KAAhB;MACAP,SAAS,GAAGH,MAAM,CAACS,SAAP,CAAiBjB,wBAAwB,CAACS,UAAD,EAAaU,SAAb,EAAwB,MAAM;QAC/E,IAAI,EAAET,KAAF,GAAUL,KAAd,EAAqB;UACjB,IAAIM,SAAJ,EAAe;YACXC,WAAW;UACd,CAFD,MAGK;YACDM,SAAS,GAAG,IAAZ;UACH;QACJ,CAPD,MAQK;UACDT,UAAU,CAACW,QAAX;QACH;MACJ,CAZoD,CAAzC,CAAZ;;MAaA,IAAIF,SAAJ,EAAe;QACXN,WAAW;MACd;IACJ,CAlBD;;IAmBAI,iBAAiB;EACpB,CAtCQ,CAFb;AAyCH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}