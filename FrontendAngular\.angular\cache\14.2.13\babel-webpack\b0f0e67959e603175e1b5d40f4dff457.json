{"ast": null, "code": "import { map } from './map';\nexport function mapTo(value) {\n  return map(() => value);\n}", "map": {"version": 3, "names": ["map", "mapTo", "value"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/rxjs/dist/esm/internal/operators/mapTo.js"], "sourcesContent": ["import { map } from './map';\nexport function mapTo(value) {\n    return map(() => value);\n}\n"], "mappings": "AAAA,SAASA,GAAT,QAAoB,OAApB;AACA,OAAO,SAASC,KAAT,CAAeC,KAAf,EAAsB;EACzB,OAAOF,GAAG,CAAC,MAAME,KAAP,CAAV;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}