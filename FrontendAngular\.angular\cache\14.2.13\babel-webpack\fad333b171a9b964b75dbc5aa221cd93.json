{"ast": null, "code": "import { transports as DEFAULT_TRANSPORTS } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nimport { createCookieJar, defaultBinaryType, nextTick } from \"./globals.node.js\";\nconst withEventListeners = typeof addEventListener === \"function\" && typeof removeEventListener === \"function\";\nconst OFFLINE_EVENT_LISTENERS = [];\n\nif (withEventListeners) {\n  // within a ServiceWorker, any event handler for the 'offline' event must be added on the initial evaluation of the\n  // script, so we create one single event listener here which will forward the event to the socket instances\n  addEventListener(\"offline\", () => {\n    OFFLINE_EVENT_LISTENERS.forEach(listener => listener());\n  }, false);\n}\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes without upgrade mechanism, which means that it will keep the first low-level transport that\n * successfully establishes the connection.\n *\n * In order to allow tree-shaking, there are no transports included, that's why the `transports` option is mandatory.\n *\n * @example\n * import { SocketWithoutUpgrade, WebSocket } from \"engine.io-client\";\n *\n * const socket = new SocketWithoutUpgrade({\n *   transports: [WebSocket]\n * });\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithUpgrade\n * @see Socket\n */\n\n\nexport let SocketWithoutUpgrade = /*#__PURE__*/(() => {\n  class SocketWithoutUpgrade extends Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri - uri or options\n     * @param {Object} opts - options\n     */\n    constructor(uri, opts) {\n      super();\n      this.binaryType = defaultBinaryType;\n      this.writeBuffer = [];\n      this._prevBufferLen = 0;\n      this._pingInterval = -1;\n      this._pingTimeout = -1;\n      this._maxPayload = -1;\n      /**\n       * The expiration timestamp of the {@link _pingTimeoutTimer} object is tracked, in case the timer is throttled and the\n       * callback is not fired on time. This can happen for example when a laptop is suspended or when a phone is locked.\n       */\n\n      this._pingTimeoutTime = Infinity;\n\n      if (uri && \"object\" === typeof uri) {\n        opts = uri;\n        uri = null;\n      }\n\n      if (uri) {\n        const parsedUri = parse(uri);\n        opts.hostname = parsedUri.host;\n        opts.secure = parsedUri.protocol === \"https\" || parsedUri.protocol === \"wss\";\n        opts.port = parsedUri.port;\n        if (parsedUri.query) opts.query = parsedUri.query;\n      } else if (opts.host) {\n        opts.hostname = parse(opts.host).host;\n      }\n\n      installTimerFunctions(this, opts);\n      this.secure = null != opts.secure ? opts.secure : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n\n      if (opts.hostname && !opts.port) {\n        // if no port is specified manually, use the protocol default\n        opts.port = this.secure ? \"443\" : \"80\";\n      }\n\n      this.hostname = opts.hostname || (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n      this.port = opts.port || (typeof location !== \"undefined\" && location.port ? location.port : this.secure ? \"443\" : \"80\");\n      this.transports = [];\n      this._transportsByName = {};\n      opts.transports.forEach(t => {\n        const transportName = t.prototype.name;\n        this.transports.push(transportName);\n        this._transportsByName[transportName] = t;\n      });\n      this.opts = Object.assign({\n        path: \"/engine.io\",\n        agent: false,\n        withCredentials: false,\n        upgrade: true,\n        timestampParam: \"t\",\n        rememberUpgrade: false,\n        addTrailingSlash: true,\n        rejectUnauthorized: true,\n        perMessageDeflate: {\n          threshold: 1024\n        },\n        transportOptions: {},\n        closeOnBeforeunload: false\n      }, opts);\n      this.opts.path = this.opts.path.replace(/\\/$/, \"\") + (this.opts.addTrailingSlash ? \"/\" : \"\");\n\n      if (typeof this.opts.query === \"string\") {\n        this.opts.query = decode(this.opts.query);\n      }\n\n      if (withEventListeners) {\n        if (this.opts.closeOnBeforeunload) {\n          // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n          // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n          // closed/reloaded)\n          this._beforeunloadEventListener = () => {\n            if (this.transport) {\n              // silently close the transport\n              this.transport.removeAllListeners();\n              this.transport.close();\n            }\n          };\n\n          addEventListener(\"beforeunload\", this._beforeunloadEventListener, false);\n        }\n\n        if (this.hostname !== \"localhost\") {\n          this._offlineEventListener = () => {\n            this._onClose(\"transport close\", {\n              description: \"network connection lost\"\n            });\n          };\n\n          OFFLINE_EVENT_LISTENERS.push(this._offlineEventListener);\n        }\n      }\n\n      if (this.opts.withCredentials) {\n        this._cookieJar = createCookieJar();\n      }\n\n      this._open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} name - transport name\n     * @return {Transport}\n     * @private\n     */\n\n\n    createTransport(name) {\n      const query = Object.assign({}, this.opts.query); // append engine.io protocol identifier\n\n      query.EIO = protocol; // transport name\n\n      query.transport = name; // session id if we already have one\n\n      if (this.id) query.sid = this.id;\n      const opts = Object.assign({}, this.opts, {\n        query,\n        socket: this,\n        hostname: this.hostname,\n        secure: this.secure,\n        port: this.port\n      }, this.opts.transportOptions[name]);\n      return new this._transportsByName[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @private\n     */\n\n\n    _open() {\n      if (this.transports.length === 0) {\n        // Emit error on next tick so it can be listened to\n        this.setTimeoutFn(() => {\n          this.emitReserved(\"error\", \"No transports available\");\n        }, 0);\n        return;\n      }\n\n      const transportName = this.opts.rememberUpgrade && SocketWithoutUpgrade.priorWebsocketSuccess && this.transports.indexOf(\"websocket\") !== -1 ? \"websocket\" : this.transports[0];\n      this.readyState = \"opening\";\n      const transport = this.createTransport(transportName);\n      transport.open();\n      this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @private\n     */\n\n\n    setTransport(transport) {\n      if (this.transport) {\n        this.transport.removeAllListeners();\n      } // set up transport\n\n\n      this.transport = transport; // set up transport listeners\n\n      transport.on(\"drain\", this._onDrain.bind(this)).on(\"packet\", this._onPacket.bind(this)).on(\"error\", this._onError.bind(this)).on(\"close\", reason => this._onClose(\"transport close\", reason));\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @private\n     */\n\n\n    onOpen() {\n      this.readyState = \"open\";\n      SocketWithoutUpgrade.priorWebsocketSuccess = \"websocket\" === this.transport.name;\n      this.emitReserved(\"open\");\n      this.flush();\n    }\n    /**\n     * Handles a packet.\n     *\n     * @private\n     */\n\n\n    _onPacket(packet) {\n      if (\"opening\" === this.readyState || \"open\" === this.readyState || \"closing\" === this.readyState) {\n        this.emitReserved(\"packet\", packet); // Socket is live - any packet counts\n\n        this.emitReserved(\"heartbeat\");\n\n        switch (packet.type) {\n          case \"open\":\n            this.onHandshake(JSON.parse(packet.data));\n            break;\n\n          case \"ping\":\n            this._sendPacket(\"pong\");\n\n            this.emitReserved(\"ping\");\n            this.emitReserved(\"pong\");\n\n            this._resetPingTimeout();\n\n            break;\n\n          case \"error\":\n            const err = new Error(\"server error\"); // @ts-ignore\n\n            err.code = packet.data;\n\n            this._onError(err);\n\n            break;\n\n          case \"message\":\n            this.emitReserved(\"data\", packet.data);\n            this.emitReserved(\"message\", packet.data);\n            break;\n        }\n      } else {}\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @private\n     */\n\n\n    onHandshake(data) {\n      this.emitReserved(\"handshake\", data);\n      this.id = data.sid;\n      this.transport.query.sid = data.sid;\n      this._pingInterval = data.pingInterval;\n      this._pingTimeout = data.pingTimeout;\n      this._maxPayload = data.maxPayload;\n      this.onOpen(); // In case open handler closes socket\n\n      if (\"closed\" === this.readyState) return;\n\n      this._resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @private\n     */\n\n\n    _resetPingTimeout() {\n      this.clearTimeoutFn(this._pingTimeoutTimer);\n      const delay = this._pingInterval + this._pingTimeout;\n      this._pingTimeoutTime = Date.now() + delay;\n      this._pingTimeoutTimer = this.setTimeoutFn(() => {\n        this._onClose(\"ping timeout\");\n      }, delay);\n\n      if (this.opts.autoUnref) {\n        this._pingTimeoutTimer.unref();\n      }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @private\n     */\n\n\n    _onDrain() {\n      this.writeBuffer.splice(0, this._prevBufferLen); // setting prevBufferLen = 0 is very important\n      // for example, when upgrading, upgrade packet is sent over,\n      // and a nonzero prevBufferLen could cause problems on `drain`\n\n      this._prevBufferLen = 0;\n\n      if (0 === this.writeBuffer.length) {\n        this.emitReserved(\"drain\");\n      } else {\n        this.flush();\n      }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @private\n     */\n\n\n    flush() {\n      if (\"closed\" !== this.readyState && this.transport.writable && !this.upgrading && this.writeBuffer.length) {\n        const packets = this._getWritablePackets();\n\n        this.transport.send(packets); // keep track of current length of writeBuffer\n        // splice writeBuffer and callbackBuffer on `drain`\n\n        this._prevBufferLen = packets.length;\n        this.emitReserved(\"flush\");\n      }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */\n\n\n    _getWritablePackets() {\n      const shouldCheckPayloadSize = this._maxPayload && this.transport.name === \"polling\" && this.writeBuffer.length > 1;\n\n      if (!shouldCheckPayloadSize) {\n        return this.writeBuffer;\n      }\n\n      let payloadSize = 1; // first packet type\n\n      for (let i = 0; i < this.writeBuffer.length; i++) {\n        const data = this.writeBuffer[i].data;\n\n        if (data) {\n          payloadSize += byteLength(data);\n        }\n\n        if (i > 0 && payloadSize > this._maxPayload) {\n          return this.writeBuffer.slice(0, i);\n        }\n\n        payloadSize += 2; // separator + packet type\n      }\n\n      return this.writeBuffer;\n    }\n    /**\n     * Checks whether the heartbeat timer has expired but the socket has not yet been notified.\n     *\n     * Note: this method is private for now because it does not really fit the WebSocket API, but if we put it in the\n     * `write()` method then the message would not be buffered by the Socket.IO client.\n     *\n     * @return {boolean}\n     * @private\n     */\n\n    /* private */\n\n\n    _hasPingExpired() {\n      if (!this._pingTimeoutTime) return true;\n\n      const hasExpired = Date.now() > this._pingTimeoutTime;\n\n      if (hasExpired) {\n        this._pingTimeoutTime = 0;\n        nextTick(() => {\n          this._onClose(\"ping timeout\");\n        }, this.setTimeoutFn);\n      }\n\n      return hasExpired;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @return {Socket} for chaining.\n     */\n\n\n    write(msg, options, fn) {\n      this._sendPacket(\"message\", msg, options, fn);\n\n      return this;\n    }\n    /**\n     * Sends a message. Alias of {@link Socket#write}.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @return {Socket} for chaining.\n     */\n\n\n    send(msg, options, fn) {\n      this._sendPacket(\"message\", msg, options, fn);\n\n      return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} type: packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @private\n     */\n\n\n    _sendPacket(type, data, options, fn) {\n      if (\"function\" === typeof data) {\n        fn = data;\n        data = undefined;\n      }\n\n      if (\"function\" === typeof options) {\n        fn = options;\n        options = null;\n      }\n\n      if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n        return;\n      }\n\n      options = options || {};\n      options.compress = false !== options.compress;\n      const packet = {\n        type: type,\n        data: data,\n        options: options\n      };\n      this.emitReserved(\"packetCreate\", packet);\n      this.writeBuffer.push(packet);\n      if (fn) this.once(\"flush\", fn);\n      this.flush();\n    }\n    /**\n     * Closes the connection.\n     */\n\n\n    close() {\n      const close = () => {\n        this._onClose(\"forced close\");\n\n        this.transport.close();\n      };\n\n      const cleanupAndClose = () => {\n        this.off(\"upgrade\", cleanupAndClose);\n        this.off(\"upgradeError\", cleanupAndClose);\n        close();\n      };\n\n      const waitForUpgrade = () => {\n        // wait for upgrade to finish since we can't send packets while pausing a transport\n        this.once(\"upgrade\", cleanupAndClose);\n        this.once(\"upgradeError\", cleanupAndClose);\n      };\n\n      if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n        this.readyState = \"closing\";\n\n        if (this.writeBuffer.length) {\n          this.once(\"drain\", () => {\n            if (this.upgrading) {\n              waitForUpgrade();\n            } else {\n              close();\n            }\n          });\n        } else if (this.upgrading) {\n          waitForUpgrade();\n        } else {\n          close();\n        }\n      }\n\n      return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @private\n     */\n\n\n    _onError(err) {\n      SocketWithoutUpgrade.priorWebsocketSuccess = false;\n\n      if (this.opts.tryAllTransports && this.transports.length > 1 && this.readyState === \"opening\") {\n        this.transports.shift();\n        return this._open();\n      }\n\n      this.emitReserved(\"error\", err);\n\n      this._onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @private\n     */\n\n\n    _onClose(reason, description) {\n      if (\"opening\" === this.readyState || \"open\" === this.readyState || \"closing\" === this.readyState) {\n        // clear timers\n        this.clearTimeoutFn(this._pingTimeoutTimer); // stop event from firing again for transport\n\n        this.transport.removeAllListeners(\"close\"); // ensure transport won't stay open\n\n        this.transport.close(); // ignore further transport communication\n\n        this.transport.removeAllListeners();\n\n        if (withEventListeners) {\n          if (this._beforeunloadEventListener) {\n            removeEventListener(\"beforeunload\", this._beforeunloadEventListener, false);\n          }\n\n          if (this._offlineEventListener) {\n            const i = OFFLINE_EVENT_LISTENERS.indexOf(this._offlineEventListener);\n\n            if (i !== -1) {\n              OFFLINE_EVENT_LISTENERS.splice(i, 1);\n            }\n          }\n        } // set ready state\n\n\n        this.readyState = \"closed\"; // clear session id\n\n        this.id = null; // emit close event\n\n        this.emitReserved(\"close\", reason, description); // clean buffers after, so users can still\n        // grab the buffers on `close` event\n\n        this.writeBuffer = [];\n        this._prevBufferLen = 0;\n      }\n    }\n\n  }\n\n  SocketWithoutUpgrade.protocol = protocol;\n  /**\n   * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n   * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n   *\n   * This class comes with an upgrade mechanism, which means that once the connection is established with the first\n   * low-level transport, it will try to upgrade to a better transport.\n   *\n   * In order to allow tree-shaking, there are no transports included, that's why the `transports` option is mandatory.\n   *\n   * @example\n   * import { SocketWithUpgrade, WebSocket } from \"engine.io-client\";\n   *\n   * const socket = new SocketWithUpgrade({\n   *   transports: [WebSocket]\n   * });\n   *\n   * socket.on(\"open\", () => {\n   *   socket.send(\"hello\");\n   * });\n   *\n   * @see SocketWithoutUpgrade\n   * @see Socket\n   */\n\n  return SocketWithoutUpgrade;\n})();\nexport class SocketWithUpgrade extends SocketWithoutUpgrade {\n  constructor() {\n    super(...arguments);\n    this._upgrades = [];\n  }\n\n  onOpen() {\n    super.onOpen();\n\n    if (\"open\" === this.readyState && this.opts.upgrade) {\n      for (let i = 0; i < this._upgrades.length; i++) {\n        this._probe(this._upgrades[i]);\n      }\n    }\n  }\n  /**\n   * Probes a transport.\n   *\n   * @param {String} name - transport name\n   * @private\n   */\n\n\n  _probe(name) {\n    let transport = this.createTransport(name);\n    let failed = false;\n    SocketWithoutUpgrade.priorWebsocketSuccess = false;\n\n    const onTransportOpen = () => {\n      if (failed) return;\n      transport.send([{\n        type: \"ping\",\n        data: \"probe\"\n      }]);\n      transport.once(\"packet\", msg => {\n        if (failed) return;\n\n        if (\"pong\" === msg.type && \"probe\" === msg.data) {\n          this.upgrading = true;\n          this.emitReserved(\"upgrading\", transport);\n          if (!transport) return;\n          SocketWithoutUpgrade.priorWebsocketSuccess = \"websocket\" === transport.name;\n          this.transport.pause(() => {\n            if (failed) return;\n            if (\"closed\" === this.readyState) return;\n            cleanup();\n            this.setTransport(transport);\n            transport.send([{\n              type: \"upgrade\"\n            }]);\n            this.emitReserved(\"upgrade\", transport);\n            transport = null;\n            this.upgrading = false;\n            this.flush();\n          });\n        } else {\n          const err = new Error(\"probe error\"); // @ts-ignore\n\n          err.transport = transport.name;\n          this.emitReserved(\"upgradeError\", err);\n        }\n      });\n    };\n\n    function freezeTransport() {\n      if (failed) return; // Any callback called by transport should be ignored since now\n\n      failed = true;\n      cleanup();\n      transport.close();\n      transport = null;\n    } // Handle any error that happens while probing\n\n\n    const onerror = err => {\n      const error = new Error(\"probe error: \" + err); // @ts-ignore\n\n      error.transport = transport.name;\n      freezeTransport();\n      this.emitReserved(\"upgradeError\", error);\n    };\n\n    function onTransportClose() {\n      onerror(\"transport closed\");\n    } // When the socket is closed while we're probing\n\n\n    function onclose() {\n      onerror(\"socket closed\");\n    } // When the socket is upgraded while we're probing\n\n\n    function onupgrade(to) {\n      if (transport && to.name !== transport.name) {\n        freezeTransport();\n      }\n    } // Remove all listeners on the transport and on self\n\n\n    const cleanup = () => {\n      transport.removeListener(\"open\", onTransportOpen);\n      transport.removeListener(\"error\", onerror);\n      transport.removeListener(\"close\", onTransportClose);\n      this.off(\"close\", onclose);\n      this.off(\"upgrading\", onupgrade);\n    };\n\n    transport.once(\"open\", onTransportOpen);\n    transport.once(\"error\", onerror);\n    transport.once(\"close\", onTransportClose);\n    this.once(\"close\", onclose);\n    this.once(\"upgrading\", onupgrade);\n\n    if (this._upgrades.indexOf(\"webtransport\") !== -1 && name !== \"webtransport\") {\n      // favor WebTransport\n      this.setTimeoutFn(() => {\n        if (!failed) {\n          transport.open();\n        }\n      }, 200);\n    } else {\n      transport.open();\n    }\n  }\n\n  onHandshake(data) {\n    this._upgrades = this._filterUpgrades(data.upgrades);\n    super.onHandshake(data);\n  }\n  /**\n   * Filters upgrades, returning only those matching client transports.\n   *\n   * @param {Array} upgrades - server upgrades\n   * @private\n   */\n\n\n  _filterUpgrades(upgrades) {\n    const filteredUpgrades = [];\n\n    for (let i = 0; i < upgrades.length; i++) {\n      if (~this.transports.indexOf(upgrades[i])) filteredUpgrades.push(upgrades[i]);\n    }\n\n    return filteredUpgrades;\n  }\n\n}\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes with an upgrade mechanism, which means that once the connection is established with the first\n * low-level transport, it will try to upgrade to a better transport.\n *\n * @example\n * import { Socket } from \"engine.io-client\";\n *\n * const socket = new Socket();\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithoutUpgrade\n * @see SocketWithUpgrade\n */\n\nexport class Socket extends SocketWithUpgrade {\n  constructor(uri, opts = {}) {\n    const o = typeof uri === \"object\" ? uri : opts;\n\n    if (!o.transports || o.transports && typeof o.transports[0] === \"string\") {\n      o.transports = (o.transports || [\"polling\", \"websocket\", \"webtransport\"]).map(transportName => DEFAULT_TRANSPORTS[transportName]).filter(t => !!t);\n    }\n\n    super(uri, o);\n  }\n\n}", "map": null, "metadata": {}, "sourceType": "module"}