{"ast": null, "code": "export const timeoutProvider = {\n  setTimeout(handler, timeout, ...args) {\n    const {\n      delegate\n    } = timeoutProvider;\n\n    if (delegate === null || delegate === void 0 ? void 0 : delegate.setTimeout) {\n      return delegate.setTimeout(handler, timeout, ...args);\n    }\n\n    return setTimeout(handler, timeout, ...args);\n  },\n\n  clearTimeout(handle) {\n    const {\n      delegate\n    } = timeoutProvider;\n    return ((delegate === null || delegate === void 0 ? void 0 : delegate.clearTimeout) || clearTimeout)(handle);\n  },\n\n  delegate: undefined\n};", "map": {"version": 3, "names": ["timeout<PERSON>rovider", "setTimeout", "handler", "timeout", "args", "delegate", "clearTimeout", "handle", "undefined"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/rxjs/dist/esm/internal/scheduler/timeoutProvider.js"], "sourcesContent": ["export const timeoutProvider = {\n    setTimeout(handler, timeout, ...args) {\n        const { delegate } = timeoutProvider;\n        if (delegate === null || delegate === void 0 ? void 0 : delegate.setTimeout) {\n            return delegate.setTimeout(handler, timeout, ...args);\n        }\n        return setTimeout(handler, timeout, ...args);\n    },\n    clearTimeout(handle) {\n        const { delegate } = timeoutProvider;\n        return ((delegate === null || delegate === void 0 ? void 0 : delegate.clearTimeout) || clearTimeout)(handle);\n    },\n    delegate: undefined,\n};\n"], "mappings": "AAAA,OAAO,MAAMA,eAAe,GAAG;EAC3BC,UAAU,CAACC,OAAD,EAAUC,OAAV,EAAmB,GAAGC,IAAtB,EAA4B;IAClC,MAAM;MAAEC;IAAF,IAAeL,eAArB;;IACA,IAAIK,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,KAAK,KAAK,CAAvC,GAA2C,KAAK,CAAhD,GAAoDA,QAAQ,CAACJ,UAAjE,EAA6E;MACzE,OAAOI,QAAQ,CAACJ,UAAT,CAAoBC,OAApB,EAA6BC,OAA7B,EAAsC,GAAGC,IAAzC,CAAP;IACH;;IACD,OAAOH,UAAU,CAACC,OAAD,EAAUC,OAAV,EAAmB,GAAGC,IAAtB,CAAjB;EACH,CAP0B;;EAQ3BE,YAAY,CAACC,MAAD,EAAS;IACjB,MAAM;MAAEF;IAAF,IAAeL,eAArB;IACA,OAAO,CAAC,CAACK,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,KAAK,KAAK,CAAvC,GAA2C,KAAK,CAAhD,GAAoDA,QAAQ,CAACC,YAA9D,KAA+EA,YAAhF,EAA8FC,MAA9F,CAAP;EACH,CAX0B;;EAY3BF,QAAQ,EAAEG;AAZiB,CAAxB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}