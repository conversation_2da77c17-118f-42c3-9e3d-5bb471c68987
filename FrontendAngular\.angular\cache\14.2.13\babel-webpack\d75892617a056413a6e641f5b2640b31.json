{"ast": null, "code": "import { Transport } from \"../transport.js\";\nimport { pick, randomString } from \"../util.js\";\nimport { encodePacket } from \"engine.io-parser\";\nimport { globalThisShim as globalThis, nextTick } from \"../globals.node.js\"; // detect ReactNative environment\n\nconst isReactNative = typeof navigator !== \"undefined\" && typeof navigator.product === \"string\" && navigator.product.toLowerCase() === \"reactnative\";\nexport class BaseWS extends Transport {\n  get name() {\n    return \"websocket\";\n  }\n\n  doOpen() {\n    const uri = this.uri();\n    const protocols = this.opts.protocols; // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n\n    const opts = isReactNative ? {} : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n\n    if (this.opts.extraHeaders) {\n      opts.headers = this.opts.extraHeaders;\n    }\n\n    try {\n      this.ws = this.createSocket(uri, protocols, opts);\n    } catch (err) {\n      return this.emitReserved(\"error\", err);\n    }\n\n    this.ws.binaryType = this.socket.binaryType;\n    this.addEventListeners();\n  }\n  /**\n   * Adds event listeners to the socket\n   *\n   * @private\n   */\n\n\n  addEventListeners() {\n    this.ws.onopen = () => {\n      if (this.opts.autoUnref) {\n        this.ws._socket.unref();\n      }\n\n      this.onOpen();\n    };\n\n    this.ws.onclose = closeEvent => this.onClose({\n      description: \"websocket connection closed\",\n      context: closeEvent\n    });\n\n    this.ws.onmessage = ev => this.onData(ev.data);\n\n    this.ws.onerror = e => this.onError(\"websocket error\", e);\n  }\n\n  write(packets) {\n    this.writable = false; // encodePacket efficient as it uses WS framing\n    // no need for encodePayload\n\n    for (let i = 0; i < packets.length; i++) {\n      const packet = packets[i];\n      const lastPacket = i === packets.length - 1;\n      encodePacket(packet, this.supportsBinary, data => {\n        // Sometimes the websocket has already been closed but the browser didn't\n        // have a chance of informing us about it yet, in that case send will\n        // throw an error\n        try {\n          this.doWrite(packet, data);\n        } catch (e) {}\n\n        if (lastPacket) {\n          // fake drain\n          // defer to next tick to allow Socket to clear writeBuffer\n          nextTick(() => {\n            this.writable = true;\n            this.emitReserved(\"drain\");\n          }, this.setTimeoutFn);\n        }\n      });\n    }\n  }\n\n  doClose() {\n    if (typeof this.ws !== \"undefined\") {\n      this.ws.onerror = () => {};\n\n      this.ws.close();\n      this.ws = null;\n    }\n  }\n  /**\n   * Generates uri for connection.\n   *\n   * @private\n   */\n\n\n  uri() {\n    const schema = this.opts.secure ? \"wss\" : \"ws\";\n    const query = this.query || {}; // append timestamp to URI\n\n    if (this.opts.timestampRequests) {\n      query[this.opts.timestampParam] = randomString();\n    } // communicate binary support capabilities\n\n\n    if (!this.supportsBinary) {\n      query.b64 = 1;\n    }\n\n    return this.createUri(schema, query);\n  }\n\n}\nconst WebSocketCtor = globalThis.WebSocket || globalThis.MozWebSocket;\n/**\n * WebSocket transport based on the built-in `WebSocket` object.\n *\n * Usage: browser, Node.js (since v21), Deno, Bun\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket\n * @see https://caniuse.com/mdn-api_websocket\n * @see https://nodejs.org/api/globals.html#websocket\n */\n\nexport class WS extends BaseWS {\n  createSocket(uri, protocols, opts) {\n    return !isReactNative ? protocols ? new WebSocketCtor(uri, protocols) : new WebSocketCtor(uri) : new WebSocketCtor(uri, protocols, opts);\n  }\n\n  doWrite(_packet, data) {\n    this.ws.send(data);\n  }\n\n}", "map": {"version": 3, "names": ["Transport", "pick", "randomString", "encodePacket", "globalThisShim", "globalThis", "nextTick", "isReactNative", "navigator", "product", "toLowerCase", "BaseWS", "name", "doOpen", "uri", "protocols", "opts", "extraHeaders", "headers", "ws", "createSocket", "err", "emit<PERSON><PERSON><PERSON><PERSON>", "binaryType", "socket", "addEventListeners", "onopen", "autoUnref", "_socket", "unref", "onOpen", "onclose", "closeEvent", "onClose", "description", "context", "onmessage", "ev", "onData", "data", "onerror", "e", "onError", "write", "packets", "writable", "i", "length", "packet", "lastPacket", "supportsBinary", "doWrite", "setTimeoutFn", "doClose", "close", "schema", "secure", "query", "timestampRequests", "timestampParam", "b64", "createUri", "WebSocketCtor", "WebSocket", "MozWebSocket", "WS", "_packet", "send"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/engine.io-client/build/esm/transports/websocket.js"], "sourcesContent": ["import { Transport } from \"../transport.js\";\nimport { pick, randomString } from \"../util.js\";\nimport { encodePacket } from \"engine.io-parser\";\nimport { globalThisShim as globalThis, nextTick } from \"../globals.node.js\";\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" &&\n    typeof navigator.product === \"string\" &&\n    navigator.product.toLowerCase() === \"reactnative\";\nexport class BaseWS extends Transport {\n    get name() {\n        return \"websocket\";\n    }\n    doOpen() {\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative\n            ? {}\n            : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws = this.createSocket(uri, protocols, opts);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @private\n     */\n    addEventListeners() {\n        this.ws.onopen = () => {\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = (closeEvent) => this.onClose({\n            description: \"websocket connection closed\",\n            context: closeEvent,\n        });\n        this.ws.onmessage = (ev) => this.onData(ev.data);\n        this.ws.onerror = (e) => this.onError(\"websocket error\", e);\n    }\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            encodePacket(packet, this.supportsBinary, (data) => {\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    this.doWrite(packet, data);\n                }\n                catch (e) {\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.onerror = () => { };\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        const query = this.query || {};\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = randomString();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n}\nconst WebSocketCtor = globalThis.WebSocket || globalThis.MozWebSocket;\n/**\n * WebSocket transport based on the built-in `WebSocket` object.\n *\n * Usage: browser, Node.js (since v21), Deno, Bun\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket\n * @see https://caniuse.com/mdn-api_websocket\n * @see https://nodejs.org/api/globals.html#websocket\n */\nexport class WS extends BaseWS {\n    createSocket(uri, protocols, opts) {\n        return !isReactNative\n            ? protocols\n                ? new WebSocketCtor(uri, protocols)\n                : new WebSocketCtor(uri)\n            : new WebSocketCtor(uri, protocols, opts);\n    }\n    doWrite(_packet, data) {\n        this.ws.send(data);\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAT,QAA0B,iBAA1B;AACA,SAASC,IAAT,EAAeC,YAAf,QAAmC,YAAnC;AACA,SAASC,YAAT,QAA6B,kBAA7B;AACA,SAASC,cAAc,IAAIC,UAA3B,EAAuCC,QAAvC,QAAuD,oBAAvD,C,CACA;;AACA,MAAMC,aAAa,GAAG,OAAOC,SAAP,KAAqB,WAArB,IAClB,OAAOA,SAAS,CAACC,OAAjB,KAA6B,QADX,IAElBD,SAAS,CAACC,OAAV,CAAkBC,WAAlB,OAAoC,aAFxC;AAGA,OAAO,MAAMC,MAAN,SAAqBX,SAArB,CAA+B;EAC1B,IAAJY,IAAI,GAAG;IACP,OAAO,WAAP;EACH;;EACDC,MAAM,GAAG;IACL,MAAMC,GAAG,GAAG,KAAKA,GAAL,EAAZ;IACA,MAAMC,SAAS,GAAG,KAAKC,IAAL,CAAUD,SAA5B,CAFK,CAGL;;IACA,MAAMC,IAAI,GAAGT,aAAa,GACpB,EADoB,GAEpBN,IAAI,CAAC,KAAKe,IAAN,EAAY,OAAZ,EAAqB,mBAArB,EAA0C,KAA1C,EAAiD,KAAjD,EAAwD,YAAxD,EAAsE,MAAtE,EAA8E,IAA9E,EAAoF,SAApF,EAA+F,oBAA/F,EAAqH,cAArH,EAAqI,iBAArI,EAAwJ,QAAxJ,EAAkK,YAAlK,EAAgL,QAAhL,EAA0L,qBAA1L,CAFV;;IAGA,IAAI,KAAKA,IAAL,CAAUC,YAAd,EAA4B;MACxBD,IAAI,CAACE,OAAL,GAAe,KAAKF,IAAL,CAAUC,YAAzB;IACH;;IACD,IAAI;MACA,KAAKE,EAAL,GAAU,KAAKC,YAAL,CAAkBN,GAAlB,EAAuBC,SAAvB,EAAkCC,IAAlC,CAAV;IACH,CAFD,CAGA,OAAOK,GAAP,EAAY;MACR,OAAO,KAAKC,YAAL,CAAkB,OAAlB,EAA2BD,GAA3B,CAAP;IACH;;IACD,KAAKF,EAAL,CAAQI,UAAR,GAAqB,KAAKC,MAAL,CAAYD,UAAjC;IACA,KAAKE,iBAAL;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIA,iBAAiB,GAAG;IAChB,KAAKN,EAAL,CAAQO,MAAR,GAAiB,MAAM;MACnB,IAAI,KAAKV,IAAL,CAAUW,SAAd,EAAyB;QACrB,KAAKR,EAAL,CAAQS,OAAR,CAAgBC,KAAhB;MACH;;MACD,KAAKC,MAAL;IACH,CALD;;IAMA,KAAKX,EAAL,CAAQY,OAAR,GAAmBC,UAAD,IAAgB,KAAKC,OAAL,CAAa;MAC3CC,WAAW,EAAE,6BAD8B;MAE3CC,OAAO,EAAEH;IAFkC,CAAb,CAAlC;;IAIA,KAAKb,EAAL,CAAQiB,SAAR,GAAqBC,EAAD,IAAQ,KAAKC,MAAL,CAAYD,EAAE,CAACE,IAAf,CAA5B;;IACA,KAAKpB,EAAL,CAAQqB,OAAR,GAAmBC,CAAD,IAAO,KAAKC,OAAL,CAAa,iBAAb,EAAgCD,CAAhC,CAAzB;EACH;;EACDE,KAAK,CAACC,OAAD,EAAU;IACX,KAAKC,QAAL,GAAgB,KAAhB,CADW,CAEX;IACA;;IACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,OAAO,CAACG,MAA5B,EAAoCD,CAAC,EAArC,EAAyC;MACrC,MAAME,MAAM,GAAGJ,OAAO,CAACE,CAAD,CAAtB;MACA,MAAMG,UAAU,GAAGH,CAAC,KAAKF,OAAO,CAACG,MAAR,GAAiB,CAA1C;MACA5C,YAAY,CAAC6C,MAAD,EAAS,KAAKE,cAAd,EAA+BX,IAAD,IAAU;QAChD;QACA;QACA;QACA,IAAI;UACA,KAAKY,OAAL,CAAaH,MAAb,EAAqBT,IAArB;QACH,CAFD,CAGA,OAAOE,CAAP,EAAU,CACT;;QACD,IAAIQ,UAAJ,EAAgB;UACZ;UACA;UACA3C,QAAQ,CAAC,MAAM;YACX,KAAKuC,QAAL,GAAgB,IAAhB;YACA,KAAKvB,YAAL,CAAkB,OAAlB;UACH,CAHO,EAGL,KAAK8B,YAHA,CAAR;QAIH;MACJ,CAjBW,CAAZ;IAkBH;EACJ;;EACDC,OAAO,GAAG;IACN,IAAI,OAAO,KAAKlC,EAAZ,KAAmB,WAAvB,EAAoC;MAChC,KAAKA,EAAL,CAAQqB,OAAR,GAAkB,MAAM,CAAG,CAA3B;;MACA,KAAKrB,EAAL,CAAQmC,KAAR;MACA,KAAKnC,EAAL,GAAU,IAAV;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;;;EACIL,GAAG,GAAG;IACF,MAAMyC,MAAM,GAAG,KAAKvC,IAAL,CAAUwC,MAAV,GAAmB,KAAnB,GAA2B,IAA1C;IACA,MAAMC,KAAK,GAAG,KAAKA,KAAL,IAAc,EAA5B,CAFE,CAGF;;IACA,IAAI,KAAKzC,IAAL,CAAU0C,iBAAd,EAAiC;MAC7BD,KAAK,CAAC,KAAKzC,IAAL,CAAU2C,cAAX,CAAL,GAAkCzD,YAAY,EAA9C;IACH,CANC,CAOF;;;IACA,IAAI,CAAC,KAAKgD,cAAV,EAA0B;MACtBO,KAAK,CAACG,GAAN,GAAY,CAAZ;IACH;;IACD,OAAO,KAAKC,SAAL,CAAeN,MAAf,EAAuBE,KAAvB,CAAP;EACH;;AA7FiC;AA+FtC,MAAMK,aAAa,GAAGzD,UAAU,CAAC0D,SAAX,IAAwB1D,UAAU,CAAC2D,YAAzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,MAAMC,EAAN,SAAiBtD,MAAjB,CAAwB;EAC3BS,YAAY,CAACN,GAAD,EAAMC,SAAN,EAAiBC,IAAjB,EAAuB;IAC/B,OAAO,CAACT,aAAD,GACDQ,SAAS,GACL,IAAI+C,aAAJ,CAAkBhD,GAAlB,EAAuBC,SAAvB,CADK,GAEL,IAAI+C,aAAJ,CAAkBhD,GAAlB,CAHH,GAID,IAAIgD,aAAJ,CAAkBhD,GAAlB,EAAuBC,SAAvB,EAAkCC,IAAlC,CAJN;EAKH;;EACDmC,OAAO,CAACe,OAAD,EAAU3B,IAAV,EAAgB;IACnB,KAAKpB,EAAL,CAAQgD,IAAR,CAAa5B,IAAb;EACH;;AAV0B", "ignoreList": []}, "metadata": {}, "sourceType": "module"}