{"ast": null, "code": "import { map } from './map';\nexport function pluck(...properties) {\n  const length = properties.length;\n\n  if (length === 0) {\n    throw new Error('list of properties cannot be empty.');\n  }\n\n  return map(x => {\n    let currentProp = x;\n\n    for (let i = 0; i < length; i++) {\n      const p = currentProp === null || currentProp === void 0 ? void 0 : currentProp[properties[i]];\n\n      if (typeof p !== 'undefined') {\n        currentProp = p;\n      } else {\n        return undefined;\n      }\n    }\n\n    return currentProp;\n  });\n}", "map": {"version": 3, "names": ["map", "pluck", "properties", "length", "Error", "x", "currentProp", "i", "p", "undefined"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/rxjs/dist/esm/internal/operators/pluck.js"], "sourcesContent": ["import { map } from './map';\nexport function pluck(...properties) {\n    const length = properties.length;\n    if (length === 0) {\n        throw new Error('list of properties cannot be empty.');\n    }\n    return map((x) => {\n        let currentProp = x;\n        for (let i = 0; i < length; i++) {\n            const p = currentProp === null || currentProp === void 0 ? void 0 : currentProp[properties[i]];\n            if (typeof p !== 'undefined') {\n                currentProp = p;\n            }\n            else {\n                return undefined;\n            }\n        }\n        return currentProp;\n    });\n}\n"], "mappings": "AAAA,SAASA,GAAT,QAAoB,OAApB;AACA,OAAO,SAASC,KAAT,CAAe,GAAGC,UAAlB,EAA8B;EACjC,MAAMC,MAAM,GAAGD,UAAU,CAACC,MAA1B;;EACA,IAAIA,MAAM,KAAK,CAAf,EAAkB;IACd,MAAM,IAAIC,KAAJ,CAAU,qCAAV,CAAN;EACH;;EACD,OAAOJ,GAAG,CAAEK,CAAD,IAAO;IACd,IAAIC,WAAW,GAAGD,CAAlB;;IACA,KAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,MAApB,EAA4BI,CAAC,EAA7B,EAAiC;MAC7B,MAAMC,CAAC,GAAGF,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,WAAW,CAACJ,UAAU,CAACK,CAAD,CAAX,CAA/E;;MACA,IAAI,OAAOC,CAAP,KAAa,WAAjB,EAA8B;QAC1BF,WAAW,GAAGE,CAAd;MACH,CAFD,MAGK;QACD,OAAOC,SAAP;MACH;IACJ;;IACD,OAAOH,WAAP;EACH,CAZS,CAAV;AAaH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}