{"ast": null, "code": "import { Observable } from '../Observable';\nimport { argsArgArrayOrObject } from '../util/argsArgArrayOrObject';\nimport { innerFrom } from './innerFrom';\nimport { popResultSelector } from '../util/args';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { createObject } from '../util/createObject';\nexport function forkJoin(...args) {\n  const resultSelector = popResultSelector(args);\n  const {\n    args: sources,\n    keys\n  } = argsArgArrayOrObject(args);\n  const result = new Observable(subscriber => {\n    const {\n      length\n    } = sources;\n\n    if (!length) {\n      subscriber.complete();\n      return;\n    }\n\n    const values = new Array(length);\n    let remainingCompletions = length;\n    let remainingEmissions = length;\n\n    for (let sourceIndex = 0; sourceIndex < length; sourceIndex++) {\n      let hasValue = false;\n      innerFrom(sources[sourceIndex]).subscribe(createOperatorSubscriber(subscriber, value => {\n        if (!hasValue) {\n          hasValue = true;\n          remainingEmissions--;\n        }\n\n        values[sourceIndex] = value;\n      }, () => remainingCompletions--, undefined, () => {\n        if (!remainingCompletions || !hasValue) {\n          if (!remainingEmissions) {\n            subscriber.next(keys ? createObject(keys, values) : values);\n          }\n\n          subscriber.complete();\n        }\n      }));\n    }\n  });\n  return resultSelector ? result.pipe(mapOneOrManyArgs(resultSelector)) : result;\n}", "map": {"version": 3, "names": ["Observable", "argsArgArrayOrObject", "innerFrom", "popResultSelector", "createOperatorSubscriber", "mapOneOrManyArgs", "createObject", "fork<PERSON><PERSON>n", "args", "resultSelector", "sources", "keys", "result", "subscriber", "length", "complete", "values", "Array", "remainingCompletions", "remainingEmissions", "sourceIndex", "hasValue", "subscribe", "value", "undefined", "next", "pipe"], "sources": ["R:/chateye/FrontendAngular/node_modules/rxjs/dist/esm/internal/observable/forkJoin.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { argsArgArrayOrObject } from '../util/argsArgArrayOrObject';\nimport { innerFrom } from './innerFrom';\nimport { popResultSelector } from '../util/args';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { createObject } from '../util/createObject';\nexport function forkJoin(...args) {\n    const resultSelector = popResultSelector(args);\n    const { args: sources, keys } = argsArgArrayOrObject(args);\n    const result = new Observable((subscriber) => {\n        const { length } = sources;\n        if (!length) {\n            subscriber.complete();\n            return;\n        }\n        const values = new Array(length);\n        let remainingCompletions = length;\n        let remainingEmissions = length;\n        for (let sourceIndex = 0; sourceIndex < length; sourceIndex++) {\n            let hasValue = false;\n            innerFrom(sources[sourceIndex]).subscribe(createOperatorSubscriber(subscriber, (value) => {\n                if (!hasValue) {\n                    hasValue = true;\n                    remainingEmissions--;\n                }\n                values[sourceIndex] = value;\n            }, () => remainingCompletions--, undefined, () => {\n                if (!remainingCompletions || !hasValue) {\n                    if (!remainingEmissions) {\n                        subscriber.next(keys ? createObject(keys, values) : values);\n                    }\n                    subscriber.complete();\n                }\n            }));\n        }\n    });\n    return resultSelector ? result.pipe(mapOneOrManyArgs(resultSelector)) : result;\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,oBAAT,QAAqC,8BAArC;AACA,SAASC,SAAT,QAA0B,aAA1B;AACA,SAASC,iBAAT,QAAkC,cAAlC;AACA,SAASC,wBAAT,QAAyC,iCAAzC;AACA,SAASC,gBAAT,QAAiC,0BAAjC;AACA,SAASC,YAAT,QAA6B,sBAA7B;AACA,OAAO,SAASC,QAAT,CAAkB,GAAGC,IAArB,EAA2B;EAC9B,MAAMC,cAAc,GAAGN,iBAAiB,CAACK,IAAD,CAAxC;EACA,MAAM;IAAEA,IAAI,EAAEE,OAAR;IAAiBC;EAAjB,IAA0BV,oBAAoB,CAACO,IAAD,CAApD;EACA,MAAMI,MAAM,GAAG,IAAIZ,UAAJ,CAAgBa,UAAD,IAAgB;IAC1C,MAAM;MAAEC;IAAF,IAAaJ,OAAnB;;IACA,IAAI,CAACI,MAAL,EAAa;MACTD,UAAU,CAACE,QAAX;MACA;IACH;;IACD,MAAMC,MAAM,GAAG,IAAIC,KAAJ,CAAUH,MAAV,CAAf;IACA,IAAII,oBAAoB,GAAGJ,MAA3B;IACA,IAAIK,kBAAkB,GAAGL,MAAzB;;IACA,KAAK,IAAIM,WAAW,GAAG,CAAvB,EAA0BA,WAAW,GAAGN,MAAxC,EAAgDM,WAAW,EAA3D,EAA+D;MAC3D,IAAIC,QAAQ,GAAG,KAAf;MACAnB,SAAS,CAACQ,OAAO,CAACU,WAAD,CAAR,CAAT,CAAgCE,SAAhC,CAA0ClB,wBAAwB,CAACS,UAAD,EAAcU,KAAD,IAAW;QACtF,IAAI,CAACF,QAAL,EAAe;UACXA,QAAQ,GAAG,IAAX;UACAF,kBAAkB;QACrB;;QACDH,MAAM,CAACI,WAAD,CAAN,GAAsBG,KAAtB;MACH,CANiE,EAM/D,MAAML,oBAAoB,EANqC,EAMjCM,SANiC,EAMtB,MAAM;QAC9C,IAAI,CAACN,oBAAD,IAAyB,CAACG,QAA9B,EAAwC;UACpC,IAAI,CAACF,kBAAL,EAAyB;YACrBN,UAAU,CAACY,IAAX,CAAgBd,IAAI,GAAGL,YAAY,CAACK,IAAD,EAAOK,MAAP,CAAf,GAAgCA,MAApD;UACH;;UACDH,UAAU,CAACE,QAAX;QACH;MACJ,CAbiE,CAAlE;IAcH;EACJ,CA1Bc,CAAf;EA2BA,OAAON,cAAc,GAAGG,MAAM,CAACc,IAAP,CAAYrB,gBAAgB,CAACI,cAAD,CAA5B,CAAH,GAAmDG,MAAxE;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}