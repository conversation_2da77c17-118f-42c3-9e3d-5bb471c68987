{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nexport const defaultThrottleConfig = {\n  leading: true,\n  trailing: false\n};\nexport function throttle(durationSelector, config = defaultThrottleConfig) {\n  return operate((source, subscriber) => {\n    const {\n      leading,\n      trailing\n    } = config;\n    let hasValue = false;\n    let sendValue = null;\n    let throttled = null;\n    let isComplete = false;\n\n    const endThrottling = () => {\n      throttled === null || throttled === void 0 ? void 0 : throttled.unsubscribe();\n      throttled = null;\n\n      if (trailing) {\n        send();\n        isComplete && subscriber.complete();\n      }\n    };\n\n    const cleanupThrottling = () => {\n      throttled = null;\n      isComplete && subscriber.complete();\n    };\n\n    const startThrottle = value => throttled = innerFrom(durationSelector(value)).subscribe(createOperatorSubscriber(subscriber, endThrottling, cleanupThrottling));\n\n    const send = () => {\n      if (hasValue) {\n        hasValue = false;\n        const value = sendValue;\n        sendValue = null;\n        subscriber.next(value);\n        !isComplete && startThrottle(value);\n      }\n    };\n\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      hasValue = true;\n      sendValue = value;\n      !(throttled && !throttled.closed) && (leading ? send() : startThrottle(value));\n    }, () => {\n      isComplete = true;\n      !(trailing && hasValue && throttled && !throttled.closed) && subscriber.complete();\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "innerFrom", "defaultThrottleConfig", "leading", "trailing", "throttle", "durationSelector", "config", "source", "subscriber", "hasValue", "sendValue", "throttled", "isComplete", "endThrottling", "unsubscribe", "send", "complete", "cleanupThrottling", "startThrottle", "value", "subscribe", "next", "closed"], "sources": ["R:/chateye/FrontendAngular/node_modules/rxjs/dist/esm/internal/operators/throttle.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nexport const defaultThrottleConfig = {\n    leading: true,\n    trailing: false,\n};\nexport function throttle(durationSelector, config = defaultThrottleConfig) {\n    return operate((source, subscriber) => {\n        const { leading, trailing } = config;\n        let hasValue = false;\n        let sendValue = null;\n        let throttled = null;\n        let isComplete = false;\n        const endThrottling = () => {\n            throttled === null || throttled === void 0 ? void 0 : throttled.unsubscribe();\n            throttled = null;\n            if (trailing) {\n                send();\n                isComplete && subscriber.complete();\n            }\n        };\n        const cleanupThrottling = () => {\n            throttled = null;\n            isComplete && subscriber.complete();\n        };\n        const startThrottle = (value) => (throttled = innerFrom(durationSelector(value)).subscribe(createOperatorSubscriber(subscriber, endThrottling, cleanupThrottling)));\n        const send = () => {\n            if (hasValue) {\n                hasValue = false;\n                const value = sendValue;\n                sendValue = null;\n                subscriber.next(value);\n                !isComplete && startThrottle(value);\n            }\n        };\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            hasValue = true;\n            sendValue = value;\n            !(throttled && !throttled.closed) && (leading ? send() : startThrottle(value));\n        }, () => {\n            isComplete = true;\n            !(trailing && hasValue && throttled && !throttled.closed) && subscriber.complete();\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,SAASC,SAAT,QAA0B,yBAA1B;AACA,OAAO,MAAMC,qBAAqB,GAAG;EACjCC,OAAO,EAAE,IADwB;EAEjCC,QAAQ,EAAE;AAFuB,CAA9B;AAIP,OAAO,SAASC,QAAT,CAAkBC,gBAAlB,EAAoCC,MAAM,GAAGL,qBAA7C,EAAoE;EACvE,OAAOH,OAAO,CAAC,CAACS,MAAD,EAASC,UAAT,KAAwB;IACnC,MAAM;MAAEN,OAAF;MAAWC;IAAX,IAAwBG,MAA9B;IACA,IAAIG,QAAQ,GAAG,KAAf;IACA,IAAIC,SAAS,GAAG,IAAhB;IACA,IAAIC,SAAS,GAAG,IAAhB;IACA,IAAIC,UAAU,GAAG,KAAjB;;IACA,MAAMC,aAAa,GAAG,MAAM;MACxBF,SAAS,KAAK,IAAd,IAAsBA,SAAS,KAAK,KAAK,CAAzC,GAA6C,KAAK,CAAlD,GAAsDA,SAAS,CAACG,WAAV,EAAtD;MACAH,SAAS,GAAG,IAAZ;;MACA,IAAIR,QAAJ,EAAc;QACVY,IAAI;QACJH,UAAU,IAAIJ,UAAU,CAACQ,QAAX,EAAd;MACH;IACJ,CAPD;;IAQA,MAAMC,iBAAiB,GAAG,MAAM;MAC5BN,SAAS,GAAG,IAAZ;MACAC,UAAU,IAAIJ,UAAU,CAACQ,QAAX,EAAd;IACH,CAHD;;IAIA,MAAME,aAAa,GAAIC,KAAD,IAAYR,SAAS,GAAGX,SAAS,CAACK,gBAAgB,CAACc,KAAD,CAAjB,CAAT,CAAmCC,SAAnC,CAA6CrB,wBAAwB,CAACS,UAAD,EAAaK,aAAb,EAA4BI,iBAA5B,CAArE,CAA9C;;IACA,MAAMF,IAAI,GAAG,MAAM;MACf,IAAIN,QAAJ,EAAc;QACVA,QAAQ,GAAG,KAAX;QACA,MAAMU,KAAK,GAAGT,SAAd;QACAA,SAAS,GAAG,IAAZ;QACAF,UAAU,CAACa,IAAX,CAAgBF,KAAhB;QACA,CAACP,UAAD,IAAeM,aAAa,CAACC,KAAD,CAA5B;MACH;IACJ,CARD;;IASAZ,MAAM,CAACa,SAAP,CAAiBrB,wBAAwB,CAACS,UAAD,EAAcW,KAAD,IAAW;MAC7DV,QAAQ,GAAG,IAAX;MACAC,SAAS,GAAGS,KAAZ;MACA,EAAER,SAAS,IAAI,CAACA,SAAS,CAACW,MAA1B,MAAsCpB,OAAO,GAAGa,IAAI,EAAP,GAAYG,aAAa,CAACC,KAAD,CAAtE;IACH,CAJwC,EAItC,MAAM;MACLP,UAAU,GAAG,IAAb;MACA,EAAET,QAAQ,IAAIM,QAAZ,IAAwBE,SAAxB,IAAqC,CAACA,SAAS,CAACW,MAAlD,KAA6Dd,UAAU,CAACQ,QAAX,EAA7D;IACH,CAPwC,CAAzC;EAQH,CApCa,CAAd;AAqCH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}