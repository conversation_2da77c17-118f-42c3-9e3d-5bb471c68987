{"ast": null, "code": "import { Observable } from './Observable';\nimport { Subscription, EMPTY_SUBSCRIPTION } from './Subscription';\nimport { ObjectUnsubscribedError } from './util/ObjectUnsubscribedError';\nimport { arrRemove } from './util/arrRemove';\nimport { errorContext } from './util/errorContext';\nexport class Subject extends Observable {\n  constructor() {\n    super();\n    this.closed = false;\n    this.currentObservers = null;\n    this.observers = [];\n    this.isStopped = false;\n    this.hasError = false;\n    this.thrownError = null;\n  }\n\n  lift(operator) {\n    const subject = new AnonymousSubject(this, this);\n    subject.operator = operator;\n    return subject;\n  }\n\n  _throwIfClosed() {\n    if (this.closed) {\n      throw new ObjectUnsubscribedError();\n    }\n  }\n\n  next(value) {\n    errorContext(() => {\n      this._throwIfClosed();\n\n      if (!this.isStopped) {\n        if (!this.currentObservers) {\n          this.currentObservers = Array.from(this.observers);\n        }\n\n        for (const observer of this.currentObservers) {\n          observer.next(value);\n        }\n      }\n    });\n  }\n\n  error(err) {\n    errorContext(() => {\n      this._throwIfClosed();\n\n      if (!this.isStopped) {\n        this.hasError = this.isStopped = true;\n        this.thrownError = err;\n        const {\n          observers\n        } = this;\n\n        while (observers.length) {\n          observers.shift().error(err);\n        }\n      }\n    });\n  }\n\n  complete() {\n    errorContext(() => {\n      this._throwIfClosed();\n\n      if (!this.isStopped) {\n        this.isStopped = true;\n        const {\n          observers\n        } = this;\n\n        while (observers.length) {\n          observers.shift().complete();\n        }\n      }\n    });\n  }\n\n  unsubscribe() {\n    this.isStopped = this.closed = true;\n    this.observers = this.currentObservers = null;\n  }\n\n  get observed() {\n    var _a;\n\n    return ((_a = this.observers) === null || _a === void 0 ? void 0 : _a.length) > 0;\n  }\n\n  _trySubscribe(subscriber) {\n    this._throwIfClosed();\n\n    return super._trySubscribe(subscriber);\n  }\n\n  _subscribe(subscriber) {\n    this._throwIfClosed();\n\n    this._checkFinalizedStatuses(subscriber);\n\n    return this._innerSubscribe(subscriber);\n  }\n\n  _innerSubscribe(subscriber) {\n    const {\n      hasError,\n      isStopped,\n      observers\n    } = this;\n\n    if (hasError || isStopped) {\n      return EMPTY_SUBSCRIPTION;\n    }\n\n    this.currentObservers = null;\n    observers.push(subscriber);\n    return new Subscription(() => {\n      this.currentObservers = null;\n      arrRemove(observers, subscriber);\n    });\n  }\n\n  _checkFinalizedStatuses(subscriber) {\n    const {\n      hasError,\n      thrownError,\n      isStopped\n    } = this;\n\n    if (hasError) {\n      subscriber.error(thrownError);\n    } else if (isStopped) {\n      subscriber.complete();\n    }\n  }\n\n  asObservable() {\n    const observable = new Observable();\n    observable.source = this;\n    return observable;\n  }\n\n}\n\nSubject.create = (destination, source) => {\n  return new AnonymousSubject(destination, source);\n};\n\nexport class AnonymousSubject extends Subject {\n  constructor(destination, source) {\n    super();\n    this.destination = destination;\n    this.source = source;\n  }\n\n  next(value) {\n    var _a, _b;\n\n    (_b = (_a = this.destination) === null || _a === void 0 ? void 0 : _a.next) === null || _b === void 0 ? void 0 : _b.call(_a, value);\n  }\n\n  error(err) {\n    var _a, _b;\n\n    (_b = (_a = this.destination) === null || _a === void 0 ? void 0 : _a.error) === null || _b === void 0 ? void 0 : _b.call(_a, err);\n  }\n\n  complete() {\n    var _a, _b;\n\n    (_b = (_a = this.destination) === null || _a === void 0 ? void 0 : _a.complete) === null || _b === void 0 ? void 0 : _b.call(_a);\n  }\n\n  _subscribe(subscriber) {\n    var _a, _b;\n\n    return (_b = (_a = this.source) === null || _a === void 0 ? void 0 : _a.subscribe(subscriber)) !== null && _b !== void 0 ? _b : EMPTY_SUBSCRIPTION;\n  }\n\n}", "map": {"version": 3, "names": ["Observable", "Subscription", "EMPTY_SUBSCRIPTION", "ObjectUnsubscribedError", "arr<PERSON><PERSON><PERSON>", "errorContext", "Subject", "constructor", "closed", "currentObservers", "observers", "isStopped", "<PERSON><PERSON><PERSON><PERSON>", "thrownError", "lift", "operator", "subject", "AnonymousSubject", "_throwIfClosed", "next", "value", "Array", "from", "observer", "error", "err", "length", "shift", "complete", "unsubscribe", "observed", "_a", "_trySubscribe", "subscriber", "_subscribe", "_checkFinalizedStatuses", "_innerSubscribe", "push", "asObservable", "observable", "source", "create", "destination", "_b", "call", "subscribe"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/rxjs/dist/esm/internal/Subject.js"], "sourcesContent": ["import { Observable } from './Observable';\nimport { Subscription, EMPTY_SUBSCRIPTION } from './Subscription';\nimport { ObjectUnsubscribedError } from './util/ObjectUnsubscribedError';\nimport { arrRemove } from './util/arrRemove';\nimport { errorContext } from './util/errorContext';\nexport class Subject extends Observable {\n    constructor() {\n        super();\n        this.closed = false;\n        this.currentObservers = null;\n        this.observers = [];\n        this.isStopped = false;\n        this.hasError = false;\n        this.thrownError = null;\n    }\n    lift(operator) {\n        const subject = new AnonymousSubject(this, this);\n        subject.operator = operator;\n        return subject;\n    }\n    _throwIfClosed() {\n        if (this.closed) {\n            throw new ObjectUnsubscribedError();\n        }\n    }\n    next(value) {\n        errorContext(() => {\n            this._throwIfClosed();\n            if (!this.isStopped) {\n                if (!this.currentObservers) {\n                    this.currentObservers = Array.from(this.observers);\n                }\n                for (const observer of this.currentObservers) {\n                    observer.next(value);\n                }\n            }\n        });\n    }\n    error(err) {\n        errorContext(() => {\n            this._throwIfClosed();\n            if (!this.isStopped) {\n                this.hasError = this.isStopped = true;\n                this.thrownError = err;\n                const { observers } = this;\n                while (observers.length) {\n                    observers.shift().error(err);\n                }\n            }\n        });\n    }\n    complete() {\n        errorContext(() => {\n            this._throwIfClosed();\n            if (!this.isStopped) {\n                this.isStopped = true;\n                const { observers } = this;\n                while (observers.length) {\n                    observers.shift().complete();\n                }\n            }\n        });\n    }\n    unsubscribe() {\n        this.isStopped = this.closed = true;\n        this.observers = this.currentObservers = null;\n    }\n    get observed() {\n        var _a;\n        return ((_a = this.observers) === null || _a === void 0 ? void 0 : _a.length) > 0;\n    }\n    _trySubscribe(subscriber) {\n        this._throwIfClosed();\n        return super._trySubscribe(subscriber);\n    }\n    _subscribe(subscriber) {\n        this._throwIfClosed();\n        this._checkFinalizedStatuses(subscriber);\n        return this._innerSubscribe(subscriber);\n    }\n    _innerSubscribe(subscriber) {\n        const { hasError, isStopped, observers } = this;\n        if (hasError || isStopped) {\n            return EMPTY_SUBSCRIPTION;\n        }\n        this.currentObservers = null;\n        observers.push(subscriber);\n        return new Subscription(() => {\n            this.currentObservers = null;\n            arrRemove(observers, subscriber);\n        });\n    }\n    _checkFinalizedStatuses(subscriber) {\n        const { hasError, thrownError, isStopped } = this;\n        if (hasError) {\n            subscriber.error(thrownError);\n        }\n        else if (isStopped) {\n            subscriber.complete();\n        }\n    }\n    asObservable() {\n        const observable = new Observable();\n        observable.source = this;\n        return observable;\n    }\n}\nSubject.create = (destination, source) => {\n    return new AnonymousSubject(destination, source);\n};\nexport class AnonymousSubject extends Subject {\n    constructor(destination, source) {\n        super();\n        this.destination = destination;\n        this.source = source;\n    }\n    next(value) {\n        var _a, _b;\n        (_b = (_a = this.destination) === null || _a === void 0 ? void 0 : _a.next) === null || _b === void 0 ? void 0 : _b.call(_a, value);\n    }\n    error(err) {\n        var _a, _b;\n        (_b = (_a = this.destination) === null || _a === void 0 ? void 0 : _a.error) === null || _b === void 0 ? void 0 : _b.call(_a, err);\n    }\n    complete() {\n        var _a, _b;\n        (_b = (_a = this.destination) === null || _a === void 0 ? void 0 : _a.complete) === null || _b === void 0 ? void 0 : _b.call(_a);\n    }\n    _subscribe(subscriber) {\n        var _a, _b;\n        return (_b = (_a = this.source) === null || _a === void 0 ? void 0 : _a.subscribe(subscriber)) !== null && _b !== void 0 ? _b : EMPTY_SUBSCRIPTION;\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,cAA3B;AACA,SAASC,YAAT,EAAuBC,kBAAvB,QAAiD,gBAAjD;AACA,SAASC,uBAAT,QAAwC,gCAAxC;AACA,SAASC,SAAT,QAA0B,kBAA1B;AACA,SAASC,YAAT,QAA6B,qBAA7B;AACA,OAAO,MAAMC,OAAN,SAAsBN,UAAtB,CAAiC;EACpCO,WAAW,GAAG;IACV;IACA,KAAKC,MAAL,GAAc,KAAd;IACA,KAAKC,gBAAL,GAAwB,IAAxB;IACA,KAAKC,SAAL,GAAiB,EAAjB;IACA,KAAKC,SAAL,GAAiB,KAAjB;IACA,KAAKC,QAAL,GAAgB,KAAhB;IACA,KAAKC,WAAL,GAAmB,IAAnB;EACH;;EACDC,IAAI,CAACC,QAAD,EAAW;IACX,MAAMC,OAAO,GAAG,IAAIC,gBAAJ,CAAqB,IAArB,EAA2B,IAA3B,CAAhB;IACAD,OAAO,CAACD,QAAR,GAAmBA,QAAnB;IACA,OAAOC,OAAP;EACH;;EACDE,cAAc,GAAG;IACb,IAAI,KAAKV,MAAT,EAAiB;MACb,MAAM,IAAIL,uBAAJ,EAAN;IACH;EACJ;;EACDgB,IAAI,CAACC,KAAD,EAAQ;IACRf,YAAY,CAAC,MAAM;MACf,KAAKa,cAAL;;MACA,IAAI,CAAC,KAAKP,SAAV,EAAqB;QACjB,IAAI,CAAC,KAAKF,gBAAV,EAA4B;UACxB,KAAKA,gBAAL,GAAwBY,KAAK,CAACC,IAAN,CAAW,KAAKZ,SAAhB,CAAxB;QACH;;QACD,KAAK,MAAMa,QAAX,IAAuB,KAAKd,gBAA5B,EAA8C;UAC1Cc,QAAQ,CAACJ,IAAT,CAAcC,KAAd;QACH;MACJ;IACJ,CAVW,CAAZ;EAWH;;EACDI,KAAK,CAACC,GAAD,EAAM;IACPpB,YAAY,CAAC,MAAM;MACf,KAAKa,cAAL;;MACA,IAAI,CAAC,KAAKP,SAAV,EAAqB;QACjB,KAAKC,QAAL,GAAgB,KAAKD,SAAL,GAAiB,IAAjC;QACA,KAAKE,WAAL,GAAmBY,GAAnB;QACA,MAAM;UAAEf;QAAF,IAAgB,IAAtB;;QACA,OAAOA,SAAS,CAACgB,MAAjB,EAAyB;UACrBhB,SAAS,CAACiB,KAAV,GAAkBH,KAAlB,CAAwBC,GAAxB;QACH;MACJ;IACJ,CAVW,CAAZ;EAWH;;EACDG,QAAQ,GAAG;IACPvB,YAAY,CAAC,MAAM;MACf,KAAKa,cAAL;;MACA,IAAI,CAAC,KAAKP,SAAV,EAAqB;QACjB,KAAKA,SAAL,GAAiB,IAAjB;QACA,MAAM;UAAED;QAAF,IAAgB,IAAtB;;QACA,OAAOA,SAAS,CAACgB,MAAjB,EAAyB;UACrBhB,SAAS,CAACiB,KAAV,GAAkBC,QAAlB;QACH;MACJ;IACJ,CATW,CAAZ;EAUH;;EACDC,WAAW,GAAG;IACV,KAAKlB,SAAL,GAAiB,KAAKH,MAAL,GAAc,IAA/B;IACA,KAAKE,SAAL,GAAiB,KAAKD,gBAAL,GAAwB,IAAzC;EACH;;EACW,IAARqB,QAAQ,GAAG;IACX,IAAIC,EAAJ;;IACA,OAAO,CAAC,CAACA,EAAE,GAAG,KAAKrB,SAAX,MAA0B,IAA1B,IAAkCqB,EAAE,KAAK,KAAK,CAA9C,GAAkD,KAAK,CAAvD,GAA2DA,EAAE,CAACL,MAA/D,IAAyE,CAAhF;EACH;;EACDM,aAAa,CAACC,UAAD,EAAa;IACtB,KAAKf,cAAL;;IACA,OAAO,MAAMc,aAAN,CAAoBC,UAApB,CAAP;EACH;;EACDC,UAAU,CAACD,UAAD,EAAa;IACnB,KAAKf,cAAL;;IACA,KAAKiB,uBAAL,CAA6BF,UAA7B;;IACA,OAAO,KAAKG,eAAL,CAAqBH,UAArB,CAAP;EACH;;EACDG,eAAe,CAACH,UAAD,EAAa;IACxB,MAAM;MAAErB,QAAF;MAAYD,SAAZ;MAAuBD;IAAvB,IAAqC,IAA3C;;IACA,IAAIE,QAAQ,IAAID,SAAhB,EAA2B;MACvB,OAAOT,kBAAP;IACH;;IACD,KAAKO,gBAAL,GAAwB,IAAxB;IACAC,SAAS,CAAC2B,IAAV,CAAeJ,UAAf;IACA,OAAO,IAAIhC,YAAJ,CAAiB,MAAM;MAC1B,KAAKQ,gBAAL,GAAwB,IAAxB;MACAL,SAAS,CAACM,SAAD,EAAYuB,UAAZ,CAAT;IACH,CAHM,CAAP;EAIH;;EACDE,uBAAuB,CAACF,UAAD,EAAa;IAChC,MAAM;MAAErB,QAAF;MAAYC,WAAZ;MAAyBF;IAAzB,IAAuC,IAA7C;;IACA,IAAIC,QAAJ,EAAc;MACVqB,UAAU,CAACT,KAAX,CAAiBX,WAAjB;IACH,CAFD,MAGK,IAAIF,SAAJ,EAAe;MAChBsB,UAAU,CAACL,QAAX;IACH;EACJ;;EACDU,YAAY,GAAG;IACX,MAAMC,UAAU,GAAG,IAAIvC,UAAJ,EAAnB;IACAuC,UAAU,CAACC,MAAX,GAAoB,IAApB;IACA,OAAOD,UAAP;EACH;;AApGmC;;AAsGxCjC,OAAO,CAACmC,MAAR,GAAiB,CAACC,WAAD,EAAcF,MAAd,KAAyB;EACtC,OAAO,IAAIvB,gBAAJ,CAAqByB,WAArB,EAAkCF,MAAlC,CAAP;AACH,CAFD;;AAGA,OAAO,MAAMvB,gBAAN,SAA+BX,OAA/B,CAAuC;EAC1CC,WAAW,CAACmC,WAAD,EAAcF,MAAd,EAAsB;IAC7B;IACA,KAAKE,WAAL,GAAmBA,WAAnB;IACA,KAAKF,MAAL,GAAcA,MAAd;EACH;;EACDrB,IAAI,CAACC,KAAD,EAAQ;IACR,IAAIW,EAAJ,EAAQY,EAAR;;IACA,CAACA,EAAE,GAAG,CAACZ,EAAE,GAAG,KAAKW,WAAX,MAA4B,IAA5B,IAAoCX,EAAE,KAAK,KAAK,CAAhD,GAAoD,KAAK,CAAzD,GAA6DA,EAAE,CAACZ,IAAtE,MAAgF,IAAhF,IAAwFwB,EAAE,KAAK,KAAK,CAApG,GAAwG,KAAK,CAA7G,GAAiHA,EAAE,CAACC,IAAH,CAAQb,EAAR,EAAYX,KAAZ,CAAjH;EACH;;EACDI,KAAK,CAACC,GAAD,EAAM;IACP,IAAIM,EAAJ,EAAQY,EAAR;;IACA,CAACA,EAAE,GAAG,CAACZ,EAAE,GAAG,KAAKW,WAAX,MAA4B,IAA5B,IAAoCX,EAAE,KAAK,KAAK,CAAhD,GAAoD,KAAK,CAAzD,GAA6DA,EAAE,CAACP,KAAtE,MAAiF,IAAjF,IAAyFmB,EAAE,KAAK,KAAK,CAArG,GAAyG,KAAK,CAA9G,GAAkHA,EAAE,CAACC,IAAH,CAAQb,EAAR,EAAYN,GAAZ,CAAlH;EACH;;EACDG,QAAQ,GAAG;IACP,IAAIG,EAAJ,EAAQY,EAAR;;IACA,CAACA,EAAE,GAAG,CAACZ,EAAE,GAAG,KAAKW,WAAX,MAA4B,IAA5B,IAAoCX,EAAE,KAAK,KAAK,CAAhD,GAAoD,KAAK,CAAzD,GAA6DA,EAAE,CAACH,QAAtE,MAAoF,IAApF,IAA4Fe,EAAE,KAAK,KAAK,CAAxG,GAA4G,KAAK,CAAjH,GAAqHA,EAAE,CAACC,IAAH,CAAQb,EAAR,CAArH;EACH;;EACDG,UAAU,CAACD,UAAD,EAAa;IACnB,IAAIF,EAAJ,EAAQY,EAAR;;IACA,OAAO,CAACA,EAAE,GAAG,CAACZ,EAAE,GAAG,KAAKS,MAAX,MAAuB,IAAvB,IAA+BT,EAAE,KAAK,KAAK,CAA3C,GAA+C,KAAK,CAApD,GAAwDA,EAAE,CAACc,SAAH,CAAaZ,UAAb,CAA9D,MAA4F,IAA5F,IAAoGU,EAAE,KAAK,KAAK,CAAhH,GAAoHA,EAApH,GAAyHzC,kBAAhI;EACH;;AArByC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}