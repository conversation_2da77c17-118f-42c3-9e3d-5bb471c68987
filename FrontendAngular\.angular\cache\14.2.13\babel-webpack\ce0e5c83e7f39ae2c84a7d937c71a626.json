{"ast": null, "code": "import { popScheduler } from '../util/args';\nimport { from } from './from';\nexport function of(...args) {\n  const scheduler = popScheduler(args);\n  return from(args, scheduler);\n}", "map": {"version": 3, "names": ["popScheduler", "from", "of", "args", "scheduler"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/rxjs/dist/esm/internal/observable/of.js"], "sourcesContent": ["import { popScheduler } from '../util/args';\nimport { from } from './from';\nexport function of(...args) {\n    const scheduler = popScheduler(args);\n    return from(args, scheduler);\n}\n"], "mappings": "AAAA,SAASA,YAAT,QAA6B,cAA7B;AACA,SAASC,IAAT,QAAqB,QAArB;AACA,OAAO,SAASC,EAAT,CAAY,GAAGC,IAAf,EAAqB;EACxB,MAAMC,SAAS,GAAGJ,YAAY,CAACG,IAAD,CAA9B;EACA,OAAOF,IAAI,CAACE,IAAD,EAAOC,SAAP,CAAX;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}