const { query } = require('../database/db');

class ReadReceipt {
  static async markAsRead(messageId, userId) {
    const result = await query(
      'INSERT INTO read_receipts (message_id, user_id) VALUES ($1, $2) ON CONFLICT (message_id, user_id) DO UPDATE SET read_at = CURRENT_TIMESTAMP RETURNING *',
      [messageId, userId]
    );
    return result.rows[0];
  }

  static async getReadReceipts(messageId) {
    const result = await query(`
      SELECT 
        rr.id,
        rr.message_id,
        rr.user_id,
        rr.read_at,
        u.username
      FROM read_receipts rr
      JOIN users u ON rr.user_id = u.id
      WHERE rr.message_id = $1
      ORDER BY rr.read_at ASC
    `, [messageId]);
    
    return result.rows;
  }

  static async getReadReceiptsForMessages(messageIds) {
    if (!messageIds || messageIds.length === 0) {
      return {};
    }

    const result = await query(`
      SELECT 
        rr.message_id,
        rr.user_id,
        rr.read_at,
        u.username
      FROM read_receipts rr
      JOIN users u ON rr.user_id = u.id
      WHERE rr.message_id = ANY($1)
      ORDER BY rr.message_id, rr.read_at ASC
    `, [messageIds]);
    
    // Group by message_id
    const readReceipts = {};
    result.rows.forEach(row => {
      if (!readReceipts[row.message_id]) {
        readReceipts[row.message_id] = [];
      }
      readReceipts[row.message_id].push({
        userId: row.user_id,
        username: row.username,
        readAt: row.read_at
      });
    });
    
    return readReceipts;
  }

  static async markMessagesAsRead(messageIds, userId) {
    if (!messageIds || messageIds.length === 0) {
      return [];
    }

    const values = messageIds.map((messageId, index) => 
      `($${index * 2 + 1}, $${index * 2 + 2})`
    ).join(', ');
    
    const params = messageIds.flatMap(messageId => [messageId, userId]);
    
    const result = await query(`
      INSERT INTO read_receipts (message_id, user_id) 
      VALUES ${values}
      ON CONFLICT (message_id, user_id) 
      DO UPDATE SET read_at = CURRENT_TIMESTAMP 
      RETURNING *
    `, params);
    
    return result.rows;
  }

  static async getUnreadMessageCount(userId, groupId) {
    const result = await query(`
      SELECT COUNT(*) as unread_count
      FROM messages m
      LEFT JOIN read_receipts rr ON m.id = rr.message_id AND rr.user_id = $1
      WHERE m.group_id = $2 
        AND m.user_id != $1 
        AND rr.id IS NULL
    `, [userId, groupId]);
    
    return parseInt(result.rows[0].unread_count) || 0;
  }

  static async deleteReadReceipts(messageId) {
    const result = await query(
      'DELETE FROM read_receipts WHERE message_id = $1 RETURNING *',
      [messageId]
    );
    return result.rows;
  }
}

module.exports = ReadReceipt;
