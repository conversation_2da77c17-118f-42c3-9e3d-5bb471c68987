{"ast": null, "code": "import _asyncToGenerator from \"R:/chateye/FrontendAngular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Observable } from 'rxjs';\nimport { take } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./services/chat.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/toolbar\";\nimport * as i4 from \"@angular/material/sidenav\";\nimport * as i5 from \"@angular/material/card\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/chips\";\nimport * as i9 from \"@angular/material/tooltip\";\nimport * as i10 from \"./components/login-form/login-form.component\";\nimport * as i11 from \"./components/sidebar/sidebar.component\";\nimport * as i12 from \"./components/message-list/message-list.component\";\nimport * as i13 from \"./components/message-input/message-input.component\";\nimport * as i14 from \"./components/admin-panel/admin-panel.component\";\nimport * as i15 from \"./components/password-change/password-change.component\";\nimport * as i16 from \"./components/message-edit/message-edit.component\";\n\nfunction AppComponent_app_login_form_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-login-form\");\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_2_p_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 28);\n    i0.ɵɵtext(1, \" Please select a group to start chatting \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_2_p_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (tmp_0_0 = i0.ɵɵpipeBind1(2, 1, ctx_r7.currentGroup$)) == null ? null : tmp_0_0.description, \" \");\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_2_button_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function AppComponent_mat_sidenav_container_2_button_36_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.onShowAdminPanel());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"admin_panel_settings\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_2_app_message_input_44_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"app-message-input\", 31);\n    i0.ɵɵlistener(\"onSendMessage\", function AppComponent_mat_sidenav_container_2_app_message_input_44_Template_app_message_input_onSendMessage_0_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r13.onSendMessage($event));\n    })(\"onCancelReply\", function AppComponent_mat_sidenav_container_2_app_message_input_44_Template_app_message_input_onCancelReply_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.onCancelReply());\n    });\n    i0.ɵɵpipe(1, \"async\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"replyTo\", i0.ɵɵpipeBind1(1, 1, ctx_r9.replyTo$));\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_2_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"mat-card\")(2, \"mat-card-content\")(3, \"p\");\n    i0.ɵɵtext(4, \"Select a group to start chatting\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\n\nconst _c0 = function () {\n  return [];\n};\n\nfunction AppComponent_mat_sidenav_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"mat-sidenav-container\", 5)(1, \"mat-sidenav\", 6, 7)(3, \"app-sidebar\", 8);\n    i0.ɵɵlistener(\"onPasswordChange\", function AppComponent_mat_sidenav_container_2_Template_app_sidebar_onPasswordChange_3_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.onPasswordChange());\n    })(\"onLogout\", function AppComponent_mat_sidenav_container_2_Template_app_sidebar_onLogout_3_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.onLogout());\n    });\n    i0.ɵɵpipe(4, \"async\");\n    i0.ɵɵpipe(5, \"async\");\n    i0.ɵɵpipe(6, \"async\");\n    i0.ɵɵpipe(7, \"async\");\n    i0.ɵɵpipe(8, \"async\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"mat-sidenav-content\", 9)(10, \"mat-toolbar\", 10)(11, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function AppComponent_mat_sidenav_container_2_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r17);\n\n      const _r5 = i0.ɵɵreference(2);\n\n      return i0.ɵɵresetView(_r5.toggle());\n    });\n    i0.ɵɵelementStart(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"menu\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 12)(15, \"div\", 13)(16, \"h1\", 14);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"async\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, AppComponent_mat_sidenav_container_2_p_19_Template, 2, 0, \"p\", 15);\n    i0.ɵɵpipe(20, \"async\");\n    i0.ɵɵtemplate(21, AppComponent_mat_sidenav_container_2_p_21_Template, 3, 3, \"p\", 16);\n    i0.ɵɵpipe(22, \"async\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 17)(24, \"div\", 18)(25, \"mat-icon\", 19);\n    i0.ɵɵpipe(26, \"async\");\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"async\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"span\", 20);\n    i0.ɵɵtext(30);\n    i0.ɵɵpipe(31, \"async\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"mat-chip-list\")(33, \"mat-chip\", 21);\n    i0.ɵɵtext(34);\n    i0.ɵɵpipe(35, \"async\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(36, AppComponent_mat_sidenav_container_2_button_36_Template, 3, 0, \"button\", 22);\n    i0.ɵɵpipe(37, \"async\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(38, \"div\", 23)(39, \"app-message-list\", 24);\n    i0.ɵɵlistener(\"onReply\", function AppComponent_mat_sidenav_container_2_Template_app_message_list_onReply_39_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.onReply($event));\n    })(\"onAddReaction\", function AppComponent_mat_sidenav_container_2_Template_app_message_list_onAddReaction_39_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.onAddReaction($event));\n    })(\"onRemoveReaction\", function AppComponent_mat_sidenav_container_2_Template_app_message_list_onRemoveReaction_39_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.onRemoveReaction($event));\n    })(\"onEdit\", function AppComponent_mat_sidenav_container_2_Template_app_message_list_onEdit_39_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.onEdit($event));\n    })(\"onDelete\", function AppComponent_mat_sidenav_container_2_Template_app_message_list_onDelete_39_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.onDelete($event));\n    })(\"onMessageClick\", function AppComponent_mat_sidenav_container_2_Template_app_message_list_onMessageClick_39_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.onMessageClick($event));\n    });\n    i0.ɵɵpipe(40, \"async\");\n    i0.ɵɵpipe(41, \"async\");\n    i0.ɵɵpipe(42, \"async\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"div\", 25);\n    i0.ɵɵtemplate(44, AppComponent_mat_sidenav_container_2_app_message_input_44_Template, 2, 3, \"app-message-input\", 26);\n    i0.ɵɵpipe(45, \"async\");\n    i0.ɵɵtemplate(46, AppComponent_mat_sidenav_container_2_div_46_Template, 5, 0, \"div\", 27);\n    i0.ɵɵpipe(47, \"async\");\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_6_0;\n    let tmp_8_0;\n    let tmp_12_0;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"onlineUsers\", i0.ɵɵpipeBind1(4, 21, ctx_r1.onlineUsers$) || i0.ɵɵpureFunction0(57, _c0))(\"currentUser\", i0.ɵɵpipeBind1(5, 23, ctx_r1.user$))(\"isAdmin\", i0.ɵɵpipeBind1(6, 25, ctx_r1.isAdmin$) || false)(\"groups\", i0.ɵɵpipeBind1(7, 27, ctx_r1.groups$) || i0.ɵɵpureFunction0(58, _c0))(\"currentGroup\", i0.ɵɵpipeBind1(8, 29, ctx_r1.currentGroup$))(\"onJoinGroup\", ctx_r1.onJoinGroup.bind(ctx_r1));\n    i0.ɵɵadvance(14);\n    i0.ɵɵtextInterpolate1(\" \", ((tmp_6_0 = i0.ɵɵpipeBind1(18, 31, ctx_r1.currentGroup$)) == null ? null : tmp_6_0.name) || \"Select a Group\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !i0.ɵɵpipeBind1(20, 33, ctx_r1.currentGroup$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (tmp_8_0 = i0.ɵɵpipeBind1(22, 35, ctx_r1.currentGroup$)) == null ? null : tmp_8_0.description);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"connected\", i0.ɵɵpipeBind1(26, 37, ctx_r1.connected$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(28, 39, ctx_r1.connected$) ? \"wifi\" : \"wifi_off\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(31, 41, ctx_r1.connected$) ? \"Connected\" : \"Disconnected\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ((tmp_12_0 = i0.ɵɵpipeBind1(35, 43, ctx_r1.onlineUsers$)) == null ? null : tmp_12_0.length) || 0, \" online \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(37, 45, ctx_r1.isAdmin$));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"messages\", i0.ɵɵpipeBind1(40, 47, ctx_r1.messages$) || i0.ɵɵpureFunction0(59, _c0))(\"currentUser\", i0.ɵɵpipeBind1(41, 49, ctx_r1.user$))(\"loading\", i0.ɵɵpipeBind1(42, 51, ctx_r1.loading$) || false)(\"highlightedMessageId\", ctx_r1.highlightedMessageId);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(45, 53, ctx_r1.currentGroup$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !i0.ɵɵpipeBind1(47, 55, ctx_r1.currentGroup$));\n  }\n}\n\nfunction AppComponent_app_admin_panel_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"app-admin-panel\", 33);\n    i0.ɵɵlistener(\"onClose\", function AppComponent_app_admin_panel_4_Template_app_admin_panel_onClose_0_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.onHideAdminPanel());\n    });\n    i0.ɵɵpipe(1, \"async\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"currentUser\", i0.ɵɵpipeBind1(1, 1, ctx_r2.user$));\n  }\n}\n\nfunction AppComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"mat-card\", 35)(2, \"mat-card-header\")(3, \"mat-card-title\");\n    i0.ɵɵtext(4, \"Change Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function AppComponent_div_6_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.onClosePasswordChange());\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"close\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"mat-card-content\");\n    i0.ɵɵelement(9, \"app-password-change\");\n    i0.ɵɵelementEnd()()();\n  }\n}\n\nfunction AppComponent_app_message_edit_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"app-message-edit\", 37);\n    i0.ɵɵlistener(\"onSave\", function AppComponent_app_message_edit_7_Template_app_message_edit_onSave_0_listener($event) {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.onSaveEdit($event));\n    })(\"onCancel\", function AppComponent_app_message_edit_7_Template_app_message_edit_onCancel_0_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r33 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r33.onCancelEdit());\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const editingMessage_r30 = ctx.ngIf;\n    i0.ɵɵproperty(\"message\", editingMessage_r30);\n  }\n}\n\nexport class AppComponent {\n  constructor(chatService) {\n    this.chatService = chatService;\n    this.showPasswordChange = false;\n    this.highlightedMessageId = null;\n    this.user$ = this.chatService.user$;\n    this.messages$ = this.chatService.messages$;\n    this.onlineUsers$ = this.chatService.onlineUsers$;\n    this.groups$ = this.chatService.groups$;\n    this.currentGroup$ = this.chatService.currentGroup$;\n    this.replyTo$ = this.chatService.replyTo$;\n    this.loading$ = this.chatService.loading$;\n    this.isAdmin$ = this.chatService.isAdmin$;\n    this.showAdminPanel$ = this.chatService.showAdminPanel$;\n    this.connected$ = this.chatService.connected$;\n    this.isLoggedIn$ = this.chatService.isLoggedIn$;\n    this.editingMessage$ = this.chatService.editingMessage$;\n  }\n\n  ngOnInit() {// Component initialization\n  }\n\n  ngOnDestroy() {\n    this.chatService.logout();\n  }\n\n  onJoinGroup(groupId) {\n    this.chatService.joinGroup(groupId);\n  }\n\n  onSendMessage(event) {\n    this.chatService.sendMessage(event.text, event.replyToId);\n  }\n\n  onReply(message) {\n    this.chatService.replyToMessage(message);\n  }\n\n  onCancelReply() {\n    this.chatService.cancelReply();\n  }\n\n  onAddReaction(event) {\n    this.chatService.addReaction(event.messageId, event.emoji);\n  }\n\n  onRemoveReaction(data) {\n    this.chatService.removeReaction(data);\n  }\n\n  onEdit(message) {\n    this.chatService.startEditingMessage(message);\n  }\n\n  onSaveEdit(newText) {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        const editingMessage = yield _this.editingMessage$.pipe(take(1)).toPromise();\n\n        if (editingMessage) {\n          yield _this.chatService.updateMessage(editingMessage.id, newText);\n\n          _this.chatService.cancelEditingMessage();\n        }\n      } catch (error) {\n        console.error('Failed to edit message:', error);\n        alert('Failed to edit message. Please try again.');\n      }\n    })();\n  }\n\n  onCancelEdit() {\n    this.chatService.cancelEditingMessage();\n  }\n\n  onDelete(message) {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        if (confirm('Are you sure you want to delete this message?')) {\n          yield _this2.chatService.deleteMessage(message.id);\n        }\n      } catch (error) {\n        console.error('Failed to delete message:', error);\n        alert('Failed to delete message. Please try again.');\n      }\n    })();\n  }\n\n  onShowAdminPanel() {\n    this.chatService.showAdminPanel();\n  }\n\n  onHideAdminPanel() {\n    this.chatService.hideAdminPanel();\n  }\n\n  onPasswordChange() {\n    this.showPasswordChange = true;\n  }\n\n  onClosePasswordChange() {\n    this.showPasswordChange = false;\n  }\n\n  onLogout() {\n    this.chatService.logout();\n  }\n\n  onMessageClick(message) {\n    this.highlightedMessageId = message.id; // Clear highlight after 3 seconds\n\n    setTimeout(() => {\n      this.highlightedMessageId = null;\n    }, 3000);\n  }\n\n  onReply(message) {\n    this.chatService.replyToMessage(message); // Highlight the replied-to message\n\n    this.highlightedMessageId = message.id; // Clear highlight after 3 seconds\n\n    setTimeout(() => {\n      this.highlightedMessageId = null;\n    }, 3000);\n  }\n\n}\n\nAppComponent.ɵfac = function AppComponent_Factory(t) {\n  return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.ChatService));\n};\n\nAppComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: AppComponent,\n  selectors: [[\"app-root\"]],\n  decls: 9,\n  vars: 13,\n  consts: [[4, \"ngIf\"], [\"class\", \"chat-container\", 4, \"ngIf\"], [3, \"currentUser\", \"onClose\", 4, \"ngIf\"], [\"class\", \"modal-overlay\", 4, \"ngIf\"], [3, \"message\", \"onSave\", \"onCancel\", 4, \"ngIf\"], [1, \"chat-container\"], [\"mode\", \"side\", \"opened\", \"true\", 1, \"chat-sidebar\"], [\"sidenav\", \"\"], [3, \"onlineUsers\", \"currentUser\", \"isAdmin\", \"groups\", \"currentGroup\", \"onJoinGroup\", \"onPasswordChange\", \"onLogout\"], [1, \"chat-main-content\"], [1, \"chat-header\"], [\"mat-icon-button\", \"\", 1, \"menu-button\", 3, \"click\"], [1, \"header-content\"], [1, \"group-info\"], [1, \"group-name\"], [\"class\", \"no-group-message\", 4, \"ngIf\"], [\"class\", \"group-description\", 4, \"ngIf\"], [1, \"header-actions\"], [1, \"connection-status\"], [1, \"status-icon\"], [1, \"status-text\"], [1, \"online-count\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Admin Panel\", 3, \"click\", 4, \"ngIf\"], [1, \"messages-container\"], [3, \"messages\", \"currentUser\", \"loading\", \"highlightedMessageId\", \"onReply\", \"onAddReaction\", \"onRemoveReaction\", \"onEdit\", \"onDelete\", \"onMessageClick\"], [1, \"input-container\"], [3, \"replyTo\", \"onSendMessage\", \"onCancelReply\", 4, \"ngIf\"], [\"class\", \"no-group-input\", 4, \"ngIf\"], [1, \"no-group-message\"], [1, \"group-description\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Admin Panel\", 3, \"click\"], [3, \"replyTo\", \"onSendMessage\", \"onCancelReply\"], [1, \"no-group-input\"], [3, \"currentUser\", \"onClose\"], [1, \"modal-overlay\"], [1, \"modal-card\"], [\"mat-icon-button\", \"\", 1, \"close-button\", 3, \"click\"], [3, \"message\", \"onSave\", \"onCancel\"]],\n  template: function AppComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, AppComponent_app_login_form_0_Template, 1, 0, \"app-login-form\", 0);\n      i0.ɵɵpipe(1, \"async\");\n      i0.ɵɵtemplate(2, AppComponent_mat_sidenav_container_2_Template, 48, 60, \"mat-sidenav-container\", 1);\n      i0.ɵɵpipe(3, \"async\");\n      i0.ɵɵtemplate(4, AppComponent_app_admin_panel_4_Template, 2, 3, \"app-admin-panel\", 2);\n      i0.ɵɵpipe(5, \"async\");\n      i0.ɵɵtemplate(6, AppComponent_div_6_Template, 10, 0, \"div\", 3);\n      i0.ɵɵtemplate(7, AppComponent_app_message_edit_7_Template, 1, 1, \"app-message-edit\", 4);\n      i0.ɵɵpipe(8, \"async\");\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", !i0.ɵɵpipeBind1(1, 5, ctx.isLoggedIn$));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(3, 7, ctx.isLoggedIn$));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(5, 9, ctx.showAdminPanel$));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.showPasswordChange);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(8, 11, ctx.editingMessage$));\n    }\n  },\n  dependencies: [i2.NgIf, i3.MatToolbar, i4.MatSidenav, i4.MatSidenavContainer, i4.MatSidenavContent, i5.MatCard, i5.MatCardHeader, i5.MatCardContent, i5.MatCardTitle, i6.MatButton, i7.MatIcon, i8.MatChipList, i8.MatChip, i9.MatTooltip, i10.LoginFormComponent, i11.SidebarComponent, i12.MessageListComponent, i13.MessageInputComponent, i14.AdminPanelComponent, i15.PasswordChangeComponent, i16.MessageEditComponent, i2.AsyncPipe],\n  styles: [\".chat-container[_ngcontent-%COMP%] {\\r\\n  height: 100vh;\\r\\n  width: 100vw;\\r\\n  background: #f5f5f5;\\r\\n}\\r\\n.chat-sidebar[_ngcontent-%COMP%] {\\r\\n  width: 280px;\\r\\n  min-width: 280px;\\r\\n  background: white;\\r\\n  border-right: 1px solid #e0e0e0;\\r\\n  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);\\r\\n}\\r\\n.chat-main-content[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  height: 100vh;\\r\\n  background: white;\\r\\n}\\r\\n.chat-header[_ngcontent-%COMP%] {\\r\\n  background: linear-gradient(135deg, #3f51b5 0%, #5c6bc0 100%);\\r\\n  color: white;\\r\\n  padding: 0 16px;\\r\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\r\\n  z-index: 10;\\r\\n}\\r\\n.menu-button[_ngcontent-%COMP%] {\\r\\n  margin-right: 16px;\\r\\n}\\r\\n.header-content[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: space-between;\\r\\n  align-items: center;\\r\\n  flex: 1;\\r\\n  min-width: 0;\\r\\n}\\r\\n.group-info[_ngcontent-%COMP%] {\\r\\n  flex: 1;\\r\\n  min-width: 0;\\r\\n  margin-right: 16px;\\r\\n}\\r\\n.group-name[_ngcontent-%COMP%] {\\r\\n  font-size: 1.25rem;\\r\\n  font-weight: 500;\\r\\n  margin: 0 0 4px 0;\\r\\n  line-height: 1.2;\\r\\n}\\r\\n.no-group-message[_ngcontent-%COMP%] {\\r\\n  color: rgba(255, 255, 255, 0.8);\\r\\n  font-size: 0.875rem;\\r\\n  margin: 0;\\r\\n  font-weight: 400;\\r\\n}\\r\\n.group-description[_ngcontent-%COMP%] {\\r\\n  color: rgba(255, 255, 255, 0.9);\\r\\n  font-size: 0.75rem;\\r\\n  margin: 0;\\r\\n  opacity: 0.9;\\r\\n}\\r\\n.header-actions[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 16px;\\r\\n  flex-shrink: 0;\\r\\n}\\r\\n.connection-status[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 8px;\\r\\n  background: rgba(255, 255, 255, 0.1);\\r\\n  padding: 8px 12px;\\r\\n  border-radius: 16px;\\r\\n  backdrop-filter: blur(10px);\\r\\n}\\r\\n.status-icon[_ngcontent-%COMP%] {\\r\\n  font-size: 18px;\\r\\n  width: 18px;\\r\\n  height: 18px;\\r\\n  color: #ef4444;\\r\\n  transition: all 0.3s ease;\\r\\n}\\r\\n.status-icon.connected[_ngcontent-%COMP%] {\\r\\n  color: #10b981;\\r\\n}\\r\\n.status-text[_ngcontent-%COMP%] {\\r\\n  font-size: 0.75rem;\\r\\n  font-weight: 500;\\r\\n}\\r\\n.online-count[_ngcontent-%COMP%] {\\r\\n  background: rgba(255, 255, 255, 0.1);\\r\\n  color: white;\\r\\n  font-size: 0.75rem;\\r\\n  font-weight: 500;\\r\\n}\\r\\n.messages-container[_ngcontent-%COMP%] {\\r\\n  flex: 1;\\r\\n  min-height: 0;\\r\\n  background: #f8f9fa;\\r\\n  overflow: hidden;\\r\\n  position: relative;\\r\\n}\\r\\n.input-container[_ngcontent-%COMP%] {\\r\\n  background: white;\\r\\n  border-top: 1px solid #e0e0e0;\\r\\n  padding: 16px;\\r\\n}\\r\\n.no-group-input[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  height: 60px;\\r\\n}\\r\\n.modal-overlay[_ngcontent-%COMP%] {\\r\\n  position: fixed;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  right: 0;\\r\\n  bottom: 0;\\r\\n  background: rgba(0, 0, 0, 0.5);\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  z-index: 1000;\\r\\n}\\r\\n.modal-card[_ngcontent-%COMP%] {\\r\\n  width: 90%;\\r\\n  max-width: 500px;\\r\\n  max-height: 90vh;\\r\\n  overflow-y: auto;\\r\\n}\\r\\n.close-button[_ngcontent-%COMP%] {\\r\\n  position: absolute;\\r\\n  top: 8px;\\r\\n  right: 8px;\\r\\n}\\r\\n\\r\\n@media (max-width: 1024px) {\\r\\n  .chat-sidebar[_ngcontent-%COMP%] {\\r\\n    width: 260px;\\r\\n    min-width: 260px;\\r\\n  }\\r\\n  \\r\\n  .group-name[_ngcontent-%COMP%] {\\r\\n    font-size: 1.125rem;\\r\\n  }\\r\\n  \\r\\n  .header-actions[_ngcontent-%COMP%] {\\r\\n    gap: 12px;\\r\\n  }\\r\\n}\\r\\n@media (max-width: 768px) {\\r\\n  .chat-sidebar[_ngcontent-%COMP%] {\\r\\n    width: 100%;\\r\\n    min-width: 100%;\\r\\n  }\\r\\n  \\r\\n  .chat-main-content[_ngcontent-%COMP%] {\\r\\n    height: calc(100vh - 60px);\\r\\n  }\\r\\n  \\r\\n  .group-name[_ngcontent-%COMP%] {\\r\\n    font-size: 1rem;\\r\\n  }\\r\\n  \\r\\n  .header-actions[_ngcontent-%COMP%] {\\r\\n    gap: 8px;\\r\\n  }\\r\\n  \\r\\n  .connection-status[_ngcontent-%COMP%] {\\r\\n    padding: 6px 10px;\\r\\n  }\\r\\n  \\r\\n  .status-text[_ngcontent-%COMP%] {\\r\\n    font-size: 0.7rem;\\r\\n  }\\r\\n  \\r\\n  .online-count[_ngcontent-%COMP%] {\\r\\n    font-size: 0.7rem;\\r\\n  }\\r\\n}\\r\\n@media (max-width: 480px) {\\r\\n  .header-content[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n    align-items: flex-start;\\r\\n    gap: 8px;\\r\\n  }\\r\\n  \\r\\n  .header-actions[_ngcontent-%COMP%] {\\r\\n    width: 100%;\\r\\n    justify-content: space-between;\\r\\n  }\\r\\n  \\r\\n  .group-name[_ngcontent-%COMP%] {\\r\\n    font-size: 0.9rem;\\r\\n  }\\r\\n  \\r\\n  .chat-header[_ngcontent-%COMP%] {\\r\\n    padding: 0 8px;\\r\\n  }\\r\\n  \\r\\n  .input-container[_ngcontent-%COMP%] {\\r\\n    padding: 12px;\\r\\n  }\\r\\n}\\r\\n\\r\\n*[_ngcontent-%COMP%] {\\r\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\r\\n}\\r\\n\\r\\n[_ngcontent-%COMP%]::-webkit-scrollbar {\\r\\n  width: 6px;\\r\\n}\\r\\n[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\r\\n  background: transparent;\\r\\n}\\r\\n[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\r\\n  background: rgba(0, 0, 0, 0.2);\\r\\n  border-radius: 3px;\\r\\n}\\r\\n[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\r\\n  background: rgba(0, 0, 0, 0.3);\\r\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\"]\n});", "map": {"version": 3, "mappings": ";AACA,SAASA,UAAT,QAA0C,MAA1C;AACA,SAAcC,IAAd,QAA0B,gBAA1B;;;;;;;;;;;;;;;;;;;;;ICDAC;;;;;;IA+BUA;IACEA;IACFA;;;;;;IACAA;IACEA;;IACFA;;;;;;IADEA;IAAAA;;;;;;;;IAoBFA;IAEEA;MAAAA;MAAA;MAAA,OAASA,0CAAT;IAA2B,CAA3B;IAIAA;IAAUA;IAAoBA;;;;;;;;IAwBpCA;IAGEA;MAAAA;MAAA;MAAA,OAAiBA,6CAAjB;IAAsC,CAAtC,EAAuC,eAAvC,EAAuC;MAAAA;MAAA;MAAA,OACtBA,uCADsB;IACP,CADhC;;IAEDA;;;;;IAHCA;;;;;;IAKFA,gCAA6D,CAA7D,EAA6D,UAA7D,EAA6D,CAA7D,EAA6D,kBAA7D,EAA6D,CAA7D,EAA6D,GAA7D;IAGSA;IAAgCA;;;;;;;;;;;;IA5F/CA,iDAA0E,CAA1E,EAA0E,aAA1E,EAA0E,CAA1E,EAA0E,CAA1E,EAA0E,CAA1E,EAA0E,aAA1E,EAA0E,CAA1E;IAUMA;MAAAA;MAAA;MAAA,OAAoBA,0CAApB;IAAsC,CAAtC,EAAuC,UAAvC,EAAuC;MAAAA;MAAA;MAAA,OAC3BA,kCAD2B;IACjB,CADtB;;;;;;IAEDA;IAIHA,+CAA+C,EAA/C,EAA+C,aAA/C,EAA+C,EAA/C,EAA+C,EAA/C,EAA+C,QAA/C,EAA+C,EAA/C;IAG4BA;MAAAA;;MAAA;;MAAA,OAASA,4BAAT;IAAyB,CAAzB;IACtBA;IAAUA;IAAIA;IAGhBA,iCAA4B,EAA5B,EAA4B,KAA5B,EAA4B,EAA5B,EAA4B,EAA5B,EAA4B,IAA5B,EAA4B,EAA5B;IAGMA;;IACFA;IACAA;;IAGAA;;IAGFA;IAEAA,iCAA4B,EAA5B,EAA4B,KAA5B,EAA4B,EAA5B,EAA4B,EAA5B,EAA4B,UAA5B,EAA4B,EAA5B;;IAGMA;;IACFA;IACAA;IACEA;;IACFA;IAGFA,uCAAe,EAAf,EAAe,UAAf,EAAe,EAAf;IAEIA;;IACFA;IAGFA;;IAQFA;IAKJA,iCAAgC,EAAhC,EAAgC,kBAAhC,EAAgC,EAAhC;IAMIA;MAAAA;MAAA;MAAA,OAAWA,uCAAX;IAA0B,CAA1B,EAA2B,eAA3B,EAA2B;MAAAA;MAAA;MAAA,OACVA,6CADU;IACW,CADtC,EAA2B,kBAA3B,EAA2B;MAAAA;MAAA;MAAA,OAEPA,gDAFO;IAEiB,CAF5C,EAA2B,QAA3B,EAA2B;MAAAA;MAAA;MAAA,OAGjBA,sCAHiB;IAGH,CAHxB,EAA2B,UAA3B,EAA2B;MAAAA;MAAA;MAAA,OAIfA,wCAJe;IAIC,CAJ5B,EAA2B,gBAA3B,EAA2B;MAAAA;MAAA;MAAA,OAKTA,8CALS;IAKa,CALxC;;;;IAMDA;IAIHA;IACEA;;IAOAA;;IAOFA;;;;;;;;IA5FEA;IAAAA,wGAA4C,aAA5C,EAA4CA,mCAA5C,EAA4C,SAA5C,EAA4CA,+CAA5C,EAA4C,QAA5C,EAA4CA,oEAA5C,EAA4C,cAA5C,EAA4CA,2CAA5C,EAA4C,aAA5C,EAA4CC,+BAA5C;IAsBMD;IAAAA;IAEEA;IAAAA;IAGAA;IAAAA;IAOQA;IAAAA;IACRA;IAAAA;IAGAA;IAAAA;IAMAA;IAAAA;IAKDA;IAAAA;IAcLA;IAAAA,mGAAsC,aAAtC,EAAsCA,oCAAtC,EAAsC,SAAtC,EAAsCA,gDAAtC,EAAsC,sBAAtC,EAAsCC,2BAAtC;IAgBCD;IAAAA;IAMGA;IAAAA;;;;;;;;IAYZA;IAGEA;MAAAA;MAAA;MAAA,OAAWA,0CAAX;IAA6B,CAA7B;;IACDA;;;;;IAFCA;;;;;;;;IAKFA,gCAAsD,CAAtD,EAAsD,UAAtD,EAAsD,EAAtD,EAAsD,CAAtD,EAAsD,iBAAtD,EAAsD,CAAtD,EAAsD,gBAAtD;IAGsBA;IAAeA;IAC/BA;IACEA;MAAAA;MAAA;MAAA,OAASA,+CAAT;IAAgC,CAAhC;IAIAA;IAAUA;IAAKA;IAGnBA;IACEA;IACFA;;;;;;;;IAKJA;IAGEA;MAAAA;MAAA;MAAA,OAAUA,0CAAV;IAA4B,CAA5B,EAA6B,UAA7B,EAA6B;MAAAA;MAAA;MAAA,OACjBA,sCADiB;IACH,CAD1B;IAEDA;;;;;IAHCA;;;;AD1HF,OAAM,MAAOE,YAAP,CAAmB;EAgBvBC,YAAoBC,WAApB,EAA4C;IAAxB;IAJpB,0BAAqB,KAArB;IAEA,4BAAsC,IAAtC;IAGE,KAAKC,KAAL,GAAa,KAAKD,WAAL,CAAiBC,KAA9B;IACA,KAAKC,SAAL,GAAiB,KAAKF,WAAL,CAAiBE,SAAlC;IACA,KAAKC,YAAL,GAAoB,KAAKH,WAAL,CAAiBG,YAArC;IACA,KAAKC,OAAL,GAAe,KAAKJ,WAAL,CAAiBI,OAAhC;IACA,KAAKC,aAAL,GAAqB,KAAKL,WAAL,CAAiBK,aAAtC;IACA,KAAKC,QAAL,GAAgB,KAAKN,WAAL,CAAiBM,QAAjC;IACA,KAAKC,QAAL,GAAgB,KAAKP,WAAL,CAAiBO,QAAjC;IACA,KAAKC,QAAL,GAAgB,KAAKR,WAAL,CAAiBQ,QAAjC;IACA,KAAKC,eAAL,GAAuB,KAAKT,WAAL,CAAiBS,eAAxC;IACA,KAAKC,UAAL,GAAkB,KAAKV,WAAL,CAAiBU,UAAnC;IACA,KAAKC,WAAL,GAAmB,KAAKX,WAAL,CAAiBW,WAApC;IACA,KAAKC,eAAL,GAAuB,KAAKZ,WAAL,CAAiBY,eAAxC;EACD;;EAEDC,QAAQ,IACN;EACD;;EAEDC,WAAW;IACT,KAAKd,WAAL,CAAiBe,MAAjB;EACD;;EAEDC,WAAW,CAACC,OAAD,EAAgB;IACzB,KAAKjB,WAAL,CAAiBkB,SAAjB,CAA2BD,OAA3B;EACD;;EAEDE,aAAa,CAACC,KAAD,EAAkD;IAC7D,KAAKpB,WAAL,CAAiBqB,WAAjB,CAA6BD,KAAK,CAACE,IAAnC,EAAyCF,KAAK,CAACG,SAA/C;EACD;;EAEDC,OAAO,CAACC,OAAD,EAAiB;IACtB,KAAKzB,WAAL,CAAiB0B,cAAjB,CAAgCD,OAAhC;EACD;;EAEDE,aAAa;IACX,KAAK3B,WAAL,CAAiB4B,WAAjB;EACD;;EAEDC,aAAa,CAACT,KAAD,EAA4C;IACvD,KAAKpB,WAAL,CAAiB8B,WAAjB,CAA6BV,KAAK,CAACW,SAAnC,EAA8CX,KAAK,CAACY,KAApD;EACD;;EAEDC,gBAAgB,CAACC,IAAD,EAA2C;IACzD,KAAKlC,WAAL,CAAiBmC,cAAjB,CAAgCD,IAAhC;EACD;;EAEDE,MAAM,CAACX,OAAD,EAAiB;IACrB,KAAKzB,WAAL,CAAiBqC,mBAAjB,CAAqCZ,OAArC;EACD;;EAEKa,UAAU,CAACC,OAAD,EAAgB;IAAA;;IAAA;MAC9B,IAAI;QACF,MAAMC,cAAc,SAAS,KAAI,CAAC5B,eAAL,CAAqB6B,IAArB,CAA0B9C,IAAI,CAAC,CAAD,CAA9B,EAAmC+C,SAAnC,EAA7B;;QACA,IAAIF,cAAJ,EAAoB;UAClB,MAAM,KAAI,CAACxC,WAAL,CAAiB2C,aAAjB,CAA+BH,cAAc,CAACI,EAA9C,EAAkDL,OAAlD,CAAN;;UACA,KAAI,CAACvC,WAAL,CAAiB6C,oBAAjB;QACD;MACF,CAND,CAME,OAAOC,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,yBAAd,EAAyCA,KAAzC;QACAE,KAAK,CAAC,2CAAD,CAAL;MACD;IAV6B;EAW/B;;EAEDC,YAAY;IACV,KAAKjD,WAAL,CAAiB6C,oBAAjB;EACD;;EAEKK,QAAQ,CAACzB,OAAD,EAAiB;IAAA;;IAAA;MAC7B,IAAI;QACF,IAAI0B,OAAO,CAAC,+CAAD,CAAX,EAA8D;UAC5D,MAAM,MAAI,CAACnD,WAAL,CAAiBoD,aAAjB,CAA+B3B,OAAO,CAACmB,EAAvC,CAAN;QACD;MACF,CAJD,CAIE,OAAOE,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,2BAAd,EAA2CA,KAA3C;QACAE,KAAK,CAAC,6CAAD,CAAL;MACD;IAR4B;EAS9B;;EAEDK,gBAAgB;IACd,KAAKrD,WAAL,CAAiBsD,cAAjB;EACD;;EAEDC,gBAAgB;IACd,KAAKvD,WAAL,CAAiBwD,cAAjB;EACD;;EAEDC,gBAAgB;IACd,KAAKC,kBAAL,GAA0B,IAA1B;EACD;;EAEDC,qBAAqB;IACnB,KAAKD,kBAAL,GAA0B,KAA1B;EACD;;EAEDE,QAAQ;IACN,KAAK5D,WAAL,CAAiBe,MAAjB;EACD;;EAED8C,cAAc,CAACpC,OAAD,EAAiB;IAC7B,KAAKqC,oBAAL,GAA4BrC,OAAO,CAACmB,EAApC,CAD6B,CAG7B;;IACAmB,UAAU,CAAC,MAAK;MACd,KAAKD,oBAAL,GAA4B,IAA5B;IACD,CAFS,EAEP,IAFO,CAAV;EAGD;;EAEDtC,OAAO,CAACC,OAAD,EAAiB;IACtB,KAAKzB,WAAL,CAAiB0B,cAAjB,CAAgCD,OAAhC,EADsB,CAGtB;;IACA,KAAKqC,oBAAL,GAA4BrC,OAAO,CAACmB,EAApC,CAJsB,CAMtB;;IACAmB,UAAU,CAAC,MAAK;MACd,KAAKD,oBAAL,GAA4B,IAA5B;IACD,CAFS,EAEP,IAFO,CAAV;EAGD;;AAtIsB;;;mBAAZhE,cAAYF;AAAA;;;QAAZE;EAAYkE;EAAAC;EAAAC;EAAAC;EAAAC;IAAA;MCVzBxE;;MAGAA;;MAqGAA;;MAOAA;MAmBAA;;;;;MAlIiBA;MAGOA;MAAAA;MAsGrBA;MAAAA;MAMGA;MAAAA;MAoBHA;MAAAA", "names": ["Observable", "take", "i0", "ctx_r1", "AppComponent", "constructor", "chatService", "user$", "messages$", "onlineUsers$", "groups$", "currentGroup$", "replyTo$", "loading$", "isAdmin$", "showAdminPanel$", "connected$", "isLoggedIn$", "editingMessage$", "ngOnInit", "ngOnDestroy", "logout", "onJoinGroup", "groupId", "joinGroup", "onSendMessage", "event", "sendMessage", "text", "replyToId", "onReply", "message", "replyToMessage", "onCancelReply", "cancelReply", "onAddReaction", "addReaction", "messageId", "emoji", "onRemoveReaction", "data", "removeReaction", "onEdit", "startEditingMessage", "onSaveEdit", "newText", "editingMessage", "pipe", "to<PERSON>romise", "updateMessage", "id", "cancelEditingMessage", "error", "console", "alert", "onCancelEdit", "onDelete", "confirm", "deleteMessage", "onShowAdminPanel", "showAdminPanel", "onHideAdminPanel", "hideAdminPanel", "onPasswordChange", "showPasswordChange", "onClosePasswordChange", "onLogout", "onMessageClick", "highlightedMessageId", "setTimeout", "selectors", "decls", "vars", "consts", "template"], "sourceRoot": "", "sources": ["R:\\chateye\\FrontendAngular\\src\\app\\app.component.ts", "R:\\chateye\\FrontendAngular\\src\\app\\app.component.html"], "sourcesContent": ["import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';\nimport { Observable, combineLatest } from 'rxjs';\nimport { map, take } from 'rxjs/operators';\nimport { ChatService } from './services/chat.service';\nimport { Message, Group, User } from './services/api.service';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.css']\n})\nexport class AppComponent implements OnInit, OnDestroy {\n  user$: Observable<string | null>;\n  messages$: Observable<Message[]>;\n  onlineUsers$: Observable<User[]>;\n  groups$: Observable<Group[]>;\n  currentGroup$: Observable<Group | null>;\n  replyTo$: Observable<Message | null>;\n  loading$: Observable<boolean>;\n  isAdmin$: Observable<boolean>;\n  showAdminPanel$: Observable<boolean>;\n  connected$: Observable<boolean>;\n  isLoggedIn$: Observable<boolean>;\n  showPasswordChange = false;\n  editingMessage$: Observable<Message | null>;\n  highlightedMessageId: string | null = null;\n\n  constructor(private chatService: ChatService) {\n    this.user$ = this.chatService.user$;\n    this.messages$ = this.chatService.messages$;\n    this.onlineUsers$ = this.chatService.onlineUsers$;\n    this.groups$ = this.chatService.groups$;\n    this.currentGroup$ = this.chatService.currentGroup$;\n    this.replyTo$ = this.chatService.replyTo$;\n    this.loading$ = this.chatService.loading$;\n    this.isAdmin$ = this.chatService.isAdmin$;\n    this.showAdminPanel$ = this.chatService.showAdminPanel$;\n    this.connected$ = this.chatService.connected$;\n    this.isLoggedIn$ = this.chatService.isLoggedIn$;\n    this.editingMessage$ = this.chatService.editingMessage$;\n  }\n\n  ngOnInit(): void {\n    // Component initialization\n  }\n\n  ngOnDestroy(): void {\n    this.chatService.logout();\n  }\n\n  onJoinGroup(groupId: string): void {\n    this.chatService.joinGroup(groupId);\n  }\n\n  onSendMessage(event: { text: string; replyToId: string | null }): void {\n    this.chatService.sendMessage(event.text, event.replyToId);\n  }\n\n  onReply(message: Message): void {\n    this.chatService.replyToMessage(message);\n  }\n\n  onCancelReply(): void {\n    this.chatService.cancelReply();\n  }\n\n  onAddReaction(event: { messageId: string; emoji: string }): void {\n    this.chatService.addReaction(event.messageId, event.emoji);\n  }\n\n  onRemoveReaction(data: { messageId: string; emoji: string }): void {\n    this.chatService.removeReaction(data);\n  }\n\n  onEdit(message: Message): void {\n    this.chatService.startEditingMessage(message);\n  }\n\n  async onSaveEdit(newText: string): Promise<void> {\n    try {\n      const editingMessage = await this.editingMessage$.pipe(take(1)).toPromise();\n      if (editingMessage) {\n        await this.chatService.updateMessage(editingMessage.id, newText);\n        this.chatService.cancelEditingMessage();\n      }\n    } catch (error) {\n      console.error('Failed to edit message:', error);\n      alert('Failed to edit message. Please try again.');\n    }\n  }\n\n  onCancelEdit(): void {\n    this.chatService.cancelEditingMessage();\n  }\n\n  async onDelete(message: Message): Promise<void> {\n    try {\n      if (confirm('Are you sure you want to delete this message?')) {\n        await this.chatService.deleteMessage(message.id);\n      }\n    } catch (error) {\n      console.error('Failed to delete message:', error);\n      alert('Failed to delete message. Please try again.');\n    }\n  }\n\n  onShowAdminPanel(): void {\n    this.chatService.showAdminPanel();\n  }\n\n  onHideAdminPanel(): void {\n    this.chatService.hideAdminPanel();\n  }\n\n  onPasswordChange(): void {\n    this.showPasswordChange = true;\n  }\n\n  onClosePasswordChange(): void {\n    this.showPasswordChange = false;\n  }\n\n  onLogout(): void {\n    this.chatService.logout();\n  }\n\n  onMessageClick(message: Message): void {\n    this.highlightedMessageId = message.id;\n    \n    // Clear highlight after 3 seconds\n    setTimeout(() => {\n      this.highlightedMessageId = null;\n    }, 3000);\n  }\n\n  onReply(message: Message): void {\n    this.chatService.replyToMessage(message);\n    \n    // Highlight the replied-to message\n    this.highlightedMessageId = message.id;\n    \n    // Clear highlight after 3 seconds\n    setTimeout(() => {\n      this.highlightedMessageId = null;\n    }, 3000);\n  }\n}\n", "<!-- Login Form -->\n<app-login-form *ngIf=\"!(isLoggedIn$ | async)\"></app-login-form>\n\n<!-- Material Design Chat Interface -->\n<mat-sidenav-container *ngIf=\"isLoggedIn$ | async\" class=\"chat-container\">\n  <!-- Sidebar -->\n  <mat-sidenav #sidenav mode=\"side\" opened=\"true\" class=\"chat-sidebar\">\n    <app-sidebar \n      [onlineUsers]=\"(onlineUsers$ | async) || []\"\n      [currentUser]=\"user$ | async\"\n      [isAdmin]=\"(isAdmin$ | async) || false\"\n      [groups]=\"(groups$ | async) || []\"\n      [currentGroup]=\"currentGroup$ | async\"\n      [onJoinGroup]=\"onJoinGroup.bind(this)\"\n      (onPasswordChange)=\"onPasswordChange()\"\n      (onLogout)=\"onLogout()\"\n    ></app-sidebar>\n  </mat-sidenav>\n\n  <!-- Main Chat Area -->\n  <mat-sidenav-content class=\"chat-main-content\">\n    <!-- Chat Header -->\n    <mat-toolbar class=\"chat-header\">\n      <button mat-icon-button (click)=\"sidenav.toggle()\" class=\"menu-button\">\n        <mat-icon>menu</mat-icon>\n      </button>\n      \n      <div class=\"header-content\">\n        <div class=\"group-info\">\n          <h1 class=\"group-name\">\n            {{ (currentGroup$ | async)?.name || 'Select a Group' }}\n          </h1>\n          <p *ngIf=\"!(currentGroup$ | async)\" class=\"no-group-message\">\n            Please select a group to start chatting\n          </p>\n          <p *ngIf=\"(currentGroup$ | async)?.description\" class=\"group-description\">\n            {{ (currentGroup$ | async)?.description }}\n          </p>\n        </div>\n        \n        <div class=\"header-actions\">\n          <div class=\"connection-status\">\n            <mat-icon [class.connected]=\"connected$ | async\" class=\"status-icon\">\n              {{ (connected$ | async) ? 'wifi' : 'wifi_off' }}\n            </mat-icon>\n            <span class=\"status-text\">\n              {{ (connected$ | async) ? 'Connected' : 'Disconnected' }}\n            </span>\n          </div>\n          \n          <mat-chip-list>\n            <mat-chip class=\"online-count\">\n              {{ (onlineUsers$ | async)?.length || 0 }} online\n            </mat-chip>\n          </mat-chip-list>\n          \n          <button\n            *ngIf=\"isAdmin$ | async\"\n            (click)=\"onShowAdminPanel()\"\n            mat-icon-button\n            matTooltip=\"Admin Panel\"\n          >\n            <mat-icon>admin_panel_settings</mat-icon>\n          </button>\n        </div>\n      </div>\n    </mat-toolbar>\n\n    <!-- Messages Area -->\n    <div class=\"messages-container\">\n      <app-message-list\n        [messages]=\"(messages$ | async) || []\"\n        [currentUser]=\"user$ | async\"\n        [loading]=\"(loading$ | async) || false\"\n        [highlightedMessageId]=\"highlightedMessageId\"\n        (onReply)=\"onReply($event)\"\n        (onAddReaction)=\"onAddReaction($event)\"\n        (onRemoveReaction)=\"onRemoveReaction($event)\"\n        (onEdit)=\"onEdit($event)\"\n        (onDelete)=\"onDelete($event)\"\n        (onMessageClick)=\"onMessageClick($event)\"\n      ></app-message-list>\n    </div>\n\n    <!-- Message Input Area -->\n    <div class=\"input-container\">\n      <app-message-input\n        *ngIf=\"currentGroup$ | async\"\n        [replyTo]=\"replyTo$ | async\"\n        (onSendMessage)=\"onSendMessage($event)\"\n        (onCancelReply)=\"onCancelReply()\"\n      ></app-message-input>\n      \n      <div *ngIf=\"!(currentGroup$ | async)\" class=\"no-group-input\">\n        <mat-card>\n          <mat-card-content>\n            <p>Select a group to start chatting</p>\n          </mat-card-content>\n        </mat-card>\n      </div>\n    </div>\n  </mat-sidenav-content>\n</mat-sidenav-container>\n\n<!-- Admin Panel Modal -->\n<app-admin-panel\n  *ngIf=\"showAdminPanel$ | async\"\n  [currentUser]=\"user$ | async\"\n  (onClose)=\"onHideAdminPanel()\"\n></app-admin-panel>\n\n<!-- Password Change Modal -->\n<div *ngIf=\"showPasswordChange\" class=\"modal-overlay\">\n  <mat-card class=\"modal-card\">\n    <mat-card-header>\n      <mat-card-title>Change Password</mat-card-title>\n      <button\n        (click)=\"onClosePasswordChange()\"\n        mat-icon-button\n        class=\"close-button\"\n      >\n        <mat-icon>close</mat-icon>\n      </button>\n    </mat-card-header>\n    <mat-card-content>\n      <app-password-change></app-password-change>\n    </mat-card-content>\n  </mat-card>\n</div>\n\n<!-- Message Edit Modal -->\n<app-message-edit\n  *ngIf=\"editingMessage$ | async as editingMessage\"\n  [message]=\"editingMessage\"\n  (onSave)=\"onSaveEdit($event)\"\n  (onCancel)=\"onCancelEdit()\"\n></app-message-edit>"]}, "metadata": {}, "sourceType": "module"}