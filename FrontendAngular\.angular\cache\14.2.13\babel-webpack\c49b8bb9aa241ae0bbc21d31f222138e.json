{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nimport { noop } from '../util/noop';\nexport function skipUntil(notifier) {\n  return operate((source, subscriber) => {\n    let taking = false;\n    const skipSubscriber = createOperatorSubscriber(subscriber, () => {\n      skipSubscriber === null || skipSubscriber === void 0 ? void 0 : skipSubscriber.unsubscribe();\n      taking = true;\n    }, noop);\n    innerFrom(notifier).subscribe(skipSubscriber);\n    source.subscribe(createOperatorSubscriber(subscriber, value => taking && subscriber.next(value)));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "innerFrom", "noop", "<PERSON><PERSON><PERSON><PERSON>", "notifier", "source", "subscriber", "taking", "skipSubscriber", "unsubscribe", "subscribe", "value", "next"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/rxjs/dist/esm/internal/operators/skipUntil.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nimport { noop } from '../util/noop';\nexport function skipUntil(notifier) {\n    return operate((source, subscriber) => {\n        let taking = false;\n        const skipSubscriber = createOperatorSubscriber(subscriber, () => {\n            skipSubscriber === null || skipSubscriber === void 0 ? void 0 : skipSubscriber.unsubscribe();\n            taking = true;\n        }, noop);\n        innerFrom(notifier).subscribe(skipSubscriber);\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => taking && subscriber.next(value)));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,SAASC,SAAT,QAA0B,yBAA1B;AACA,SAASC,IAAT,QAAqB,cAArB;AACA,OAAO,SAASC,SAAT,CAAmBC,QAAnB,EAA6B;EAChC,OAAOL,OAAO,CAAC,CAACM,MAAD,EAASC,UAAT,KAAwB;IACnC,IAAIC,MAAM,GAAG,KAAb;IACA,MAAMC,cAAc,GAAGR,wBAAwB,CAACM,UAAD,EAAa,MAAM;MAC9DE,cAAc,KAAK,IAAnB,IAA2BA,cAAc,KAAK,KAAK,CAAnD,GAAuD,KAAK,CAA5D,GAAgEA,cAAc,CAACC,WAAf,EAAhE;MACAF,MAAM,GAAG,IAAT;IACH,CAH8C,EAG5CL,IAH4C,CAA/C;IAIAD,SAAS,CAACG,QAAD,CAAT,CAAoBM,SAApB,CAA8BF,cAA9B;IACAH,MAAM,CAACK,SAAP,CAAiBV,wBAAwB,CAACM,UAAD,EAAcK,KAAD,IAAWJ,MAAM,IAAID,UAAU,CAACM,IAAX,CAAgBD,KAAhB,CAAlC,CAAzC;EACH,CARa,CAAd;AASH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}