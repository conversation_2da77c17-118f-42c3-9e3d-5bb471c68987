{"ast": null, "code": "import { SPAC<PERSON>, BACKSPACE, DELE<PERSON>, TAB, hasMod<PERSON><PERSON><PERSON>, ENT<PERSON> } from '@angular/cdk/keycodes';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, EventEmitter, Optional, Inject, Attribute, ContentChild, Input, Output, Component, ViewEncapsulation, ChangeDetectionStrategy, Self, ContentChildren, NgModule } from '@angular/core';\nimport * as i3 from '@angular/material/core';\nimport { mixinTabIndex, mixinColor, mixinDisableRipple, RippleRenderer, MAT_RIPPLE_GLOBAL_OPTIONS, mixinErrorState, MatCommonModule, ErrorStateMatcher } from '@angular/material/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i1 from '@angular/cdk/platform';\nimport { DOCUMENT } from '@angular/common';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { Subject, merge } from 'rxjs';\nimport { take, takeUntil, startWith } from 'rxjs/operators';\nimport { FocusKeyManager } from '@angular/cdk/a11y';\nimport * as i1$1 from '@angular/cdk/bidi';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport * as i2 from '@angular/forms';\nimport { Validators } from '@angular/forms';\nimport { MatFormFieldControl } from '@angular/material/form-field';\n/** Event object emitted by MatChip when selected or deselected. */\n\nconst _c0 = [\"*\"];\n\nclass MatChipSelectionChange {\n  constructor(\n  /** Reference to the chip that emitted the event. */\n  source,\n  /** Whether the chip that emitted the event is selected. */\n  selected,\n  /** Whether the selection change was a result of a user interaction. */\n  isUserInput = false) {\n    this.source = source;\n    this.selected = selected;\n    this.isUserInput = isUserInput;\n  }\n\n}\n/**\n * Injection token that can be used to reference instances of `MatChipRemove`. It serves as\n * alternative token to the actual `MatChipRemove` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\n\n\nconst MAT_CHIP_REMOVE = new InjectionToken('MatChipRemove');\n/**\n * Injection token that can be used to reference instances of `MatChipAvatar`. It serves as\n * alternative token to the actual `MatChipAvatar` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\n\nconst MAT_CHIP_AVATAR = new InjectionToken('MatChipAvatar');\n/**\n * Injection token that can be used to reference instances of `MatChipTrailingIcon`. It serves as\n * alternative token to the actual `MatChipTrailingIcon` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\n\nconst MAT_CHIP_TRAILING_ICON = new InjectionToken('MatChipTrailingIcon'); // Boilerplate for applying mixins to MatChip.\n\n/** @docs-private */\n\nclass MatChipBase {\n  constructor(_elementRef) {\n    this._elementRef = _elementRef;\n  }\n\n}\n\nconst _MatChipMixinBase = mixinTabIndex(mixinColor(mixinDisableRipple(MatChipBase), 'primary'), -1);\n/**\n * Dummy directive to add CSS class to chip avatar.\n * @docs-private\n */\n\n\nclass MatChipAvatar {}\n\nMatChipAvatar.ɵfac = function MatChipAvatar_Factory(t) {\n  return new (t || MatChipAvatar)();\n};\n\nMatChipAvatar.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatChipAvatar,\n  selectors: [[\"mat-chip-avatar\"], [\"\", \"matChipAvatar\", \"\"]],\n  hostAttrs: [1, \"mat-chip-avatar\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_CHIP_AVATAR,\n    useExisting: MatChipAvatar\n  }])]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipAvatar, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-chip-avatar, [matChipAvatar]',\n      host: {\n        'class': 'mat-chip-avatar'\n      },\n      providers: [{\n        provide: MAT_CHIP_AVATAR,\n        useExisting: MatChipAvatar\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Dummy directive to add CSS class to chip trailing icon.\n * @docs-private\n */\n\n\nclass MatChipTrailingIcon {}\n\nMatChipTrailingIcon.ɵfac = function MatChipTrailingIcon_Factory(t) {\n  return new (t || MatChipTrailingIcon)();\n};\n\nMatChipTrailingIcon.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatChipTrailingIcon,\n  selectors: [[\"mat-chip-trailing-icon\"], [\"\", \"matChipTrailingIcon\", \"\"]],\n  hostAttrs: [1, \"mat-chip-trailing-icon\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_CHIP_TRAILING_ICON,\n    useExisting: MatChipTrailingIcon\n  }])]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipTrailingIcon, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-chip-trailing-icon, [matChipTrailingIcon]',\n      host: {\n        'class': 'mat-chip-trailing-icon'\n      },\n      providers: [{\n        provide: MAT_CHIP_TRAILING_ICON,\n        useExisting: MatChipTrailingIcon\n      }]\n    }]\n  }], null, null);\n})();\n/** Material Design styled chip directive. Used inside the MatChipList component. */\n\n\nclass MatChip extends _MatChipMixinBase {\n  constructor(elementRef, _ngZone, platform, globalRippleOptions, _changeDetectorRef, _document, animationMode, tabIndex) {\n    super(elementRef);\n    this._ngZone = _ngZone;\n    this._changeDetectorRef = _changeDetectorRef;\n    /** Whether the chip has focus. */\n\n    this._hasFocus = false;\n    /** Whether the chip list is selectable */\n\n    this.chipListSelectable = true;\n    /** Whether the chip list is in multi-selection mode. */\n\n    this._chipListMultiple = false;\n    /** Whether the chip list as a whole is disabled. */\n\n    this._chipListDisabled = false;\n    /** ARIA role that should be applied to the chip. */\n\n    this.role = 'option';\n    this._selected = false;\n    this._selectable = true;\n    this._disabled = false;\n    this._removable = true;\n    /** Emits when the chip is focused. */\n\n    this._onFocus = new Subject();\n    /** Emits when the chip is blurred. */\n\n    this._onBlur = new Subject();\n    /** Emitted when the chip is selected or deselected. */\n\n    this.selectionChange = new EventEmitter();\n    /** Emitted when the chip is destroyed. */\n\n    this.destroyed = new EventEmitter();\n    /** Emitted when a chip is to be removed. */\n\n    this.removed = new EventEmitter();\n\n    this._addHostClassName(); // Dynamically create the ripple target, append it within the chip, and use it as the\n    // chip's ripple target. Adding the class '.mat-chip-ripple' ensures that it will have\n    // the proper styles.\n\n\n    this._chipRippleTarget = _document.createElement('div');\n\n    this._chipRippleTarget.classList.add('mat-chip-ripple');\n\n    this._elementRef.nativeElement.appendChild(this._chipRippleTarget);\n\n    this._chipRipple = new RippleRenderer(this, _ngZone, this._chipRippleTarget, platform);\n\n    this._chipRipple.setupTriggerEvents(elementRef);\n\n    this.rippleConfig = globalRippleOptions || {};\n    this._animationsDisabled = animationMode === 'NoopAnimations';\n    this.tabIndex = tabIndex != null ? parseInt(tabIndex) || -1 : -1;\n  }\n  /**\n   * Whether ripples are disabled on interaction\n   * @docs-private\n   */\n\n\n  get rippleDisabled() {\n    return this.disabled || this.disableRipple || this._animationsDisabled || !!this.rippleConfig.disabled;\n  }\n  /** Whether the chip is selected. */\n\n\n  get selected() {\n    return this._selected;\n  }\n\n  set selected(value) {\n    const coercedValue = coerceBooleanProperty(value);\n\n    if (coercedValue !== this._selected) {\n      this._selected = coercedValue;\n\n      this._dispatchSelectionChange();\n    }\n  }\n  /** The value of the chip. Defaults to the content inside `<mat-chip>` tags. */\n\n\n  get value() {\n    return this._value !== undefined ? this._value : this._elementRef.nativeElement.textContent;\n  }\n\n  set value(value) {\n    this._value = value;\n  }\n  /**\n   * Whether or not the chip is selectable. When a chip is not selectable,\n   * changes to its selected state are always ignored. By default a chip is\n   * selectable, and it becomes non-selectable if its parent chip list is\n   * not selectable.\n   */\n\n\n  get selectable() {\n    return this._selectable && this.chipListSelectable;\n  }\n\n  set selectable(value) {\n    this._selectable = coerceBooleanProperty(value);\n  }\n  /** Whether the chip is disabled. */\n\n\n  get disabled() {\n    return this._chipListDisabled || this._disabled;\n  }\n\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value);\n  }\n  /**\n   * Determines whether or not the chip displays the remove styling and emits (removed) events.\n   */\n\n\n  get removable() {\n    return this._removable;\n  }\n\n  set removable(value) {\n    this._removable = coerceBooleanProperty(value);\n  }\n  /** The ARIA selected applied to the chip. */\n\n\n  get ariaSelected() {\n    // Remove the `aria-selected` when the chip is deselected in single-selection mode, because\n    // it adds noise to NVDA users where \"not selected\" will be read out for each chip.\n    return this.selectable && (this._chipListMultiple || this.selected) ? this.selected.toString() : null;\n  }\n\n  _addHostClassName() {\n    const basicChipAttrName = 'mat-basic-chip';\n    const element = this._elementRef.nativeElement;\n\n    if (element.hasAttribute(basicChipAttrName) || element.tagName.toLowerCase() === basicChipAttrName) {\n      element.classList.add(basicChipAttrName);\n      return;\n    } else {\n      element.classList.add('mat-standard-chip');\n    }\n  }\n\n  ngOnDestroy() {\n    this.destroyed.emit({\n      chip: this\n    });\n\n    this._chipRipple._removeTriggerEvents();\n  }\n  /** Selects the chip. */\n\n\n  select() {\n    if (!this._selected) {\n      this._selected = true;\n\n      this._dispatchSelectionChange();\n\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /** Deselects the chip. */\n\n\n  deselect() {\n    if (this._selected) {\n      this._selected = false;\n\n      this._dispatchSelectionChange();\n\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /** Select this chip and emit selected event */\n\n\n  selectViaInteraction() {\n    if (!this._selected) {\n      this._selected = true;\n\n      this._dispatchSelectionChange(true);\n\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /** Toggles the current selected state of this chip. */\n\n\n  toggleSelected(isUserInput = false) {\n    this._selected = !this.selected;\n\n    this._dispatchSelectionChange(isUserInput);\n\n    this._changeDetectorRef.markForCheck();\n\n    return this.selected;\n  }\n  /** Allows for programmatic focusing of the chip. */\n\n\n  focus() {\n    if (!this._hasFocus) {\n      this._elementRef.nativeElement.focus();\n\n      this._onFocus.next({\n        chip: this\n      });\n    }\n\n    this._hasFocus = true;\n  }\n  /**\n   * Allows for programmatic removal of the chip. Called by the MatChipList when the DELETE or\n   * BACKSPACE keys are pressed.\n   *\n   * Informs any listeners of the removal request. Does not remove the chip from the DOM.\n   */\n\n\n  remove() {\n    if (this.removable) {\n      this.removed.emit({\n        chip: this\n      });\n    }\n  }\n  /** Handles click events on the chip. */\n\n\n  _handleClick(event) {\n    if (this.disabled) {\n      event.preventDefault();\n    }\n  }\n  /** Handle custom key presses. */\n\n\n  _handleKeydown(event) {\n    if (this.disabled) {\n      return;\n    }\n\n    switch (event.keyCode) {\n      case DELETE:\n      case BACKSPACE:\n        // If we are removable, remove the focused chip\n        this.remove(); // Always prevent so page navigation does not occur\n\n        event.preventDefault();\n        break;\n\n      case SPACE:\n        // If we are selectable, toggle the focused chip\n        if (this.selectable) {\n          this.toggleSelected(true);\n        } // Always prevent space from scrolling the page since the list has focus\n\n\n        event.preventDefault();\n        break;\n    }\n  }\n\n  _blur() {\n    // When animations are enabled, Angular may end up removing the chip from the DOM a little\n    // earlier than usual, causing it to be blurred and throwing off the logic in the chip list\n    // that moves focus not the next item. To work around the issue, we defer marking the chip\n    // as not focused until the next time the zone stabilizes.\n    this._ngZone.onStable.pipe(take(1)).subscribe(() => {\n      this._ngZone.run(() => {\n        this._hasFocus = false;\n\n        this._onBlur.next({\n          chip: this\n        });\n      });\n    });\n  }\n\n  _dispatchSelectionChange(isUserInput = false) {\n    this.selectionChange.emit({\n      source: this,\n      isUserInput,\n      selected: this._selected\n    });\n  }\n\n}\n\nMatChip.ɵfac = function MatChip_Factory(t) {\n  return new (t || MatChip)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1.Platform), i0.ɵɵdirectiveInject(MAT_RIPPLE_GLOBAL_OPTIONS, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵinjectAttribute('tabindex'));\n};\n\nMatChip.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatChip,\n  selectors: [[\"mat-basic-chip\"], [\"\", \"mat-basic-chip\", \"\"], [\"mat-chip\"], [\"\", \"mat-chip\", \"\"]],\n  contentQueries: function MatChip_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_AVATAR, 5);\n      i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_TRAILING_ICON, 5);\n      i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_REMOVE, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.avatar = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.trailingIcon = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.removeIcon = _t.first);\n    }\n  },\n  hostAttrs: [1, \"mat-chip\", \"mat-focus-indicator\"],\n  hostVars: 15,\n  hostBindings: function MatChip_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function MatChip_click_HostBindingHandler($event) {\n        return ctx._handleClick($event);\n      })(\"keydown\", function MatChip_keydown_HostBindingHandler($event) {\n        return ctx._handleKeydown($event);\n      })(\"focus\", function MatChip_focus_HostBindingHandler() {\n        return ctx.focus();\n      })(\"blur\", function MatChip_blur_HostBindingHandler() {\n        return ctx._blur();\n      });\n    }\n\n    if (rf & 2) {\n      i0.ɵɵattribute(\"tabindex\", ctx.disabled ? null : ctx.tabIndex)(\"role\", ctx.role)(\"disabled\", ctx.disabled || null)(\"aria-disabled\", ctx.disabled.toString())(\"aria-selected\", ctx.ariaSelected);\n      i0.ɵɵclassProp(\"mat-chip-selected\", ctx.selected)(\"mat-chip-with-avatar\", ctx.avatar)(\"mat-chip-with-trailing-icon\", ctx.trailingIcon || ctx.removeIcon)(\"mat-chip-disabled\", ctx.disabled)(\"_mat-animation-noopable\", ctx._animationsDisabled);\n    }\n  },\n  inputs: {\n    color: \"color\",\n    disableRipple: \"disableRipple\",\n    tabIndex: \"tabIndex\",\n    role: \"role\",\n    selected: \"selected\",\n    value: \"value\",\n    selectable: \"selectable\",\n    disabled: \"disabled\",\n    removable: \"removable\"\n  },\n  outputs: {\n    selectionChange: \"selectionChange\",\n    destroyed: \"destroyed\",\n    removed: \"removed\"\n  },\n  exportAs: [\"matChip\"],\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChip, [{\n    type: Directive,\n    args: [{\n      selector: `mat-basic-chip, [mat-basic-chip], mat-chip, [mat-chip]`,\n      inputs: ['color', 'disableRipple', 'tabIndex'],\n      exportAs: 'matChip',\n      host: {\n        'class': 'mat-chip mat-focus-indicator',\n        '[attr.tabindex]': 'disabled ? null : tabIndex',\n        '[attr.role]': 'role',\n        '[class.mat-chip-selected]': 'selected',\n        '[class.mat-chip-with-avatar]': 'avatar',\n        '[class.mat-chip-with-trailing-icon]': 'trailingIcon || removeIcon',\n        '[class.mat-chip-disabled]': 'disabled',\n        '[class._mat-animation-noopable]': '_animationsDisabled',\n        '[attr.disabled]': 'disabled || null',\n        '[attr.aria-disabled]': 'disabled.toString()',\n        '[attr.aria-selected]': 'ariaSelected',\n        '(click)': '_handleClick($event)',\n        '(keydown)': '_handleKeydown($event)',\n        '(focus)': 'focus()',\n        '(blur)': '_blur()'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i1.Platform\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_RIPPLE_GLOBAL_OPTIONS]\n      }]\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Attribute,\n        args: ['tabindex']\n      }]\n    }];\n  }, {\n    avatar: [{\n      type: ContentChild,\n      args: [MAT_CHIP_AVATAR]\n    }],\n    trailingIcon: [{\n      type: ContentChild,\n      args: [MAT_CHIP_TRAILING_ICON]\n    }],\n    removeIcon: [{\n      type: ContentChild,\n      args: [MAT_CHIP_REMOVE]\n    }],\n    role: [{\n      type: Input\n    }],\n    selected: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    selectable: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    removable: [{\n      type: Input\n    }],\n    selectionChange: [{\n      type: Output\n    }],\n    destroyed: [{\n      type: Output\n    }],\n    removed: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * Applies proper (click) support and adds styling for use with the Material Design \"cancel\" icon\n * available at https://material.io/icons/#ic_cancel.\n *\n * Example:\n *\n *     `<mat-chip>\n *       <mat-icon matChipRemove>cancel</mat-icon>\n *     </mat-chip>`\n *\n * You *may* use a custom icon, but you may need to override the `mat-chip-remove` positioning\n * styles to properly center the icon within the chip.\n */\n\n\nclass MatChipRemove {\n  constructor(_parentChip, elementRef) {\n    this._parentChip = _parentChip;\n\n    if (elementRef.nativeElement.nodeName === 'BUTTON') {\n      elementRef.nativeElement.setAttribute('type', 'button');\n    }\n  }\n  /** Calls the parent chip's public `remove()` method if applicable. */\n\n\n  _handleClick(event) {\n    const parentChip = this._parentChip;\n\n    if (parentChip.removable && !parentChip.disabled) {\n      parentChip.remove();\n    } // We need to stop event propagation because otherwise the event will bubble up to the\n    // form field and cause the `onContainerClick` method to be invoked. This method would then\n    // reset the focused chip that has been focused after chip removal. Usually the parent\n    // the parent click listener of the `MatChip` would prevent propagation, but it can happen\n    // that the chip is being removed before the event bubbles up.\n\n\n    event.stopPropagation();\n    event.preventDefault();\n  }\n\n}\n\nMatChipRemove.ɵfac = function MatChipRemove_Factory(t) {\n  return new (t || MatChipRemove)(i0.ɵɵdirectiveInject(MatChip), i0.ɵɵdirectiveInject(i0.ElementRef));\n};\n\nMatChipRemove.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatChipRemove,\n  selectors: [[\"\", \"matChipRemove\", \"\"]],\n  hostAttrs: [1, \"mat-chip-remove\", \"mat-chip-trailing-icon\"],\n  hostBindings: function MatChipRemove_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function MatChipRemove_click_HostBindingHandler($event) {\n        return ctx._handleClick($event);\n      });\n    }\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_CHIP_REMOVE,\n    useExisting: MatChipRemove\n  }])]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipRemove, [{\n    type: Directive,\n    args: [{\n      selector: '[matChipRemove]',\n      host: {\n        'class': 'mat-chip-remove mat-chip-trailing-icon',\n        '(click)': '_handleClick($event)'\n      },\n      providers: [{\n        provide: MAT_CHIP_REMOVE,\n        useExisting: MatChipRemove\n      }]\n    }]\n  }], function () {\n    return [{\n      type: MatChip\n    }, {\n      type: i0.ElementRef\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Injection token to be used to override the default options for the chips module. */\n\n\nconst MAT_CHIPS_DEFAULT_OPTIONS = new InjectionToken('mat-chips-default-options');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to MatChipList.\n\n/** @docs-private */\n\nconst _MatChipListBase = mixinErrorState(class {\n  constructor(_defaultErrorStateMatcher, _parentForm, _parentFormGroup,\n  /**\n   * Form control bound to the component.\n   * Implemented as part of `MatFormFieldControl`.\n   * @docs-private\n   */\n  ngControl) {\n    this._defaultErrorStateMatcher = _defaultErrorStateMatcher;\n    this._parentForm = _parentForm;\n    this._parentFormGroup = _parentFormGroup;\n    this.ngControl = ngControl;\n    /**\n     * Emits whenever the component state changes and should cause the parent\n     * form-field to update. Implemented as part of `MatFormFieldControl`.\n     * @docs-private\n     */\n\n    this.stateChanges = new Subject();\n  }\n\n}); // Increasing integer for generating unique ids for chip-list components.\n\n\nlet nextUniqueId$1 = 0;\n/** Change event object that is emitted when the chip list value has changed. */\n\nclass MatChipListChange {\n  constructor(\n  /** Chip list that emitted the event. */\n  source,\n  /** Value of the chip list when the event was emitted. */\n  value) {\n    this.source = source;\n    this.value = value;\n  }\n\n}\n/**\n * A material design chips component (named ChipList for its similarity to the List component).\n */\n\n\nclass MatChipList extends _MatChipListBase {\n  constructor(_elementRef, _changeDetectorRef, _dir, _parentForm, _parentFormGroup, _defaultErrorStateMatcher, ngControl) {\n    super(_defaultErrorStateMatcher, _parentForm, _parentFormGroup, ngControl);\n    this._elementRef = _elementRef;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._dir = _dir;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n\n    this.controlType = 'mat-chip-list';\n    /**\n     * When a chip is destroyed, we store the index of the destroyed chip until the chips\n     * query list notifies about the update. This is necessary because we cannot determine an\n     * appropriate chip that should receive focus until the array of chips updated completely.\n     */\n\n    this._lastDestroyedChipIndex = null;\n    /** Subject that emits when the component has been destroyed. */\n\n    this._destroyed = new Subject();\n    /** Uid of the chip list */\n\n    this._uid = `mat-chip-list-${nextUniqueId$1++}`;\n    /** Tab index for the chip list. */\n\n    this._tabIndex = 0;\n    /**\n     * User defined tab index.\n     * When it is not null, use user defined tab index. Otherwise use _tabIndex\n     */\n\n    this._userTabIndex = null;\n    /** Function when touched */\n\n    this._onTouched = () => {};\n    /** Function when changed */\n\n\n    this._onChange = () => {};\n\n    this._multiple = false;\n\n    this._compareWith = (o1, o2) => o1 === o2;\n\n    this._disabled = false;\n    /** Orientation of the chip list. */\n\n    this.ariaOrientation = 'horizontal';\n    this._selectable = true;\n    /** Event emitted when the selected chip list value has been changed by the user. */\n\n    this.change = new EventEmitter();\n    /**\n     * Event that emits whenever the raw value of the chip-list changes. This is here primarily\n     * to facilitate the two-way binding for the `value` input.\n     * @docs-private\n     */\n\n    this.valueChange = new EventEmitter();\n\n    if (this.ngControl) {\n      this.ngControl.valueAccessor = this;\n    }\n  }\n  /** The array of selected chips inside chip list. */\n\n\n  get selected() {\n    return this.multiple ? this._selectionModel?.selected || [] : this._selectionModel?.selected[0];\n  }\n  /** The ARIA role applied to the chip list. */\n\n\n  get role() {\n    if (this._explicitRole) {\n      return this._explicitRole;\n    }\n\n    return this.empty ? null : 'listbox';\n  }\n\n  set role(role) {\n    this._explicitRole = role;\n  }\n  /** Whether the user should be allowed to select multiple chips. */\n\n\n  get multiple() {\n    return this._multiple;\n  }\n\n  set multiple(value) {\n    this._multiple = coerceBooleanProperty(value);\n\n    this._syncChipsState();\n  }\n  /**\n   * A function to compare the option values with the selected values. The first argument\n   * is a value from an option. The second is a value from the selection. A boolean\n   * should be returned.\n   */\n\n\n  get compareWith() {\n    return this._compareWith;\n  }\n\n  set compareWith(fn) {\n    this._compareWith = fn;\n\n    if (this._selectionModel) {\n      // A different comparator means the selection could change.\n      this._initializeSelection();\n    }\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n\n\n  get value() {\n    return this._value;\n  }\n\n  set value(value) {\n    this.writeValue(value);\n    this._value = value;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n\n\n  get id() {\n    return this._chipInput ? this._chipInput.id : this._uid;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n\n\n  get required() {\n    return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n  }\n\n  set required(value) {\n    this._required = coerceBooleanProperty(value);\n    this.stateChanges.next();\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n\n\n  get placeholder() {\n    return this._chipInput ? this._chipInput.placeholder : this._placeholder;\n  }\n\n  set placeholder(value) {\n    this._placeholder = value;\n    this.stateChanges.next();\n  }\n  /** Whether any chips or the matChipInput inside of this chip-list has focus. */\n\n\n  get focused() {\n    return this._chipInput && this._chipInput.focused || this._hasFocusedChip();\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n\n\n  get empty() {\n    return (!this._chipInput || this._chipInput.empty) && (!this.chips || this.chips.length === 0);\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n\n\n  get shouldLabelFloat() {\n    return !this.empty || this.focused;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n\n\n  get disabled() {\n    return this.ngControl ? !!this.ngControl.disabled : this._disabled;\n  }\n\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value);\n\n    this._syncChipsState();\n  }\n  /**\n   * Whether or not this chip list is selectable. When a chip list is not selectable,\n   * the selected states for all the chips inside the chip list are always ignored.\n   */\n\n\n  get selectable() {\n    return this._selectable;\n  }\n\n  set selectable(value) {\n    this._selectable = coerceBooleanProperty(value);\n\n    this._syncChipsState();\n  }\n\n  set tabIndex(value) {\n    this._userTabIndex = value;\n    this._tabIndex = value;\n  }\n  /** Combined stream of all of the child chips' selection change events. */\n\n\n  get chipSelectionChanges() {\n    return merge(...this.chips.map(chip => chip.selectionChange));\n  }\n  /** Combined stream of all of the child chips' focus change events. */\n\n\n  get chipFocusChanges() {\n    return merge(...this.chips.map(chip => chip._onFocus));\n  }\n  /** Combined stream of all of the child chips' blur change events. */\n\n\n  get chipBlurChanges() {\n    return merge(...this.chips.map(chip => chip._onBlur));\n  }\n  /** Combined stream of all of the child chips' remove change events. */\n\n\n  get chipRemoveChanges() {\n    return merge(...this.chips.map(chip => chip.destroyed));\n  }\n\n  ngAfterContentInit() {\n    this._keyManager = new FocusKeyManager(this.chips).withWrap().withVerticalOrientation().withHomeAndEnd().withHorizontalOrientation(this._dir ? this._dir.value : 'ltr');\n\n    if (this._dir) {\n      this._dir.change.pipe(takeUntil(this._destroyed)).subscribe(dir => this._keyManager.withHorizontalOrientation(dir));\n    }\n\n    this._keyManager.tabOut.pipe(takeUntil(this._destroyed)).subscribe(() => {\n      this._allowFocusEscape();\n    }); // When the list changes, re-subscribe\n\n\n    this.chips.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n      if (this.disabled || !this.selectable) {\n        // Since this happens after the content has been\n        // checked, we need to defer it to the next tick.\n        Promise.resolve().then(() => {\n          this._syncChipsState();\n        });\n      }\n\n      this._resetChips(); // Reset chips selected/deselected status\n\n\n      this._initializeSelection(); // Check to see if we need to update our tab index\n\n\n      this._updateTabIndex(); // Check to see if we have a destroyed chip and need to refocus\n\n\n      this._updateFocusForDestroyedChips();\n\n      this.stateChanges.next();\n    });\n  }\n\n  ngOnInit() {\n    this._selectionModel = new SelectionModel(this.multiple, undefined, false);\n    this.stateChanges.next();\n  }\n\n  ngDoCheck() {\n    if (this.ngControl) {\n      // We need to re-evaluate this on every change detection cycle, because there are some\n      // error triggers that we can't subscribe to (e.g. parent form submissions). This means\n      // that whatever logic is in here has to be super lean or we risk destroying the performance.\n      this.updateErrorState();\n\n      if (this.ngControl.disabled !== this._disabled) {\n        this.disabled = !!this.ngControl.disabled;\n      }\n    }\n  }\n\n  ngOnDestroy() {\n    this._destroyed.next();\n\n    this._destroyed.complete();\n\n    this.stateChanges.complete();\n\n    this._dropSubscriptions();\n  }\n  /** Associates an HTML input element with this chip list. */\n\n\n  registerInput(inputElement) {\n    this._chipInput = inputElement; // We use this attribute to match the chip list to its input in test harnesses.\n    // Set the attribute directly here to avoid \"changed after checked\" errors.\n\n    this._elementRef.nativeElement.setAttribute('data-mat-chip-input', inputElement.id);\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n\n\n  setDescribedByIds(ids) {\n    if (ids.length) {\n      this._elementRef.nativeElement.setAttribute('aria-describedby', ids.join(' '));\n    } else {\n      this._elementRef.nativeElement.removeAttribute('aria-describedby');\n    }\n  } // Implemented as part of ControlValueAccessor.\n\n\n  writeValue(value) {\n    if (this.chips) {\n      this._setSelectionByValue(value, false);\n    }\n  } // Implemented as part of ControlValueAccessor.\n\n\n  registerOnChange(fn) {\n    this._onChange = fn;\n  } // Implemented as part of ControlValueAccessor.\n\n\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  } // Implemented as part of ControlValueAccessor.\n\n\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n    this.stateChanges.next();\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n\n\n  onContainerClick(event) {\n    if (!this._originatesFromChip(event)) {\n      this.focus();\n    }\n  }\n  /**\n   * Focuses the first non-disabled chip in this chip list, or the associated input when there\n   * are no eligible chips.\n   */\n\n\n  focus(options) {\n    if (this.disabled) {\n      return;\n    } // TODO: ARIA says this should focus the first `selected` chip if any are selected.\n    // Focus on first element if there's no chipInput inside chip-list\n\n\n    if (this._chipInput && this._chipInput.focused) {// do nothing\n    } else if (this.chips.length > 0) {\n      this._keyManager.setFirstItemActive();\n\n      this.stateChanges.next();\n    } else {\n      this._focusInput(options);\n\n      this.stateChanges.next();\n    }\n  }\n  /** Attempt to focus an input if we have one. */\n\n\n  _focusInput(options) {\n    if (this._chipInput) {\n      this._chipInput.focus(options);\n    }\n  }\n  /**\n   * Pass events to the keyboard manager. Available here for tests.\n   */\n\n\n  _keydown(event) {\n    const target = event.target;\n\n    if (target && target.classList.contains('mat-chip')) {\n      this._keyManager.onKeydown(event);\n\n      this.stateChanges.next();\n    }\n  }\n  /**\n   * Check the tab index as you should not be allowed to focus an empty list.\n   */\n\n\n  _updateTabIndex() {\n    // If we have 0 chips, we should not allow keyboard focus\n    this._tabIndex = this._userTabIndex || (this.chips.length === 0 ? -1 : 0);\n  }\n  /**\n   * If the amount of chips changed, we need to update the\n   * key manager state and focus the next closest chip.\n   */\n\n\n  _updateFocusForDestroyedChips() {\n    // Move focus to the closest chip. If no other chips remain, focus the chip-list itself.\n    if (this._lastDestroyedChipIndex != null) {\n      if (this.chips.length) {\n        const newChipIndex = Math.min(this._lastDestroyedChipIndex, this.chips.length - 1);\n\n        this._keyManager.setActiveItem(newChipIndex);\n      } else {\n        this.focus();\n      }\n    }\n\n    this._lastDestroyedChipIndex = null;\n  }\n  /**\n   * Utility to ensure all indexes are valid.\n   *\n   * @param index The index to be checked.\n   * @returns True if the index is valid for our list of chips.\n   */\n\n\n  _isValidIndex(index) {\n    return index >= 0 && index < this.chips.length;\n  }\n\n  _setSelectionByValue(value, isUserInput = true) {\n    this._clearSelection();\n\n    this.chips.forEach(chip => chip.deselect());\n\n    if (Array.isArray(value)) {\n      value.forEach(currentValue => this._selectValue(currentValue, isUserInput));\n\n      this._sortValues();\n    } else {\n      const correspondingChip = this._selectValue(value, isUserInput); // Shift focus to the active item. Note that we shouldn't do this in multiple\n      // mode, because we don't know what chip the user interacted with last.\n\n\n      if (correspondingChip) {\n        if (isUserInput) {\n          this._keyManager.setActiveItem(correspondingChip);\n        }\n      }\n    }\n  }\n  /**\n   * Finds and selects the chip based on its value.\n   * @returns Chip that has the corresponding value.\n   */\n\n\n  _selectValue(value, isUserInput = true) {\n    const correspondingChip = this.chips.find(chip => {\n      return chip.value != null && this._compareWith(chip.value, value);\n    });\n\n    if (correspondingChip) {\n      isUserInput ? correspondingChip.selectViaInteraction() : correspondingChip.select();\n\n      this._selectionModel.select(correspondingChip);\n    }\n\n    return correspondingChip;\n  }\n\n  _initializeSelection() {\n    // Defer setting the value in order to avoid the \"Expression\n    // has changed after it was checked\" errors from Angular.\n    Promise.resolve().then(() => {\n      if (this.ngControl || this._value) {\n        this._setSelectionByValue(this.ngControl ? this.ngControl.value : this._value, false);\n\n        this.stateChanges.next();\n      }\n    });\n  }\n  /**\n   * Deselects every chip in the list.\n   * @param skip Chip that should not be deselected.\n   */\n\n\n  _clearSelection(skip) {\n    this._selectionModel.clear();\n\n    this.chips.forEach(chip => {\n      if (chip !== skip) {\n        chip.deselect();\n      }\n    });\n    this.stateChanges.next();\n  }\n  /**\n   * Sorts the model values, ensuring that they keep the same\n   * order that they have in the panel.\n   */\n\n\n  _sortValues() {\n    if (this._multiple) {\n      this._selectionModel.clear();\n\n      this.chips.forEach(chip => {\n        if (chip.selected) {\n          this._selectionModel.select(chip);\n        }\n      });\n      this.stateChanges.next();\n    }\n  }\n  /** Emits change event to set the model value. */\n\n\n  _propagateChanges(fallbackValue) {\n    let valueToEmit = null;\n\n    if (Array.isArray(this.selected)) {\n      valueToEmit = this.selected.map(chip => chip.value);\n    } else {\n      valueToEmit = this.selected ? this.selected.value : fallbackValue;\n    }\n\n    this._value = valueToEmit;\n    this.change.emit(new MatChipListChange(this, valueToEmit));\n    this.valueChange.emit(valueToEmit);\n\n    this._onChange(valueToEmit);\n\n    this._changeDetectorRef.markForCheck();\n  }\n  /** When blurred, mark the field as touched when focus moved outside the chip list. */\n\n\n  _blur() {\n    if (!this._hasFocusedChip()) {\n      this._keyManager.setActiveItem(-1);\n    }\n\n    if (!this.disabled) {\n      if (this._chipInput) {\n        // If there's a chip input, we should check whether the focus moved to chip input.\n        // If the focus is not moved to chip input, mark the field as touched. If the focus moved\n        // to chip input, do nothing.\n        // Timeout is needed to wait for the focus() event trigger on chip input.\n        setTimeout(() => {\n          if (!this.focused) {\n            this._markAsTouched();\n          }\n        });\n      } else {\n        // If there's no chip input, then mark the field as touched.\n        this._markAsTouched();\n      }\n    }\n  }\n  /** Mark the field as touched */\n\n\n  _markAsTouched() {\n    this._onTouched();\n\n    this._changeDetectorRef.markForCheck();\n\n    this.stateChanges.next();\n  }\n  /**\n   * Removes the `tabindex` from the chip list and resets it back afterwards, allowing the\n   * user to tab out of it. This prevents the list from capturing focus and redirecting\n   * it back to the first chip, creating a focus trap, if it user tries to tab away.\n   */\n\n\n  _allowFocusEscape() {\n    if (this._tabIndex !== -1) {\n      this._tabIndex = -1;\n      setTimeout(() => {\n        this._tabIndex = this._userTabIndex || 0;\n\n        this._changeDetectorRef.markForCheck();\n      });\n    }\n  }\n\n  _resetChips() {\n    this._dropSubscriptions();\n\n    this._listenToChipsFocus();\n\n    this._listenToChipsSelection();\n\n    this._listenToChipsRemoved();\n  }\n\n  _dropSubscriptions() {\n    if (this._chipFocusSubscription) {\n      this._chipFocusSubscription.unsubscribe();\n\n      this._chipFocusSubscription = null;\n    }\n\n    if (this._chipBlurSubscription) {\n      this._chipBlurSubscription.unsubscribe();\n\n      this._chipBlurSubscription = null;\n    }\n\n    if (this._chipSelectionSubscription) {\n      this._chipSelectionSubscription.unsubscribe();\n\n      this._chipSelectionSubscription = null;\n    }\n\n    if (this._chipRemoveSubscription) {\n      this._chipRemoveSubscription.unsubscribe();\n\n      this._chipRemoveSubscription = null;\n    }\n  }\n  /** Listens to user-generated selection events on each chip. */\n\n\n  _listenToChipsSelection() {\n    this._chipSelectionSubscription = this.chipSelectionChanges.subscribe(event => {\n      event.source.selected ? this._selectionModel.select(event.source) : this._selectionModel.deselect(event.source); // For single selection chip list, make sure the deselected value is unselected.\n\n      if (!this.multiple) {\n        this.chips.forEach(chip => {\n          if (!this._selectionModel.isSelected(chip) && chip.selected) {\n            chip.deselect();\n          }\n        });\n      }\n\n      if (event.isUserInput) {\n        this._propagateChanges();\n      }\n    });\n  }\n  /** Listens to user-generated selection events on each chip. */\n\n\n  _listenToChipsFocus() {\n    this._chipFocusSubscription = this.chipFocusChanges.subscribe(event => {\n      let chipIndex = this.chips.toArray().indexOf(event.chip);\n\n      if (this._isValidIndex(chipIndex)) {\n        this._keyManager.updateActiveItem(chipIndex);\n      }\n\n      this.stateChanges.next();\n    });\n    this._chipBlurSubscription = this.chipBlurChanges.subscribe(() => {\n      this._blur();\n\n      this.stateChanges.next();\n    });\n  }\n\n  _listenToChipsRemoved() {\n    this._chipRemoveSubscription = this.chipRemoveChanges.subscribe(event => {\n      const chip = event.chip;\n      const chipIndex = this.chips.toArray().indexOf(event.chip); // In case the chip that will be removed is currently focused, we temporarily store\n      // the index in order to be able to determine an appropriate sibling chip that will\n      // receive focus.\n\n      if (this._isValidIndex(chipIndex) && chip._hasFocus) {\n        this._lastDestroyedChipIndex = chipIndex;\n      }\n    });\n  }\n  /** Checks whether an event comes from inside a chip element. */\n\n\n  _originatesFromChip(event) {\n    let currentElement = event.target;\n\n    while (currentElement && currentElement !== this._elementRef.nativeElement) {\n      if (currentElement.classList.contains('mat-chip')) {\n        return true;\n      }\n\n      currentElement = currentElement.parentElement;\n    }\n\n    return false;\n  }\n  /** Checks whether any of the chips is focused. */\n\n\n  _hasFocusedChip() {\n    return this.chips && this.chips.some(chip => chip._hasFocus);\n  }\n  /** Syncs the list's state with the individual chips. */\n\n\n  _syncChipsState() {\n    if (this.chips) {\n      this.chips.forEach(chip => {\n        chip._chipListDisabled = this._disabled;\n        chip._chipListMultiple = this.multiple;\n        chip.chipListSelectable = this._selectable;\n      });\n    }\n  }\n\n}\n\nMatChipList.ɵfac = function MatChipList_Factory(t) {\n  return new (t || MatChipList)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.Directionality, 8), i0.ɵɵdirectiveInject(i2.NgForm, 8), i0.ɵɵdirectiveInject(i2.FormGroupDirective, 8), i0.ɵɵdirectiveInject(i3.ErrorStateMatcher), i0.ɵɵdirectiveInject(i2.NgControl, 10));\n};\n\nMatChipList.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatChipList,\n  selectors: [[\"mat-chip-list\"]],\n  contentQueries: function MatChipList_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MatChip, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.chips = _t);\n    }\n  },\n  hostAttrs: [1, \"mat-chip-list\"],\n  hostVars: 14,\n  hostBindings: function MatChipList_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"focus\", function MatChipList_focus_HostBindingHandler() {\n        return ctx.focus();\n      })(\"blur\", function MatChipList_blur_HostBindingHandler() {\n        return ctx._blur();\n      })(\"keydown\", function MatChipList_keydown_HostBindingHandler($event) {\n        return ctx._keydown($event);\n      });\n    }\n\n    if (rf & 2) {\n      i0.ɵɵhostProperty(\"id\", ctx._uid);\n      i0.ɵɵattribute(\"tabindex\", ctx.disabled ? null : ctx._tabIndex)(\"aria-required\", ctx.role ? ctx.required : null)(\"aria-disabled\", ctx.disabled.toString())(\"aria-invalid\", ctx.errorState)(\"aria-multiselectable\", ctx.multiple)(\"role\", ctx.role)(\"aria-orientation\", ctx.ariaOrientation);\n      i0.ɵɵclassProp(\"mat-chip-list-disabled\", ctx.disabled)(\"mat-chip-list-invalid\", ctx.errorState)(\"mat-chip-list-required\", ctx.required);\n    }\n  },\n  inputs: {\n    role: \"role\",\n    userAriaDescribedBy: [\"aria-describedby\", \"userAriaDescribedBy\"],\n    errorStateMatcher: \"errorStateMatcher\",\n    multiple: \"multiple\",\n    compareWith: \"compareWith\",\n    value: \"value\",\n    required: \"required\",\n    placeholder: \"placeholder\",\n    disabled: \"disabled\",\n    ariaOrientation: [\"aria-orientation\", \"ariaOrientation\"],\n    selectable: \"selectable\",\n    tabIndex: \"tabIndex\"\n  },\n  outputs: {\n    change: \"change\",\n    valueChange: \"valueChange\"\n  },\n  exportAs: [\"matChipList\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MatFormFieldControl,\n    useExisting: MatChipList\n  }]), i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c0,\n  decls: 2,\n  vars: 0,\n  consts: [[1, \"mat-chip-list-wrapper\"]],\n  template: function MatChipList_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵprojection(1);\n      i0.ɵɵelementEnd();\n    }\n  },\n  styles: [\".mat-chip{position:relative;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0);border:none;-webkit-appearance:none;-moz-appearance:none}.mat-chip::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px) * -1)}.mat-standard-chip{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);display:inline-flex;padding:7px 12px;border-radius:16px;align-items:center;cursor:default;min-height:32px;height:1px}.mat-standard-chip._mat-animation-noopable{transition:none !important;animation:none !important}.mat-standard-chip .mat-chip-remove{border:none;-webkit-appearance:none;-moz-appearance:none;padding:0;background:none}.mat-standard-chip .mat-chip-remove.mat-icon,.mat-standard-chip .mat-chip-remove .mat-icon{width:18px;height:18px;font-size:18px}.mat-standard-chip::after{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit;opacity:0;content:\\\"\\\";pointer-events:none;transition:opacity 200ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-standard-chip:hover::after{opacity:.12}.mat-standard-chip:focus{outline:none}.mat-standard-chip:focus::after{opacity:.16}.cdk-high-contrast-active .mat-standard-chip{outline:solid 1px}.cdk-high-contrast-active .mat-standard-chip.mat-chip-selected{outline-width:3px}.mat-standard-chip.mat-chip-disabled::after{opacity:0}.mat-standard-chip.mat-chip-disabled .mat-chip-remove,.mat-standard-chip.mat-chip-disabled .mat-chip-trailing-icon{cursor:default}.mat-standard-chip.mat-chip-with-trailing-icon.mat-chip-with-avatar,.mat-standard-chip.mat-chip-with-avatar{padding-top:0;padding-bottom:0}.mat-standard-chip.mat-chip-with-trailing-icon.mat-chip-with-avatar{padding-right:8px;padding-left:0}[dir=rtl] .mat-standard-chip.mat-chip-with-trailing-icon.mat-chip-with-avatar{padding-left:8px;padding-right:0}.mat-standard-chip.mat-chip-with-trailing-icon{padding-top:7px;padding-bottom:7px;padding-right:8px;padding-left:12px}[dir=rtl] .mat-standard-chip.mat-chip-with-trailing-icon{padding-left:8px;padding-right:12px}.mat-standard-chip.mat-chip-with-avatar{padding-left:0;padding-right:12px}[dir=rtl] .mat-standard-chip.mat-chip-with-avatar{padding-right:0;padding-left:12px}.mat-standard-chip .mat-chip-avatar{width:24px;height:24px;margin-right:8px;margin-left:4px}[dir=rtl] .mat-standard-chip .mat-chip-avatar{margin-left:8px;margin-right:4px}.mat-standard-chip .mat-chip-remove,.mat-standard-chip .mat-chip-trailing-icon{width:18px;height:18px;cursor:pointer}.mat-standard-chip .mat-chip-remove,.mat-standard-chip .mat-chip-trailing-icon{margin-left:8px;margin-right:0}[dir=rtl] .mat-standard-chip .mat-chip-remove,[dir=rtl] .mat-standard-chip .mat-chip-trailing-icon{margin-right:8px;margin-left:0}.mat-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit;overflow:hidden;transform:translateZ(0)}.mat-chip-list-wrapper{display:flex;flex-direction:row;flex-wrap:wrap;align-items:center;margin:-4px}.mat-chip-list-wrapper input.mat-input-element,.mat-chip-list-wrapper .mat-standard-chip{margin:4px}.mat-chip-list-stacked .mat-chip-list-wrapper{flex-direction:column;align-items:flex-start}.mat-chip-list-stacked .mat-chip-list-wrapper .mat-standard-chip{width:100%}.mat-chip-avatar{border-radius:50%;justify-content:center;align-items:center;display:flex;overflow:hidden;object-fit:cover}input.mat-chip-input{width:150px;margin:4px;flex:1 0 150px}\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipList, [{\n    type: Component,\n    args: [{\n      selector: 'mat-chip-list',\n      template: `<div class=\"mat-chip-list-wrapper\"><ng-content></ng-content></div>`,\n      exportAs: 'matChipList',\n      host: {\n        '[attr.tabindex]': 'disabled ? null : _tabIndex',\n        '[attr.aria-required]': 'role ? required : null',\n        '[attr.aria-disabled]': 'disabled.toString()',\n        '[attr.aria-invalid]': 'errorState',\n        '[attr.aria-multiselectable]': 'multiple',\n        '[attr.role]': 'role',\n        '[class.mat-chip-list-disabled]': 'disabled',\n        '[class.mat-chip-list-invalid]': 'errorState',\n        '[class.mat-chip-list-required]': 'required',\n        '[attr.aria-orientation]': 'ariaOrientation',\n        'class': 'mat-chip-list',\n        '(focus)': 'focus()',\n        '(blur)': '_blur()',\n        '(keydown)': '_keydown($event)',\n        '[id]': '_uid'\n      },\n      providers: [{\n        provide: MatFormFieldControl,\n        useExisting: MatChipList\n      }],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\".mat-chip{position:relative;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0);border:none;-webkit-appearance:none;-moz-appearance:none}.mat-chip::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px) * -1)}.mat-standard-chip{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);display:inline-flex;padding:7px 12px;border-radius:16px;align-items:center;cursor:default;min-height:32px;height:1px}.mat-standard-chip._mat-animation-noopable{transition:none !important;animation:none !important}.mat-standard-chip .mat-chip-remove{border:none;-webkit-appearance:none;-moz-appearance:none;padding:0;background:none}.mat-standard-chip .mat-chip-remove.mat-icon,.mat-standard-chip .mat-chip-remove .mat-icon{width:18px;height:18px;font-size:18px}.mat-standard-chip::after{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit;opacity:0;content:\\\"\\\";pointer-events:none;transition:opacity 200ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-standard-chip:hover::after{opacity:.12}.mat-standard-chip:focus{outline:none}.mat-standard-chip:focus::after{opacity:.16}.cdk-high-contrast-active .mat-standard-chip{outline:solid 1px}.cdk-high-contrast-active .mat-standard-chip.mat-chip-selected{outline-width:3px}.mat-standard-chip.mat-chip-disabled::after{opacity:0}.mat-standard-chip.mat-chip-disabled .mat-chip-remove,.mat-standard-chip.mat-chip-disabled .mat-chip-trailing-icon{cursor:default}.mat-standard-chip.mat-chip-with-trailing-icon.mat-chip-with-avatar,.mat-standard-chip.mat-chip-with-avatar{padding-top:0;padding-bottom:0}.mat-standard-chip.mat-chip-with-trailing-icon.mat-chip-with-avatar{padding-right:8px;padding-left:0}[dir=rtl] .mat-standard-chip.mat-chip-with-trailing-icon.mat-chip-with-avatar{padding-left:8px;padding-right:0}.mat-standard-chip.mat-chip-with-trailing-icon{padding-top:7px;padding-bottom:7px;padding-right:8px;padding-left:12px}[dir=rtl] .mat-standard-chip.mat-chip-with-trailing-icon{padding-left:8px;padding-right:12px}.mat-standard-chip.mat-chip-with-avatar{padding-left:0;padding-right:12px}[dir=rtl] .mat-standard-chip.mat-chip-with-avatar{padding-right:0;padding-left:12px}.mat-standard-chip .mat-chip-avatar{width:24px;height:24px;margin-right:8px;margin-left:4px}[dir=rtl] .mat-standard-chip .mat-chip-avatar{margin-left:8px;margin-right:4px}.mat-standard-chip .mat-chip-remove,.mat-standard-chip .mat-chip-trailing-icon{width:18px;height:18px;cursor:pointer}.mat-standard-chip .mat-chip-remove,.mat-standard-chip .mat-chip-trailing-icon{margin-left:8px;margin-right:0}[dir=rtl] .mat-standard-chip .mat-chip-remove,[dir=rtl] .mat-standard-chip .mat-chip-trailing-icon{margin-right:8px;margin-left:0}.mat-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit;overflow:hidden;transform:translateZ(0)}.mat-chip-list-wrapper{display:flex;flex-direction:row;flex-wrap:wrap;align-items:center;margin:-4px}.mat-chip-list-wrapper input.mat-input-element,.mat-chip-list-wrapper .mat-standard-chip{margin:4px}.mat-chip-list-stacked .mat-chip-list-wrapper{flex-direction:column;align-items:flex-start}.mat-chip-list-stacked .mat-chip-list-wrapper .mat-standard-chip{width:100%}.mat-chip-avatar{border-radius:50%;justify-content:center;align-items:center;display:flex;overflow:hidden;object-fit:cover}input.mat-chip-input{width:150px;margin:4px;flex:1 0 150px}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1$1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i2.NgForm,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i2.FormGroupDirective,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i3.ErrorStateMatcher\n    }, {\n      type: i2.NgControl,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Self\n      }]\n    }];\n  }, {\n    role: [{\n      type: Input\n    }],\n    userAriaDescribedBy: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    errorStateMatcher: [{\n      type: Input\n    }],\n    multiple: [{\n      type: Input\n    }],\n    compareWith: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    required: [{\n      type: Input\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    ariaOrientation: [{\n      type: Input,\n      args: ['aria-orientation']\n    }],\n    selectable: [{\n      type: Input\n    }],\n    tabIndex: [{\n      type: Input\n    }],\n    change: [{\n      type: Output\n    }],\n    valueChange: [{\n      type: Output\n    }],\n    chips: [{\n      type: ContentChildren,\n      args: [MatChip, {\n        // We need to use `descendants: true`, because Ivy will no longer match\n        // indirect descendants if it's left as false.\n        descendants: true\n      }]\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Increasing integer for generating unique ids.\n\n\nlet nextUniqueId = 0;\n/**\n * Directive that adds chip-specific behaviors to an input element inside `<mat-form-field>`.\n * May be placed inside or outside of an `<mat-chip-list>`.\n */\n\nclass MatChipInput {\n  constructor(_elementRef, _defaultOptions) {\n    this._elementRef = _elementRef;\n    this._defaultOptions = _defaultOptions;\n    /** Whether the control is focused. */\n\n    this.focused = false;\n    this._addOnBlur = false;\n    /**\n     * The list of key codes that will trigger a chipEnd event.\n     *\n     * Defaults to `[ENTER]`.\n     */\n\n    this.separatorKeyCodes = this._defaultOptions.separatorKeyCodes;\n    /** Emitted when a chip is to be added. */\n\n    this.chipEnd = new EventEmitter();\n    /** The input's placeholder text. */\n\n    this.placeholder = '';\n    /** Unique id for the input. */\n\n    this.id = `mat-chip-list-input-${nextUniqueId++}`;\n    this._disabled = false;\n    this.inputElement = this._elementRef.nativeElement;\n  }\n  /** Register input for chip list */\n\n\n  set chipList(value) {\n    if (value) {\n      this._chipList = value;\n\n      this._chipList.registerInput(this);\n    }\n  }\n  /**\n   * Whether or not the chipEnd event will be emitted when the input is blurred.\n   */\n\n\n  get addOnBlur() {\n    return this._addOnBlur;\n  }\n\n  set addOnBlur(value) {\n    this._addOnBlur = coerceBooleanProperty(value);\n  }\n  /** Whether the input is disabled. */\n\n\n  get disabled() {\n    return this._disabled || this._chipList && this._chipList.disabled;\n  }\n\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value);\n  }\n  /** Whether the input is empty. */\n\n\n  get empty() {\n    return !this.inputElement.value;\n  }\n\n  ngOnChanges() {\n    this._chipList.stateChanges.next();\n  }\n\n  ngOnDestroy() {\n    this.chipEnd.complete();\n  }\n\n  ngAfterContentInit() {\n    this._focusLastChipOnBackspace = this.empty;\n  }\n  /** Utility method to make host definition/tests more clear. */\n\n\n  _keydown(event) {\n    if (event) {\n      // Allow the user's focus to escape when they're tabbing forward. Note that we don't\n      // want to do this when going backwards, because focus should go back to the first chip.\n      if (event.keyCode === TAB && !hasModifierKey(event, 'shiftKey')) {\n        this._chipList._allowFocusEscape();\n      } // To prevent the user from accidentally deleting chips when pressing BACKSPACE continuously,\n      // We focus the last chip on backspace only after the user has released the backspace button,\n      // and the input is empty (see behaviour in _keyup)\n\n\n      if (event.keyCode === BACKSPACE && this._focusLastChipOnBackspace) {\n        this._chipList._keyManager.setLastItemActive();\n\n        event.preventDefault();\n        return;\n      } else {\n        this._focusLastChipOnBackspace = false;\n      }\n    }\n\n    this._emitChipEnd(event);\n  }\n  /**\n   * Pass events to the keyboard manager. Available here for tests.\n   */\n\n\n  _keyup(event) {\n    // Allow user to move focus to chips next time he presses backspace\n    if (!this._focusLastChipOnBackspace && event.keyCode === BACKSPACE && this.empty) {\n      this._focusLastChipOnBackspace = true;\n      event.preventDefault();\n    }\n  }\n  /** Checks to see if the blur should emit the (chipEnd) event. */\n\n\n  _blur() {\n    if (this.addOnBlur) {\n      this._emitChipEnd();\n    }\n\n    this.focused = false; // Blur the chip list if it is not focused\n\n    if (!this._chipList.focused) {\n      this._chipList._blur();\n    }\n\n    this._chipList.stateChanges.next();\n  }\n\n  _focus() {\n    this.focused = true;\n    this._focusLastChipOnBackspace = this.empty;\n\n    this._chipList.stateChanges.next();\n  }\n  /** Checks to see if the (chipEnd) event needs to be emitted. */\n\n\n  _emitChipEnd(event) {\n    if (!this.inputElement.value && !!event) {\n      this._chipList._keydown(event);\n    }\n\n    if (!event || this._isSeparatorKey(event)) {\n      this.chipEnd.emit({\n        input: this.inputElement,\n        value: this.inputElement.value,\n        chipInput: this\n      });\n      event?.preventDefault();\n    }\n  }\n\n  _onInput() {\n    // Let chip list know whenever the value changes.\n    this._chipList.stateChanges.next();\n  }\n  /** Focuses the input. */\n\n\n  focus(options) {\n    this.inputElement.focus(options);\n  }\n  /** Clears the input */\n\n\n  clear() {\n    this.inputElement.value = '';\n    this._focusLastChipOnBackspace = true;\n  }\n  /** Checks whether a keycode is one of the configured separators. */\n\n\n  _isSeparatorKey(event) {\n    return !hasModifierKey(event) && new Set(this.separatorKeyCodes).has(event.keyCode);\n  }\n\n}\n\nMatChipInput.ɵfac = function MatChipInput_Factory(t) {\n  return new (t || MatChipInput)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MAT_CHIPS_DEFAULT_OPTIONS));\n};\n\nMatChipInput.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatChipInput,\n  selectors: [[\"input\", \"matChipInputFor\", \"\"]],\n  hostAttrs: [1, \"mat-chip-input\", \"mat-input-element\"],\n  hostVars: 5,\n  hostBindings: function MatChipInput_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"keydown\", function MatChipInput_keydown_HostBindingHandler($event) {\n        return ctx._keydown($event);\n      })(\"keyup\", function MatChipInput_keyup_HostBindingHandler($event) {\n        return ctx._keyup($event);\n      })(\"blur\", function MatChipInput_blur_HostBindingHandler() {\n        return ctx._blur();\n      })(\"focus\", function MatChipInput_focus_HostBindingHandler() {\n        return ctx._focus();\n      })(\"input\", function MatChipInput_input_HostBindingHandler() {\n        return ctx._onInput();\n      });\n    }\n\n    if (rf & 2) {\n      i0.ɵɵhostProperty(\"id\", ctx.id);\n      i0.ɵɵattribute(\"disabled\", ctx.disabled || null)(\"placeholder\", ctx.placeholder || null)(\"aria-invalid\", ctx._chipList && ctx._chipList.ngControl ? ctx._chipList.ngControl.invalid : null)(\"aria-required\", ctx._chipList && ctx._chipList.required || null);\n    }\n  },\n  inputs: {\n    chipList: [\"matChipInputFor\", \"chipList\"],\n    addOnBlur: [\"matChipInputAddOnBlur\", \"addOnBlur\"],\n    separatorKeyCodes: [\"matChipInputSeparatorKeyCodes\", \"separatorKeyCodes\"],\n    placeholder: \"placeholder\",\n    id: \"id\",\n    disabled: \"disabled\"\n  },\n  outputs: {\n    chipEnd: \"matChipInputTokenEnd\"\n  },\n  exportAs: [\"matChipInput\", \"matChipInputFor\"],\n  features: [i0.ɵɵNgOnChangesFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipInput, [{\n    type: Directive,\n    args: [{\n      selector: 'input[matChipInputFor]',\n      exportAs: 'matChipInput, matChipInputFor',\n      host: {\n        'class': 'mat-chip-input mat-input-element',\n        '(keydown)': '_keydown($event)',\n        '(keyup)': '_keyup($event)',\n        '(blur)': '_blur()',\n        '(focus)': '_focus()',\n        '(input)': '_onInput()',\n        '[id]': 'id',\n        '[attr.disabled]': 'disabled || null',\n        '[attr.placeholder]': 'placeholder || null',\n        '[attr.aria-invalid]': '_chipList && _chipList.ngControl ? _chipList.ngControl.invalid : null',\n        '[attr.aria-required]': '_chipList && _chipList.required || null'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_CHIPS_DEFAULT_OPTIONS]\n      }]\n    }];\n  }, {\n    chipList: [{\n      type: Input,\n      args: ['matChipInputFor']\n    }],\n    addOnBlur: [{\n      type: Input,\n      args: ['matChipInputAddOnBlur']\n    }],\n    separatorKeyCodes: [{\n      type: Input,\n      args: ['matChipInputSeparatorKeyCodes']\n    }],\n    chipEnd: [{\n      type: Output,\n      args: ['matChipInputTokenEnd']\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst CHIP_DECLARATIONS = [MatChipList, MatChip, MatChipInput, MatChipRemove, MatChipAvatar, MatChipTrailingIcon];\n\nclass MatChipsModule {}\n\nMatChipsModule.ɵfac = function MatChipsModule_Factory(t) {\n  return new (t || MatChipsModule)();\n};\n\nMatChipsModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatChipsModule\n});\nMatChipsModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [ErrorStateMatcher, {\n    provide: MAT_CHIPS_DEFAULT_OPTIONS,\n    useValue: {\n      separatorKeyCodes: [ENTER]\n    }\n  }],\n  imports: [MatCommonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipsModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule],\n      exports: CHIP_DECLARATIONS,\n      declarations: CHIP_DECLARATIONS,\n      providers: [ErrorStateMatcher, {\n        provide: MAT_CHIPS_DEFAULT_OPTIONS,\n        useValue: {\n          separatorKeyCodes: [ENTER]\n        }\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { MAT_CHIPS_DEFAULT_OPTIONS, MAT_CHIP_AVATAR, MAT_CHIP_REMOVE, MAT_CHIP_TRAILING_ICON, MatChip, MatChipAvatar, MatChipInput, MatChipList, MatChipListChange, MatChipRemove, MatChipSelectionChange, MatChipTrailingIcon, MatChipsModule };", "map": {"version": 3, "names": ["SPACE", "BACKSPACE", "DELETE", "TAB", "hasModifierKey", "ENTER", "i0", "InjectionToken", "Directive", "EventEmitter", "Optional", "Inject", "Attribute", "ContentChild", "Input", "Output", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Self", "ContentChildren", "NgModule", "i3", "mixinTabIndex", "mixinColor", "mixinDisableRipple", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MAT_RIPPLE_GLOBAL_OPTIONS", "mixinErrorState", "MatCommonModule", "ErrorStateMatcher", "coerceBooleanProperty", "i1", "DOCUMENT", "ANIMATION_MODULE_TYPE", "Subject", "merge", "take", "takeUntil", "startWith", "FocusKeyManager", "i1$1", "SelectionModel", "i2", "Validators", "MatFormFieldControl", "MatChipSelectionChange", "constructor", "source", "selected", "isUserInput", "MAT_CHIP_REMOVE", "MAT_CHIP_AVATAR", "MAT_CHIP_TRAILING_ICON", "MatChipBase", "_elementRef", "_MatChipMixinBase", "MatChipAvatar", "ɵfac", "ɵdir", "provide", "useExisting", "type", "args", "selector", "host", "providers", "MatChipTrailingIcon", "MatChip", "elementRef", "_ngZone", "platform", "globalRippleOptions", "_changeDetectorRef", "_document", "animationMode", "tabIndex", "_hasFocus", "chipListSelectable", "_chipListMultiple", "_chipListDisabled", "role", "_selected", "_selectable", "_disabled", "_removable", "_onFocus", "_onBlur", "selectionChange", "destroyed", "removed", "_addHostClassName", "_chipRippleTarget", "createElement", "classList", "add", "nativeElement", "append<PERSON><PERSON><PERSON>", "_chipRipple", "setupTriggerEvents", "rippleConfig", "_animationsDisabled", "parseInt", "rippleDisabled", "disabled", "disable<PERSON><PERSON><PERSON>", "value", "coerced<PERSON><PERSON><PERSON>", "_dispatchSelectionChange", "_value", "undefined", "textContent", "selectable", "removable", "ariaSelected", "toString", "basicChipAttrName", "element", "hasAttribute", "tagName", "toLowerCase", "ngOnDestroy", "emit", "chip", "_removeTriggerEvents", "select", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deselect", "selectViaInteraction", "toggleSelected", "focus", "next", "remove", "_handleClick", "event", "preventDefault", "_handleKeydown", "keyCode", "_blur", "onStable", "pipe", "subscribe", "run", "ElementRef", "NgZone", "Platform", "ChangeDetectorRef", "inputs", "exportAs", "decorators", "avatar", "trailingIcon", "removeIcon", "MatChipRemove", "_parentChip", "nodeName", "setAttribute", "parentChip", "stopPropagation", "MAT_CHIPS_DEFAULT_OPTIONS", "_MatChipListBase", "_defaultErrorStateMatcher", "_parentForm", "_parentFormGroup", "ngControl", "stateChanges", "nextUniqueId$1", "MatChipListChange", "MatChipList", "_dir", "controlType", "_lastDestroyedChipIndex", "_destroyed", "_uid", "_tabIndex", "_userTabIndex", "_onTouched", "_onChange", "_multiple", "_compareWith", "o1", "o2", "ariaOrientation", "change", "valueChange", "valueAccessor", "multiple", "_selectionModel", "_explicitRole", "empty", "_syncChipsState", "compareWith", "fn", "_initializeSelection", "writeValue", "id", "_chipInput", "required", "_required", "control", "hasValidator", "placeholder", "_placeholder", "focused", "_hasFocusedChip", "chips", "length", "shouldLabelFloat", "chipSelectionChanges", "map", "chipFocusChanges", "chipBlurChanges", "chipRemoveChanges", "ngAfterContentInit", "_keyManager", "withWrap", "withVerticalOrientation", "withHomeAndEnd", "withHorizontalOrientation", "dir", "tabOut", "_allowFocusEscape", "changes", "Promise", "resolve", "then", "_resetChips", "_updateTabIndex", "_updateFocusForDestroyedChips", "ngOnInit", "ngDoCheck", "updateErrorState", "complete", "_dropSubscriptions", "registerInput", "inputElement", "setDescribedByIds", "ids", "join", "removeAttribute", "_setSelectionByValue", "registerOnChange", "registerOnTouched", "setDisabledState", "isDisabled", "onContainerClick", "_originatesFromChip", "options", "setFirstItemActive", "_focusInput", "_keydown", "target", "contains", "onKeydown", "newChipIndex", "Math", "min", "setActiveItem", "_isValidIndex", "index", "_clearSelection", "for<PERSON>ach", "Array", "isArray", "currentValue", "_selectValue", "_sortValues", "correspondingChip", "find", "skip", "clear", "_propagateChanges", "fallback<PERSON><PERSON><PERSON>", "valueToEmit", "setTimeout", "_markAsTouched", "_listenToChipsFocus", "_listenToChipsSelection", "_listenToChipsRemoved", "_chipFocusSubscription", "unsubscribe", "_chipBlurSubscription", "_chipSelectionSubscription", "_chipRemoveSubscription", "isSelected", "chipIndex", "toArray", "indexOf", "updateActiveItem", "currentElement", "parentElement", "some", "Directionality", "NgForm", "FormGroupDirective", "NgControl", "ɵcmp", "template", "encapsulation", "None", "changeDetection", "OnPush", "styles", "userAriaDescribedBy", "errorStateMatcher", "descendants", "nextUniqueId", "MatChipInput", "_defaultOptions", "_addOnBlur", "separatorKeyCodes", "chipEnd", "chipList", "_chipList", "addOnBlur", "ngOnChanges", "_focusLastChipOnBackspace", "setLastItemActive", "_emitChipEnd", "_keyup", "_focus", "_isSeparator<PERSON>ey", "input", "chipInput", "_onInput", "Set", "has", "CHIP_DECLARATIONS", "MatChipsModule", "ɵmod", "ɵinj", "useValue", "imports", "exports", "declarations"], "sources": ["R:/chateye/FrontendAngular/node_modules/@angular/material/fesm2020/chips.mjs"], "sourcesContent": ["import { <PERSON>AC<PERSON>, BACKSPACE, DELE<PERSON>, TAB, hasMod<PERSON><PERSON><PERSON>, ENT<PERSON> } from '@angular/cdk/keycodes';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, EventEmitter, Optional, Inject, Attribute, ContentChild, Input, Output, Component, ViewEncapsulation, ChangeDetectionStrategy, Self, ContentChildren, NgModule } from '@angular/core';\nimport * as i3 from '@angular/material/core';\nimport { mixinTabIndex, mixinColor, mixinDisableRipple, RippleRenderer, MAT_RIPPLE_GLOBAL_OPTIONS, mixinErrorState, MatCommonModule, ErrorStateMatcher } from '@angular/material/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i1 from '@angular/cdk/platform';\nimport { DOCUMENT } from '@angular/common';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { Subject, merge } from 'rxjs';\nimport { take, takeUntil, startWith } from 'rxjs/operators';\nimport { FocusKeyManager } from '@angular/cdk/a11y';\nimport * as i1$1 from '@angular/cdk/bidi';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport * as i2 from '@angular/forms';\nimport { Validators } from '@angular/forms';\nimport { MatFormFieldControl } from '@angular/material/form-field';\n\n/** Event object emitted by MatChip when selected or deselected. */\nclass MatChipSelectionChange {\n    constructor(\n    /** Reference to the chip that emitted the event. */\n    source, \n    /** Whether the chip that emitted the event is selected. */\n    selected, \n    /** Whether the selection change was a result of a user interaction. */\n    isUserInput = false) {\n        this.source = source;\n        this.selected = selected;\n        this.isUserInput = isUserInput;\n    }\n}\n/**\n * Injection token that can be used to reference instances of `MatChipRemove`. It serves as\n * alternative token to the actual `MatChipRemove` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_CHIP_REMOVE = new InjectionToken('MatChipRemove');\n/**\n * Injection token that can be used to reference instances of `MatChipAvatar`. It serves as\n * alternative token to the actual `MatChipAvatar` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_CHIP_AVATAR = new InjectionToken('MatChipAvatar');\n/**\n * Injection token that can be used to reference instances of `MatChipTrailingIcon`. It serves as\n * alternative token to the actual `MatChipTrailingIcon` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_CHIP_TRAILING_ICON = new InjectionToken('MatChipTrailingIcon');\n// Boilerplate for applying mixins to MatChip.\n/** @docs-private */\nclass MatChipBase {\n    constructor(_elementRef) {\n        this._elementRef = _elementRef;\n    }\n}\nconst _MatChipMixinBase = mixinTabIndex(mixinColor(mixinDisableRipple(MatChipBase), 'primary'), -1);\n/**\n * Dummy directive to add CSS class to chip avatar.\n * @docs-private\n */\nclass MatChipAvatar {\n}\nMatChipAvatar.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatChipAvatar, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatChipAvatar.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatChipAvatar, selector: \"mat-chip-avatar, [matChipAvatar]\", host: { classAttribute: \"mat-chip-avatar\" }, providers: [{ provide: MAT_CHIP_AVATAR, useExisting: MatChipAvatar }], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatChipAvatar, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-chip-avatar, [matChipAvatar]',\n                    host: { 'class': 'mat-chip-avatar' },\n                    providers: [{ provide: MAT_CHIP_AVATAR, useExisting: MatChipAvatar }],\n                }]\n        }] });\n/**\n * Dummy directive to add CSS class to chip trailing icon.\n * @docs-private\n */\nclass MatChipTrailingIcon {\n}\nMatChipTrailingIcon.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatChipTrailingIcon, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatChipTrailingIcon.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatChipTrailingIcon, selector: \"mat-chip-trailing-icon, [matChipTrailingIcon]\", host: { classAttribute: \"mat-chip-trailing-icon\" }, providers: [{ provide: MAT_CHIP_TRAILING_ICON, useExisting: MatChipTrailingIcon }], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatChipTrailingIcon, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-chip-trailing-icon, [matChipTrailingIcon]',\n                    host: { 'class': 'mat-chip-trailing-icon' },\n                    providers: [{ provide: MAT_CHIP_TRAILING_ICON, useExisting: MatChipTrailingIcon }],\n                }]\n        }] });\n/** Material Design styled chip directive. Used inside the MatChipList component. */\nclass MatChip extends _MatChipMixinBase {\n    constructor(elementRef, _ngZone, platform, globalRippleOptions, _changeDetectorRef, _document, animationMode, tabIndex) {\n        super(elementRef);\n        this._ngZone = _ngZone;\n        this._changeDetectorRef = _changeDetectorRef;\n        /** Whether the chip has focus. */\n        this._hasFocus = false;\n        /** Whether the chip list is selectable */\n        this.chipListSelectable = true;\n        /** Whether the chip list is in multi-selection mode. */\n        this._chipListMultiple = false;\n        /** Whether the chip list as a whole is disabled. */\n        this._chipListDisabled = false;\n        /** ARIA role that should be applied to the chip. */\n        this.role = 'option';\n        this._selected = false;\n        this._selectable = true;\n        this._disabled = false;\n        this._removable = true;\n        /** Emits when the chip is focused. */\n        this._onFocus = new Subject();\n        /** Emits when the chip is blurred. */\n        this._onBlur = new Subject();\n        /** Emitted when the chip is selected or deselected. */\n        this.selectionChange = new EventEmitter();\n        /** Emitted when the chip is destroyed. */\n        this.destroyed = new EventEmitter();\n        /** Emitted when a chip is to be removed. */\n        this.removed = new EventEmitter();\n        this._addHostClassName();\n        // Dynamically create the ripple target, append it within the chip, and use it as the\n        // chip's ripple target. Adding the class '.mat-chip-ripple' ensures that it will have\n        // the proper styles.\n        this._chipRippleTarget = _document.createElement('div');\n        this._chipRippleTarget.classList.add('mat-chip-ripple');\n        this._elementRef.nativeElement.appendChild(this._chipRippleTarget);\n        this._chipRipple = new RippleRenderer(this, _ngZone, this._chipRippleTarget, platform);\n        this._chipRipple.setupTriggerEvents(elementRef);\n        this.rippleConfig = globalRippleOptions || {};\n        this._animationsDisabled = animationMode === 'NoopAnimations';\n        this.tabIndex = tabIndex != null ? parseInt(tabIndex) || -1 : -1;\n    }\n    /**\n     * Whether ripples are disabled on interaction\n     * @docs-private\n     */\n    get rippleDisabled() {\n        return (this.disabled ||\n            this.disableRipple ||\n            this._animationsDisabled ||\n            !!this.rippleConfig.disabled);\n    }\n    /** Whether the chip is selected. */\n    get selected() {\n        return this._selected;\n    }\n    set selected(value) {\n        const coercedValue = coerceBooleanProperty(value);\n        if (coercedValue !== this._selected) {\n            this._selected = coercedValue;\n            this._dispatchSelectionChange();\n        }\n    }\n    /** The value of the chip. Defaults to the content inside `<mat-chip>` tags. */\n    get value() {\n        return this._value !== undefined ? this._value : this._elementRef.nativeElement.textContent;\n    }\n    set value(value) {\n        this._value = value;\n    }\n    /**\n     * Whether or not the chip is selectable. When a chip is not selectable,\n     * changes to its selected state are always ignored. By default a chip is\n     * selectable, and it becomes non-selectable if its parent chip list is\n     * not selectable.\n     */\n    get selectable() {\n        return this._selectable && this.chipListSelectable;\n    }\n    set selectable(value) {\n        this._selectable = coerceBooleanProperty(value);\n    }\n    /** Whether the chip is disabled. */\n    get disabled() {\n        return this._chipListDisabled || this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n    }\n    /**\n     * Determines whether or not the chip displays the remove styling and emits (removed) events.\n     */\n    get removable() {\n        return this._removable;\n    }\n    set removable(value) {\n        this._removable = coerceBooleanProperty(value);\n    }\n    /** The ARIA selected applied to the chip. */\n    get ariaSelected() {\n        // Remove the `aria-selected` when the chip is deselected in single-selection mode, because\n        // it adds noise to NVDA users where \"not selected\" will be read out for each chip.\n        return this.selectable && (this._chipListMultiple || this.selected)\n            ? this.selected.toString()\n            : null;\n    }\n    _addHostClassName() {\n        const basicChipAttrName = 'mat-basic-chip';\n        const element = this._elementRef.nativeElement;\n        if (element.hasAttribute(basicChipAttrName) ||\n            element.tagName.toLowerCase() === basicChipAttrName) {\n            element.classList.add(basicChipAttrName);\n            return;\n        }\n        else {\n            element.classList.add('mat-standard-chip');\n        }\n    }\n    ngOnDestroy() {\n        this.destroyed.emit({ chip: this });\n        this._chipRipple._removeTriggerEvents();\n    }\n    /** Selects the chip. */\n    select() {\n        if (!this._selected) {\n            this._selected = true;\n            this._dispatchSelectionChange();\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /** Deselects the chip. */\n    deselect() {\n        if (this._selected) {\n            this._selected = false;\n            this._dispatchSelectionChange();\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /** Select this chip and emit selected event */\n    selectViaInteraction() {\n        if (!this._selected) {\n            this._selected = true;\n            this._dispatchSelectionChange(true);\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /** Toggles the current selected state of this chip. */\n    toggleSelected(isUserInput = false) {\n        this._selected = !this.selected;\n        this._dispatchSelectionChange(isUserInput);\n        this._changeDetectorRef.markForCheck();\n        return this.selected;\n    }\n    /** Allows for programmatic focusing of the chip. */\n    focus() {\n        if (!this._hasFocus) {\n            this._elementRef.nativeElement.focus();\n            this._onFocus.next({ chip: this });\n        }\n        this._hasFocus = true;\n    }\n    /**\n     * Allows for programmatic removal of the chip. Called by the MatChipList when the DELETE or\n     * BACKSPACE keys are pressed.\n     *\n     * Informs any listeners of the removal request. Does not remove the chip from the DOM.\n     */\n    remove() {\n        if (this.removable) {\n            this.removed.emit({ chip: this });\n        }\n    }\n    /** Handles click events on the chip. */\n    _handleClick(event) {\n        if (this.disabled) {\n            event.preventDefault();\n        }\n    }\n    /** Handle custom key presses. */\n    _handleKeydown(event) {\n        if (this.disabled) {\n            return;\n        }\n        switch (event.keyCode) {\n            case DELETE:\n            case BACKSPACE:\n                // If we are removable, remove the focused chip\n                this.remove();\n                // Always prevent so page navigation does not occur\n                event.preventDefault();\n                break;\n            case SPACE:\n                // If we are selectable, toggle the focused chip\n                if (this.selectable) {\n                    this.toggleSelected(true);\n                }\n                // Always prevent space from scrolling the page since the list has focus\n                event.preventDefault();\n                break;\n        }\n    }\n    _blur() {\n        // When animations are enabled, Angular may end up removing the chip from the DOM a little\n        // earlier than usual, causing it to be blurred and throwing off the logic in the chip list\n        // that moves focus not the next item. To work around the issue, we defer marking the chip\n        // as not focused until the next time the zone stabilizes.\n        this._ngZone.onStable.pipe(take(1)).subscribe(() => {\n            this._ngZone.run(() => {\n                this._hasFocus = false;\n                this._onBlur.next({ chip: this });\n            });\n        });\n    }\n    _dispatchSelectionChange(isUserInput = false) {\n        this.selectionChange.emit({\n            source: this,\n            isUserInput,\n            selected: this._selected,\n        });\n    }\n}\nMatChip.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatChip, deps: [{ token: i0.ElementRef }, { token: i0.NgZone }, { token: i1.Platform }, { token: MAT_RIPPLE_GLOBAL_OPTIONS, optional: true }, { token: i0.ChangeDetectorRef }, { token: DOCUMENT }, { token: ANIMATION_MODULE_TYPE, optional: true }, { token: 'tabindex', attribute: true }], target: i0.ɵɵFactoryTarget.Directive });\nMatChip.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatChip, selector: \"mat-basic-chip, [mat-basic-chip], mat-chip, [mat-chip]\", inputs: { color: \"color\", disableRipple: \"disableRipple\", tabIndex: \"tabIndex\", role: \"role\", selected: \"selected\", value: \"value\", selectable: \"selectable\", disabled: \"disabled\", removable: \"removable\" }, outputs: { selectionChange: \"selectionChange\", destroyed: \"destroyed\", removed: \"removed\" }, host: { listeners: { \"click\": \"_handleClick($event)\", \"keydown\": \"_handleKeydown($event)\", \"focus\": \"focus()\", \"blur\": \"_blur()\" }, properties: { \"attr.tabindex\": \"disabled ? null : tabIndex\", \"attr.role\": \"role\", \"class.mat-chip-selected\": \"selected\", \"class.mat-chip-with-avatar\": \"avatar\", \"class.mat-chip-with-trailing-icon\": \"trailingIcon || removeIcon\", \"class.mat-chip-disabled\": \"disabled\", \"class._mat-animation-noopable\": \"_animationsDisabled\", \"attr.disabled\": \"disabled || null\", \"attr.aria-disabled\": \"disabled.toString()\", \"attr.aria-selected\": \"ariaSelected\" }, classAttribute: \"mat-chip mat-focus-indicator\" }, queries: [{ propertyName: \"avatar\", first: true, predicate: MAT_CHIP_AVATAR, descendants: true }, { propertyName: \"trailingIcon\", first: true, predicate: MAT_CHIP_TRAILING_ICON, descendants: true }, { propertyName: \"removeIcon\", first: true, predicate: MAT_CHIP_REMOVE, descendants: true }], exportAs: [\"matChip\"], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatChip, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `mat-basic-chip, [mat-basic-chip], mat-chip, [mat-chip]`,\n                    inputs: ['color', 'disableRipple', 'tabIndex'],\n                    exportAs: 'matChip',\n                    host: {\n                        'class': 'mat-chip mat-focus-indicator',\n                        '[attr.tabindex]': 'disabled ? null : tabIndex',\n                        '[attr.role]': 'role',\n                        '[class.mat-chip-selected]': 'selected',\n                        '[class.mat-chip-with-avatar]': 'avatar',\n                        '[class.mat-chip-with-trailing-icon]': 'trailingIcon || removeIcon',\n                        '[class.mat-chip-disabled]': 'disabled',\n                        '[class._mat-animation-noopable]': '_animationsDisabled',\n                        '[attr.disabled]': 'disabled || null',\n                        '[attr.aria-disabled]': 'disabled.toString()',\n                        '[attr.aria-selected]': 'ariaSelected',\n                        '(click)': '_handleClick($event)',\n                        '(keydown)': '_handleKeydown($event)',\n                        '(focus)': 'focus()',\n                        '(blur)': '_blur()',\n                    },\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.NgZone }, { type: i1.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_RIPPLE_GLOBAL_OPTIONS]\n                }] }, { type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['tabindex']\n                }] }]; }, propDecorators: { avatar: [{\n                type: ContentChild,\n                args: [MAT_CHIP_AVATAR]\n            }], trailingIcon: [{\n                type: ContentChild,\n                args: [MAT_CHIP_TRAILING_ICON]\n            }], removeIcon: [{\n                type: ContentChild,\n                args: [MAT_CHIP_REMOVE]\n            }], role: [{\n                type: Input\n            }], selected: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], selectable: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], removable: [{\n                type: Input\n            }], selectionChange: [{\n                type: Output\n            }], destroyed: [{\n                type: Output\n            }], removed: [{\n                type: Output\n            }] } });\n/**\n * Applies proper (click) support and adds styling for use with the Material Design \"cancel\" icon\n * available at https://material.io/icons/#ic_cancel.\n *\n * Example:\n *\n *     `<mat-chip>\n *       <mat-icon matChipRemove>cancel</mat-icon>\n *     </mat-chip>`\n *\n * You *may* use a custom icon, but you may need to override the `mat-chip-remove` positioning\n * styles to properly center the icon within the chip.\n */\nclass MatChipRemove {\n    constructor(_parentChip, elementRef) {\n        this._parentChip = _parentChip;\n        if (elementRef.nativeElement.nodeName === 'BUTTON') {\n            elementRef.nativeElement.setAttribute('type', 'button');\n        }\n    }\n    /** Calls the parent chip's public `remove()` method if applicable. */\n    _handleClick(event) {\n        const parentChip = this._parentChip;\n        if (parentChip.removable && !parentChip.disabled) {\n            parentChip.remove();\n        }\n        // We need to stop event propagation because otherwise the event will bubble up to the\n        // form field and cause the `onContainerClick` method to be invoked. This method would then\n        // reset the focused chip that has been focused after chip removal. Usually the parent\n        // the parent click listener of the `MatChip` would prevent propagation, but it can happen\n        // that the chip is being removed before the event bubbles up.\n        event.stopPropagation();\n        event.preventDefault();\n    }\n}\nMatChipRemove.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatChipRemove, deps: [{ token: MatChip }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive });\nMatChipRemove.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatChipRemove, selector: \"[matChipRemove]\", host: { listeners: { \"click\": \"_handleClick($event)\" }, classAttribute: \"mat-chip-remove mat-chip-trailing-icon\" }, providers: [{ provide: MAT_CHIP_REMOVE, useExisting: MatChipRemove }], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatChipRemove, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matChipRemove]',\n                    host: {\n                        'class': 'mat-chip-remove mat-chip-trailing-icon',\n                        '(click)': '_handleClick($event)',\n                    },\n                    providers: [{ provide: MAT_CHIP_REMOVE, useExisting: MatChipRemove }],\n                }]\n        }], ctorParameters: function () { return [{ type: MatChip }, { type: i0.ElementRef }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Injection token to be used to override the default options for the chips module. */\nconst MAT_CHIPS_DEFAULT_OPTIONS = new InjectionToken('mat-chips-default-options');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to MatChipList.\n/** @docs-private */\nconst _MatChipListBase = mixinErrorState(class {\n    constructor(_defaultErrorStateMatcher, _parentForm, _parentFormGroup, \n    /**\n     * Form control bound to the component.\n     * Implemented as part of `MatFormFieldControl`.\n     * @docs-private\n     */\n    ngControl) {\n        this._defaultErrorStateMatcher = _defaultErrorStateMatcher;\n        this._parentForm = _parentForm;\n        this._parentFormGroup = _parentFormGroup;\n        this.ngControl = ngControl;\n        /**\n         * Emits whenever the component state changes and should cause the parent\n         * form-field to update. Implemented as part of `MatFormFieldControl`.\n         * @docs-private\n         */\n        this.stateChanges = new Subject();\n    }\n});\n// Increasing integer for generating unique ids for chip-list components.\nlet nextUniqueId$1 = 0;\n/** Change event object that is emitted when the chip list value has changed. */\nclass MatChipListChange {\n    constructor(\n    /** Chip list that emitted the event. */\n    source, \n    /** Value of the chip list when the event was emitted. */\n    value) {\n        this.source = source;\n        this.value = value;\n    }\n}\n/**\n * A material design chips component (named ChipList for its similarity to the List component).\n */\nclass MatChipList extends _MatChipListBase {\n    constructor(_elementRef, _changeDetectorRef, _dir, _parentForm, _parentFormGroup, _defaultErrorStateMatcher, ngControl) {\n        super(_defaultErrorStateMatcher, _parentForm, _parentFormGroup, ngControl);\n        this._elementRef = _elementRef;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._dir = _dir;\n        /**\n         * Implemented as part of MatFormFieldControl.\n         * @docs-private\n         */\n        this.controlType = 'mat-chip-list';\n        /**\n         * When a chip is destroyed, we store the index of the destroyed chip until the chips\n         * query list notifies about the update. This is necessary because we cannot determine an\n         * appropriate chip that should receive focus until the array of chips updated completely.\n         */\n        this._lastDestroyedChipIndex = null;\n        /** Subject that emits when the component has been destroyed. */\n        this._destroyed = new Subject();\n        /** Uid of the chip list */\n        this._uid = `mat-chip-list-${nextUniqueId$1++}`;\n        /** Tab index for the chip list. */\n        this._tabIndex = 0;\n        /**\n         * User defined tab index.\n         * When it is not null, use user defined tab index. Otherwise use _tabIndex\n         */\n        this._userTabIndex = null;\n        /** Function when touched */\n        this._onTouched = () => { };\n        /** Function when changed */\n        this._onChange = () => { };\n        this._multiple = false;\n        this._compareWith = (o1, o2) => o1 === o2;\n        this._disabled = false;\n        /** Orientation of the chip list. */\n        this.ariaOrientation = 'horizontal';\n        this._selectable = true;\n        /** Event emitted when the selected chip list value has been changed by the user. */\n        this.change = new EventEmitter();\n        /**\n         * Event that emits whenever the raw value of the chip-list changes. This is here primarily\n         * to facilitate the two-way binding for the `value` input.\n         * @docs-private\n         */\n        this.valueChange = new EventEmitter();\n        if (this.ngControl) {\n            this.ngControl.valueAccessor = this;\n        }\n    }\n    /** The array of selected chips inside chip list. */\n    get selected() {\n        return this.multiple ? this._selectionModel?.selected || [] : this._selectionModel?.selected[0];\n    }\n    /** The ARIA role applied to the chip list. */\n    get role() {\n        if (this._explicitRole) {\n            return this._explicitRole;\n        }\n        return this.empty ? null : 'listbox';\n    }\n    set role(role) {\n        this._explicitRole = role;\n    }\n    /** Whether the user should be allowed to select multiple chips. */\n    get multiple() {\n        return this._multiple;\n    }\n    set multiple(value) {\n        this._multiple = coerceBooleanProperty(value);\n        this._syncChipsState();\n    }\n    /**\n     * A function to compare the option values with the selected values. The first argument\n     * is a value from an option. The second is a value from the selection. A boolean\n     * should be returned.\n     */\n    get compareWith() {\n        return this._compareWith;\n    }\n    set compareWith(fn) {\n        this._compareWith = fn;\n        if (this._selectionModel) {\n            // A different comparator means the selection could change.\n            this._initializeSelection();\n        }\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get value() {\n        return this._value;\n    }\n    set value(value) {\n        this.writeValue(value);\n        this._value = value;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get id() {\n        return this._chipInput ? this._chipInput.id : this._uid;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get required() {\n        return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n    }\n    set required(value) {\n        this._required = coerceBooleanProperty(value);\n        this.stateChanges.next();\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get placeholder() {\n        return this._chipInput ? this._chipInput.placeholder : this._placeholder;\n    }\n    set placeholder(value) {\n        this._placeholder = value;\n        this.stateChanges.next();\n    }\n    /** Whether any chips or the matChipInput inside of this chip-list has focus. */\n    get focused() {\n        return (this._chipInput && this._chipInput.focused) || this._hasFocusedChip();\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get empty() {\n        return (!this._chipInput || this._chipInput.empty) && (!this.chips || this.chips.length === 0);\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get shouldLabelFloat() {\n        return !this.empty || this.focused;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get disabled() {\n        return this.ngControl ? !!this.ngControl.disabled : this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n        this._syncChipsState();\n    }\n    /**\n     * Whether or not this chip list is selectable. When a chip list is not selectable,\n     * the selected states for all the chips inside the chip list are always ignored.\n     */\n    get selectable() {\n        return this._selectable;\n    }\n    set selectable(value) {\n        this._selectable = coerceBooleanProperty(value);\n        this._syncChipsState();\n    }\n    set tabIndex(value) {\n        this._userTabIndex = value;\n        this._tabIndex = value;\n    }\n    /** Combined stream of all of the child chips' selection change events. */\n    get chipSelectionChanges() {\n        return merge(...this.chips.map(chip => chip.selectionChange));\n    }\n    /** Combined stream of all of the child chips' focus change events. */\n    get chipFocusChanges() {\n        return merge(...this.chips.map(chip => chip._onFocus));\n    }\n    /** Combined stream of all of the child chips' blur change events. */\n    get chipBlurChanges() {\n        return merge(...this.chips.map(chip => chip._onBlur));\n    }\n    /** Combined stream of all of the child chips' remove change events. */\n    get chipRemoveChanges() {\n        return merge(...this.chips.map(chip => chip.destroyed));\n    }\n    ngAfterContentInit() {\n        this._keyManager = new FocusKeyManager(this.chips)\n            .withWrap()\n            .withVerticalOrientation()\n            .withHomeAndEnd()\n            .withHorizontalOrientation(this._dir ? this._dir.value : 'ltr');\n        if (this._dir) {\n            this._dir.change\n                .pipe(takeUntil(this._destroyed))\n                .subscribe(dir => this._keyManager.withHorizontalOrientation(dir));\n        }\n        this._keyManager.tabOut.pipe(takeUntil(this._destroyed)).subscribe(() => {\n            this._allowFocusEscape();\n        });\n        // When the list changes, re-subscribe\n        this.chips.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n            if (this.disabled || !this.selectable) {\n                // Since this happens after the content has been\n                // checked, we need to defer it to the next tick.\n                Promise.resolve().then(() => {\n                    this._syncChipsState();\n                });\n            }\n            this._resetChips();\n            // Reset chips selected/deselected status\n            this._initializeSelection();\n            // Check to see if we need to update our tab index\n            this._updateTabIndex();\n            // Check to see if we have a destroyed chip and need to refocus\n            this._updateFocusForDestroyedChips();\n            this.stateChanges.next();\n        });\n    }\n    ngOnInit() {\n        this._selectionModel = new SelectionModel(this.multiple, undefined, false);\n        this.stateChanges.next();\n    }\n    ngDoCheck() {\n        if (this.ngControl) {\n            // We need to re-evaluate this on every change detection cycle, because there are some\n            // error triggers that we can't subscribe to (e.g. parent form submissions). This means\n            // that whatever logic is in here has to be super lean or we risk destroying the performance.\n            this.updateErrorState();\n            if (this.ngControl.disabled !== this._disabled) {\n                this.disabled = !!this.ngControl.disabled;\n            }\n        }\n    }\n    ngOnDestroy() {\n        this._destroyed.next();\n        this._destroyed.complete();\n        this.stateChanges.complete();\n        this._dropSubscriptions();\n    }\n    /** Associates an HTML input element with this chip list. */\n    registerInput(inputElement) {\n        this._chipInput = inputElement;\n        // We use this attribute to match the chip list to its input in test harnesses.\n        // Set the attribute directly here to avoid \"changed after checked\" errors.\n        this._elementRef.nativeElement.setAttribute('data-mat-chip-input', inputElement.id);\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    setDescribedByIds(ids) {\n        if (ids.length) {\n            this._elementRef.nativeElement.setAttribute('aria-describedby', ids.join(' '));\n        }\n        else {\n            this._elementRef.nativeElement.removeAttribute('aria-describedby');\n        }\n    }\n    // Implemented as part of ControlValueAccessor.\n    writeValue(value) {\n        if (this.chips) {\n            this._setSelectionByValue(value, false);\n        }\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnChange(fn) {\n        this._onChange = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnTouched(fn) {\n        this._onTouched = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n        this.stateChanges.next();\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    onContainerClick(event) {\n        if (!this._originatesFromChip(event)) {\n            this.focus();\n        }\n    }\n    /**\n     * Focuses the first non-disabled chip in this chip list, or the associated input when there\n     * are no eligible chips.\n     */\n    focus(options) {\n        if (this.disabled) {\n            return;\n        }\n        // TODO: ARIA says this should focus the first `selected` chip if any are selected.\n        // Focus on first element if there's no chipInput inside chip-list\n        if (this._chipInput && this._chipInput.focused) {\n            // do nothing\n        }\n        else if (this.chips.length > 0) {\n            this._keyManager.setFirstItemActive();\n            this.stateChanges.next();\n        }\n        else {\n            this._focusInput(options);\n            this.stateChanges.next();\n        }\n    }\n    /** Attempt to focus an input if we have one. */\n    _focusInput(options) {\n        if (this._chipInput) {\n            this._chipInput.focus(options);\n        }\n    }\n    /**\n     * Pass events to the keyboard manager. Available here for tests.\n     */\n    _keydown(event) {\n        const target = event.target;\n        if (target && target.classList.contains('mat-chip')) {\n            this._keyManager.onKeydown(event);\n            this.stateChanges.next();\n        }\n    }\n    /**\n     * Check the tab index as you should not be allowed to focus an empty list.\n     */\n    _updateTabIndex() {\n        // If we have 0 chips, we should not allow keyboard focus\n        this._tabIndex = this._userTabIndex || (this.chips.length === 0 ? -1 : 0);\n    }\n    /**\n     * If the amount of chips changed, we need to update the\n     * key manager state and focus the next closest chip.\n     */\n    _updateFocusForDestroyedChips() {\n        // Move focus to the closest chip. If no other chips remain, focus the chip-list itself.\n        if (this._lastDestroyedChipIndex != null) {\n            if (this.chips.length) {\n                const newChipIndex = Math.min(this._lastDestroyedChipIndex, this.chips.length - 1);\n                this._keyManager.setActiveItem(newChipIndex);\n            }\n            else {\n                this.focus();\n            }\n        }\n        this._lastDestroyedChipIndex = null;\n    }\n    /**\n     * Utility to ensure all indexes are valid.\n     *\n     * @param index The index to be checked.\n     * @returns True if the index is valid for our list of chips.\n     */\n    _isValidIndex(index) {\n        return index >= 0 && index < this.chips.length;\n    }\n    _setSelectionByValue(value, isUserInput = true) {\n        this._clearSelection();\n        this.chips.forEach(chip => chip.deselect());\n        if (Array.isArray(value)) {\n            value.forEach(currentValue => this._selectValue(currentValue, isUserInput));\n            this._sortValues();\n        }\n        else {\n            const correspondingChip = this._selectValue(value, isUserInput);\n            // Shift focus to the active item. Note that we shouldn't do this in multiple\n            // mode, because we don't know what chip the user interacted with last.\n            if (correspondingChip) {\n                if (isUserInput) {\n                    this._keyManager.setActiveItem(correspondingChip);\n                }\n            }\n        }\n    }\n    /**\n     * Finds and selects the chip based on its value.\n     * @returns Chip that has the corresponding value.\n     */\n    _selectValue(value, isUserInput = true) {\n        const correspondingChip = this.chips.find(chip => {\n            return chip.value != null && this._compareWith(chip.value, value);\n        });\n        if (correspondingChip) {\n            isUserInput ? correspondingChip.selectViaInteraction() : correspondingChip.select();\n            this._selectionModel.select(correspondingChip);\n        }\n        return correspondingChip;\n    }\n    _initializeSelection() {\n        // Defer setting the value in order to avoid the \"Expression\n        // has changed after it was checked\" errors from Angular.\n        Promise.resolve().then(() => {\n            if (this.ngControl || this._value) {\n                this._setSelectionByValue(this.ngControl ? this.ngControl.value : this._value, false);\n                this.stateChanges.next();\n            }\n        });\n    }\n    /**\n     * Deselects every chip in the list.\n     * @param skip Chip that should not be deselected.\n     */\n    _clearSelection(skip) {\n        this._selectionModel.clear();\n        this.chips.forEach(chip => {\n            if (chip !== skip) {\n                chip.deselect();\n            }\n        });\n        this.stateChanges.next();\n    }\n    /**\n     * Sorts the model values, ensuring that they keep the same\n     * order that they have in the panel.\n     */\n    _sortValues() {\n        if (this._multiple) {\n            this._selectionModel.clear();\n            this.chips.forEach(chip => {\n                if (chip.selected) {\n                    this._selectionModel.select(chip);\n                }\n            });\n            this.stateChanges.next();\n        }\n    }\n    /** Emits change event to set the model value. */\n    _propagateChanges(fallbackValue) {\n        let valueToEmit = null;\n        if (Array.isArray(this.selected)) {\n            valueToEmit = this.selected.map(chip => chip.value);\n        }\n        else {\n            valueToEmit = this.selected ? this.selected.value : fallbackValue;\n        }\n        this._value = valueToEmit;\n        this.change.emit(new MatChipListChange(this, valueToEmit));\n        this.valueChange.emit(valueToEmit);\n        this._onChange(valueToEmit);\n        this._changeDetectorRef.markForCheck();\n    }\n    /** When blurred, mark the field as touched when focus moved outside the chip list. */\n    _blur() {\n        if (!this._hasFocusedChip()) {\n            this._keyManager.setActiveItem(-1);\n        }\n        if (!this.disabled) {\n            if (this._chipInput) {\n                // If there's a chip input, we should check whether the focus moved to chip input.\n                // If the focus is not moved to chip input, mark the field as touched. If the focus moved\n                // to chip input, do nothing.\n                // Timeout is needed to wait for the focus() event trigger on chip input.\n                setTimeout(() => {\n                    if (!this.focused) {\n                        this._markAsTouched();\n                    }\n                });\n            }\n            else {\n                // If there's no chip input, then mark the field as touched.\n                this._markAsTouched();\n            }\n        }\n    }\n    /** Mark the field as touched */\n    _markAsTouched() {\n        this._onTouched();\n        this._changeDetectorRef.markForCheck();\n        this.stateChanges.next();\n    }\n    /**\n     * Removes the `tabindex` from the chip list and resets it back afterwards, allowing the\n     * user to tab out of it. This prevents the list from capturing focus and redirecting\n     * it back to the first chip, creating a focus trap, if it user tries to tab away.\n     */\n    _allowFocusEscape() {\n        if (this._tabIndex !== -1) {\n            this._tabIndex = -1;\n            setTimeout(() => {\n                this._tabIndex = this._userTabIndex || 0;\n                this._changeDetectorRef.markForCheck();\n            });\n        }\n    }\n    _resetChips() {\n        this._dropSubscriptions();\n        this._listenToChipsFocus();\n        this._listenToChipsSelection();\n        this._listenToChipsRemoved();\n    }\n    _dropSubscriptions() {\n        if (this._chipFocusSubscription) {\n            this._chipFocusSubscription.unsubscribe();\n            this._chipFocusSubscription = null;\n        }\n        if (this._chipBlurSubscription) {\n            this._chipBlurSubscription.unsubscribe();\n            this._chipBlurSubscription = null;\n        }\n        if (this._chipSelectionSubscription) {\n            this._chipSelectionSubscription.unsubscribe();\n            this._chipSelectionSubscription = null;\n        }\n        if (this._chipRemoveSubscription) {\n            this._chipRemoveSubscription.unsubscribe();\n            this._chipRemoveSubscription = null;\n        }\n    }\n    /** Listens to user-generated selection events on each chip. */\n    _listenToChipsSelection() {\n        this._chipSelectionSubscription = this.chipSelectionChanges.subscribe(event => {\n            event.source.selected\n                ? this._selectionModel.select(event.source)\n                : this._selectionModel.deselect(event.source);\n            // For single selection chip list, make sure the deselected value is unselected.\n            if (!this.multiple) {\n                this.chips.forEach(chip => {\n                    if (!this._selectionModel.isSelected(chip) && chip.selected) {\n                        chip.deselect();\n                    }\n                });\n            }\n            if (event.isUserInput) {\n                this._propagateChanges();\n            }\n        });\n    }\n    /** Listens to user-generated selection events on each chip. */\n    _listenToChipsFocus() {\n        this._chipFocusSubscription = this.chipFocusChanges.subscribe(event => {\n            let chipIndex = this.chips.toArray().indexOf(event.chip);\n            if (this._isValidIndex(chipIndex)) {\n                this._keyManager.updateActiveItem(chipIndex);\n            }\n            this.stateChanges.next();\n        });\n        this._chipBlurSubscription = this.chipBlurChanges.subscribe(() => {\n            this._blur();\n            this.stateChanges.next();\n        });\n    }\n    _listenToChipsRemoved() {\n        this._chipRemoveSubscription = this.chipRemoveChanges.subscribe(event => {\n            const chip = event.chip;\n            const chipIndex = this.chips.toArray().indexOf(event.chip);\n            // In case the chip that will be removed is currently focused, we temporarily store\n            // the index in order to be able to determine an appropriate sibling chip that will\n            // receive focus.\n            if (this._isValidIndex(chipIndex) && chip._hasFocus) {\n                this._lastDestroyedChipIndex = chipIndex;\n            }\n        });\n    }\n    /** Checks whether an event comes from inside a chip element. */\n    _originatesFromChip(event) {\n        let currentElement = event.target;\n        while (currentElement && currentElement !== this._elementRef.nativeElement) {\n            if (currentElement.classList.contains('mat-chip')) {\n                return true;\n            }\n            currentElement = currentElement.parentElement;\n        }\n        return false;\n    }\n    /** Checks whether any of the chips is focused. */\n    _hasFocusedChip() {\n        return this.chips && this.chips.some(chip => chip._hasFocus);\n    }\n    /** Syncs the list's state with the individual chips. */\n    _syncChipsState() {\n        if (this.chips) {\n            this.chips.forEach(chip => {\n                chip._chipListDisabled = this._disabled;\n                chip._chipListMultiple = this.multiple;\n                chip.chipListSelectable = this._selectable;\n            });\n        }\n    }\n}\nMatChipList.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatChipList, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i1$1.Directionality, optional: true }, { token: i2.NgForm, optional: true }, { token: i2.FormGroupDirective, optional: true }, { token: i3.ErrorStateMatcher }, { token: i2.NgControl, optional: true, self: true }], target: i0.ɵɵFactoryTarget.Component });\nMatChipList.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatChipList, selector: \"mat-chip-list\", inputs: { role: \"role\", userAriaDescribedBy: [\"aria-describedby\", \"userAriaDescribedBy\"], errorStateMatcher: \"errorStateMatcher\", multiple: \"multiple\", compareWith: \"compareWith\", value: \"value\", required: \"required\", placeholder: \"placeholder\", disabled: \"disabled\", ariaOrientation: [\"aria-orientation\", \"ariaOrientation\"], selectable: \"selectable\", tabIndex: \"tabIndex\" }, outputs: { change: \"change\", valueChange: \"valueChange\" }, host: { listeners: { \"focus\": \"focus()\", \"blur\": \"_blur()\", \"keydown\": \"_keydown($event)\" }, properties: { \"attr.tabindex\": \"disabled ? null : _tabIndex\", \"attr.aria-required\": \"role ? required : null\", \"attr.aria-disabled\": \"disabled.toString()\", \"attr.aria-invalid\": \"errorState\", \"attr.aria-multiselectable\": \"multiple\", \"attr.role\": \"role\", \"class.mat-chip-list-disabled\": \"disabled\", \"class.mat-chip-list-invalid\": \"errorState\", \"class.mat-chip-list-required\": \"required\", \"attr.aria-orientation\": \"ariaOrientation\", \"id\": \"_uid\" }, classAttribute: \"mat-chip-list\" }, providers: [{ provide: MatFormFieldControl, useExisting: MatChipList }], queries: [{ propertyName: \"chips\", predicate: MatChip, descendants: true }], exportAs: [\"matChipList\"], usesInheritance: true, ngImport: i0, template: `<div class=\"mat-chip-list-wrapper\"><ng-content></ng-content></div>`, isInline: true, styles: [\".mat-chip{position:relative;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0);border:none;-webkit-appearance:none;-moz-appearance:none}.mat-chip::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px) * -1)}.mat-standard-chip{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);display:inline-flex;padding:7px 12px;border-radius:16px;align-items:center;cursor:default;min-height:32px;height:1px}.mat-standard-chip._mat-animation-noopable{transition:none !important;animation:none !important}.mat-standard-chip .mat-chip-remove{border:none;-webkit-appearance:none;-moz-appearance:none;padding:0;background:none}.mat-standard-chip .mat-chip-remove.mat-icon,.mat-standard-chip .mat-chip-remove .mat-icon{width:18px;height:18px;font-size:18px}.mat-standard-chip::after{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit;opacity:0;content:\\\"\\\";pointer-events:none;transition:opacity 200ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-standard-chip:hover::after{opacity:.12}.mat-standard-chip:focus{outline:none}.mat-standard-chip:focus::after{opacity:.16}.cdk-high-contrast-active .mat-standard-chip{outline:solid 1px}.cdk-high-contrast-active .mat-standard-chip.mat-chip-selected{outline-width:3px}.mat-standard-chip.mat-chip-disabled::after{opacity:0}.mat-standard-chip.mat-chip-disabled .mat-chip-remove,.mat-standard-chip.mat-chip-disabled .mat-chip-trailing-icon{cursor:default}.mat-standard-chip.mat-chip-with-trailing-icon.mat-chip-with-avatar,.mat-standard-chip.mat-chip-with-avatar{padding-top:0;padding-bottom:0}.mat-standard-chip.mat-chip-with-trailing-icon.mat-chip-with-avatar{padding-right:8px;padding-left:0}[dir=rtl] .mat-standard-chip.mat-chip-with-trailing-icon.mat-chip-with-avatar{padding-left:8px;padding-right:0}.mat-standard-chip.mat-chip-with-trailing-icon{padding-top:7px;padding-bottom:7px;padding-right:8px;padding-left:12px}[dir=rtl] .mat-standard-chip.mat-chip-with-trailing-icon{padding-left:8px;padding-right:12px}.mat-standard-chip.mat-chip-with-avatar{padding-left:0;padding-right:12px}[dir=rtl] .mat-standard-chip.mat-chip-with-avatar{padding-right:0;padding-left:12px}.mat-standard-chip .mat-chip-avatar{width:24px;height:24px;margin-right:8px;margin-left:4px}[dir=rtl] .mat-standard-chip .mat-chip-avatar{margin-left:8px;margin-right:4px}.mat-standard-chip .mat-chip-remove,.mat-standard-chip .mat-chip-trailing-icon{width:18px;height:18px;cursor:pointer}.mat-standard-chip .mat-chip-remove,.mat-standard-chip .mat-chip-trailing-icon{margin-left:8px;margin-right:0}[dir=rtl] .mat-standard-chip .mat-chip-remove,[dir=rtl] .mat-standard-chip .mat-chip-trailing-icon{margin-right:8px;margin-left:0}.mat-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit;overflow:hidden;transform:translateZ(0)}.mat-chip-list-wrapper{display:flex;flex-direction:row;flex-wrap:wrap;align-items:center;margin:-4px}.mat-chip-list-wrapper input.mat-input-element,.mat-chip-list-wrapper .mat-standard-chip{margin:4px}.mat-chip-list-stacked .mat-chip-list-wrapper{flex-direction:column;align-items:flex-start}.mat-chip-list-stacked .mat-chip-list-wrapper .mat-standard-chip{width:100%}.mat-chip-avatar{border-radius:50%;justify-content:center;align-items:center;display:flex;overflow:hidden;object-fit:cover}input.mat-chip-input{width:150px;margin:4px;flex:1 0 150px}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatChipList, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-chip-list', template: `<div class=\"mat-chip-list-wrapper\"><ng-content></ng-content></div>`, exportAs: 'matChipList', host: {\n                        '[attr.tabindex]': 'disabled ? null : _tabIndex',\n                        '[attr.aria-required]': 'role ? required : null',\n                        '[attr.aria-disabled]': 'disabled.toString()',\n                        '[attr.aria-invalid]': 'errorState',\n                        '[attr.aria-multiselectable]': 'multiple',\n                        '[attr.role]': 'role',\n                        '[class.mat-chip-list-disabled]': 'disabled',\n                        '[class.mat-chip-list-invalid]': 'errorState',\n                        '[class.mat-chip-list-required]': 'required',\n                        '[attr.aria-orientation]': 'ariaOrientation',\n                        'class': 'mat-chip-list',\n                        '(focus)': 'focus()',\n                        '(blur)': '_blur()',\n                        '(keydown)': '_keydown($event)',\n                        '[id]': '_uid',\n                    }, providers: [{ provide: MatFormFieldControl, useExisting: MatChipList }], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, styles: [\".mat-chip{position:relative;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0);border:none;-webkit-appearance:none;-moz-appearance:none}.mat-chip::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px) * -1)}.mat-standard-chip{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);display:inline-flex;padding:7px 12px;border-radius:16px;align-items:center;cursor:default;min-height:32px;height:1px}.mat-standard-chip._mat-animation-noopable{transition:none !important;animation:none !important}.mat-standard-chip .mat-chip-remove{border:none;-webkit-appearance:none;-moz-appearance:none;padding:0;background:none}.mat-standard-chip .mat-chip-remove.mat-icon,.mat-standard-chip .mat-chip-remove .mat-icon{width:18px;height:18px;font-size:18px}.mat-standard-chip::after{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit;opacity:0;content:\\\"\\\";pointer-events:none;transition:opacity 200ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-standard-chip:hover::after{opacity:.12}.mat-standard-chip:focus{outline:none}.mat-standard-chip:focus::after{opacity:.16}.cdk-high-contrast-active .mat-standard-chip{outline:solid 1px}.cdk-high-contrast-active .mat-standard-chip.mat-chip-selected{outline-width:3px}.mat-standard-chip.mat-chip-disabled::after{opacity:0}.mat-standard-chip.mat-chip-disabled .mat-chip-remove,.mat-standard-chip.mat-chip-disabled .mat-chip-trailing-icon{cursor:default}.mat-standard-chip.mat-chip-with-trailing-icon.mat-chip-with-avatar,.mat-standard-chip.mat-chip-with-avatar{padding-top:0;padding-bottom:0}.mat-standard-chip.mat-chip-with-trailing-icon.mat-chip-with-avatar{padding-right:8px;padding-left:0}[dir=rtl] .mat-standard-chip.mat-chip-with-trailing-icon.mat-chip-with-avatar{padding-left:8px;padding-right:0}.mat-standard-chip.mat-chip-with-trailing-icon{padding-top:7px;padding-bottom:7px;padding-right:8px;padding-left:12px}[dir=rtl] .mat-standard-chip.mat-chip-with-trailing-icon{padding-left:8px;padding-right:12px}.mat-standard-chip.mat-chip-with-avatar{padding-left:0;padding-right:12px}[dir=rtl] .mat-standard-chip.mat-chip-with-avatar{padding-right:0;padding-left:12px}.mat-standard-chip .mat-chip-avatar{width:24px;height:24px;margin-right:8px;margin-left:4px}[dir=rtl] .mat-standard-chip .mat-chip-avatar{margin-left:8px;margin-right:4px}.mat-standard-chip .mat-chip-remove,.mat-standard-chip .mat-chip-trailing-icon{width:18px;height:18px;cursor:pointer}.mat-standard-chip .mat-chip-remove,.mat-standard-chip .mat-chip-trailing-icon{margin-left:8px;margin-right:0}[dir=rtl] .mat-standard-chip .mat-chip-remove,[dir=rtl] .mat-standard-chip .mat-chip-trailing-icon{margin-right:8px;margin-left:0}.mat-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit;overflow:hidden;transform:translateZ(0)}.mat-chip-list-wrapper{display:flex;flex-direction:row;flex-wrap:wrap;align-items:center;margin:-4px}.mat-chip-list-wrapper input.mat-input-element,.mat-chip-list-wrapper .mat-standard-chip{margin:4px}.mat-chip-list-stacked .mat-chip-list-wrapper{flex-direction:column;align-items:flex-start}.mat-chip-list-stacked .mat-chip-list-wrapper .mat-standard-chip{width:100%}.mat-chip-avatar{border-radius:50%;justify-content:center;align-items:center;display:flex;overflow:hidden;object-fit:cover}input.mat-chip-input{width:150px;margin:4px;flex:1 0 150px}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i1$1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i2.NgForm, decorators: [{\n                    type: Optional\n                }] }, { type: i2.FormGroupDirective, decorators: [{\n                    type: Optional\n                }] }, { type: i3.ErrorStateMatcher }, { type: i2.NgControl, decorators: [{\n                    type: Optional\n                }, {\n                    type: Self\n                }] }]; }, propDecorators: { role: [{\n                type: Input\n            }], userAriaDescribedBy: [{\n                type: Input,\n                args: ['aria-describedby']\n            }], errorStateMatcher: [{\n                type: Input\n            }], multiple: [{\n                type: Input\n            }], compareWith: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], required: [{\n                type: Input\n            }], placeholder: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], ariaOrientation: [{\n                type: Input,\n                args: ['aria-orientation']\n            }], selectable: [{\n                type: Input\n            }], tabIndex: [{\n                type: Input\n            }], change: [{\n                type: Output\n            }], valueChange: [{\n                type: Output\n            }], chips: [{\n                type: ContentChildren,\n                args: [MatChip, {\n                        // We need to use `descendants: true`, because Ivy will no longer match\n                        // indirect descendants if it's left as false.\n                        descendants: true,\n                    }]\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Increasing integer for generating unique ids.\nlet nextUniqueId = 0;\n/**\n * Directive that adds chip-specific behaviors to an input element inside `<mat-form-field>`.\n * May be placed inside or outside of an `<mat-chip-list>`.\n */\nclass MatChipInput {\n    constructor(_elementRef, _defaultOptions) {\n        this._elementRef = _elementRef;\n        this._defaultOptions = _defaultOptions;\n        /** Whether the control is focused. */\n        this.focused = false;\n        this._addOnBlur = false;\n        /**\n         * The list of key codes that will trigger a chipEnd event.\n         *\n         * Defaults to `[ENTER]`.\n         */\n        this.separatorKeyCodes = this._defaultOptions.separatorKeyCodes;\n        /** Emitted when a chip is to be added. */\n        this.chipEnd = new EventEmitter();\n        /** The input's placeholder text. */\n        this.placeholder = '';\n        /** Unique id for the input. */\n        this.id = `mat-chip-list-input-${nextUniqueId++}`;\n        this._disabled = false;\n        this.inputElement = this._elementRef.nativeElement;\n    }\n    /** Register input for chip list */\n    set chipList(value) {\n        if (value) {\n            this._chipList = value;\n            this._chipList.registerInput(this);\n        }\n    }\n    /**\n     * Whether or not the chipEnd event will be emitted when the input is blurred.\n     */\n    get addOnBlur() {\n        return this._addOnBlur;\n    }\n    set addOnBlur(value) {\n        this._addOnBlur = coerceBooleanProperty(value);\n    }\n    /** Whether the input is disabled. */\n    get disabled() {\n        return this._disabled || (this._chipList && this._chipList.disabled);\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n    }\n    /** Whether the input is empty. */\n    get empty() {\n        return !this.inputElement.value;\n    }\n    ngOnChanges() {\n        this._chipList.stateChanges.next();\n    }\n    ngOnDestroy() {\n        this.chipEnd.complete();\n    }\n    ngAfterContentInit() {\n        this._focusLastChipOnBackspace = this.empty;\n    }\n    /** Utility method to make host definition/tests more clear. */\n    _keydown(event) {\n        if (event) {\n            // Allow the user's focus to escape when they're tabbing forward. Note that we don't\n            // want to do this when going backwards, because focus should go back to the first chip.\n            if (event.keyCode === TAB && !hasModifierKey(event, 'shiftKey')) {\n                this._chipList._allowFocusEscape();\n            }\n            // To prevent the user from accidentally deleting chips when pressing BACKSPACE continuously,\n            // We focus the last chip on backspace only after the user has released the backspace button,\n            // and the input is empty (see behaviour in _keyup)\n            if (event.keyCode === BACKSPACE && this._focusLastChipOnBackspace) {\n                this._chipList._keyManager.setLastItemActive();\n                event.preventDefault();\n                return;\n            }\n            else {\n                this._focusLastChipOnBackspace = false;\n            }\n        }\n        this._emitChipEnd(event);\n    }\n    /**\n     * Pass events to the keyboard manager. Available here for tests.\n     */\n    _keyup(event) {\n        // Allow user to move focus to chips next time he presses backspace\n        if (!this._focusLastChipOnBackspace && event.keyCode === BACKSPACE && this.empty) {\n            this._focusLastChipOnBackspace = true;\n            event.preventDefault();\n        }\n    }\n    /** Checks to see if the blur should emit the (chipEnd) event. */\n    _blur() {\n        if (this.addOnBlur) {\n            this._emitChipEnd();\n        }\n        this.focused = false;\n        // Blur the chip list if it is not focused\n        if (!this._chipList.focused) {\n            this._chipList._blur();\n        }\n        this._chipList.stateChanges.next();\n    }\n    _focus() {\n        this.focused = true;\n        this._focusLastChipOnBackspace = this.empty;\n        this._chipList.stateChanges.next();\n    }\n    /** Checks to see if the (chipEnd) event needs to be emitted. */\n    _emitChipEnd(event) {\n        if (!this.inputElement.value && !!event) {\n            this._chipList._keydown(event);\n        }\n        if (!event || this._isSeparatorKey(event)) {\n            this.chipEnd.emit({\n                input: this.inputElement,\n                value: this.inputElement.value,\n                chipInput: this,\n            });\n            event?.preventDefault();\n        }\n    }\n    _onInput() {\n        // Let chip list know whenever the value changes.\n        this._chipList.stateChanges.next();\n    }\n    /** Focuses the input. */\n    focus(options) {\n        this.inputElement.focus(options);\n    }\n    /** Clears the input */\n    clear() {\n        this.inputElement.value = '';\n        this._focusLastChipOnBackspace = true;\n    }\n    /** Checks whether a keycode is one of the configured separators. */\n    _isSeparatorKey(event) {\n        return !hasModifierKey(event) && new Set(this.separatorKeyCodes).has(event.keyCode);\n    }\n}\nMatChipInput.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatChipInput, deps: [{ token: i0.ElementRef }, { token: MAT_CHIPS_DEFAULT_OPTIONS }], target: i0.ɵɵFactoryTarget.Directive });\nMatChipInput.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatChipInput, selector: \"input[matChipInputFor]\", inputs: { chipList: [\"matChipInputFor\", \"chipList\"], addOnBlur: [\"matChipInputAddOnBlur\", \"addOnBlur\"], separatorKeyCodes: [\"matChipInputSeparatorKeyCodes\", \"separatorKeyCodes\"], placeholder: \"placeholder\", id: \"id\", disabled: \"disabled\" }, outputs: { chipEnd: \"matChipInputTokenEnd\" }, host: { listeners: { \"keydown\": \"_keydown($event)\", \"keyup\": \"_keyup($event)\", \"blur\": \"_blur()\", \"focus\": \"_focus()\", \"input\": \"_onInput()\" }, properties: { \"id\": \"id\", \"attr.disabled\": \"disabled || null\", \"attr.placeholder\": \"placeholder || null\", \"attr.aria-invalid\": \"_chipList && _chipList.ngControl ? _chipList.ngControl.invalid : null\", \"attr.aria-required\": \"_chipList && _chipList.required || null\" }, classAttribute: \"mat-chip-input mat-input-element\" }, exportAs: [\"matChipInput\", \"matChipInputFor\"], usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatChipInput, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'input[matChipInputFor]',\n                    exportAs: 'matChipInput, matChipInputFor',\n                    host: {\n                        'class': 'mat-chip-input mat-input-element',\n                        '(keydown)': '_keydown($event)',\n                        '(keyup)': '_keyup($event)',\n                        '(blur)': '_blur()',\n                        '(focus)': '_focus()',\n                        '(input)': '_onInput()',\n                        '[id]': 'id',\n                        '[attr.disabled]': 'disabled || null',\n                        '[attr.placeholder]': 'placeholder || null',\n                        '[attr.aria-invalid]': '_chipList && _chipList.ngControl ? _chipList.ngControl.invalid : null',\n                        '[attr.aria-required]': '_chipList && _chipList.required || null',\n                    },\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_CHIPS_DEFAULT_OPTIONS]\n                }] }]; }, propDecorators: { chipList: [{\n                type: Input,\n                args: ['matChipInputFor']\n            }], addOnBlur: [{\n                type: Input,\n                args: ['matChipInputAddOnBlur']\n            }], separatorKeyCodes: [{\n                type: Input,\n                args: ['matChipInputSeparatorKeyCodes']\n            }], chipEnd: [{\n                type: Output,\n                args: ['matChipInputTokenEnd']\n            }], placeholder: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst CHIP_DECLARATIONS = [\n    MatChipList,\n    MatChip,\n    MatChipInput,\n    MatChipRemove,\n    MatChipAvatar,\n    MatChipTrailingIcon,\n];\nclass MatChipsModule {\n}\nMatChipsModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatChipsModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatChipsModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.2.0\", ngImport: i0, type: MatChipsModule, declarations: [MatChipList,\n        MatChip,\n        MatChipInput,\n        MatChipRemove,\n        MatChipAvatar,\n        MatChipTrailingIcon], imports: [MatCommonModule], exports: [MatChipList,\n        MatChip,\n        MatChipInput,\n        MatChipRemove,\n        MatChipAvatar,\n        MatChipTrailingIcon] });\nMatChipsModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatChipsModule, providers: [\n        ErrorStateMatcher,\n        {\n            provide: MAT_CHIPS_DEFAULT_OPTIONS,\n            useValue: {\n                separatorKeyCodes: [ENTER],\n            },\n        },\n    ], imports: [MatCommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatChipsModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule],\n                    exports: CHIP_DECLARATIONS,\n                    declarations: CHIP_DECLARATIONS,\n                    providers: [\n                        ErrorStateMatcher,\n                        {\n                            provide: MAT_CHIPS_DEFAULT_OPTIONS,\n                            useValue: {\n                                separatorKeyCodes: [ENTER],\n                            },\n                        },\n                    ],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_CHIPS_DEFAULT_OPTIONS, MAT_CHIP_AVATAR, MAT_CHIP_REMOVE, MAT_CHIP_TRAILING_ICON, MatChip, MatChipAvatar, MatChipInput, MatChipList, MatChipListChange, MatChipRemove, MatChipSelectionChange, MatChipTrailingIcon, MatChipsModule };\n"], "mappings": "AAAA,SAASA,KAAT,EAAgBC,SAAhB,EAA2BC,MAA3B,EAAmCC,GAAnC,EAAwCC,cAAxC,EAAwDC,KAAxD,QAAqE,uBAArE;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,SAASC,cAAT,EAAyBC,SAAzB,EAAoCC,YAApC,EAAkDC,QAAlD,EAA4DC,MAA5D,EAAoEC,SAApE,EAA+EC,YAA/E,EAA6FC,KAA7F,EAAoGC,MAApG,EAA4GC,SAA5G,EAAuHC,iBAAvH,EAA0IC,uBAA1I,EAAmKC,IAAnK,EAAyKC,eAAzK,EAA0LC,QAA1L,QAA0M,eAA1M;AACA,OAAO,KAAKC,EAAZ,MAAoB,wBAApB;AACA,SAASC,aAAT,EAAwBC,UAAxB,EAAoCC,kBAApC,EAAwDC,cAAxD,EAAwEC,yBAAxE,EAAmGC,eAAnG,EAAoHC,eAApH,EAAqIC,iBAArI,QAA8J,wBAA9J;AACA,SAASC,qBAAT,QAAsC,uBAAtC;AACA,OAAO,KAAKC,EAAZ,MAAoB,uBAApB;AACA,SAASC,QAAT,QAAyB,iBAAzB;AACA,SAASC,qBAAT,QAAsC,sCAAtC;AACA,SAASC,OAAT,EAAkBC,KAAlB,QAA+B,MAA/B;AACA,SAASC,IAAT,EAAeC,SAAf,EAA0BC,SAA1B,QAA2C,gBAA3C;AACA,SAASC,eAAT,QAAgC,mBAAhC;AACA,OAAO,KAAKC,IAAZ,MAAsB,mBAAtB;AACA,SAASC,cAAT,QAA+B,0BAA/B;AACA,OAAO,KAAKC,EAAZ,MAAoB,gBAApB;AACA,SAASC,UAAT,QAA2B,gBAA3B;AACA,SAASC,mBAAT,QAAoC,8BAApC;AAEA;;;;AACA,MAAMC,sBAAN,CAA6B;EACzBC,WAAW;EACX;EACAC,MAFW;EAGX;EACAC,QAJW;EAKX;EACAC,WAAW,GAAG,KANH,EAMU;IACjB,KAAKF,MAAL,GAAcA,MAAd;IACA,KAAKC,QAAL,GAAgBA,QAAhB;IACA,KAAKC,WAAL,GAAmBA,WAAnB;EACH;;AAXwB;AAa7B;AACA;AACA;AACA;AACA;;;AACA,MAAMC,eAAe,GAAG,IAAI5C,cAAJ,CAAmB,eAAnB,CAAxB;AACA;AACA;AACA;AACA;AACA;;AACA,MAAM6C,eAAe,GAAG,IAAI7C,cAAJ,CAAmB,eAAnB,CAAxB;AACA;AACA;AACA;AACA;AACA;;AACA,MAAM8C,sBAAsB,GAAG,IAAI9C,cAAJ,CAAmB,qBAAnB,CAA/B,C,CACA;;AACA;;AACA,MAAM+C,WAAN,CAAkB;EACdP,WAAW,CAACQ,WAAD,EAAc;IACrB,KAAKA,WAAL,GAAmBA,WAAnB;EACH;;AAHa;;AAKlB,MAAMC,iBAAiB,GAAGjC,aAAa,CAACC,UAAU,CAACC,kBAAkB,CAAC6B,WAAD,CAAnB,EAAkC,SAAlC,CAAX,EAAyD,CAAC,CAA1D,CAAvC;AACA;AACA;AACA;AACA;;;AACA,MAAMG,aAAN,CAAoB;;AAEpBA,aAAa,CAACC,IAAd;EAAA,iBAA0GD,aAA1G;AAAA;;AACAA,aAAa,CAACE,IAAd,kBADgGrD,EAChG;EAAA,MAA8FmD,aAA9F;EAAA;EAAA;EAAA,WADgGnD,EAChG,oBAAmN,CAAC;IAAEsD,OAAO,EAAER,eAAX;IAA4BS,WAAW,EAAEJ;EAAzC,CAAD,CAAnN;AAAA;;AACA;EAAA,mDAFgGnD,EAEhG,mBAA2FmD,aAA3F,EAAsH,CAAC;IAC3GK,IAAI,EAAEtD,SADqG;IAE3GuD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kCADX;MAECC,IAAI,EAAE;QAAE,SAAS;MAAX,CAFP;MAGCC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAER,eAAX;QAA4BS,WAAW,EAAEJ;MAAzC,CAAD;IAHZ,CAAD;EAFqG,CAAD,CAAtH;AAAA;AAQA;AACA;AACA;AACA;;;AACA,MAAMU,mBAAN,CAA0B;;AAE1BA,mBAAmB,CAACT,IAApB;EAAA,iBAAgHS,mBAAhH;AAAA;;AACAA,mBAAmB,CAACR,IAApB,kBAjBgGrD,EAiBhG;EAAA,MAAoG6D,mBAApG;EAAA;EAAA;EAAA,WAjBgG7D,EAiBhG,oBAAmP,CAAC;IAAEsD,OAAO,EAAEP,sBAAX;IAAmCQ,WAAW,EAAEM;EAAhD,CAAD,CAAnP;AAAA;;AACA;EAAA,mDAlBgG7D,EAkBhG,mBAA2F6D,mBAA3F,EAA4H,CAAC;IACjHL,IAAI,EAAEtD,SAD2G;IAEjHuD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,+CADX;MAECC,IAAI,EAAE;QAAE,SAAS;MAAX,CAFP;MAGCC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEP,sBAAX;QAAmCQ,WAAW,EAAEM;MAAhD,CAAD;IAHZ,CAAD;EAF2G,CAAD,CAA5H;AAAA;AAQA;;;AACA,MAAMC,OAAN,SAAsBZ,iBAAtB,CAAwC;EACpCT,WAAW,CAACsB,UAAD,EAAaC,OAAb,EAAsBC,QAAtB,EAAgCC,mBAAhC,EAAqDC,kBAArD,EAAyEC,SAAzE,EAAoFC,aAApF,EAAmGC,QAAnG,EAA6G;IACpH,MAAMP,UAAN;IACA,KAAKC,OAAL,GAAeA,OAAf;IACA,KAAKG,kBAAL,GAA0BA,kBAA1B;IACA;;IACA,KAAKI,SAAL,GAAiB,KAAjB;IACA;;IACA,KAAKC,kBAAL,GAA0B,IAA1B;IACA;;IACA,KAAKC,iBAAL,GAAyB,KAAzB;IACA;;IACA,KAAKC,iBAAL,GAAyB,KAAzB;IACA;;IACA,KAAKC,IAAL,GAAY,QAAZ;IACA,KAAKC,SAAL,GAAiB,KAAjB;IACA,KAAKC,WAAL,GAAmB,IAAnB;IACA,KAAKC,SAAL,GAAiB,KAAjB;IACA,KAAKC,UAAL,GAAkB,IAAlB;IACA;;IACA,KAAKC,QAAL,GAAgB,IAAInD,OAAJ,EAAhB;IACA;;IACA,KAAKoD,OAAL,GAAe,IAAIpD,OAAJ,EAAf;IACA;;IACA,KAAKqD,eAAL,GAAuB,IAAI/E,YAAJ,EAAvB;IACA;;IACA,KAAKgF,SAAL,GAAiB,IAAIhF,YAAJ,EAAjB;IACA;;IACA,KAAKiF,OAAL,GAAe,IAAIjF,YAAJ,EAAf;;IACA,KAAKkF,iBAAL,GA5BoH,CA6BpH;IACA;IACA;;;IACA,KAAKC,iBAAL,GAAyBlB,SAAS,CAACmB,aAAV,CAAwB,KAAxB,CAAzB;;IACA,KAAKD,iBAAL,CAAuBE,SAAvB,CAAiCC,GAAjC,CAAqC,iBAArC;;IACA,KAAKxC,WAAL,CAAiByC,aAAjB,CAA+BC,WAA/B,CAA2C,KAAKL,iBAAhD;;IACA,KAAKM,WAAL,GAAmB,IAAIxE,cAAJ,CAAmB,IAAnB,EAAyB4C,OAAzB,EAAkC,KAAKsB,iBAAvC,EAA0DrB,QAA1D,CAAnB;;IACA,KAAK2B,WAAL,CAAiBC,kBAAjB,CAAoC9B,UAApC;;IACA,KAAK+B,YAAL,GAAoB5B,mBAAmB,IAAI,EAA3C;IACA,KAAK6B,mBAAL,GAA2B1B,aAAa,KAAK,gBAA7C;IACA,KAAKC,QAAL,GAAgBA,QAAQ,IAAI,IAAZ,GAAmB0B,QAAQ,CAAC1B,QAAD,CAAR,IAAsB,CAAC,CAA1C,GAA8C,CAAC,CAA/D;EACH;EACD;AACJ;AACA;AACA;;;EACsB,IAAd2B,cAAc,GAAG;IACjB,OAAQ,KAAKC,QAAL,IACJ,KAAKC,aADD,IAEJ,KAAKJ,mBAFD,IAGJ,CAAC,CAAC,KAAKD,YAAL,CAAkBI,QAHxB;EAIH;EACD;;;EACY,IAARvD,QAAQ,GAAG;IACX,OAAO,KAAKiC,SAAZ;EACH;;EACW,IAARjC,QAAQ,CAACyD,KAAD,EAAQ;IAChB,MAAMC,YAAY,GAAG5E,qBAAqB,CAAC2E,KAAD,CAA1C;;IACA,IAAIC,YAAY,KAAK,KAAKzB,SAA1B,EAAqC;MACjC,KAAKA,SAAL,GAAiByB,YAAjB;;MACA,KAAKC,wBAAL;IACH;EACJ;EACD;;;EACS,IAALF,KAAK,GAAG;IACR,OAAO,KAAKG,MAAL,KAAgBC,SAAhB,GAA4B,KAAKD,MAAjC,GAA0C,KAAKtD,WAAL,CAAiByC,aAAjB,CAA+Be,WAAhF;EACH;;EACQ,IAALL,KAAK,CAACA,KAAD,EAAQ;IACb,KAAKG,MAAL,GAAcH,KAAd;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACkB,IAAVM,UAAU,GAAG;IACb,OAAO,KAAK7B,WAAL,IAAoB,KAAKL,kBAAhC;EACH;;EACa,IAAVkC,UAAU,CAACN,KAAD,EAAQ;IAClB,KAAKvB,WAAL,GAAmBpD,qBAAqB,CAAC2E,KAAD,CAAxC;EACH;EACD;;;EACY,IAARF,QAAQ,GAAG;IACX,OAAO,KAAKxB,iBAAL,IAA0B,KAAKI,SAAtC;EACH;;EACW,IAARoB,QAAQ,CAACE,KAAD,EAAQ;IAChB,KAAKtB,SAAL,GAAiBrD,qBAAqB,CAAC2E,KAAD,CAAtC;EACH;EACD;AACJ;AACA;;;EACiB,IAATO,SAAS,GAAG;IACZ,OAAO,KAAK5B,UAAZ;EACH;;EACY,IAAT4B,SAAS,CAACP,KAAD,EAAQ;IACjB,KAAKrB,UAAL,GAAkBtD,qBAAqB,CAAC2E,KAAD,CAAvC;EACH;EACD;;;EACgB,IAAZQ,YAAY,GAAG;IACf;IACA;IACA,OAAO,KAAKF,UAAL,KAAoB,KAAKjC,iBAAL,IAA0B,KAAK9B,QAAnD,IACD,KAAKA,QAAL,CAAckE,QAAd,EADC,GAED,IAFN;EAGH;;EACDxB,iBAAiB,GAAG;IAChB,MAAMyB,iBAAiB,GAAG,gBAA1B;IACA,MAAMC,OAAO,GAAG,KAAK9D,WAAL,CAAiByC,aAAjC;;IACA,IAAIqB,OAAO,CAACC,YAAR,CAAqBF,iBAArB,KACAC,OAAO,CAACE,OAAR,CAAgBC,WAAhB,OAAkCJ,iBADtC,EACyD;MACrDC,OAAO,CAACvB,SAAR,CAAkBC,GAAlB,CAAsBqB,iBAAtB;MACA;IACH,CAJD,MAKK;MACDC,OAAO,CAACvB,SAAR,CAAkBC,GAAlB,CAAsB,mBAAtB;IACH;EACJ;;EACD0B,WAAW,GAAG;IACV,KAAKhC,SAAL,CAAeiC,IAAf,CAAoB;MAAEC,IAAI,EAAE;IAAR,CAApB;;IACA,KAAKzB,WAAL,CAAiB0B,oBAAjB;EACH;EACD;;;EACAC,MAAM,GAAG;IACL,IAAI,CAAC,KAAK3C,SAAV,EAAqB;MACjB,KAAKA,SAAL,GAAiB,IAAjB;;MACA,KAAK0B,wBAAL;;MACA,KAAKnC,kBAAL,CAAwBqD,YAAxB;IACH;EACJ;EACD;;;EACAC,QAAQ,GAAG;IACP,IAAI,KAAK7C,SAAT,EAAoB;MAChB,KAAKA,SAAL,GAAiB,KAAjB;;MACA,KAAK0B,wBAAL;;MACA,KAAKnC,kBAAL,CAAwBqD,YAAxB;IACH;EACJ;EACD;;;EACAE,oBAAoB,GAAG;IACnB,IAAI,CAAC,KAAK9C,SAAV,EAAqB;MACjB,KAAKA,SAAL,GAAiB,IAAjB;;MACA,KAAK0B,wBAAL,CAA8B,IAA9B;;MACA,KAAKnC,kBAAL,CAAwBqD,YAAxB;IACH;EACJ;EACD;;;EACAG,cAAc,CAAC/E,WAAW,GAAG,KAAf,EAAsB;IAChC,KAAKgC,SAAL,GAAiB,CAAC,KAAKjC,QAAvB;;IACA,KAAK2D,wBAAL,CAA8B1D,WAA9B;;IACA,KAAKuB,kBAAL,CAAwBqD,YAAxB;;IACA,OAAO,KAAK7E,QAAZ;EACH;EACD;;;EACAiF,KAAK,GAAG;IACJ,IAAI,CAAC,KAAKrD,SAAV,EAAqB;MACjB,KAAKtB,WAAL,CAAiByC,aAAjB,CAA+BkC,KAA/B;;MACA,KAAK5C,QAAL,CAAc6C,IAAd,CAAmB;QAAER,IAAI,EAAE;MAAR,CAAnB;IACH;;IACD,KAAK9C,SAAL,GAAiB,IAAjB;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIuD,MAAM,GAAG;IACL,IAAI,KAAKnB,SAAT,EAAoB;MAChB,KAAKvB,OAAL,CAAagC,IAAb,CAAkB;QAAEC,IAAI,EAAE;MAAR,CAAlB;IACH;EACJ;EACD;;;EACAU,YAAY,CAACC,KAAD,EAAQ;IAChB,IAAI,KAAK9B,QAAT,EAAmB;MACf8B,KAAK,CAACC,cAAN;IACH;EACJ;EACD;;;EACAC,cAAc,CAACF,KAAD,EAAQ;IAClB,IAAI,KAAK9B,QAAT,EAAmB;MACf;IACH;;IACD,QAAQ8B,KAAK,CAACG,OAAd;MACI,KAAKvI,MAAL;MACA,KAAKD,SAAL;QACI;QACA,KAAKmI,MAAL,GAFJ,CAGI;;QACAE,KAAK,CAACC,cAAN;QACA;;MACJ,KAAKvI,KAAL;QACI;QACA,IAAI,KAAKgH,UAAT,EAAqB;UACjB,KAAKiB,cAAL,CAAoB,IAApB;QACH,CAJL,CAKI;;;QACAK,KAAK,CAACC,cAAN;QACA;IAfR;EAiBH;;EACDG,KAAK,GAAG;IACJ;IACA;IACA;IACA;IACA,KAAKpE,OAAL,CAAaqE,QAAb,CAAsBC,IAAtB,CAA2BvG,IAAI,CAAC,CAAD,CAA/B,EAAoCwG,SAApC,CAA8C,MAAM;MAChD,KAAKvE,OAAL,CAAawE,GAAb,CAAiB,MAAM;QACnB,KAAKjE,SAAL,GAAiB,KAAjB;;QACA,KAAKU,OAAL,CAAa4C,IAAb,CAAkB;UAAER,IAAI,EAAE;QAAR,CAAlB;MACH,CAHD;IAIH,CALD;EAMH;;EACDf,wBAAwB,CAAC1D,WAAW,GAAG,KAAf,EAAsB;IAC1C,KAAKsC,eAAL,CAAqBkC,IAArB,CAA0B;MACtB1E,MAAM,EAAE,IADc;MAEtBE,WAFsB;MAGtBD,QAAQ,EAAE,KAAKiC;IAHO,CAA1B;EAKH;;AA3NmC;;AA6NxCd,OAAO,CAACV,IAAR;EAAA,iBAAoGU,OAApG,EAxPgG9D,EAwPhG,mBAA6HA,EAAE,CAACyI,UAAhI,GAxPgGzI,EAwPhG,mBAAuJA,EAAE,CAAC0I,MAA1J,GAxPgG1I,EAwPhG,mBAA6K0B,EAAE,CAACiH,QAAhL,GAxPgG3I,EAwPhG,mBAAqMqB,yBAArM,MAxPgGrB,EAwPhG,mBAA2PA,EAAE,CAAC4I,iBAA9P,GAxPgG5I,EAwPhG,mBAA4R2B,QAA5R,GAxPgG3B,EAwPhG,mBAAiT4B,qBAAjT,MAxPgG5B,EAwPhG,mBAAmW,UAAnW;AAAA;;AACA8D,OAAO,CAACT,IAAR,kBAzPgGrD,EAyPhG;EAAA,MAAwF8D,OAAxF;EAAA;EAAA;IAAA;MAzPgG9D,EAyPhG,0BAA+nC8C,eAA/nC;MAzPgG9C,EAyPhG,0BAA6tC+C,sBAA7tC;MAzPgG/C,EAyPhG,0BAAg0C6C,eAAh0C;IAAA;;IAAA;MAAA;;MAzPgG7C,EAyPhG,qBAzPgGA,EAyPhG;MAzPgGA,EAyPhG,qBAzPgGA,EAyPhG;MAzPgGA,EAyPhG,qBAzPgGA,EAyPhG;IAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAzPgGA,EAyPhG;QAAA,OAAwF,wBAAxF;MAAA;QAAA,OAAwF,0BAAxF;MAAA;QAAA,OAAwF,WAAxF;MAAA;QAAA,OAAwF,WAAxF;MAAA;IAAA;;IAAA;MAzPgGA,EAyPhG;MAzPgGA,EAyPhG;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WAzPgGA,EAyPhG;AAAA;;AACA;EAAA,mDA1PgGA,EA0PhG,mBAA2F8D,OAA3F,EAAgH,CAAC;IACrGN,IAAI,EAAEtD,SAD+F;IAErGuD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAG,wDADZ;MAECmF,MAAM,EAAE,CAAC,OAAD,EAAU,eAAV,EAA2B,UAA3B,CAFT;MAGCC,QAAQ,EAAE,SAHX;MAICnF,IAAI,EAAE;QACF,SAAS,8BADP;QAEF,mBAAmB,4BAFjB;QAGF,eAAe,MAHb;QAIF,6BAA6B,UAJ3B;QAKF,gCAAgC,QAL9B;QAMF,uCAAuC,4BANrC;QAOF,6BAA6B,UAP3B;QAQF,mCAAmC,qBARjC;QASF,mBAAmB,kBATjB;QAUF,wBAAwB,qBAVtB;QAWF,wBAAwB,cAXtB;QAYF,WAAW,sBAZT;QAaF,aAAa,wBAbX;QAcF,WAAW,SAdT;QAeF,UAAU;MAfR;IAJP,CAAD;EAF+F,CAAD,CAAhH,EAwB4B,YAAY;IAAE,OAAO,CAAC;MAAEH,IAAI,EAAExD,EAAE,CAACyI;IAAX,CAAD,EAA0B;MAAEjF,IAAI,EAAExD,EAAE,CAAC0I;IAAX,CAA1B,EAA+C;MAAElF,IAAI,EAAE9B,EAAE,CAACiH;IAAX,CAA/C,EAAsE;MAAEnF,IAAI,EAAEgD,SAAR;MAAmBuC,UAAU,EAAE,CAAC;QACnIvF,IAAI,EAAEpD;MAD6H,CAAD,EAEnI;QACCoD,IAAI,EAAEnD,MADP;QAECoD,IAAI,EAAE,CAACpC,yBAAD;MAFP,CAFmI;IAA/B,CAAtE,EAK3B;MAAEmC,IAAI,EAAExD,EAAE,CAAC4I;IAAX,CAL2B,EAKK;MAAEpF,IAAI,EAAEgD,SAAR;MAAmBuC,UAAU,EAAE,CAAC;QAClEvF,IAAI,EAAEnD,MAD4D;QAElEoD,IAAI,EAAE,CAAC9B,QAAD;MAF4D,CAAD;IAA/B,CALL,EAQ3B;MAAE6B,IAAI,EAAEgD,SAAR;MAAmBuC,UAAU,EAAE,CAAC;QAClCvF,IAAI,EAAEpD;MAD4B,CAAD,EAElC;QACCoD,IAAI,EAAEnD,MADP;QAECoD,IAAI,EAAE,CAAC7B,qBAAD;MAFP,CAFkC;IAA/B,CAR2B,EAa3B;MAAE4B,IAAI,EAAEgD,SAAR;MAAmBuC,UAAU,EAAE,CAAC;QAClCvF,IAAI,EAAElD,SAD4B;QAElCmD,IAAI,EAAE,CAAC,UAAD;MAF4B,CAAD;IAA/B,CAb2B,CAAP;EAgBlB,CAxCxB,EAwC0C;IAAEuF,MAAM,EAAE,CAAC;MACrCxF,IAAI,EAAEjD,YAD+B;MAErCkD,IAAI,EAAE,CAACX,eAAD;IAF+B,CAAD,CAAV;IAG1BmG,YAAY,EAAE,CAAC;MACfzF,IAAI,EAAEjD,YADS;MAEfkD,IAAI,EAAE,CAACV,sBAAD;IAFS,CAAD,CAHY;IAM1BmG,UAAU,EAAE,CAAC;MACb1F,IAAI,EAAEjD,YADO;MAEbkD,IAAI,EAAE,CAACZ,eAAD;IAFO,CAAD,CANc;IAS1B8B,IAAI,EAAE,CAAC;MACPnB,IAAI,EAAEhD;IADC,CAAD,CAToB;IAW1BmC,QAAQ,EAAE,CAAC;MACXa,IAAI,EAAEhD;IADK,CAAD,CAXgB;IAa1B4F,KAAK,EAAE,CAAC;MACR5C,IAAI,EAAEhD;IADE,CAAD,CAbmB;IAe1BkG,UAAU,EAAE,CAAC;MACblD,IAAI,EAAEhD;IADO,CAAD,CAfc;IAiB1B0F,QAAQ,EAAE,CAAC;MACX1C,IAAI,EAAEhD;IADK,CAAD,CAjBgB;IAmB1BmG,SAAS,EAAE,CAAC;MACZnD,IAAI,EAAEhD;IADM,CAAD,CAnBe;IAqB1B0E,eAAe,EAAE,CAAC;MAClB1B,IAAI,EAAE/C;IADY,CAAD,CArBS;IAuB1B0E,SAAS,EAAE,CAAC;MACZ3B,IAAI,EAAE/C;IADM,CAAD,CAvBe;IAyB1B2E,OAAO,EAAE,CAAC;MACV5B,IAAI,EAAE/C;IADI,CAAD;EAzBiB,CAxC1C;AAAA;AAoEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM0I,aAAN,CAAoB;EAChB1G,WAAW,CAAC2G,WAAD,EAAcrF,UAAd,EAA0B;IACjC,KAAKqF,WAAL,GAAmBA,WAAnB;;IACA,IAAIrF,UAAU,CAAC2B,aAAX,CAAyB2D,QAAzB,KAAsC,QAA1C,EAAoD;MAChDtF,UAAU,CAAC2B,aAAX,CAAyB4D,YAAzB,CAAsC,MAAtC,EAA8C,QAA9C;IACH;EACJ;EACD;;;EACAvB,YAAY,CAACC,KAAD,EAAQ;IAChB,MAAMuB,UAAU,GAAG,KAAKH,WAAxB;;IACA,IAAIG,UAAU,CAAC5C,SAAX,IAAwB,CAAC4C,UAAU,CAACrD,QAAxC,EAAkD;MAC9CqD,UAAU,CAACzB,MAAX;IACH,CAJe,CAKhB;IACA;IACA;IACA;IACA;;;IACAE,KAAK,CAACwB,eAAN;IACAxB,KAAK,CAACC,cAAN;EACH;;AApBe;;AAsBpBkB,aAAa,CAAC/F,IAAd;EAAA,iBAA0G+F,aAA1G,EAjWgGnJ,EAiWhG,mBAAyI8D,OAAzI,GAjWgG9D,EAiWhG,mBAA6JA,EAAE,CAACyI,UAAhK;AAAA;;AACAU,aAAa,CAAC9F,IAAd,kBAlWgGrD,EAkWhG;EAAA,MAA8FmJ,aAA9F;EAAA;EAAA;EAAA;IAAA;MAlWgGnJ,EAkWhG;QAAA,OAA8F,wBAA9F;MAAA;IAAA;EAAA;EAAA,WAlWgGA,EAkWhG,oBAAyQ,CAAC;IAAEsD,OAAO,EAAET,eAAX;IAA4BU,WAAW,EAAE4F;EAAzC,CAAD,CAAzQ;AAAA;;AACA;EAAA,mDAnWgGnJ,EAmWhG,mBAA2FmJ,aAA3F,EAAsH,CAAC;IAC3G3F,IAAI,EAAEtD,SADqG;IAE3GuD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBADX;MAECC,IAAI,EAAE;QACF,SAAS,wCADP;QAEF,WAAW;MAFT,CAFP;MAMCC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAET,eAAX;QAA4BU,WAAW,EAAE4F;MAAzC,CAAD;IANZ,CAAD;EAFqG,CAAD,CAAtH,EAU4B,YAAY;IAAE,OAAO,CAAC;MAAE3F,IAAI,EAAEM;IAAR,CAAD,EAAoB;MAAEN,IAAI,EAAExD,EAAE,CAACyI;IAAX,CAApB,CAAP;EAAsD,CAVhG;AAAA;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMgB,yBAAyB,GAAG,IAAIxJ,cAAJ,CAAmB,2BAAnB,CAAlC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;AACA,MAAMyJ,gBAAgB,GAAGpI,eAAe,CAAC,MAAM;EAC3CmB,WAAW,CAACkH,yBAAD,EAA4BC,WAA5B,EAAyCC,gBAAzC;EACX;AACJ;AACA;AACA;AACA;EACIC,SANW,EAMA;IACP,KAAKH,yBAAL,GAAiCA,yBAAjC;IACA,KAAKC,WAAL,GAAmBA,WAAnB;IACA,KAAKC,gBAAL,GAAwBA,gBAAxB;IACA,KAAKC,SAAL,GAAiBA,SAAjB;IACA;AACR;AACA;AACA;AACA;;IACQ,KAAKC,YAAL,GAAoB,IAAIlI,OAAJ,EAApB;EACH;;AAlB0C,CAAP,CAAxC,C,CAoBA;;;AACA,IAAImI,cAAc,GAAG,CAArB;AACA;;AACA,MAAMC,iBAAN,CAAwB;EACpBxH,WAAW;EACX;EACAC,MAFW;EAGX;EACA0D,KAJW,EAIJ;IACH,KAAK1D,MAAL,GAAcA,MAAd;IACA,KAAK0D,KAAL,GAAaA,KAAb;EACH;;AARmB;AAUxB;AACA;AACA;;;AACA,MAAM8D,WAAN,SAA0BR,gBAA1B,CAA2C;EACvCjH,WAAW,CAACQ,WAAD,EAAckB,kBAAd,EAAkCgG,IAAlC,EAAwCP,WAAxC,EAAqDC,gBAArD,EAAuEF,yBAAvE,EAAkGG,SAAlG,EAA6G;IACpH,MAAMH,yBAAN,EAAiCC,WAAjC,EAA8CC,gBAA9C,EAAgEC,SAAhE;IACA,KAAK7G,WAAL,GAAmBA,WAAnB;IACA,KAAKkB,kBAAL,GAA0BA,kBAA1B;IACA,KAAKgG,IAAL,GAAYA,IAAZ;IACA;AACR;AACA;AACA;;IACQ,KAAKC,WAAL,GAAmB,eAAnB;IACA;AACR;AACA;AACA;AACA;;IACQ,KAAKC,uBAAL,GAA+B,IAA/B;IACA;;IACA,KAAKC,UAAL,GAAkB,IAAIzI,OAAJ,EAAlB;IACA;;IACA,KAAK0I,IAAL,GAAa,iBAAgBP,cAAc,EAAG,EAA9C;IACA;;IACA,KAAKQ,SAAL,GAAiB,CAAjB;IACA;AACR;AACA;AACA;;IACQ,KAAKC,aAAL,GAAqB,IAArB;IACA;;IACA,KAAKC,UAAL,GAAkB,MAAM,CAAG,CAA3B;IACA;;;IACA,KAAKC,SAAL,GAAiB,MAAM,CAAG,CAA1B;;IACA,KAAKC,SAAL,GAAiB,KAAjB;;IACA,KAAKC,YAAL,GAAoB,CAACC,EAAD,EAAKC,EAAL,KAAYD,EAAE,KAAKC,EAAvC;;IACA,KAAKjG,SAAL,GAAiB,KAAjB;IACA;;IACA,KAAKkG,eAAL,GAAuB,YAAvB;IACA,KAAKnG,WAAL,GAAmB,IAAnB;IACA;;IACA,KAAKoG,MAAL,GAAc,IAAI9K,YAAJ,EAAd;IACA;AACR;AACA;AACA;AACA;;IACQ,KAAK+K,WAAL,GAAmB,IAAI/K,YAAJ,EAAnB;;IACA,IAAI,KAAK2J,SAAT,EAAoB;MAChB,KAAKA,SAAL,CAAeqB,aAAf,GAA+B,IAA/B;IACH;EACJ;EACD;;;EACY,IAARxI,QAAQ,GAAG;IACX,OAAO,KAAKyI,QAAL,GAAgB,KAAKC,eAAL,EAAsB1I,QAAtB,IAAkC,EAAlD,GAAuD,KAAK0I,eAAL,EAAsB1I,QAAtB,CAA+B,CAA/B,CAA9D;EACH;EACD;;;EACQ,IAAJgC,IAAI,GAAG;IACP,IAAI,KAAK2G,aAAT,EAAwB;MACpB,OAAO,KAAKA,aAAZ;IACH;;IACD,OAAO,KAAKC,KAAL,GAAa,IAAb,GAAoB,SAA3B;EACH;;EACO,IAAJ5G,IAAI,CAACA,IAAD,EAAO;IACX,KAAK2G,aAAL,GAAqB3G,IAArB;EACH;EACD;;;EACY,IAARyG,QAAQ,GAAG;IACX,OAAO,KAAKR,SAAZ;EACH;;EACW,IAARQ,QAAQ,CAAChF,KAAD,EAAQ;IAChB,KAAKwE,SAAL,GAAiBnJ,qBAAqB,CAAC2E,KAAD,CAAtC;;IACA,KAAKoF,eAAL;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACmB,IAAXC,WAAW,GAAG;IACd,OAAO,KAAKZ,YAAZ;EACH;;EACc,IAAXY,WAAW,CAACC,EAAD,EAAK;IAChB,KAAKb,YAAL,GAAoBa,EAApB;;IACA,IAAI,KAAKL,eAAT,EAA0B;MACtB;MACA,KAAKM,oBAAL;IACH;EACJ;EACD;AACJ;AACA;AACA;;;EACa,IAALvF,KAAK,GAAG;IACR,OAAO,KAAKG,MAAZ;EACH;;EACQ,IAALH,KAAK,CAACA,KAAD,EAAQ;IACb,KAAKwF,UAAL,CAAgBxF,KAAhB;IACA,KAAKG,MAAL,GAAcH,KAAd;EACH;EACD;AACJ;AACA;AACA;;;EACU,IAAFyF,EAAE,GAAG;IACL,OAAO,KAAKC,UAAL,GAAkB,KAAKA,UAAL,CAAgBD,EAAlC,GAAuC,KAAKtB,IAAnD;EACH;EACD;AACJ;AACA;AACA;;;EACgB,IAARwB,QAAQ,GAAG;IACX,OAAO,KAAKC,SAAL,IAAkB,KAAKlC,SAAL,EAAgBmC,OAAhB,EAAyBC,YAAzB,CAAsC5J,UAAU,CAACyJ,QAAjD,CAAlB,IAAgF,KAAvF;EACH;;EACW,IAARA,QAAQ,CAAC3F,KAAD,EAAQ;IAChB,KAAK4F,SAAL,GAAiBvK,qBAAqB,CAAC2E,KAAD,CAAtC;IACA,KAAK2D,YAAL,CAAkBlC,IAAlB;EACH;EACD;AACJ;AACA;AACA;;;EACmB,IAAXsE,WAAW,GAAG;IACd,OAAO,KAAKL,UAAL,GAAkB,KAAKA,UAAL,CAAgBK,WAAlC,GAAgD,KAAKC,YAA5D;EACH;;EACc,IAAXD,WAAW,CAAC/F,KAAD,EAAQ;IACnB,KAAKgG,YAAL,GAAoBhG,KAApB;IACA,KAAK2D,YAAL,CAAkBlC,IAAlB;EACH;EACD;;;EACW,IAAPwE,OAAO,GAAG;IACV,OAAQ,KAAKP,UAAL,IAAmB,KAAKA,UAAL,CAAgBO,OAApC,IAAgD,KAAKC,eAAL,EAAvD;EACH;EACD;AACJ;AACA;AACA;;;EACa,IAALf,KAAK,GAAG;IACR,OAAO,CAAC,CAAC,KAAKO,UAAN,IAAoB,KAAKA,UAAL,CAAgBP,KAArC,MAAgD,CAAC,KAAKgB,KAAN,IAAe,KAAKA,KAAL,CAAWC,MAAX,KAAsB,CAArF,CAAP;EACH;EACD;AACJ;AACA;AACA;;;EACwB,IAAhBC,gBAAgB,GAAG;IACnB,OAAO,CAAC,KAAKlB,KAAN,IAAe,KAAKc,OAA3B;EACH;EACD;AACJ;AACA;AACA;;;EACgB,IAARnG,QAAQ,GAAG;IACX,OAAO,KAAK4D,SAAL,GAAiB,CAAC,CAAC,KAAKA,SAAL,CAAe5D,QAAlC,GAA6C,KAAKpB,SAAzD;EACH;;EACW,IAARoB,QAAQ,CAACE,KAAD,EAAQ;IAChB,KAAKtB,SAAL,GAAiBrD,qBAAqB,CAAC2E,KAAD,CAAtC;;IACA,KAAKoF,eAAL;EACH;EACD;AACJ;AACA;AACA;;;EACkB,IAAV9E,UAAU,GAAG;IACb,OAAO,KAAK7B,WAAZ;EACH;;EACa,IAAV6B,UAAU,CAACN,KAAD,EAAQ;IAClB,KAAKvB,WAAL,GAAmBpD,qBAAqB,CAAC2E,KAAD,CAAxC;;IACA,KAAKoF,eAAL;EACH;;EACW,IAARlH,QAAQ,CAAC8B,KAAD,EAAQ;IAChB,KAAKqE,aAAL,GAAqBrE,KAArB;IACA,KAAKoE,SAAL,GAAiBpE,KAAjB;EACH;EACD;;;EACwB,IAApBsG,oBAAoB,GAAG;IACvB,OAAO5K,KAAK,CAAC,GAAG,KAAKyK,KAAL,CAAWI,GAAX,CAAetF,IAAI,IAAIA,IAAI,CAACnC,eAA5B,CAAJ,CAAZ;EACH;EACD;;;EACoB,IAAhB0H,gBAAgB,GAAG;IACnB,OAAO9K,KAAK,CAAC,GAAG,KAAKyK,KAAL,CAAWI,GAAX,CAAetF,IAAI,IAAIA,IAAI,CAACrC,QAA5B,CAAJ,CAAZ;EACH;EACD;;;EACmB,IAAf6H,eAAe,GAAG;IAClB,OAAO/K,KAAK,CAAC,GAAG,KAAKyK,KAAL,CAAWI,GAAX,CAAetF,IAAI,IAAIA,IAAI,CAACpC,OAA5B,CAAJ,CAAZ;EACH;EACD;;;EACqB,IAAjB6H,iBAAiB,GAAG;IACpB,OAAOhL,KAAK,CAAC,GAAG,KAAKyK,KAAL,CAAWI,GAAX,CAAetF,IAAI,IAAIA,IAAI,CAAClC,SAA5B,CAAJ,CAAZ;EACH;;EACD4H,kBAAkB,GAAG;IACjB,KAAKC,WAAL,GAAmB,IAAI9K,eAAJ,CAAoB,KAAKqK,KAAzB,EACdU,QADc,GAEdC,uBAFc,GAGdC,cAHc,GAIdC,yBAJc,CAIY,KAAKjD,IAAL,GAAY,KAAKA,IAAL,CAAU/D,KAAtB,GAA8B,KAJ1C,CAAnB;;IAKA,IAAI,KAAK+D,IAAT,EAAe;MACX,KAAKA,IAAL,CAAUc,MAAV,CACK3C,IADL,CACUtG,SAAS,CAAC,KAAKsI,UAAN,CADnB,EAEK/B,SAFL,CAEe8E,GAAG,IAAI,KAAKL,WAAL,CAAiBI,yBAAjB,CAA2CC,GAA3C,CAFtB;IAGH;;IACD,KAAKL,WAAL,CAAiBM,MAAjB,CAAwBhF,IAAxB,CAA6BtG,SAAS,CAAC,KAAKsI,UAAN,CAAtC,EAAyD/B,SAAzD,CAAmE,MAAM;MACrE,KAAKgF,iBAAL;IACH,CAFD,EAXiB,CAcjB;;;IACA,KAAKhB,KAAL,CAAWiB,OAAX,CAAmBlF,IAAnB,CAAwBrG,SAAS,CAAC,IAAD,CAAjC,EAAyCD,SAAS,CAAC,KAAKsI,UAAN,CAAlD,EAAqE/B,SAArE,CAA+E,MAAM;MACjF,IAAI,KAAKrC,QAAL,IAAiB,CAAC,KAAKQ,UAA3B,EAAuC;QACnC;QACA;QACA+G,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuB,MAAM;UACzB,KAAKnC,eAAL;QACH,CAFD;MAGH;;MACD,KAAKoC,WAAL,GARiF,CASjF;;;MACA,KAAKjC,oBAAL,GAViF,CAWjF;;;MACA,KAAKkC,eAAL,GAZiF,CAajF;;;MACA,KAAKC,6BAAL;;MACA,KAAK/D,YAAL,CAAkBlC,IAAlB;IACH,CAhBD;EAiBH;;EACDkG,QAAQ,GAAG;IACP,KAAK1C,eAAL,GAAuB,IAAIjJ,cAAJ,CAAmB,KAAKgJ,QAAxB,EAAkC5E,SAAlC,EAA6C,KAA7C,CAAvB;IACA,KAAKuD,YAAL,CAAkBlC,IAAlB;EACH;;EACDmG,SAAS,GAAG;IACR,IAAI,KAAKlE,SAAT,EAAoB;MAChB;MACA;MACA;MACA,KAAKmE,gBAAL;;MACA,IAAI,KAAKnE,SAAL,CAAe5D,QAAf,KAA4B,KAAKpB,SAArC,EAAgD;QAC5C,KAAKoB,QAAL,GAAgB,CAAC,CAAC,KAAK4D,SAAL,CAAe5D,QAAjC;MACH;IACJ;EACJ;;EACDiB,WAAW,GAAG;IACV,KAAKmD,UAAL,CAAgBzC,IAAhB;;IACA,KAAKyC,UAAL,CAAgB4D,QAAhB;;IACA,KAAKnE,YAAL,CAAkBmE,QAAlB;;IACA,KAAKC,kBAAL;EACH;EACD;;;EACAC,aAAa,CAACC,YAAD,EAAe;IACxB,KAAKvC,UAAL,GAAkBuC,YAAlB,CADwB,CAExB;IACA;;IACA,KAAKpL,WAAL,CAAiByC,aAAjB,CAA+B4D,YAA/B,CAA4C,qBAA5C,EAAmE+E,YAAY,CAACxC,EAAhF;EACH;EACD;AACJ;AACA;AACA;;;EACIyC,iBAAiB,CAACC,GAAD,EAAM;IACnB,IAAIA,GAAG,CAAC/B,MAAR,EAAgB;MACZ,KAAKvJ,WAAL,CAAiByC,aAAjB,CAA+B4D,YAA/B,CAA4C,kBAA5C,EAAgEiF,GAAG,CAACC,IAAJ,CAAS,GAAT,CAAhE;IACH,CAFD,MAGK;MACD,KAAKvL,WAAL,CAAiByC,aAAjB,CAA+B+I,eAA/B,CAA+C,kBAA/C;IACH;EACJ,CAnQsC,CAoQvC;;;EACA7C,UAAU,CAACxF,KAAD,EAAQ;IACd,IAAI,KAAKmG,KAAT,EAAgB;MACZ,KAAKmC,oBAAL,CAA0BtI,KAA1B,EAAiC,KAAjC;IACH;EACJ,CAzQsC,CA0QvC;;;EACAuI,gBAAgB,CAACjD,EAAD,EAAK;IACjB,KAAKf,SAAL,GAAiBe,EAAjB;EACH,CA7QsC,CA8QvC;;;EACAkD,iBAAiB,CAAClD,EAAD,EAAK;IAClB,KAAKhB,UAAL,GAAkBgB,EAAlB;EACH,CAjRsC,CAkRvC;;;EACAmD,gBAAgB,CAACC,UAAD,EAAa;IACzB,KAAK5I,QAAL,GAAgB4I,UAAhB;IACA,KAAK/E,YAAL,CAAkBlC,IAAlB;EACH;EACD;AACJ;AACA;AACA;;;EACIkH,gBAAgB,CAAC/G,KAAD,EAAQ;IACpB,IAAI,CAAC,KAAKgH,mBAAL,CAAyBhH,KAAzB,CAAL,EAAsC;MAClC,KAAKJ,KAAL;IACH;EACJ;EACD;AACJ;AACA;AACA;;;EACIA,KAAK,CAACqH,OAAD,EAAU;IACX,IAAI,KAAK/I,QAAT,EAAmB;MACf;IACH,CAHU,CAIX;IACA;;;IACA,IAAI,KAAK4F,UAAL,IAAmB,KAAKA,UAAL,CAAgBO,OAAvC,EAAgD,CAC5C;IACH,CAFD,MAGK,IAAI,KAAKE,KAAL,CAAWC,MAAX,GAAoB,CAAxB,EAA2B;MAC5B,KAAKQ,WAAL,CAAiBkC,kBAAjB;;MACA,KAAKnF,YAAL,CAAkBlC,IAAlB;IACH,CAHI,MAIA;MACD,KAAKsH,WAAL,CAAiBF,OAAjB;;MACA,KAAKlF,YAAL,CAAkBlC,IAAlB;IACH;EACJ;EACD;;;EACAsH,WAAW,CAACF,OAAD,EAAU;IACjB,IAAI,KAAKnD,UAAT,EAAqB;MACjB,KAAKA,UAAL,CAAgBlE,KAAhB,CAAsBqH,OAAtB;IACH;EACJ;EACD;AACJ;AACA;;;EACIG,QAAQ,CAACpH,KAAD,EAAQ;IACZ,MAAMqH,MAAM,GAAGrH,KAAK,CAACqH,MAArB;;IACA,IAAIA,MAAM,IAAIA,MAAM,CAAC7J,SAAP,CAAiB8J,QAAjB,CAA0B,UAA1B,CAAd,EAAqD;MACjD,KAAKtC,WAAL,CAAiBuC,SAAjB,CAA2BvH,KAA3B;;MACA,KAAK+B,YAAL,CAAkBlC,IAAlB;IACH;EACJ;EACD;AACJ;AACA;;;EACIgG,eAAe,GAAG;IACd;IACA,KAAKrD,SAAL,GAAiB,KAAKC,aAAL,KAAuB,KAAK8B,KAAL,CAAWC,MAAX,KAAsB,CAAtB,GAA0B,CAAC,CAA3B,GAA+B,CAAtD,CAAjB;EACH;EACD;AACJ;AACA;AACA;;;EACIsB,6BAA6B,GAAG;IAC5B;IACA,IAAI,KAAKzD,uBAAL,IAAgC,IAApC,EAA0C;MACtC,IAAI,KAAKkC,KAAL,CAAWC,MAAf,EAAuB;QACnB,MAAMgD,YAAY,GAAGC,IAAI,CAACC,GAAL,CAAS,KAAKrF,uBAAd,EAAuC,KAAKkC,KAAL,CAAWC,MAAX,GAAoB,CAA3D,CAArB;;QACA,KAAKQ,WAAL,CAAiB2C,aAAjB,CAA+BH,YAA/B;MACH,CAHD,MAIK;QACD,KAAK5H,KAAL;MACH;IACJ;;IACD,KAAKyC,uBAAL,GAA+B,IAA/B;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIuF,aAAa,CAACC,KAAD,EAAQ;IACjB,OAAOA,KAAK,IAAI,CAAT,IAAcA,KAAK,GAAG,KAAKtD,KAAL,CAAWC,MAAxC;EACH;;EACDkC,oBAAoB,CAACtI,KAAD,EAAQxD,WAAW,GAAG,IAAtB,EAA4B;IAC5C,KAAKkN,eAAL;;IACA,KAAKvD,KAAL,CAAWwD,OAAX,CAAmB1I,IAAI,IAAIA,IAAI,CAACI,QAAL,EAA3B;;IACA,IAAIuI,KAAK,CAACC,OAAN,CAAc7J,KAAd,CAAJ,EAA0B;MACtBA,KAAK,CAAC2J,OAAN,CAAcG,YAAY,IAAI,KAAKC,YAAL,CAAkBD,YAAlB,EAAgCtN,WAAhC,CAA9B;;MACA,KAAKwN,WAAL;IACH,CAHD,MAIK;MACD,MAAMC,iBAAiB,GAAG,KAAKF,YAAL,CAAkB/J,KAAlB,EAAyBxD,WAAzB,CAA1B,CADC,CAED;MACA;;;MACA,IAAIyN,iBAAJ,EAAuB;QACnB,IAAIzN,WAAJ,EAAiB;UACb,KAAKoK,WAAL,CAAiB2C,aAAjB,CAA+BU,iBAA/B;QACH;MACJ;IACJ;EACJ;EACD;AACJ;AACA;AACA;;;EACIF,YAAY,CAAC/J,KAAD,EAAQxD,WAAW,GAAG,IAAtB,EAA4B;IACpC,MAAMyN,iBAAiB,GAAG,KAAK9D,KAAL,CAAW+D,IAAX,CAAgBjJ,IAAI,IAAI;MAC9C,OAAOA,IAAI,CAACjB,KAAL,IAAc,IAAd,IAAsB,KAAKyE,YAAL,CAAkBxD,IAAI,CAACjB,KAAvB,EAA8BA,KAA9B,CAA7B;IACH,CAFyB,CAA1B;;IAGA,IAAIiK,iBAAJ,EAAuB;MACnBzN,WAAW,GAAGyN,iBAAiB,CAAC3I,oBAAlB,EAAH,GAA8C2I,iBAAiB,CAAC9I,MAAlB,EAAzD;;MACA,KAAK8D,eAAL,CAAqB9D,MAArB,CAA4B8I,iBAA5B;IACH;;IACD,OAAOA,iBAAP;EACH;;EACD1E,oBAAoB,GAAG;IACnB;IACA;IACA8B,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuB,MAAM;MACzB,IAAI,KAAK7D,SAAL,IAAkB,KAAKvD,MAA3B,EAAmC;QAC/B,KAAKmI,oBAAL,CAA0B,KAAK5E,SAAL,GAAiB,KAAKA,SAAL,CAAe1D,KAAhC,GAAwC,KAAKG,MAAvE,EAA+E,KAA/E;;QACA,KAAKwD,YAAL,CAAkBlC,IAAlB;MACH;IACJ,CALD;EAMH;EACD;AACJ;AACA;AACA;;;EACIiI,eAAe,CAACS,IAAD,EAAO;IAClB,KAAKlF,eAAL,CAAqBmF,KAArB;;IACA,KAAKjE,KAAL,CAAWwD,OAAX,CAAmB1I,IAAI,IAAI;MACvB,IAAIA,IAAI,KAAKkJ,IAAb,EAAmB;QACflJ,IAAI,CAACI,QAAL;MACH;IACJ,CAJD;IAKA,KAAKsC,YAAL,CAAkBlC,IAAlB;EACH;EACD;AACJ;AACA;AACA;;;EACIuI,WAAW,GAAG;IACV,IAAI,KAAKxF,SAAT,EAAoB;MAChB,KAAKS,eAAL,CAAqBmF,KAArB;;MACA,KAAKjE,KAAL,CAAWwD,OAAX,CAAmB1I,IAAI,IAAI;QACvB,IAAIA,IAAI,CAAC1E,QAAT,EAAmB;UACf,KAAK0I,eAAL,CAAqB9D,MAArB,CAA4BF,IAA5B;QACH;MACJ,CAJD;MAKA,KAAK0C,YAAL,CAAkBlC,IAAlB;IACH;EACJ;EACD;;;EACA4I,iBAAiB,CAACC,aAAD,EAAgB;IAC7B,IAAIC,WAAW,GAAG,IAAlB;;IACA,IAAIX,KAAK,CAACC,OAAN,CAAc,KAAKtN,QAAnB,CAAJ,EAAkC;MAC9BgO,WAAW,GAAG,KAAKhO,QAAL,CAAcgK,GAAd,CAAkBtF,IAAI,IAAIA,IAAI,CAACjB,KAA/B,CAAd;IACH,CAFD,MAGK;MACDuK,WAAW,GAAG,KAAKhO,QAAL,GAAgB,KAAKA,QAAL,CAAcyD,KAA9B,GAAsCsK,aAApD;IACH;;IACD,KAAKnK,MAAL,GAAcoK,WAAd;IACA,KAAK1F,MAAL,CAAY7D,IAAZ,CAAiB,IAAI6C,iBAAJ,CAAsB,IAAtB,EAA4B0G,WAA5B,CAAjB;IACA,KAAKzF,WAAL,CAAiB9D,IAAjB,CAAsBuJ,WAAtB;;IACA,KAAKhG,SAAL,CAAegG,WAAf;;IACA,KAAKxM,kBAAL,CAAwBqD,YAAxB;EACH;EACD;;;EACAY,KAAK,GAAG;IACJ,IAAI,CAAC,KAAKkE,eAAL,EAAL,EAA6B;MACzB,KAAKU,WAAL,CAAiB2C,aAAjB,CAA+B,CAAC,CAAhC;IACH;;IACD,IAAI,CAAC,KAAKzJ,QAAV,EAAoB;MAChB,IAAI,KAAK4F,UAAT,EAAqB;QACjB;QACA;QACA;QACA;QACA8E,UAAU,CAAC,MAAM;UACb,IAAI,CAAC,KAAKvE,OAAV,EAAmB;YACf,KAAKwE,cAAL;UACH;QACJ,CAJS,CAAV;MAKH,CAVD,MAWK;QACD;QACA,KAAKA,cAAL;MACH;IACJ;EACJ;EACD;;;EACAA,cAAc,GAAG;IACb,KAAKnG,UAAL;;IACA,KAAKvG,kBAAL,CAAwBqD,YAAxB;;IACA,KAAKuC,YAAL,CAAkBlC,IAAlB;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACI0F,iBAAiB,GAAG;IAChB,IAAI,KAAK/C,SAAL,KAAmB,CAAC,CAAxB,EAA2B;MACvB,KAAKA,SAAL,GAAiB,CAAC,CAAlB;MACAoG,UAAU,CAAC,MAAM;QACb,KAAKpG,SAAL,GAAiB,KAAKC,aAAL,IAAsB,CAAvC;;QACA,KAAKtG,kBAAL,CAAwBqD,YAAxB;MACH,CAHS,CAAV;IAIH;EACJ;;EACDoG,WAAW,GAAG;IACV,KAAKO,kBAAL;;IACA,KAAK2C,mBAAL;;IACA,KAAKC,uBAAL;;IACA,KAAKC,qBAAL;EACH;;EACD7C,kBAAkB,GAAG;IACjB,IAAI,KAAK8C,sBAAT,EAAiC;MAC7B,KAAKA,sBAAL,CAA4BC,WAA5B;;MACA,KAAKD,sBAAL,GAA8B,IAA9B;IACH;;IACD,IAAI,KAAKE,qBAAT,EAAgC;MAC5B,KAAKA,qBAAL,CAA2BD,WAA3B;;MACA,KAAKC,qBAAL,GAA6B,IAA7B;IACH;;IACD,IAAI,KAAKC,0BAAT,EAAqC;MACjC,KAAKA,0BAAL,CAAgCF,WAAhC;;MACA,KAAKE,0BAAL,GAAkC,IAAlC;IACH;;IACD,IAAI,KAAKC,uBAAT,EAAkC;MAC9B,KAAKA,uBAAL,CAA6BH,WAA7B;;MACA,KAAKG,uBAAL,GAA+B,IAA/B;IACH;EACJ;EACD;;;EACAN,uBAAuB,GAAG;IACtB,KAAKK,0BAAL,GAAkC,KAAK1E,oBAAL,CAA0BnE,SAA1B,CAAoCP,KAAK,IAAI;MAC3EA,KAAK,CAACtF,MAAN,CAAaC,QAAb,GACM,KAAK0I,eAAL,CAAqB9D,MAArB,CAA4BS,KAAK,CAACtF,MAAlC,CADN,GAEM,KAAK2I,eAAL,CAAqB5D,QAArB,CAA8BO,KAAK,CAACtF,MAApC,CAFN,CAD2E,CAI3E;;MACA,IAAI,CAAC,KAAK0I,QAAV,EAAoB;QAChB,KAAKmB,KAAL,CAAWwD,OAAX,CAAmB1I,IAAI,IAAI;UACvB,IAAI,CAAC,KAAKgE,eAAL,CAAqBiG,UAArB,CAAgCjK,IAAhC,CAAD,IAA0CA,IAAI,CAAC1E,QAAnD,EAA6D;YACzD0E,IAAI,CAACI,QAAL;UACH;QACJ,CAJD;MAKH;;MACD,IAAIO,KAAK,CAACpF,WAAV,EAAuB;QACnB,KAAK6N,iBAAL;MACH;IACJ,CAfiC,CAAlC;EAgBH;EACD;;;EACAK,mBAAmB,GAAG;IAClB,KAAKG,sBAAL,GAA8B,KAAKrE,gBAAL,CAAsBrE,SAAtB,CAAgCP,KAAK,IAAI;MACnE,IAAIuJ,SAAS,GAAG,KAAKhF,KAAL,CAAWiF,OAAX,GAAqBC,OAArB,CAA6BzJ,KAAK,CAACX,IAAnC,CAAhB;;MACA,IAAI,KAAKuI,aAAL,CAAmB2B,SAAnB,CAAJ,EAAmC;QAC/B,KAAKvE,WAAL,CAAiB0E,gBAAjB,CAAkCH,SAAlC;MACH;;MACD,KAAKxH,YAAL,CAAkBlC,IAAlB;IACH,CAN6B,CAA9B;IAOA,KAAKsJ,qBAAL,GAA6B,KAAKtE,eAAL,CAAqBtE,SAArB,CAA+B,MAAM;MAC9D,KAAKH,KAAL;;MACA,KAAK2B,YAAL,CAAkBlC,IAAlB;IACH,CAH4B,CAA7B;EAIH;;EACDmJ,qBAAqB,GAAG;IACpB,KAAKK,uBAAL,GAA+B,KAAKvE,iBAAL,CAAuBvE,SAAvB,CAAiCP,KAAK,IAAI;MACrE,MAAMX,IAAI,GAAGW,KAAK,CAACX,IAAnB;MACA,MAAMkK,SAAS,GAAG,KAAKhF,KAAL,CAAWiF,OAAX,GAAqBC,OAArB,CAA6BzJ,KAAK,CAACX,IAAnC,CAAlB,CAFqE,CAGrE;MACA;MACA;;MACA,IAAI,KAAKuI,aAAL,CAAmB2B,SAAnB,KAAiClK,IAAI,CAAC9C,SAA1C,EAAqD;QACjD,KAAK8F,uBAAL,GAA+BkH,SAA/B;MACH;IACJ,CAT8B,CAA/B;EAUH;EACD;;;EACAvC,mBAAmB,CAAChH,KAAD,EAAQ;IACvB,IAAI2J,cAAc,GAAG3J,KAAK,CAACqH,MAA3B;;IACA,OAAOsC,cAAc,IAAIA,cAAc,KAAK,KAAK1O,WAAL,CAAiByC,aAA7D,EAA4E;MACxE,IAAIiM,cAAc,CAACnM,SAAf,CAAyB8J,QAAzB,CAAkC,UAAlC,CAAJ,EAAmD;QAC/C,OAAO,IAAP;MACH;;MACDqC,cAAc,GAAGA,cAAc,CAACC,aAAhC;IACH;;IACD,OAAO,KAAP;EACH;EACD;;;EACAtF,eAAe,GAAG;IACd,OAAO,KAAKC,KAAL,IAAc,KAAKA,KAAL,CAAWsF,IAAX,CAAgBxK,IAAI,IAAIA,IAAI,CAAC9C,SAA7B,CAArB;EACH;EACD;;;EACAiH,eAAe,GAAG;IACd,IAAI,KAAKe,KAAT,EAAgB;MACZ,KAAKA,KAAL,CAAWwD,OAAX,CAAmB1I,IAAI,IAAI;QACvBA,IAAI,CAAC3C,iBAAL,GAAyB,KAAKI,SAA9B;QACAuC,IAAI,CAAC5C,iBAAL,GAAyB,KAAK2G,QAA9B;QACA/D,IAAI,CAAC7C,kBAAL,GAA0B,KAAKK,WAA/B;MACH,CAJD;IAKH;EACJ;;AApkBsC;;AAskB3CqF,WAAW,CAAC9G,IAAZ;EAAA,iBAAwG8G,WAAxG,EA5+BgGlK,EA4+BhG,mBAAqIA,EAAE,CAACyI,UAAxI,GA5+BgGzI,EA4+BhG,mBAA+JA,EAAE,CAAC4I,iBAAlK,GA5+BgG5I,EA4+BhG,mBAAgMmC,IAAI,CAAC2P,cAArM,MA5+BgG9R,EA4+BhG,mBAAgPqC,EAAE,CAAC0P,MAAnP,MA5+BgG/R,EA4+BhG,mBAAsRqC,EAAE,CAAC2P,kBAAzR,MA5+BgGhS,EA4+BhG,mBAAwUgB,EAAE,CAACQ,iBAA3U,GA5+BgGxB,EA4+BhG,mBAAyWqC,EAAE,CAAC4P,SAA5W;AAAA;;AACA/H,WAAW,CAACgI,IAAZ,kBA7+BgGlS,EA6+BhG;EAAA,MAA4FkK,WAA5F;EAAA;EAAA;IAAA;MA7+BgGlK,EA6+BhG,0BAA2uC8D,OAA3uC;IAAA;;IAAA;MAAA;;MA7+BgG9D,EA6+BhG,qBA7+BgGA,EA6+BhG;IAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MA7+BgGA,EA6+BhG;QAAA,OAA4F,WAA5F;MAAA;QAAA,OAA4F,WAA5F;MAAA;QAAA,OAA4F,oBAA5F;MAAA;IAAA;;IAAA;MA7+BgGA,EA6+BhG;MA7+BgGA,EA6+BhG;MA7+BgGA,EA6+BhG;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WA7+BgGA,EA6+BhG,oBAA+nC,CAAC;IAAEsD,OAAO,EAAEf,mBAAX;IAAgCgB,WAAW,EAAE2G;EAA7C,CAAD,CAA/nC,GA7+BgGlK,EA6+BhG;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MA7+BgGA,EA6+BhG;MA7+BgGA,EA6+BqvC,4BAAr1C;MA7+BgGA,EA6+BwxC,gBAAx3C;MA7+BgGA,EA6+BizC,eAAj5C;IAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AACA;EAAA,mDA9+BgGA,EA8+BhG,mBAA2FkK,WAA3F,EAAoH,CAAC;IACzG1G,IAAI,EAAE9C,SADmG;IAEzG+C,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,eAAZ;MAA6ByO,QAAQ,EAAG,oEAAxC;MAA6GrJ,QAAQ,EAAE,aAAvH;MAAsInF,IAAI,EAAE;QACvI,mBAAmB,6BADoH;QAEvI,wBAAwB,wBAF+G;QAGvI,wBAAwB,qBAH+G;QAIvI,uBAAuB,YAJgH;QAKvI,+BAA+B,UALwG;QAMvI,eAAe,MANwH;QAOvI,kCAAkC,UAPqG;QAQvI,iCAAiC,YARsG;QASvI,kCAAkC,UATqG;QAUvI,2BAA2B,iBAV4G;QAWvI,SAAS,eAX8H;QAYvI,WAAW,SAZ4H;QAavI,UAAU,SAb6H;QAcvI,aAAa,kBAd0H;QAevI,QAAQ;MAf+H,CAA5I;MAgBIC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEf,mBAAX;QAAgCgB,WAAW,EAAE2G;MAA7C,CAAD,CAhBf;MAgB6EkI,aAAa,EAAEzR,iBAAiB,CAAC0R,IAhB9G;MAgBoHC,eAAe,EAAE1R,uBAAuB,CAAC2R,MAhB7J;MAgBqKC,MAAM,EAAE,CAAC,+yGAAD;IAhB7K,CAAD;EAFmG,CAAD,CAApH,EAmB4B,YAAY;IAAE,OAAO,CAAC;MAAEhP,IAAI,EAAExD,EAAE,CAACyI;IAAX,CAAD,EAA0B;MAAEjF,IAAI,EAAExD,EAAE,CAAC4I;IAAX,CAA1B,EAA0D;MAAEpF,IAAI,EAAErB,IAAI,CAAC2P,cAAb;MAA6B/I,UAAU,EAAE,CAAC;QACjIvF,IAAI,EAAEpD;MAD2H,CAAD;IAAzC,CAA1D,EAE3B;MAAEoD,IAAI,EAAEnB,EAAE,CAAC0P,MAAX;MAAmBhJ,UAAU,EAAE,CAAC;QAClCvF,IAAI,EAAEpD;MAD4B,CAAD;IAA/B,CAF2B,EAI3B;MAAEoD,IAAI,EAAEnB,EAAE,CAAC2P,kBAAX;MAA+BjJ,UAAU,EAAE,CAAC;QAC9CvF,IAAI,EAAEpD;MADwC,CAAD;IAA3C,CAJ2B,EAM3B;MAAEoD,IAAI,EAAExC,EAAE,CAACQ;IAAX,CAN2B,EAMK;MAAEgC,IAAI,EAAEnB,EAAE,CAAC4P,SAAX;MAAsBlJ,UAAU,EAAE,CAAC;QACrEvF,IAAI,EAAEpD;MAD+D,CAAD,EAErE;QACCoD,IAAI,EAAE3C;MADP,CAFqE;IAAlC,CANL,CAAP;EAUlB,CA7BxB,EA6B0C;IAAE8D,IAAI,EAAE,CAAC;MACnCnB,IAAI,EAAEhD;IAD6B,CAAD,CAAR;IAE1BiS,mBAAmB,EAAE,CAAC;MACtBjP,IAAI,EAAEhD,KADgB;MAEtBiD,IAAI,EAAE,CAAC,kBAAD;IAFgB,CAAD,CAFK;IAK1BiP,iBAAiB,EAAE,CAAC;MACpBlP,IAAI,EAAEhD;IADc,CAAD,CALO;IAO1B4K,QAAQ,EAAE,CAAC;MACX5H,IAAI,EAAEhD;IADK,CAAD,CAPgB;IAS1BiL,WAAW,EAAE,CAAC;MACdjI,IAAI,EAAEhD;IADQ,CAAD,CATa;IAW1B4F,KAAK,EAAE,CAAC;MACR5C,IAAI,EAAEhD;IADE,CAAD,CAXmB;IAa1BuL,QAAQ,EAAE,CAAC;MACXvI,IAAI,EAAEhD;IADK,CAAD,CAbgB;IAe1B2L,WAAW,EAAE,CAAC;MACd3I,IAAI,EAAEhD;IADQ,CAAD,CAfa;IAiB1B0F,QAAQ,EAAE,CAAC;MACX1C,IAAI,EAAEhD;IADK,CAAD,CAjBgB;IAmB1BwK,eAAe,EAAE,CAAC;MAClBxH,IAAI,EAAEhD,KADY;MAElBiD,IAAI,EAAE,CAAC,kBAAD;IAFY,CAAD,CAnBS;IAsB1BiD,UAAU,EAAE,CAAC;MACblD,IAAI,EAAEhD;IADO,CAAD,CAtBc;IAwB1B8D,QAAQ,EAAE,CAAC;MACXd,IAAI,EAAEhD;IADK,CAAD,CAxBgB;IA0B1ByK,MAAM,EAAE,CAAC;MACTzH,IAAI,EAAE/C;IADG,CAAD,CA1BkB;IA4B1ByK,WAAW,EAAE,CAAC;MACd1H,IAAI,EAAE/C;IADQ,CAAD,CA5Ba;IA8B1B8L,KAAK,EAAE,CAAC;MACR/I,IAAI,EAAE1C,eADE;MAER2C,IAAI,EAAE,CAACK,OAAD,EAAU;QACR;QACA;QACA6O,WAAW,EAAE;MAHL,CAAV;IAFE,CAAD;EA9BmB,CA7B1C;AAAA;AAoEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAIC,YAAY,GAAG,CAAnB;AACA;AACA;AACA;AACA;;AACA,MAAMC,YAAN,CAAmB;EACfpQ,WAAW,CAACQ,WAAD,EAAc6P,eAAd,EAA+B;IACtC,KAAK7P,WAAL,GAAmBA,WAAnB;IACA,KAAK6P,eAAL,GAAuBA,eAAvB;IACA;;IACA,KAAKzG,OAAL,GAAe,KAAf;IACA,KAAK0G,UAAL,GAAkB,KAAlB;IACA;AACR;AACA;AACA;AACA;;IACQ,KAAKC,iBAAL,GAAyB,KAAKF,eAAL,CAAqBE,iBAA9C;IACA;;IACA,KAAKC,OAAL,GAAe,IAAI9S,YAAJ,EAAf;IACA;;IACA,KAAKgM,WAAL,GAAmB,EAAnB;IACA;;IACA,KAAKN,EAAL,GAAW,uBAAsB+G,YAAY,EAAG,EAAhD;IACA,KAAK9N,SAAL,GAAiB,KAAjB;IACA,KAAKuJ,YAAL,GAAoB,KAAKpL,WAAL,CAAiByC,aAArC;EACH;EACD;;;EACY,IAARwN,QAAQ,CAAC9M,KAAD,EAAQ;IAChB,IAAIA,KAAJ,EAAW;MACP,KAAK+M,SAAL,GAAiB/M,KAAjB;;MACA,KAAK+M,SAAL,CAAe/E,aAAf,CAA6B,IAA7B;IACH;EACJ;EACD;AACJ;AACA;;;EACiB,IAATgF,SAAS,GAAG;IACZ,OAAO,KAAKL,UAAZ;EACH;;EACY,IAATK,SAAS,CAAChN,KAAD,EAAQ;IACjB,KAAK2M,UAAL,GAAkBtR,qBAAqB,CAAC2E,KAAD,CAAvC;EACH;EACD;;;EACY,IAARF,QAAQ,GAAG;IACX,OAAO,KAAKpB,SAAL,IAAmB,KAAKqO,SAAL,IAAkB,KAAKA,SAAL,CAAejN,QAA3D;EACH;;EACW,IAARA,QAAQ,CAACE,KAAD,EAAQ;IAChB,KAAKtB,SAAL,GAAiBrD,qBAAqB,CAAC2E,KAAD,CAAtC;EACH;EACD;;;EACS,IAALmF,KAAK,GAAG;IACR,OAAO,CAAC,KAAK8C,YAAL,CAAkBjI,KAA1B;EACH;;EACDiN,WAAW,GAAG;IACV,KAAKF,SAAL,CAAepJ,YAAf,CAA4BlC,IAA5B;EACH;;EACDV,WAAW,GAAG;IACV,KAAK8L,OAAL,CAAa/E,QAAb;EACH;;EACDnB,kBAAkB,GAAG;IACjB,KAAKuG,yBAAL,GAAiC,KAAK/H,KAAtC;EACH;EACD;;;EACA6D,QAAQ,CAACpH,KAAD,EAAQ;IACZ,IAAIA,KAAJ,EAAW;MACP;MACA;MACA,IAAIA,KAAK,CAACG,OAAN,KAAkBtI,GAAlB,IAAyB,CAACC,cAAc,CAACkI,KAAD,EAAQ,UAAR,CAA5C,EAAiE;QAC7D,KAAKmL,SAAL,CAAe5F,iBAAf;MACH,CALM,CAMP;MACA;MACA;;;MACA,IAAIvF,KAAK,CAACG,OAAN,KAAkBxI,SAAlB,IAA+B,KAAK2T,yBAAxC,EAAmE;QAC/D,KAAKH,SAAL,CAAenG,WAAf,CAA2BuG,iBAA3B;;QACAvL,KAAK,CAACC,cAAN;QACA;MACH,CAJD,MAKK;QACD,KAAKqL,yBAAL,GAAiC,KAAjC;MACH;IACJ;;IACD,KAAKE,YAAL,CAAkBxL,KAAlB;EACH;EACD;AACJ;AACA;;;EACIyL,MAAM,CAACzL,KAAD,EAAQ;IACV;IACA,IAAI,CAAC,KAAKsL,yBAAN,IAAmCtL,KAAK,CAACG,OAAN,KAAkBxI,SAArD,IAAkE,KAAK4L,KAA3E,EAAkF;MAC9E,KAAK+H,yBAAL,GAAiC,IAAjC;MACAtL,KAAK,CAACC,cAAN;IACH;EACJ;EACD;;;EACAG,KAAK,GAAG;IACJ,IAAI,KAAKgL,SAAT,EAAoB;MAChB,KAAKI,YAAL;IACH;;IACD,KAAKnH,OAAL,GAAe,KAAf,CAJI,CAKJ;;IACA,IAAI,CAAC,KAAK8G,SAAL,CAAe9G,OAApB,EAA6B;MACzB,KAAK8G,SAAL,CAAe/K,KAAf;IACH;;IACD,KAAK+K,SAAL,CAAepJ,YAAf,CAA4BlC,IAA5B;EACH;;EACD6L,MAAM,GAAG;IACL,KAAKrH,OAAL,GAAe,IAAf;IACA,KAAKiH,yBAAL,GAAiC,KAAK/H,KAAtC;;IACA,KAAK4H,SAAL,CAAepJ,YAAf,CAA4BlC,IAA5B;EACH;EACD;;;EACA2L,YAAY,CAACxL,KAAD,EAAQ;IAChB,IAAI,CAAC,KAAKqG,YAAL,CAAkBjI,KAAnB,IAA4B,CAAC,CAAC4B,KAAlC,EAAyC;MACrC,KAAKmL,SAAL,CAAe/D,QAAf,CAAwBpH,KAAxB;IACH;;IACD,IAAI,CAACA,KAAD,IAAU,KAAK2L,eAAL,CAAqB3L,KAArB,CAAd,EAA2C;MACvC,KAAKiL,OAAL,CAAa7L,IAAb,CAAkB;QACdwM,KAAK,EAAE,KAAKvF,YADE;QAEdjI,KAAK,EAAE,KAAKiI,YAAL,CAAkBjI,KAFX;QAGdyN,SAAS,EAAE;MAHG,CAAlB;MAKA7L,KAAK,EAAEC,cAAP;IACH;EACJ;;EACD6L,QAAQ,GAAG;IACP;IACA,KAAKX,SAAL,CAAepJ,YAAf,CAA4BlC,IAA5B;EACH;EACD;;;EACAD,KAAK,CAACqH,OAAD,EAAU;IACX,KAAKZ,YAAL,CAAkBzG,KAAlB,CAAwBqH,OAAxB;EACH;EACD;;;EACAuB,KAAK,GAAG;IACJ,KAAKnC,YAAL,CAAkBjI,KAAlB,GAA0B,EAA1B;IACA,KAAKkN,yBAAL,GAAiC,IAAjC;EACH;EACD;;;EACAK,eAAe,CAAC3L,KAAD,EAAQ;IACnB,OAAO,CAAClI,cAAc,CAACkI,KAAD,CAAf,IAA0B,IAAI+L,GAAJ,CAAQ,KAAKf,iBAAb,EAAgCgB,GAAhC,CAAoChM,KAAK,CAACG,OAA1C,CAAjC;EACH;;AAzIc;;AA2InB0K,YAAY,CAACzP,IAAb;EAAA,iBAAyGyP,YAAzG,EA1sCgG7S,EA0sChG,mBAAuIA,EAAE,CAACyI,UAA1I,GA1sCgGzI,EA0sChG,mBAAiKyJ,yBAAjK;AAAA;;AACAoJ,YAAY,CAACxP,IAAb,kBA3sCgGrD,EA2sChG;EAAA,MAA6F6S,YAA7F;EAAA;EAAA;EAAA;EAAA;IAAA;MA3sCgG7S,EA2sChG;QAAA,OAA6F,oBAA7F;MAAA;QAAA,OAA6F,kBAA7F;MAAA;QAAA,OAA6F,WAA7F;MAAA;QAAA,OAA6F,YAA7F;MAAA;QAAA,OAA6F,cAA7F;MAAA;IAAA;;IAAA;MA3sCgGA,EA2sChG;MA3sCgGA,EA2sChG;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA;EAAA,WA3sCgGA,EA2sChG;AAAA;;AACA;EAAA,mDA5sCgGA,EA4sChG,mBAA2F6S,YAA3F,EAAqH,CAAC;IAC1GrP,IAAI,EAAEtD,SADoG;IAE1GuD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,wBADX;MAECoF,QAAQ,EAAE,+BAFX;MAGCnF,IAAI,EAAE;QACF,SAAS,kCADP;QAEF,aAAa,kBAFX;QAGF,WAAW,gBAHT;QAIF,UAAU,SAJR;QAKF,WAAW,UALT;QAMF,WAAW,YANT;QAOF,QAAQ,IAPN;QAQF,mBAAmB,kBARjB;QASF,sBAAsB,qBATpB;QAUF,uBAAuB,uEAVrB;QAWF,wBAAwB;MAXtB;IAHP,CAAD;EAFoG,CAAD,CAArH,EAmB4B,YAAY;IAAE,OAAO,CAAC;MAAEH,IAAI,EAAExD,EAAE,CAACyI;IAAX,CAAD,EAA0B;MAAEjF,IAAI,EAAEgD,SAAR;MAAmBuC,UAAU,EAAE,CAAC;QACvFvF,IAAI,EAAEnD,MADiF;QAEvFoD,IAAI,EAAE,CAACgG,yBAAD;MAFiF,CAAD;IAA/B,CAA1B,CAAP;EAGlB,CAtBxB,EAsB0C;IAAEyJ,QAAQ,EAAE,CAAC;MACvC1P,IAAI,EAAEhD,KADiC;MAEvCiD,IAAI,EAAE,CAAC,iBAAD;IAFiC,CAAD,CAAZ;IAG1B2P,SAAS,EAAE,CAAC;MACZ5P,IAAI,EAAEhD,KADM;MAEZiD,IAAI,EAAE,CAAC,uBAAD;IAFM,CAAD,CAHe;IAM1BuP,iBAAiB,EAAE,CAAC;MACpBxP,IAAI,EAAEhD,KADc;MAEpBiD,IAAI,EAAE,CAAC,+BAAD;IAFc,CAAD,CANO;IAS1BwP,OAAO,EAAE,CAAC;MACVzP,IAAI,EAAE/C,MADI;MAEVgD,IAAI,EAAE,CAAC,sBAAD;IAFI,CAAD,CATiB;IAY1B0I,WAAW,EAAE,CAAC;MACd3I,IAAI,EAAEhD;IADQ,CAAD,CAZa;IAc1BqL,EAAE,EAAE,CAAC;MACLrI,IAAI,EAAEhD;IADD,CAAD,CAdsB;IAgB1B0F,QAAQ,EAAE,CAAC;MACX1C,IAAI,EAAEhD;IADK,CAAD;EAhBgB,CAtB1C;AAAA;AA0CA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMyT,iBAAiB,GAAG,CACtB/J,WADsB,EAEtBpG,OAFsB,EAGtB+O,YAHsB,EAItB1J,aAJsB,EAKtBhG,aALsB,EAMtBU,mBANsB,CAA1B;;AAQA,MAAMqQ,cAAN,CAAqB;;AAErBA,cAAc,CAAC9Q,IAAf;EAAA,iBAA2G8Q,cAA3G;AAAA;;AACAA,cAAc,CAACC,IAAf,kBAxwCgGnU,EAwwChG;EAAA,MAA4GkU;AAA5G;AAWAA,cAAc,CAACE,IAAf,kBAnxCgGpU,EAmxChG;EAAA,WAAuI,CAC/HwB,iBAD+H,EAE/H;IACI8B,OAAO,EAAEmG,yBADb;IAEI4K,QAAQ,EAAE;MACNrB,iBAAiB,EAAE,CAACjT,KAAD;IADb;EAFd,CAF+H,CAAvI;EAAA,UAQiBwB,eARjB;AAAA;;AASA;EAAA,mDA5xCgGvB,EA4xChG,mBAA2FkU,cAA3F,EAAuH,CAAC;IAC5G1Q,IAAI,EAAEzC,QADsG;IAE5G0C,IAAI,EAAE,CAAC;MACC6Q,OAAO,EAAE,CAAC/S,eAAD,CADV;MAECgT,OAAO,EAAEN,iBAFV;MAGCO,YAAY,EAAEP,iBAHf;MAICrQ,SAAS,EAAE,CACPpC,iBADO,EAEP;QACI8B,OAAO,EAAEmG,yBADb;QAEI4K,QAAQ,EAAE;UACNrB,iBAAiB,EAAE,CAACjT,KAAD;QADb;MAFd,CAFO;IAJZ,CAAD;EAFsG,CAAD,CAAvH;AAAA;AAkBA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAAS0J,yBAAT,EAAoC3G,eAApC,EAAqDD,eAArD,EAAsEE,sBAAtE,EAA8Fe,OAA9F,EAAuGX,aAAvG,EAAsH0P,YAAtH,EAAoI3I,WAApI,EAAiJD,iBAAjJ,EAAoKd,aAApK,EAAmL3G,sBAAnL,EAA2MqB,mBAA3M,EAAgOqQ,cAAhO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}