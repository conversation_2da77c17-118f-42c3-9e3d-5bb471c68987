{"ast": null, "code": "// imported from https://github.com/socketio/base64-arraybuffer\nconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'; // Use a lookup table to find the index.\n\nconst lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\n\nfor (let i = 0; i < chars.length; i++) {\n  lookup[chars.charCodeAt(i)] = i;\n}\n\nexport const encode = arraybuffer => {\n  let bytes = new Uint8Array(arraybuffer),\n      i,\n      len = bytes.length,\n      base64 = '';\n\n  for (i = 0; i < len; i += 3) {\n    base64 += chars[bytes[i] >> 2];\n    base64 += chars[(bytes[i] & 3) << 4 | bytes[i + 1] >> 4];\n    base64 += chars[(bytes[i + 1] & 15) << 2 | bytes[i + 2] >> 6];\n    base64 += chars[bytes[i + 2] & 63];\n  }\n\n  if (len % 3 === 2) {\n    base64 = base64.substring(0, base64.length - 1) + '=';\n  } else if (len % 3 === 1) {\n    base64 = base64.substring(0, base64.length - 2) + '==';\n  }\n\n  return base64;\n};\nexport const decode = base64 => {\n  let bufferLength = base64.length * 0.75,\n      len = base64.length,\n      i,\n      p = 0,\n      encoded1,\n      encoded2,\n      encoded3,\n      encoded4;\n\n  if (base64[base64.length - 1] === '=') {\n    bufferLength--;\n\n    if (base64[base64.length - 2] === '=') {\n      bufferLength--;\n    }\n  }\n\n  const arraybuffer = new ArrayBuffer(bufferLength),\n        bytes = new Uint8Array(arraybuffer);\n\n  for (i = 0; i < len; i += 4) {\n    encoded1 = lookup[base64.charCodeAt(i)];\n    encoded2 = lookup[base64.charCodeAt(i + 1)];\n    encoded3 = lookup[base64.charCodeAt(i + 2)];\n    encoded4 = lookup[base64.charCodeAt(i + 3)];\n    bytes[p++] = encoded1 << 2 | encoded2 >> 4;\n    bytes[p++] = (encoded2 & 15) << 4 | encoded3 >> 2;\n    bytes[p++] = (encoded3 & 3) << 6 | encoded4 & 63;\n  }\n\n  return arraybuffer;\n};", "map": {"version": 3, "names": ["chars", "lookup", "Uint8Array", "i", "length", "charCodeAt", "encode", "arraybuffer", "bytes", "len", "base64", "substring", "decode", "bufferLength", "p", "encoded1", "encoded2", "encoded3", "encoded4", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["R:/chateye/FrontendAngular/node_modules/engine.io-parser/build/esm/contrib/base64-arraybuffer.js"], "sourcesContent": ["// imported from https://github.com/socketio/base64-arraybuffer\nconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nconst lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (let i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nexport const encode = (arraybuffer) => {\n    let bytes = new Uint8Array(arraybuffer), i, len = bytes.length, base64 = '';\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    }\n    else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n    return base64;\n};\nexport const decode = (base64) => {\n    let bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    const arraybuffer = new ArrayBuffer(bufferLength), bytes = new Uint8Array(arraybuffer);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return arraybuffer;\n};\n"], "mappings": "AAAA;AACA,MAAMA,KAAK,GAAG,kEAAd,C,CACA;;AACA,MAAMC,MAAM,GAAG,OAAOC,UAAP,KAAsB,WAAtB,GAAoC,EAApC,GAAyC,IAAIA,UAAJ,CAAe,GAAf,CAAxD;;AACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,KAAK,CAACI,MAA1B,EAAkCD,CAAC,EAAnC,EAAuC;EACnCF,MAAM,CAACD,KAAK,CAACK,UAAN,CAAiBF,CAAjB,CAAD,CAAN,GAA8BA,CAA9B;AACH;;AACD,OAAO,MAAMG,MAAM,GAAIC,WAAD,IAAiB;EACnC,IAAIC,KAAK,GAAG,IAAIN,UAAJ,CAAeK,WAAf,CAAZ;EAAA,IAAyCJ,CAAzC;EAAA,IAA4CM,GAAG,GAAGD,KAAK,CAACJ,MAAxD;EAAA,IAAgEM,MAAM,GAAG,EAAzE;;EACA,KAAKP,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGM,GAAhB,EAAqBN,CAAC,IAAI,CAA1B,EAA6B;IACzBO,MAAM,IAAIV,KAAK,CAACQ,KAAK,CAACL,CAAD,CAAL,IAAY,CAAb,CAAf;IACAO,MAAM,IAAIV,KAAK,CAAE,CAACQ,KAAK,CAACL,CAAD,CAAL,GAAW,CAAZ,KAAkB,CAAnB,GAAyBK,KAAK,CAACL,CAAC,GAAG,CAAL,CAAL,IAAgB,CAA1C,CAAf;IACAO,MAAM,IAAIV,KAAK,CAAE,CAACQ,KAAK,CAACL,CAAC,GAAG,CAAL,CAAL,GAAe,EAAhB,KAAuB,CAAxB,GAA8BK,KAAK,CAACL,CAAC,GAAG,CAAL,CAAL,IAAgB,CAA/C,CAAf;IACAO,MAAM,IAAIV,KAAK,CAACQ,KAAK,CAACL,CAAC,GAAG,CAAL,CAAL,GAAe,EAAhB,CAAf;EACH;;EACD,IAAIM,GAAG,GAAG,CAAN,KAAY,CAAhB,EAAmB;IACfC,MAAM,GAAGA,MAAM,CAACC,SAAP,CAAiB,CAAjB,EAAoBD,MAAM,CAACN,MAAP,GAAgB,CAApC,IAAyC,GAAlD;EACH,CAFD,MAGK,IAAIK,GAAG,GAAG,CAAN,KAAY,CAAhB,EAAmB;IACpBC,MAAM,GAAGA,MAAM,CAACC,SAAP,CAAiB,CAAjB,EAAoBD,MAAM,CAACN,MAAP,GAAgB,CAApC,IAAyC,IAAlD;EACH;;EACD,OAAOM,MAAP;AACH,CAfM;AAgBP,OAAO,MAAME,MAAM,GAAIF,MAAD,IAAY;EAC9B,IAAIG,YAAY,GAAGH,MAAM,CAACN,MAAP,GAAgB,IAAnC;EAAA,IAAyCK,GAAG,GAAGC,MAAM,CAACN,MAAtD;EAAA,IAA8DD,CAA9D;EAAA,IAAiEW,CAAC,GAAG,CAArE;EAAA,IAAwEC,QAAxE;EAAA,IAAkFC,QAAlF;EAAA,IAA4FC,QAA5F;EAAA,IAAsGC,QAAtG;;EACA,IAAIR,MAAM,CAACA,MAAM,CAACN,MAAP,GAAgB,CAAjB,CAAN,KAA8B,GAAlC,EAAuC;IACnCS,YAAY;;IACZ,IAAIH,MAAM,CAACA,MAAM,CAACN,MAAP,GAAgB,CAAjB,CAAN,KAA8B,GAAlC,EAAuC;MACnCS,YAAY;IACf;EACJ;;EACD,MAAMN,WAAW,GAAG,IAAIY,WAAJ,CAAgBN,YAAhB,CAApB;EAAA,MAAmDL,KAAK,GAAG,IAAIN,UAAJ,CAAeK,WAAf,CAA3D;;EACA,KAAKJ,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGM,GAAhB,EAAqBN,CAAC,IAAI,CAA1B,EAA6B;IACzBY,QAAQ,GAAGd,MAAM,CAACS,MAAM,CAACL,UAAP,CAAkBF,CAAlB,CAAD,CAAjB;IACAa,QAAQ,GAAGf,MAAM,CAACS,MAAM,CAACL,UAAP,CAAkBF,CAAC,GAAG,CAAtB,CAAD,CAAjB;IACAc,QAAQ,GAAGhB,MAAM,CAACS,MAAM,CAACL,UAAP,CAAkBF,CAAC,GAAG,CAAtB,CAAD,CAAjB;IACAe,QAAQ,GAAGjB,MAAM,CAACS,MAAM,CAACL,UAAP,CAAkBF,CAAC,GAAG,CAAtB,CAAD,CAAjB;IACAK,KAAK,CAACM,CAAC,EAAF,CAAL,GAAcC,QAAQ,IAAI,CAAb,GAAmBC,QAAQ,IAAI,CAA5C;IACAR,KAAK,CAACM,CAAC,EAAF,CAAL,GAAc,CAACE,QAAQ,GAAG,EAAZ,KAAmB,CAApB,GAA0BC,QAAQ,IAAI,CAAnD;IACAT,KAAK,CAACM,CAAC,EAAF,CAAL,GAAc,CAACG,QAAQ,GAAG,CAAZ,KAAkB,CAAnB,GAAyBC,QAAQ,GAAG,EAAjD;EACH;;EACD,OAAOX,WAAP;AACH,CAnBM", "ignoreList": []}, "metadata": {}, "sourceType": "module"}