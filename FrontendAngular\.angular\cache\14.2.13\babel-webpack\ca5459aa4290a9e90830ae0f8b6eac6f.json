{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function filter(predicate, thisArg) {\n  return operate((source, subscriber) => {\n    let index = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, value => predicate.call(thisArg, value, index++) && subscriber.next(value)));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "filter", "predicate", "thisArg", "source", "subscriber", "index", "subscribe", "value", "call", "next"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/rxjs/dist/esm/internal/operators/filter.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function filter(predicate, thisArg) {\n    return operate((source, subscriber) => {\n        let index = 0;\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => predicate.call(thisArg, value, index++) && subscriber.next(value)));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,OAAO,SAASC,MAAT,CAAgBC,SAAhB,EAA2BC,OAA3B,EAAoC;EACvC,OAAOJ,OAAO,CAAC,CAACK,MAAD,EAASC,UAAT,KAAwB;IACnC,IAAIC,KAAK,GAAG,CAAZ;IACAF,MAAM,CAACG,SAAP,CAAiBP,wBAAwB,CAACK,UAAD,EAAcG,KAAD,IAAWN,SAAS,CAACO,IAAV,CAAeN,OAAf,EAAwBK,KAAxB,EAA+BF,KAAK,EAApC,KAA2CD,UAAU,CAACK,IAAX,CAAgBF,KAAhB,CAAnE,CAAzC;EACH,CAHa,CAAd;AAIH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}