{"ast": null, "code": "import { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { EMPTY } from './empty';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { popResultSelector } from '../util/args';\nexport function zip(...args) {\n  const resultSelector = popResultSelector(args);\n  const sources = argsOrArgArray(args);\n  return sources.length ? new Observable(subscriber => {\n    let buffers = sources.map(() => []);\n    let completed = sources.map(() => false);\n    subscriber.add(() => {\n      buffers = completed = null;\n    });\n\n    for (let sourceIndex = 0; !subscriber.closed && sourceIndex < sources.length; sourceIndex++) {\n      innerFrom(sources[sourceIndex]).subscribe(createOperatorSubscriber(subscriber, value => {\n        buffers[sourceIndex].push(value);\n\n        if (buffers.every(buffer => buffer.length)) {\n          const result = buffers.map(buffer => buffer.shift());\n          subscriber.next(resultSelector ? resultSelector(...result) : result);\n\n          if (buffers.some((buffer, i) => !buffer.length && completed[i])) {\n            subscriber.complete();\n          }\n        }\n      }, () => {\n        completed[sourceIndex] = true;\n        !buffers[sourceIndex].length && subscriber.complete();\n      }));\n    }\n\n    return () => {\n      buffers = completed = null;\n    };\n  }) : EMPTY;\n}", "map": {"version": 3, "names": ["Observable", "innerFrom", "argsOrArgArray", "EMPTY", "createOperatorSubscriber", "popResultSelector", "zip", "args", "resultSelector", "sources", "length", "subscriber", "buffers", "map", "completed", "add", "sourceIndex", "closed", "subscribe", "value", "push", "every", "buffer", "result", "shift", "next", "some", "i", "complete"], "sources": ["R:/chateye/FrontendAngular/node_modules/rxjs/dist/esm/internal/observable/zip.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { EMPTY } from './empty';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { popResultSelector } from '../util/args';\nexport function zip(...args) {\n    const resultSelector = popResultSelector(args);\n    const sources = argsOrArgArray(args);\n    return sources.length\n        ? new Observable((subscriber) => {\n            let buffers = sources.map(() => []);\n            let completed = sources.map(() => false);\n            subscriber.add(() => {\n                buffers = completed = null;\n            });\n            for (let sourceIndex = 0; !subscriber.closed && sourceIndex < sources.length; sourceIndex++) {\n                innerFrom(sources[sourceIndex]).subscribe(createOperatorSubscriber(subscriber, (value) => {\n                    buffers[sourceIndex].push(value);\n                    if (buffers.every((buffer) => buffer.length)) {\n                        const result = buffers.map((buffer) => buffer.shift());\n                        subscriber.next(resultSelector ? resultSelector(...result) : result);\n                        if (buffers.some((buffer, i) => !buffer.length && completed[i])) {\n                            subscriber.complete();\n                        }\n                    }\n                }, () => {\n                    completed[sourceIndex] = true;\n                    !buffers[sourceIndex].length && subscriber.complete();\n                }));\n            }\n            return () => {\n                buffers = completed = null;\n            };\n        })\n        : EMPTY;\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,SAAT,QAA0B,aAA1B;AACA,SAASC,cAAT,QAA+B,wBAA/B;AACA,SAASC,KAAT,QAAsB,SAAtB;AACA,SAASC,wBAAT,QAAyC,iCAAzC;AACA,SAASC,iBAAT,QAAkC,cAAlC;AACA,OAAO,SAASC,GAAT,CAAa,GAAGC,IAAhB,EAAsB;EACzB,MAAMC,cAAc,GAAGH,iBAAiB,CAACE,IAAD,CAAxC;EACA,MAAME,OAAO,GAAGP,cAAc,CAACK,IAAD,CAA9B;EACA,OAAOE,OAAO,CAACC,MAAR,GACD,IAAIV,UAAJ,CAAgBW,UAAD,IAAgB;IAC7B,IAAIC,OAAO,GAAGH,OAAO,CAACI,GAAR,CAAY,MAAM,EAAlB,CAAd;IACA,IAAIC,SAAS,GAAGL,OAAO,CAACI,GAAR,CAAY,MAAM,KAAlB,CAAhB;IACAF,UAAU,CAACI,GAAX,CAAe,MAAM;MACjBH,OAAO,GAAGE,SAAS,GAAG,IAAtB;IACH,CAFD;;IAGA,KAAK,IAAIE,WAAW,GAAG,CAAvB,EAA0B,CAACL,UAAU,CAACM,MAAZ,IAAsBD,WAAW,GAAGP,OAAO,CAACC,MAAtE,EAA8EM,WAAW,EAAzF,EAA6F;MACzFf,SAAS,CAACQ,OAAO,CAACO,WAAD,CAAR,CAAT,CAAgCE,SAAhC,CAA0Cd,wBAAwB,CAACO,UAAD,EAAcQ,KAAD,IAAW;QACtFP,OAAO,CAACI,WAAD,CAAP,CAAqBI,IAArB,CAA0BD,KAA1B;;QACA,IAAIP,OAAO,CAACS,KAAR,CAAeC,MAAD,IAAYA,MAAM,CAACZ,MAAjC,CAAJ,EAA8C;UAC1C,MAAMa,MAAM,GAAGX,OAAO,CAACC,GAAR,CAAaS,MAAD,IAAYA,MAAM,CAACE,KAAP,EAAxB,CAAf;UACAb,UAAU,CAACc,IAAX,CAAgBjB,cAAc,GAAGA,cAAc,CAAC,GAAGe,MAAJ,CAAjB,GAA+BA,MAA7D;;UACA,IAAIX,OAAO,CAACc,IAAR,CAAa,CAACJ,MAAD,EAASK,CAAT,KAAe,CAACL,MAAM,CAACZ,MAAR,IAAkBI,SAAS,CAACa,CAAD,CAAvD,CAAJ,EAAiE;YAC7DhB,UAAU,CAACiB,QAAX;UACH;QACJ;MACJ,CATiE,EAS/D,MAAM;QACLd,SAAS,CAACE,WAAD,CAAT,GAAyB,IAAzB;QACA,CAACJ,OAAO,CAACI,WAAD,CAAP,CAAqBN,MAAtB,IAAgCC,UAAU,CAACiB,QAAX,EAAhC;MACH,CAZiE,CAAlE;IAaH;;IACD,OAAO,MAAM;MACThB,OAAO,GAAGE,SAAS,GAAG,IAAtB;IACH,CAFD;EAGH,CAxBC,CADC,GA0BDX,KA1BN;AA2BH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}