{"ast": null, "code": "import _asyncToGenerator from \"R:/chateye/FrontendAngular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Observable } from 'rxjs';\nimport { take } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./services/chat.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/toolbar\";\nimport * as i4 from \"@angular/material/sidenav\";\nimport * as i5 from \"@angular/material/card\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/chips\";\nimport * as i9 from \"@angular/material/tooltip\";\nimport * as i10 from \"./components/login-form/login-form.component\";\nimport * as i11 from \"./components/sidebar/sidebar.component\";\nimport * as i12 from \"./components/message-list/message-list.component\";\nimport * as i13 from \"./components/message-input/message-input.component\";\nimport * as i14 from \"./components/admin-panel/admin-panel.component\";\nimport * as i15 from \"./components/password-change/password-change.component\";\nimport * as i16 from \"./components/message-edit/message-edit.component\";\n\nfunction AppComponent_app_login_form_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-login-form\");\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_2_p_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 28);\n    i0.ɵɵtext(1, \" Please select a group to start chatting \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_2_p_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (tmp_0_0 = i0.ɵɵpipeBind1(2, 1, ctx_r7.currentGroup$)) == null ? null : tmp_0_0.description, \" \");\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_2_button_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function AppComponent_mat_sidenav_container_2_button_36_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.onShowAdminPanel());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"admin_panel_settings\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_2_app_message_input_44_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"app-message-input\", 31);\n    i0.ɵɵlistener(\"onSendMessage\", function AppComponent_mat_sidenav_container_2_app_message_input_44_Template_app_message_input_onSendMessage_0_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r13.onSendMessage($event));\n    })(\"onCancelReply\", function AppComponent_mat_sidenav_container_2_app_message_input_44_Template_app_message_input_onCancelReply_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.onCancelReply());\n    });\n    i0.ɵɵpipe(1, \"async\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"replyTo\", i0.ɵɵpipeBind1(1, 1, ctx_r9.replyTo$));\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_2_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"mat-card\")(2, \"mat-card-content\")(3, \"p\");\n    i0.ɵɵtext(4, \"Select a group to start chatting\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\n\nconst _c0 = function () {\n  return [];\n};\n\nfunction AppComponent_mat_sidenav_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"mat-sidenav-container\", 5)(1, \"mat-sidenav\", 6, 7)(3, \"app-sidebar\", 8);\n    i0.ɵɵlistener(\"onPasswordChange\", function AppComponent_mat_sidenav_container_2_Template_app_sidebar_onPasswordChange_3_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.onPasswordChange());\n    })(\"onLogout\", function AppComponent_mat_sidenav_container_2_Template_app_sidebar_onLogout_3_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.onLogout());\n    });\n    i0.ɵɵpipe(4, \"async\");\n    i0.ɵɵpipe(5, \"async\");\n    i0.ɵɵpipe(6, \"async\");\n    i0.ɵɵpipe(7, \"async\");\n    i0.ɵɵpipe(8, \"async\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"mat-sidenav-content\", 9)(10, \"mat-toolbar\", 10)(11, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function AppComponent_mat_sidenav_container_2_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r17);\n\n      const _r5 = i0.ɵɵreference(2);\n\n      return i0.ɵɵresetView(_r5.toggle());\n    });\n    i0.ɵɵelementStart(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"menu\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 12)(15, \"div\", 13)(16, \"h1\", 14);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"async\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, AppComponent_mat_sidenav_container_2_p_19_Template, 2, 0, \"p\", 15);\n    i0.ɵɵpipe(20, \"async\");\n    i0.ɵɵtemplate(21, AppComponent_mat_sidenav_container_2_p_21_Template, 3, 3, \"p\", 16);\n    i0.ɵɵpipe(22, \"async\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 17)(24, \"div\", 18)(25, \"mat-icon\", 19);\n    i0.ɵɵpipe(26, \"async\");\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"async\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"span\", 20);\n    i0.ɵɵtext(30);\n    i0.ɵɵpipe(31, \"async\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"mat-chip-list\")(33, \"mat-chip\", 21);\n    i0.ɵɵtext(34);\n    i0.ɵɵpipe(35, \"async\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(36, AppComponent_mat_sidenav_container_2_button_36_Template, 3, 0, \"button\", 22);\n    i0.ɵɵpipe(37, \"async\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(38, \"div\", 23)(39, \"app-message-list\", 24);\n    i0.ɵɵlistener(\"onReply\", function AppComponent_mat_sidenav_container_2_Template_app_message_list_onReply_39_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.onReply($event));\n    })(\"onAddReaction\", function AppComponent_mat_sidenav_container_2_Template_app_message_list_onAddReaction_39_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.onAddReaction($event));\n    })(\"onRemoveReaction\", function AppComponent_mat_sidenav_container_2_Template_app_message_list_onRemoveReaction_39_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.onRemoveReaction($event));\n    })(\"onEdit\", function AppComponent_mat_sidenav_container_2_Template_app_message_list_onEdit_39_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.onEdit($event));\n    })(\"onDelete\", function AppComponent_mat_sidenav_container_2_Template_app_message_list_onDelete_39_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.onDelete($event));\n    })(\"onMessageClick\", function AppComponent_mat_sidenav_container_2_Template_app_message_list_onMessageClick_39_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.onMessageClick($event));\n    });\n    i0.ɵɵpipe(40, \"async\");\n    i0.ɵɵpipe(41, \"async\");\n    i0.ɵɵpipe(42, \"async\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"div\", 25);\n    i0.ɵɵtemplate(44, AppComponent_mat_sidenav_container_2_app_message_input_44_Template, 2, 3, \"app-message-input\", 26);\n    i0.ɵɵpipe(45, \"async\");\n    i0.ɵɵtemplate(46, AppComponent_mat_sidenav_container_2_div_46_Template, 5, 0, \"div\", 27);\n    i0.ɵɵpipe(47, \"async\");\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_6_0;\n    let tmp_8_0;\n    let tmp_12_0;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"onlineUsers\", i0.ɵɵpipeBind1(4, 21, ctx_r1.onlineUsers$) || i0.ɵɵpureFunction0(57, _c0))(\"currentUser\", i0.ɵɵpipeBind1(5, 23, ctx_r1.user$))(\"isAdmin\", i0.ɵɵpipeBind1(6, 25, ctx_r1.isAdmin$) || false)(\"groups\", i0.ɵɵpipeBind1(7, 27, ctx_r1.groups$) || i0.ɵɵpureFunction0(58, _c0))(\"currentGroup\", i0.ɵɵpipeBind1(8, 29, ctx_r1.currentGroup$))(\"onJoinGroup\", ctx_r1.onJoinGroup.bind(ctx_r1));\n    i0.ɵɵadvance(14);\n    i0.ɵɵtextInterpolate1(\" \", ((tmp_6_0 = i0.ɵɵpipeBind1(18, 31, ctx_r1.currentGroup$)) == null ? null : tmp_6_0.name) || \"Select a Group\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !i0.ɵɵpipeBind1(20, 33, ctx_r1.currentGroup$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (tmp_8_0 = i0.ɵɵpipeBind1(22, 35, ctx_r1.currentGroup$)) == null ? null : tmp_8_0.description);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"connected\", i0.ɵɵpipeBind1(26, 37, ctx_r1.connected$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(28, 39, ctx_r1.connected$) ? \"wifi\" : \"wifi_off\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(31, 41, ctx_r1.connected$) ? \"Connected\" : \"Disconnected\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ((tmp_12_0 = i0.ɵɵpipeBind1(35, 43, ctx_r1.onlineUsers$)) == null ? null : tmp_12_0.length) || 0, \" online \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(37, 45, ctx_r1.isAdmin$));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"messages\", i0.ɵɵpipeBind1(40, 47, ctx_r1.messages$) || i0.ɵɵpureFunction0(59, _c0))(\"currentUser\", i0.ɵɵpipeBind1(41, 49, ctx_r1.user$))(\"loading\", i0.ɵɵpipeBind1(42, 51, ctx_r1.loading$) || false)(\"highlightedMessageId\", ctx_r1.highlightedMessageId);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(45, 53, ctx_r1.currentGroup$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !i0.ɵɵpipeBind1(47, 55, ctx_r1.currentGroup$));\n  }\n}\n\nfunction AppComponent_app_admin_panel_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"app-admin-panel\", 33);\n    i0.ɵɵlistener(\"onClose\", function AppComponent_app_admin_panel_4_Template_app_admin_panel_onClose_0_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.onHideAdminPanel());\n    });\n    i0.ɵɵpipe(1, \"async\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"currentUser\", i0.ɵɵpipeBind1(1, 1, ctx_r2.user$));\n  }\n}\n\nfunction AppComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"mat-card\", 35)(2, \"mat-card-header\")(3, \"mat-card-title\");\n    i0.ɵɵtext(4, \"Change Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function AppComponent_div_6_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.onClosePasswordChange());\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"close\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"mat-card-content\");\n    i0.ɵɵelement(9, \"app-password-change\");\n    i0.ɵɵelementEnd()()();\n  }\n}\n\nfunction AppComponent_app_message_edit_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"app-message-edit\", 37);\n    i0.ɵɵlistener(\"onSave\", function AppComponent_app_message_edit_7_Template_app_message_edit_onSave_0_listener($event) {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.onSaveEdit($event));\n    })(\"onCancel\", function AppComponent_app_message_edit_7_Template_app_message_edit_onCancel_0_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r33 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r33.onCancelEdit());\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const editingMessage_r30 = ctx.ngIf;\n    i0.ɵɵproperty(\"message\", editingMessage_r30);\n  }\n}\n\nexport let AppComponent = /*#__PURE__*/(() => {\n  class AppComponent {\n    constructor(chatService) {\n      this.chatService = chatService;\n      this.showPasswordChange = false;\n      this.highlightedMessageId = null;\n      this.user$ = this.chatService.user$;\n      this.messages$ = this.chatService.messages$;\n      this.onlineUsers$ = this.chatService.onlineUsers$;\n      this.groups$ = this.chatService.groups$;\n      this.currentGroup$ = this.chatService.currentGroup$;\n      this.replyTo$ = this.chatService.replyTo$;\n      this.loading$ = this.chatService.loading$;\n      this.isAdmin$ = this.chatService.isAdmin$;\n      this.showAdminPanel$ = this.chatService.showAdminPanel$;\n      this.connected$ = this.chatService.connected$;\n      this.isLoggedIn$ = this.chatService.isLoggedIn$;\n      this.editingMessage$ = this.chatService.editingMessage$;\n    }\n\n    ngOnInit() {// Component initialization\n    }\n\n    ngOnDestroy() {\n      this.chatService.logout();\n    }\n\n    onJoinGroup(groupId) {\n      this.chatService.joinGroup(groupId);\n    }\n\n    onSendMessage(event) {\n      this.chatService.sendMessage(event.text, event.replyToId);\n    }\n\n    onReply(message) {\n      this.chatService.replyToMessage(message); // Highlight the replied-to message\n\n      this.highlightedMessageId = message.id; // Clear highlight after 3 seconds\n\n      setTimeout(() => {\n        this.highlightedMessageId = null;\n      }, 3000);\n    }\n\n    onCancelReply() {\n      this.chatService.cancelReply();\n    }\n\n    onAddReaction(event) {\n      this.chatService.addReaction(event.messageId, event.emoji);\n    }\n\n    onRemoveReaction(data) {\n      this.chatService.removeReaction(data);\n    }\n\n    onEdit(message) {\n      this.chatService.startEditingMessage(message);\n    }\n\n    onSaveEdit(newText) {\n      var _this = this;\n\n      return _asyncToGenerator(function* () {\n        try {\n          const editingMessage = yield _this.editingMessage$.pipe(take(1)).toPromise();\n\n          if (editingMessage) {\n            yield _this.chatService.updateMessage(editingMessage.id, newText);\n\n            _this.chatService.cancelEditingMessage();\n          }\n        } catch (error) {\n          console.error('Failed to edit message:', error);\n          alert('Failed to edit message. Please try again.');\n        }\n      })();\n    }\n\n    onCancelEdit() {\n      this.chatService.cancelEditingMessage();\n    }\n\n    onDelete(message) {\n      var _this2 = this;\n\n      return _asyncToGenerator(function* () {\n        try {\n          if (confirm('Are you sure you want to delete this message?')) {\n            yield _this2.chatService.deleteMessage(message.id);\n          }\n        } catch (error) {\n          console.error('Failed to delete message:', error);\n          alert('Failed to delete message. Please try again.');\n        }\n      })();\n    }\n\n    onShowAdminPanel() {\n      this.chatService.showAdminPanel();\n    }\n\n    onHideAdminPanel() {\n      this.chatService.hideAdminPanel();\n    }\n\n    onPasswordChange() {\n      this.showPasswordChange = true;\n    }\n\n    onClosePasswordChange() {\n      this.showPasswordChange = false;\n    }\n\n    onLogout() {\n      this.chatService.logout();\n    }\n\n    onMessageClick(message) {\n      this.highlightedMessageId = message.id; // Clear highlight after 3 seconds\n\n      setTimeout(() => {\n        this.highlightedMessageId = null;\n      }, 3000);\n    }\n\n  }\n\n  AppComponent.ɵfac = function AppComponent_Factory(t) {\n    return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.ChatService));\n  };\n\n  AppComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppComponent,\n    selectors: [[\"app-root\"]],\n    decls: 9,\n    vars: 13,\n    consts: [[4, \"ngIf\"], [\"class\", \"chat-container\", 4, \"ngIf\"], [3, \"currentUser\", \"onClose\", 4, \"ngIf\"], [\"class\", \"modal-overlay\", 4, \"ngIf\"], [3, \"message\", \"onSave\", \"onCancel\", 4, \"ngIf\"], [1, \"chat-container\"], [\"mode\", \"side\", \"opened\", \"true\", 1, \"chat-sidebar\"], [\"sidenav\", \"\"], [3, \"onlineUsers\", \"currentUser\", \"isAdmin\", \"groups\", \"currentGroup\", \"onJoinGroup\", \"onPasswordChange\", \"onLogout\"], [1, \"chat-main-content\"], [1, \"chat-header\"], [\"mat-icon-button\", \"\", 1, \"menu-button\", 3, \"click\"], [1, \"header-content\"], [1, \"group-info\"], [1, \"group-name\"], [\"class\", \"no-group-message\", 4, \"ngIf\"], [\"class\", \"group-description\", 4, \"ngIf\"], [1, \"header-actions\"], [1, \"connection-status\"], [1, \"status-icon\"], [1, \"status-text\"], [1, \"online-count\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Admin Panel\", 3, \"click\", 4, \"ngIf\"], [1, \"messages-container\"], [3, \"messages\", \"currentUser\", \"loading\", \"highlightedMessageId\", \"onReply\", \"onAddReaction\", \"onRemoveReaction\", \"onEdit\", \"onDelete\", \"onMessageClick\"], [1, \"input-container\"], [3, \"replyTo\", \"onSendMessage\", \"onCancelReply\", 4, \"ngIf\"], [\"class\", \"no-group-input\", 4, \"ngIf\"], [1, \"no-group-message\"], [1, \"group-description\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Admin Panel\", 3, \"click\"], [3, \"replyTo\", \"onSendMessage\", \"onCancelReply\"], [1, \"no-group-input\"], [3, \"currentUser\", \"onClose\"], [1, \"modal-overlay\"], [1, \"modal-card\"], [\"mat-icon-button\", \"\", 1, \"close-button\", 3, \"click\"], [3, \"message\", \"onSave\", \"onCancel\"]],\n    template: function AppComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, AppComponent_app_login_form_0_Template, 1, 0, \"app-login-form\", 0);\n        i0.ɵɵpipe(1, \"async\");\n        i0.ɵɵtemplate(2, AppComponent_mat_sidenav_container_2_Template, 48, 60, \"mat-sidenav-container\", 1);\n        i0.ɵɵpipe(3, \"async\");\n        i0.ɵɵtemplate(4, AppComponent_app_admin_panel_4_Template, 2, 3, \"app-admin-panel\", 2);\n        i0.ɵɵpipe(5, \"async\");\n        i0.ɵɵtemplate(6, AppComponent_div_6_Template, 10, 0, \"div\", 3);\n        i0.ɵɵtemplate(7, AppComponent_app_message_edit_7_Template, 1, 1, \"app-message-edit\", 4);\n        i0.ɵɵpipe(8, \"async\");\n      }\n\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", !i0.ɵɵpipeBind1(1, 5, ctx.isLoggedIn$));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(3, 7, ctx.isLoggedIn$));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(5, 9, ctx.showAdminPanel$));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.showPasswordChange);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(8, 11, ctx.editingMessage$));\n      }\n    },\n    dependencies: [i2.NgIf, i3.MatToolbar, i4.MatSidenav, i4.MatSidenavContainer, i4.MatSidenavContent, i5.MatCard, i5.MatCardHeader, i5.MatCardContent, i5.MatCardTitle, i6.MatButton, i7.MatIcon, i8.MatChipList, i8.MatChip, i9.MatTooltip, i10.LoginFormComponent, i11.SidebarComponent, i12.MessageListComponent, i13.MessageInputComponent, i14.AdminPanelComponent, i15.PasswordChangeComponent, i16.MessageEditComponent, i2.AsyncPipe],\n    styles: [\".chat-container[_ngcontent-%COMP%]{height:100vh;width:100vw;background:#f5f5f5}.chat-sidebar[_ngcontent-%COMP%]{width:280px;min-width:280px;background:white;border-right:1px solid #e0e0e0;box-shadow:2px 0 8px #0000001a}.chat-main-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:100vh;background:white}.chat-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#3f51b5 0%,#5c6bc0 100%);color:#fff;padding:0 16px;box-shadow:0 2px 8px #0000001a;z-index:10}.menu-button[_ngcontent-%COMP%]{margin-right:16px}.header-content[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;flex:1;min-width:0}.group-info[_ngcontent-%COMP%]{flex:1;min-width:0;margin-right:16px}.group-name[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:500;margin:0 0 4px;line-height:1.2}.no-group-message[_ngcontent-%COMP%]{color:#fffc;font-size:.875rem;margin:0;font-weight:400}.group-description[_ngcontent-%COMP%]{color:#ffffffe6;font-size:.75rem;margin:0;opacity:.9}.header-actions[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px;flex-shrink:0}.connection-status[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;background:rgba(255,255,255,.1);padding:8px 12px;border-radius:16px;backdrop-filter:blur(10px)}.status-icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px;color:#ef4444;transition:all .3s ease}.status-icon.connected[_ngcontent-%COMP%]{color:#10b981}.status-text[_ngcontent-%COMP%]{font-size:.75rem;font-weight:500}.online-count[_ngcontent-%COMP%]{background:rgba(255,255,255,.1);color:#fff;font-size:.75rem;font-weight:500}.messages-container[_ngcontent-%COMP%]{flex:1;min-height:0;background:#f8f9fa;overflow:hidden;position:relative}.input-container[_ngcontent-%COMP%]{background:white;border-top:1px solid #e0e0e0;padding:16px}.no-group-input[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;height:60px}.modal-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background:rgba(0,0,0,.5);display:flex;align-items:center;justify-content:center;z-index:1000}.modal-card[_ngcontent-%COMP%]{width:90%;max-width:500px;max-height:90vh;overflow-y:auto}.close-button[_ngcontent-%COMP%]{position:absolute;top:8px;right:8px}@media (max-width: 1024px){.chat-sidebar[_ngcontent-%COMP%]{width:260px;min-width:260px}.group-name[_ngcontent-%COMP%]{font-size:1.125rem}.header-actions[_ngcontent-%COMP%]{gap:12px}}@media (max-width: 768px){.chat-sidebar[_ngcontent-%COMP%]{width:100%;min-width:100%}.chat-main-content[_ngcontent-%COMP%]{height:calc(100vh - 60px)}.group-name[_ngcontent-%COMP%]{font-size:1rem}.header-actions[_ngcontent-%COMP%]{gap:8px}.connection-status[_ngcontent-%COMP%]{padding:6px 10px}.status-text[_ngcontent-%COMP%], .online-count[_ngcontent-%COMP%]{font-size:.7rem}}@media (max-width: 480px){.header-content[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:8px}.header-actions[_ngcontent-%COMP%]{width:100%;justify-content:space-between}.group-name[_ngcontent-%COMP%]{font-size:.9rem}.chat-header[_ngcontent-%COMP%]{padding:0 8px}.input-container[_ngcontent-%COMP%]{padding:12px}}*[_ngcontent-%COMP%]{transition:all .3s cubic-bezier(.4,0,.2,1)}[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px}[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:transparent}[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:rgba(0,0,0,.2);border-radius:3px}[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:rgba(0,0,0,.3)}\"]\n  });\n  return AppComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}