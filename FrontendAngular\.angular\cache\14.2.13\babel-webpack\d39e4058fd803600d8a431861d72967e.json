{"ast": null, "code": "import _asyncToGenerator from \"R:/chateye/FrontendAngular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { ApiService, Message, Group, User, SecurityInfo } from './api.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./api.service\";\nimport * as i2 from \"./socket.service\";\nimport * as i3 from \"./notification.service\";\nexport let ChatService = /*#__PURE__*/(() => {\n  class ChatService {\n    constructor(apiService, socketService, notificationService) {\n      this.apiService = apiService;\n      this.socketService = socketService;\n      this.notificationService = notificationService;\n      this.userSubject = new BehaviorSubject(null);\n      this.messagesSubject = new BehaviorSubject([]);\n      this.onlineUsersSubject = new BehaviorSubject([]);\n      this.groupsSubject = new BehaviorSubject([]);\n      this.currentGroupSubject = new BehaviorSubject(null);\n      this.replyToSubject = new BehaviorSubject(null);\n      this.loadingSubject = new BehaviorSubject(false);\n      this.isAdminSubject = new BehaviorSubject(false);\n      this.showAdminPanelSubject = new BehaviorSubject(false);\n      this.securityInfoSubject = new BehaviorSubject(null);\n      this.editingMessageSubject = new BehaviorSubject(null); // Public observables\n\n      this.user$ = this.userSubject.asObservable();\n      this.messages$ = this.messagesSubject.asObservable();\n      this.onlineUsers$ = this.onlineUsersSubject.asObservable();\n      this.groups$ = this.groupsSubject.asObservable();\n      this.currentGroup$ = this.currentGroupSubject.asObservable();\n      this.replyTo$ = this.replyToSubject.asObservable();\n      this.loading$ = this.loadingSubject.asObservable();\n      this.isAdmin$ = this.isAdminSubject.asObservable();\n      this.showAdminPanel$ = this.showAdminPanelSubject.asObservable();\n      this.securityInfo$ = this.securityInfoSubject.asObservable();\n      this.editingMessage$ = this.editingMessageSubject.asObservable();\n      this.connected$ = this.socketService.isConnected$; // Computed observables\n\n      this.isLoggedIn$ = this.user$.pipe(map(user => !!user));\n      this.setupSocketListeners();\n      this.setupMessageRefresh();\n    }\n\n    setupSocketListeners() {\n      this.socketService.on('connect', () => {\n        console.log('Connected to server');\n      });\n      this.socketService.on('disconnect', () => {\n        console.log('Disconnected from server');\n      });\n      this.socketService.on('userGroups', userGroups => {\n        console.log('Received user groups:', userGroups);\n        this.groupsSubject.next(userGroups || []);\n\n        if (userGroups && userGroups.length > 0) {\n          console.log('Auto-selecting first group:', userGroups[0]);\n          this.currentGroupSubject.next(userGroups[0]); // Load messages for the selected group\n\n          this.loadRecentMessages(userGroups[0].id);\n        } else {\n          console.log('No groups available for user');\n        }\n      });\n      this.socketService.on('recentMessages', messages => {\n        console.log('Received recent messages:', messages.length);\n        this.messagesSubject.next(messages || []);\n      });\n      this.socketService.on('groupJoined', ({\n        groupId\n      }) => {\n        console.log('Joined group:', groupId);\n        const groups = this.groupsSubject.value;\n        const group = groups.find(g => g.id === groupId);\n\n        if (group) {\n          this.currentGroupSubject.next(group);\n        }\n      });\n      this.socketService.on('newMessage', message => {\n        console.log('New message received:', message);\n        const currentMessages = this.messagesSubject.value;\n        this.messagesSubject.next([...currentMessages, message]); // Show notification if not current user and window not focused\n\n        const currentUser = this.userSubject.value;\n\n        if (message.username !== currentUser && document.hidden) {\n          this.notificationService.showMessageNotification(message.username, message.text);\n        }\n      });\n      this.socketService.on('reactionUpdate', ({\n        messageId,\n        reactions\n      }) => {\n        console.log('Reaction update:', messageId, reactions);\n        const currentMessages = this.messagesSubject.value;\n        this.messagesSubject.next(currentMessages.map(msg => msg.id === messageId ? { ...msg,\n          reactions\n        } : msg));\n      });\n      this.socketService.on('onlineUsersUpdate', users => {\n        console.log('Online users updated:', users);\n        this.onlineUsersSubject.next(users || []);\n      });\n      this.socketService.on('userJoined', ({\n        username\n      }) => {\n        console.log('User joined:', username); // Online users will be updated via onlineUsersUpdate event\n      });\n      this.socketService.on('userLeft', ({\n        username\n      }) => {\n        console.log('User left:', username); // Online users will be updated via onlineUsersUpdate event\n      });\n      this.socketService.on('error', error => {\n        console.error('Socket error:', error);\n      });\n    }\n\n    setupMessageRefresh() {\n      // Refresh messages every 5 seconds if socket is not connected\n      this.messageRefreshInterval = setInterval(() => {\n        if (!this.socketService.isConnectedSubject.value) {\n          const currentGroup = this.currentGroupSubject.value;\n\n          if (currentGroup) {\n            console.log('Refreshing messages via HTTP API');\n            this.loadRecentMessages(currentGroup.id);\n          }\n        }\n      }, 5000);\n    }\n\n    login(_x, _x2) {\n      var _this = this;\n\n      return _asyncToGenerator(function* (username, password, inviteCode = null) {\n        try {\n          _this.loadingSubject.next(true);\n\n          console.log('Starting login process for:', username);\n          const userData = yield _this.apiService.loginUser(username, password, inviteCode || undefined).toPromise();\n          console.log('Login API response:', userData); // Check if password is required\n\n          if (userData?.requiresPassword) {\n            throw new Error('Password required for this user');\n          } // Connect to socket with auth data\n\n\n          console.log('Connecting to socket...');\n\n          _this.socketService.connect(username, password, inviteCode);\n\n          _this.userSubject.next(username);\n\n          _this.isAdminSubject.next(userData?.isAdmin || false);\n\n          _this.securityInfoSubject.next(userData?.securityInfo || null);\n\n          console.log('Login completed successfully'); // Load user groups after successful login\n\n          _this.loadUserGroups(username);\n        } catch (error) {\n          console.error('Login failed:', error);\n          throw error;\n        } finally {\n          _this.loadingSubject.next(false);\n        }\n      }).apply(this, arguments);\n    }\n\n    loadRecentMessages(groupId) {\n      var _this2 = this;\n\n      return _asyncToGenerator(function* () {\n        try {\n          const messages = yield _this2.apiService.getMessages(groupId, 50).toPromise();\n\n          _this2.messagesSubject.next(messages || []);\n        } catch (error) {\n          console.error('Failed to load messages:', error);\n        }\n      })();\n    }\n\n    loadUserGroups(username) {\n      var _this3 = this;\n\n      return _asyncToGenerator(function* () {\n        try {\n          console.log('Loading user groups for:', username);\n          const groups = yield _this3.apiService.getUserGroups(username).toPromise();\n          console.log('Loaded user groups:', groups);\n\n          _this3.groupsSubject.next(groups || []);\n\n          if (groups && groups.length > 0) {\n            console.log('Auto-selecting first group:', groups[0]);\n\n            _this3.currentGroupSubject.next(groups[0]);\n\n            _this3.loadRecentMessages(groups[0].id);\n          }\n        } catch (error) {\n          console.error('Failed to load user groups:', error);\n        }\n      })();\n    }\n\n    joinGroup(groupId) {\n      const currentUser = this.userSubject.value;\n      if (!groupId || !currentUser) return;\n      console.log('Joining group:', groupId);\n      this.socketService.joinGroup(groupId);\n      const groups = this.groupsSubject.value;\n      const group = groups.find(g => g.id === groupId);\n\n      if (group) {\n        this.currentGroupSubject.next(group); // Load recent messages for the group\n\n        this.loadRecentMessages(groupId);\n      }\n    }\n\n    sendMessage(text, replyToId = null) {\n      const currentUser = this.userSubject.value;\n      const currentGroup = this.currentGroupSubject.value;\n\n      if (!text.trim()) {\n        console.error('Cannot send message - empty text');\n        return;\n      }\n\n      if (!currentUser) {\n        console.error('Cannot send message - user not logged in');\n        return;\n      }\n\n      if (!currentGroup) {\n        console.error('Cannot send message - no group selected. Available groups:', this.groupsSubject.value); // Try to auto-select the first available group\n\n        const groups = this.groupsSubject.value;\n\n        if (groups && groups.length > 0) {\n          console.log('Auto-selecting first available group:', groups[0]);\n          this.currentGroupSubject.next(groups[0]);\n          this.loadRecentMessages(groups[0].id); // Retry sending the message\n\n          setTimeout(() => this.sendMessage(text, replyToId), 100);\n          return;\n        } else {\n          console.error('No groups available for user');\n          return;\n        }\n      }\n\n      console.log('Sending message via socket:', {\n        text,\n        groupId: currentGroup.id,\n        replyToId\n      });\n      this.socketService.sendMessage(text, currentGroup.id, replyToId); // Fallback: If socket is not connected, try HTTP API\n\n      if (!this.socketService.isConnectedSubject.value) {\n        console.log('Socket not connected, trying HTTP API fallback');\n        this.apiService.sendMessage(text, currentUser, currentGroup.id, replyToId).subscribe({\n          next: response => {\n            console.log('Message sent via HTTP API:', response);\n          },\n          error: error => {\n            console.error('Failed to send message via HTTP API:', error);\n          }\n        });\n      }\n    }\n\n    replyToMessage(message) {\n      this.replyToSubject.next(message);\n    }\n\n    cancelReply() {\n      this.replyToSubject.next(null);\n    }\n\n    addReaction(messageId, emoji) {\n      this.socketService.addReaction(messageId, emoji);\n    }\n\n    removeReaction(data) {\n      this.socketService.removeReaction(data);\n    }\n\n    updateMessage(messageId, newText) {\n      var _this4 = this;\n\n      return _asyncToGenerator(function* () {\n        const currentUser = _this4.userSubject.value;\n\n        if (!currentUser) {\n          console.error('Cannot update message - user not logged in');\n          return;\n        }\n\n        try {\n          const updatedMessage = yield _this4.apiService.updateMessage(messageId, newText, currentUser).toPromise(); // Update the message in the local state\n\n          const currentMessages = _this4.messagesSubject.value;\n\n          _this4.messagesSubject.next(currentMessages.map(msg => msg.id === messageId ? { ...msg,\n            text: newText,\n            updated_at: updatedMessage.updated_at\n          } : msg)); // Emit socket event for real-time updates\n\n\n          _this4.socketService.emit('messageUpdated', {\n            messageId,\n            newText\n          });\n        } catch (error) {\n          console.error('Failed to update message:', error);\n          throw error;\n        }\n      })();\n    }\n\n    deleteMessage(messageId) {\n      var _this5 = this;\n\n      return _asyncToGenerator(function* () {\n        const currentUser = _this5.userSubject.value;\n\n        if (!currentUser) {\n          console.error('Cannot delete message - user not logged in');\n          return;\n        }\n\n        try {\n          yield _this5.apiService.deleteMessage(messageId, currentUser).toPromise(); // Remove the message from local state\n\n          const currentMessages = _this5.messagesSubject.value;\n\n          _this5.messagesSubject.next(currentMessages.filter(msg => msg.id !== messageId)); // Emit socket event for real-time updates\n\n\n          _this5.socketService.emit('messageDeleted', {\n            messageId\n          });\n        } catch (error) {\n          console.error('Failed to delete message:', error);\n          throw error;\n        }\n      })();\n    }\n\n    showAdminPanel() {\n      this.showAdminPanelSubject.next(true);\n    }\n\n    hideAdminPanel() {\n      this.showAdminPanelSubject.next(false);\n    }\n\n    startEditingMessage(message) {\n      this.editingMessageSubject.next(message);\n    }\n\n    cancelEditingMessage() {\n      this.editingMessageSubject.next(null);\n    }\n\n    logout() {\n      this.socketService.disconnect();\n\n      if (this.messageRefreshInterval) {\n        clearInterval(this.messageRefreshInterval);\n      }\n\n      this.userSubject.next(null);\n      this.messagesSubject.next([]);\n      this.onlineUsersSubject.next([]);\n      this.groupsSubject.next([]);\n      this.currentGroupSubject.next(null);\n      this.replyToSubject.next(null);\n      this.loadingSubject.next(false);\n      this.isAdminSubject.next(false);\n      this.showAdminPanelSubject.next(false);\n      this.securityInfoSubject.next(null);\n      this.editingMessageSubject.next(null);\n    }\n\n  }\n\n  ChatService.ɵfac = function ChatService_Factory(t) {\n    return new (t || ChatService)(i0.ɵɵinject(i1.ApiService), i0.ɵɵinject(i2.SocketService), i0.ɵɵinject(i3.NotificationService));\n  };\n\n  ChatService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ChatService,\n    factory: ChatService.ɵfac,\n    providedIn: 'root'\n  });\n  return ChatService;\n})();", "map": null, "metadata": {}, "sourceType": "module"}