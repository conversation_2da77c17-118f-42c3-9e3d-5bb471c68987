{"ast": null, "code": "import { Emitter } from \"@socket.io/component-emitter\";\nimport { deconstructPacket, reconstructPacket } from \"./binary.js\";\nimport { isBinary, hasBinary } from \"./is-binary.js\";\n/**\n * These strings must not be used as event names, as they have a special meaning.\n */\n\nconst RESERVED_EVENTS = [\"connect\", \"connect_error\", \"disconnect\", \"disconnecting\", \"newListener\", \"removeListener\" // used by the Node.js EventEmitter\n];\n/**\n * Protocol version.\n *\n * @public\n */\n\nexport const protocol = 5;\nexport var PacketType;\n\n(function (PacketType) {\n  PacketType[PacketType[\"CONNECT\"] = 0] = \"CONNECT\";\n  PacketType[PacketType[\"DISCONNECT\"] = 1] = \"DISCONNECT\";\n  PacketType[PacketType[\"EVENT\"] = 2] = \"EVENT\";\n  PacketType[PacketType[\"ACK\"] = 3] = \"ACK\";\n  PacketType[PacketType[\"CONNECT_ERROR\"] = 4] = \"CONNECT_ERROR\";\n  PacketType[PacketType[\"BINARY_EVENT\"] = 5] = \"BINARY_EVENT\";\n  PacketType[PacketType[\"BINARY_ACK\"] = 6] = \"BINARY_ACK\";\n})(PacketType || (PacketType = {}));\n/**\n * A socket.io Encoder instance\n */\n\n\nexport class Encoder {\n  /**\n   * Encoder constructor\n   *\n   * @param {function} replacer - custom replacer to pass down to JSON.parse\n   */\n  constructor(replacer) {\n    this.replacer = replacer;\n  }\n  /**\n   * Encode a packet as a single string if non-binary, or as a\n   * buffer sequence, depending on packet type.\n   *\n   * @param {Object} obj - packet object\n   */\n\n\n  encode(obj) {\n    if (obj.type === PacketType.EVENT || obj.type === PacketType.ACK) {\n      if (hasBinary(obj)) {\n        return this.encodeAsBinary({\n          type: obj.type === PacketType.EVENT ? PacketType.BINARY_EVENT : PacketType.BINARY_ACK,\n          nsp: obj.nsp,\n          data: obj.data,\n          id: obj.id\n        });\n      }\n    }\n\n    return [this.encodeAsString(obj)];\n  }\n  /**\n   * Encode packet as string.\n   */\n\n\n  encodeAsString(obj) {\n    // first is type\n    let str = \"\" + obj.type; // attachments if we have them\n\n    if (obj.type === PacketType.BINARY_EVENT || obj.type === PacketType.BINARY_ACK) {\n      str += obj.attachments + \"-\";\n    } // if we have a namespace other than `/`\n    // we append it followed by a comma `,`\n\n\n    if (obj.nsp && \"/\" !== obj.nsp) {\n      str += obj.nsp + \",\";\n    } // immediately followed by the id\n\n\n    if (null != obj.id) {\n      str += obj.id;\n    } // json data\n\n\n    if (null != obj.data) {\n      str += JSON.stringify(obj.data, this.replacer);\n    }\n\n    return str;\n  }\n  /**\n   * Encode packet as 'buffer sequence' by removing blobs, and\n   * deconstructing packet into object with placeholders and\n   * a list of buffers.\n   */\n\n\n  encodeAsBinary(obj) {\n    const deconstruction = deconstructPacket(obj);\n    const pack = this.encodeAsString(deconstruction.packet);\n    const buffers = deconstruction.buffers;\n    buffers.unshift(pack); // add packet info to beginning of data list\n\n    return buffers; // write all the buffers\n  }\n\n} // see https://stackoverflow.com/questions/8511281/check-if-a-value-is-an-object-in-javascript\n\nfunction isObject(value) {\n  return Object.prototype.toString.call(value) === \"[object Object]\";\n}\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n */\n\n\nexport class Decoder extends Emitter {\n  /**\n   * Decoder constructor\n   *\n   * @param {function} reviver - custom reviver to pass down to JSON.stringify\n   */\n  constructor(reviver) {\n    super();\n    this.reviver = reviver;\n  }\n  /**\n   * Decodes an encoded packet string into packet JSON.\n   *\n   * @param {String} obj - encoded packet\n   */\n\n\n  add(obj) {\n    let packet;\n\n    if (typeof obj === \"string\") {\n      if (this.reconstructor) {\n        throw new Error(\"got plaintext data when reconstructing a packet\");\n      }\n\n      packet = this.decodeString(obj);\n      const isBinaryEvent = packet.type === PacketType.BINARY_EVENT;\n\n      if (isBinaryEvent || packet.type === PacketType.BINARY_ACK) {\n        packet.type = isBinaryEvent ? PacketType.EVENT : PacketType.ACK; // binary packet's json\n\n        this.reconstructor = new BinaryReconstructor(packet); // no attachments, labeled binary but no binary data to follow\n\n        if (packet.attachments === 0) {\n          super.emitReserved(\"decoded\", packet);\n        }\n      } else {\n        // non-binary full packet\n        super.emitReserved(\"decoded\", packet);\n      }\n    } else if (isBinary(obj) || obj.base64) {\n      // raw binary data\n      if (!this.reconstructor) {\n        throw new Error(\"got binary data when not reconstructing a packet\");\n      } else {\n        packet = this.reconstructor.takeBinaryData(obj);\n\n        if (packet) {\n          // received final buffer\n          this.reconstructor = null;\n          super.emitReserved(\"decoded\", packet);\n        }\n      }\n    } else {\n      throw new Error(\"Unknown type: \" + obj);\n    }\n  }\n  /**\n   * Decode a packet String (JSON data)\n   *\n   * @param {String} str\n   * @return {Object} packet\n   */\n\n\n  decodeString(str) {\n    let i = 0; // look up type\n\n    const p = {\n      type: Number(str.charAt(0))\n    };\n\n    if (PacketType[p.type] === undefined) {\n      throw new Error(\"unknown packet type \" + p.type);\n    } // look up attachments if type binary\n\n\n    if (p.type === PacketType.BINARY_EVENT || p.type === PacketType.BINARY_ACK) {\n      const start = i + 1;\n\n      while (str.charAt(++i) !== \"-\" && i != str.length) {}\n\n      const buf = str.substring(start, i);\n\n      if (buf != Number(buf) || str.charAt(i) !== \"-\") {\n        throw new Error(\"Illegal attachments\");\n      }\n\n      p.attachments = Number(buf);\n    } // look up namespace (if any)\n\n\n    if (\"/\" === str.charAt(i + 1)) {\n      const start = i + 1;\n\n      while (++i) {\n        const c = str.charAt(i);\n        if (\",\" === c) break;\n        if (i === str.length) break;\n      }\n\n      p.nsp = str.substring(start, i);\n    } else {\n      p.nsp = \"/\";\n    } // look up id\n\n\n    const next = str.charAt(i + 1);\n\n    if (\"\" !== next && Number(next) == next) {\n      const start = i + 1;\n\n      while (++i) {\n        const c = str.charAt(i);\n\n        if (null == c || Number(c) != c) {\n          --i;\n          break;\n        }\n\n        if (i === str.length) break;\n      }\n\n      p.id = Number(str.substring(start, i + 1));\n    } // look up json data\n\n\n    if (str.charAt(++i)) {\n      const payload = this.tryParse(str.substr(i));\n\n      if (Decoder.isPayloadValid(p.type, payload)) {\n        p.data = payload;\n      } else {\n        throw new Error(\"invalid payload\");\n      }\n    }\n\n    return p;\n  }\n\n  tryParse(str) {\n    try {\n      return JSON.parse(str, this.reviver);\n    } catch (e) {\n      return false;\n    }\n  }\n\n  static isPayloadValid(type, payload) {\n    switch (type) {\n      case PacketType.CONNECT:\n        return isObject(payload);\n\n      case PacketType.DISCONNECT:\n        return payload === undefined;\n\n      case PacketType.CONNECT_ERROR:\n        return typeof payload === \"string\" || isObject(payload);\n\n      case PacketType.EVENT:\n      case PacketType.BINARY_EVENT:\n        return Array.isArray(payload) && (typeof payload[0] === \"number\" || typeof payload[0] === \"string\" && RESERVED_EVENTS.indexOf(payload[0]) === -1);\n\n      case PacketType.ACK:\n      case PacketType.BINARY_ACK:\n        return Array.isArray(payload);\n    }\n  }\n  /**\n   * Deallocates a parser's resources\n   */\n\n\n  destroy() {\n    if (this.reconstructor) {\n      this.reconstructor.finishedReconstruction();\n      this.reconstructor = null;\n    }\n  }\n\n}\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n */\n\nclass BinaryReconstructor {\n  constructor(packet) {\n    this.packet = packet;\n    this.buffers = [];\n    this.reconPack = packet;\n  }\n  /**\n   * Method to be called when binary data received from connection\n   * after a BINARY_EVENT packet.\n   *\n   * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n   * @return {null | Object} returns null if more binary data is expected or\n   *   a reconstructed packet object if all buffers have been received.\n   */\n\n\n  takeBinaryData(binData) {\n    this.buffers.push(binData);\n\n    if (this.buffers.length === this.reconPack.attachments) {\n      // done with buffer list\n      const packet = reconstructPacket(this.reconPack, this.buffers);\n      this.finishedReconstruction();\n      return packet;\n    }\n\n    return null;\n  }\n  /**\n   * Cleans up binary packet reconstruction variables.\n   */\n\n\n  finishedReconstruction() {\n    this.reconPack = null;\n    this.buffers = [];\n  }\n\n}", "map": {"version": 3, "names": ["Emitter", "deconstructPacket", "reconstructPacket", "isBinary", "hasBinary", "RESERVED_EVENTS", "protocol", "PacketType", "Encoder", "constructor", "replacer", "encode", "obj", "type", "EVENT", "ACK", "encodeAsBinary", "BINARY_EVENT", "BINARY_ACK", "nsp", "data", "id", "encodeAsString", "str", "attachments", "JSON", "stringify", "deconstruction", "pack", "packet", "buffers", "unshift", "isObject", "value", "Object", "prototype", "toString", "call", "Decoder", "reviver", "add", "reconstructor", "Error", "decodeString", "isBinaryEvent", "BinaryReconstructor", "emit<PERSON><PERSON><PERSON><PERSON>", "base64", "takeBinaryData", "i", "p", "Number", "char<PERSON>t", "undefined", "start", "length", "buf", "substring", "c", "next", "payload", "try<PERSON><PERSON><PERSON>", "substr", "isPayloadValid", "parse", "e", "CONNECT", "DISCONNECT", "CONNECT_ERROR", "Array", "isArray", "indexOf", "destroy", "finishedReconstruction", "reconPack", "binData", "push"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/socket.io-parser/build/esm/index.js"], "sourcesContent": ["import { Emitter } from \"@socket.io/component-emitter\";\nimport { deconstructPacket, reconstructPacket } from \"./binary.js\";\nimport { isBinary, hasBinary } from \"./is-binary.js\";\n/**\n * These strings must not be used as event names, as they have a special meaning.\n */\nconst RESERVED_EVENTS = [\n    \"connect\",\n    \"connect_error\",\n    \"disconnect\",\n    \"disconnecting\",\n    \"newListener\",\n    \"removeListener\", // used by the Node.js EventEmitter\n];\n/**\n * Protocol version.\n *\n * @public\n */\nexport const protocol = 5;\nexport var PacketType;\n(function (PacketType) {\n    PacketType[PacketType[\"CONNECT\"] = 0] = \"CONNECT\";\n    PacketType[PacketType[\"DISCONNECT\"] = 1] = \"DISCONNECT\";\n    PacketType[PacketType[\"EVENT\"] = 2] = \"EVENT\";\n    PacketType[PacketType[\"ACK\"] = 3] = \"ACK\";\n    PacketType[PacketType[\"CONNECT_ERROR\"] = 4] = \"CONNECT_ERROR\";\n    PacketType[PacketType[\"BINARY_EVENT\"] = 5] = \"BINARY_EVENT\";\n    PacketType[PacketType[\"BINARY_ACK\"] = 6] = \"BINARY_ACK\";\n})(PacketType || (PacketType = {}));\n/**\n * A socket.io Encoder instance\n */\nexport class Encoder {\n    /**\n     * Encoder constructor\n     *\n     * @param {function} replacer - custom replacer to pass down to JSON.parse\n     */\n    constructor(replacer) {\n        this.replacer = replacer;\n    }\n    /**\n     * Encode a packet as a single string if non-binary, or as a\n     * buffer sequence, depending on packet type.\n     *\n     * @param {Object} obj - packet object\n     */\n    encode(obj) {\n        if (obj.type === PacketType.EVENT || obj.type === PacketType.ACK) {\n            if (hasBinary(obj)) {\n                return this.encodeAsBinary({\n                    type: obj.type === PacketType.EVENT\n                        ? PacketType.BINARY_EVENT\n                        : PacketType.BINARY_ACK,\n                    nsp: obj.nsp,\n                    data: obj.data,\n                    id: obj.id,\n                });\n            }\n        }\n        return [this.encodeAsString(obj)];\n    }\n    /**\n     * Encode packet as string.\n     */\n    encodeAsString(obj) {\n        // first is type\n        let str = \"\" + obj.type;\n        // attachments if we have them\n        if (obj.type === PacketType.BINARY_EVENT ||\n            obj.type === PacketType.BINARY_ACK) {\n            str += obj.attachments + \"-\";\n        }\n        // if we have a namespace other than `/`\n        // we append it followed by a comma `,`\n        if (obj.nsp && \"/\" !== obj.nsp) {\n            str += obj.nsp + \",\";\n        }\n        // immediately followed by the id\n        if (null != obj.id) {\n            str += obj.id;\n        }\n        // json data\n        if (null != obj.data) {\n            str += JSON.stringify(obj.data, this.replacer);\n        }\n        return str;\n    }\n    /**\n     * Encode packet as 'buffer sequence' by removing blobs, and\n     * deconstructing packet into object with placeholders and\n     * a list of buffers.\n     */\n    encodeAsBinary(obj) {\n        const deconstruction = deconstructPacket(obj);\n        const pack = this.encodeAsString(deconstruction.packet);\n        const buffers = deconstruction.buffers;\n        buffers.unshift(pack); // add packet info to beginning of data list\n        return buffers; // write all the buffers\n    }\n}\n// see https://stackoverflow.com/questions/8511281/check-if-a-value-is-an-object-in-javascript\nfunction isObject(value) {\n    return Object.prototype.toString.call(value) === \"[object Object]\";\n}\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n */\nexport class Decoder extends Emitter {\n    /**\n     * Decoder constructor\n     *\n     * @param {function} reviver - custom reviver to pass down to JSON.stringify\n     */\n    constructor(reviver) {\n        super();\n        this.reviver = reviver;\n    }\n    /**\n     * Decodes an encoded packet string into packet JSON.\n     *\n     * @param {String} obj - encoded packet\n     */\n    add(obj) {\n        let packet;\n        if (typeof obj === \"string\") {\n            if (this.reconstructor) {\n                throw new Error(\"got plaintext data when reconstructing a packet\");\n            }\n            packet = this.decodeString(obj);\n            const isBinaryEvent = packet.type === PacketType.BINARY_EVENT;\n            if (isBinaryEvent || packet.type === PacketType.BINARY_ACK) {\n                packet.type = isBinaryEvent ? PacketType.EVENT : PacketType.ACK;\n                // binary packet's json\n                this.reconstructor = new BinaryReconstructor(packet);\n                // no attachments, labeled binary but no binary data to follow\n                if (packet.attachments === 0) {\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n            else {\n                // non-binary full packet\n                super.emitReserved(\"decoded\", packet);\n            }\n        }\n        else if (isBinary(obj) || obj.base64) {\n            // raw binary data\n            if (!this.reconstructor) {\n                throw new Error(\"got binary data when not reconstructing a packet\");\n            }\n            else {\n                packet = this.reconstructor.takeBinaryData(obj);\n                if (packet) {\n                    // received final buffer\n                    this.reconstructor = null;\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n        }\n        else {\n            throw new Error(\"Unknown type: \" + obj);\n        }\n    }\n    /**\n     * Decode a packet String (JSON data)\n     *\n     * @param {String} str\n     * @return {Object} packet\n     */\n    decodeString(str) {\n        let i = 0;\n        // look up type\n        const p = {\n            type: Number(str.charAt(0)),\n        };\n        if (PacketType[p.type] === undefined) {\n            throw new Error(\"unknown packet type \" + p.type);\n        }\n        // look up attachments if type binary\n        if (p.type === PacketType.BINARY_EVENT ||\n            p.type === PacketType.BINARY_ACK) {\n            const start = i + 1;\n            while (str.charAt(++i) !== \"-\" && i != str.length) { }\n            const buf = str.substring(start, i);\n            if (buf != Number(buf) || str.charAt(i) !== \"-\") {\n                throw new Error(\"Illegal attachments\");\n            }\n            p.attachments = Number(buf);\n        }\n        // look up namespace (if any)\n        if (\"/\" === str.charAt(i + 1)) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (\",\" === c)\n                    break;\n                if (i === str.length)\n                    break;\n            }\n            p.nsp = str.substring(start, i);\n        }\n        else {\n            p.nsp = \"/\";\n        }\n        // look up id\n        const next = str.charAt(i + 1);\n        if (\"\" !== next && Number(next) == next) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (null == c || Number(c) != c) {\n                    --i;\n                    break;\n                }\n                if (i === str.length)\n                    break;\n            }\n            p.id = Number(str.substring(start, i + 1));\n        }\n        // look up json data\n        if (str.charAt(++i)) {\n            const payload = this.tryParse(str.substr(i));\n            if (Decoder.isPayloadValid(p.type, payload)) {\n                p.data = payload;\n            }\n            else {\n                throw new Error(\"invalid payload\");\n            }\n        }\n        return p;\n    }\n    tryParse(str) {\n        try {\n            return JSON.parse(str, this.reviver);\n        }\n        catch (e) {\n            return false;\n        }\n    }\n    static isPayloadValid(type, payload) {\n        switch (type) {\n            case PacketType.CONNECT:\n                return isObject(payload);\n            case PacketType.DISCONNECT:\n                return payload === undefined;\n            case PacketType.CONNECT_ERROR:\n                return typeof payload === \"string\" || isObject(payload);\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                return (Array.isArray(payload) &&\n                    (typeof payload[0] === \"number\" ||\n                        (typeof payload[0] === \"string\" &&\n                            RESERVED_EVENTS.indexOf(payload[0]) === -1)));\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                return Array.isArray(payload);\n        }\n    }\n    /**\n     * Deallocates a parser's resources\n     */\n    destroy() {\n        if (this.reconstructor) {\n            this.reconstructor.finishedReconstruction();\n            this.reconstructor = null;\n        }\n    }\n}\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n */\nclass BinaryReconstructor {\n    constructor(packet) {\n        this.packet = packet;\n        this.buffers = [];\n        this.reconPack = packet;\n    }\n    /**\n     * Method to be called when binary data received from connection\n     * after a BINARY_EVENT packet.\n     *\n     * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n     * @return {null | Object} returns null if more binary data is expected or\n     *   a reconstructed packet object if all buffers have been received.\n     */\n    takeBinaryData(binData) {\n        this.buffers.push(binData);\n        if (this.buffers.length === this.reconPack.attachments) {\n            // done with buffer list\n            const packet = reconstructPacket(this.reconPack, this.buffers);\n            this.finishedReconstruction();\n            return packet;\n        }\n        return null;\n    }\n    /**\n     * Cleans up binary packet reconstruction variables.\n     */\n    finishedReconstruction() {\n        this.reconPack = null;\n        this.buffers = [];\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,8BAAxB;AACA,SAASC,iBAAT,EAA4BC,iBAA5B,QAAqD,aAArD;AACA,SAASC,QAAT,EAAmBC,SAAnB,QAAoC,gBAApC;AACA;AACA;AACA;;AACA,MAAMC,eAAe,GAAG,CACpB,SADoB,EAEpB,eAFoB,EAGpB,YAHoB,EAIpB,eAJoB,EAKpB,aALoB,EAMpB,gBANoB,CAMF;AANE,CAAxB;AAQA;AACA;AACA;AACA;AACA;;AACA,OAAO,MAAMC,QAAQ,GAAG,CAAjB;AACP,OAAO,IAAIC,UAAJ;;AACP,CAAC,UAAUA,UAAV,EAAsB;EACnBA,UAAU,CAACA,UAAU,CAAC,SAAD,CAAV,GAAwB,CAAzB,CAAV,GAAwC,SAAxC;EACAA,UAAU,CAACA,UAAU,CAAC,YAAD,CAAV,GAA2B,CAA5B,CAAV,GAA2C,YAA3C;EACAA,UAAU,CAACA,UAAU,CAAC,OAAD,CAAV,GAAsB,CAAvB,CAAV,GAAsC,OAAtC;EACAA,UAAU,CAACA,UAAU,CAAC,KAAD,CAAV,GAAoB,CAArB,CAAV,GAAoC,KAApC;EACAA,UAAU,CAACA,UAAU,CAAC,eAAD,CAAV,GAA8B,CAA/B,CAAV,GAA8C,eAA9C;EACAA,UAAU,CAACA,UAAU,CAAC,cAAD,CAAV,GAA6B,CAA9B,CAAV,GAA6C,cAA7C;EACAA,UAAU,CAACA,UAAU,CAAC,YAAD,CAAV,GAA2B,CAA5B,CAAV,GAA2C,YAA3C;AACH,CARD,EAQGA,UAAU,KAAKA,UAAU,GAAG,EAAlB,CARb;AASA;AACA;AACA;;;AACA,OAAO,MAAMC,OAAN,CAAc;EACjB;AACJ;AACA;AACA;AACA;EACIC,WAAW,CAACC,QAAD,EAAW;IAClB,KAAKA,QAAL,GAAgBA,QAAhB;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIC,MAAM,CAACC,GAAD,EAAM;IACR,IAAIA,GAAG,CAACC,IAAJ,KAAaN,UAAU,CAACO,KAAxB,IAAiCF,GAAG,CAACC,IAAJ,KAAaN,UAAU,CAACQ,GAA7D,EAAkE;MAC9D,IAAIX,SAAS,CAACQ,GAAD,CAAb,EAAoB;QAChB,OAAO,KAAKI,cAAL,CAAoB;UACvBH,IAAI,EAAED,GAAG,CAACC,IAAJ,KAAaN,UAAU,CAACO,KAAxB,GACAP,UAAU,CAACU,YADX,GAEAV,UAAU,CAACW,UAHM;UAIvBC,GAAG,EAAEP,GAAG,CAACO,GAJc;UAKvBC,IAAI,EAAER,GAAG,CAACQ,IALa;UAMvBC,EAAE,EAAET,GAAG,CAACS;QANe,CAApB,CAAP;MAQH;IACJ;;IACD,OAAO,CAAC,KAAKC,cAAL,CAAoBV,GAApB,CAAD,CAAP;EACH;EACD;AACJ;AACA;;;EACIU,cAAc,CAACV,GAAD,EAAM;IAChB;IACA,IAAIW,GAAG,GAAG,KAAKX,GAAG,CAACC,IAAnB,CAFgB,CAGhB;;IACA,IAAID,GAAG,CAACC,IAAJ,KAAaN,UAAU,CAACU,YAAxB,IACAL,GAAG,CAACC,IAAJ,KAAaN,UAAU,CAACW,UAD5B,EACwC;MACpCK,GAAG,IAAIX,GAAG,CAACY,WAAJ,GAAkB,GAAzB;IACH,CAPe,CAQhB;IACA;;;IACA,IAAIZ,GAAG,CAACO,GAAJ,IAAW,QAAQP,GAAG,CAACO,GAA3B,EAAgC;MAC5BI,GAAG,IAAIX,GAAG,CAACO,GAAJ,GAAU,GAAjB;IACH,CAZe,CAahB;;;IACA,IAAI,QAAQP,GAAG,CAACS,EAAhB,EAAoB;MAChBE,GAAG,IAAIX,GAAG,CAACS,EAAX;IACH,CAhBe,CAiBhB;;;IACA,IAAI,QAAQT,GAAG,CAACQ,IAAhB,EAAsB;MAClBG,GAAG,IAAIE,IAAI,CAACC,SAAL,CAAed,GAAG,CAACQ,IAAnB,EAAyB,KAAKV,QAA9B,CAAP;IACH;;IACD,OAAOa,GAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIP,cAAc,CAACJ,GAAD,EAAM;IAChB,MAAMe,cAAc,GAAG1B,iBAAiB,CAACW,GAAD,CAAxC;IACA,MAAMgB,IAAI,GAAG,KAAKN,cAAL,CAAoBK,cAAc,CAACE,MAAnC,CAAb;IACA,MAAMC,OAAO,GAAGH,cAAc,CAACG,OAA/B;IACAA,OAAO,CAACC,OAAR,CAAgBH,IAAhB,EAJgB,CAIO;;IACvB,OAAOE,OAAP,CALgB,CAKA;EACnB;;AAnEgB,C,CAqErB;;AACA,SAASE,QAAT,CAAkBC,KAAlB,EAAyB;EACrB,OAAOC,MAAM,CAACC,SAAP,CAAiBC,QAAjB,CAA0BC,IAA1B,CAA+BJ,KAA/B,MAA0C,iBAAjD;AACH;AACD;AACA;AACA;AACA;AACA;;;AACA,OAAO,MAAMK,OAAN,SAAsBtC,OAAtB,CAA8B;EACjC;AACJ;AACA;AACA;AACA;EACIS,WAAW,CAAC8B,OAAD,EAAU;IACjB;IACA,KAAKA,OAAL,GAAeA,OAAf;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIC,GAAG,CAAC5B,GAAD,EAAM;IACL,IAAIiB,MAAJ;;IACA,IAAI,OAAOjB,GAAP,KAAe,QAAnB,EAA6B;MACzB,IAAI,KAAK6B,aAAT,EAAwB;QACpB,MAAM,IAAIC,KAAJ,CAAU,iDAAV,CAAN;MACH;;MACDb,MAAM,GAAG,KAAKc,YAAL,CAAkB/B,GAAlB,CAAT;MACA,MAAMgC,aAAa,GAAGf,MAAM,CAAChB,IAAP,KAAgBN,UAAU,CAACU,YAAjD;;MACA,IAAI2B,aAAa,IAAIf,MAAM,CAAChB,IAAP,KAAgBN,UAAU,CAACW,UAAhD,EAA4D;QACxDW,MAAM,CAAChB,IAAP,GAAc+B,aAAa,GAAGrC,UAAU,CAACO,KAAd,GAAsBP,UAAU,CAACQ,GAA5D,CADwD,CAExD;;QACA,KAAK0B,aAAL,GAAqB,IAAII,mBAAJ,CAAwBhB,MAAxB,CAArB,CAHwD,CAIxD;;QACA,IAAIA,MAAM,CAACL,WAAP,KAAuB,CAA3B,EAA8B;UAC1B,MAAMsB,YAAN,CAAmB,SAAnB,EAA8BjB,MAA9B;QACH;MACJ,CARD,MASK;QACD;QACA,MAAMiB,YAAN,CAAmB,SAAnB,EAA8BjB,MAA9B;MACH;IACJ,CAnBD,MAoBK,IAAI1B,QAAQ,CAACS,GAAD,CAAR,IAAiBA,GAAG,CAACmC,MAAzB,EAAiC;MAClC;MACA,IAAI,CAAC,KAAKN,aAAV,EAAyB;QACrB,MAAM,IAAIC,KAAJ,CAAU,kDAAV,CAAN;MACH,CAFD,MAGK;QACDb,MAAM,GAAG,KAAKY,aAAL,CAAmBO,cAAnB,CAAkCpC,GAAlC,CAAT;;QACA,IAAIiB,MAAJ,EAAY;UACR;UACA,KAAKY,aAAL,GAAqB,IAArB;UACA,MAAMK,YAAN,CAAmB,SAAnB,EAA8BjB,MAA9B;QACH;MACJ;IACJ,CAbI,MAcA;MACD,MAAM,IAAIa,KAAJ,CAAU,mBAAmB9B,GAA7B,CAAN;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACI+B,YAAY,CAACpB,GAAD,EAAM;IACd,IAAI0B,CAAC,GAAG,CAAR,CADc,CAEd;;IACA,MAAMC,CAAC,GAAG;MACNrC,IAAI,EAAEsC,MAAM,CAAC5B,GAAG,CAAC6B,MAAJ,CAAW,CAAX,CAAD;IADN,CAAV;;IAGA,IAAI7C,UAAU,CAAC2C,CAAC,CAACrC,IAAH,CAAV,KAAuBwC,SAA3B,EAAsC;MAClC,MAAM,IAAIX,KAAJ,CAAU,yBAAyBQ,CAAC,CAACrC,IAArC,CAAN;IACH,CARa,CASd;;;IACA,IAAIqC,CAAC,CAACrC,IAAF,KAAWN,UAAU,CAACU,YAAtB,IACAiC,CAAC,CAACrC,IAAF,KAAWN,UAAU,CAACW,UAD1B,EACsC;MAClC,MAAMoC,KAAK,GAAGL,CAAC,GAAG,CAAlB;;MACA,OAAO1B,GAAG,CAAC6B,MAAJ,CAAW,EAAEH,CAAb,MAAoB,GAApB,IAA2BA,CAAC,IAAI1B,GAAG,CAACgC,MAA3C,EAAmD,CAAG;;MACtD,MAAMC,GAAG,GAAGjC,GAAG,CAACkC,SAAJ,CAAcH,KAAd,EAAqBL,CAArB,CAAZ;;MACA,IAAIO,GAAG,IAAIL,MAAM,CAACK,GAAD,CAAb,IAAsBjC,GAAG,CAAC6B,MAAJ,CAAWH,CAAX,MAAkB,GAA5C,EAAiD;QAC7C,MAAM,IAAIP,KAAJ,CAAU,qBAAV,CAAN;MACH;;MACDQ,CAAC,CAAC1B,WAAF,GAAgB2B,MAAM,CAACK,GAAD,CAAtB;IACH,CAnBa,CAoBd;;;IACA,IAAI,QAAQjC,GAAG,CAAC6B,MAAJ,CAAWH,CAAC,GAAG,CAAf,CAAZ,EAA+B;MAC3B,MAAMK,KAAK,GAAGL,CAAC,GAAG,CAAlB;;MACA,OAAO,EAAEA,CAAT,EAAY;QACR,MAAMS,CAAC,GAAGnC,GAAG,CAAC6B,MAAJ,CAAWH,CAAX,CAAV;QACA,IAAI,QAAQS,CAAZ,EACI;QACJ,IAAIT,CAAC,KAAK1B,GAAG,CAACgC,MAAd,EACI;MACP;;MACDL,CAAC,CAAC/B,GAAF,GAAQI,GAAG,CAACkC,SAAJ,CAAcH,KAAd,EAAqBL,CAArB,CAAR;IACH,CAVD,MAWK;MACDC,CAAC,CAAC/B,GAAF,GAAQ,GAAR;IACH,CAlCa,CAmCd;;;IACA,MAAMwC,IAAI,GAAGpC,GAAG,CAAC6B,MAAJ,CAAWH,CAAC,GAAG,CAAf,CAAb;;IACA,IAAI,OAAOU,IAAP,IAAeR,MAAM,CAACQ,IAAD,CAAN,IAAgBA,IAAnC,EAAyC;MACrC,MAAML,KAAK,GAAGL,CAAC,GAAG,CAAlB;;MACA,OAAO,EAAEA,CAAT,EAAY;QACR,MAAMS,CAAC,GAAGnC,GAAG,CAAC6B,MAAJ,CAAWH,CAAX,CAAV;;QACA,IAAI,QAAQS,CAAR,IAAaP,MAAM,CAACO,CAAD,CAAN,IAAaA,CAA9B,EAAiC;UAC7B,EAAET,CAAF;UACA;QACH;;QACD,IAAIA,CAAC,KAAK1B,GAAG,CAACgC,MAAd,EACI;MACP;;MACDL,CAAC,CAAC7B,EAAF,GAAO8B,MAAM,CAAC5B,GAAG,CAACkC,SAAJ,CAAcH,KAAd,EAAqBL,CAAC,GAAG,CAAzB,CAAD,CAAb;IACH,CAjDa,CAkDd;;;IACA,IAAI1B,GAAG,CAAC6B,MAAJ,CAAW,EAAEH,CAAb,CAAJ,EAAqB;MACjB,MAAMW,OAAO,GAAG,KAAKC,QAAL,CAActC,GAAG,CAACuC,MAAJ,CAAWb,CAAX,CAAd,CAAhB;;MACA,IAAIX,OAAO,CAACyB,cAAR,CAAuBb,CAAC,CAACrC,IAAzB,EAA+B+C,OAA/B,CAAJ,EAA6C;QACzCV,CAAC,CAAC9B,IAAF,GAASwC,OAAT;MACH,CAFD,MAGK;QACD,MAAM,IAAIlB,KAAJ,CAAU,iBAAV,CAAN;MACH;IACJ;;IACD,OAAOQ,CAAP;EACH;;EACDW,QAAQ,CAACtC,GAAD,EAAM;IACV,IAAI;MACA,OAAOE,IAAI,CAACuC,KAAL,CAAWzC,GAAX,EAAgB,KAAKgB,OAArB,CAAP;IACH,CAFD,CAGA,OAAO0B,CAAP,EAAU;MACN,OAAO,KAAP;IACH;EACJ;;EACoB,OAAdF,cAAc,CAAClD,IAAD,EAAO+C,OAAP,EAAgB;IACjC,QAAQ/C,IAAR;MACI,KAAKN,UAAU,CAAC2D,OAAhB;QACI,OAAOlC,QAAQ,CAAC4B,OAAD,CAAf;;MACJ,KAAKrD,UAAU,CAAC4D,UAAhB;QACI,OAAOP,OAAO,KAAKP,SAAnB;;MACJ,KAAK9C,UAAU,CAAC6D,aAAhB;QACI,OAAO,OAAOR,OAAP,KAAmB,QAAnB,IAA+B5B,QAAQ,CAAC4B,OAAD,CAA9C;;MACJ,KAAKrD,UAAU,CAACO,KAAhB;MACA,KAAKP,UAAU,CAACU,YAAhB;QACI,OAAQoD,KAAK,CAACC,OAAN,CAAcV,OAAd,MACH,OAAOA,OAAO,CAAC,CAAD,CAAd,KAAsB,QAAtB,IACI,OAAOA,OAAO,CAAC,CAAD,CAAd,KAAsB,QAAtB,IACGvD,eAAe,CAACkE,OAAhB,CAAwBX,OAAO,CAAC,CAAD,CAA/B,MAAwC,CAAC,CAH7C,CAAR;;MAIJ,KAAKrD,UAAU,CAACQ,GAAhB;MACA,KAAKR,UAAU,CAACW,UAAhB;QACI,OAAOmD,KAAK,CAACC,OAAN,CAAcV,OAAd,CAAP;IAfR;EAiBH;EACD;AACJ;AACA;;;EACIY,OAAO,GAAG;IACN,IAAI,KAAK/B,aAAT,EAAwB;MACpB,KAAKA,aAAL,CAAmBgC,sBAAnB;MACA,KAAKhC,aAAL,GAAqB,IAArB;IACH;EACJ;;AA9JgC;AAgKrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMI,mBAAN,CAA0B;EACtBpC,WAAW,CAACoB,MAAD,EAAS;IAChB,KAAKA,MAAL,GAAcA,MAAd;IACA,KAAKC,OAAL,GAAe,EAAf;IACA,KAAK4C,SAAL,GAAiB7C,MAAjB;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;EACImB,cAAc,CAAC2B,OAAD,EAAU;IACpB,KAAK7C,OAAL,CAAa8C,IAAb,CAAkBD,OAAlB;;IACA,IAAI,KAAK7C,OAAL,CAAayB,MAAb,KAAwB,KAAKmB,SAAL,CAAelD,WAA3C,EAAwD;MACpD;MACA,MAAMK,MAAM,GAAG3B,iBAAiB,CAAC,KAAKwE,SAAN,EAAiB,KAAK5C,OAAtB,CAAhC;MACA,KAAK2C,sBAAL;MACA,OAAO5C,MAAP;IACH;;IACD,OAAO,IAAP;EACH;EACD;AACJ;AACA;;;EACI4C,sBAAsB,GAAG;IACrB,KAAKC,SAAL,GAAiB,IAAjB;IACA,KAAK5C,OAAL,GAAe,EAAf;EACH;;AA9BqB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}