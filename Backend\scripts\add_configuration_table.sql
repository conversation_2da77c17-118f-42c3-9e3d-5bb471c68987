-- Add configuration table for storing security and app settings
CREATE TABLE IF NOT EXISTS configuration (
    id SERIAL PRIMARY KEY,
    key VARCHAR(100) UNIQUE NOT NULL,
    value TEXT NOT NULL,
    description TEXT,
    updated_by VARCHAR(50),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_configuration_key ON configuration(key);

-- Insert default configuration values
INSERT INTO configuration (key, value, description, updated_by) VALUES
('SECURITY_MODE', 'whitelist', 'Security mode: open, whitelist, or invite', 'system'),
('ADMIN_USERS', 'admin', 'Comma-separated list of admin usernames', 'system'),
('MAX_MESSAGE_LENGTH', '1000', 'Maximum message length in characters', 'system'),
('MESSAGE_RATE_LIMIT', '10', 'Maximum messages per minute per user', 'system'),
('SESSION_TIMEOUT', '3600', 'Session timeout in seconds (1 hour)', 'system')
ON CONFLICT (key) DO NOTHING;
