{"ast": null, "code": "const withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n\nconst isView = obj => {\n  return typeof ArrayBuffer.isView === \"function\" ? ArrayBuffer.isView(obj) : obj.buffer instanceof ArrayBuffer;\n};\n\nconst toString = Object.prototype.toString;\nconst withNativeBlob = typeof Blob === \"function\" || typeof Blob !== \"undefined\" && toString.call(Blob) === \"[object BlobConstructor]\";\nconst withNativeFile = typeof File === \"function\" || typeof File !== \"undefined\" && toString.call(File) === \"[object FileConstructor]\";\n/**\n * Returns true if obj is a Buffer, an ArrayBuffer, a Blob or a File.\n *\n * @private\n */\n\nexport function isBinary(obj) {\n  return withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj)) || withNativeBlob && obj instanceof Blob || withNativeFile && obj instanceof File;\n}\nexport function hasBinary(obj, toJSON) {\n  if (!obj || typeof obj !== \"object\") {\n    return false;\n  }\n\n  if (Array.isArray(obj)) {\n    for (let i = 0, l = obj.length; i < l; i++) {\n      if (hasBinary(obj[i])) {\n        return true;\n      }\n    }\n\n    return false;\n  }\n\n  if (isBinary(obj)) {\n    return true;\n  }\n\n  if (obj.toJSON && typeof obj.toJSON === \"function\" && arguments.length === 1) {\n    return hasBinary(obj.toJSON(), true);\n  }\n\n  for (const key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n      return true;\n    }\n  }\n\n  return false;\n}", "map": {"version": 3, "names": ["with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "obj", "buffer", "toString", "Object", "prototype", "withNativeBlob", "Blob", "call", "withNativeFile", "File", "isBinary", "hasBinary", "toJSON", "Array", "isArray", "i", "l", "length", "arguments", "key", "hasOwnProperty"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/socket.io-parser/build/esm/is-binary.js"], "sourcesContent": ["const withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj.buffer instanceof ArrayBuffer;\n};\nconst toString = Object.prototype.toString;\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeFile = typeof File === \"function\" ||\n    (typeof File !== \"undefined\" &&\n        toString.call(File) === \"[object FileConstructor]\");\n/**\n * Returns true if obj is a Buffer, an ArrayBuffer, a Blob or a File.\n *\n * @private\n */\nexport function isBinary(obj) {\n    return ((withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj))) ||\n        (withNativeBlob && obj instanceof Blob) ||\n        (withNativeFile && obj instanceof File));\n}\nexport function hasBinary(obj, toJSON) {\n    if (!obj || typeof obj !== \"object\") {\n        return false;\n    }\n    if (Array.isArray(obj)) {\n        for (let i = 0, l = obj.length; i < l; i++) {\n            if (hasBinary(obj[i])) {\n                return true;\n            }\n        }\n        return false;\n    }\n    if (isBinary(obj)) {\n        return true;\n    }\n    if (obj.toJSON &&\n        typeof obj.toJSON === \"function\" &&\n        arguments.length === 1) {\n        return hasBinary(obj.toJSON(), true);\n    }\n    for (const key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n            return true;\n        }\n    }\n    return false;\n}\n"], "mappings": "AAAA,MAAMA,qBAAqB,GAAG,OAAOC,WAAP,KAAuB,UAArD;;AACA,MAAMC,MAAM,GAAIC,GAAD,IAAS;EACpB,OAAO,OAAOF,WAAW,CAACC,MAAnB,KAA8B,UAA9B,GACDD,WAAW,CAACC,MAAZ,CAAmBC,GAAnB,CADC,GAEDA,GAAG,CAACC,MAAJ,YAAsBH,WAF5B;AAGH,CAJD;;AAKA,MAAMI,QAAQ,GAAGC,MAAM,CAACC,SAAP,CAAiBF,QAAlC;AACA,MAAMG,cAAc,GAAG,OAAOC,IAAP,KAAgB,UAAhB,IAClB,OAAOA,IAAP,KAAgB,WAAhB,IACGJ,QAAQ,CAACK,IAAT,CAAcD,IAAd,MAAwB,0BAFhC;AAGA,MAAME,cAAc,GAAG,OAAOC,IAAP,KAAgB,UAAhB,IAClB,OAAOA,IAAP,KAAgB,WAAhB,IACGP,QAAQ,CAACK,IAAT,CAAcE,IAAd,MAAwB,0BAFhC;AAGA;AACA;AACA;AACA;AACA;;AACA,OAAO,SAASC,QAAT,CAAkBV,GAAlB,EAAuB;EAC1B,OAASH,qBAAqB,KAAKG,GAAG,YAAYF,WAAf,IAA8BC,MAAM,CAACC,GAAD,CAAzC,CAAtB,IACHK,cAAc,IAAIL,GAAG,YAAYM,IAD9B,IAEHE,cAAc,IAAIR,GAAG,YAAYS,IAFtC;AAGH;AACD,OAAO,SAASE,SAAT,CAAmBX,GAAnB,EAAwBY,MAAxB,EAAgC;EACnC,IAAI,CAACZ,GAAD,IAAQ,OAAOA,GAAP,KAAe,QAA3B,EAAqC;IACjC,OAAO,KAAP;EACH;;EACD,IAAIa,KAAK,CAACC,OAAN,CAAcd,GAAd,CAAJ,EAAwB;IACpB,KAAK,IAAIe,CAAC,GAAG,CAAR,EAAWC,CAAC,GAAGhB,GAAG,CAACiB,MAAxB,EAAgCF,CAAC,GAAGC,CAApC,EAAuCD,CAAC,EAAxC,EAA4C;MACxC,IAAIJ,SAAS,CAACX,GAAG,CAACe,CAAD,CAAJ,CAAb,EAAuB;QACnB,OAAO,IAAP;MACH;IACJ;;IACD,OAAO,KAAP;EACH;;EACD,IAAIL,QAAQ,CAACV,GAAD,CAAZ,EAAmB;IACf,OAAO,IAAP;EACH;;EACD,IAAIA,GAAG,CAACY,MAAJ,IACA,OAAOZ,GAAG,CAACY,MAAX,KAAsB,UADtB,IAEAM,SAAS,CAACD,MAAV,KAAqB,CAFzB,EAE4B;IACxB,OAAON,SAAS,CAACX,GAAG,CAACY,MAAJ,EAAD,EAAe,IAAf,CAAhB;EACH;;EACD,KAAK,MAAMO,GAAX,IAAkBnB,GAAlB,EAAuB;IACnB,IAAIG,MAAM,CAACC,SAAP,CAAiBgB,cAAjB,CAAgCb,IAAhC,CAAqCP,GAArC,EAA0CmB,GAA1C,KAAkDR,SAAS,CAACX,GAAG,CAACmB,GAAD,CAAJ,CAA/D,EAA2E;MACvE,OAAO,IAAP;IACH;EACJ;;EACD,OAAO,KAAP;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}