{"ast": null, "code": "import { ERROR_PACKET, PACKET_TYPES_REVERSE } from \"./commons.js\";\nimport { decode } from \"./contrib/base64-arraybuffer.js\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nexport const decodePacket = (encodedPacket, binaryType) => {\n  if (typeof encodedPacket !== \"string\") {\n    return {\n      type: \"message\",\n      data: mapBinary(encodedPacket, binaryType)\n    };\n  }\n\n  const type = encodedPacket.charAt(0);\n\n  if (type === \"b\") {\n    return {\n      type: \"message\",\n      data: decodeBase64Packet(encodedPacket.substring(1), binaryType)\n    };\n  }\n\n  const packetType = PACKET_TYPES_REVERSE[type];\n\n  if (!packetType) {\n    return ERROR_PACKET;\n  }\n\n  return encodedPacket.length > 1 ? {\n    type: PACKET_TYPES_REVERSE[type],\n    data: encodedPacket.substring(1)\n  } : {\n    type: PACKET_TYPES_REVERSE[type]\n  };\n};\n\nconst decodeBase64Packet = (data, binaryType) => {\n  if (withNativeArrayBuffer) {\n    const decoded = decode(data);\n    return mapBinary(decoded, binaryType);\n  } else {\n    return {\n      base64: true,\n      data\n    }; // fallback for old browsers\n  }\n};\n\nconst mapBinary = (data, binaryType) => {\n  switch (binaryType) {\n    case \"blob\":\n      if (data instanceof Blob) {\n        // from WebSocket + binaryType \"blob\"\n        return data;\n      } else {\n        // from HTTP long-polling or WebTransport\n        return new Blob([data]);\n      }\n\n    case \"arraybuffer\":\n    default:\n      if (data instanceof ArrayBuffer) {\n        // from HTTP long-polling (base64) or WebSocket + binaryType \"arraybuffer\"\n        return data;\n      } else {\n        // from WebTransport (Uint8Array)\n        return data.buffer;\n      }\n\n  }\n};", "map": {"version": 3, "names": ["ERROR_PACKET", "PACKET_TYPES_REVERSE", "decode", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "decodePacket", "encodedPacket", "binaryType", "type", "data", "mapBinary", "char<PERSON>t", "decodeBase64Packet", "substring", "packetType", "length", "decoded", "base64", "Blob", "buffer"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/engine.io-parser/build/esm/decodePacket.browser.js"], "sourcesContent": ["import { ERROR_PACKET, PACKET_TYPES_REVERSE, } from \"./commons.js\";\nimport { decode } from \"./contrib/base64-arraybuffer.js\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nexport const decodePacket = (encodedPacket, binaryType) => {\n    if (typeof encodedPacket !== \"string\") {\n        return {\n            type: \"message\",\n            data: mapBinary(encodedPacket, binaryType),\n        };\n    }\n    const type = encodedPacket.charAt(0);\n    if (type === \"b\") {\n        return {\n            type: \"message\",\n            data: decodeBase64Packet(encodedPacket.substring(1), binaryType),\n        };\n    }\n    const packetType = PACKET_TYPES_REVERSE[type];\n    if (!packetType) {\n        return ERROR_PACKET;\n    }\n    return encodedPacket.length > 1\n        ? {\n            type: PACKET_TYPES_REVERSE[type],\n            data: encodedPacket.substring(1),\n        }\n        : {\n            type: PACKET_TYPES_REVERSE[type],\n        };\n};\nconst decodeBase64Packet = (data, binaryType) => {\n    if (withNativeArrayBuffer) {\n        const decoded = decode(data);\n        return mapBinary(decoded, binaryType);\n    }\n    else {\n        return { base64: true, data }; // fallback for old browsers\n    }\n};\nconst mapBinary = (data, binaryType) => {\n    switch (binaryType) {\n        case \"blob\":\n            if (data instanceof Blob) {\n                // from WebSocket + binaryType \"blob\"\n                return data;\n            }\n            else {\n                // from HTTP long-polling or WebTransport\n                return new Blob([data]);\n            }\n        case \"arraybuffer\":\n        default:\n            if (data instanceof ArrayBuffer) {\n                // from HTTP long-polling (base64) or WebSocket + binaryType \"arraybuffer\"\n                return data;\n            }\n            else {\n                // from WebTransport (Uint8Array)\n                return data.buffer;\n            }\n    }\n};\n"], "mappings": "AAAA,SAASA,YAAT,EAAuBC,oBAAvB,QAAoD,cAApD;AACA,SAASC,MAAT,QAAuB,iCAAvB;AACA,MAAMC,qBAAqB,GAAG,OAAOC,WAAP,KAAuB,UAArD;AACA,OAAO,MAAMC,YAAY,GAAG,CAACC,aAAD,EAAgBC,UAAhB,KAA+B;EACvD,IAAI,OAAOD,aAAP,KAAyB,QAA7B,EAAuC;IACnC,OAAO;MACHE,IAAI,EAAE,SADH;MAEHC,IAAI,EAAEC,SAAS,CAACJ,aAAD,EAAgBC,UAAhB;IAFZ,CAAP;EAIH;;EACD,MAAMC,IAAI,GAAGF,aAAa,CAACK,MAAd,CAAqB,CAArB,CAAb;;EACA,IAAIH,IAAI,KAAK,GAAb,EAAkB;IACd,OAAO;MACHA,IAAI,EAAE,SADH;MAEHC,IAAI,EAAEG,kBAAkB,CAACN,aAAa,CAACO,SAAd,CAAwB,CAAxB,CAAD,EAA6BN,UAA7B;IAFrB,CAAP;EAIH;;EACD,MAAMO,UAAU,GAAGb,oBAAoB,CAACO,IAAD,CAAvC;;EACA,IAAI,CAACM,UAAL,EAAiB;IACb,OAAOd,YAAP;EACH;;EACD,OAAOM,aAAa,CAACS,MAAd,GAAuB,CAAvB,GACD;IACEP,IAAI,EAAEP,oBAAoB,CAACO,IAAD,CAD5B;IAEEC,IAAI,EAAEH,aAAa,CAACO,SAAd,CAAwB,CAAxB;EAFR,CADC,GAKD;IACEL,IAAI,EAAEP,oBAAoB,CAACO,IAAD;EAD5B,CALN;AAQH,CA1BM;;AA2BP,MAAMI,kBAAkB,GAAG,CAACH,IAAD,EAAOF,UAAP,KAAsB;EAC7C,IAAIJ,qBAAJ,EAA2B;IACvB,MAAMa,OAAO,GAAGd,MAAM,CAACO,IAAD,CAAtB;IACA,OAAOC,SAAS,CAACM,OAAD,EAAUT,UAAV,CAAhB;EACH,CAHD,MAIK;IACD,OAAO;MAAEU,MAAM,EAAE,IAAV;MAAgBR;IAAhB,CAAP,CADC,CAC8B;EAClC;AACJ,CARD;;AASA,MAAMC,SAAS,GAAG,CAACD,IAAD,EAAOF,UAAP,KAAsB;EACpC,QAAQA,UAAR;IACI,KAAK,MAAL;MACI,IAAIE,IAAI,YAAYS,IAApB,EAA0B;QACtB;QACA,OAAOT,IAAP;MACH,CAHD,MAIK;QACD;QACA,OAAO,IAAIS,IAAJ,CAAS,CAACT,IAAD,CAAT,CAAP;MACH;;IACL,KAAK,aAAL;IACA;MACI,IAAIA,IAAI,YAAYL,WAApB,EAAiC;QAC7B;QACA,OAAOK,IAAP;MACH,CAHD,MAIK;QACD;QACA,OAAOA,IAAI,CAACU,MAAZ;MACH;;EAnBT;AAqBH,CAtBD", "ignoreList": []}, "metadata": {}, "sourceType": "module"}