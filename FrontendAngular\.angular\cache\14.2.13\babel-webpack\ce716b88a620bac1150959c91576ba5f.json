{"ast": null, "code": "import { Emitter } from \"@socket.io/component-emitter\";\nimport { deconstructPacket, reconstructPacket } from \"./binary.js\";\nimport { isBinary, hasBinary } from \"./is-binary.js\";\n/**\n * These strings must not be used as event names, as they have a special meaning.\n */\n\nconst RESERVED_EVENTS = [\"connect\", \"connect_error\", \"disconnect\", \"disconnecting\", \"newListener\", \"removeListener\" // used by the Node.js EventEmitter\n];\n/**\n * Protocol version.\n *\n * @public\n */\n\nexport const protocol = 5;\nexport var PacketType = /*#__PURE__*/(() => {\n  (function (PacketType) {\n    PacketType[PacketType[\"CONNECT\"] = 0] = \"CONNECT\";\n    PacketType[PacketType[\"DISCONNECT\"] = 1] = \"DISCONNECT\";\n    PacketType[PacketType[\"EVENT\"] = 2] = \"EVENT\";\n    PacketType[PacketType[\"ACK\"] = 3] = \"ACK\";\n    PacketType[PacketType[\"CONNECT_ERROR\"] = 4] = \"CONNECT_ERROR\";\n    PacketType[PacketType[\"BINARY_EVENT\"] = 5] = \"BINARY_EVENT\";\n    PacketType[PacketType[\"BINARY_ACK\"] = 6] = \"BINARY_ACK\";\n  })(PacketType || (PacketType = {}));\n\n  return PacketType;\n})();\n\n/**\n * A socket.io Encoder instance\n */\nexport class Encoder {\n  /**\n   * Encoder constructor\n   *\n   * @param {function} replacer - custom replacer to pass down to JSON.parse\n   */\n  constructor(replacer) {\n    this.replacer = replacer;\n  }\n  /**\n   * Encode a packet as a single string if non-binary, or as a\n   * buffer sequence, depending on packet type.\n   *\n   * @param {Object} obj - packet object\n   */\n\n\n  encode(obj) {\n    if (obj.type === PacketType.EVENT || obj.type === PacketType.ACK) {\n      if (hasBinary(obj)) {\n        return this.encodeAsBinary({\n          type: obj.type === PacketType.EVENT ? PacketType.BINARY_EVENT : PacketType.BINARY_ACK,\n          nsp: obj.nsp,\n          data: obj.data,\n          id: obj.id\n        });\n      }\n    }\n\n    return [this.encodeAsString(obj)];\n  }\n  /**\n   * Encode packet as string.\n   */\n\n\n  encodeAsString(obj) {\n    // first is type\n    let str = \"\" + obj.type; // attachments if we have them\n\n    if (obj.type === PacketType.BINARY_EVENT || obj.type === PacketType.BINARY_ACK) {\n      str += obj.attachments + \"-\";\n    } // if we have a namespace other than `/`\n    // we append it followed by a comma `,`\n\n\n    if (obj.nsp && \"/\" !== obj.nsp) {\n      str += obj.nsp + \",\";\n    } // immediately followed by the id\n\n\n    if (null != obj.id) {\n      str += obj.id;\n    } // json data\n\n\n    if (null != obj.data) {\n      str += JSON.stringify(obj.data, this.replacer);\n    }\n\n    return str;\n  }\n  /**\n   * Encode packet as 'buffer sequence' by removing blobs, and\n   * deconstructing packet into object with placeholders and\n   * a list of buffers.\n   */\n\n\n  encodeAsBinary(obj) {\n    const deconstruction = deconstructPacket(obj);\n    const pack = this.encodeAsString(deconstruction.packet);\n    const buffers = deconstruction.buffers;\n    buffers.unshift(pack); // add packet info to beginning of data list\n\n    return buffers; // write all the buffers\n  }\n\n} // see https://stackoverflow.com/questions/8511281/check-if-a-value-is-an-object-in-javascript\n\nfunction isObject(value) {\n  return Object.prototype.toString.call(value) === \"[object Object]\";\n}\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n */\n\n\nexport class Decoder extends Emitter {\n  /**\n   * Decoder constructor\n   *\n   * @param {function} reviver - custom reviver to pass down to JSON.stringify\n   */\n  constructor(reviver) {\n    super();\n    this.reviver = reviver;\n  }\n  /**\n   * Decodes an encoded packet string into packet JSON.\n   *\n   * @param {String} obj - encoded packet\n   */\n\n\n  add(obj) {\n    let packet;\n\n    if (typeof obj === \"string\") {\n      if (this.reconstructor) {\n        throw new Error(\"got plaintext data when reconstructing a packet\");\n      }\n\n      packet = this.decodeString(obj);\n      const isBinaryEvent = packet.type === PacketType.BINARY_EVENT;\n\n      if (isBinaryEvent || packet.type === PacketType.BINARY_ACK) {\n        packet.type = isBinaryEvent ? PacketType.EVENT : PacketType.ACK; // binary packet's json\n\n        this.reconstructor = new BinaryReconstructor(packet); // no attachments, labeled binary but no binary data to follow\n\n        if (packet.attachments === 0) {\n          super.emitReserved(\"decoded\", packet);\n        }\n      } else {\n        // non-binary full packet\n        super.emitReserved(\"decoded\", packet);\n      }\n    } else if (isBinary(obj) || obj.base64) {\n      // raw binary data\n      if (!this.reconstructor) {\n        throw new Error(\"got binary data when not reconstructing a packet\");\n      } else {\n        packet = this.reconstructor.takeBinaryData(obj);\n\n        if (packet) {\n          // received final buffer\n          this.reconstructor = null;\n          super.emitReserved(\"decoded\", packet);\n        }\n      }\n    } else {\n      throw new Error(\"Unknown type: \" + obj);\n    }\n  }\n  /**\n   * Decode a packet String (JSON data)\n   *\n   * @param {String} str\n   * @return {Object} packet\n   */\n\n\n  decodeString(str) {\n    let i = 0; // look up type\n\n    const p = {\n      type: Number(str.charAt(0))\n    };\n\n    if (PacketType[p.type] === undefined) {\n      throw new Error(\"unknown packet type \" + p.type);\n    } // look up attachments if type binary\n\n\n    if (p.type === PacketType.BINARY_EVENT || p.type === PacketType.BINARY_ACK) {\n      const start = i + 1;\n\n      while (str.charAt(++i) !== \"-\" && i != str.length) {}\n\n      const buf = str.substring(start, i);\n\n      if (buf != Number(buf) || str.charAt(i) !== \"-\") {\n        throw new Error(\"Illegal attachments\");\n      }\n\n      p.attachments = Number(buf);\n    } // look up namespace (if any)\n\n\n    if (\"/\" === str.charAt(i + 1)) {\n      const start = i + 1;\n\n      while (++i) {\n        const c = str.charAt(i);\n        if (\",\" === c) break;\n        if (i === str.length) break;\n      }\n\n      p.nsp = str.substring(start, i);\n    } else {\n      p.nsp = \"/\";\n    } // look up id\n\n\n    const next = str.charAt(i + 1);\n\n    if (\"\" !== next && Number(next) == next) {\n      const start = i + 1;\n\n      while (++i) {\n        const c = str.charAt(i);\n\n        if (null == c || Number(c) != c) {\n          --i;\n          break;\n        }\n\n        if (i === str.length) break;\n      }\n\n      p.id = Number(str.substring(start, i + 1));\n    } // look up json data\n\n\n    if (str.charAt(++i)) {\n      const payload = this.tryParse(str.substr(i));\n\n      if (Decoder.isPayloadValid(p.type, payload)) {\n        p.data = payload;\n      } else {\n        throw new Error(\"invalid payload\");\n      }\n    }\n\n    return p;\n  }\n\n  tryParse(str) {\n    try {\n      return JSON.parse(str, this.reviver);\n    } catch (e) {\n      return false;\n    }\n  }\n\n  static isPayloadValid(type, payload) {\n    switch (type) {\n      case PacketType.CONNECT:\n        return isObject(payload);\n\n      case PacketType.DISCONNECT:\n        return payload === undefined;\n\n      case PacketType.CONNECT_ERROR:\n        return typeof payload === \"string\" || isObject(payload);\n\n      case PacketType.EVENT:\n      case PacketType.BINARY_EVENT:\n        return Array.isArray(payload) && (typeof payload[0] === \"number\" || typeof payload[0] === \"string\" && RESERVED_EVENTS.indexOf(payload[0]) === -1);\n\n      case PacketType.ACK:\n      case PacketType.BINARY_ACK:\n        return Array.isArray(payload);\n    }\n  }\n  /**\n   * Deallocates a parser's resources\n   */\n\n\n  destroy() {\n    if (this.reconstructor) {\n      this.reconstructor.finishedReconstruction();\n      this.reconstructor = null;\n    }\n  }\n\n}\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n */\n\nclass BinaryReconstructor {\n  constructor(packet) {\n    this.packet = packet;\n    this.buffers = [];\n    this.reconPack = packet;\n  }\n  /**\n   * Method to be called when binary data received from connection\n   * after a BINARY_EVENT packet.\n   *\n   * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n   * @return {null | Object} returns null if more binary data is expected or\n   *   a reconstructed packet object if all buffers have been received.\n   */\n\n\n  takeBinaryData(binData) {\n    this.buffers.push(binData);\n\n    if (this.buffers.length === this.reconPack.attachments) {\n      // done with buffer list\n      const packet = reconstructPacket(this.reconPack, this.buffers);\n      this.finishedReconstruction();\n      return packet;\n    }\n\n    return null;\n  }\n  /**\n   * Cleans up binary packet reconstruction variables.\n   */\n\n\n  finishedReconstruction() {\n    this.reconPack = null;\n    this.buffers = [];\n  }\n\n}", "map": null, "metadata": {}, "sourceType": "module"}