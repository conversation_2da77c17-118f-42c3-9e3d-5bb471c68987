{"ast": null, "code": "import { isFunction } from './util/isFunction';\nimport { UnsubscriptionError } from './util/UnsubscriptionError';\nimport { arrRemove } from './util/arrRemove';\nexport class Subscription {\n  constructor(initialTeardown) {\n    this.initialTeardown = initialTeardown;\n    this.closed = false;\n    this._parentage = null;\n    this._finalizers = null;\n  }\n\n  unsubscribe() {\n    let errors;\n\n    if (!this.closed) {\n      this.closed = true;\n      const {\n        _parentage\n      } = this;\n\n      if (_parentage) {\n        this._parentage = null;\n\n        if (Array.isArray(_parentage)) {\n          for (const parent of _parentage) {\n            parent.remove(this);\n          }\n        } else {\n          _parentage.remove(this);\n        }\n      }\n\n      const {\n        initialTeardown: initialFinalizer\n      } = this;\n\n      if (isFunction(initialFinalizer)) {\n        try {\n          initialFinalizer();\n        } catch (e) {\n          errors = e instanceof UnsubscriptionError ? e.errors : [e];\n        }\n      }\n\n      const {\n        _finalizers\n      } = this;\n\n      if (_finalizers) {\n        this._finalizers = null;\n\n        for (const finalizer of _finalizers) {\n          try {\n            execFinalizer(finalizer);\n          } catch (err) {\n            errors = errors !== null && errors !== void 0 ? errors : [];\n\n            if (err instanceof UnsubscriptionError) {\n              errors = [...errors, ...err.errors];\n            } else {\n              errors.push(err);\n            }\n          }\n        }\n      }\n\n      if (errors) {\n        throw new UnsubscriptionError(errors);\n      }\n    }\n  }\n\n  add(teardown) {\n    var _a;\n\n    if (teardown && teardown !== this) {\n      if (this.closed) {\n        execFinalizer(teardown);\n      } else {\n        if (teardown instanceof Subscription) {\n          if (teardown.closed || teardown._hasParent(this)) {\n            return;\n          }\n\n          teardown._addParent(this);\n        }\n\n        (this._finalizers = (_a = this._finalizers) !== null && _a !== void 0 ? _a : []).push(teardown);\n      }\n    }\n  }\n\n  _hasParent(parent) {\n    const {\n      _parentage\n    } = this;\n    return _parentage === parent || Array.isArray(_parentage) && _parentage.includes(parent);\n  }\n\n  _addParent(parent) {\n    const {\n      _parentage\n    } = this;\n    this._parentage = Array.isArray(_parentage) ? (_parentage.push(parent), _parentage) : _parentage ? [_parentage, parent] : parent;\n  }\n\n  _removeParent(parent) {\n    const {\n      _parentage\n    } = this;\n\n    if (_parentage === parent) {\n      this._parentage = null;\n    } else if (Array.isArray(_parentage)) {\n      arrRemove(_parentage, parent);\n    }\n  }\n\n  remove(teardown) {\n    const {\n      _finalizers\n    } = this;\n    _finalizers && arrRemove(_finalizers, teardown);\n\n    if (teardown instanceof Subscription) {\n      teardown._removeParent(this);\n    }\n  }\n\n}\n\nSubscription.EMPTY = (() => {\n  const empty = new Subscription();\n  empty.closed = true;\n  return empty;\n})();\n\nexport const EMPTY_SUBSCRIPTION = Subscription.EMPTY;\nexport function isSubscription(value) {\n  return value instanceof Subscription || value && 'closed' in value && isFunction(value.remove) && isFunction(value.add) && isFunction(value.unsubscribe);\n}\n\nfunction execFinalizer(finalizer) {\n  if (isFunction(finalizer)) {\n    finalizer();\n  } else {\n    finalizer.unsubscribe();\n  }\n} //# sourceMappingURL=Subscription.js.map", "map": null, "metadata": {}, "sourceType": "module"}