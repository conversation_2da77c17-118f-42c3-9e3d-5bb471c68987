{"ast": null, "code": "import { ReplaySubject } from '../ReplaySubject';\nimport { share } from './share';\nexport function shareReplay(configOrBufferSize, windowTime, scheduler) {\n  let bufferSize;\n  let refCount = false;\n\n  if (configOrBufferSize && typeof configOrBufferSize === 'object') {\n    ({\n      bufferSize = Infinity,\n      windowTime = Infinity,\n      refCount = false,\n      scheduler\n    } = configOrBufferSize);\n  } else {\n    bufferSize = configOrBufferSize !== null && configOrBufferSize !== void 0 ? configOrBufferSize : Infinity;\n  }\n\n  return share({\n    connector: () => new ReplaySubject(bufferSize, windowTime, scheduler),\n    resetOnError: true,\n    resetOnComplete: false,\n    resetOnRefCountZero: refCount\n  });\n} //# sourceMappingURL=shareReplay.js.map", "map": null, "metadata": {}, "sourceType": "module"}