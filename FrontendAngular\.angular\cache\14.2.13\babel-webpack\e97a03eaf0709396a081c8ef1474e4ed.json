{"ast": null, "code": "import { identity } from '../util/identity';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function skipLast(skipCount) {\n  return skipCount <= 0 ? identity : operate((source, subscriber) => {\n    let ring = new Array(skipCount);\n    let seen = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      const valueIndex = seen++;\n\n      if (valueIndex < skipCount) {\n        ring[valueIndex] = value;\n      } else {\n        const index = valueIndex % skipCount;\n        const oldValue = ring[index];\n        ring[index] = value;\n        subscriber.next(oldValue);\n      }\n    }));\n    return () => {\n      ring = null;\n    };\n  });\n}", "map": {"version": 3, "names": ["identity", "operate", "createOperatorSubscriber", "skipLast", "skip<PERSON><PERSON>nt", "source", "subscriber", "ring", "Array", "seen", "subscribe", "value", "valueIndex", "index", "oldValue", "next"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/rxjs/dist/esm/internal/operators/skipLast.js"], "sourcesContent": ["import { identity } from '../util/identity';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function skipLast(skipCount) {\n    return skipCount <= 0\n        ?\n            identity\n        : operate((source, subscriber) => {\n            let ring = new Array(skipCount);\n            let seen = 0;\n            source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n                const valueIndex = seen++;\n                if (valueIndex < skipCount) {\n                    ring[valueIndex] = value;\n                }\n                else {\n                    const index = valueIndex % skipCount;\n                    const oldValue = ring[index];\n                    ring[index] = value;\n                    subscriber.next(oldValue);\n                }\n            }));\n            return () => {\n                ring = null;\n            };\n        });\n}\n"], "mappings": "AAAA,SAASA,QAAT,QAAyB,kBAAzB;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,OAAO,SAASC,QAAT,CAAkBC,SAAlB,EAA6B;EAChC,OAAOA,SAAS,IAAI,CAAb,GAECJ,QAFD,GAGDC,OAAO,CAAC,CAACI,MAAD,EAASC,UAAT,KAAwB;IAC9B,IAAIC,IAAI,GAAG,IAAIC,KAAJ,CAAUJ,SAAV,CAAX;IACA,IAAIK,IAAI,GAAG,CAAX;IACAJ,MAAM,CAACK,SAAP,CAAiBR,wBAAwB,CAACI,UAAD,EAAcK,KAAD,IAAW;MAC7D,MAAMC,UAAU,GAAGH,IAAI,EAAvB;;MACA,IAAIG,UAAU,GAAGR,SAAjB,EAA4B;QACxBG,IAAI,CAACK,UAAD,CAAJ,GAAmBD,KAAnB;MACH,CAFD,MAGK;QACD,MAAME,KAAK,GAAGD,UAAU,GAAGR,SAA3B;QACA,MAAMU,QAAQ,GAAGP,IAAI,CAACM,KAAD,CAArB;QACAN,IAAI,CAACM,KAAD,CAAJ,GAAcF,KAAd;QACAL,UAAU,CAACS,IAAX,CAAgBD,QAAhB;MACH;IACJ,CAXwC,CAAzC;IAYA,OAAO,MAAM;MACTP,IAAI,GAAG,IAAP;IACH,CAFD;EAGH,CAlBQ,CAHb;AAsBH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}