const express = require('express');
const router = express.Router();
const User = require('../models/User');
const AuthMiddleware = require('../middleware/auth');

// GET /users - Get all users
router.get('/', async (req, res) => {
  try {
    const users = await User.getAllUsers();
    res.json(users);
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ error: 'Failed to fetch users' });
  }
});

// GET /users/online - Get online users
router.get('/online', async (req, res) => {
  try {
    const users = await User.getOnlineUsers();
    res.json(users);
  } catch (error) {
    console.error('Error fetching online users:', error);
    res.status(500).json({ error: 'Failed to fetch online users' });
  }
});

// POST /users - Create or login user
router.post('/', AuthMiddleware.apiAuthMiddleware, async (req, res) => {
  try {
    const { username, password } = req.body;
    
    const user = await User.findOrCreate(username, password);
    await User.setOnlineStatus(user.id, true);
    
    // Set admin status if applicable
    if (req.userAuth.isAdmin) {
      await User.setAdminStatus(user.id, true);
    }
    
    res.json({
      ...user,
      isAdmin: req.userAuth.isAdmin,
      securityInfo: AuthMiddleware.getSecurityInfo()
    });
  } catch (error) {
    console.error('Error creating/logging in user:', error);
    res.status(500).json({ error: 'Failed to create/login user' });
  }
});

// GET /users/security-info - Get security configuration
router.get('/security-info', async (req, res) => {
  try {
    const securityInfo = await AuthMiddleware.getSecurityInfo();
    res.json(securityInfo);
  } catch (error) {
    console.error('Error getting security info:', error);
    res.status(500).json({ error: 'Failed to get security info' });
  }
});

module.exports = router;
