{"ast": null, "code": "import { Observable } from '../Observable';\nimport { innerFrom } from '../observable/innerFrom';\nimport { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber, OperatorSubscriber } from './OperatorSubscriber';\nexport function groupBy(keySelector, elementOrOptions, duration, connector) {\n  return operate((source, subscriber) => {\n    let element;\n\n    if (!elementOrOptions || typeof elementOrOptions === 'function') {\n      element = elementOrOptions;\n    } else {\n      ({\n        duration,\n        element,\n        connector\n      } = elementOrOptions);\n    }\n\n    const groups = new Map();\n\n    const notify = cb => {\n      groups.forEach(cb);\n      cb(subscriber);\n    };\n\n    const handleError = err => notify(consumer => consumer.error(err));\n\n    let activeGroups = 0;\n    let teardownAttempted = false;\n    const groupBySourceSubscriber = new OperatorSubscriber(subscriber, value => {\n      try {\n        const key = keySelector(value);\n        let group = groups.get(key);\n\n        if (!group) {\n          groups.set(key, group = connector ? connector() : new Subject());\n          const grouped = createGroupedObservable(key, group);\n          subscriber.next(grouped);\n\n          if (duration) {\n            const durationSubscriber = createOperatorSubscriber(group, () => {\n              group.complete();\n              durationSubscriber === null || durationSubscriber === void 0 ? void 0 : durationSubscriber.unsubscribe();\n            }, undefined, undefined, () => groups.delete(key));\n            groupBySourceSubscriber.add(innerFrom(duration(grouped)).subscribe(durationSubscriber));\n          }\n        }\n\n        group.next(element ? element(value) : value);\n      } catch (err) {\n        handleError(err);\n      }\n    }, () => notify(consumer => consumer.complete()), handleError, () => groups.clear(), () => {\n      teardownAttempted = true;\n      return activeGroups === 0;\n    });\n    source.subscribe(groupBySourceSubscriber);\n\n    function createGroupedObservable(key, groupSubject) {\n      const result = new Observable(groupSubscriber => {\n        activeGroups++;\n        const innerSub = groupSubject.subscribe(groupSubscriber);\n        return () => {\n          innerSub.unsubscribe();\n          --activeGroups === 0 && teardownAttempted && groupBySourceSubscriber.unsubscribe();\n        };\n      });\n      result.key = key;\n      return result;\n    }\n  });\n}", "map": {"version": 3, "names": ["Observable", "innerFrom", "Subject", "operate", "createOperatorSubscriber", "OperatorSubscriber", "groupBy", "keySelector", "elementOrOptions", "duration", "connector", "source", "subscriber", "element", "groups", "Map", "notify", "cb", "for<PERSON>ach", "handleError", "err", "consumer", "error", "activeGroups", "teardownAttempted", "groupBySourceSubscriber", "value", "key", "group", "get", "set", "grouped", "createGroupedObservable", "next", "durationSubscriber", "complete", "unsubscribe", "undefined", "delete", "add", "subscribe", "clear", "groupSubject", "result", "groupSubscriber", "innerSub"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/rxjs/dist/esm/internal/operators/groupBy.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { innerFrom } from '../observable/innerFrom';\nimport { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber, OperatorSubscriber } from './OperatorSubscriber';\nexport function groupBy(keySelector, elementOrOptions, duration, connector) {\n    return operate((source, subscriber) => {\n        let element;\n        if (!elementOrOptions || typeof elementOrOptions === 'function') {\n            element = elementOrOptions;\n        }\n        else {\n            ({ duration, element, connector } = elementOrOptions);\n        }\n        const groups = new Map();\n        const notify = (cb) => {\n            groups.forEach(cb);\n            cb(subscriber);\n        };\n        const handleError = (err) => notify((consumer) => consumer.error(err));\n        let activeGroups = 0;\n        let teardownAttempted = false;\n        const groupBySourceSubscriber = new OperatorSubscriber(subscriber, (value) => {\n            try {\n                const key = keySelector(value);\n                let group = groups.get(key);\n                if (!group) {\n                    groups.set(key, (group = connector ? connector() : new Subject()));\n                    const grouped = createGroupedObservable(key, group);\n                    subscriber.next(grouped);\n                    if (duration) {\n                        const durationSubscriber = createOperatorSubscriber(group, () => {\n                            group.complete();\n                            durationSubscriber === null || durationSubscriber === void 0 ? void 0 : durationSubscriber.unsubscribe();\n                        }, undefined, undefined, () => groups.delete(key));\n                        groupBySourceSubscriber.add(innerFrom(duration(grouped)).subscribe(durationSubscriber));\n                    }\n                }\n                group.next(element ? element(value) : value);\n            }\n            catch (err) {\n                handleError(err);\n            }\n        }, () => notify((consumer) => consumer.complete()), handleError, () => groups.clear(), () => {\n            teardownAttempted = true;\n            return activeGroups === 0;\n        });\n        source.subscribe(groupBySourceSubscriber);\n        function createGroupedObservable(key, groupSubject) {\n            const result = new Observable((groupSubscriber) => {\n                activeGroups++;\n                const innerSub = groupSubject.subscribe(groupSubscriber);\n                return () => {\n                    innerSub.unsubscribe();\n                    --activeGroups === 0 && teardownAttempted && groupBySourceSubscriber.unsubscribe();\n                };\n            });\n            result.key = key;\n            return result;\n        }\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,SAAT,QAA0B,yBAA1B;AACA,SAASC,OAAT,QAAwB,YAAxB;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,EAAmCC,kBAAnC,QAA6D,sBAA7D;AACA,OAAO,SAASC,OAAT,CAAiBC,WAAjB,EAA8BC,gBAA9B,EAAgDC,QAAhD,EAA0DC,SAA1D,EAAqE;EACxE,OAAOP,OAAO,CAAC,CAACQ,MAAD,EAASC,UAAT,KAAwB;IACnC,IAAIC,OAAJ;;IACA,IAAI,CAACL,gBAAD,IAAqB,OAAOA,gBAAP,KAA4B,UAArD,EAAiE;MAC7DK,OAAO,GAAGL,gBAAV;IACH,CAFD,MAGK;MACD,CAAC;QAAEC,QAAF;QAAYI,OAAZ;QAAqBH;MAArB,IAAmCF,gBAApC;IACH;;IACD,MAAMM,MAAM,GAAG,IAAIC,GAAJ,EAAf;;IACA,MAAMC,MAAM,GAAIC,EAAD,IAAQ;MACnBH,MAAM,CAACI,OAAP,CAAeD,EAAf;MACAA,EAAE,CAACL,UAAD,CAAF;IACH,CAHD;;IAIA,MAAMO,WAAW,GAAIC,GAAD,IAASJ,MAAM,CAAEK,QAAD,IAAcA,QAAQ,CAACC,KAAT,CAAeF,GAAf,CAAf,CAAnC;;IACA,IAAIG,YAAY,GAAG,CAAnB;IACA,IAAIC,iBAAiB,GAAG,KAAxB;IACA,MAAMC,uBAAuB,GAAG,IAAIpB,kBAAJ,CAAuBO,UAAvB,EAAoCc,KAAD,IAAW;MAC1E,IAAI;QACA,MAAMC,GAAG,GAAGpB,WAAW,CAACmB,KAAD,CAAvB;QACA,IAAIE,KAAK,GAAGd,MAAM,CAACe,GAAP,CAAWF,GAAX,CAAZ;;QACA,IAAI,CAACC,KAAL,EAAY;UACRd,MAAM,CAACgB,GAAP,CAAWH,GAAX,EAAiBC,KAAK,GAAGlB,SAAS,GAAGA,SAAS,EAAZ,GAAiB,IAAIR,OAAJ,EAAnD;UACA,MAAM6B,OAAO,GAAGC,uBAAuB,CAACL,GAAD,EAAMC,KAAN,CAAvC;UACAhB,UAAU,CAACqB,IAAX,CAAgBF,OAAhB;;UACA,IAAItB,QAAJ,EAAc;YACV,MAAMyB,kBAAkB,GAAG9B,wBAAwB,CAACwB,KAAD,EAAQ,MAAM;cAC7DA,KAAK,CAACO,QAAN;cACAD,kBAAkB,KAAK,IAAvB,IAA+BA,kBAAkB,KAAK,KAAK,CAA3D,GAA+D,KAAK,CAApE,GAAwEA,kBAAkB,CAACE,WAAnB,EAAxE;YACH,CAHkD,EAGhDC,SAHgD,EAGrCA,SAHqC,EAG1B,MAAMvB,MAAM,CAACwB,MAAP,CAAcX,GAAd,CAHoB,CAAnD;YAIAF,uBAAuB,CAACc,GAAxB,CAA4BtC,SAAS,CAACQ,QAAQ,CAACsB,OAAD,CAAT,CAAT,CAA6BS,SAA7B,CAAuCN,kBAAvC,CAA5B;UACH;QACJ;;QACDN,KAAK,CAACK,IAAN,CAAWpB,OAAO,GAAGA,OAAO,CAACa,KAAD,CAAV,GAAoBA,KAAtC;MACH,CAhBD,CAiBA,OAAON,GAAP,EAAY;QACRD,WAAW,CAACC,GAAD,CAAX;MACH;IACJ,CArB+B,EAqB7B,MAAMJ,MAAM,CAAEK,QAAD,IAAcA,QAAQ,CAACc,QAAT,EAAf,CArBiB,EAqBoBhB,WArBpB,EAqBiC,MAAML,MAAM,CAAC2B,KAAP,EArBvC,EAqBuD,MAAM;MACzFjB,iBAAiB,GAAG,IAApB;MACA,OAAOD,YAAY,KAAK,CAAxB;IACH,CAxB+B,CAAhC;IAyBAZ,MAAM,CAAC6B,SAAP,CAAiBf,uBAAjB;;IACA,SAASO,uBAAT,CAAiCL,GAAjC,EAAsCe,YAAtC,EAAoD;MAChD,MAAMC,MAAM,GAAG,IAAI3C,UAAJ,CAAgB4C,eAAD,IAAqB;QAC/CrB,YAAY;QACZ,MAAMsB,QAAQ,GAAGH,YAAY,CAACF,SAAb,CAAuBI,eAAvB,CAAjB;QACA,OAAO,MAAM;UACTC,QAAQ,CAACT,WAAT;UACA,EAAEb,YAAF,KAAmB,CAAnB,IAAwBC,iBAAxB,IAA6CC,uBAAuB,CAACW,WAAxB,EAA7C;QACH,CAHD;MAIH,CAPc,CAAf;MAQAO,MAAM,CAAChB,GAAP,GAAaA,GAAb;MACA,OAAOgB,MAAP;IACH;EACJ,CAtDa,CAAd;AAuDH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}