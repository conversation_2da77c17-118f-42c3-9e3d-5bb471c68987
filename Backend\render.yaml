services:
  - type: web
    name: chateye-backend
    env: node
    plan: free
    buildCommand: npm install
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: DATABASE_URL
        fromDatabase:
          name: chateye-db
          property: connectionString
      - key: FRONTEND_URL
        value: https://your-frontend-domain.vercel.app

databases:
  - name: chateye-db
    plan: free
    databaseName: chateye
    user: chateye
