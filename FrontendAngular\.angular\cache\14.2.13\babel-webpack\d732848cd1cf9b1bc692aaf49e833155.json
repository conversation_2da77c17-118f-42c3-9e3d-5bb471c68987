{"ast": null, "code": "import { coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i1 from '@angular/cdk/platform';\nimport { _getShadowRoot } from '@angular/cdk/platform';\nimport * as i2 from '@angular/cdk/scrolling';\nimport * as i3 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Component, ChangeDetectionStrategy, ViewEncapsulation, Optional, Inject, Input, NgModule } from '@angular/core';\nimport { mixinColor, MatCommonModule } from '@angular/material/core';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { Subscription } from 'rxjs';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Base reference size of the spinner.\n * @docs-private\n */\n\nfunction MatProgressSpinner__svg_circle_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelement(0, \"circle\", 4);\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n\n    const _r0 = i0.ɵɵreference(1);\n\n    i0.ɵɵstyleProp(\"animation-name\", \"mat-progress-spinner-stroke-rotate-\" + ctx_r1._spinnerAnimationLabel)(\"stroke-dashoffset\", ctx_r1._getStrokeDashOffset(), \"px\")(\"stroke-dasharray\", ctx_r1._getStrokeCircumference(), \"px\")(\"stroke-width\", ctx_r1._getCircleStrokeWidth(), \"%\")(\"transform-origin\", ctx_r1._getCircleTransformOrigin(_r0));\n    i0.ɵɵattribute(\"r\", ctx_r1._getCircleRadius());\n  }\n}\n\nfunction MatProgressSpinner__svg_circle_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelement(0, \"circle\", 4);\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n\n    const _r0 = i0.ɵɵreference(1);\n\n    i0.ɵɵstyleProp(\"stroke-dashoffset\", ctx_r2._getStrokeDashOffset(), \"px\")(\"stroke-dasharray\", ctx_r2._getStrokeCircumference(), \"px\")(\"stroke-width\", ctx_r2._getCircleStrokeWidth(), \"%\")(\"transform-origin\", ctx_r2._getCircleTransformOrigin(_r0));\n    i0.ɵɵattribute(\"r\", ctx_r2._getCircleRadius());\n  }\n}\n\nconst BASE_SIZE = 100;\n/**\n * Base reference stroke width of the spinner.\n * @docs-private\n */\n\nconst BASE_STROKE_WIDTH = 10; // Boilerplate for applying mixins to MatProgressSpinner.\n\n/** @docs-private */\n\nconst _MatProgressSpinnerBase = mixinColor(class {\n  constructor(_elementRef) {\n    this._elementRef = _elementRef;\n  }\n\n}, 'primary');\n/** Injection token to be used to override the default options for `mat-progress-spinner`. */\n\n\nconst MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS = new InjectionToken('mat-progress-spinner-default-options', {\n  providedIn: 'root',\n  factory: MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY\n});\n/** @docs-private */\n\nfunction MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    diameter: BASE_SIZE\n  };\n} // .0001 percentage difference is necessary in order to avoid unwanted animation frames\n// for example because the animation duration is 4 seconds, .1% accounts to 4ms\n// which are enough to see the flicker described in\n// https://github.com/angular/components/issues/8984\n\n\nconst INDETERMINATE_ANIMATION_TEMPLATE = `\n @keyframes mat-progress-spinner-stroke-rotate-DIAMETER {\n    0%      { stroke-dashoffset: START_VALUE;  transform: rotate(0); }\n    12.5%   { stroke-dashoffset: END_VALUE;    transform: rotate(0); }\n    12.5001%  { stroke-dashoffset: END_VALUE;    transform: rotateX(180deg) rotate(72.5deg); }\n    25%     { stroke-dashoffset: START_VALUE;  transform: rotateX(180deg) rotate(72.5deg); }\n\n    25.0001%   { stroke-dashoffset: START_VALUE;  transform: rotate(270deg); }\n    37.5%   { stroke-dashoffset: END_VALUE;    transform: rotate(270deg); }\n    37.5001%  { stroke-dashoffset: END_VALUE;    transform: rotateX(180deg) rotate(161.5deg); }\n    50%     { stroke-dashoffset: START_VALUE;  transform: rotateX(180deg) rotate(161.5deg); }\n\n    50.0001%  { stroke-dashoffset: START_VALUE;  transform: rotate(180deg); }\n    62.5%   { stroke-dashoffset: END_VALUE;    transform: rotate(180deg); }\n    62.5001%  { stroke-dashoffset: END_VALUE;    transform: rotateX(180deg) rotate(251.5deg); }\n    75%     { stroke-dashoffset: START_VALUE;  transform: rotateX(180deg) rotate(251.5deg); }\n\n    75.0001%  { stroke-dashoffset: START_VALUE;  transform: rotate(90deg); }\n    87.5%   { stroke-dashoffset: END_VALUE;    transform: rotate(90deg); }\n    87.5001%  { stroke-dashoffset: END_VALUE;    transform: rotateX(180deg) rotate(341.5deg); }\n    100%    { stroke-dashoffset: START_VALUE;  transform: rotateX(180deg) rotate(341.5deg); }\n  }\n`;\n/**\n * `<mat-progress-spinner>` component.\n */\n\nclass MatProgressSpinner extends _MatProgressSpinnerBase {\n  constructor(elementRef, _platform, _document, animationMode, defaults,\n  /**\n   * @deprecated `changeDetectorRef`, `viewportRuler` and `ngZone`\n   * parameters to become required.\n   * @breaking-change 14.0.0\n   */\n  changeDetectorRef, viewportRuler, ngZone) {\n    super(elementRef);\n    this._document = _document;\n    this._diameter = BASE_SIZE;\n    this._value = 0;\n    this._resizeSubscription = Subscription.EMPTY;\n    /** Mode of the progress circle */\n\n    this.mode = 'determinate';\n    const trackedDiameters = MatProgressSpinner._diameters;\n    this._spinnerAnimationLabel = this._getSpinnerAnimationLabel(); // The base size is already inserted via the component's structural styles. We still\n    // need to track it so we don't end up adding the same styles again.\n\n    if (!trackedDiameters.has(_document.head)) {\n      trackedDiameters.set(_document.head, new Set([BASE_SIZE]));\n    }\n\n    this._noopAnimations = animationMode === 'NoopAnimations' && !!defaults && !defaults._forceAnimations;\n\n    if (elementRef.nativeElement.nodeName.toLowerCase() === 'mat-spinner') {\n      this.mode = 'indeterminate';\n    }\n\n    if (defaults) {\n      if (defaults.color) {\n        this.color = this.defaultColor = defaults.color;\n      }\n\n      if (defaults.diameter) {\n        this.diameter = defaults.diameter;\n      }\n\n      if (defaults.strokeWidth) {\n        this.strokeWidth = defaults.strokeWidth;\n      }\n    } // Safari has an issue where the circle isn't positioned correctly when the page has a\n    // different zoom level from the default. This handler triggers a recalculation of the\n    // `transform-origin` when the page zoom level changes.\n    // See `_getCircleTransformOrigin` for more info.\n    // @breaking-change 14.0.0 Remove null checks for `_changeDetectorRef`,\n    // `viewportRuler` and `ngZone`.\n\n\n    if (_platform.isBrowser && _platform.SAFARI && viewportRuler && changeDetectorRef && ngZone) {\n      this._resizeSubscription = viewportRuler.change(150).subscribe(() => {\n        // When the window is resize while the spinner is in `indeterminate` mode, we\n        // have to mark for check so the transform origin of the circle can be recomputed.\n        if (this.mode === 'indeterminate') {\n          ngZone.run(() => changeDetectorRef.markForCheck());\n        }\n      });\n    }\n  }\n  /** The diameter of the progress spinner (will set width and height of svg). */\n\n\n  get diameter() {\n    return this._diameter;\n  }\n\n  set diameter(size) {\n    this._diameter = coerceNumberProperty(size);\n    this._spinnerAnimationLabel = this._getSpinnerAnimationLabel(); // If this is set before `ngOnInit`, the style root may not have been resolved yet.\n\n    if (this._styleRoot) {\n      this._attachStyleNode();\n    }\n  }\n  /** Stroke width of the progress spinner. */\n\n\n  get strokeWidth() {\n    return this._strokeWidth || this.diameter / 10;\n  }\n\n  set strokeWidth(value) {\n    this._strokeWidth = coerceNumberProperty(value);\n  }\n  /** Value of the progress circle. */\n\n\n  get value() {\n    return this.mode === 'determinate' ? this._value : 0;\n  }\n\n  set value(newValue) {\n    this._value = Math.max(0, Math.min(100, coerceNumberProperty(newValue)));\n  }\n\n  ngOnInit() {\n    const element = this._elementRef.nativeElement; // Note that we need to look up the root node in ngOnInit, rather than the constructor, because\n    // Angular seems to create the element outside the shadow root and then moves it inside, if the\n    // node is inside an `ngIf` and a ShadowDom-encapsulated component.\n\n    this._styleRoot = _getShadowRoot(element) || this._document.head;\n\n    this._attachStyleNode();\n\n    element.classList.add('mat-progress-spinner-indeterminate-animation');\n  }\n\n  ngOnDestroy() {\n    this._resizeSubscription.unsubscribe();\n  }\n  /** The radius of the spinner, adjusted for stroke width. */\n\n\n  _getCircleRadius() {\n    return (this.diameter - BASE_STROKE_WIDTH) / 2;\n  }\n  /** The view box of the spinner's svg element. */\n\n\n  _getViewBox() {\n    const viewBox = this._getCircleRadius() * 2 + this.strokeWidth;\n    return `0 0 ${viewBox} ${viewBox}`;\n  }\n  /** The stroke circumference of the svg circle. */\n\n\n  _getStrokeCircumference() {\n    return 2 * Math.PI * this._getCircleRadius();\n  }\n  /** The dash offset of the svg circle. */\n\n\n  _getStrokeDashOffset() {\n    if (this.mode === 'determinate') {\n      return this._getStrokeCircumference() * (100 - this._value) / 100;\n    }\n\n    return null;\n  }\n  /** Stroke width of the circle in percent. */\n\n\n  _getCircleStrokeWidth() {\n    return this.strokeWidth / this.diameter * 100;\n  }\n  /** Gets the `transform-origin` for the inner circle element. */\n\n\n  _getCircleTransformOrigin(svg) {\n    // Safari has an issue where the `transform-origin` doesn't work as expected when the page\n    // has a different zoom level from the default. The problem appears to be that a zoom\n    // is applied on the `svg` node itself. We can work around it by calculating the origin\n    // based on the zoom level. On all other browsers the `currentScale` appears to always be 1.\n    const scale = (svg.currentScale ?? 1) * 50;\n    return `${scale}% ${scale}%`;\n  }\n  /** Dynamically generates a style tag containing the correct animation for this diameter. */\n\n\n  _attachStyleNode() {\n    const styleRoot = this._styleRoot;\n    const currentDiameter = this._diameter;\n    const diameters = MatProgressSpinner._diameters;\n    let diametersForElement = diameters.get(styleRoot);\n\n    if (!diametersForElement || !diametersForElement.has(currentDiameter)) {\n      const styleTag = this._document.createElement('style');\n\n      styleTag.setAttribute('mat-spinner-animation', this._spinnerAnimationLabel);\n      styleTag.textContent = this._getAnimationText();\n      styleRoot.appendChild(styleTag);\n\n      if (!diametersForElement) {\n        diametersForElement = new Set();\n        diameters.set(styleRoot, diametersForElement);\n      }\n\n      diametersForElement.add(currentDiameter);\n    }\n  }\n  /** Generates animation styles adjusted for the spinner's diameter. */\n\n\n  _getAnimationText() {\n    const strokeCircumference = this._getStrokeCircumference();\n\n    return INDETERMINATE_ANIMATION_TEMPLATE // Animation should begin at 5% and end at 80%\n    .replace(/START_VALUE/g, `${0.95 * strokeCircumference}`).replace(/END_VALUE/g, `${0.2 * strokeCircumference}`).replace(/DIAMETER/g, `${this._spinnerAnimationLabel}`);\n  }\n  /** Returns the circle diameter formatted for use with the animation-name CSS property. */\n\n\n  _getSpinnerAnimationLabel() {\n    // The string of a float point number will include a period ‘.’ character,\n    // which is not valid for a CSS animation-name.\n    return this.diameter.toString().replace('.', '_');\n  }\n\n}\n/**\n * Tracks diameters of existing instances to de-dupe generated styles (default d = 100).\n * We need to keep track of which elements the diameters were attached to, because for\n * elements in the Shadow DOM the style tags are attached to the shadow root, rather\n * than the document head.\n */\n\n\nMatProgressSpinner._diameters = new WeakMap();\n\nMatProgressSpinner.ɵfac = function MatProgressSpinner_Factory(t) {\n  return new (t || MatProgressSpinner)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Platform), i0.ɵɵdirectiveInject(DOCUMENT, 8), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.ViewportRuler), i0.ɵɵdirectiveInject(i0.NgZone));\n};\n\nMatProgressSpinner.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatProgressSpinner,\n  selectors: [[\"mat-progress-spinner\"], [\"mat-spinner\"]],\n  hostAttrs: [\"role\", \"progressbar\", \"tabindex\", \"-1\", 1, \"mat-progress-spinner\", \"mat-spinner\"],\n  hostVars: 10,\n  hostBindings: function MatProgressSpinner_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"aria-valuemin\", ctx.mode === \"determinate\" ? 0 : null)(\"aria-valuemax\", ctx.mode === \"determinate\" ? 100 : null)(\"aria-valuenow\", ctx.mode === \"determinate\" ? ctx.value : null)(\"mode\", ctx.mode);\n      i0.ɵɵstyleProp(\"width\", ctx.diameter, \"px\")(\"height\", ctx.diameter, \"px\");\n      i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._noopAnimations);\n    }\n  },\n  inputs: {\n    color: \"color\",\n    diameter: \"diameter\",\n    strokeWidth: \"strokeWidth\",\n    mode: \"mode\",\n    value: \"value\"\n  },\n  exportAs: [\"matProgressSpinner\"],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  decls: 4,\n  vars: 8,\n  consts: [[\"preserveAspectRatio\", \"xMidYMid meet\", \"focusable\", \"false\", \"aria-hidden\", \"true\", 3, \"ngSwitch\"], [\"svg\", \"\"], [\"cx\", \"50%\", \"cy\", \"50%\", 3, \"animation-name\", \"stroke-dashoffset\", \"stroke-dasharray\", \"stroke-width\", \"transform-origin\", 4, \"ngSwitchCase\"], [\"cx\", \"50%\", \"cy\", \"50%\", 3, \"stroke-dashoffset\", \"stroke-dasharray\", \"stroke-width\", \"transform-origin\", 4, \"ngSwitchCase\"], [\"cx\", \"50%\", \"cy\", \"50%\"]],\n  template: function MatProgressSpinner_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵnamespaceSVG();\n      i0.ɵɵelementStart(0, \"svg\", 0, 1);\n      i0.ɵɵtemplate(2, MatProgressSpinner__svg_circle_2_Template, 1, 11, \"circle\", 2);\n      i0.ɵɵtemplate(3, MatProgressSpinner__svg_circle_3_Template, 1, 9, \"circle\", 3);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵstyleProp(\"width\", ctx.diameter, \"px\")(\"height\", ctx.diameter, \"px\");\n      i0.ɵɵproperty(\"ngSwitch\", ctx.mode === \"indeterminate\");\n      i0.ɵɵattribute(\"viewBox\", ctx._getViewBox());\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngSwitchCase\", true);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngSwitchCase\", false);\n    }\n  },\n  dependencies: [i3.NgSwitch, i3.NgSwitchCase],\n  styles: [\".mat-progress-spinner{display:block;position:relative;overflow:hidden}.mat-progress-spinner svg{position:absolute;transform:rotate(-90deg);top:0;left:0;transform-origin:center;overflow:visible}.mat-progress-spinner circle{fill:rgba(0,0,0,0);transition:stroke-dashoffset 225ms linear}.cdk-high-contrast-active .mat-progress-spinner circle{stroke:CanvasText}.mat-progress-spinner[mode=indeterminate] svg{animation:mat-progress-spinner-linear-rotate 2000ms linear infinite}.mat-progress-spinner[mode=indeterminate] circle{transition-property:stroke;animation-duration:4000ms;animation-timing-function:cubic-bezier(0.35, 0, 0.25, 1);animation-iteration-count:infinite}.mat-progress-spinner._mat-animation-noopable svg,.mat-progress-spinner._mat-animation-noopable circle{animation:none;transition:none}@keyframes mat-progress-spinner-linear-rotate{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes mat-progress-spinner-stroke-rotate-100{0%{stroke-dashoffset:268.606171575px;transform:rotate(0)}12.5%{stroke-dashoffset:56.5486677px;transform:rotate(0)}12.5001%{stroke-dashoffset:56.5486677px;transform:rotateX(180deg) rotate(72.5deg)}25%{stroke-dashoffset:268.606171575px;transform:rotateX(180deg) rotate(72.5deg)}25.0001%{stroke-dashoffset:268.606171575px;transform:rotate(270deg)}37.5%{stroke-dashoffset:56.5486677px;transform:rotate(270deg)}37.5001%{stroke-dashoffset:56.5486677px;transform:rotateX(180deg) rotate(161.5deg)}50%{stroke-dashoffset:268.606171575px;transform:rotateX(180deg) rotate(161.5deg)}50.0001%{stroke-dashoffset:268.606171575px;transform:rotate(180deg)}62.5%{stroke-dashoffset:56.5486677px;transform:rotate(180deg)}62.5001%{stroke-dashoffset:56.5486677px;transform:rotateX(180deg) rotate(251.5deg)}75%{stroke-dashoffset:268.606171575px;transform:rotateX(180deg) rotate(251.5deg)}75.0001%{stroke-dashoffset:268.606171575px;transform:rotate(90deg)}87.5%{stroke-dashoffset:56.5486677px;transform:rotate(90deg)}87.5001%{stroke-dashoffset:56.5486677px;transform:rotateX(180deg) rotate(341.5deg)}100%{stroke-dashoffset:268.606171575px;transform:rotateX(180deg) rotate(341.5deg)}}\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatProgressSpinner, [{\n    type: Component,\n    args: [{\n      selector: 'mat-progress-spinner, mat-spinner',\n      exportAs: 'matProgressSpinner',\n      host: {\n        'role': 'progressbar',\n        // `mat-spinner` is here for backward compatibility.\n        'class': 'mat-progress-spinner mat-spinner',\n        // set tab index to -1 so screen readers will read the aria-label\n        // Note: there is a known issue with JAWS that does not read progressbar aria labels on FireFox\n        'tabindex': '-1',\n        '[class._mat-animation-noopable]': `_noopAnimations`,\n        '[style.width.px]': 'diameter',\n        '[style.height.px]': 'diameter',\n        '[attr.aria-valuemin]': 'mode === \"determinate\" ? 0 : null',\n        '[attr.aria-valuemax]': 'mode === \"determinate\" ? 100 : null',\n        '[attr.aria-valuenow]': 'mode === \"determinate\" ? value : null',\n        '[attr.mode]': 'mode'\n      },\n      inputs: ['color'],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: \"<!--\\n  preserveAspectRatio of xMidYMid meet as the center of the viewport is the circle's\\n  center. The center of the circle will remain at the center of the mat-progress-spinner\\n  element containing the SVG.\\n-->\\n<!--\\n  All children need to be hidden for screen readers in order to support ChromeVox.\\n  More context in the issue: https://github.com/angular/components/issues/22165.\\n-->\\n<svg\\n  [style.width.px]=\\\"diameter\\\"\\n  [style.height.px]=\\\"diameter\\\"\\n  [attr.viewBox]=\\\"_getViewBox()\\\"\\n  preserveAspectRatio=\\\"xMidYMid meet\\\"\\n  focusable=\\\"false\\\"\\n  [ngSwitch]=\\\"mode === 'indeterminate'\\\"\\n  aria-hidden=\\\"true\\\"\\n  #svg>\\n\\n  <!--\\n    Technically we can reuse the same `circle` element, however Safari has an issue that breaks\\n    the SVG rendering in determinate mode, after switching between indeterminate and determinate.\\n    Using a different element avoids the issue. An alternative to this is adding `display: none`\\n    for a split second and then removing it when switching between modes, but it's hard to know\\n    for how long to hide the element and it can cause the UI to blink.\\n  -->\\n  <circle\\n    *ngSwitchCase=\\\"true\\\"\\n    cx=\\\"50%\\\"\\n    cy=\\\"50%\\\"\\n    [attr.r]=\\\"_getCircleRadius()\\\"\\n    [style.animation-name]=\\\"'mat-progress-spinner-stroke-rotate-' + _spinnerAnimationLabel\\\"\\n    [style.stroke-dashoffset.px]=\\\"_getStrokeDashOffset()\\\"\\n    [style.stroke-dasharray.px]=\\\"_getStrokeCircumference()\\\"\\n    [style.stroke-width.%]=\\\"_getCircleStrokeWidth()\\\"\\n    [style.transform-origin]=\\\"_getCircleTransformOrigin(svg)\\\"></circle>\\n\\n  <circle\\n    *ngSwitchCase=\\\"false\\\"\\n    cx=\\\"50%\\\"\\n    cy=\\\"50%\\\"\\n    [attr.r]=\\\"_getCircleRadius()\\\"\\n    [style.stroke-dashoffset.px]=\\\"_getStrokeDashOffset()\\\"\\n    [style.stroke-dasharray.px]=\\\"_getStrokeCircumference()\\\"\\n    [style.stroke-width.%]=\\\"_getCircleStrokeWidth()\\\"\\n    [style.transform-origin]=\\\"_getCircleTransformOrigin(svg)\\\"></circle>\\n</svg>\\n\",\n      styles: [\".mat-progress-spinner{display:block;position:relative;overflow:hidden}.mat-progress-spinner svg{position:absolute;transform:rotate(-90deg);top:0;left:0;transform-origin:center;overflow:visible}.mat-progress-spinner circle{fill:rgba(0,0,0,0);transition:stroke-dashoffset 225ms linear}.cdk-high-contrast-active .mat-progress-spinner circle{stroke:CanvasText}.mat-progress-spinner[mode=indeterminate] svg{animation:mat-progress-spinner-linear-rotate 2000ms linear infinite}.mat-progress-spinner[mode=indeterminate] circle{transition-property:stroke;animation-duration:4000ms;animation-timing-function:cubic-bezier(0.35, 0, 0.25, 1);animation-iteration-count:infinite}.mat-progress-spinner._mat-animation-noopable svg,.mat-progress-spinner._mat-animation-noopable circle{animation:none;transition:none}@keyframes mat-progress-spinner-linear-rotate{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes mat-progress-spinner-stroke-rotate-100{0%{stroke-dashoffset:268.606171575px;transform:rotate(0)}12.5%{stroke-dashoffset:56.5486677px;transform:rotate(0)}12.5001%{stroke-dashoffset:56.5486677px;transform:rotateX(180deg) rotate(72.5deg)}25%{stroke-dashoffset:268.606171575px;transform:rotateX(180deg) rotate(72.5deg)}25.0001%{stroke-dashoffset:268.606171575px;transform:rotate(270deg)}37.5%{stroke-dashoffset:56.5486677px;transform:rotate(270deg)}37.5001%{stroke-dashoffset:56.5486677px;transform:rotateX(180deg) rotate(161.5deg)}50%{stroke-dashoffset:268.606171575px;transform:rotateX(180deg) rotate(161.5deg)}50.0001%{stroke-dashoffset:268.606171575px;transform:rotate(180deg)}62.5%{stroke-dashoffset:56.5486677px;transform:rotate(180deg)}62.5001%{stroke-dashoffset:56.5486677px;transform:rotateX(180deg) rotate(251.5deg)}75%{stroke-dashoffset:268.606171575px;transform:rotateX(180deg) rotate(251.5deg)}75.0001%{stroke-dashoffset:268.606171575px;transform:rotate(90deg)}87.5%{stroke-dashoffset:56.5486677px;transform:rotate(90deg)}87.5001%{stroke-dashoffset:56.5486677px;transform:rotateX(180deg) rotate(341.5deg)}100%{stroke-dashoffset:268.606171575px;transform:rotateX(180deg) rotate(341.5deg)}}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.Platform\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS]\n      }]\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i2.ViewportRuler\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    diameter: [{\n      type: Input\n    }],\n    strokeWidth: [{\n      type: Input\n    }],\n    mode: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass MatProgressSpinnerModule {}\n\nMatProgressSpinnerModule.ɵfac = function MatProgressSpinnerModule_Factory(t) {\n  return new (t || MatProgressSpinnerModule)();\n};\n\nMatProgressSpinnerModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatProgressSpinnerModule\n});\nMatProgressSpinnerModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [MatCommonModule, CommonModule, MatCommonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatProgressSpinnerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, CommonModule],\n      exports: [MatProgressSpinner, MatCommonModule],\n      declarations: [MatProgressSpinner]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @deprecated Import `MatProgressSpinner` instead. Note that the\n *    `mat-spinner` selector isn't deprecated.\n * @breaking-change 8.0.0\n */\n// tslint:disable-next-line:variable-name\n\n\nconst MatSpinner = MatProgressSpinner;\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS, MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY, MatProgressSpinner, MatProgressSpinnerModule, MatSpinner };", "map": {"version": 3, "names": ["coerceNumberProperty", "i1", "_getShadowRoot", "i2", "i3", "DOCUMENT", "CommonModule", "i0", "InjectionToken", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Optional", "Inject", "Input", "NgModule", "mixinColor", "MatCommonModule", "ANIMATION_MODULE_TYPE", "Subscription", "BASE_SIZE", "BASE_STROKE_WIDTH", "_MatProgressSpinnerBase", "constructor", "_elementRef", "MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS", "providedIn", "factory", "MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY", "diameter", "INDETERMINATE_ANIMATION_TEMPLATE", "MatProgressSpinner", "elementRef", "_platform", "_document", "animationMode", "defaults", "changeDetectorRef", "viewportRuler", "ngZone", "_diameter", "_value", "_resizeSubscription", "EMPTY", "mode", "trackedDiameters", "_diameters", "_spinnerAnimationLabel", "_getSpinnerAnimationLabel", "has", "head", "set", "Set", "_noopAnimations", "_forceAnimations", "nativeElement", "nodeName", "toLowerCase", "color", "defaultColor", "strokeWidth", "<PERSON><PERSON><PERSON><PERSON>", "SAFARI", "change", "subscribe", "run", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "size", "_styleRoot", "_attachStyleNode", "_strokeWidth", "value", "newValue", "Math", "max", "min", "ngOnInit", "element", "classList", "add", "ngOnDestroy", "unsubscribe", "_getCircleRadius", "_getViewBox", "viewBox", "_getStrokeCircumference", "PI", "_getStrokeDashOffset", "_getCircleStrokeWidth", "_getCircleTransformOrigin", "svg", "scale", "currentScale", "styleRoot", "currentDiameter", "diameters", "diametersForElement", "get", "styleTag", "createElement", "setAttribute", "textContent", "_getAnimationText", "append<PERSON><PERSON><PERSON>", "strokeCircumference", "replace", "toString", "WeakMap", "ɵfac", "ElementRef", "Platform", "ChangeDetectorRef", "ViewportRuler", "NgZone", "ɵcmp", "NgSwitch", "NgSwitchCase", "type", "args", "selector", "exportAs", "host", "inputs", "changeDetection", "OnPush", "encapsulation", "None", "template", "styles", "undefined", "decorators", "MatProgressSpinnerModule", "ɵmod", "ɵinj", "imports", "exports", "declarations", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["R:/chateye/FrontendAngular/node_modules/@angular/material/fesm2020/progress-spinner.mjs"], "sourcesContent": ["import { coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i1 from '@angular/cdk/platform';\nimport { _getShadowRoot } from '@angular/cdk/platform';\nimport * as i2 from '@angular/cdk/scrolling';\nimport * as i3 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Component, ChangeDetectionStrategy, ViewEncapsulation, Optional, Inject, Input, NgModule } from '@angular/core';\nimport { mixinColor, MatCommonModule } from '@angular/material/core';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { Subscription } from 'rxjs';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Base reference size of the spinner.\n * @docs-private\n */\nconst BASE_SIZE = 100;\n/**\n * Base reference stroke width of the spinner.\n * @docs-private\n */\nconst BASE_STROKE_WIDTH = 10;\n// Boilerplate for applying mixins to MatProgressSpinner.\n/** @docs-private */\nconst _MatProgressSpinnerBase = mixinColor(class {\n    constructor(_elementRef) {\n        this._elementRef = _elementRef;\n    }\n}, 'primary');\n/** Injection token to be used to override the default options for `mat-progress-spinner`. */\nconst MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS = new InjectionToken('mat-progress-spinner-default-options', {\n    providedIn: 'root',\n    factory: MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY,\n});\n/** @docs-private */\nfunction MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY() {\n    return { diameter: BASE_SIZE };\n}\n// .0001 percentage difference is necessary in order to avoid unwanted animation frames\n// for example because the animation duration is 4 seconds, .1% accounts to 4ms\n// which are enough to see the flicker described in\n// https://github.com/angular/components/issues/8984\nconst INDETERMINATE_ANIMATION_TEMPLATE = `\n @keyframes mat-progress-spinner-stroke-rotate-DIAMETER {\n    0%      { stroke-dashoffset: START_VALUE;  transform: rotate(0); }\n    12.5%   { stroke-dashoffset: END_VALUE;    transform: rotate(0); }\n    12.5001%  { stroke-dashoffset: END_VALUE;    transform: rotateX(180deg) rotate(72.5deg); }\n    25%     { stroke-dashoffset: START_VALUE;  transform: rotateX(180deg) rotate(72.5deg); }\n\n    25.0001%   { stroke-dashoffset: START_VALUE;  transform: rotate(270deg); }\n    37.5%   { stroke-dashoffset: END_VALUE;    transform: rotate(270deg); }\n    37.5001%  { stroke-dashoffset: END_VALUE;    transform: rotateX(180deg) rotate(161.5deg); }\n    50%     { stroke-dashoffset: START_VALUE;  transform: rotateX(180deg) rotate(161.5deg); }\n\n    50.0001%  { stroke-dashoffset: START_VALUE;  transform: rotate(180deg); }\n    62.5%   { stroke-dashoffset: END_VALUE;    transform: rotate(180deg); }\n    62.5001%  { stroke-dashoffset: END_VALUE;    transform: rotateX(180deg) rotate(251.5deg); }\n    75%     { stroke-dashoffset: START_VALUE;  transform: rotateX(180deg) rotate(251.5deg); }\n\n    75.0001%  { stroke-dashoffset: START_VALUE;  transform: rotate(90deg); }\n    87.5%   { stroke-dashoffset: END_VALUE;    transform: rotate(90deg); }\n    87.5001%  { stroke-dashoffset: END_VALUE;    transform: rotateX(180deg) rotate(341.5deg); }\n    100%    { stroke-dashoffset: START_VALUE;  transform: rotateX(180deg) rotate(341.5deg); }\n  }\n`;\n/**\n * `<mat-progress-spinner>` component.\n */\nclass MatProgressSpinner extends _MatProgressSpinnerBase {\n    constructor(elementRef, _platform, _document, animationMode, defaults, \n    /**\n     * @deprecated `changeDetectorRef`, `viewportRuler` and `ngZone`\n     * parameters to become required.\n     * @breaking-change 14.0.0\n     */\n    changeDetectorRef, viewportRuler, ngZone) {\n        super(elementRef);\n        this._document = _document;\n        this._diameter = BASE_SIZE;\n        this._value = 0;\n        this._resizeSubscription = Subscription.EMPTY;\n        /** Mode of the progress circle */\n        this.mode = 'determinate';\n        const trackedDiameters = MatProgressSpinner._diameters;\n        this._spinnerAnimationLabel = this._getSpinnerAnimationLabel();\n        // The base size is already inserted via the component's structural styles. We still\n        // need to track it so we don't end up adding the same styles again.\n        if (!trackedDiameters.has(_document.head)) {\n            trackedDiameters.set(_document.head, new Set([BASE_SIZE]));\n        }\n        this._noopAnimations =\n            animationMode === 'NoopAnimations' && !!defaults && !defaults._forceAnimations;\n        if (elementRef.nativeElement.nodeName.toLowerCase() === 'mat-spinner') {\n            this.mode = 'indeterminate';\n        }\n        if (defaults) {\n            if (defaults.color) {\n                this.color = this.defaultColor = defaults.color;\n            }\n            if (defaults.diameter) {\n                this.diameter = defaults.diameter;\n            }\n            if (defaults.strokeWidth) {\n                this.strokeWidth = defaults.strokeWidth;\n            }\n        }\n        // Safari has an issue where the circle isn't positioned correctly when the page has a\n        // different zoom level from the default. This handler triggers a recalculation of the\n        // `transform-origin` when the page zoom level changes.\n        // See `_getCircleTransformOrigin` for more info.\n        // @breaking-change 14.0.0 Remove null checks for `_changeDetectorRef`,\n        // `viewportRuler` and `ngZone`.\n        if (_platform.isBrowser && _platform.SAFARI && viewportRuler && changeDetectorRef && ngZone) {\n            this._resizeSubscription = viewportRuler.change(150).subscribe(() => {\n                // When the window is resize while the spinner is in `indeterminate` mode, we\n                // have to mark for check so the transform origin of the circle can be recomputed.\n                if (this.mode === 'indeterminate') {\n                    ngZone.run(() => changeDetectorRef.markForCheck());\n                }\n            });\n        }\n    }\n    /** The diameter of the progress spinner (will set width and height of svg). */\n    get diameter() {\n        return this._diameter;\n    }\n    set diameter(size) {\n        this._diameter = coerceNumberProperty(size);\n        this._spinnerAnimationLabel = this._getSpinnerAnimationLabel();\n        // If this is set before `ngOnInit`, the style root may not have been resolved yet.\n        if (this._styleRoot) {\n            this._attachStyleNode();\n        }\n    }\n    /** Stroke width of the progress spinner. */\n    get strokeWidth() {\n        return this._strokeWidth || this.diameter / 10;\n    }\n    set strokeWidth(value) {\n        this._strokeWidth = coerceNumberProperty(value);\n    }\n    /** Value of the progress circle. */\n    get value() {\n        return this.mode === 'determinate' ? this._value : 0;\n    }\n    set value(newValue) {\n        this._value = Math.max(0, Math.min(100, coerceNumberProperty(newValue)));\n    }\n    ngOnInit() {\n        const element = this._elementRef.nativeElement;\n        // Note that we need to look up the root node in ngOnInit, rather than the constructor, because\n        // Angular seems to create the element outside the shadow root and then moves it inside, if the\n        // node is inside an `ngIf` and a ShadowDom-encapsulated component.\n        this._styleRoot = _getShadowRoot(element) || this._document.head;\n        this._attachStyleNode();\n        element.classList.add('mat-progress-spinner-indeterminate-animation');\n    }\n    ngOnDestroy() {\n        this._resizeSubscription.unsubscribe();\n    }\n    /** The radius of the spinner, adjusted for stroke width. */\n    _getCircleRadius() {\n        return (this.diameter - BASE_STROKE_WIDTH) / 2;\n    }\n    /** The view box of the spinner's svg element. */\n    _getViewBox() {\n        const viewBox = this._getCircleRadius() * 2 + this.strokeWidth;\n        return `0 0 ${viewBox} ${viewBox}`;\n    }\n    /** The stroke circumference of the svg circle. */\n    _getStrokeCircumference() {\n        return 2 * Math.PI * this._getCircleRadius();\n    }\n    /** The dash offset of the svg circle. */\n    _getStrokeDashOffset() {\n        if (this.mode === 'determinate') {\n            return (this._getStrokeCircumference() * (100 - this._value)) / 100;\n        }\n        return null;\n    }\n    /** Stroke width of the circle in percent. */\n    _getCircleStrokeWidth() {\n        return (this.strokeWidth / this.diameter) * 100;\n    }\n    /** Gets the `transform-origin` for the inner circle element. */\n    _getCircleTransformOrigin(svg) {\n        // Safari has an issue where the `transform-origin` doesn't work as expected when the page\n        // has a different zoom level from the default. The problem appears to be that a zoom\n        // is applied on the `svg` node itself. We can work around it by calculating the origin\n        // based on the zoom level. On all other browsers the `currentScale` appears to always be 1.\n        const scale = (svg.currentScale ?? 1) * 50;\n        return `${scale}% ${scale}%`;\n    }\n    /** Dynamically generates a style tag containing the correct animation for this diameter. */\n    _attachStyleNode() {\n        const styleRoot = this._styleRoot;\n        const currentDiameter = this._diameter;\n        const diameters = MatProgressSpinner._diameters;\n        let diametersForElement = diameters.get(styleRoot);\n        if (!diametersForElement || !diametersForElement.has(currentDiameter)) {\n            const styleTag = this._document.createElement('style');\n            styleTag.setAttribute('mat-spinner-animation', this._spinnerAnimationLabel);\n            styleTag.textContent = this._getAnimationText();\n            styleRoot.appendChild(styleTag);\n            if (!diametersForElement) {\n                diametersForElement = new Set();\n                diameters.set(styleRoot, diametersForElement);\n            }\n            diametersForElement.add(currentDiameter);\n        }\n    }\n    /** Generates animation styles adjusted for the spinner's diameter. */\n    _getAnimationText() {\n        const strokeCircumference = this._getStrokeCircumference();\n        return (INDETERMINATE_ANIMATION_TEMPLATE\n            // Animation should begin at 5% and end at 80%\n            .replace(/START_VALUE/g, `${0.95 * strokeCircumference}`)\n            .replace(/END_VALUE/g, `${0.2 * strokeCircumference}`)\n            .replace(/DIAMETER/g, `${this._spinnerAnimationLabel}`));\n    }\n    /** Returns the circle diameter formatted for use with the animation-name CSS property. */\n    _getSpinnerAnimationLabel() {\n        // The string of a float point number will include a period ‘.’ character,\n        // which is not valid for a CSS animation-name.\n        return this.diameter.toString().replace('.', '_');\n    }\n}\n/**\n * Tracks diameters of existing instances to de-dupe generated styles (default d = 100).\n * We need to keep track of which elements the diameters were attached to, because for\n * elements in the Shadow DOM the style tags are attached to the shadow root, rather\n * than the document head.\n */\nMatProgressSpinner._diameters = new WeakMap();\nMatProgressSpinner.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatProgressSpinner, deps: [{ token: i0.ElementRef }, { token: i1.Platform }, { token: DOCUMENT, optional: true }, { token: ANIMATION_MODULE_TYPE, optional: true }, { token: MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS }, { token: i0.ChangeDetectorRef }, { token: i2.ViewportRuler }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Component });\nMatProgressSpinner.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatProgressSpinner, selector: \"mat-progress-spinner, mat-spinner\", inputs: { color: \"color\", diameter: \"diameter\", strokeWidth: \"strokeWidth\", mode: \"mode\", value: \"value\" }, host: { attributes: { \"role\": \"progressbar\", \"tabindex\": \"-1\" }, properties: { \"class._mat-animation-noopable\": \"_noopAnimations\", \"style.width.px\": \"diameter\", \"style.height.px\": \"diameter\", \"attr.aria-valuemin\": \"mode === \\\"determinate\\\" ? 0 : null\", \"attr.aria-valuemax\": \"mode === \\\"determinate\\\" ? 100 : null\", \"attr.aria-valuenow\": \"mode === \\\"determinate\\\" ? value : null\", \"attr.mode\": \"mode\" }, classAttribute: \"mat-progress-spinner mat-spinner\" }, exportAs: [\"matProgressSpinner\"], usesInheritance: true, ngImport: i0, template: \"<!--\\n  preserveAspectRatio of xMidYMid meet as the center of the viewport is the circle's\\n  center. The center of the circle will remain at the center of the mat-progress-spinner\\n  element containing the SVG.\\n-->\\n<!--\\n  All children need to be hidden for screen readers in order to support ChromeVox.\\n  More context in the issue: https://github.com/angular/components/issues/22165.\\n-->\\n<svg\\n  [style.width.px]=\\\"diameter\\\"\\n  [style.height.px]=\\\"diameter\\\"\\n  [attr.viewBox]=\\\"_getViewBox()\\\"\\n  preserveAspectRatio=\\\"xMidYMid meet\\\"\\n  focusable=\\\"false\\\"\\n  [ngSwitch]=\\\"mode === 'indeterminate'\\\"\\n  aria-hidden=\\\"true\\\"\\n  #svg>\\n\\n  <!--\\n    Technically we can reuse the same `circle` element, however Safari has an issue that breaks\\n    the SVG rendering in determinate mode, after switching between indeterminate and determinate.\\n    Using a different element avoids the issue. An alternative to this is adding `display: none`\\n    for a split second and then removing it when switching between modes, but it's hard to know\\n    for how long to hide the element and it can cause the UI to blink.\\n  -->\\n  <circle\\n    *ngSwitchCase=\\\"true\\\"\\n    cx=\\\"50%\\\"\\n    cy=\\\"50%\\\"\\n    [attr.r]=\\\"_getCircleRadius()\\\"\\n    [style.animation-name]=\\\"'mat-progress-spinner-stroke-rotate-' + _spinnerAnimationLabel\\\"\\n    [style.stroke-dashoffset.px]=\\\"_getStrokeDashOffset()\\\"\\n    [style.stroke-dasharray.px]=\\\"_getStrokeCircumference()\\\"\\n    [style.stroke-width.%]=\\\"_getCircleStrokeWidth()\\\"\\n    [style.transform-origin]=\\\"_getCircleTransformOrigin(svg)\\\"></circle>\\n\\n  <circle\\n    *ngSwitchCase=\\\"false\\\"\\n    cx=\\\"50%\\\"\\n    cy=\\\"50%\\\"\\n    [attr.r]=\\\"_getCircleRadius()\\\"\\n    [style.stroke-dashoffset.px]=\\\"_getStrokeDashOffset()\\\"\\n    [style.stroke-dasharray.px]=\\\"_getStrokeCircumference()\\\"\\n    [style.stroke-width.%]=\\\"_getCircleStrokeWidth()\\\"\\n    [style.transform-origin]=\\\"_getCircleTransformOrigin(svg)\\\"></circle>\\n</svg>\\n\", styles: [\".mat-progress-spinner{display:block;position:relative;overflow:hidden}.mat-progress-spinner svg{position:absolute;transform:rotate(-90deg);top:0;left:0;transform-origin:center;overflow:visible}.mat-progress-spinner circle{fill:rgba(0,0,0,0);transition:stroke-dashoffset 225ms linear}.cdk-high-contrast-active .mat-progress-spinner circle{stroke:CanvasText}.mat-progress-spinner[mode=indeterminate] svg{animation:mat-progress-spinner-linear-rotate 2000ms linear infinite}.mat-progress-spinner[mode=indeterminate] circle{transition-property:stroke;animation-duration:4000ms;animation-timing-function:cubic-bezier(0.35, 0, 0.25, 1);animation-iteration-count:infinite}.mat-progress-spinner._mat-animation-noopable svg,.mat-progress-spinner._mat-animation-noopable circle{animation:none;transition:none}@keyframes mat-progress-spinner-linear-rotate{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes mat-progress-spinner-stroke-rotate-100{0%{stroke-dashoffset:268.606171575px;transform:rotate(0)}12.5%{stroke-dashoffset:56.5486677px;transform:rotate(0)}12.5001%{stroke-dashoffset:56.5486677px;transform:rotateX(180deg) rotate(72.5deg)}25%{stroke-dashoffset:268.606171575px;transform:rotateX(180deg) rotate(72.5deg)}25.0001%{stroke-dashoffset:268.606171575px;transform:rotate(270deg)}37.5%{stroke-dashoffset:56.5486677px;transform:rotate(270deg)}37.5001%{stroke-dashoffset:56.5486677px;transform:rotateX(180deg) rotate(161.5deg)}50%{stroke-dashoffset:268.606171575px;transform:rotateX(180deg) rotate(161.5deg)}50.0001%{stroke-dashoffset:268.606171575px;transform:rotate(180deg)}62.5%{stroke-dashoffset:56.5486677px;transform:rotate(180deg)}62.5001%{stroke-dashoffset:56.5486677px;transform:rotateX(180deg) rotate(251.5deg)}75%{stroke-dashoffset:268.606171575px;transform:rotateX(180deg) rotate(251.5deg)}75.0001%{stroke-dashoffset:268.606171575px;transform:rotate(90deg)}87.5%{stroke-dashoffset:56.5486677px;transform:rotate(90deg)}87.5001%{stroke-dashoffset:56.5486677px;transform:rotateX(180deg) rotate(341.5deg)}100%{stroke-dashoffset:268.606171575px;transform:rotateX(180deg) rotate(341.5deg)}}\"], dependencies: [{ kind: \"directive\", type: i3.NgSwitch, selector: \"[ngSwitch]\", inputs: [\"ngSwitch\"] }, { kind: \"directive\", type: i3.NgSwitchCase, selector: \"[ngSwitchCase]\", inputs: [\"ngSwitchCase\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatProgressSpinner, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-progress-spinner, mat-spinner', exportAs: 'matProgressSpinner', host: {\n                        'role': 'progressbar',\n                        // `mat-spinner` is here for backward compatibility.\n                        'class': 'mat-progress-spinner mat-spinner',\n                        // set tab index to -1 so screen readers will read the aria-label\n                        // Note: there is a known issue with JAWS that does not read progressbar aria labels on FireFox\n                        'tabindex': '-1',\n                        '[class._mat-animation-noopable]': `_noopAnimations`,\n                        '[style.width.px]': 'diameter',\n                        '[style.height.px]': 'diameter',\n                        '[attr.aria-valuemin]': 'mode === \"determinate\" ? 0 : null',\n                        '[attr.aria-valuemax]': 'mode === \"determinate\" ? 100 : null',\n                        '[attr.aria-valuenow]': 'mode === \"determinate\" ? value : null',\n                        '[attr.mode]': 'mode',\n                    }, inputs: ['color'], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, template: \"<!--\\n  preserveAspectRatio of xMidYMid meet as the center of the viewport is the circle's\\n  center. The center of the circle will remain at the center of the mat-progress-spinner\\n  element containing the SVG.\\n-->\\n<!--\\n  All children need to be hidden for screen readers in order to support ChromeVox.\\n  More context in the issue: https://github.com/angular/components/issues/22165.\\n-->\\n<svg\\n  [style.width.px]=\\\"diameter\\\"\\n  [style.height.px]=\\\"diameter\\\"\\n  [attr.viewBox]=\\\"_getViewBox()\\\"\\n  preserveAspectRatio=\\\"xMidYMid meet\\\"\\n  focusable=\\\"false\\\"\\n  [ngSwitch]=\\\"mode === 'indeterminate'\\\"\\n  aria-hidden=\\\"true\\\"\\n  #svg>\\n\\n  <!--\\n    Technically we can reuse the same `circle` element, however Safari has an issue that breaks\\n    the SVG rendering in determinate mode, after switching between indeterminate and determinate.\\n    Using a different element avoids the issue. An alternative to this is adding `display: none`\\n    for a split second and then removing it when switching between modes, but it's hard to know\\n    for how long to hide the element and it can cause the UI to blink.\\n  -->\\n  <circle\\n    *ngSwitchCase=\\\"true\\\"\\n    cx=\\\"50%\\\"\\n    cy=\\\"50%\\\"\\n    [attr.r]=\\\"_getCircleRadius()\\\"\\n    [style.animation-name]=\\\"'mat-progress-spinner-stroke-rotate-' + _spinnerAnimationLabel\\\"\\n    [style.stroke-dashoffset.px]=\\\"_getStrokeDashOffset()\\\"\\n    [style.stroke-dasharray.px]=\\\"_getStrokeCircumference()\\\"\\n    [style.stroke-width.%]=\\\"_getCircleStrokeWidth()\\\"\\n    [style.transform-origin]=\\\"_getCircleTransformOrigin(svg)\\\"></circle>\\n\\n  <circle\\n    *ngSwitchCase=\\\"false\\\"\\n    cx=\\\"50%\\\"\\n    cy=\\\"50%\\\"\\n    [attr.r]=\\\"_getCircleRadius()\\\"\\n    [style.stroke-dashoffset.px]=\\\"_getStrokeDashOffset()\\\"\\n    [style.stroke-dasharray.px]=\\\"_getStrokeCircumference()\\\"\\n    [style.stroke-width.%]=\\\"_getCircleStrokeWidth()\\\"\\n    [style.transform-origin]=\\\"_getCircleTransformOrigin(svg)\\\"></circle>\\n</svg>\\n\", styles: [\".mat-progress-spinner{display:block;position:relative;overflow:hidden}.mat-progress-spinner svg{position:absolute;transform:rotate(-90deg);top:0;left:0;transform-origin:center;overflow:visible}.mat-progress-spinner circle{fill:rgba(0,0,0,0);transition:stroke-dashoffset 225ms linear}.cdk-high-contrast-active .mat-progress-spinner circle{stroke:CanvasText}.mat-progress-spinner[mode=indeterminate] svg{animation:mat-progress-spinner-linear-rotate 2000ms linear infinite}.mat-progress-spinner[mode=indeterminate] circle{transition-property:stroke;animation-duration:4000ms;animation-timing-function:cubic-bezier(0.35, 0, 0.25, 1);animation-iteration-count:infinite}.mat-progress-spinner._mat-animation-noopable svg,.mat-progress-spinner._mat-animation-noopable circle{animation:none;transition:none}@keyframes mat-progress-spinner-linear-rotate{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes mat-progress-spinner-stroke-rotate-100{0%{stroke-dashoffset:268.606171575px;transform:rotate(0)}12.5%{stroke-dashoffset:56.5486677px;transform:rotate(0)}12.5001%{stroke-dashoffset:56.5486677px;transform:rotateX(180deg) rotate(72.5deg)}25%{stroke-dashoffset:268.606171575px;transform:rotateX(180deg) rotate(72.5deg)}25.0001%{stroke-dashoffset:268.606171575px;transform:rotate(270deg)}37.5%{stroke-dashoffset:56.5486677px;transform:rotate(270deg)}37.5001%{stroke-dashoffset:56.5486677px;transform:rotateX(180deg) rotate(161.5deg)}50%{stroke-dashoffset:268.606171575px;transform:rotateX(180deg) rotate(161.5deg)}50.0001%{stroke-dashoffset:268.606171575px;transform:rotate(180deg)}62.5%{stroke-dashoffset:56.5486677px;transform:rotate(180deg)}62.5001%{stroke-dashoffset:56.5486677px;transform:rotateX(180deg) rotate(251.5deg)}75%{stroke-dashoffset:268.606171575px;transform:rotateX(180deg) rotate(251.5deg)}75.0001%{stroke-dashoffset:268.606171575px;transform:rotate(90deg)}87.5%{stroke-dashoffset:56.5486677px;transform:rotate(90deg)}87.5001%{stroke-dashoffset:56.5486677px;transform:rotateX(180deg) rotate(341.5deg)}100%{stroke-dashoffset:268.606171575px;transform:rotateX(180deg) rotate(341.5deg)}}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS]\n                }] }, { type: i0.ChangeDetectorRef }, { type: i2.ViewportRuler }, { type: i0.NgZone }]; }, propDecorators: { diameter: [{\n                type: Input\n            }], strokeWidth: [{\n                type: Input\n            }], mode: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatProgressSpinnerModule {\n}\nMatProgressSpinnerModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatProgressSpinnerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatProgressSpinnerModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.2.0\", ngImport: i0, type: MatProgressSpinnerModule, declarations: [MatProgressSpinner], imports: [MatCommonModule, CommonModule], exports: [MatProgressSpinner, MatCommonModule] });\nMatProgressSpinnerModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatProgressSpinnerModule, imports: [MatCommonModule, CommonModule, MatCommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatProgressSpinnerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, CommonModule],\n                    exports: [MatProgressSpinner, MatCommonModule],\n                    declarations: [MatProgressSpinner],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * @deprecated Import `MatProgressSpinner` instead. Note that the\n *    `mat-spinner` selector isn't deprecated.\n * @breaking-change 8.0.0\n */\n// tslint:disable-next-line:variable-name\nconst MatSpinner = MatProgressSpinner;\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS, MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY, MatProgressSpinner, MatProgressSpinnerModule, MatSpinner };\n"], "mappings": "AAAA,SAASA,oBAAT,QAAqC,uBAArC;AACA,OAAO,KAAKC,EAAZ,MAAoB,uBAApB;AACA,SAASC,cAAT,QAA+B,uBAA/B;AACA,OAAO,KAAKC,EAAZ,MAAoB,wBAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,QAAT,EAAmBC,YAAnB,QAAuC,iBAAvC;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,SAASC,cAAT,EAAyBC,SAAzB,EAAoCC,uBAApC,EAA6DC,iBAA7D,EAAgFC,QAAhF,EAA0FC,MAA1F,EAAkGC,KAAlG,EAAyGC,QAAzG,QAAyH,eAAzH;AACA,SAASC,UAAT,EAAqBC,eAArB,QAA4C,wBAA5C;AACA,SAASC,qBAAT,QAAsC,sCAAtC;AACA,SAASC,YAAT,QAA6B,MAA7B;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;;;IA2NqGZ,E;IAAAA,EAC+yD,0B;;;;mBAD/yDA,E;;gBAAAA,E;;IAAAA,EAC65D,2U;IAD75DA,EACw3D,4C;;;;;;IADx3DA,E;IAAAA,EAC2vE,0B;;;;mBAD3vEA,E;;gBAAAA,E;;IAAAA,EAC02E,kP;IAD12EA,EACq0E,4C;;;;AA3N16E,MAAMa,SAAS,GAAG,GAAlB;AACA;AACA;AACA;AACA;;AACA,MAAMC,iBAAiB,GAAG,EAA1B,C,CACA;;AACA;;AACA,MAAMC,uBAAuB,GAAGN,UAAU,CAAC,MAAM;EAC7CO,WAAW,CAACC,WAAD,EAAc;IACrB,KAAKA,WAAL,GAAmBA,WAAnB;EACH;;AAH4C,CAAP,EAIvC,SAJuC,CAA1C;AAKA;;;AACA,MAAMC,oCAAoC,GAAG,IAAIjB,cAAJ,CAAmB,sCAAnB,EAA2D;EACpGkB,UAAU,EAAE,MADwF;EAEpGC,OAAO,EAAEC;AAF2F,CAA3D,CAA7C;AAIA;;AACA,SAASA,4CAAT,GAAwD;EACpD,OAAO;IAAEC,QAAQ,EAAET;EAAZ,CAAP;AACH,C,CACD;AACA;AACA;AACA;;;AACA,MAAMU,gCAAgC,GAAI;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAtBA;AAuBA;AACA;AACA;;AACA,MAAMC,kBAAN,SAAiCT,uBAAjC,CAAyD;EACrDC,WAAW,CAACS,UAAD,EAAaC,SAAb,EAAwBC,SAAxB,EAAmCC,aAAnC,EAAkDC,QAAlD;EACX;AACJ;AACA;AACA;AACA;EACIC,iBANW,EAMQC,aANR,EAMuBC,MANvB,EAM+B;IACtC,MAAMP,UAAN;IACA,KAAKE,SAAL,GAAiBA,SAAjB;IACA,KAAKM,SAAL,GAAiBpB,SAAjB;IACA,KAAKqB,MAAL,GAAc,CAAd;IACA,KAAKC,mBAAL,GAA2BvB,YAAY,CAACwB,KAAxC;IACA;;IACA,KAAKC,IAAL,GAAY,aAAZ;IACA,MAAMC,gBAAgB,GAAGd,kBAAkB,CAACe,UAA5C;IACA,KAAKC,sBAAL,GAA8B,KAAKC,yBAAL,EAA9B,CATsC,CAUtC;IACA;;IACA,IAAI,CAACH,gBAAgB,CAACI,GAAjB,CAAqBf,SAAS,CAACgB,IAA/B,CAAL,EAA2C;MACvCL,gBAAgB,CAACM,GAAjB,CAAqBjB,SAAS,CAACgB,IAA/B,EAAqC,IAAIE,GAAJ,CAAQ,CAAChC,SAAD,CAAR,CAArC;IACH;;IACD,KAAKiC,eAAL,GACIlB,aAAa,KAAK,gBAAlB,IAAsC,CAAC,CAACC,QAAxC,IAAoD,CAACA,QAAQ,CAACkB,gBADlE;;IAEA,IAAItB,UAAU,CAACuB,aAAX,CAAyBC,QAAzB,CAAkCC,WAAlC,OAAoD,aAAxD,EAAuE;MACnE,KAAKb,IAAL,GAAY,eAAZ;IACH;;IACD,IAAIR,QAAJ,EAAc;MACV,IAAIA,QAAQ,CAACsB,KAAb,EAAoB;QAChB,KAAKA,KAAL,GAAa,KAAKC,YAAL,GAAoBvB,QAAQ,CAACsB,KAA1C;MACH;;MACD,IAAItB,QAAQ,CAACP,QAAb,EAAuB;QACnB,KAAKA,QAAL,GAAgBO,QAAQ,CAACP,QAAzB;MACH;;MACD,IAAIO,QAAQ,CAACwB,WAAb,EAA0B;QACtB,KAAKA,WAAL,GAAmBxB,QAAQ,CAACwB,WAA5B;MACH;IACJ,CA9BqC,CA+BtC;IACA;IACA;IACA;IACA;IACA;;;IACA,IAAI3B,SAAS,CAAC4B,SAAV,IAAuB5B,SAAS,CAAC6B,MAAjC,IAA2CxB,aAA3C,IAA4DD,iBAA5D,IAAiFE,MAArF,EAA6F;MACzF,KAAKG,mBAAL,GAA2BJ,aAAa,CAACyB,MAAd,CAAqB,GAArB,EAA0BC,SAA1B,CAAoC,MAAM;QACjE;QACA;QACA,IAAI,KAAKpB,IAAL,KAAc,eAAlB,EAAmC;UAC/BL,MAAM,CAAC0B,GAAP,CAAW,MAAM5B,iBAAiB,CAAC6B,YAAlB,EAAjB;QACH;MACJ,CAN0B,CAA3B;IAOH;EACJ;EACD;;;EACY,IAARrC,QAAQ,GAAG;IACX,OAAO,KAAKW,SAAZ;EACH;;EACW,IAARX,QAAQ,CAACsC,IAAD,EAAO;IACf,KAAK3B,SAAL,GAAiBxC,oBAAoB,CAACmE,IAAD,CAArC;IACA,KAAKpB,sBAAL,GAA8B,KAAKC,yBAAL,EAA9B,CAFe,CAGf;;IACA,IAAI,KAAKoB,UAAT,EAAqB;MACjB,KAAKC,gBAAL;IACH;EACJ;EACD;;;EACe,IAAXT,WAAW,GAAG;IACd,OAAO,KAAKU,YAAL,IAAqB,KAAKzC,QAAL,GAAgB,EAA5C;EACH;;EACc,IAAX+B,WAAW,CAACW,KAAD,EAAQ;IACnB,KAAKD,YAAL,GAAoBtE,oBAAoB,CAACuE,KAAD,CAAxC;EACH;EACD;;;EACS,IAALA,KAAK,GAAG;IACR,OAAO,KAAK3B,IAAL,KAAc,aAAd,GAA8B,KAAKH,MAAnC,GAA4C,CAAnD;EACH;;EACQ,IAAL8B,KAAK,CAACC,QAAD,EAAW;IAChB,KAAK/B,MAAL,GAAcgC,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYD,IAAI,CAACE,GAAL,CAAS,GAAT,EAAc3E,oBAAoB,CAACwE,QAAD,CAAlC,CAAZ,CAAd;EACH;;EACDI,QAAQ,GAAG;IACP,MAAMC,OAAO,GAAG,KAAKrD,WAAL,CAAiB+B,aAAjC,CADO,CAEP;IACA;IACA;;IACA,KAAKa,UAAL,GAAkBlE,cAAc,CAAC2E,OAAD,CAAd,IAA2B,KAAK3C,SAAL,CAAegB,IAA5D;;IACA,KAAKmB,gBAAL;;IACAQ,OAAO,CAACC,SAAR,CAAkBC,GAAlB,CAAsB,8CAAtB;EACH;;EACDC,WAAW,GAAG;IACV,KAAKtC,mBAAL,CAAyBuC,WAAzB;EACH;EACD;;;EACAC,gBAAgB,GAAG;IACf,OAAO,CAAC,KAAKrD,QAAL,GAAgBR,iBAAjB,IAAsC,CAA7C;EACH;EACD;;;EACA8D,WAAW,GAAG;IACV,MAAMC,OAAO,GAAG,KAAKF,gBAAL,KAA0B,CAA1B,GAA8B,KAAKtB,WAAnD;IACA,OAAQ,OAAMwB,OAAQ,IAAGA,OAAQ,EAAjC;EACH;EACD;;;EACAC,uBAAuB,GAAG;IACtB,OAAO,IAAIZ,IAAI,CAACa,EAAT,GAAc,KAAKJ,gBAAL,EAArB;EACH;EACD;;;EACAK,oBAAoB,GAAG;IACnB,IAAI,KAAK3C,IAAL,KAAc,aAAlB,EAAiC;MAC7B,OAAQ,KAAKyC,uBAAL,MAAkC,MAAM,KAAK5C,MAA7C,CAAD,GAAyD,GAAhE;IACH;;IACD,OAAO,IAAP;EACH;EACD;;;EACA+C,qBAAqB,GAAG;IACpB,OAAQ,KAAK5B,WAAL,GAAmB,KAAK/B,QAAzB,GAAqC,GAA5C;EACH;EACD;;;EACA4D,yBAAyB,CAACC,GAAD,EAAM;IAC3B;IACA;IACA;IACA;IACA,MAAMC,KAAK,GAAG,CAACD,GAAG,CAACE,YAAJ,IAAoB,CAArB,IAA0B,EAAxC;IACA,OAAQ,GAAED,KAAM,KAAIA,KAAM,GAA1B;EACH;EACD;;;EACAtB,gBAAgB,GAAG;IACf,MAAMwB,SAAS,GAAG,KAAKzB,UAAvB;IACA,MAAM0B,eAAe,GAAG,KAAKtD,SAA7B;IACA,MAAMuD,SAAS,GAAGhE,kBAAkB,CAACe,UAArC;IACA,IAAIkD,mBAAmB,GAAGD,SAAS,CAACE,GAAV,CAAcJ,SAAd,CAA1B;;IACA,IAAI,CAACG,mBAAD,IAAwB,CAACA,mBAAmB,CAAC/C,GAApB,CAAwB6C,eAAxB,CAA7B,EAAuE;MACnE,MAAMI,QAAQ,GAAG,KAAKhE,SAAL,CAAeiE,aAAf,CAA6B,OAA7B,CAAjB;;MACAD,QAAQ,CAACE,YAAT,CAAsB,uBAAtB,EAA+C,KAAKrD,sBAApD;MACAmD,QAAQ,CAACG,WAAT,GAAuB,KAAKC,iBAAL,EAAvB;MACAT,SAAS,CAACU,WAAV,CAAsBL,QAAtB;;MACA,IAAI,CAACF,mBAAL,EAA0B;QACtBA,mBAAmB,GAAG,IAAI5C,GAAJ,EAAtB;QACA2C,SAAS,CAAC5C,GAAV,CAAc0C,SAAd,EAAyBG,mBAAzB;MACH;;MACDA,mBAAmB,CAACjB,GAApB,CAAwBe,eAAxB;IACH;EACJ;EACD;;;EACAQ,iBAAiB,GAAG;IAChB,MAAME,mBAAmB,GAAG,KAAKnB,uBAAL,EAA5B;;IACA,OAAQvD,gCAAgC,CACpC;IADoC,CAEnC2E,OAFG,CAEK,cAFL,EAEsB,GAAE,OAAOD,mBAAoB,EAFnD,EAGHC,OAHG,CAGK,YAHL,EAGoB,GAAE,MAAMD,mBAAoB,EAHhD,EAIHC,OAJG,CAIK,WAJL,EAImB,GAAE,KAAK1D,sBAAuB,EAJjD,CAAR;EAKH;EACD;;;EACAC,yBAAyB,GAAG;IACxB;IACA;IACA,OAAO,KAAKnB,QAAL,CAAc6E,QAAd,GAAyBD,OAAzB,CAAiC,GAAjC,EAAsC,GAAtC,CAAP;EACH;;AA7JoD;AA+JzD;AACA;AACA;AACA;AACA;AACA;;;AACA1E,kBAAkB,CAACe,UAAnB,GAAgC,IAAI6D,OAAJ,EAAhC;;AACA5E,kBAAkB,CAAC6E,IAAnB;EAAA,iBAA+G7E,kBAA/G,EAAqGxB,EAArG,mBAAmJA,EAAE,CAACsG,UAAtJ,GAAqGtG,EAArG,mBAA6KN,EAAE,CAAC6G,QAAhL,GAAqGvG,EAArG,mBAAqMF,QAArM,MAAqGE,EAArG,mBAA0OW,qBAA1O,MAAqGX,EAArG,mBAA4RkB,oCAA5R,GAAqGlB,EAArG,mBAA6UA,EAAE,CAACwG,iBAAhV,GAAqGxG,EAArG,mBAA8WJ,EAAE,CAAC6G,aAAjX,GAAqGzG,EAArG,mBAA2YA,EAAE,CAAC0G,MAA9Y;AAAA;;AACAlF,kBAAkB,CAACmF,IAAnB,kBADqG3G,EACrG;EAAA,MAAmGwB,kBAAnG;EAAA;EAAA,oBAAgT,aAAhT,cAA2U,IAA3U;EAAA;EAAA;IAAA;MADqGxB,EACrG;MADqGA,EACrG;MADqGA,EACrG;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WADqGA,EACrG;EAAA;EAAA;EAAA;EAAA;IAAA;MADqGA,EAColC,iBAAzrC;MADqGA,EAColC,+BAAzrC;MADqGA,EAC+yD,6EAAp5D;MADqGA,EAC2vE,4EAAh2E;MADqGA,EACqmF,eAA1sF;IAAA;;IAAA;MADqGA,EAC4lC,uEAAjsC;MADqGA,EACmwC,qDAAx2C;MADqGA,EAC+pC,0CAApwC;MADqGA,EAC6zD,aAAl6D;MADqGA,EAC6zD,iCAAl6D;MADqGA,EACywE,aAA92E;MADqGA,EACywE,kCAA92E;IAAA;EAAA;EAAA,eAA00JH,EAAE,CAAC+G,QAA70J,EAAk6J/G,EAAE,CAACgH,YAAr6J;EAAA;EAAA;EAAA;AAAA;;AACA;EAAA,mDAFqG7G,EAErG,mBAA2FwB,kBAA3F,EAA2H,CAAC;IAChHsF,IAAI,EAAE5G,SAD0G;IAEhH6G,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,mCAAZ;MAAiDC,QAAQ,EAAE,oBAA3D;MAAiFC,IAAI,EAAE;QAClF,QAAQ,aAD0E;QAElF;QACA,SAAS,kCAHyE;QAIlF;QACA;QACA,YAAY,IANsE;QAOlF,mCAAoC,iBAP8C;QAQlF,oBAAoB,UAR8D;QASlF,qBAAqB,UAT6D;QAUlF,wBAAwB,mCAV0D;QAWlF,wBAAwB,qCAX0D;QAYlF,wBAAwB,uCAZ0D;QAalF,eAAe;MAbmE,CAAvF;MAcIC,MAAM,EAAE,CAAC,OAAD,CAdZ;MAcuBC,eAAe,EAAEjH,uBAAuB,CAACkH,MAdhE;MAcwEC,aAAa,EAAElH,iBAAiB,CAACmH,IAdzG;MAc+GC,QAAQ,EAAE,s6DAdzH;MAciiEC,MAAM,EAAE,CAAC,+jEAAD;IAdziE,CAAD;EAF0G,CAAD,CAA3H,EAiB4B,YAAY;IAAE,OAAO,CAAC;MAAEX,IAAI,EAAE9G,EAAE,CAACsG;IAAX,CAAD,EAA0B;MAAEQ,IAAI,EAAEpH,EAAE,CAAC6G;IAAX,CAA1B,EAAiD;MAAEO,IAAI,EAAEY,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC9Gb,IAAI,EAAEzG;MADwG,CAAD,EAE9G;QACCyG,IAAI,EAAExG,MADP;QAECyG,IAAI,EAAE,CAACjH,QAAD;MAFP,CAF8G;IAA/B,CAAjD,EAK3B;MAAEgH,IAAI,EAAEY,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAClCb,IAAI,EAAEzG;MAD4B,CAAD,EAElC;QACCyG,IAAI,EAAExG,MADP;QAECyG,IAAI,EAAE,CAACpG,qBAAD;MAFP,CAFkC;IAA/B,CAL2B,EAU3B;MAAEmG,IAAI,EAAEY,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAClCb,IAAI,EAAExG,MAD4B;QAElCyG,IAAI,EAAE,CAAC7F,oCAAD;MAF4B,CAAD;IAA/B,CAV2B,EAa3B;MAAE4F,IAAI,EAAE9G,EAAE,CAACwG;IAAX,CAb2B,EAaK;MAAEM,IAAI,EAAElH,EAAE,CAAC6G;IAAX,CAbL,EAaiC;MAAEK,IAAI,EAAE9G,EAAE,CAAC0G;IAAX,CAbjC,CAAP;EAa+D,CA9BzG,EA8B2H;IAAEpF,QAAQ,EAAE,CAAC;MACxHwF,IAAI,EAAEvG;IADkH,CAAD,CAAZ;IAE3G8C,WAAW,EAAE,CAAC;MACdyD,IAAI,EAAEvG;IADQ,CAAD,CAF8F;IAI3G8B,IAAI,EAAE,CAAC;MACPyE,IAAI,EAAEvG;IADC,CAAD,CAJqG;IAM3GyD,KAAK,EAAE,CAAC;MACR8C,IAAI,EAAEvG;IADE,CAAD;EANoG,CA9B3H;AAAA;AAwCA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMqH,wBAAN,CAA+B;;AAE/BA,wBAAwB,CAACvB,IAAzB;EAAA,iBAAqHuB,wBAArH;AAAA;;AACAA,wBAAwB,CAACC,IAAzB,kBApDqG7H,EAoDrG;EAAA,MAAsH4H;AAAtH;AACAA,wBAAwB,CAACE,IAAzB,kBArDqG9H,EAqDrG;EAAA,UAA0JU,eAA1J,EAA2KX,YAA3K,EAAyLW,eAAzL;AAAA;;AACA;EAAA,mDAtDqGV,EAsDrG,mBAA2F4H,wBAA3F,EAAiI,CAAC;IACtHd,IAAI,EAAEtG,QADgH;IAEtHuG,IAAI,EAAE,CAAC;MACCgB,OAAO,EAAE,CAACrH,eAAD,EAAkBX,YAAlB,CADV;MAECiI,OAAO,EAAE,CAACxG,kBAAD,EAAqBd,eAArB,CAFV;MAGCuH,YAAY,EAAE,CAACzG,kBAAD;IAHf,CAAD;EAFgH,CAAD,CAAjI;AAAA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM0G,UAAU,GAAG1G,kBAAnB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASN,oCAAT,EAA+CG,4CAA/C,EAA6FG,kBAA7F,EAAiHoG,wBAAjH,EAA2IM,UAA3I", "ignoreList": []}, "metadata": {}, "sourceType": "module"}