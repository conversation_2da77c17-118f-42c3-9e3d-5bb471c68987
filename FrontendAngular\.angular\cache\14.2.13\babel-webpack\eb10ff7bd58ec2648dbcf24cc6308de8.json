{"ast": null, "code": "/**\n * @license Angular v14.3.0\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\nimport { ɵAnimationGroupPlayer, NoopAnimationPlayer, AUTO_STYLE, ɵPRE_STYLE, sequence, style } from '@angular/animations';\nimport * as i0 from '@angular/core';\nimport { ɵRuntimeError, Injectable } from '@angular/core';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nconst LINE_START = '\\n - ';\n\nfunction invalidTimingValue(exp) {\n  return new ɵRuntimeError(3000\n  /* RuntimeErrorCode.INVALID_TIMING_VALUE */\n  , ngDevMode && `The provided timing value \"${exp}\" is invalid.`);\n}\n\nfunction negativeStepValue() {\n  return new ɵRuntimeError(3100\n  /* RuntimeErrorCode.NEGATIVE_STEP_VALUE */\n  , ngDevMode && 'Duration values below 0 are not allowed for this animation step.');\n}\n\nfunction negativeDelayValue() {\n  return new ɵRuntimeError(3101\n  /* RuntimeErrorCode.NEGATIVE_DELAY_VALUE */\n  , ngDevMode && 'Delay values below 0 are not allowed for this animation step.');\n}\n\nfunction invalidStyleParams(varName) {\n  return new ɵRuntimeError(3001\n  /* RuntimeErrorCode.INVALID_STYLE_PARAMS */\n  , ngDevMode && `Unable to resolve the local animation param ${varName} in the given list of values`);\n}\n\nfunction invalidParamValue(varName) {\n  return new ɵRuntimeError(3003\n  /* RuntimeErrorCode.INVALID_PARAM_VALUE */\n  , ngDevMode && `Please provide a value for the animation param ${varName}`);\n}\n\nfunction invalidNodeType(nodeType) {\n  return new ɵRuntimeError(3004\n  /* RuntimeErrorCode.INVALID_NODE_TYPE */\n  , ngDevMode && `Unable to resolve animation metadata node #${nodeType}`);\n}\n\nfunction invalidCssUnitValue(userProvidedProperty, value) {\n  return new ɵRuntimeError(3005\n  /* RuntimeErrorCode.INVALID_CSS_UNIT_VALUE */\n  , ngDevMode && `Please provide a CSS unit value for ${userProvidedProperty}:${value}`);\n}\n\nfunction invalidTrigger() {\n  return new ɵRuntimeError(3006\n  /* RuntimeErrorCode.INVALID_TRIGGER */\n  , ngDevMode && 'animation triggers cannot be prefixed with an `@` sign (e.g. trigger(\\'@foo\\', [...]))');\n}\n\nfunction invalidDefinition() {\n  return new ɵRuntimeError(3007\n  /* RuntimeErrorCode.INVALID_DEFINITION */\n  , ngDevMode && 'only state() and transition() definitions can sit inside of a trigger()');\n}\n\nfunction invalidState(metadataName, missingSubs) {\n  return new ɵRuntimeError(3008\n  /* RuntimeErrorCode.INVALID_STATE */\n  , ngDevMode && `state(\"${metadataName}\", ...) must define default values for all the following style substitutions: ${missingSubs.join(', ')}`);\n}\n\nfunction invalidStyleValue(value) {\n  return new ɵRuntimeError(3002\n  /* RuntimeErrorCode.INVALID_STYLE_VALUE */\n  , ngDevMode && `The provided style string value ${value} is not allowed.`);\n}\n\nfunction invalidProperty(prop) {\n  return new ɵRuntimeError(3009\n  /* RuntimeErrorCode.INVALID_PROPERTY */\n  , ngDevMode && `The provided animation property \"${prop}\" is not a supported CSS property for animations`);\n}\n\nfunction invalidParallelAnimation(prop, firstStart, firstEnd, secondStart, secondEnd) {\n  return new ɵRuntimeError(3010\n  /* RuntimeErrorCode.INVALID_PARALLEL_ANIMATION */\n  , ngDevMode && `The CSS property \"${prop}\" that exists between the times of \"${firstStart}ms\" and \"${firstEnd}ms\" is also being animated in a parallel animation between the times of \"${secondStart}ms\" and \"${secondEnd}ms\"`);\n}\n\nfunction invalidKeyframes() {\n  return new ɵRuntimeError(3011\n  /* RuntimeErrorCode.INVALID_KEYFRAMES */\n  , ngDevMode && `keyframes() must be placed inside of a call to animate()`);\n}\n\nfunction invalidOffset() {\n  return new ɵRuntimeError(3012\n  /* RuntimeErrorCode.INVALID_OFFSET */\n  , ngDevMode && `Please ensure that all keyframe offsets are between 0 and 1`);\n}\n\nfunction keyframeOffsetsOutOfOrder() {\n  return new ɵRuntimeError(3200\n  /* RuntimeErrorCode.KEYFRAME_OFFSETS_OUT_OF_ORDER */\n  , ngDevMode && `Please ensure that all keyframe offsets are in order`);\n}\n\nfunction keyframesMissingOffsets() {\n  return new ɵRuntimeError(3202\n  /* RuntimeErrorCode.KEYFRAMES_MISSING_OFFSETS */\n  , ngDevMode && `Not all style() steps within the declared keyframes() contain offsets`);\n}\n\nfunction invalidStagger() {\n  return new ɵRuntimeError(3013\n  /* RuntimeErrorCode.INVALID_STAGGER */\n  , ngDevMode && `stagger() can only be used inside of query()`);\n}\n\nfunction invalidQuery(selector) {\n  return new ɵRuntimeError(3014\n  /* RuntimeErrorCode.INVALID_QUERY */\n  , ngDevMode && `\\`query(\"${selector}\")\\` returned zero elements. (Use \\`query(\"${selector}\", { optional: true })\\` if you wish to allow this.)`);\n}\n\nfunction invalidExpression(expr) {\n  return new ɵRuntimeError(3015\n  /* RuntimeErrorCode.INVALID_EXPRESSION */\n  , ngDevMode && `The provided transition expression \"${expr}\" is not supported`);\n}\n\nfunction invalidTransitionAlias(alias) {\n  return new ɵRuntimeError(3016\n  /* RuntimeErrorCode.INVALID_TRANSITION_ALIAS */\n  , ngDevMode && `The transition alias value \"${alias}\" is not supported`);\n}\n\nfunction validationFailed(errors) {\n  return new ɵRuntimeError(3500\n  /* RuntimeErrorCode.VALIDATION_FAILED */\n  , ngDevMode && `animation validation failed:\\n${errors.map(err => err.message).join('\\n')}`);\n}\n\nfunction buildingFailed(errors) {\n  return new ɵRuntimeError(3501\n  /* RuntimeErrorCode.BUILDING_FAILED */\n  , ngDevMode && `animation building failed:\\n${errors.map(err => err.message).join('\\n')}`);\n}\n\nfunction triggerBuildFailed(name, errors) {\n  return new ɵRuntimeError(3404\n  /* RuntimeErrorCode.TRIGGER_BUILD_FAILED */\n  , ngDevMode && `The animation trigger \"${name}\" has failed to build due to the following errors:\\n - ${errors.map(err => err.message).join('\\n - ')}`);\n}\n\nfunction animationFailed(errors) {\n  return new ɵRuntimeError(3502\n  /* RuntimeErrorCode.ANIMATION_FAILED */\n  , ngDevMode && `Unable to animate due to the following errors:${LINE_START}${errors.map(err => err.message).join(LINE_START)}`);\n}\n\nfunction registerFailed(errors) {\n  return new ɵRuntimeError(3503\n  /* RuntimeErrorCode.REGISTRATION_FAILED */\n  , ngDevMode && `Unable to build the animation due to the following errors: ${errors.map(err => err.message).join('\\n')}`);\n}\n\nfunction missingOrDestroyedAnimation() {\n  return new ɵRuntimeError(3300\n  /* RuntimeErrorCode.MISSING_OR_DESTROYED_ANIMATION */\n  , ngDevMode && 'The requested animation doesn\\'t exist or has already been destroyed');\n}\n\nfunction createAnimationFailed(errors) {\n  return new ɵRuntimeError(3504\n  /* RuntimeErrorCode.CREATE_ANIMATION_FAILED */\n  , ngDevMode && `Unable to create the animation due to the following errors:${errors.map(err => err.message).join('\\n')}`);\n}\n\nfunction missingPlayer(id) {\n  return new ɵRuntimeError(3301\n  /* RuntimeErrorCode.MISSING_PLAYER */\n  , ngDevMode && `Unable to find the timeline player referenced by ${id}`);\n}\n\nfunction missingTrigger(phase, name) {\n  return new ɵRuntimeError(3302\n  /* RuntimeErrorCode.MISSING_TRIGGER */\n  , ngDevMode && `Unable to listen on the animation trigger event \"${phase}\" because the animation trigger \"${name}\" doesn\\'t exist!`);\n}\n\nfunction missingEvent(name) {\n  return new ɵRuntimeError(3303\n  /* RuntimeErrorCode.MISSING_EVENT */\n  , ngDevMode && `Unable to listen on the animation trigger \"${name}\" because the provided event is undefined!`);\n}\n\nfunction unsupportedTriggerEvent(phase, name) {\n  return new ɵRuntimeError(3400\n  /* RuntimeErrorCode.UNSUPPORTED_TRIGGER_EVENT */\n  , ngDevMode && `The provided animation trigger event \"${phase}\" for the animation trigger \"${name}\" is not supported!`);\n}\n\nfunction unregisteredTrigger(name) {\n  return new ɵRuntimeError(3401\n  /* RuntimeErrorCode.UNREGISTERED_TRIGGER */\n  , ngDevMode && `The provided animation trigger \"${name}\" has not been registered!`);\n}\n\nfunction triggerTransitionsFailed(errors) {\n  return new ɵRuntimeError(3402\n  /* RuntimeErrorCode.TRIGGER_TRANSITIONS_FAILED */\n  , ngDevMode && `Unable to process animations due to the following failed trigger transitions\\n ${errors.map(err => err.message).join('\\n')}`);\n}\n\nfunction triggerParsingFailed(name, errors) {\n  return new ɵRuntimeError(3403\n  /* RuntimeErrorCode.TRIGGER_PARSING_FAILED */\n  , ngDevMode && `Animation parsing for the ${name} trigger have failed:${LINE_START}${errors.map(err => err.message).join(LINE_START)}`);\n}\n\nfunction transitionFailed(name, errors) {\n  return new ɵRuntimeError(3505\n  /* RuntimeErrorCode.TRANSITION_FAILED */\n  , ngDevMode && `@${name} has failed due to:\\n ${errors.map(err => err.message).join('\\n- ')}`);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Set of all animatable CSS properties\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_animated_properties\n */\n\n\nconst ANIMATABLE_PROP_SET = /*#__PURE__*/new Set(['-moz-outline-radius', '-moz-outline-radius-bottomleft', '-moz-outline-radius-bottomright', '-moz-outline-radius-topleft', '-moz-outline-radius-topright', '-ms-grid-columns', '-ms-grid-rows', '-webkit-line-clamp', '-webkit-text-fill-color', '-webkit-text-stroke', '-webkit-text-stroke-color', 'accent-color', 'all', 'backdrop-filter', 'background', 'background-color', 'background-position', 'background-size', 'block-size', 'border', 'border-block-end', 'border-block-end-color', 'border-block-end-width', 'border-block-start', 'border-block-start-color', 'border-block-start-width', 'border-bottom', 'border-bottom-color', 'border-bottom-left-radius', 'border-bottom-right-radius', 'border-bottom-width', 'border-color', 'border-end-end-radius', 'border-end-start-radius', 'border-image-outset', 'border-image-slice', 'border-image-width', 'border-inline-end', 'border-inline-end-color', 'border-inline-end-width', 'border-inline-start', 'border-inline-start-color', 'border-inline-start-width', 'border-left', 'border-left-color', 'border-left-width', 'border-radius', 'border-right', 'border-right-color', 'border-right-width', 'border-start-end-radius', 'border-start-start-radius', 'border-top', 'border-top-color', 'border-top-left-radius', 'border-top-right-radius', 'border-top-width', 'border-width', 'bottom', 'box-shadow', 'caret-color', 'clip', 'clip-path', 'color', 'column-count', 'column-gap', 'column-rule', 'column-rule-color', 'column-rule-width', 'column-width', 'columns', 'filter', 'flex', 'flex-basis', 'flex-grow', 'flex-shrink', 'font', 'font-size', 'font-size-adjust', 'font-stretch', 'font-variation-settings', 'font-weight', 'gap', 'grid-column-gap', 'grid-gap', 'grid-row-gap', 'grid-template-columns', 'grid-template-rows', 'height', 'inline-size', 'input-security', 'inset', 'inset-block', 'inset-block-end', 'inset-block-start', 'inset-inline', 'inset-inline-end', 'inset-inline-start', 'left', 'letter-spacing', 'line-clamp', 'line-height', 'margin', 'margin-block-end', 'margin-block-start', 'margin-bottom', 'margin-inline-end', 'margin-inline-start', 'margin-left', 'margin-right', 'margin-top', 'mask', 'mask-border', 'mask-position', 'mask-size', 'max-block-size', 'max-height', 'max-inline-size', 'max-lines', 'max-width', 'min-block-size', 'min-height', 'min-inline-size', 'min-width', 'object-position', 'offset', 'offset-anchor', 'offset-distance', 'offset-path', 'offset-position', 'offset-rotate', 'opacity', 'order', 'outline', 'outline-color', 'outline-offset', 'outline-width', 'padding', 'padding-block-end', 'padding-block-start', 'padding-bottom', 'padding-inline-end', 'padding-inline-start', 'padding-left', 'padding-right', 'padding-top', 'perspective', 'perspective-origin', 'right', 'rotate', 'row-gap', 'scale', 'scroll-margin', 'scroll-margin-block', 'scroll-margin-block-end', 'scroll-margin-block-start', 'scroll-margin-bottom', 'scroll-margin-inline', 'scroll-margin-inline-end', 'scroll-margin-inline-start', 'scroll-margin-left', 'scroll-margin-right', 'scroll-margin-top', 'scroll-padding', 'scroll-padding-block', 'scroll-padding-block-end', 'scroll-padding-block-start', 'scroll-padding-bottom', 'scroll-padding-inline', 'scroll-padding-inline-end', 'scroll-padding-inline-start', 'scroll-padding-left', 'scroll-padding-right', 'scroll-padding-top', 'scroll-snap-coordinate', 'scroll-snap-destination', 'scrollbar-color', 'shape-image-threshold', 'shape-margin', 'shape-outside', 'tab-size', 'text-decoration', 'text-decoration-color', 'text-decoration-thickness', 'text-emphasis', 'text-emphasis-color', 'text-indent', 'text-shadow', 'text-underline-offset', 'top', 'transform', 'transform-origin', 'translate', 'vertical-align', 'visibility', 'width', 'word-spacing', 'z-index', 'zoom']);\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nfunction isBrowser() {\n  return typeof window !== 'undefined' && typeof window.document !== 'undefined';\n}\n\nfunction isNode() {\n  // Checking only for `process` isn't enough to identify whether or not we're in a Node\n  // environment, because Webpack by default will polyfill the `process`. While we can discern\n  // that Webpack polyfilled it by looking at `process.browser`, it's very Webpack-specific and\n  // might not be future-proof. Instead we look at the stringified version of `process` which\n  // is `[object process]` in Node and `[object Object]` when polyfilled.\n  return typeof process !== 'undefined' && {}.toString.call(process) === '[object process]';\n}\n\nfunction optimizeGroupPlayer(players) {\n  switch (players.length) {\n    case 0:\n      return new NoopAnimationPlayer();\n\n    case 1:\n      return players[0];\n\n    default:\n      return new ɵAnimationGroupPlayer(players);\n  }\n}\n\nfunction normalizeKeyframes$1(driver, normalizer, element, keyframes, preStyles = new Map(), postStyles = new Map()) {\n  const errors = [];\n  const normalizedKeyframes = [];\n  let previousOffset = -1;\n  let previousKeyframe = null;\n  keyframes.forEach(kf => {\n    const offset = kf.get('offset');\n    const isSameOffset = offset == previousOffset;\n    const normalizedKeyframe = isSameOffset && previousKeyframe || new Map();\n    kf.forEach((val, prop) => {\n      let normalizedProp = prop;\n      let normalizedValue = val;\n\n      if (prop !== 'offset') {\n        normalizedProp = normalizer.normalizePropertyName(normalizedProp, errors);\n\n        switch (normalizedValue) {\n          case ɵPRE_STYLE:\n            normalizedValue = preStyles.get(prop);\n            break;\n\n          case AUTO_STYLE:\n            normalizedValue = postStyles.get(prop);\n            break;\n\n          default:\n            normalizedValue = normalizer.normalizeStyleValue(prop, normalizedProp, normalizedValue, errors);\n            break;\n        }\n      }\n\n      normalizedKeyframe.set(normalizedProp, normalizedValue);\n    });\n\n    if (!isSameOffset) {\n      normalizedKeyframes.push(normalizedKeyframe);\n    }\n\n    previousKeyframe = normalizedKeyframe;\n    previousOffset = offset;\n  });\n\n  if (errors.length) {\n    throw animationFailed(errors);\n  }\n\n  return normalizedKeyframes;\n}\n\nfunction listenOnPlayer(player, eventName, event, callback) {\n  switch (eventName) {\n    case 'start':\n      player.onStart(() => callback(event && copyAnimationEvent(event, 'start', player)));\n      break;\n\n    case 'done':\n      player.onDone(() => callback(event && copyAnimationEvent(event, 'done', player)));\n      break;\n\n    case 'destroy':\n      player.onDestroy(() => callback(event && copyAnimationEvent(event, 'destroy', player)));\n      break;\n  }\n}\n\nfunction copyAnimationEvent(e, phaseName, player) {\n  const totalTime = player.totalTime;\n  const disabled = player.disabled ? true : false;\n  const event = makeAnimationEvent(e.element, e.triggerName, e.fromState, e.toState, phaseName || e.phaseName, totalTime == undefined ? e.totalTime : totalTime, disabled);\n  const data = e['_data'];\n\n  if (data != null) {\n    event['_data'] = data;\n  }\n\n  return event;\n}\n\nfunction makeAnimationEvent(element, triggerName, fromState, toState, phaseName = '', totalTime = 0, disabled) {\n  return {\n    element,\n    triggerName,\n    fromState,\n    toState,\n    phaseName,\n    totalTime,\n    disabled: !!disabled\n  };\n}\n\nfunction getOrSetDefaultValue(map, key, defaultValue) {\n  let value = map.get(key);\n\n  if (!value) {\n    map.set(key, value = defaultValue);\n  }\n\n  return value;\n}\n\nfunction parseTimelineCommand(command) {\n  const separatorPos = command.indexOf(':');\n  const id = command.substring(1, separatorPos);\n  const action = command.slice(separatorPos + 1);\n  return [id, action];\n}\n\nlet _contains = (elm1, elm2) => false;\n\nlet _query = (element, selector, multi) => {\n  return [];\n};\n\nlet _documentElement = null;\n\nfunction getParentElement(element) {\n  const parent = element.parentNode || element.host; // consider host to support shadow DOM\n\n  if (parent === _documentElement) {\n    return null;\n  }\n\n  return parent;\n} // Define utility methods for browsers and platform-server(domino) where Element\n// and utility methods exist.\n\n\nconst _isNode = /*#__PURE__*/isNode();\n\nif (_isNode || typeof Element !== 'undefined') {\n  if (! /*#__PURE__*/isBrowser()) {\n    _contains = (elm1, elm2) => elm1.contains(elm2);\n  } else {\n    // Read the document element in an IIFE that's been marked pure to avoid a top-level property\n    // read that may prevent tree-shaking.\n    _documentElement = /* @__PURE__ */(() => document.documentElement)();\n\n    _contains = (elm1, elm2) => {\n      while (elm2) {\n        if (elm2 === elm1) {\n          return true;\n        }\n\n        elm2 = getParentElement(elm2);\n      }\n\n      return false;\n    };\n  }\n\n  _query = (element, selector, multi) => {\n    if (multi) {\n      return Array.from(element.querySelectorAll(selector));\n    }\n\n    const elem = element.querySelector(selector);\n    return elem ? [elem] : [];\n  };\n}\n\nfunction containsVendorPrefix(prop) {\n  // Webkit is the only real popular vendor prefix nowadays\n  // cc: http://shouldiprefix.com/\n  return prop.substring(1, 6) == 'ebkit'; // webkit or Webkit\n}\n\nlet _CACHED_BODY = null;\nlet _IS_WEBKIT = false;\n\nfunction validateStyleProperty(prop) {\n  if (!_CACHED_BODY) {\n    _CACHED_BODY = getBodyNode() || {};\n    _IS_WEBKIT = _CACHED_BODY.style ? 'WebkitAppearance' in _CACHED_BODY.style : false;\n  }\n\n  let result = true;\n\n  if (_CACHED_BODY.style && !containsVendorPrefix(prop)) {\n    result = prop in _CACHED_BODY.style;\n\n    if (!result && _IS_WEBKIT) {\n      const camelProp = 'Webkit' + prop.charAt(0).toUpperCase() + prop.slice(1);\n      result = camelProp in _CACHED_BODY.style;\n    }\n  }\n\n  return result;\n}\n\nfunction validateWebAnimatableStyleProperty(prop) {\n  return ANIMATABLE_PROP_SET.has(prop);\n}\n\nfunction getBodyNode() {\n  if (typeof document != 'undefined') {\n    return document.body;\n  }\n\n  return null;\n}\n\nconst containsElement = _contains;\nconst invokeQuery = _query;\n\nfunction hypenatePropsKeys(original) {\n  const newMap = new Map();\n  original.forEach((val, prop) => {\n    const newProp = prop.replace(/([a-z])([A-Z])/g, '$1-$2');\n    newMap.set(newProp, val);\n  });\n  return newMap;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @publicApi\n */\n\n\nlet NoopAnimationDriver = /*#__PURE__*/(() => {\n  class NoopAnimationDriver {\n    validateStyleProperty(prop) {\n      return validateStyleProperty(prop);\n    }\n\n    matchesElement(_element, _selector) {\n      // This method is deprecated and no longer in use so we return false.\n      return false;\n    }\n\n    containsElement(elm1, elm2) {\n      return containsElement(elm1, elm2);\n    }\n\n    getParentElement(element) {\n      return getParentElement(element);\n    }\n\n    query(element, selector, multi) {\n      return invokeQuery(element, selector, multi);\n    }\n\n    computeStyle(element, prop, defaultValue) {\n      return defaultValue || '';\n    }\n\n    animate(element, keyframes, duration, delay, easing, previousPlayers = [], scrubberAccessRequested) {\n      return new NoopAnimationPlayer(duration, delay);\n    }\n\n  }\n\n  NoopAnimationDriver.ɵfac = function NoopAnimationDriver_Factory(t) {\n    return new (t || NoopAnimationDriver)();\n  };\n\n  NoopAnimationDriver.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NoopAnimationDriver,\n    factory: NoopAnimationDriver.ɵfac\n  });\n  return NoopAnimationDriver;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @publicApi\n */\n\n\nlet AnimationDriver = /*#__PURE__*/(() => {\n  class AnimationDriver {}\n\n  AnimationDriver.NOOP = /* @__PURE__ */new NoopAnimationDriver();\n  /**\n   * @license\n   * Copyright Google LLC All Rights Reserved.\n   *\n   * Use of this source code is governed by an MIT-style license that can be\n   * found in the LICENSE file at https://angular.io/license\n   */\n\n  return AnimationDriver;\n})();\nconst ONE_SECOND = 1000;\nconst SUBSTITUTION_EXPR_START = '{{';\nconst SUBSTITUTION_EXPR_END = '}}';\nconst ENTER_CLASSNAME = 'ng-enter';\nconst LEAVE_CLASSNAME = 'ng-leave';\nconst NG_TRIGGER_CLASSNAME = 'ng-trigger';\nconst NG_TRIGGER_SELECTOR = '.ng-trigger';\nconst NG_ANIMATING_CLASSNAME = 'ng-animating';\nconst NG_ANIMATING_SELECTOR = '.ng-animating';\n\nfunction resolveTimingValue(value) {\n  if (typeof value == 'number') return value;\n  const matches = value.match(/^(-?[\\.\\d]+)(m?s)/);\n  if (!matches || matches.length < 2) return 0;\n  return _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n}\n\nfunction _convertTimeValueToMS(value, unit) {\n  switch (unit) {\n    case 's':\n      return value * ONE_SECOND;\n\n    default:\n      // ms or something else\n      return value;\n  }\n}\n\nfunction resolveTiming(timings, errors, allowNegativeValues) {\n  return timings.hasOwnProperty('duration') ? timings : parseTimeExpression(timings, errors, allowNegativeValues);\n}\n\nfunction parseTimeExpression(exp, errors, allowNegativeValues) {\n  const regex = /^(-?[\\.\\d]+)(m?s)(?:\\s+(-?[\\.\\d]+)(m?s))?(?:\\s+([-a-z]+(?:\\(.+?\\))?))?$/i;\n  let duration;\n  let delay = 0;\n  let easing = '';\n\n  if (typeof exp === 'string') {\n    const matches = exp.match(regex);\n\n    if (matches === null) {\n      errors.push(invalidTimingValue(exp));\n      return {\n        duration: 0,\n        delay: 0,\n        easing: ''\n      };\n    }\n\n    duration = _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n    const delayMatch = matches[3];\n\n    if (delayMatch != null) {\n      delay = _convertTimeValueToMS(parseFloat(delayMatch), matches[4]);\n    }\n\n    const easingVal = matches[5];\n\n    if (easingVal) {\n      easing = easingVal;\n    }\n  } else {\n    duration = exp;\n  }\n\n  if (!allowNegativeValues) {\n    let containsErrors = false;\n    let startIndex = errors.length;\n\n    if (duration < 0) {\n      errors.push(negativeStepValue());\n      containsErrors = true;\n    }\n\n    if (delay < 0) {\n      errors.push(negativeDelayValue());\n      containsErrors = true;\n    }\n\n    if (containsErrors) {\n      errors.splice(startIndex, 0, invalidTimingValue(exp));\n    }\n  }\n\n  return {\n    duration,\n    delay,\n    easing\n  };\n}\n\nfunction copyObj(obj, destination = {}) {\n  Object.keys(obj).forEach(prop => {\n    destination[prop] = obj[prop];\n  });\n  return destination;\n}\n\nfunction convertToMap(obj) {\n  const styleMap = new Map();\n  Object.keys(obj).forEach(prop => {\n    const val = obj[prop];\n    styleMap.set(prop, val);\n  });\n  return styleMap;\n}\n\nfunction normalizeKeyframes(keyframes) {\n  if (!keyframes.length) {\n    return [];\n  }\n\n  if (keyframes[0] instanceof Map) {\n    return keyframes;\n  }\n\n  return keyframes.map(kf => convertToMap(kf));\n}\n\nfunction normalizeStyles(styles) {\n  const normalizedStyles = new Map();\n\n  if (Array.isArray(styles)) {\n    styles.forEach(data => copyStyles(data, normalizedStyles));\n  } else {\n    copyStyles(styles, normalizedStyles);\n  }\n\n  return normalizedStyles;\n}\n\nfunction copyStyles(styles, destination = new Map(), backfill) {\n  if (backfill) {\n    for (let [prop, val] of backfill) {\n      destination.set(prop, val);\n    }\n  }\n\n  for (let [prop, val] of styles) {\n    destination.set(prop, val);\n  }\n\n  return destination;\n}\n\nfunction getStyleAttributeString(element, key, value) {\n  // Return the key-value pair string to be added to the style attribute for the\n  // given CSS style key.\n  if (value) {\n    return key + ':' + value + ';';\n  } else {\n    return '';\n  }\n}\n\nfunction writeStyleAttribute(element) {\n  // Read the style property of the element and manually reflect it to the\n  // style attribute. This is needed because Domino on platform-server doesn't\n  // understand the full set of allowed CSS properties and doesn't reflect some\n  // of them automatically.\n  let styleAttrValue = '';\n\n  for (let i = 0; i < element.style.length; i++) {\n    const key = element.style.item(i);\n    styleAttrValue += getStyleAttributeString(element, key, element.style.getPropertyValue(key));\n  }\n\n  for (const key in element.style) {\n    // Skip internal Domino properties that don't need to be reflected.\n    if (!element.style.hasOwnProperty(key) || key.startsWith('_')) {\n      continue;\n    }\n\n    const dashKey = camelCaseToDashCase(key);\n    styleAttrValue += getStyleAttributeString(element, dashKey, element.style[key]);\n  }\n\n  element.setAttribute('style', styleAttrValue);\n}\n\nfunction setStyles(element, styles, formerStyles) {\n  if (element['style']) {\n    styles.forEach((val, prop) => {\n      const camelProp = dashCaseToCamelCase(prop);\n\n      if (formerStyles && !formerStyles.has(prop)) {\n        formerStyles.set(prop, element.style[camelProp]);\n      }\n\n      element.style[camelProp] = val;\n    }); // On the server set the 'style' attribute since it's not automatically reflected.\n\n    if (isNode()) {\n      writeStyleAttribute(element);\n    }\n  }\n}\n\nfunction eraseStyles(element, styles) {\n  if (element['style']) {\n    styles.forEach((_, prop) => {\n      const camelProp = dashCaseToCamelCase(prop);\n      element.style[camelProp] = '';\n    }); // On the server set the 'style' attribute since it's not automatically reflected.\n\n    if (isNode()) {\n      writeStyleAttribute(element);\n    }\n  }\n}\n\nfunction normalizeAnimationEntry(steps) {\n  if (Array.isArray(steps)) {\n    if (steps.length == 1) return steps[0];\n    return sequence(steps);\n  }\n\n  return steps;\n}\n\nfunction validateStyleParams(value, options, errors) {\n  const params = options.params || {};\n  const matches = extractStyleParams(value);\n\n  if (matches.length) {\n    matches.forEach(varName => {\n      if (!params.hasOwnProperty(varName)) {\n        errors.push(invalidStyleParams(varName));\n      }\n    });\n  }\n}\n\nconst PARAM_REGEX = /*#__PURE__*/new RegExp(`${SUBSTITUTION_EXPR_START}\\\\s*(.+?)\\\\s*${SUBSTITUTION_EXPR_END}`, 'g');\n\nfunction extractStyleParams(value) {\n  let params = [];\n\n  if (typeof value === 'string') {\n    let match;\n\n    while (match = PARAM_REGEX.exec(value)) {\n      params.push(match[1]);\n    }\n\n    PARAM_REGEX.lastIndex = 0;\n  }\n\n  return params;\n}\n\nfunction interpolateParams(value, params, errors) {\n  const original = value.toString();\n  const str = original.replace(PARAM_REGEX, (_, varName) => {\n    let localVal = params[varName]; // this means that the value was never overridden by the data passed in by the user\n\n    if (localVal == null) {\n      errors.push(invalidParamValue(varName));\n      localVal = '';\n    }\n\n    return localVal.toString();\n  }); // we do this to assert that numeric values stay as they are\n\n  return str == original ? value : str;\n}\n\nfunction iteratorToArray(iterator) {\n  const arr = [];\n  let item = iterator.next();\n\n  while (!item.done) {\n    arr.push(item.value);\n    item = iterator.next();\n  }\n\n  return arr;\n}\n\nconst DASH_CASE_REGEXP = /-+([a-z0-9])/g;\n\nfunction dashCaseToCamelCase(input) {\n  return input.replace(DASH_CASE_REGEXP, (...m) => m[1].toUpperCase());\n}\n\nfunction camelCaseToDashCase(input) {\n  return input.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();\n}\n\nfunction allowPreviousPlayerStylesMerge(duration, delay) {\n  return duration === 0 || delay === 0;\n}\n\nfunction balancePreviousStylesIntoKeyframes(element, keyframes, previousStyles) {\n  if (previousStyles.size && keyframes.length) {\n    let startingKeyframe = keyframes[0];\n    let missingStyleProps = [];\n    previousStyles.forEach((val, prop) => {\n      if (!startingKeyframe.has(prop)) {\n        missingStyleProps.push(prop);\n      }\n\n      startingKeyframe.set(prop, val);\n    });\n\n    if (missingStyleProps.length) {\n      for (let i = 1; i < keyframes.length; i++) {\n        let kf = keyframes[i];\n        missingStyleProps.forEach(prop => kf.set(prop, computeStyle(element, prop)));\n      }\n    }\n  }\n\n  return keyframes;\n}\n\nfunction visitDslNode(visitor, node, context) {\n  switch (node.type) {\n    case 7\n    /* AnimationMetadataType.Trigger */\n    :\n      return visitor.visitTrigger(node, context);\n\n    case 0\n    /* AnimationMetadataType.State */\n    :\n      return visitor.visitState(node, context);\n\n    case 1\n    /* AnimationMetadataType.Transition */\n    :\n      return visitor.visitTransition(node, context);\n\n    case 2\n    /* AnimationMetadataType.Sequence */\n    :\n      return visitor.visitSequence(node, context);\n\n    case 3\n    /* AnimationMetadataType.Group */\n    :\n      return visitor.visitGroup(node, context);\n\n    case 4\n    /* AnimationMetadataType.Animate */\n    :\n      return visitor.visitAnimate(node, context);\n\n    case 5\n    /* AnimationMetadataType.Keyframes */\n    :\n      return visitor.visitKeyframes(node, context);\n\n    case 6\n    /* AnimationMetadataType.Style */\n    :\n      return visitor.visitStyle(node, context);\n\n    case 8\n    /* AnimationMetadataType.Reference */\n    :\n      return visitor.visitReference(node, context);\n\n    case 9\n    /* AnimationMetadataType.AnimateChild */\n    :\n      return visitor.visitAnimateChild(node, context);\n\n    case 10\n    /* AnimationMetadataType.AnimateRef */\n    :\n      return visitor.visitAnimateRef(node, context);\n\n    case 11\n    /* AnimationMetadataType.Query */\n    :\n      return visitor.visitQuery(node, context);\n\n    case 12\n    /* AnimationMetadataType.Stagger */\n    :\n      return visitor.visitStagger(node, context);\n\n    default:\n      throw invalidNodeType(node.type);\n  }\n}\n\nfunction computeStyle(element, prop) {\n  return window.getComputedStyle(element)[prop];\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst NG_DEV_MODE = typeof ngDevMode === 'undefined' || !!ngDevMode;\n\nfunction createListOfWarnings(warnings) {\n  const LINE_START = '\\n - ';\n  return `${LINE_START}${warnings.filter(Boolean).map(warning => warning).join(LINE_START)}`;\n}\n\nfunction warnValidation(warnings) {\n  NG_DEV_MODE && console.warn(`animation validation warnings:${createListOfWarnings(warnings)}`);\n}\n\nfunction warnTriggerBuild(name, warnings) {\n  NG_DEV_MODE && console.warn(`The animation trigger \"${name}\" has built with the following warnings:${createListOfWarnings(warnings)}`);\n}\n\nfunction warnRegister(warnings) {\n  NG_DEV_MODE && console.warn(`Animation built with the following warnings:${createListOfWarnings(warnings)}`);\n}\n\nfunction triggerParsingWarnings(name, warnings) {\n  NG_DEV_MODE && console.warn(`Animation parsing for the ${name} trigger presents the following warnings:${createListOfWarnings(warnings)}`);\n}\n\nfunction pushUnrecognizedPropertiesWarning(warnings, props) {\n  if (props.length) {\n    warnings.push(`The following provided properties are not recognized: ${props.join(', ')}`);\n  }\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst ANY_STATE = '*';\n\nfunction parseTransitionExpr(transitionValue, errors) {\n  const expressions = [];\n\n  if (typeof transitionValue == 'string') {\n    transitionValue.split(/\\s*,\\s*/).forEach(str => parseInnerTransitionStr(str, expressions, errors));\n  } else {\n    expressions.push(transitionValue);\n  }\n\n  return expressions;\n}\n\nfunction parseInnerTransitionStr(eventStr, expressions, errors) {\n  if (eventStr[0] == ':') {\n    const result = parseAnimationAlias(eventStr, errors);\n\n    if (typeof result == 'function') {\n      expressions.push(result);\n      return;\n    }\n\n    eventStr = result;\n  }\n\n  const match = eventStr.match(/^(\\*|[-\\w]+)\\s*(<?[=-]>)\\s*(\\*|[-\\w]+)$/);\n\n  if (match == null || match.length < 4) {\n    errors.push(invalidExpression(eventStr));\n    return expressions;\n  }\n\n  const fromState = match[1];\n  const separator = match[2];\n  const toState = match[3];\n  expressions.push(makeLambdaFromStates(fromState, toState));\n  const isFullAnyStateExpr = fromState == ANY_STATE && toState == ANY_STATE;\n\n  if (separator[0] == '<' && !isFullAnyStateExpr) {\n    expressions.push(makeLambdaFromStates(toState, fromState));\n  }\n}\n\nfunction parseAnimationAlias(alias, errors) {\n  switch (alias) {\n    case ':enter':\n      return 'void => *';\n\n    case ':leave':\n      return '* => void';\n\n    case ':increment':\n      return (fromState, toState) => parseFloat(toState) > parseFloat(fromState);\n\n    case ':decrement':\n      return (fromState, toState) => parseFloat(toState) < parseFloat(fromState);\n\n    default:\n      errors.push(invalidTransitionAlias(alias));\n      return '* => *';\n  }\n} // DO NOT REFACTOR ... keep the follow set instantiations\n// with the values intact (closure compiler for some reason\n// removes follow-up lines that add the values outside of\n// the constructor...\n\n\nconst TRUE_BOOLEAN_VALUES = /*#__PURE__*/new Set(['true', '1']);\nconst FALSE_BOOLEAN_VALUES = /*#__PURE__*/new Set(['false', '0']);\n\nfunction makeLambdaFromStates(lhs, rhs) {\n  const LHS_MATCH_BOOLEAN = TRUE_BOOLEAN_VALUES.has(lhs) || FALSE_BOOLEAN_VALUES.has(lhs);\n  const RHS_MATCH_BOOLEAN = TRUE_BOOLEAN_VALUES.has(rhs) || FALSE_BOOLEAN_VALUES.has(rhs);\n  return (fromState, toState) => {\n    let lhsMatch = lhs == ANY_STATE || lhs == fromState;\n    let rhsMatch = rhs == ANY_STATE || rhs == toState;\n\n    if (!lhsMatch && LHS_MATCH_BOOLEAN && typeof fromState === 'boolean') {\n      lhsMatch = fromState ? TRUE_BOOLEAN_VALUES.has(lhs) : FALSE_BOOLEAN_VALUES.has(lhs);\n    }\n\n    if (!rhsMatch && RHS_MATCH_BOOLEAN && typeof toState === 'boolean') {\n      rhsMatch = toState ? TRUE_BOOLEAN_VALUES.has(rhs) : FALSE_BOOLEAN_VALUES.has(rhs);\n    }\n\n    return lhsMatch && rhsMatch;\n  };\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst SELF_TOKEN = ':self';\nconst SELF_TOKEN_REGEX = /*#__PURE__*/new RegExp(`\\s*${SELF_TOKEN}\\s*,?`, 'g');\n/*\n * [Validation]\n * The visitor code below will traverse the animation AST generated by the animation verb functions\n * (the output is a tree of objects) and attempt to perform a series of validations on the data. The\n * following corner-cases will be validated:\n *\n * 1. Overlap of animations\n * Given that a CSS property cannot be animated in more than one place at the same time, it's\n * important that this behavior is detected and validated. The way in which this occurs is that\n * each time a style property is examined, a string-map containing the property will be updated with\n * the start and end times for when the property is used within an animation step.\n *\n * If there are two or more parallel animations that are currently running (these are invoked by the\n * group()) on the same element then the validator will throw an error. Since the start/end timing\n * values are collected for each property then if the current animation step is animating the same\n * property and its timing values fall anywhere into the window of time that the property is\n * currently being animated within then this is what causes an error.\n *\n * 2. Timing values\n * The validator will validate to see if a timing value of `duration delay easing` or\n * `durationNumber` is valid or not.\n *\n * (note that upon validation the code below will replace the timing data with an object containing\n * {duration,delay,easing}.\n *\n * 3. Offset Validation\n * Each of the style() calls are allowed to have an offset value when placed inside of keyframes().\n * Offsets within keyframes() are considered valid when:\n *\n *   - No offsets are used at all\n *   - Each style() entry contains an offset value\n *   - Each offset is between 0 and 1\n *   - Each offset is greater to or equal than the previous one\n *\n * Otherwise an error will be thrown.\n */\n\nfunction buildAnimationAst(driver, metadata, errors, warnings) {\n  return new AnimationAstBuilderVisitor(driver).build(metadata, errors, warnings);\n}\n\nconst ROOT_SELECTOR = '';\n\nclass AnimationAstBuilderVisitor {\n  constructor(_driver) {\n    this._driver = _driver;\n  }\n\n  build(metadata, errors, warnings) {\n    const context = new AnimationAstBuilderContext(errors);\n\n    this._resetContextStyleTimingState(context);\n\n    const ast = visitDslNode(this, normalizeAnimationEntry(metadata), context);\n\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (context.unsupportedCSSPropertiesFound.size) {\n        pushUnrecognizedPropertiesWarning(warnings, [...context.unsupportedCSSPropertiesFound.keys()]);\n      }\n    }\n\n    return ast;\n  }\n\n  _resetContextStyleTimingState(context) {\n    context.currentQuerySelector = ROOT_SELECTOR;\n    context.collectedStyles = new Map();\n    context.collectedStyles.set(ROOT_SELECTOR, new Map());\n    context.currentTime = 0;\n  }\n\n  visitTrigger(metadata, context) {\n    let queryCount = context.queryCount = 0;\n    let depCount = context.depCount = 0;\n    const states = [];\n    const transitions = [];\n\n    if (metadata.name.charAt(0) == '@') {\n      context.errors.push(invalidTrigger());\n    }\n\n    metadata.definitions.forEach(def => {\n      this._resetContextStyleTimingState(context);\n\n      if (def.type == 0\n      /* AnimationMetadataType.State */\n      ) {\n        const stateDef = def;\n        const name = stateDef.name;\n        name.toString().split(/\\s*,\\s*/).forEach(n => {\n          stateDef.name = n;\n          states.push(this.visitState(stateDef, context));\n        });\n        stateDef.name = name;\n      } else if (def.type == 1\n      /* AnimationMetadataType.Transition */\n      ) {\n        const transition = this.visitTransition(def, context);\n        queryCount += transition.queryCount;\n        depCount += transition.depCount;\n        transitions.push(transition);\n      } else {\n        context.errors.push(invalidDefinition());\n      }\n    });\n    return {\n      type: 7\n      /* AnimationMetadataType.Trigger */\n      ,\n      name: metadata.name,\n      states,\n      transitions,\n      queryCount,\n      depCount,\n      options: null\n    };\n  }\n\n  visitState(metadata, context) {\n    const styleAst = this.visitStyle(metadata.styles, context);\n    const astParams = metadata.options && metadata.options.params || null;\n\n    if (styleAst.containsDynamicStyles) {\n      const missingSubs = new Set();\n      const params = astParams || {};\n      styleAst.styles.forEach(style => {\n        if (style instanceof Map) {\n          style.forEach(value => {\n            extractStyleParams(value).forEach(sub => {\n              if (!params.hasOwnProperty(sub)) {\n                missingSubs.add(sub);\n              }\n            });\n          });\n        }\n      });\n\n      if (missingSubs.size) {\n        const missingSubsArr = iteratorToArray(missingSubs.values());\n        context.errors.push(invalidState(metadata.name, missingSubsArr));\n      }\n    }\n\n    return {\n      type: 0\n      /* AnimationMetadataType.State */\n      ,\n      name: metadata.name,\n      style: styleAst,\n      options: astParams ? {\n        params: astParams\n      } : null\n    };\n  }\n\n  visitTransition(metadata, context) {\n    context.queryCount = 0;\n    context.depCount = 0;\n    const animation = visitDslNode(this, normalizeAnimationEntry(metadata.animation), context);\n    const matchers = parseTransitionExpr(metadata.expr, context.errors);\n    return {\n      type: 1\n      /* AnimationMetadataType.Transition */\n      ,\n      matchers,\n      animation,\n      queryCount: context.queryCount,\n      depCount: context.depCount,\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n\n  visitSequence(metadata, context) {\n    return {\n      type: 2\n      /* AnimationMetadataType.Sequence */\n      ,\n      steps: metadata.steps.map(s => visitDslNode(this, s, context)),\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n\n  visitGroup(metadata, context) {\n    const currentTime = context.currentTime;\n    let furthestTime = 0;\n    const steps = metadata.steps.map(step => {\n      context.currentTime = currentTime;\n      const innerAst = visitDslNode(this, step, context);\n      furthestTime = Math.max(furthestTime, context.currentTime);\n      return innerAst;\n    });\n    context.currentTime = furthestTime;\n    return {\n      type: 3\n      /* AnimationMetadataType.Group */\n      ,\n      steps,\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n\n  visitAnimate(metadata, context) {\n    const timingAst = constructTimingAst(metadata.timings, context.errors);\n    context.currentAnimateTimings = timingAst;\n    let styleAst;\n    let styleMetadata = metadata.styles ? metadata.styles : style({});\n\n    if (styleMetadata.type == 5\n    /* AnimationMetadataType.Keyframes */\n    ) {\n      styleAst = this.visitKeyframes(styleMetadata, context);\n    } else {\n      let styleMetadata = metadata.styles;\n      let isEmpty = false;\n\n      if (!styleMetadata) {\n        isEmpty = true;\n        const newStyleData = {};\n\n        if (timingAst.easing) {\n          newStyleData['easing'] = timingAst.easing;\n        }\n\n        styleMetadata = style(newStyleData);\n      }\n\n      context.currentTime += timingAst.duration + timingAst.delay;\n\n      const _styleAst = this.visitStyle(styleMetadata, context);\n\n      _styleAst.isEmptyStep = isEmpty;\n      styleAst = _styleAst;\n    }\n\n    context.currentAnimateTimings = null;\n    return {\n      type: 4\n      /* AnimationMetadataType.Animate */\n      ,\n      timings: timingAst,\n      style: styleAst,\n      options: null\n    };\n  }\n\n  visitStyle(metadata, context) {\n    const ast = this._makeStyleAst(metadata, context);\n\n    this._validateStyleAst(ast, context);\n\n    return ast;\n  }\n\n  _makeStyleAst(metadata, context) {\n    const styles = [];\n    const metadataStyles = Array.isArray(metadata.styles) ? metadata.styles : [metadata.styles];\n\n    for (let styleTuple of metadataStyles) {\n      if (typeof styleTuple === 'string') {\n        if (styleTuple === AUTO_STYLE) {\n          styles.push(styleTuple);\n        } else {\n          context.errors.push(invalidStyleValue(styleTuple));\n        }\n      } else {\n        styles.push(convertToMap(styleTuple));\n      }\n    }\n\n    let containsDynamicStyles = false;\n    let collectedEasing = null;\n    styles.forEach(styleData => {\n      if (styleData instanceof Map) {\n        if (styleData.has('easing')) {\n          collectedEasing = styleData.get('easing');\n          styleData.delete('easing');\n        }\n\n        if (!containsDynamicStyles) {\n          for (let value of styleData.values()) {\n            if (value.toString().indexOf(SUBSTITUTION_EXPR_START) >= 0) {\n              containsDynamicStyles = true;\n              break;\n            }\n          }\n        }\n      }\n    });\n    return {\n      type: 6\n      /* AnimationMetadataType.Style */\n      ,\n      styles,\n      easing: collectedEasing,\n      offset: metadata.offset,\n      containsDynamicStyles,\n      options: null\n    };\n  }\n\n  _validateStyleAst(ast, context) {\n    const timings = context.currentAnimateTimings;\n    let endTime = context.currentTime;\n    let startTime = context.currentTime;\n\n    if (timings && startTime > 0) {\n      startTime -= timings.duration + timings.delay;\n    }\n\n    ast.styles.forEach(tuple => {\n      if (typeof tuple === 'string') return;\n      tuple.forEach((value, prop) => {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          if (!this._driver.validateStyleProperty(prop)) {\n            tuple.delete(prop);\n            context.unsupportedCSSPropertiesFound.add(prop);\n            return;\n          }\n        } // This is guaranteed to have a defined Map at this querySelector location making it\n        // safe to add the assertion here. It is set as a default empty map in prior methods.\n\n\n        const collectedStyles = context.collectedStyles.get(context.currentQuerySelector);\n        const collectedEntry = collectedStyles.get(prop);\n        let updateCollectedStyle = true;\n\n        if (collectedEntry) {\n          if (startTime != endTime && startTime >= collectedEntry.startTime && endTime <= collectedEntry.endTime) {\n            context.errors.push(invalidParallelAnimation(prop, collectedEntry.startTime, collectedEntry.endTime, startTime, endTime));\n            updateCollectedStyle = false;\n          } // we always choose the smaller start time value since we\n          // want to have a record of the entire animation window where\n          // the style property is being animated in between\n\n\n          startTime = collectedEntry.startTime;\n        }\n\n        if (updateCollectedStyle) {\n          collectedStyles.set(prop, {\n            startTime,\n            endTime\n          });\n        }\n\n        if (context.options) {\n          validateStyleParams(value, context.options, context.errors);\n        }\n      });\n    });\n  }\n\n  visitKeyframes(metadata, context) {\n    const ast = {\n      type: 5\n      /* AnimationMetadataType.Keyframes */\n      ,\n      styles: [],\n      options: null\n    };\n\n    if (!context.currentAnimateTimings) {\n      context.errors.push(invalidKeyframes());\n      return ast;\n    }\n\n    const MAX_KEYFRAME_OFFSET = 1;\n    let totalKeyframesWithOffsets = 0;\n    const offsets = [];\n    let offsetsOutOfOrder = false;\n    let keyframesOutOfRange = false;\n    let previousOffset = 0;\n    const keyframes = metadata.steps.map(styles => {\n      const style = this._makeStyleAst(styles, context);\n\n      let offsetVal = style.offset != null ? style.offset : consumeOffset(style.styles);\n      let offset = 0;\n\n      if (offsetVal != null) {\n        totalKeyframesWithOffsets++;\n        offset = style.offset = offsetVal;\n      }\n\n      keyframesOutOfRange = keyframesOutOfRange || offset < 0 || offset > 1;\n      offsetsOutOfOrder = offsetsOutOfOrder || offset < previousOffset;\n      previousOffset = offset;\n      offsets.push(offset);\n      return style;\n    });\n\n    if (keyframesOutOfRange) {\n      context.errors.push(invalidOffset());\n    }\n\n    if (offsetsOutOfOrder) {\n      context.errors.push(keyframeOffsetsOutOfOrder());\n    }\n\n    const length = metadata.steps.length;\n    let generatedOffset = 0;\n\n    if (totalKeyframesWithOffsets > 0 && totalKeyframesWithOffsets < length) {\n      context.errors.push(keyframesMissingOffsets());\n    } else if (totalKeyframesWithOffsets == 0) {\n      generatedOffset = MAX_KEYFRAME_OFFSET / (length - 1);\n    }\n\n    const limit = length - 1;\n    const currentTime = context.currentTime;\n    const currentAnimateTimings = context.currentAnimateTimings;\n    const animateDuration = currentAnimateTimings.duration;\n    keyframes.forEach((kf, i) => {\n      const offset = generatedOffset > 0 ? i == limit ? 1 : generatedOffset * i : offsets[i];\n      const durationUpToThisFrame = offset * animateDuration;\n      context.currentTime = currentTime + currentAnimateTimings.delay + durationUpToThisFrame;\n      currentAnimateTimings.duration = durationUpToThisFrame;\n\n      this._validateStyleAst(kf, context);\n\n      kf.offset = offset;\n      ast.styles.push(kf);\n    });\n    return ast;\n  }\n\n  visitReference(metadata, context) {\n    return {\n      type: 8\n      /* AnimationMetadataType.Reference */\n      ,\n      animation: visitDslNode(this, normalizeAnimationEntry(metadata.animation), context),\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n\n  visitAnimateChild(metadata, context) {\n    context.depCount++;\n    return {\n      type: 9\n      /* AnimationMetadataType.AnimateChild */\n      ,\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n\n  visitAnimateRef(metadata, context) {\n    return {\n      type: 10\n      /* AnimationMetadataType.AnimateRef */\n      ,\n      animation: this.visitReference(metadata.animation, context),\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n\n  visitQuery(metadata, context) {\n    const parentSelector = context.currentQuerySelector;\n    const options = metadata.options || {};\n    context.queryCount++;\n    context.currentQuery = metadata;\n    const [selector, includeSelf] = normalizeSelector(metadata.selector);\n    context.currentQuerySelector = parentSelector.length ? parentSelector + ' ' + selector : selector;\n    getOrSetDefaultValue(context.collectedStyles, context.currentQuerySelector, new Map());\n    const animation = visitDslNode(this, normalizeAnimationEntry(metadata.animation), context);\n    context.currentQuery = null;\n    context.currentQuerySelector = parentSelector;\n    return {\n      type: 11\n      /* AnimationMetadataType.Query */\n      ,\n      selector,\n      limit: options.limit || 0,\n      optional: !!options.optional,\n      includeSelf,\n      animation,\n      originalSelector: metadata.selector,\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n\n  visitStagger(metadata, context) {\n    if (!context.currentQuery) {\n      context.errors.push(invalidStagger());\n    }\n\n    const timings = metadata.timings === 'full' ? {\n      duration: 0,\n      delay: 0,\n      easing: 'full'\n    } : resolveTiming(metadata.timings, context.errors, true);\n    return {\n      type: 12\n      /* AnimationMetadataType.Stagger */\n      ,\n      animation: visitDslNode(this, normalizeAnimationEntry(metadata.animation), context),\n      timings,\n      options: null\n    };\n  }\n\n}\n\nfunction normalizeSelector(selector) {\n  const hasAmpersand = selector.split(/\\s*,\\s*/).find(token => token == SELF_TOKEN) ? true : false;\n\n  if (hasAmpersand) {\n    selector = selector.replace(SELF_TOKEN_REGEX, '');\n  } // Note: the :enter and :leave aren't normalized here since those\n  // selectors are filled in at runtime during timeline building\n\n\n  selector = selector.replace(/@\\*/g, NG_TRIGGER_SELECTOR).replace(/@\\w+/g, match => NG_TRIGGER_SELECTOR + '-' + match.slice(1)).replace(/:animating/g, NG_ANIMATING_SELECTOR);\n  return [selector, hasAmpersand];\n}\n\nfunction normalizeParams(obj) {\n  return obj ? copyObj(obj) : null;\n}\n\nclass AnimationAstBuilderContext {\n  constructor(errors) {\n    this.errors = errors;\n    this.queryCount = 0;\n    this.depCount = 0;\n    this.currentTransition = null;\n    this.currentQuery = null;\n    this.currentQuerySelector = null;\n    this.currentAnimateTimings = null;\n    this.currentTime = 0;\n    this.collectedStyles = new Map();\n    this.options = null;\n    this.unsupportedCSSPropertiesFound = new Set();\n  }\n\n}\n\nfunction consumeOffset(styles) {\n  if (typeof styles == 'string') return null;\n  let offset = null;\n\n  if (Array.isArray(styles)) {\n    styles.forEach(styleTuple => {\n      if (styleTuple instanceof Map && styleTuple.has('offset')) {\n        const obj = styleTuple;\n        offset = parseFloat(obj.get('offset'));\n        obj.delete('offset');\n      }\n    });\n  } else if (styles instanceof Map && styles.has('offset')) {\n    const obj = styles;\n    offset = parseFloat(obj.get('offset'));\n    obj.delete('offset');\n  }\n\n  return offset;\n}\n\nfunction constructTimingAst(value, errors) {\n  if (value.hasOwnProperty('duration')) {\n    return value;\n  }\n\n  if (typeof value == 'number') {\n    const duration = resolveTiming(value, errors).duration;\n    return makeTimingAst(duration, 0, '');\n  }\n\n  const strValue = value;\n  const isDynamic = strValue.split(/\\s+/).some(v => v.charAt(0) == '{' && v.charAt(1) == '{');\n\n  if (isDynamic) {\n    const ast = makeTimingAst(0, 0, '');\n    ast.dynamic = true;\n    ast.strValue = strValue;\n    return ast;\n  }\n\n  const timings = resolveTiming(strValue, errors);\n  return makeTimingAst(timings.duration, timings.delay, timings.easing);\n}\n\nfunction normalizeAnimationOptions(options) {\n  if (options) {\n    options = copyObj(options);\n\n    if (options['params']) {\n      options['params'] = normalizeParams(options['params']);\n    }\n  } else {\n    options = {};\n  }\n\n  return options;\n}\n\nfunction makeTimingAst(duration, delay, easing) {\n  return {\n    duration,\n    delay,\n    easing\n  };\n}\n\nfunction createTimelineInstruction(element, keyframes, preStyleProps, postStyleProps, duration, delay, easing = null, subTimeline = false) {\n  return {\n    type: 1\n    /* AnimationTransitionInstructionType.TimelineAnimation */\n    ,\n    element,\n    keyframes,\n    preStyleProps,\n    postStyleProps,\n    duration,\n    delay,\n    totalTime: duration + delay,\n    easing,\n    subTimeline\n  };\n}\n\nclass ElementInstructionMap {\n  constructor() {\n    this._map = new Map();\n  }\n\n  get(element) {\n    return this._map.get(element) || [];\n  }\n\n  append(element, instructions) {\n    let existingInstructions = this._map.get(element);\n\n    if (!existingInstructions) {\n      this._map.set(element, existingInstructions = []);\n    }\n\n    existingInstructions.push(...instructions);\n  }\n\n  has(element) {\n    return this._map.has(element);\n  }\n\n  clear() {\n    this._map.clear();\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst ONE_FRAME_IN_MILLISECONDS = 1;\nconst ENTER_TOKEN = ':enter';\nconst ENTER_TOKEN_REGEX = /*#__PURE__*/new RegExp(ENTER_TOKEN, 'g');\nconst LEAVE_TOKEN = ':leave';\nconst LEAVE_TOKEN_REGEX = /*#__PURE__*/new RegExp(LEAVE_TOKEN, 'g');\n/*\n * The code within this file aims to generate web-animations-compatible keyframes from Angular's\n * animation DSL code.\n *\n * The code below will be converted from:\n *\n * ```\n * sequence([\n *   style({ opacity: 0 }),\n *   animate(1000, style({ opacity: 0 }))\n * ])\n * ```\n *\n * To:\n * ```\n * keyframes = [{ opacity: 0, offset: 0 }, { opacity: 1, offset: 1 }]\n * duration = 1000\n * delay = 0\n * easing = ''\n * ```\n *\n * For this operation to cover the combination of animation verbs (style, animate, group, etc...) a\n * combination of AST traversal and merge-sort-like algorithms are used.\n *\n * [AST Traversal]\n * Each of the animation verbs, when executed, will return an string-map object representing what\n * type of action it is (style, animate, group, etc...) and the data associated with it. This means\n * that when functional composition mix of these functions is evaluated (like in the example above)\n * then it will end up producing a tree of objects representing the animation itself.\n *\n * When this animation object tree is processed by the visitor code below it will visit each of the\n * verb statements within the visitor. And during each visit it will build the context of the\n * animation keyframes by interacting with the `TimelineBuilder`.\n *\n * [TimelineBuilder]\n * This class is responsible for tracking the styles and building a series of keyframe objects for a\n * timeline between a start and end time. The builder starts off with an initial timeline and each\n * time the AST comes across a `group()`, `keyframes()` or a combination of the two within a\n * `sequence()` then it will generate a sub timeline for each step as well as a new one after\n * they are complete.\n *\n * As the AST is traversed, the timing state on each of the timelines will be incremented. If a sub\n * timeline was created (based on one of the cases above) then the parent timeline will attempt to\n * merge the styles used within the sub timelines into itself (only with group() this will happen).\n * This happens with a merge operation (much like how the merge works in mergeSort) and it will only\n * copy the most recently used styles from the sub timelines into the parent timeline. This ensures\n * that if the styles are used later on in another phase of the animation then they will be the most\n * up-to-date values.\n *\n * [How Missing Styles Are Updated]\n * Each timeline has a `backFill` property which is responsible for filling in new styles into\n * already processed keyframes if a new style shows up later within the animation sequence.\n *\n * ```\n * sequence([\n *   style({ width: 0 }),\n *   animate(1000, style({ width: 100 })),\n *   animate(1000, style({ width: 200 })),\n *   animate(1000, style({ width: 300 }))\n *   animate(1000, style({ width: 400, height: 400 })) // notice how `height` doesn't exist anywhere\n * else\n * ])\n * ```\n *\n * What is happening here is that the `height` value is added later in the sequence, but is missing\n * from all previous animation steps. Therefore when a keyframe is created it would also be missing\n * from all previous keyframes up until where it is first used. For the timeline keyframe generation\n * to properly fill in the style it will place the previous value (the value from the parent\n * timeline) or a default value of `*` into the backFill map. The `copyStyles` method in util.ts\n * handles propagating that backfill map to the styles object.\n *\n * When a sub-timeline is created it will have its own backFill property. This is done so that\n * styles present within the sub-timeline do not accidentally seep into the previous/future timeline\n * keyframes\n *\n * [Validation]\n * The code in this file is not responsible for validation. That functionality happens with within\n * the `AnimationValidatorVisitor` code.\n */\n\nfunction buildAnimationTimelines(driver, rootElement, ast, enterClassName, leaveClassName, startingStyles = new Map(), finalStyles = new Map(), options, subInstructions, errors = []) {\n  return new AnimationTimelineBuilderVisitor().buildKeyframes(driver, rootElement, ast, enterClassName, leaveClassName, startingStyles, finalStyles, options, subInstructions, errors);\n}\n\nclass AnimationTimelineBuilderVisitor {\n  buildKeyframes(driver, rootElement, ast, enterClassName, leaveClassName, startingStyles, finalStyles, options, subInstructions, errors = []) {\n    subInstructions = subInstructions || new ElementInstructionMap();\n    const context = new AnimationTimelineContext(driver, rootElement, subInstructions, enterClassName, leaveClassName, errors, []);\n    context.options = options;\n    const delay = options.delay ? resolveTimingValue(options.delay) : 0;\n    context.currentTimeline.delayNextStep(delay);\n    context.currentTimeline.setStyles([startingStyles], null, context.errors, options);\n    visitDslNode(this, ast, context); // this checks to see if an actual animation happened\n\n    const timelines = context.timelines.filter(timeline => timeline.containsAnimation()); // note: we just want to apply the final styles for the rootElement, so we do not\n    //       just apply the styles to the last timeline but the last timeline which\n    //       element is the root one (basically `*`-styles are replaced with the actual\n    //       state style values only for the root element)\n\n    if (timelines.length && finalStyles.size) {\n      let lastRootTimeline;\n\n      for (let i = timelines.length - 1; i >= 0; i--) {\n        const timeline = timelines[i];\n\n        if (timeline.element === rootElement) {\n          lastRootTimeline = timeline;\n          break;\n        }\n      }\n\n      if (lastRootTimeline && !lastRootTimeline.allowOnlyTimelineStyles()) {\n        lastRootTimeline.setStyles([finalStyles], null, context.errors, options);\n      }\n    }\n\n    return timelines.length ? timelines.map(timeline => timeline.buildKeyframes()) : [createTimelineInstruction(rootElement, [], [], [], 0, delay, '', false)];\n  }\n\n  visitTrigger(ast, context) {// these values are not visited in this AST\n  }\n\n  visitState(ast, context) {// these values are not visited in this AST\n  }\n\n  visitTransition(ast, context) {// these values are not visited in this AST\n  }\n\n  visitAnimateChild(ast, context) {\n    const elementInstructions = context.subInstructions.get(context.element);\n\n    if (elementInstructions) {\n      const innerContext = context.createSubContext(ast.options);\n      const startTime = context.currentTimeline.currentTime;\n\n      const endTime = this._visitSubInstructions(elementInstructions, innerContext, innerContext.options);\n\n      if (startTime != endTime) {\n        // we do this on the upper context because we created a sub context for\n        // the sub child animations\n        context.transformIntoNewTimeline(endTime);\n      }\n    }\n\n    context.previousNode = ast;\n  }\n\n  visitAnimateRef(ast, context) {\n    const innerContext = context.createSubContext(ast.options);\n    innerContext.transformIntoNewTimeline();\n\n    this._applyAnimationRefDelays([ast.options, ast.animation.options], context, innerContext);\n\n    this.visitReference(ast.animation, innerContext);\n    context.transformIntoNewTimeline(innerContext.currentTimeline.currentTime);\n    context.previousNode = ast;\n  }\n\n  _applyAnimationRefDelays(animationsRefsOptions, context, innerContext) {\n    for (const animationRefOptions of animationsRefsOptions) {\n      const animationDelay = animationRefOptions?.delay;\n\n      if (animationDelay) {\n        const animationDelayValue = typeof animationDelay === 'number' ? animationDelay : resolveTimingValue(interpolateParams(animationDelay, animationRefOptions?.params ?? {}, context.errors));\n        innerContext.delayNextStep(animationDelayValue);\n      }\n    }\n  }\n\n  _visitSubInstructions(instructions, context, options) {\n    const startTime = context.currentTimeline.currentTime;\n    let furthestTime = startTime; // this is a special-case for when a user wants to skip a sub\n    // animation from being fired entirely.\n\n    const duration = options.duration != null ? resolveTimingValue(options.duration) : null;\n    const delay = options.delay != null ? resolveTimingValue(options.delay) : null;\n\n    if (duration !== 0) {\n      instructions.forEach(instruction => {\n        const instructionTimings = context.appendInstructionToTimeline(instruction, duration, delay);\n        furthestTime = Math.max(furthestTime, instructionTimings.duration + instructionTimings.delay);\n      });\n    }\n\n    return furthestTime;\n  }\n\n  visitReference(ast, context) {\n    context.updateOptions(ast.options, true);\n    visitDslNode(this, ast.animation, context);\n    context.previousNode = ast;\n  }\n\n  visitSequence(ast, context) {\n    const subContextCount = context.subContextCount;\n    let ctx = context;\n    const options = ast.options;\n\n    if (options && (options.params || options.delay)) {\n      ctx = context.createSubContext(options);\n      ctx.transformIntoNewTimeline();\n\n      if (options.delay != null) {\n        if (ctx.previousNode.type == 6\n        /* AnimationMetadataType.Style */\n        ) {\n          ctx.currentTimeline.snapshotCurrentStyles();\n          ctx.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n        }\n\n        const delay = resolveTimingValue(options.delay);\n        ctx.delayNextStep(delay);\n      }\n    }\n\n    if (ast.steps.length) {\n      ast.steps.forEach(s => visitDslNode(this, s, ctx)); // this is here just in case the inner steps only contain or end with a style() call\n\n      ctx.currentTimeline.applyStylesToKeyframe(); // this means that some animation function within the sequence\n      // ended up creating a sub timeline (which means the current\n      // timeline cannot overlap with the contents of the sequence)\n\n      if (ctx.subContextCount > subContextCount) {\n        ctx.transformIntoNewTimeline();\n      }\n    }\n\n    context.previousNode = ast;\n  }\n\n  visitGroup(ast, context) {\n    const innerTimelines = [];\n    let furthestTime = context.currentTimeline.currentTime;\n    const delay = ast.options && ast.options.delay ? resolveTimingValue(ast.options.delay) : 0;\n    ast.steps.forEach(s => {\n      const innerContext = context.createSubContext(ast.options);\n\n      if (delay) {\n        innerContext.delayNextStep(delay);\n      }\n\n      visitDslNode(this, s, innerContext);\n      furthestTime = Math.max(furthestTime, innerContext.currentTimeline.currentTime);\n      innerTimelines.push(innerContext.currentTimeline);\n    }); // this operation is run after the AST loop because otherwise\n    // if the parent timeline's collected styles were updated then\n    // it would pass in invalid data into the new-to-be forked items\n\n    innerTimelines.forEach(timeline => context.currentTimeline.mergeTimelineCollectedStyles(timeline));\n    context.transformIntoNewTimeline(furthestTime);\n    context.previousNode = ast;\n  }\n\n  _visitTiming(ast, context) {\n    if (ast.dynamic) {\n      const strValue = ast.strValue;\n      const timingValue = context.params ? interpolateParams(strValue, context.params, context.errors) : strValue;\n      return resolveTiming(timingValue, context.errors);\n    } else {\n      return {\n        duration: ast.duration,\n        delay: ast.delay,\n        easing: ast.easing\n      };\n    }\n  }\n\n  visitAnimate(ast, context) {\n    const timings = context.currentAnimateTimings = this._visitTiming(ast.timings, context);\n\n    const timeline = context.currentTimeline;\n\n    if (timings.delay) {\n      context.incrementTime(timings.delay);\n      timeline.snapshotCurrentStyles();\n    }\n\n    const style = ast.style;\n\n    if (style.type == 5\n    /* AnimationMetadataType.Keyframes */\n    ) {\n      this.visitKeyframes(style, context);\n    } else {\n      context.incrementTime(timings.duration);\n      this.visitStyle(style, context);\n      timeline.applyStylesToKeyframe();\n    }\n\n    context.currentAnimateTimings = null;\n    context.previousNode = ast;\n  }\n\n  visitStyle(ast, context) {\n    const timeline = context.currentTimeline;\n    const timings = context.currentAnimateTimings; // this is a special case for when a style() call\n    // directly follows  an animate() call (but not inside of an animate() call)\n\n    if (!timings && timeline.hasCurrentStyleProperties()) {\n      timeline.forwardFrame();\n    }\n\n    const easing = timings && timings.easing || ast.easing;\n\n    if (ast.isEmptyStep) {\n      timeline.applyEmptyStep(easing);\n    } else {\n      timeline.setStyles(ast.styles, easing, context.errors, context.options);\n    }\n\n    context.previousNode = ast;\n  }\n\n  visitKeyframes(ast, context) {\n    const currentAnimateTimings = context.currentAnimateTimings;\n    const startTime = context.currentTimeline.duration;\n    const duration = currentAnimateTimings.duration;\n    const innerContext = context.createSubContext();\n    const innerTimeline = innerContext.currentTimeline;\n    innerTimeline.easing = currentAnimateTimings.easing;\n    ast.styles.forEach(step => {\n      const offset = step.offset || 0;\n      innerTimeline.forwardTime(offset * duration);\n      innerTimeline.setStyles(step.styles, step.easing, context.errors, context.options);\n      innerTimeline.applyStylesToKeyframe();\n    }); // this will ensure that the parent timeline gets all the styles from\n    // the child even if the new timeline below is not used\n\n    context.currentTimeline.mergeTimelineCollectedStyles(innerTimeline); // we do this because the window between this timeline and the sub timeline\n    // should ensure that the styles within are exactly the same as they were before\n\n    context.transformIntoNewTimeline(startTime + duration);\n    context.previousNode = ast;\n  }\n\n  visitQuery(ast, context) {\n    // in the event that the first step before this is a style step we need\n    // to ensure the styles are applied before the children are animated\n    const startTime = context.currentTimeline.currentTime;\n    const options = ast.options || {};\n    const delay = options.delay ? resolveTimingValue(options.delay) : 0;\n\n    if (delay && (context.previousNode.type === 6\n    /* AnimationMetadataType.Style */\n    || startTime == 0 && context.currentTimeline.hasCurrentStyleProperties())) {\n      context.currentTimeline.snapshotCurrentStyles();\n      context.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n    }\n\n    let furthestTime = startTime;\n    const elms = context.invokeQuery(ast.selector, ast.originalSelector, ast.limit, ast.includeSelf, options.optional ? true : false, context.errors);\n    context.currentQueryTotal = elms.length;\n    let sameElementTimeline = null;\n    elms.forEach((element, i) => {\n      context.currentQueryIndex = i;\n      const innerContext = context.createSubContext(ast.options, element);\n\n      if (delay) {\n        innerContext.delayNextStep(delay);\n      }\n\n      if (element === context.element) {\n        sameElementTimeline = innerContext.currentTimeline;\n      }\n\n      visitDslNode(this, ast.animation, innerContext); // this is here just incase the inner steps only contain or end\n      // with a style() call (which is here to signal that this is a preparatory\n      // call to style an element before it is animated again)\n\n      innerContext.currentTimeline.applyStylesToKeyframe();\n      const endTime = innerContext.currentTimeline.currentTime;\n      furthestTime = Math.max(furthestTime, endTime);\n    });\n    context.currentQueryIndex = 0;\n    context.currentQueryTotal = 0;\n    context.transformIntoNewTimeline(furthestTime);\n\n    if (sameElementTimeline) {\n      context.currentTimeline.mergeTimelineCollectedStyles(sameElementTimeline);\n      context.currentTimeline.snapshotCurrentStyles();\n    }\n\n    context.previousNode = ast;\n  }\n\n  visitStagger(ast, context) {\n    const parentContext = context.parentContext;\n    const tl = context.currentTimeline;\n    const timings = ast.timings;\n    const duration = Math.abs(timings.duration);\n    const maxTime = duration * (context.currentQueryTotal - 1);\n    let delay = duration * context.currentQueryIndex;\n    let staggerTransformer = timings.duration < 0 ? 'reverse' : timings.easing;\n\n    switch (staggerTransformer) {\n      case 'reverse':\n        delay = maxTime - delay;\n        break;\n\n      case 'full':\n        delay = parentContext.currentStaggerTime;\n        break;\n    }\n\n    const timeline = context.currentTimeline;\n\n    if (delay) {\n      timeline.delayNextStep(delay);\n    }\n\n    const startingTime = timeline.currentTime;\n    visitDslNode(this, ast.animation, context);\n    context.previousNode = ast; // time = duration + delay\n    // the reason why this computation is so complex is because\n    // the inner timeline may either have a delay value or a stretched\n    // keyframe depending on if a subtimeline is not used or is used.\n\n    parentContext.currentStaggerTime = tl.currentTime - startingTime + (tl.startTime - parentContext.currentTimeline.startTime);\n  }\n\n}\n\nconst DEFAULT_NOOP_PREVIOUS_NODE = {};\n\nclass AnimationTimelineContext {\n  constructor(_driver, element, subInstructions, _enterClassName, _leaveClassName, errors, timelines, initialTimeline) {\n    this._driver = _driver;\n    this.element = element;\n    this.subInstructions = subInstructions;\n    this._enterClassName = _enterClassName;\n    this._leaveClassName = _leaveClassName;\n    this.errors = errors;\n    this.timelines = timelines;\n    this.parentContext = null;\n    this.currentAnimateTimings = null;\n    this.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n    this.subContextCount = 0;\n    this.options = {};\n    this.currentQueryIndex = 0;\n    this.currentQueryTotal = 0;\n    this.currentStaggerTime = 0;\n    this.currentTimeline = initialTimeline || new TimelineBuilder(this._driver, element, 0);\n    timelines.push(this.currentTimeline);\n  }\n\n  get params() {\n    return this.options.params;\n  }\n\n  updateOptions(options, skipIfExists) {\n    if (!options) return;\n    const newOptions = options;\n    let optionsToUpdate = this.options; // NOTE: this will get patched up when other animation methods support duration overrides\n\n    if (newOptions.duration != null) {\n      optionsToUpdate.duration = resolveTimingValue(newOptions.duration);\n    }\n\n    if (newOptions.delay != null) {\n      optionsToUpdate.delay = resolveTimingValue(newOptions.delay);\n    }\n\n    const newParams = newOptions.params;\n\n    if (newParams) {\n      let paramsToUpdate = optionsToUpdate.params;\n\n      if (!paramsToUpdate) {\n        paramsToUpdate = this.options.params = {};\n      }\n\n      Object.keys(newParams).forEach(name => {\n        if (!skipIfExists || !paramsToUpdate.hasOwnProperty(name)) {\n          paramsToUpdate[name] = interpolateParams(newParams[name], paramsToUpdate, this.errors);\n        }\n      });\n    }\n  }\n\n  _copyOptions() {\n    const options = {};\n\n    if (this.options) {\n      const oldParams = this.options.params;\n\n      if (oldParams) {\n        const params = options['params'] = {};\n        Object.keys(oldParams).forEach(name => {\n          params[name] = oldParams[name];\n        });\n      }\n    }\n\n    return options;\n  }\n\n  createSubContext(options = null, element, newTime) {\n    const target = element || this.element;\n    const context = new AnimationTimelineContext(this._driver, target, this.subInstructions, this._enterClassName, this._leaveClassName, this.errors, this.timelines, this.currentTimeline.fork(target, newTime || 0));\n    context.previousNode = this.previousNode;\n    context.currentAnimateTimings = this.currentAnimateTimings;\n    context.options = this._copyOptions();\n    context.updateOptions(options);\n    context.currentQueryIndex = this.currentQueryIndex;\n    context.currentQueryTotal = this.currentQueryTotal;\n    context.parentContext = this;\n    this.subContextCount++;\n    return context;\n  }\n\n  transformIntoNewTimeline(newTime) {\n    this.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n    this.currentTimeline = this.currentTimeline.fork(this.element, newTime);\n    this.timelines.push(this.currentTimeline);\n    return this.currentTimeline;\n  }\n\n  appendInstructionToTimeline(instruction, duration, delay) {\n    const updatedTimings = {\n      duration: duration != null ? duration : instruction.duration,\n      delay: this.currentTimeline.currentTime + (delay != null ? delay : 0) + instruction.delay,\n      easing: ''\n    };\n    const builder = new SubTimelineBuilder(this._driver, instruction.element, instruction.keyframes, instruction.preStyleProps, instruction.postStyleProps, updatedTimings, instruction.stretchStartingKeyframe);\n    this.timelines.push(builder);\n    return updatedTimings;\n  }\n\n  incrementTime(time) {\n    this.currentTimeline.forwardTime(this.currentTimeline.duration + time);\n  }\n\n  delayNextStep(delay) {\n    // negative delays are not yet supported\n    if (delay > 0) {\n      this.currentTimeline.delayNextStep(delay);\n    }\n  }\n\n  invokeQuery(selector, originalSelector, limit, includeSelf, optional, errors) {\n    let results = [];\n\n    if (includeSelf) {\n      results.push(this.element);\n    }\n\n    if (selector.length > 0) {\n      // only if :self is used then the selector can be empty\n      selector = selector.replace(ENTER_TOKEN_REGEX, '.' + this._enterClassName);\n      selector = selector.replace(LEAVE_TOKEN_REGEX, '.' + this._leaveClassName);\n      const multi = limit != 1;\n\n      let elements = this._driver.query(this.element, selector, multi);\n\n      if (limit !== 0) {\n        elements = limit < 0 ? elements.slice(elements.length + limit, elements.length) : elements.slice(0, limit);\n      }\n\n      results.push(...elements);\n    }\n\n    if (!optional && results.length == 0) {\n      errors.push(invalidQuery(originalSelector));\n    }\n\n    return results;\n  }\n\n}\n\nclass TimelineBuilder {\n  constructor(_driver, element, startTime, _elementTimelineStylesLookup) {\n    this._driver = _driver;\n    this.element = element;\n    this.startTime = startTime;\n    this._elementTimelineStylesLookup = _elementTimelineStylesLookup;\n    this.duration = 0;\n    this._previousKeyframe = new Map();\n    this._currentKeyframe = new Map();\n    this._keyframes = new Map();\n    this._styleSummary = new Map();\n    this._localTimelineStyles = new Map();\n    this._pendingStyles = new Map();\n    this._backFill = new Map();\n    this._currentEmptyStepKeyframe = null;\n\n    if (!this._elementTimelineStylesLookup) {\n      this._elementTimelineStylesLookup = new Map();\n    }\n\n    this._globalTimelineStyles = this._elementTimelineStylesLookup.get(element);\n\n    if (!this._globalTimelineStyles) {\n      this._globalTimelineStyles = this._localTimelineStyles;\n\n      this._elementTimelineStylesLookup.set(element, this._localTimelineStyles);\n    }\n\n    this._loadKeyframe();\n  }\n\n  containsAnimation() {\n    switch (this._keyframes.size) {\n      case 0:\n        return false;\n\n      case 1:\n        return this.hasCurrentStyleProperties();\n\n      default:\n        return true;\n    }\n  }\n\n  hasCurrentStyleProperties() {\n    return this._currentKeyframe.size > 0;\n  }\n\n  get currentTime() {\n    return this.startTime + this.duration;\n  }\n\n  delayNextStep(delay) {\n    // in the event that a style() step is placed right before a stagger()\n    // and that style() step is the very first style() value in the animation\n    // then we need to make a copy of the keyframe [0, copy, 1] so that the delay\n    // properly applies the style() values to work with the stagger...\n    const hasPreStyleStep = this._keyframes.size === 1 && this._pendingStyles.size;\n\n    if (this.duration || hasPreStyleStep) {\n      this.forwardTime(this.currentTime + delay);\n\n      if (hasPreStyleStep) {\n        this.snapshotCurrentStyles();\n      }\n    } else {\n      this.startTime += delay;\n    }\n  }\n\n  fork(element, currentTime) {\n    this.applyStylesToKeyframe();\n    return new TimelineBuilder(this._driver, element, currentTime || this.currentTime, this._elementTimelineStylesLookup);\n  }\n\n  _loadKeyframe() {\n    if (this._currentKeyframe) {\n      this._previousKeyframe = this._currentKeyframe;\n    }\n\n    this._currentKeyframe = this._keyframes.get(this.duration);\n\n    if (!this._currentKeyframe) {\n      this._currentKeyframe = new Map();\n\n      this._keyframes.set(this.duration, this._currentKeyframe);\n    }\n  }\n\n  forwardFrame() {\n    this.duration += ONE_FRAME_IN_MILLISECONDS;\n\n    this._loadKeyframe();\n  }\n\n  forwardTime(time) {\n    this.applyStylesToKeyframe();\n    this.duration = time;\n\n    this._loadKeyframe();\n  }\n\n  _updateStyle(prop, value) {\n    this._localTimelineStyles.set(prop, value);\n\n    this._globalTimelineStyles.set(prop, value);\n\n    this._styleSummary.set(prop, {\n      time: this.currentTime,\n      value\n    });\n  }\n\n  allowOnlyTimelineStyles() {\n    return this._currentEmptyStepKeyframe !== this._currentKeyframe;\n  }\n\n  applyEmptyStep(easing) {\n    if (easing) {\n      this._previousKeyframe.set('easing', easing);\n    } // special case for animate(duration):\n    // all missing styles are filled with a `*` value then\n    // if any destination styles are filled in later on the same\n    // keyframe then they will override the overridden styles\n    // We use `_globalTimelineStyles` here because there may be\n    // styles in previous keyframes that are not present in this timeline\n\n\n    for (let [prop, value] of this._globalTimelineStyles) {\n      this._backFill.set(prop, value || AUTO_STYLE);\n\n      this._currentKeyframe.set(prop, AUTO_STYLE);\n    }\n\n    this._currentEmptyStepKeyframe = this._currentKeyframe;\n  }\n\n  setStyles(input, easing, errors, options) {\n    if (easing) {\n      this._previousKeyframe.set('easing', easing);\n    }\n\n    const params = options && options.params || {};\n    const styles = flattenStyles(input, this._globalTimelineStyles);\n\n    for (let [prop, value] of styles) {\n      const val = interpolateParams(value, params, errors);\n\n      this._pendingStyles.set(prop, val);\n\n      if (!this._localTimelineStyles.has(prop)) {\n        this._backFill.set(prop, this._globalTimelineStyles.get(prop) ?? AUTO_STYLE);\n      }\n\n      this._updateStyle(prop, val);\n    }\n  }\n\n  applyStylesToKeyframe() {\n    if (this._pendingStyles.size == 0) return;\n\n    this._pendingStyles.forEach((val, prop) => {\n      this._currentKeyframe.set(prop, val);\n    });\n\n    this._pendingStyles.clear();\n\n    this._localTimelineStyles.forEach((val, prop) => {\n      if (!this._currentKeyframe.has(prop)) {\n        this._currentKeyframe.set(prop, val);\n      }\n    });\n  }\n\n  snapshotCurrentStyles() {\n    for (let [prop, val] of this._localTimelineStyles) {\n      this._pendingStyles.set(prop, val);\n\n      this._updateStyle(prop, val);\n    }\n  }\n\n  getFinalKeyframe() {\n    return this._keyframes.get(this.duration);\n  }\n\n  get properties() {\n    const properties = [];\n\n    for (let prop in this._currentKeyframe) {\n      properties.push(prop);\n    }\n\n    return properties;\n  }\n\n  mergeTimelineCollectedStyles(timeline) {\n    timeline._styleSummary.forEach((details1, prop) => {\n      const details0 = this._styleSummary.get(prop);\n\n      if (!details0 || details1.time > details0.time) {\n        this._updateStyle(prop, details1.value);\n      }\n    });\n  }\n\n  buildKeyframes() {\n    this.applyStylesToKeyframe();\n    const preStyleProps = new Set();\n    const postStyleProps = new Set();\n    const isEmpty = this._keyframes.size === 1 && this.duration === 0;\n    let finalKeyframes = [];\n\n    this._keyframes.forEach((keyframe, time) => {\n      const finalKeyframe = copyStyles(keyframe, new Map(), this._backFill);\n      finalKeyframe.forEach((value, prop) => {\n        if (value === ɵPRE_STYLE) {\n          preStyleProps.add(prop);\n        } else if (value === AUTO_STYLE) {\n          postStyleProps.add(prop);\n        }\n      });\n\n      if (!isEmpty) {\n        finalKeyframe.set('offset', time / this.duration);\n      }\n\n      finalKeyframes.push(finalKeyframe);\n    });\n\n    const preProps = preStyleProps.size ? iteratorToArray(preStyleProps.values()) : [];\n    const postProps = postStyleProps.size ? iteratorToArray(postStyleProps.values()) : []; // special case for a 0-second animation (which is designed just to place styles onscreen)\n\n    if (isEmpty) {\n      const kf0 = finalKeyframes[0];\n      const kf1 = new Map(kf0);\n      kf0.set('offset', 0);\n      kf1.set('offset', 1);\n      finalKeyframes = [kf0, kf1];\n    }\n\n    return createTimelineInstruction(this.element, finalKeyframes, preProps, postProps, this.duration, this.startTime, this.easing, false);\n  }\n\n}\n\nclass SubTimelineBuilder extends TimelineBuilder {\n  constructor(driver, element, keyframes, preStyleProps, postStyleProps, timings, _stretchStartingKeyframe = false) {\n    super(driver, element, timings.delay);\n    this.keyframes = keyframes;\n    this.preStyleProps = preStyleProps;\n    this.postStyleProps = postStyleProps;\n    this._stretchStartingKeyframe = _stretchStartingKeyframe;\n    this.timings = {\n      duration: timings.duration,\n      delay: timings.delay,\n      easing: timings.easing\n    };\n  }\n\n  containsAnimation() {\n    return this.keyframes.length > 1;\n  }\n\n  buildKeyframes() {\n    let keyframes = this.keyframes;\n    let {\n      delay,\n      duration,\n      easing\n    } = this.timings;\n\n    if (this._stretchStartingKeyframe && delay) {\n      const newKeyframes = [];\n      const totalTime = duration + delay;\n      const startingGap = delay / totalTime; // the original starting keyframe now starts once the delay is done\n\n      const newFirstKeyframe = copyStyles(keyframes[0]);\n      newFirstKeyframe.set('offset', 0);\n      newKeyframes.push(newFirstKeyframe);\n      const oldFirstKeyframe = copyStyles(keyframes[0]);\n      oldFirstKeyframe.set('offset', roundOffset(startingGap));\n      newKeyframes.push(oldFirstKeyframe);\n      /*\n        When the keyframe is stretched then it means that the delay before the animation\n        starts is gone. Instead the first keyframe is placed at the start of the animation\n        and it is then copied to where it starts when the original delay is over. This basically\n        means nothing animates during that delay, but the styles are still rendered. For this\n        to work the original offset values that exist in the original keyframes must be \"warped\"\n        so that they can take the new keyframe + delay into account.\n               delay=1000, duration=1000, keyframes = 0 .5 1\n               turns into\n               delay=0, duration=2000, keyframes = 0 .33 .66 1\n       */\n      // offsets between 1 ... n -1 are all warped by the keyframe stretch\n\n      const limit = keyframes.length - 1;\n\n      for (let i = 1; i <= limit; i++) {\n        let kf = copyStyles(keyframes[i]);\n        const oldOffset = kf.get('offset');\n        const timeAtKeyframe = delay + oldOffset * duration;\n        kf.set('offset', roundOffset(timeAtKeyframe / totalTime));\n        newKeyframes.push(kf);\n      } // the new starting keyframe should be added at the start\n\n\n      duration = totalTime;\n      delay = 0;\n      easing = '';\n      keyframes = newKeyframes;\n    }\n\n    return createTimelineInstruction(this.element, keyframes, this.preStyleProps, this.postStyleProps, duration, delay, easing, true);\n  }\n\n}\n\nfunction roundOffset(offset, decimalPoints = 3) {\n  const mult = Math.pow(10, decimalPoints - 1);\n  return Math.round(offset * mult) / mult;\n}\n\nfunction flattenStyles(input, allStyles) {\n  const styles = new Map();\n  let allProperties;\n  input.forEach(token => {\n    if (token === '*') {\n      allProperties = allProperties || allStyles.keys();\n\n      for (let prop of allProperties) {\n        styles.set(prop, AUTO_STYLE);\n      }\n    } else {\n      copyStyles(token, styles);\n    }\n  });\n  return styles;\n}\n\nclass Animation {\n  constructor(_driver, input) {\n    this._driver = _driver;\n    const errors = [];\n    const warnings = [];\n    const ast = buildAnimationAst(_driver, input, errors, warnings);\n\n    if (errors.length) {\n      throw validationFailed(errors);\n    }\n\n    if (warnings.length) {\n      warnValidation(warnings);\n    }\n\n    this._animationAst = ast;\n  }\n\n  buildTimelines(element, startingStyles, destinationStyles, options, subInstructions) {\n    const start = Array.isArray(startingStyles) ? normalizeStyles(startingStyles) : startingStyles;\n    const dest = Array.isArray(destinationStyles) ? normalizeStyles(destinationStyles) : destinationStyles;\n    const errors = [];\n    subInstructions = subInstructions || new ElementInstructionMap();\n    const result = buildAnimationTimelines(this._driver, element, this._animationAst, ENTER_CLASSNAME, LEAVE_CLASSNAME, start, dest, options, subInstructions, errors);\n\n    if (errors.length) {\n      throw buildingFailed(errors);\n    }\n\n    return result;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @publicApi\n */\n\n\nclass AnimationStyleNormalizer {}\n/**\n * @publicApi\n */\n\n\nclass NoopAnimationStyleNormalizer {\n  normalizePropertyName(propertyName, errors) {\n    return propertyName;\n  }\n\n  normalizeStyleValue(userProvidedProperty, normalizedProperty, value, errors) {\n    return value;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst DIMENSIONAL_PROP_SET = /*#__PURE__*/new Set(['width', 'height', 'minWidth', 'minHeight', 'maxWidth', 'maxHeight', 'left', 'top', 'bottom', 'right', 'fontSize', 'outlineWidth', 'outlineOffset', 'paddingTop', 'paddingLeft', 'paddingBottom', 'paddingRight', 'marginTop', 'marginLeft', 'marginBottom', 'marginRight', 'borderRadius', 'borderWidth', 'borderTopWidth', 'borderLeftWidth', 'borderRightWidth', 'borderBottomWidth', 'textIndent', 'perspective']);\n\nclass WebAnimationsStyleNormalizer extends AnimationStyleNormalizer {\n  normalizePropertyName(propertyName, errors) {\n    return dashCaseToCamelCase(propertyName);\n  }\n\n  normalizeStyleValue(userProvidedProperty, normalizedProperty, value, errors) {\n    let unit = '';\n    const strVal = value.toString().trim();\n\n    if (DIMENSIONAL_PROP_SET.has(normalizedProperty) && value !== 0 && value !== '0') {\n      if (typeof value === 'number') {\n        unit = 'px';\n      } else {\n        const valAndSuffixMatch = value.match(/^[+-]?[\\d\\.]+([a-z]*)$/);\n\n        if (valAndSuffixMatch && valAndSuffixMatch[1].length == 0) {\n          errors.push(invalidCssUnitValue(userProvidedProperty, value));\n        }\n      }\n    }\n\n    return strVal + unit;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction createTransitionInstruction(element, triggerName, fromState, toState, isRemovalTransition, fromStyles, toStyles, timelines, queriedElements, preStyleProps, postStyleProps, totalTime, errors) {\n  return {\n    type: 0\n    /* AnimationTransitionInstructionType.TransitionAnimation */\n    ,\n    element,\n    triggerName,\n    isRemovalTransition,\n    fromState,\n    fromStyles,\n    toState,\n    toStyles,\n    timelines,\n    queriedElements,\n    preStyleProps,\n    postStyleProps,\n    totalTime,\n    errors\n  };\n}\n\nconst EMPTY_OBJECT = {};\n\nclass AnimationTransitionFactory {\n  constructor(_triggerName, ast, _stateStyles) {\n    this._triggerName = _triggerName;\n    this.ast = ast;\n    this._stateStyles = _stateStyles;\n  }\n\n  match(currentState, nextState, element, params) {\n    return oneOrMoreTransitionsMatch(this.ast.matchers, currentState, nextState, element, params);\n  }\n\n  buildStyles(stateName, params, errors) {\n    let styler = this._stateStyles.get('*');\n\n    if (stateName !== undefined) {\n      styler = this._stateStyles.get(stateName?.toString()) || styler;\n    }\n\n    return styler ? styler.buildStyles(params, errors) : new Map();\n  }\n\n  build(driver, element, currentState, nextState, enterClassName, leaveClassName, currentOptions, nextOptions, subInstructions, skipAstBuild) {\n    const errors = [];\n    const transitionAnimationParams = this.ast.options && this.ast.options.params || EMPTY_OBJECT;\n    const currentAnimationParams = currentOptions && currentOptions.params || EMPTY_OBJECT;\n    const currentStateStyles = this.buildStyles(currentState, currentAnimationParams, errors);\n    const nextAnimationParams = nextOptions && nextOptions.params || EMPTY_OBJECT;\n    const nextStateStyles = this.buildStyles(nextState, nextAnimationParams, errors);\n    const queriedElements = new Set();\n    const preStyleMap = new Map();\n    const postStyleMap = new Map();\n    const isRemoval = nextState === 'void';\n    const animationOptions = {\n      params: applyParamDefaults(nextAnimationParams, transitionAnimationParams),\n      delay: this.ast.options?.delay\n    };\n    const timelines = skipAstBuild ? [] : buildAnimationTimelines(driver, element, this.ast.animation, enterClassName, leaveClassName, currentStateStyles, nextStateStyles, animationOptions, subInstructions, errors);\n    let totalTime = 0;\n    timelines.forEach(tl => {\n      totalTime = Math.max(tl.duration + tl.delay, totalTime);\n    });\n\n    if (errors.length) {\n      return createTransitionInstruction(element, this._triggerName, currentState, nextState, isRemoval, currentStateStyles, nextStateStyles, [], [], preStyleMap, postStyleMap, totalTime, errors);\n    }\n\n    timelines.forEach(tl => {\n      const elm = tl.element;\n      const preProps = getOrSetDefaultValue(preStyleMap, elm, new Set());\n      tl.preStyleProps.forEach(prop => preProps.add(prop));\n      const postProps = getOrSetDefaultValue(postStyleMap, elm, new Set());\n      tl.postStyleProps.forEach(prop => postProps.add(prop));\n\n      if (elm !== element) {\n        queriedElements.add(elm);\n      }\n    });\n\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      checkNonAnimatableInTimelines(timelines, this._triggerName, driver);\n    }\n\n    const queriedElementsList = iteratorToArray(queriedElements.values());\n    return createTransitionInstruction(element, this._triggerName, currentState, nextState, isRemoval, currentStateStyles, nextStateStyles, timelines, queriedElementsList, preStyleMap, postStyleMap, totalTime);\n  }\n\n}\n/**\n * Checks inside a set of timelines if they try to animate a css property which is not considered\n * animatable, in that case it prints a warning on the console.\n * Besides that the function doesn't have any other effect.\n *\n * Note: this check is done here after the timelines are built instead of doing on a lower level so\n * that we can make sure that the warning appears only once per instruction (we can aggregate here\n * all the issues instead of finding them separately).\n *\n * @param timelines The built timelines for the current instruction.\n * @param triggerName The name of the trigger for the current instruction.\n * @param driver Animation driver used to perform the check.\n *\n */\n\n\nfunction checkNonAnimatableInTimelines(timelines, triggerName, driver) {\n  if (!driver.validateAnimatableStyleProperty) {\n    return;\n  }\n\n  const invalidNonAnimatableProps = new Set();\n  timelines.forEach(({\n    keyframes\n  }) => {\n    const nonAnimatablePropsInitialValues = new Map();\n    keyframes.forEach(keyframe => {\n      for (const [prop, value] of keyframe.entries()) {\n        if (!driver.validateAnimatableStyleProperty(prop)) {\n          if (nonAnimatablePropsInitialValues.has(prop) && !invalidNonAnimatableProps.has(prop)) {\n            const propInitialValue = nonAnimatablePropsInitialValues.get(prop);\n\n            if (propInitialValue !== value) {\n              invalidNonAnimatableProps.add(prop);\n            }\n          } else {\n            nonAnimatablePropsInitialValues.set(prop, value);\n          }\n        }\n      }\n    });\n  });\n\n  if (invalidNonAnimatableProps.size > 0) {\n    console.warn(`Warning: The animation trigger \"${triggerName}\" is attempting to animate the following` + ' not animatable properties: ' + Array.from(invalidNonAnimatableProps).join(', ') + '\\n' + '(to check the list of all animatable properties visit https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_animated_properties)');\n  }\n}\n\nfunction oneOrMoreTransitionsMatch(matchFns, currentState, nextState, element, params) {\n  return matchFns.some(fn => fn(currentState, nextState, element, params));\n}\n\nfunction applyParamDefaults(userParams, defaults) {\n  const result = copyObj(defaults);\n\n  for (const key in userParams) {\n    if (userParams.hasOwnProperty(key) && userParams[key] != null) {\n      result[key] = userParams[key];\n    }\n  }\n\n  return result;\n}\n\nclass AnimationStateStyles {\n  constructor(styles, defaultParams, normalizer) {\n    this.styles = styles;\n    this.defaultParams = defaultParams;\n    this.normalizer = normalizer;\n  }\n\n  buildStyles(params, errors) {\n    const finalStyles = new Map();\n    const combinedParams = copyObj(this.defaultParams);\n    Object.keys(params).forEach(key => {\n      const value = params[key];\n\n      if (value !== null) {\n        combinedParams[key] = value;\n      }\n    });\n    this.styles.styles.forEach(value => {\n      if (typeof value !== 'string') {\n        value.forEach((val, prop) => {\n          if (val) {\n            val = interpolateParams(val, combinedParams, errors);\n          }\n\n          const normalizedProp = this.normalizer.normalizePropertyName(prop, errors);\n          val = this.normalizer.normalizeStyleValue(prop, normalizedProp, val, errors);\n          finalStyles.set(normalizedProp, val);\n        });\n      }\n    });\n    return finalStyles;\n  }\n\n}\n\nfunction buildTrigger(name, ast, normalizer) {\n  return new AnimationTrigger(name, ast, normalizer);\n}\n\nclass AnimationTrigger {\n  constructor(name, ast, _normalizer) {\n    this.name = name;\n    this.ast = ast;\n    this._normalizer = _normalizer;\n    this.transitionFactories = [];\n    this.states = new Map();\n    ast.states.forEach(ast => {\n      const defaultParams = ast.options && ast.options.params || {};\n      this.states.set(ast.name, new AnimationStateStyles(ast.style, defaultParams, _normalizer));\n    });\n    balanceProperties(this.states, 'true', '1');\n    balanceProperties(this.states, 'false', '0');\n    ast.transitions.forEach(ast => {\n      this.transitionFactories.push(new AnimationTransitionFactory(name, ast, this.states));\n    });\n    this.fallbackTransition = createFallbackTransition(name, this.states, this._normalizer);\n  }\n\n  get containsQueries() {\n    return this.ast.queryCount > 0;\n  }\n\n  matchTransition(currentState, nextState, element, params) {\n    const entry = this.transitionFactories.find(f => f.match(currentState, nextState, element, params));\n    return entry || null;\n  }\n\n  matchStyles(currentState, params, errors) {\n    return this.fallbackTransition.buildStyles(currentState, params, errors);\n  }\n\n}\n\nfunction createFallbackTransition(triggerName, states, normalizer) {\n  const matchers = [(fromState, toState) => true];\n  const animation = {\n    type: 2\n    /* AnimationMetadataType.Sequence */\n    ,\n    steps: [],\n    options: null\n  };\n  const transition = {\n    type: 1\n    /* AnimationMetadataType.Transition */\n    ,\n    animation,\n    matchers,\n    options: null,\n    queryCount: 0,\n    depCount: 0\n  };\n  return new AnimationTransitionFactory(triggerName, transition, states);\n}\n\nfunction balanceProperties(stateMap, key1, key2) {\n  if (stateMap.has(key1)) {\n    if (!stateMap.has(key2)) {\n      stateMap.set(key2, stateMap.get(key1));\n    }\n  } else if (stateMap.has(key2)) {\n    stateMap.set(key1, stateMap.get(key2));\n  }\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst EMPTY_INSTRUCTION_MAP = /*#__PURE__*/new ElementInstructionMap();\n\nclass TimelineAnimationEngine {\n  constructor(bodyNode, _driver, _normalizer) {\n    this.bodyNode = bodyNode;\n    this._driver = _driver;\n    this._normalizer = _normalizer;\n    this._animations = new Map();\n    this._playersById = new Map();\n    this.players = [];\n  }\n\n  register(id, metadata) {\n    const errors = [];\n    const warnings = [];\n    const ast = buildAnimationAst(this._driver, metadata, errors, warnings);\n\n    if (errors.length) {\n      throw registerFailed(errors);\n    } else {\n      if (warnings.length) {\n        warnRegister(warnings);\n      }\n\n      this._animations.set(id, ast);\n    }\n  }\n\n  _buildPlayer(i, preStyles, postStyles) {\n    const element = i.element;\n    const keyframes = normalizeKeyframes$1(this._driver, this._normalizer, element, i.keyframes, preStyles, postStyles);\n    return this._driver.animate(element, keyframes, i.duration, i.delay, i.easing, [], true);\n  }\n\n  create(id, element, options = {}) {\n    const errors = [];\n\n    const ast = this._animations.get(id);\n\n    let instructions;\n    const autoStylesMap = new Map();\n\n    if (ast) {\n      instructions = buildAnimationTimelines(this._driver, element, ast, ENTER_CLASSNAME, LEAVE_CLASSNAME, new Map(), new Map(), options, EMPTY_INSTRUCTION_MAP, errors);\n      instructions.forEach(inst => {\n        const styles = getOrSetDefaultValue(autoStylesMap, inst.element, new Map());\n        inst.postStyleProps.forEach(prop => styles.set(prop, null));\n      });\n    } else {\n      errors.push(missingOrDestroyedAnimation());\n      instructions = [];\n    }\n\n    if (errors.length) {\n      throw createAnimationFailed(errors);\n    }\n\n    autoStylesMap.forEach((styles, element) => {\n      styles.forEach((_, prop) => {\n        styles.set(prop, this._driver.computeStyle(element, prop, AUTO_STYLE));\n      });\n    });\n    const players = instructions.map(i => {\n      const styles = autoStylesMap.get(i.element);\n      return this._buildPlayer(i, new Map(), styles);\n    });\n    const player = optimizeGroupPlayer(players);\n\n    this._playersById.set(id, player);\n\n    player.onDestroy(() => this.destroy(id));\n    this.players.push(player);\n    return player;\n  }\n\n  destroy(id) {\n    const player = this._getPlayer(id);\n\n    player.destroy();\n\n    this._playersById.delete(id);\n\n    const index = this.players.indexOf(player);\n\n    if (index >= 0) {\n      this.players.splice(index, 1);\n    }\n  }\n\n  _getPlayer(id) {\n    const player = this._playersById.get(id);\n\n    if (!player) {\n      throw missingPlayer(id);\n    }\n\n    return player;\n  }\n\n  listen(id, element, eventName, callback) {\n    // triggerName, fromState, toState are all ignored for timeline animations\n    const baseEvent = makeAnimationEvent(element, '', '', '');\n    listenOnPlayer(this._getPlayer(id), eventName, baseEvent, callback);\n    return () => {};\n  }\n\n  command(id, element, command, args) {\n    if (command == 'register') {\n      this.register(id, args[0]);\n      return;\n    }\n\n    if (command == 'create') {\n      const options = args[0] || {};\n      this.create(id, element, options);\n      return;\n    }\n\n    const player = this._getPlayer(id);\n\n    switch (command) {\n      case 'play':\n        player.play();\n        break;\n\n      case 'pause':\n        player.pause();\n        break;\n\n      case 'reset':\n        player.reset();\n        break;\n\n      case 'restart':\n        player.restart();\n        break;\n\n      case 'finish':\n        player.finish();\n        break;\n\n      case 'init':\n        player.init();\n        break;\n\n      case 'setPosition':\n        player.setPosition(parseFloat(args[0]));\n        break;\n\n      case 'destroy':\n        this.destroy(id);\n        break;\n    }\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst QUEUED_CLASSNAME = 'ng-animate-queued';\nconst QUEUED_SELECTOR = '.ng-animate-queued';\nconst DISABLED_CLASSNAME = 'ng-animate-disabled';\nconst DISABLED_SELECTOR = '.ng-animate-disabled';\nconst STAR_CLASSNAME = 'ng-star-inserted';\nconst STAR_SELECTOR = '.ng-star-inserted';\nconst EMPTY_PLAYER_ARRAY = [];\nconst NULL_REMOVAL_STATE = {\n  namespaceId: '',\n  setForRemoval: false,\n  setForMove: false,\n  hasAnimation: false,\n  removedBeforeQueried: false\n};\nconst NULL_REMOVED_QUERIED_STATE = {\n  namespaceId: '',\n  setForMove: false,\n  setForRemoval: false,\n  hasAnimation: false,\n  removedBeforeQueried: true\n};\nconst REMOVAL_FLAG = '__ng_removed';\n\nclass StateValue {\n  constructor(input, namespaceId = '') {\n    this.namespaceId = namespaceId;\n    const isObj = input && input.hasOwnProperty('value');\n    const value = isObj ? input['value'] : input;\n    this.value = normalizeTriggerValue(value);\n\n    if (isObj) {\n      const options = copyObj(input);\n      delete options['value'];\n      this.options = options;\n    } else {\n      this.options = {};\n    }\n\n    if (!this.options.params) {\n      this.options.params = {};\n    }\n  }\n\n  get params() {\n    return this.options.params;\n  }\n\n  absorbOptions(options) {\n    const newParams = options.params;\n\n    if (newParams) {\n      const oldParams = this.options.params;\n      Object.keys(newParams).forEach(prop => {\n        if (oldParams[prop] == null) {\n          oldParams[prop] = newParams[prop];\n        }\n      });\n    }\n  }\n\n}\n\nconst VOID_VALUE = 'void';\nconst DEFAULT_STATE_VALUE = /*#__PURE__*/new StateValue(VOID_VALUE);\n\nclass AnimationTransitionNamespace {\n  constructor(id, hostElement, _engine) {\n    this.id = id;\n    this.hostElement = hostElement;\n    this._engine = _engine;\n    this.players = [];\n    this._triggers = new Map();\n    this._queue = [];\n    this._elementListeners = new Map();\n    this._hostClassName = 'ng-tns-' + id;\n    addClass(hostElement, this._hostClassName);\n  }\n\n  listen(element, name, phase, callback) {\n    if (!this._triggers.has(name)) {\n      throw missingTrigger(phase, name);\n    }\n\n    if (phase == null || phase.length == 0) {\n      throw missingEvent(name);\n    }\n\n    if (!isTriggerEventValid(phase)) {\n      throw unsupportedTriggerEvent(phase, name);\n    }\n\n    const listeners = getOrSetDefaultValue(this._elementListeners, element, []);\n    const data = {\n      name,\n      phase,\n      callback\n    };\n    listeners.push(data);\n    const triggersWithStates = getOrSetDefaultValue(this._engine.statesByElement, element, new Map());\n\n    if (!triggersWithStates.has(name)) {\n      addClass(element, NG_TRIGGER_CLASSNAME);\n      addClass(element, NG_TRIGGER_CLASSNAME + '-' + name);\n      triggersWithStates.set(name, DEFAULT_STATE_VALUE);\n    }\n\n    return () => {\n      // the event listener is removed AFTER the flush has occurred such\n      // that leave animations callbacks can fire (otherwise if the node\n      // is removed in between then the listeners would be deregistered)\n      this._engine.afterFlush(() => {\n        const index = listeners.indexOf(data);\n\n        if (index >= 0) {\n          listeners.splice(index, 1);\n        }\n\n        if (!this._triggers.has(name)) {\n          triggersWithStates.delete(name);\n        }\n      });\n    };\n  }\n\n  register(name, ast) {\n    if (this._triggers.has(name)) {\n      // throw\n      return false;\n    } else {\n      this._triggers.set(name, ast);\n\n      return true;\n    }\n  }\n\n  _getTrigger(name) {\n    const trigger = this._triggers.get(name);\n\n    if (!trigger) {\n      throw unregisteredTrigger(name);\n    }\n\n    return trigger;\n  }\n\n  trigger(element, triggerName, value, defaultToFallback = true) {\n    const trigger = this._getTrigger(triggerName);\n\n    const player = new TransitionAnimationPlayer(this.id, triggerName, element);\n\n    let triggersWithStates = this._engine.statesByElement.get(element);\n\n    if (!triggersWithStates) {\n      addClass(element, NG_TRIGGER_CLASSNAME);\n      addClass(element, NG_TRIGGER_CLASSNAME + '-' + triggerName);\n\n      this._engine.statesByElement.set(element, triggersWithStates = new Map());\n    }\n\n    let fromState = triggersWithStates.get(triggerName);\n    const toState = new StateValue(value, this.id);\n    const isObj = value && value.hasOwnProperty('value');\n\n    if (!isObj && fromState) {\n      toState.absorbOptions(fromState.options);\n    }\n\n    triggersWithStates.set(triggerName, toState);\n\n    if (!fromState) {\n      fromState = DEFAULT_STATE_VALUE;\n    }\n\n    const isRemoval = toState.value === VOID_VALUE; // normally this isn't reached by here, however, if an object expression\n    // is passed in then it may be a new object each time. Comparing the value\n    // is important since that will stay the same despite there being a new object.\n    // The removal arc here is special cased because the same element is triggered\n    // twice in the event that it contains animations on the outer/inner portions\n    // of the host container\n\n    if (!isRemoval && fromState.value === toState.value) {\n      // this means that despite the value not changing, some inner params\n      // have changed which means that the animation final styles need to be applied\n      if (!objEquals(fromState.params, toState.params)) {\n        const errors = [];\n        const fromStyles = trigger.matchStyles(fromState.value, fromState.params, errors);\n        const toStyles = trigger.matchStyles(toState.value, toState.params, errors);\n\n        if (errors.length) {\n          this._engine.reportError(errors);\n        } else {\n          this._engine.afterFlush(() => {\n            eraseStyles(element, fromStyles);\n            setStyles(element, toStyles);\n          });\n        }\n      }\n\n      return;\n    }\n\n    const playersOnElement = getOrSetDefaultValue(this._engine.playersByElement, element, []);\n    playersOnElement.forEach(player => {\n      // only remove the player if it is queued on the EXACT same trigger/namespace\n      // we only also deal with queued players here because if the animation has\n      // started then we want to keep the player alive until the flush happens\n      // (which is where the previousPlayers are passed into the new player)\n      if (player.namespaceId == this.id && player.triggerName == triggerName && player.queued) {\n        player.destroy();\n      }\n    });\n    let transition = trigger.matchTransition(fromState.value, toState.value, element, toState.params);\n    let isFallbackTransition = false;\n\n    if (!transition) {\n      if (!defaultToFallback) return;\n      transition = trigger.fallbackTransition;\n      isFallbackTransition = true;\n    }\n\n    this._engine.totalQueuedPlayers++;\n\n    this._queue.push({\n      element,\n      triggerName,\n      transition,\n      fromState,\n      toState,\n      player,\n      isFallbackTransition\n    });\n\n    if (!isFallbackTransition) {\n      addClass(element, QUEUED_CLASSNAME);\n      player.onStart(() => {\n        removeClass(element, QUEUED_CLASSNAME);\n      });\n    }\n\n    player.onDone(() => {\n      let index = this.players.indexOf(player);\n\n      if (index >= 0) {\n        this.players.splice(index, 1);\n      }\n\n      const players = this._engine.playersByElement.get(element);\n\n      if (players) {\n        let index = players.indexOf(player);\n\n        if (index >= 0) {\n          players.splice(index, 1);\n        }\n      }\n    });\n    this.players.push(player);\n    playersOnElement.push(player);\n    return player;\n  }\n\n  deregister(name) {\n    this._triggers.delete(name);\n\n    this._engine.statesByElement.forEach(stateMap => stateMap.delete(name));\n\n    this._elementListeners.forEach((listeners, element) => {\n      this._elementListeners.set(element, listeners.filter(entry => {\n        return entry.name != name;\n      }));\n    });\n  }\n\n  clearElementCache(element) {\n    this._engine.statesByElement.delete(element);\n\n    this._elementListeners.delete(element);\n\n    const elementPlayers = this._engine.playersByElement.get(element);\n\n    if (elementPlayers) {\n      elementPlayers.forEach(player => player.destroy());\n\n      this._engine.playersByElement.delete(element);\n    }\n  }\n\n  _signalRemovalForInnerTriggers(rootElement, context) {\n    const elements = this._engine.driver.query(rootElement, NG_TRIGGER_SELECTOR, true); // emulate a leave animation for all inner nodes within this node.\n    // If there are no animations found for any of the nodes then clear the cache\n    // for the element.\n\n\n    elements.forEach(elm => {\n      // this means that an inner remove() operation has already kicked off\n      // the animation on this element...\n      if (elm[REMOVAL_FLAG]) return;\n\n      const namespaces = this._engine.fetchNamespacesByElement(elm);\n\n      if (namespaces.size) {\n        namespaces.forEach(ns => ns.triggerLeaveAnimation(elm, context, false, true));\n      } else {\n        this.clearElementCache(elm);\n      }\n    }); // If the child elements were removed along with the parent, their animations might not\n    // have completed. Clear all the elements from the cache so we don't end up with a memory leak.\n\n    this._engine.afterFlushAnimationsDone(() => elements.forEach(elm => this.clearElementCache(elm)));\n  }\n\n  triggerLeaveAnimation(element, context, destroyAfterComplete, defaultToFallback) {\n    const triggerStates = this._engine.statesByElement.get(element);\n\n    const previousTriggersValues = new Map();\n\n    if (triggerStates) {\n      const players = [];\n      triggerStates.forEach((state, triggerName) => {\n        previousTriggersValues.set(triggerName, state.value); // this check is here in the event that an element is removed\n        // twice (both on the host level and the component level)\n\n        if (this._triggers.has(triggerName)) {\n          const player = this.trigger(element, triggerName, VOID_VALUE, defaultToFallback);\n\n          if (player) {\n            players.push(player);\n          }\n        }\n      });\n\n      if (players.length) {\n        this._engine.markElementAsRemoved(this.id, element, true, context, previousTriggersValues);\n\n        if (destroyAfterComplete) {\n          optimizeGroupPlayer(players).onDone(() => this._engine.processLeaveNode(element));\n        }\n\n        return true;\n      }\n    }\n\n    return false;\n  }\n\n  prepareLeaveAnimationListeners(element) {\n    const listeners = this._elementListeners.get(element);\n\n    const elementStates = this._engine.statesByElement.get(element); // if this statement fails then it means that the element was picked up\n    // by an earlier flush (or there are no listeners at all to track the leave).\n\n\n    if (listeners && elementStates) {\n      const visitedTriggers = new Set();\n      listeners.forEach(listener => {\n        const triggerName = listener.name;\n        if (visitedTriggers.has(triggerName)) return;\n        visitedTriggers.add(triggerName);\n\n        const trigger = this._triggers.get(triggerName);\n\n        const transition = trigger.fallbackTransition;\n        const fromState = elementStates.get(triggerName) || DEFAULT_STATE_VALUE;\n        const toState = new StateValue(VOID_VALUE);\n        const player = new TransitionAnimationPlayer(this.id, triggerName, element);\n        this._engine.totalQueuedPlayers++;\n\n        this._queue.push({\n          element,\n          triggerName,\n          transition,\n          fromState,\n          toState,\n          player,\n          isFallbackTransition: true\n        });\n      });\n    }\n  }\n\n  removeNode(element, context) {\n    const engine = this._engine;\n\n    if (element.childElementCount) {\n      this._signalRemovalForInnerTriggers(element, context);\n    } // this means that a * => VOID animation was detected and kicked off\n\n\n    if (this.triggerLeaveAnimation(element, context, true)) return; // find the player that is animating and make sure that the\n    // removal is delayed until that player has completed\n\n    let containsPotentialParentTransition = false;\n\n    if (engine.totalAnimations) {\n      const currentPlayers = engine.players.length ? engine.playersByQueriedElement.get(element) : []; // when this `if statement` does not continue forward it means that\n      // a previous animation query has selected the current element and\n      // is animating it. In this situation want to continue forwards and\n      // allow the element to be queued up for animation later.\n\n      if (currentPlayers && currentPlayers.length) {\n        containsPotentialParentTransition = true;\n      } else {\n        let parent = element;\n\n        while (parent = parent.parentNode) {\n          const triggers = engine.statesByElement.get(parent);\n\n          if (triggers) {\n            containsPotentialParentTransition = true;\n            break;\n          }\n        }\n      }\n    } // at this stage we know that the element will either get removed\n    // during flush or will be picked up by a parent query. Either way\n    // we need to fire the listeners for this element when it DOES get\n    // removed (once the query parent animation is done or after flush)\n\n\n    this.prepareLeaveAnimationListeners(element); // whether or not a parent has an animation we need to delay the deferral of the leave\n    // operation until we have more information (which we do after flush() has been called)\n\n    if (containsPotentialParentTransition) {\n      engine.markElementAsRemoved(this.id, element, false, context);\n    } else {\n      const removalFlag = element[REMOVAL_FLAG];\n\n      if (!removalFlag || removalFlag === NULL_REMOVAL_STATE) {\n        // we do this after the flush has occurred such\n        // that the callbacks can be fired\n        engine.afterFlush(() => this.clearElementCache(element));\n        engine.destroyInnerAnimations(element);\n\n        engine._onRemovalComplete(element, context);\n      }\n    }\n  }\n\n  insertNode(element, parent) {\n    addClass(element, this._hostClassName);\n  }\n\n  drainQueuedTransitions(microtaskId) {\n    const instructions = [];\n\n    this._queue.forEach(entry => {\n      const player = entry.player;\n      if (player.destroyed) return;\n      const element = entry.element;\n\n      const listeners = this._elementListeners.get(element);\n\n      if (listeners) {\n        listeners.forEach(listener => {\n          if (listener.name == entry.triggerName) {\n            const baseEvent = makeAnimationEvent(element, entry.triggerName, entry.fromState.value, entry.toState.value);\n            baseEvent['_data'] = microtaskId;\n            listenOnPlayer(entry.player, listener.phase, baseEvent, listener.callback);\n          }\n        });\n      }\n\n      if (player.markedForDestroy) {\n        this._engine.afterFlush(() => {\n          // now we can destroy the element properly since the event listeners have\n          // been bound to the player\n          player.destroy();\n        });\n      } else {\n        instructions.push(entry);\n      }\n    });\n\n    this._queue = [];\n    return instructions.sort((a, b) => {\n      // if depCount == 0 them move to front\n      // otherwise if a contains b then move back\n      const d0 = a.transition.ast.depCount;\n      const d1 = b.transition.ast.depCount;\n\n      if (d0 == 0 || d1 == 0) {\n        return d0 - d1;\n      }\n\n      return this._engine.driver.containsElement(a.element, b.element) ? 1 : -1;\n    });\n  }\n\n  destroy(context) {\n    this.players.forEach(p => p.destroy());\n\n    this._signalRemovalForInnerTriggers(this.hostElement, context);\n  }\n\n  elementContainsData(element) {\n    let containsData = false;\n    if (this._elementListeners.has(element)) containsData = true;\n    containsData = (this._queue.find(entry => entry.element === element) ? true : false) || containsData;\n    return containsData;\n  }\n\n}\n\nclass TransitionAnimationEngine {\n  constructor(bodyNode, driver, _normalizer) {\n    this.bodyNode = bodyNode;\n    this.driver = driver;\n    this._normalizer = _normalizer;\n    this.players = [];\n    this.newHostElements = new Map();\n    this.playersByElement = new Map();\n    this.playersByQueriedElement = new Map();\n    this.statesByElement = new Map();\n    this.disabledNodes = new Set();\n    this.totalAnimations = 0;\n    this.totalQueuedPlayers = 0;\n    this._namespaceLookup = {};\n    this._namespaceList = [];\n    this._flushFns = [];\n    this._whenQuietFns = [];\n    this.namespacesByHostElement = new Map();\n    this.collectedEnterElements = [];\n    this.collectedLeaveElements = []; // this method is designed to be overridden by the code that uses this engine\n\n    this.onRemovalComplete = (element, context) => {};\n  }\n  /** @internal */\n\n\n  _onRemovalComplete(element, context) {\n    this.onRemovalComplete(element, context);\n  }\n\n  get queuedPlayers() {\n    const players = [];\n\n    this._namespaceList.forEach(ns => {\n      ns.players.forEach(player => {\n        if (player.queued) {\n          players.push(player);\n        }\n      });\n    });\n\n    return players;\n  }\n\n  createNamespace(namespaceId, hostElement) {\n    const ns = new AnimationTransitionNamespace(namespaceId, hostElement, this);\n\n    if (this.bodyNode && this.driver.containsElement(this.bodyNode, hostElement)) {\n      this._balanceNamespaceList(ns, hostElement);\n    } else {\n      // defer this later until flush during when the host element has\n      // been inserted so that we know exactly where to place it in\n      // the namespace list\n      this.newHostElements.set(hostElement, ns); // given that this host element is a part of the animation code, it\n      // may or may not be inserted by a parent node that is of an\n      // animation renderer type. If this happens then we can still have\n      // access to this item when we query for :enter nodes. If the parent\n      // is a renderer then the set data-structure will normalize the entry\n\n      this.collectEnterElement(hostElement);\n    }\n\n    return this._namespaceLookup[namespaceId] = ns;\n  }\n\n  _balanceNamespaceList(ns, hostElement) {\n    const namespaceList = this._namespaceList;\n    const namespacesByHostElement = this.namespacesByHostElement;\n    const limit = namespaceList.length - 1;\n\n    if (limit >= 0) {\n      let found = false; // Find the closest ancestor with an existing namespace so we can then insert `ns` after it,\n      // establishing a top-down ordering of namespaces in `this._namespaceList`.\n\n      let ancestor = this.driver.getParentElement(hostElement);\n\n      while (ancestor) {\n        const ancestorNs = namespacesByHostElement.get(ancestor);\n\n        if (ancestorNs) {\n          // An animation namespace has been registered for this ancestor, so we insert `ns`\n          // right after it to establish top-down ordering of animation namespaces.\n          const index = namespaceList.indexOf(ancestorNs);\n          namespaceList.splice(index + 1, 0, ns);\n          found = true;\n          break;\n        }\n\n        ancestor = this.driver.getParentElement(ancestor);\n      }\n\n      if (!found) {\n        // No namespace exists that is an ancestor of `ns`, so `ns` is inserted at the front to\n        // ensure that any existing descendants are ordered after `ns`, retaining the desired\n        // top-down ordering.\n        namespaceList.unshift(ns);\n      }\n    } else {\n      namespaceList.push(ns);\n    }\n\n    namespacesByHostElement.set(hostElement, ns);\n    return ns;\n  }\n\n  register(namespaceId, hostElement) {\n    let ns = this._namespaceLookup[namespaceId];\n\n    if (!ns) {\n      ns = this.createNamespace(namespaceId, hostElement);\n    }\n\n    return ns;\n  }\n\n  registerTrigger(namespaceId, name, trigger) {\n    let ns = this._namespaceLookup[namespaceId];\n\n    if (ns && ns.register(name, trigger)) {\n      this.totalAnimations++;\n    }\n  }\n\n  destroy(namespaceId, context) {\n    if (!namespaceId) return;\n\n    const ns = this._fetchNamespace(namespaceId);\n\n    this.afterFlush(() => {\n      this.namespacesByHostElement.delete(ns.hostElement);\n      delete this._namespaceLookup[namespaceId];\n\n      const index = this._namespaceList.indexOf(ns);\n\n      if (index >= 0) {\n        this._namespaceList.splice(index, 1);\n      }\n    });\n    this.afterFlushAnimationsDone(() => ns.destroy(context));\n  }\n\n  _fetchNamespace(id) {\n    return this._namespaceLookup[id];\n  }\n\n  fetchNamespacesByElement(element) {\n    // normally there should only be one namespace per element, however\n    // if @triggers are placed on both the component element and then\n    // its host element (within the component code) then there will be\n    // two namespaces returned. We use a set here to simply deduplicate\n    // the namespaces in case (for the reason described above) there are multiple triggers\n    const namespaces = new Set();\n    const elementStates = this.statesByElement.get(element);\n\n    if (elementStates) {\n      for (let stateValue of elementStates.values()) {\n        if (stateValue.namespaceId) {\n          const ns = this._fetchNamespace(stateValue.namespaceId);\n\n          if (ns) {\n            namespaces.add(ns);\n          }\n        }\n      }\n    }\n\n    return namespaces;\n  }\n\n  trigger(namespaceId, element, name, value) {\n    if (isElementNode(element)) {\n      const ns = this._fetchNamespace(namespaceId);\n\n      if (ns) {\n        ns.trigger(element, name, value);\n        return true;\n      }\n    }\n\n    return false;\n  }\n\n  insertNode(namespaceId, element, parent, insertBefore) {\n    if (!isElementNode(element)) return; // special case for when an element is removed and reinserted (move operation)\n    // when this occurs we do not want to use the element for deletion later\n\n    const details = element[REMOVAL_FLAG];\n\n    if (details && details.setForRemoval) {\n      details.setForRemoval = false;\n      details.setForMove = true;\n      const index = this.collectedLeaveElements.indexOf(element);\n\n      if (index >= 0) {\n        this.collectedLeaveElements.splice(index, 1);\n      }\n    } // in the event that the namespaceId is blank then the caller\n    // code does not contain any animation code in it, but it is\n    // just being called so that the node is marked as being inserted\n\n\n    if (namespaceId) {\n      const ns = this._fetchNamespace(namespaceId); // This if-statement is a workaround for router issue #21947.\n      // The router sometimes hits a race condition where while a route\n      // is being instantiated a new navigation arrives, triggering leave\n      // animation of DOM that has not been fully initialized, until this\n      // is resolved, we need to handle the scenario when DOM is not in a\n      // consistent state during the animation.\n\n\n      if (ns) {\n        ns.insertNode(element, parent);\n      }\n    } // only *directives and host elements are inserted before\n\n\n    if (insertBefore) {\n      this.collectEnterElement(element);\n    }\n  }\n\n  collectEnterElement(element) {\n    this.collectedEnterElements.push(element);\n  }\n\n  markElementAsDisabled(element, value) {\n    if (value) {\n      if (!this.disabledNodes.has(element)) {\n        this.disabledNodes.add(element);\n        addClass(element, DISABLED_CLASSNAME);\n      }\n    } else if (this.disabledNodes.has(element)) {\n      this.disabledNodes.delete(element);\n      removeClass(element, DISABLED_CLASSNAME);\n    }\n  }\n\n  removeNode(namespaceId, element, isHostElement, context) {\n    if (isElementNode(element)) {\n      const ns = namespaceId ? this._fetchNamespace(namespaceId) : null;\n\n      if (ns) {\n        ns.removeNode(element, context);\n      } else {\n        this.markElementAsRemoved(namespaceId, element, false, context);\n      }\n\n      if (isHostElement) {\n        const hostNS = this.namespacesByHostElement.get(element);\n\n        if (hostNS && hostNS.id !== namespaceId) {\n          hostNS.removeNode(element, context);\n        }\n      }\n    } else {\n      this._onRemovalComplete(element, context);\n    }\n  }\n\n  markElementAsRemoved(namespaceId, element, hasAnimation, context, previousTriggersValues) {\n    this.collectedLeaveElements.push(element);\n    element[REMOVAL_FLAG] = {\n      namespaceId,\n      setForRemoval: context,\n      hasAnimation,\n      removedBeforeQueried: false,\n      previousTriggersValues\n    };\n  }\n\n  listen(namespaceId, element, name, phase, callback) {\n    if (isElementNode(element)) {\n      return this._fetchNamespace(namespaceId).listen(element, name, phase, callback);\n    }\n\n    return () => {};\n  }\n\n  _buildInstruction(entry, subTimelines, enterClassName, leaveClassName, skipBuildAst) {\n    return entry.transition.build(this.driver, entry.element, entry.fromState.value, entry.toState.value, enterClassName, leaveClassName, entry.fromState.options, entry.toState.options, subTimelines, skipBuildAst);\n  }\n\n  destroyInnerAnimations(containerElement) {\n    let elements = this.driver.query(containerElement, NG_TRIGGER_SELECTOR, true);\n    elements.forEach(element => this.destroyActiveAnimationsForElement(element));\n    if (this.playersByQueriedElement.size == 0) return;\n    elements = this.driver.query(containerElement, NG_ANIMATING_SELECTOR, true);\n    elements.forEach(element => this.finishActiveQueriedAnimationOnElement(element));\n  }\n\n  destroyActiveAnimationsForElement(element) {\n    const players = this.playersByElement.get(element);\n\n    if (players) {\n      players.forEach(player => {\n        // special case for when an element is set for destruction, but hasn't started.\n        // in this situation we want to delay the destruction until the flush occurs\n        // so that any event listeners attached to the player are triggered.\n        if (player.queued) {\n          player.markedForDestroy = true;\n        } else {\n          player.destroy();\n        }\n      });\n    }\n  }\n\n  finishActiveQueriedAnimationOnElement(element) {\n    const players = this.playersByQueriedElement.get(element);\n\n    if (players) {\n      players.forEach(player => player.finish());\n    }\n  }\n\n  whenRenderingDone() {\n    return new Promise(resolve => {\n      if (this.players.length) {\n        return optimizeGroupPlayer(this.players).onDone(() => resolve());\n      } else {\n        resolve();\n      }\n    });\n  }\n\n  processLeaveNode(element) {\n    const details = element[REMOVAL_FLAG];\n\n    if (details && details.setForRemoval) {\n      // this will prevent it from removing it twice\n      element[REMOVAL_FLAG] = NULL_REMOVAL_STATE;\n\n      if (details.namespaceId) {\n        this.destroyInnerAnimations(element);\n\n        const ns = this._fetchNamespace(details.namespaceId);\n\n        if (ns) {\n          ns.clearElementCache(element);\n        }\n      }\n\n      this._onRemovalComplete(element, details.setForRemoval);\n    }\n\n    if (element.classList?.contains(DISABLED_CLASSNAME)) {\n      this.markElementAsDisabled(element, false);\n    }\n\n    this.driver.query(element, DISABLED_SELECTOR, true).forEach(node => {\n      this.markElementAsDisabled(node, false);\n    });\n  }\n\n  flush(microtaskId = -1) {\n    let players = [];\n\n    if (this.newHostElements.size) {\n      this.newHostElements.forEach((ns, element) => this._balanceNamespaceList(ns, element));\n      this.newHostElements.clear();\n    }\n\n    if (this.totalAnimations && this.collectedEnterElements.length) {\n      for (let i = 0; i < this.collectedEnterElements.length; i++) {\n        const elm = this.collectedEnterElements[i];\n        addClass(elm, STAR_CLASSNAME);\n      }\n    }\n\n    if (this._namespaceList.length && (this.totalQueuedPlayers || this.collectedLeaveElements.length)) {\n      const cleanupFns = [];\n\n      try {\n        players = this._flushAnimations(cleanupFns, microtaskId);\n      } finally {\n        for (let i = 0; i < cleanupFns.length; i++) {\n          cleanupFns[i]();\n        }\n      }\n    } else {\n      for (let i = 0; i < this.collectedLeaveElements.length; i++) {\n        const element = this.collectedLeaveElements[i];\n        this.processLeaveNode(element);\n      }\n    }\n\n    this.totalQueuedPlayers = 0;\n    this.collectedEnterElements.length = 0;\n    this.collectedLeaveElements.length = 0;\n\n    this._flushFns.forEach(fn => fn());\n\n    this._flushFns = [];\n\n    if (this._whenQuietFns.length) {\n      // we move these over to a variable so that\n      // if any new callbacks are registered in another\n      // flush they do not populate the existing set\n      const quietFns = this._whenQuietFns;\n      this._whenQuietFns = [];\n\n      if (players.length) {\n        optimizeGroupPlayer(players).onDone(() => {\n          quietFns.forEach(fn => fn());\n        });\n      } else {\n        quietFns.forEach(fn => fn());\n      }\n    }\n  }\n\n  reportError(errors) {\n    throw triggerTransitionsFailed(errors);\n  }\n\n  _flushAnimations(cleanupFns, microtaskId) {\n    const subTimelines = new ElementInstructionMap();\n    const skippedPlayers = [];\n    const skippedPlayersMap = new Map();\n    const queuedInstructions = [];\n    const queriedElements = new Map();\n    const allPreStyleElements = new Map();\n    const allPostStyleElements = new Map();\n    const disabledElementsSet = new Set();\n    this.disabledNodes.forEach(node => {\n      disabledElementsSet.add(node);\n      const nodesThatAreDisabled = this.driver.query(node, QUEUED_SELECTOR, true);\n\n      for (let i = 0; i < nodesThatAreDisabled.length; i++) {\n        disabledElementsSet.add(nodesThatAreDisabled[i]);\n      }\n    });\n    const bodyNode = this.bodyNode;\n    const allTriggerElements = Array.from(this.statesByElement.keys());\n    const enterNodeMap = buildRootMap(allTriggerElements, this.collectedEnterElements); // this must occur before the instructions are built below such that\n    // the :enter queries match the elements (since the timeline queries\n    // are fired during instruction building).\n\n    const enterNodeMapIds = new Map();\n    let i = 0;\n    enterNodeMap.forEach((nodes, root) => {\n      const className = ENTER_CLASSNAME + i++;\n      enterNodeMapIds.set(root, className);\n      nodes.forEach(node => addClass(node, className));\n    });\n    const allLeaveNodes = [];\n    const mergedLeaveNodes = new Set();\n    const leaveNodesWithoutAnimations = new Set();\n\n    for (let i = 0; i < this.collectedLeaveElements.length; i++) {\n      const element = this.collectedLeaveElements[i];\n      const details = element[REMOVAL_FLAG];\n\n      if (details && details.setForRemoval) {\n        allLeaveNodes.push(element);\n        mergedLeaveNodes.add(element);\n\n        if (details.hasAnimation) {\n          this.driver.query(element, STAR_SELECTOR, true).forEach(elm => mergedLeaveNodes.add(elm));\n        } else {\n          leaveNodesWithoutAnimations.add(element);\n        }\n      }\n    }\n\n    const leaveNodeMapIds = new Map();\n    const leaveNodeMap = buildRootMap(allTriggerElements, Array.from(mergedLeaveNodes));\n    leaveNodeMap.forEach((nodes, root) => {\n      const className = LEAVE_CLASSNAME + i++;\n      leaveNodeMapIds.set(root, className);\n      nodes.forEach(node => addClass(node, className));\n    });\n    cleanupFns.push(() => {\n      enterNodeMap.forEach((nodes, root) => {\n        const className = enterNodeMapIds.get(root);\n        nodes.forEach(node => removeClass(node, className));\n      });\n      leaveNodeMap.forEach((nodes, root) => {\n        const className = leaveNodeMapIds.get(root);\n        nodes.forEach(node => removeClass(node, className));\n      });\n      allLeaveNodes.forEach(element => {\n        this.processLeaveNode(element);\n      });\n    });\n    const allPlayers = [];\n    const erroneousTransitions = [];\n\n    for (let i = this._namespaceList.length - 1; i >= 0; i--) {\n      const ns = this._namespaceList[i];\n      ns.drainQueuedTransitions(microtaskId).forEach(entry => {\n        const player = entry.player;\n        const element = entry.element;\n        allPlayers.push(player);\n\n        if (this.collectedEnterElements.length) {\n          const details = element[REMOVAL_FLAG]; // animations for move operations (elements being removed and reinserted,\n          // e.g. when the order of an *ngFor list changes) are currently not supported\n\n          if (details && details.setForMove) {\n            if (details.previousTriggersValues && details.previousTriggersValues.has(entry.triggerName)) {\n              const previousValue = details.previousTriggersValues.get(entry.triggerName); // we need to restore the previous trigger value since the element has\n              // only been moved and hasn't actually left the DOM\n\n              const triggersWithStates = this.statesByElement.get(entry.element);\n\n              if (triggersWithStates && triggersWithStates.has(entry.triggerName)) {\n                const state = triggersWithStates.get(entry.triggerName);\n                state.value = previousValue;\n                triggersWithStates.set(entry.triggerName, state);\n              }\n            }\n\n            player.destroy();\n            return;\n          }\n        }\n\n        const nodeIsOrphaned = !bodyNode || !this.driver.containsElement(bodyNode, element);\n        const leaveClassName = leaveNodeMapIds.get(element);\n        const enterClassName = enterNodeMapIds.get(element);\n\n        const instruction = this._buildInstruction(entry, subTimelines, enterClassName, leaveClassName, nodeIsOrphaned);\n\n        if (instruction.errors && instruction.errors.length) {\n          erroneousTransitions.push(instruction);\n          return;\n        } // even though the element may not be in the DOM, it may still\n        // be added at a later point (due to the mechanics of content\n        // projection and/or dynamic component insertion) therefore it's\n        // important to still style the element.\n\n\n        if (nodeIsOrphaned) {\n          player.onStart(() => eraseStyles(element, instruction.fromStyles));\n          player.onDestroy(() => setStyles(element, instruction.toStyles));\n          skippedPlayers.push(player);\n          return;\n        } // if an unmatched transition is queued and ready to go\n        // then it SHOULD NOT render an animation and cancel the\n        // previously running animations.\n\n\n        if (entry.isFallbackTransition) {\n          player.onStart(() => eraseStyles(element, instruction.fromStyles));\n          player.onDestroy(() => setStyles(element, instruction.toStyles));\n          skippedPlayers.push(player);\n          return;\n        } // this means that if a parent animation uses this animation as a sub-trigger\n        // then it will instruct the timeline builder not to add a player delay, but\n        // instead stretch the first keyframe gap until the animation starts. This is\n        // important in order to prevent extra initialization styles from being\n        // required by the user for the animation.\n\n\n        const timelines = [];\n        instruction.timelines.forEach(tl => {\n          tl.stretchStartingKeyframe = true;\n\n          if (!this.disabledNodes.has(tl.element)) {\n            timelines.push(tl);\n          }\n        });\n        instruction.timelines = timelines;\n        subTimelines.append(element, instruction.timelines);\n        const tuple = {\n          instruction,\n          player,\n          element\n        };\n        queuedInstructions.push(tuple);\n        instruction.queriedElements.forEach(element => getOrSetDefaultValue(queriedElements, element, []).push(player));\n        instruction.preStyleProps.forEach((stringMap, element) => {\n          if (stringMap.size) {\n            let setVal = allPreStyleElements.get(element);\n\n            if (!setVal) {\n              allPreStyleElements.set(element, setVal = new Set());\n            }\n\n            stringMap.forEach((_, prop) => setVal.add(prop));\n          }\n        });\n        instruction.postStyleProps.forEach((stringMap, element) => {\n          let setVal = allPostStyleElements.get(element);\n\n          if (!setVal) {\n            allPostStyleElements.set(element, setVal = new Set());\n          }\n\n          stringMap.forEach((_, prop) => setVal.add(prop));\n        });\n      });\n    }\n\n    if (erroneousTransitions.length) {\n      const errors = [];\n      erroneousTransitions.forEach(instruction => {\n        errors.push(transitionFailed(instruction.triggerName, instruction.errors));\n      });\n      allPlayers.forEach(player => player.destroy());\n      this.reportError(errors);\n    }\n\n    const allPreviousPlayersMap = new Map(); // this map tells us which element in the DOM tree is contained by\n    // which animation. Further down this map will get populated once\n    // the players are built and in doing so we can use it to efficiently\n    // figure out if a sub player is skipped due to a parent player having priority.\n\n    const animationElementMap = new Map();\n    queuedInstructions.forEach(entry => {\n      const element = entry.element;\n\n      if (subTimelines.has(element)) {\n        animationElementMap.set(element, element);\n\n        this._beforeAnimationBuild(entry.player.namespaceId, entry.instruction, allPreviousPlayersMap);\n      }\n    });\n    skippedPlayers.forEach(player => {\n      const element = player.element;\n\n      const previousPlayers = this._getPreviousPlayers(element, false, player.namespaceId, player.triggerName, null);\n\n      previousPlayers.forEach(prevPlayer => {\n        getOrSetDefaultValue(allPreviousPlayersMap, element, []).push(prevPlayer);\n        prevPlayer.destroy();\n      });\n    }); // this is a special case for nodes that will be removed either by\n    // having their own leave animations or by being queried in a container\n    // that will be removed once a parent animation is complete. The idea\n    // here is that * styles must be identical to ! styles because of\n    // backwards compatibility (* is also filled in by default in many places).\n    // Otherwise * styles will return an empty value or \"auto\" since the element\n    // passed to getComputedStyle will not be visible (since * === destination)\n\n    const replaceNodes = allLeaveNodes.filter(node => {\n      return replacePostStylesAsPre(node, allPreStyleElements, allPostStyleElements);\n    }); // POST STAGE: fill the * styles\n\n    const postStylesMap = new Map();\n    const allLeaveQueriedNodes = cloakAndComputeStyles(postStylesMap, this.driver, leaveNodesWithoutAnimations, allPostStyleElements, AUTO_STYLE);\n    allLeaveQueriedNodes.forEach(node => {\n      if (replacePostStylesAsPre(node, allPreStyleElements, allPostStyleElements)) {\n        replaceNodes.push(node);\n      }\n    }); // PRE STAGE: fill the ! styles\n\n    const preStylesMap = new Map();\n    enterNodeMap.forEach((nodes, root) => {\n      cloakAndComputeStyles(preStylesMap, this.driver, new Set(nodes), allPreStyleElements, ɵPRE_STYLE);\n    });\n    replaceNodes.forEach(node => {\n      const post = postStylesMap.get(node);\n      const pre = preStylesMap.get(node);\n      postStylesMap.set(node, new Map([...Array.from(post?.entries() ?? []), ...Array.from(pre?.entries() ?? [])]));\n    });\n    const rootPlayers = [];\n    const subPlayers = [];\n    const NO_PARENT_ANIMATION_ELEMENT_DETECTED = {};\n    queuedInstructions.forEach(entry => {\n      const {\n        element,\n        player,\n        instruction\n      } = entry; // this means that it was never consumed by a parent animation which\n      // means that it is independent and therefore should be set for animation\n\n      if (subTimelines.has(element)) {\n        if (disabledElementsSet.has(element)) {\n          player.onDestroy(() => setStyles(element, instruction.toStyles));\n          player.disabled = true;\n          player.overrideTotalTime(instruction.totalTime);\n          skippedPlayers.push(player);\n          return;\n        } // this will flow up the DOM and query the map to figure out\n        // if a parent animation has priority over it. In the situation\n        // that a parent is detected then it will cancel the loop. If\n        // nothing is detected, or it takes a few hops to find a parent,\n        // then it will fill in the missing nodes and signal them as having\n        // a detected parent (or a NO_PARENT value via a special constant).\n\n\n        let parentWithAnimation = NO_PARENT_ANIMATION_ELEMENT_DETECTED;\n\n        if (animationElementMap.size > 1) {\n          let elm = element;\n          const parentsToAdd = [];\n\n          while (elm = elm.parentNode) {\n            const detectedParent = animationElementMap.get(elm);\n\n            if (detectedParent) {\n              parentWithAnimation = detectedParent;\n              break;\n            }\n\n            parentsToAdd.push(elm);\n          }\n\n          parentsToAdd.forEach(parent => animationElementMap.set(parent, parentWithAnimation));\n        }\n\n        const innerPlayer = this._buildAnimation(player.namespaceId, instruction, allPreviousPlayersMap, skippedPlayersMap, preStylesMap, postStylesMap);\n\n        player.setRealPlayer(innerPlayer);\n\n        if (parentWithAnimation === NO_PARENT_ANIMATION_ELEMENT_DETECTED) {\n          rootPlayers.push(player);\n        } else {\n          const parentPlayers = this.playersByElement.get(parentWithAnimation);\n\n          if (parentPlayers && parentPlayers.length) {\n            player.parentPlayer = optimizeGroupPlayer(parentPlayers);\n          }\n\n          skippedPlayers.push(player);\n        }\n      } else {\n        eraseStyles(element, instruction.fromStyles);\n        player.onDestroy(() => setStyles(element, instruction.toStyles)); // there still might be a ancestor player animating this\n        // element therefore we will still add it as a sub player\n        // even if its animation may be disabled\n\n        subPlayers.push(player);\n\n        if (disabledElementsSet.has(element)) {\n          skippedPlayers.push(player);\n        }\n      }\n    }); // find all of the sub players' corresponding inner animation players\n\n    subPlayers.forEach(player => {\n      // even if no players are found for a sub animation it\n      // will still complete itself after the next tick since it's Noop\n      const playersForElement = skippedPlayersMap.get(player.element);\n\n      if (playersForElement && playersForElement.length) {\n        const innerPlayer = optimizeGroupPlayer(playersForElement);\n        player.setRealPlayer(innerPlayer);\n      }\n    }); // the reason why we don't actually play the animation is\n    // because all that a skipped player is designed to do is to\n    // fire the start/done transition callback events\n\n    skippedPlayers.forEach(player => {\n      if (player.parentPlayer) {\n        player.syncPlayerEvents(player.parentPlayer);\n      } else {\n        player.destroy();\n      }\n    }); // run through all of the queued removals and see if they\n    // were picked up by a query. If not then perform the removal\n    // operation right away unless a parent animation is ongoing.\n\n    for (let i = 0; i < allLeaveNodes.length; i++) {\n      const element = allLeaveNodes[i];\n      const details = element[REMOVAL_FLAG];\n      removeClass(element, LEAVE_CLASSNAME); // this means the element has a removal animation that is being\n      // taken care of and therefore the inner elements will hang around\n      // until that animation is over (or the parent queried animation)\n\n      if (details && details.hasAnimation) continue;\n      let players = []; // if this element is queried or if it contains queried children\n      // then we want for the element not to be removed from the page\n      // until the queried animations have finished\n\n      if (queriedElements.size) {\n        let queriedPlayerResults = queriedElements.get(element);\n\n        if (queriedPlayerResults && queriedPlayerResults.length) {\n          players.push(...queriedPlayerResults);\n        }\n\n        let queriedInnerElements = this.driver.query(element, NG_ANIMATING_SELECTOR, true);\n\n        for (let j = 0; j < queriedInnerElements.length; j++) {\n          let queriedPlayers = queriedElements.get(queriedInnerElements[j]);\n\n          if (queriedPlayers && queriedPlayers.length) {\n            players.push(...queriedPlayers);\n          }\n        }\n      }\n\n      const activePlayers = players.filter(p => !p.destroyed);\n\n      if (activePlayers.length) {\n        removeNodesAfterAnimationDone(this, element, activePlayers);\n      } else {\n        this.processLeaveNode(element);\n      }\n    } // this is required so the cleanup method doesn't remove them\n\n\n    allLeaveNodes.length = 0;\n    rootPlayers.forEach(player => {\n      this.players.push(player);\n      player.onDone(() => {\n        player.destroy();\n        const index = this.players.indexOf(player);\n        this.players.splice(index, 1);\n      });\n      player.play();\n    });\n    return rootPlayers;\n  }\n\n  elementContainsData(namespaceId, element) {\n    let containsData = false;\n    const details = element[REMOVAL_FLAG];\n    if (details && details.setForRemoval) containsData = true;\n    if (this.playersByElement.has(element)) containsData = true;\n    if (this.playersByQueriedElement.has(element)) containsData = true;\n    if (this.statesByElement.has(element)) containsData = true;\n    return this._fetchNamespace(namespaceId).elementContainsData(element) || containsData;\n  }\n\n  afterFlush(callback) {\n    this._flushFns.push(callback);\n  }\n\n  afterFlushAnimationsDone(callback) {\n    this._whenQuietFns.push(callback);\n  }\n\n  _getPreviousPlayers(element, isQueriedElement, namespaceId, triggerName, toStateValue) {\n    let players = [];\n\n    if (isQueriedElement) {\n      const queriedElementPlayers = this.playersByQueriedElement.get(element);\n\n      if (queriedElementPlayers) {\n        players = queriedElementPlayers;\n      }\n    } else {\n      const elementPlayers = this.playersByElement.get(element);\n\n      if (elementPlayers) {\n        const isRemovalAnimation = !toStateValue || toStateValue == VOID_VALUE;\n        elementPlayers.forEach(player => {\n          if (player.queued) return;\n          if (!isRemovalAnimation && player.triggerName != triggerName) return;\n          players.push(player);\n        });\n      }\n    }\n\n    if (namespaceId || triggerName) {\n      players = players.filter(player => {\n        if (namespaceId && namespaceId != player.namespaceId) return false;\n        if (triggerName && triggerName != player.triggerName) return false;\n        return true;\n      });\n    }\n\n    return players;\n  }\n\n  _beforeAnimationBuild(namespaceId, instruction, allPreviousPlayersMap) {\n    const triggerName = instruction.triggerName;\n    const rootElement = instruction.element; // when a removal animation occurs, ALL previous players are collected\n    // and destroyed (even if they are outside of the current namespace)\n\n    const targetNameSpaceId = instruction.isRemovalTransition ? undefined : namespaceId;\n    const targetTriggerName = instruction.isRemovalTransition ? undefined : triggerName;\n\n    for (const timelineInstruction of instruction.timelines) {\n      const element = timelineInstruction.element;\n      const isQueriedElement = element !== rootElement;\n      const players = getOrSetDefaultValue(allPreviousPlayersMap, element, []);\n\n      const previousPlayers = this._getPreviousPlayers(element, isQueriedElement, targetNameSpaceId, targetTriggerName, instruction.toState);\n\n      previousPlayers.forEach(player => {\n        const realPlayer = player.getRealPlayer();\n\n        if (realPlayer.beforeDestroy) {\n          realPlayer.beforeDestroy();\n        }\n\n        player.destroy();\n        players.push(player);\n      });\n    } // this needs to be done so that the PRE/POST styles can be\n    // computed properly without interfering with the previous animation\n\n\n    eraseStyles(rootElement, instruction.fromStyles);\n  }\n\n  _buildAnimation(namespaceId, instruction, allPreviousPlayersMap, skippedPlayersMap, preStylesMap, postStylesMap) {\n    const triggerName = instruction.triggerName;\n    const rootElement = instruction.element; // we first run this so that the previous animation player\n    // data can be passed into the successive animation players\n\n    const allQueriedPlayers = [];\n    const allConsumedElements = new Set();\n    const allSubElements = new Set();\n    const allNewPlayers = instruction.timelines.map(timelineInstruction => {\n      const element = timelineInstruction.element;\n      allConsumedElements.add(element); // FIXME (matsko): make sure to-be-removed animations are removed properly\n\n      const details = element[REMOVAL_FLAG];\n      if (details && details.removedBeforeQueried) return new NoopAnimationPlayer(timelineInstruction.duration, timelineInstruction.delay);\n      const isQueriedElement = element !== rootElement;\n      const previousPlayers = flattenGroupPlayers((allPreviousPlayersMap.get(element) || EMPTY_PLAYER_ARRAY).map(p => p.getRealPlayer())).filter(p => {\n        // the `element` is not apart of the AnimationPlayer definition, but\n        // Mock/WebAnimations\n        // use the element within their implementation. This will be added in Angular5 to\n        // AnimationPlayer\n        const pp = p;\n        return pp.element ? pp.element === element : false;\n      });\n      const preStyles = preStylesMap.get(element);\n      const postStyles = postStylesMap.get(element);\n      const keyframes = normalizeKeyframes$1(this.driver, this._normalizer, element, timelineInstruction.keyframes, preStyles, postStyles);\n\n      const player = this._buildPlayer(timelineInstruction, keyframes, previousPlayers); // this means that this particular player belongs to a sub trigger. It is\n      // important that we match this player up with the corresponding (@trigger.listener)\n\n\n      if (timelineInstruction.subTimeline && skippedPlayersMap) {\n        allSubElements.add(element);\n      }\n\n      if (isQueriedElement) {\n        const wrappedPlayer = new TransitionAnimationPlayer(namespaceId, triggerName, element);\n        wrappedPlayer.setRealPlayer(player);\n        allQueriedPlayers.push(wrappedPlayer);\n      }\n\n      return player;\n    });\n    allQueriedPlayers.forEach(player => {\n      getOrSetDefaultValue(this.playersByQueriedElement, player.element, []).push(player);\n      player.onDone(() => deleteOrUnsetInMap(this.playersByQueriedElement, player.element, player));\n    });\n    allConsumedElements.forEach(element => addClass(element, NG_ANIMATING_CLASSNAME));\n    const player = optimizeGroupPlayer(allNewPlayers);\n    player.onDestroy(() => {\n      allConsumedElements.forEach(element => removeClass(element, NG_ANIMATING_CLASSNAME));\n      setStyles(rootElement, instruction.toStyles);\n    }); // this basically makes all of the callbacks for sub element animations\n    // be dependent on the upper players for when they finish\n\n    allSubElements.forEach(element => {\n      getOrSetDefaultValue(skippedPlayersMap, element, []).push(player);\n    });\n    return player;\n  }\n\n  _buildPlayer(instruction, keyframes, previousPlayers) {\n    if (keyframes.length > 0) {\n      return this.driver.animate(instruction.element, keyframes, instruction.duration, instruction.delay, instruction.easing, previousPlayers);\n    } // special case for when an empty transition|definition is provided\n    // ... there is no point in rendering an empty animation\n\n\n    return new NoopAnimationPlayer(instruction.duration, instruction.delay);\n  }\n\n}\n\nclass TransitionAnimationPlayer {\n  constructor(namespaceId, triggerName, element) {\n    this.namespaceId = namespaceId;\n    this.triggerName = triggerName;\n    this.element = element;\n    this._player = new NoopAnimationPlayer();\n    this._containsRealPlayer = false;\n    this._queuedCallbacks = new Map();\n    this.destroyed = false;\n    this.markedForDestroy = false;\n    this.disabled = false;\n    this.queued = true;\n    this.totalTime = 0;\n  }\n\n  setRealPlayer(player) {\n    if (this._containsRealPlayer) return;\n    this._player = player;\n\n    this._queuedCallbacks.forEach((callbacks, phase) => {\n      callbacks.forEach(callback => listenOnPlayer(player, phase, undefined, callback));\n    });\n\n    this._queuedCallbacks.clear();\n\n    this._containsRealPlayer = true;\n    this.overrideTotalTime(player.totalTime);\n    this.queued = false;\n  }\n\n  getRealPlayer() {\n    return this._player;\n  }\n\n  overrideTotalTime(totalTime) {\n    this.totalTime = totalTime;\n  }\n\n  syncPlayerEvents(player) {\n    const p = this._player;\n\n    if (p.triggerCallback) {\n      player.onStart(() => p.triggerCallback('start'));\n    }\n\n    player.onDone(() => this.finish());\n    player.onDestroy(() => this.destroy());\n  }\n\n  _queueEvent(name, callback) {\n    getOrSetDefaultValue(this._queuedCallbacks, name, []).push(callback);\n  }\n\n  onDone(fn) {\n    if (this.queued) {\n      this._queueEvent('done', fn);\n    }\n\n    this._player.onDone(fn);\n  }\n\n  onStart(fn) {\n    if (this.queued) {\n      this._queueEvent('start', fn);\n    }\n\n    this._player.onStart(fn);\n  }\n\n  onDestroy(fn) {\n    if (this.queued) {\n      this._queueEvent('destroy', fn);\n    }\n\n    this._player.onDestroy(fn);\n  }\n\n  init() {\n    this._player.init();\n  }\n\n  hasStarted() {\n    return this.queued ? false : this._player.hasStarted();\n  }\n\n  play() {\n    !this.queued && this._player.play();\n  }\n\n  pause() {\n    !this.queued && this._player.pause();\n  }\n\n  restart() {\n    !this.queued && this._player.restart();\n  }\n\n  finish() {\n    this._player.finish();\n  }\n\n  destroy() {\n    this.destroyed = true;\n\n    this._player.destroy();\n  }\n\n  reset() {\n    !this.queued && this._player.reset();\n  }\n\n  setPosition(p) {\n    if (!this.queued) {\n      this._player.setPosition(p);\n    }\n  }\n\n  getPosition() {\n    return this.queued ? 0 : this._player.getPosition();\n  }\n  /** @internal */\n\n\n  triggerCallback(phaseName) {\n    const p = this._player;\n\n    if (p.triggerCallback) {\n      p.triggerCallback(phaseName);\n    }\n  }\n\n}\n\nfunction deleteOrUnsetInMap(map, key, value) {\n  let currentValues = map.get(key);\n\n  if (currentValues) {\n    if (currentValues.length) {\n      const index = currentValues.indexOf(value);\n      currentValues.splice(index, 1);\n    }\n\n    if (currentValues.length == 0) {\n      map.delete(key);\n    }\n  }\n\n  return currentValues;\n}\n\nfunction normalizeTriggerValue(value) {\n  // we use `!= null` here because it's the most simple\n  // way to test against a \"falsy\" value without mixing\n  // in empty strings or a zero value. DO NOT OPTIMIZE.\n  return value != null ? value : null;\n}\n\nfunction isElementNode(node) {\n  return node && node['nodeType'] === 1;\n}\n\nfunction isTriggerEventValid(eventName) {\n  return eventName == 'start' || eventName == 'done';\n}\n\nfunction cloakElement(element, value) {\n  const oldValue = element.style.display;\n  element.style.display = value != null ? value : 'none';\n  return oldValue;\n}\n\nfunction cloakAndComputeStyles(valuesMap, driver, elements, elementPropsMap, defaultStyle) {\n  const cloakVals = [];\n  elements.forEach(element => cloakVals.push(cloakElement(element)));\n  const failedElements = [];\n  elementPropsMap.forEach((props, element) => {\n    const styles = new Map();\n    props.forEach(prop => {\n      const value = driver.computeStyle(element, prop, defaultStyle);\n      styles.set(prop, value); // there is no easy way to detect this because a sub element could be removed\n      // by a parent animation element being detached.\n\n      if (!value || value.length == 0) {\n        element[REMOVAL_FLAG] = NULL_REMOVED_QUERIED_STATE;\n        failedElements.push(element);\n      }\n    });\n    valuesMap.set(element, styles);\n  }); // we use a index variable here since Set.forEach(a, i) does not return\n  // an index value for the closure (but instead just the value)\n\n  let i = 0;\n  elements.forEach(element => cloakElement(element, cloakVals[i++]));\n  return failedElements;\n}\n/*\nSince the Angular renderer code will return a collection of inserted\nnodes in all areas of a DOM tree, it's up to this algorithm to figure\nout which nodes are roots for each animation @trigger.\n\nBy placing each inserted node into a Set and traversing upwards, it\nis possible to find the @trigger elements and well any direct *star\ninsertion nodes, if a @trigger root is found then the enter element\nis placed into the Map[@trigger] spot.\n */\n\n\nfunction buildRootMap(roots, nodes) {\n  const rootMap = new Map();\n  roots.forEach(root => rootMap.set(root, []));\n  if (nodes.length == 0) return rootMap;\n  const NULL_NODE = 1;\n  const nodeSet = new Set(nodes);\n  const localRootMap = new Map();\n\n  function getRoot(node) {\n    if (!node) return NULL_NODE;\n    let root = localRootMap.get(node);\n    if (root) return root;\n    const parent = node.parentNode;\n\n    if (rootMap.has(parent)) {\n      // ngIf inside @trigger\n      root = parent;\n    } else if (nodeSet.has(parent)) {\n      // ngIf inside ngIf\n      root = NULL_NODE;\n    } else {\n      // recurse upwards\n      root = getRoot(parent);\n    }\n\n    localRootMap.set(node, root);\n    return root;\n  }\n\n  nodes.forEach(node => {\n    const root = getRoot(node);\n\n    if (root !== NULL_NODE) {\n      rootMap.get(root).push(node);\n    }\n  });\n  return rootMap;\n}\n\nfunction addClass(element, className) {\n  element.classList?.add(className);\n}\n\nfunction removeClass(element, className) {\n  element.classList?.remove(className);\n}\n\nfunction removeNodesAfterAnimationDone(engine, element, players) {\n  optimizeGroupPlayer(players).onDone(() => engine.processLeaveNode(element));\n}\n\nfunction flattenGroupPlayers(players) {\n  const finalPlayers = [];\n\n  _flattenGroupPlayersRecur(players, finalPlayers);\n\n  return finalPlayers;\n}\n\nfunction _flattenGroupPlayersRecur(players, finalPlayers) {\n  for (let i = 0; i < players.length; i++) {\n    const player = players[i];\n\n    if (player instanceof ɵAnimationGroupPlayer) {\n      _flattenGroupPlayersRecur(player.players, finalPlayers);\n    } else {\n      finalPlayers.push(player);\n    }\n  }\n}\n\nfunction objEquals(a, b) {\n  const k1 = Object.keys(a);\n  const k2 = Object.keys(b);\n  if (k1.length != k2.length) return false;\n\n  for (let i = 0; i < k1.length; i++) {\n    const prop = k1[i];\n    if (!b.hasOwnProperty(prop) || a[prop] !== b[prop]) return false;\n  }\n\n  return true;\n}\n\nfunction replacePostStylesAsPre(element, allPreStyleElements, allPostStyleElements) {\n  const postEntry = allPostStyleElements.get(element);\n  if (!postEntry) return false;\n  let preEntry = allPreStyleElements.get(element);\n\n  if (preEntry) {\n    postEntry.forEach(data => preEntry.add(data));\n  } else {\n    allPreStyleElements.set(element, postEntry);\n  }\n\n  allPostStyleElements.delete(element);\n  return true;\n}\n\nclass AnimationEngine {\n  constructor(bodyNode, _driver, _normalizer) {\n    this.bodyNode = bodyNode;\n    this._driver = _driver;\n    this._normalizer = _normalizer;\n    this._triggerCache = {}; // this method is designed to be overridden by the code that uses this engine\n\n    this.onRemovalComplete = (element, context) => {};\n\n    this._transitionEngine = new TransitionAnimationEngine(bodyNode, _driver, _normalizer);\n    this._timelineEngine = new TimelineAnimationEngine(bodyNode, _driver, _normalizer);\n\n    this._transitionEngine.onRemovalComplete = (element, context) => this.onRemovalComplete(element, context);\n  }\n\n  registerTrigger(componentId, namespaceId, hostElement, name, metadata) {\n    const cacheKey = componentId + '-' + name;\n    let trigger = this._triggerCache[cacheKey];\n\n    if (!trigger) {\n      const errors = [];\n      const warnings = [];\n      const ast = buildAnimationAst(this._driver, metadata, errors, warnings);\n\n      if (errors.length) {\n        throw triggerBuildFailed(name, errors);\n      }\n\n      if (warnings.length) {\n        warnTriggerBuild(name, warnings);\n      }\n\n      trigger = buildTrigger(name, ast, this._normalizer);\n      this._triggerCache[cacheKey] = trigger;\n    }\n\n    this._transitionEngine.registerTrigger(namespaceId, name, trigger);\n  }\n\n  register(namespaceId, hostElement) {\n    this._transitionEngine.register(namespaceId, hostElement);\n  }\n\n  destroy(namespaceId, context) {\n    this._transitionEngine.destroy(namespaceId, context);\n  }\n\n  onInsert(namespaceId, element, parent, insertBefore) {\n    this._transitionEngine.insertNode(namespaceId, element, parent, insertBefore);\n  }\n\n  onRemove(namespaceId, element, context, isHostElement) {\n    this._transitionEngine.removeNode(namespaceId, element, isHostElement || false, context);\n  }\n\n  disableAnimations(element, disable) {\n    this._transitionEngine.markElementAsDisabled(element, disable);\n  }\n\n  process(namespaceId, element, property, value) {\n    if (property.charAt(0) == '@') {\n      const [id, action] = parseTimelineCommand(property);\n      const args = value;\n\n      this._timelineEngine.command(id, element, action, args);\n    } else {\n      this._transitionEngine.trigger(namespaceId, element, property, value);\n    }\n  }\n\n  listen(namespaceId, element, eventName, eventPhase, callback) {\n    // @@listen\n    if (eventName.charAt(0) == '@') {\n      const [id, action] = parseTimelineCommand(eventName);\n      return this._timelineEngine.listen(id, element, action, callback);\n    }\n\n    return this._transitionEngine.listen(namespaceId, element, eventName, eventPhase, callback);\n  }\n\n  flush(microtaskId = -1) {\n    this._transitionEngine.flush(microtaskId);\n  }\n\n  get players() {\n    return this._transitionEngine.players.concat(this._timelineEngine.players);\n  }\n\n  whenRenderingDone() {\n    return this._transitionEngine.whenRenderingDone();\n  }\n\n}\n/**\n * Returns an instance of `SpecialCasedStyles` if and when any special (non animateable) styles are\n * detected.\n *\n * In CSS there exist properties that cannot be animated within a keyframe animation\n * (whether it be via CSS keyframes or web-animations) and the animation implementation\n * will ignore them. This function is designed to detect those special cased styles and\n * return a container that will be executed at the start and end of the animation.\n *\n * @returns an instance of `SpecialCasedStyles` if any special styles are detected otherwise `null`\n */\n\n\nfunction packageNonAnimatableStyles(element, styles) {\n  let startStyles = null;\n  let endStyles = null;\n\n  if (Array.isArray(styles) && styles.length) {\n    startStyles = filterNonAnimatableStyles(styles[0]);\n\n    if (styles.length > 1) {\n      endStyles = filterNonAnimatableStyles(styles[styles.length - 1]);\n    }\n  } else if (styles instanceof Map) {\n    startStyles = filterNonAnimatableStyles(styles);\n  }\n\n  return startStyles || endStyles ? new SpecialCasedStyles(element, startStyles, endStyles) : null;\n}\n/**\n * Designed to be executed during a keyframe-based animation to apply any special-cased styles.\n *\n * When started (when the `start()` method is run) then the provided `startStyles`\n * will be applied. When finished (when the `finish()` method is called) the\n * `endStyles` will be applied as well any any starting styles. Finally when\n * `destroy()` is called then all styles will be removed.\n */\n\n\nlet SpecialCasedStyles = /*#__PURE__*/(() => {\n  class SpecialCasedStyles {\n    constructor(_element, _startStyles, _endStyles) {\n      this._element = _element;\n      this._startStyles = _startStyles;\n      this._endStyles = _endStyles;\n      this._state = 0\n      /* SpecialCasedStylesState.Pending */\n      ;\n      let initialStyles = SpecialCasedStyles.initialStylesByElement.get(_element);\n\n      if (!initialStyles) {\n        SpecialCasedStyles.initialStylesByElement.set(_element, initialStyles = new Map());\n      }\n\n      this._initialStyles = initialStyles;\n    }\n\n    start() {\n      if (this._state < 1\n      /* SpecialCasedStylesState.Started */\n      ) {\n        if (this._startStyles) {\n          setStyles(this._element, this._startStyles, this._initialStyles);\n        }\n\n        this._state = 1\n        /* SpecialCasedStylesState.Started */\n        ;\n      }\n    }\n\n    finish() {\n      this.start();\n\n      if (this._state < 2\n      /* SpecialCasedStylesState.Finished */\n      ) {\n        setStyles(this._element, this._initialStyles);\n\n        if (this._endStyles) {\n          setStyles(this._element, this._endStyles);\n          this._endStyles = null;\n        }\n\n        this._state = 1\n        /* SpecialCasedStylesState.Started */\n        ;\n      }\n    }\n\n    destroy() {\n      this.finish();\n\n      if (this._state < 3\n      /* SpecialCasedStylesState.Destroyed */\n      ) {\n        SpecialCasedStyles.initialStylesByElement.delete(this._element);\n\n        if (this._startStyles) {\n          eraseStyles(this._element, this._startStyles);\n          this._endStyles = null;\n        }\n\n        if (this._endStyles) {\n          eraseStyles(this._element, this._endStyles);\n          this._endStyles = null;\n        }\n\n        setStyles(this._element, this._initialStyles);\n        this._state = 3\n        /* SpecialCasedStylesState.Destroyed */\n        ;\n      }\n    }\n\n  }\n\n  SpecialCasedStyles.initialStylesByElement = /* @__PURE__ */new WeakMap();\n  return SpecialCasedStyles;\n})();\n\nfunction filterNonAnimatableStyles(styles) {\n  let result = null;\n  styles.forEach((val, prop) => {\n    if (isNonAnimatableStyle(prop)) {\n      result = result || new Map();\n      result.set(prop, val);\n    }\n  });\n  return result;\n}\n\nfunction isNonAnimatableStyle(prop) {\n  return prop === 'display' || prop === 'position';\n}\n\nclass WebAnimationsPlayer {\n  constructor(element, keyframes, options, _specialStyles) {\n    this.element = element;\n    this.keyframes = keyframes;\n    this.options = options;\n    this._specialStyles = _specialStyles;\n    this._onDoneFns = [];\n    this._onStartFns = [];\n    this._onDestroyFns = [];\n    this._initialized = false;\n    this._finished = false;\n    this._started = false;\n    this._destroyed = false; // the following original fns are persistent copies of the _onStartFns and _onDoneFns\n    // and are used to reset the fns to their original values upon reset()\n    // (since the _onStartFns and _onDoneFns get deleted after they are called)\n\n    this._originalOnDoneFns = [];\n    this._originalOnStartFns = [];\n    this.time = 0;\n    this.parentPlayer = null;\n    this.currentSnapshot = new Map();\n    this._duration = options['duration'];\n    this._delay = options['delay'] || 0;\n    this.time = this._duration + this._delay;\n  }\n\n  _onFinish() {\n    if (!this._finished) {\n      this._finished = true;\n\n      this._onDoneFns.forEach(fn => fn());\n\n      this._onDoneFns = [];\n    }\n  }\n\n  init() {\n    this._buildPlayer();\n\n    this._preparePlayerBeforeStart();\n  }\n\n  _buildPlayer() {\n    if (this._initialized) return;\n    this._initialized = true;\n    const keyframes = this.keyframes;\n    this.domPlayer = this._triggerWebAnimation(this.element, keyframes, this.options);\n    this._finalKeyframe = keyframes.length ? keyframes[keyframes.length - 1] : new Map();\n    this.domPlayer.addEventListener('finish', () => this._onFinish());\n  }\n\n  _preparePlayerBeforeStart() {\n    // this is required so that the player doesn't start to animate right away\n    if (this._delay) {\n      this._resetDomPlayerState();\n    } else {\n      this.domPlayer.pause();\n    }\n  }\n\n  _convertKeyframesToObject(keyframes) {\n    const kfs = [];\n    keyframes.forEach(frame => {\n      kfs.push(Object.fromEntries(frame));\n    });\n    return kfs;\n  }\n  /** @internal */\n\n\n  _triggerWebAnimation(element, keyframes, options) {\n    // jscompiler doesn't seem to know animate is a native property because it's not fully\n    // supported yet across common browsers (we polyfill it for Edge/Safari) [CL #*********]\n    return element['animate'](this._convertKeyframesToObject(keyframes), options);\n  }\n\n  onStart(fn) {\n    this._originalOnStartFns.push(fn);\n\n    this._onStartFns.push(fn);\n  }\n\n  onDone(fn) {\n    this._originalOnDoneFns.push(fn);\n\n    this._onDoneFns.push(fn);\n  }\n\n  onDestroy(fn) {\n    this._onDestroyFns.push(fn);\n  }\n\n  play() {\n    this._buildPlayer();\n\n    if (!this.hasStarted()) {\n      this._onStartFns.forEach(fn => fn());\n\n      this._onStartFns = [];\n      this._started = true;\n\n      if (this._specialStyles) {\n        this._specialStyles.start();\n      }\n    }\n\n    this.domPlayer.play();\n  }\n\n  pause() {\n    this.init();\n    this.domPlayer.pause();\n  }\n\n  finish() {\n    this.init();\n\n    if (this._specialStyles) {\n      this._specialStyles.finish();\n    }\n\n    this._onFinish();\n\n    this.domPlayer.finish();\n  }\n\n  reset() {\n    this._resetDomPlayerState();\n\n    this._destroyed = false;\n    this._finished = false;\n    this._started = false;\n    this._onStartFns = this._originalOnStartFns;\n    this._onDoneFns = this._originalOnDoneFns;\n  }\n\n  _resetDomPlayerState() {\n    if (this.domPlayer) {\n      this.domPlayer.cancel();\n    }\n  }\n\n  restart() {\n    this.reset();\n    this.play();\n  }\n\n  hasStarted() {\n    return this._started;\n  }\n\n  destroy() {\n    if (!this._destroyed) {\n      this._destroyed = true;\n\n      this._resetDomPlayerState();\n\n      this._onFinish();\n\n      if (this._specialStyles) {\n        this._specialStyles.destroy();\n      }\n\n      this._onDestroyFns.forEach(fn => fn());\n\n      this._onDestroyFns = [];\n    }\n  }\n\n  setPosition(p) {\n    if (this.domPlayer === undefined) {\n      this.init();\n    }\n\n    this.domPlayer.currentTime = p * this.time;\n  }\n\n  getPosition() {\n    return this.domPlayer.currentTime / this.time;\n  }\n\n  get totalTime() {\n    return this._delay + this._duration;\n  }\n\n  beforeDestroy() {\n    const styles = new Map();\n\n    if (this.hasStarted()) {\n      // note: this code is invoked only when the `play` function was called prior to this\n      // (thus `hasStarted` returns true), this implies that the code that initializes\n      // `_finalKeyframe` has also been executed and the non-null assertion can be safely used here\n      const finalKeyframe = this._finalKeyframe;\n      finalKeyframe.forEach((val, prop) => {\n        if (prop !== 'offset') {\n          styles.set(prop, this._finished ? val : computeStyle(this.element, prop));\n        }\n      });\n    }\n\n    this.currentSnapshot = styles;\n  }\n  /** @internal */\n\n\n  triggerCallback(phaseName) {\n    const methods = phaseName === 'start' ? this._onStartFns : this._onDoneFns;\n    methods.forEach(fn => fn());\n    methods.length = 0;\n  }\n\n}\n\nclass WebAnimationsDriver {\n  validateStyleProperty(prop) {\n    // Perform actual validation in dev mode only, in prod mode this check is a noop.\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      return validateStyleProperty(prop);\n    }\n\n    return true;\n  }\n\n  validateAnimatableStyleProperty(prop) {\n    // Perform actual validation in dev mode only, in prod mode this check is a noop.\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      const cssProp = camelCaseToDashCase(prop);\n      return validateWebAnimatableStyleProperty(cssProp);\n    }\n\n    return true;\n  }\n\n  matchesElement(_element, _selector) {\n    // This method is deprecated and no longer in use so we return false.\n    return false;\n  }\n\n  containsElement(elm1, elm2) {\n    return containsElement(elm1, elm2);\n  }\n\n  getParentElement(element) {\n    return getParentElement(element);\n  }\n\n  query(element, selector, multi) {\n    return invokeQuery(element, selector, multi);\n  }\n\n  computeStyle(element, prop, defaultValue) {\n    return window.getComputedStyle(element)[prop];\n  }\n\n  animate(element, keyframes, duration, delay, easing, previousPlayers = []) {\n    const fill = delay == 0 ? 'both' : 'forwards';\n    const playerOptions = {\n      duration,\n      delay,\n      fill\n    }; // we check for this to avoid having a null|undefined value be present\n    // for the easing (which results in an error for certain browsers #9752)\n\n    if (easing) {\n      playerOptions['easing'] = easing;\n    }\n\n    const previousStyles = new Map();\n    const previousWebAnimationPlayers = previousPlayers.filter(player => player instanceof WebAnimationsPlayer);\n\n    if (allowPreviousPlayerStylesMerge(duration, delay)) {\n      previousWebAnimationPlayers.forEach(player => {\n        player.currentSnapshot.forEach((val, prop) => previousStyles.set(prop, val));\n      });\n    }\n\n    let _keyframes = normalizeKeyframes(keyframes).map(styles => copyStyles(styles));\n\n    _keyframes = balancePreviousStylesIntoKeyframes(element, _keyframes, previousStyles);\n    const specialStyles = packageNonAnimatableStyles(element, _keyframes);\n    return new WebAnimationsPlayer(element, _keyframes, playerOptions, specialStyles);\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { AnimationDriver, Animation as ɵAnimation, AnimationEngine as ɵAnimationEngine, AnimationStyleNormalizer as ɵAnimationStyleNormalizer, NoopAnimationDriver as ɵNoopAnimationDriver, NoopAnimationStyleNormalizer as ɵNoopAnimationStyleNormalizer, WebAnimationsDriver as ɵWebAnimationsDriver, WebAnimationsPlayer as ɵWebAnimationsPlayer, WebAnimationsStyleNormalizer as ɵWebAnimationsStyleNormalizer, allowPreviousPlayerStylesMerge as ɵallowPreviousPlayerStylesMerge, containsElement as ɵcontainsElement, getParentElement as ɵgetParentElement, invokeQuery as ɵinvokeQuery, normalizeKeyframes as ɵnormalizeKeyframes, validateStyleProperty as ɵvalidateStyleProperty }; //# sourceMappingURL=browser.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}