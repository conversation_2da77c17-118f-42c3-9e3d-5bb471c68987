{"ast": null, "code": "import _asyncToGenerator from \"R:/chateye/FrontendAngular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { ApiService, Message, Group, User, SecurityInfo } from './api.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./api.service\";\nimport * as i2 from \"./socket.service\";\nimport * as i3 from \"./notification.service\";\nexport class ChatService {\n  constructor(apiService, socketService, notificationService) {\n    this.apiService = apiService;\n    this.socketService = socketService;\n    this.notificationService = notificationService;\n    this.userSubject = new BehaviorSubject(null);\n    this.messagesSubject = new BehaviorSubject([]);\n    this.onlineUsersSubject = new BehaviorSubject([]);\n    this.groupsSubject = new BehaviorSubject([]);\n    this.currentGroupSubject = new BehaviorSubject(null);\n    this.replyToSubject = new BehaviorSubject(null);\n    this.loadingSubject = new BehaviorSubject(false);\n    this.isAdminSubject = new BehaviorSubject(false);\n    this.showAdminPanelSubject = new BehaviorSubject(false);\n    this.securityInfoSubject = new BehaviorSubject(null);\n    this.editingMessageSubject = new BehaviorSubject(null); // Public observables\n\n    this.user$ = this.userSubject.asObservable();\n    this.messages$ = this.messagesSubject.asObservable();\n    this.onlineUsers$ = this.onlineUsersSubject.asObservable();\n    this.groups$ = this.groupsSubject.asObservable();\n    this.currentGroup$ = this.currentGroupSubject.asObservable();\n    this.replyTo$ = this.replyToSubject.asObservable();\n    this.loading$ = this.loadingSubject.asObservable();\n    this.isAdmin$ = this.isAdminSubject.asObservable();\n    this.showAdminPanel$ = this.showAdminPanelSubject.asObservable();\n    this.securityInfo$ = this.securityInfoSubject.asObservable();\n    this.editingMessage$ = this.editingMessageSubject.asObservable();\n    this.connected$ = this.socketService.isConnected$; // Typing indicators\n\n    this.typingUsersSubject = new BehaviorSubject([]);\n    this.typingUsers$ = this.typingUsersSubject.asObservable();\n    this.typingTimeout = null; // Computed observables\n\n    this.isLoggedIn$ = this.user$.pipe(map(user => !!user));\n    this.isLoadingGroups = false;\n    this.isLoadingMessages = false;\n    this.lastMessageLoadTime = 0;\n    this.MESSAGE_LOAD_THROTTLE = 1000; // 1 second throttle\n\n    try {\n      this.setupSocketListeners();\n      this.setupMessageRefresh();\n    } catch (error) {\n      console.error('Error initializing ChatService:', error);\n    }\n  }\n\n  setupSocketListeners() {\n    this.socketService.on('connect', () => {\n      // Clear any loading states when connected\n      this.loadingSubject.next(false);\n    });\n    this.socketService.on('disconnect', () => {// Don't set loading to true on disconnect to avoid UI freeze\n    });\n    this.socketService.on('connect_error', error => {\n      console.error('Socket connection error:', error);\n      this.loadingSubject.next(false);\n    });\n    this.socketService.on('userGroups', userGroups => {\n      this.groupsSubject.next(userGroups || []);\n\n      if (userGroups && userGroups.length > 0) {\n        this.currentGroupSubject.next(userGroups[0]); // Load messages for the selected group\n\n        this.loadRecentMessages(userGroups[0].id);\n      } else {\n        // Clear current group and messages if no groups\n        this.currentGroupSubject.next(null);\n        this.messagesSubject.next([]);\n      }\n    });\n    this.socketService.on('recentMessages', messages => {\n      this.messagesSubject.next(messages || []);\n    });\n    this.socketService.on('groupJoined', ({\n      groupId\n    }) => {\n      const groups = this.groupsSubject.value;\n      const group = groups.find(g => g.id === groupId);\n\n      if (group) {\n        this.currentGroupSubject.next(group);\n      }\n    });\n    this.socketService.on('newMessage', message => {\n      const currentMessages = this.messagesSubject.value;\n      const currentUser = this.userSubject.value;\n      const currentGroup = this.currentGroupSubject.value; // Only process messages for the current group to avoid confusion\n\n      if (currentGroup && message.groupId !== currentGroup.id) {\n        return;\n      } // Check for duplicate messages to prevent double-display\n\n\n      const existingMessage = currentMessages.find(msg => msg.id === message.id || msg.text === message.text && msg.username === message.username && Math.abs(new Date(msg.timestamp).getTime() - new Date(message.timestamp).getTime()) < 5000);\n\n      if (existingMessage && !existingMessage.id.startsWith('temp_')) {\n        return;\n      } // Check if this is our own message (to replace optimistic message)\n\n\n      if (message.username === currentUser) {\n        // Find and replace optimistic message with real message\n        const optimisticIndex = currentMessages.findIndex(msg => msg.id.startsWith('temp_') && msg.text === message.text && msg.username === currentUser && msg.groupId === message.groupId);\n\n        if (optimisticIndex !== -1) {\n          // Replace optimistic message\n          const updatedMessages = [...currentMessages];\n          updatedMessages[optimisticIndex] = message;\n          this.messagesSubject.next(this.sortMessagesByTimestamp(updatedMessages));\n        } else {\n          // Add new message if no optimistic message found (e.g., from another tab)\n          const updatedMessages = [...currentMessages, message];\n          this.messagesSubject.next(this.sortMessagesByTimestamp(updatedMessages));\n        }\n      } else {\n        // Add message from other users\n        const updatedMessages = [...currentMessages, message];\n        this.messagesSubject.next(this.sortMessagesByTimestamp(updatedMessages));\n      } // Show notification if not current user and window not focused\n\n\n      if (message.username !== currentUser && document.hidden) {\n        this.notificationService.showMessageNotification(message.username, message.text);\n      }\n    });\n    this.socketService.on('reactionUpdate', ({\n      messageId,\n      reactions\n    }) => {\n      const currentMessages = this.messagesSubject.value;\n      this.messagesSubject.next(currentMessages.map(msg => msg.id === messageId ? { ...msg,\n        reactions\n      } : msg));\n    });\n    this.socketService.on('onlineUsersUpdate', users => {\n      this.onlineUsersSubject.next(users || []);\n    });\n    this.socketService.on('userJoined', ({\n      username\n    }) => {// Online users will be updated via onlineUsersUpdate event\n    });\n    this.socketService.on('userLeft', ({\n      username\n    }) => {// Online users will be updated via onlineUsersUpdate event\n    }); // Handle typing indicators\n\n    this.socketService.on('userTyping', ({\n      username,\n      groupId,\n      isTyping\n    }) => {\n      const currentGroup = this.currentGroupSubject.value;\n\n      if (currentGroup && groupId === currentGroup.id) {\n        const currentTypingUsers = this.typingUsersSubject.value;\n\n        if (isTyping) {\n          // Add user to typing list if not already there\n          if (!currentTypingUsers.includes(username)) {\n            this.typingUsersSubject.next([...currentTypingUsers, username]);\n          }\n        } else {\n          // Remove user from typing list\n          this.typingUsersSubject.next(currentTypingUsers.filter(user => user !== username));\n        }\n      }\n    });\n    this.socketService.on('messageUpdated', ({\n      messageId,\n      newText,\n      updatedAt\n    }) => {\n      const currentMessages = this.messagesSubject.value;\n      this.messagesSubject.next(currentMessages.map(msg => msg.id === messageId ? { ...msg,\n        text: newText,\n        updated_at: updatedAt\n      } : msg));\n    });\n    this.socketService.on('messageDeleted', ({\n      messageId\n    }) => {\n      const currentMessages = this.messagesSubject.value;\n      this.messagesSubject.next(currentMessages.filter(msg => msg.id !== messageId));\n    });\n    this.socketService.on('error', error => {\n      console.error('Socket error:', error);\n    });\n  }\n\n  setupMessageRefresh() {\n    // Clear any existing interval first\n    if (this.messageRefreshInterval) {\n      clearInterval(this.messageRefreshInterval);\n    } // Temporarily disable message refresh to prevent browser freezing\n    // TODO: Re-enable once socket connection issues are resolved\n\n    /*\r\n    this.messageRefreshInterval = setInterval(() => {\r\n      try {\r\n        if (!this.socketService.isConnectedSubject.value) {\r\n          const currentGroup = this.currentGroupSubject.value;\r\n          if (currentGroup) {\r\n            console.log('Refreshing messages via HTTP API');\r\n            this.loadRecentMessages(currentGroup.id);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Error in message refresh interval:', error);\r\n        // Clear the interval if there's an error to prevent crashes\r\n        if (this.messageRefreshInterval) {\r\n          clearInterval(this.messageRefreshInterval);\r\n          this.messageRefreshInterval = null;\r\n        }\r\n      }\r\n    }, 5000);\r\n    */\n\n  }\n\n  login(_x, _x2) {\n    var _this = this;\n\n    return _asyncToGenerator(function* (username, password, inviteCode = null) {\n      try {\n        _this.loadingSubject.next(true);\n\n        const userData = yield _this.apiService.loginUser(username, password, inviteCode || undefined).toPromise(); // Check if password is required\n\n        if (userData?.requiresPassword) {\n          throw new Error('Password required for this user');\n        } // Connect to socket with auth data\n\n\n        _this.socketService.connect(username, password, inviteCode);\n\n        _this.userSubject.next(username);\n\n        _this.isAdminSubject.next(userData?.isAdmin || false);\n\n        _this.securityInfoSubject.next(userData?.securityInfo || null); // Wait for socket connection before loading groups\n        // The socket will emit 'userGroups' event which will handle group loading\n        // This prevents race conditions between API and socket calls\n        // Add fallback: if socket doesn't load groups within 3 seconds, try API\n\n\n        setTimeout(() => {\n          if (_this.groupsSubject.value.length === 0) {\n            _this.loadUserGroups(username);\n          }\n        }, 3000);\n      } catch (error) {\n        console.error('Login failed:', error);\n        throw error;\n      } finally {\n        _this.loadingSubject.next(false);\n      }\n    }).apply(this, arguments);\n  }\n\n  loadRecentMessages(groupId) {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      // Safety guard to prevent infinite loops\n      if (_this2.isLoadingMessages) {\n        return;\n      } // Throttle message loading to prevent excessive API calls\n\n\n      const now = Date.now();\n\n      if (now - _this2.lastMessageLoadTime < _this2.MESSAGE_LOAD_THROTTLE) {\n        return;\n      }\n\n      _this2.lastMessageLoadTime = now;\n      _this2.isLoadingMessages = true;\n\n      try {\n        const messages = yield _this2.apiService.getMessages(groupId, 50).toPromise(); // For group switching, replace messages with only the current group's messages\n        // This ensures we only show messages for the current group\n\n        _this2.messagesSubject.next(messages || []);\n      } catch (error) {\n        console.error('Failed to load messages:', error); // Set empty array to prevent UI from showing stale data\n\n        _this2.messagesSubject.next([]);\n      } finally {\n        _this2.isLoadingMessages = false;\n      }\n    })();\n  }\n\n  loadUserGroups(username) {\n    var _this3 = this;\n\n    return _asyncToGenerator(function* () {\n      // Safety guard to prevent infinite loops\n      if (_this3.isLoadingGroups) {\n        return;\n      }\n\n      _this3.isLoadingGroups = true;\n\n      try {\n        const groups = yield _this3.apiService.getUserGroups(username).toPromise();\n\n        _this3.groupsSubject.next(groups || []);\n\n        if (groups && groups.length > 0) {\n          _this3.currentGroupSubject.next(groups[0]);\n\n          _this3.loadRecentMessages(groups[0].id);\n        }\n      } catch (error) {\n        console.error('Failed to load user groups:', error);\n      } finally {\n        _this3.isLoadingGroups = false;\n      }\n    })();\n  } // Helper method to sort messages by timestamp\n\n\n  sortMessagesByTimestamp(messages) {\n    return messages.sort((a, b) => {\n      const timeA = new Date(a.timestamp).getTime();\n      const timeB = new Date(b.timestamp).getTime();\n      return timeA - timeB;\n    });\n  } // Helper method to replace optimistic message with real message\n\n\n  replaceOptimisticMessage(tempId, realMessage) {\n    const currentMessages = this.messagesSubject.value;\n    const updatedMessages = currentMessages.map(msg => msg.id === tempId ? realMessage : msg);\n    this.messagesSubject.next(this.sortMessagesByTimestamp(updatedMessages));\n  } // Helper method to remove optimistic message (on error)\n\n\n  removeOptimisticMessage(tempId) {\n    const currentMessages = this.messagesSubject.value;\n    const updatedMessages = currentMessages.filter(msg => msg.id !== tempId);\n    this.messagesSubject.next(updatedMessages);\n  } // Helper method to check if message already exists\n\n\n  messageExists(message, messages) {\n    return messages.some(msg => msg.id === message.id || msg.text === message.text && msg.username === message.username && Math.abs(new Date(msg.timestamp).getTime() - new Date(message.timestamp).getTime()) < 5000);\n  }\n\n  joinGroup(groupId) {\n    const currentUser = this.userSubject.value;\n\n    if (!groupId || !currentUser) {\n      console.error('Cannot join group - missing groupId or user');\n      return;\n    } // Check if socket is connected before joining\n\n\n    if (!this.socketService.isConnectedSubject.value) {\n      console.error('Cannot join group - socket not connected');\n      return;\n    }\n\n    this.socketService.joinGroup(groupId);\n    const groups = this.groupsSubject.value;\n    const group = groups.find(g => g.id === groupId);\n\n    if (group) {\n      this.currentGroupSubject.next(group); // Load recent messages for the group\n\n      this.loadRecentMessages(groupId);\n    } else {\n      console.error('Group not found in user groups:', groupId);\n    }\n  }\n\n  sendMessage(text, replyToId = null) {\n    const currentUser = this.userSubject.value;\n    const currentGroup = this.currentGroupSubject.value;\n\n    if (!text.trim()) {\n      console.error('Cannot send message - empty text');\n      return;\n    }\n\n    if (!currentUser) {\n      console.error('Cannot send message - user not logged in');\n      return;\n    }\n\n    if (!currentGroup) {\n      console.error('Cannot send message - no group selected. Available groups:', this.groupsSubject.value); // Try to auto-select the first available group\n\n      const groups = this.groupsSubject.value;\n\n      if (groups && groups.length > 0) {\n        this.currentGroupSubject.next(groups[0]);\n        this.loadRecentMessages(groups[0].id); // Retry sending the message - DISABLED to prevent infinite loops\n        // setTimeout(() => this.sendMessage(text, replyToId), 100);\n\n        return;\n      } else {\n        console.error('No groups available for user');\n        return;\n      }\n    } // Create optimistic message for immediate display\n\n\n    const optimisticMessage = {\n      id: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n      text: text.trim(),\n      username: currentUser,\n      timestamp: new Date().toISOString(),\n      groupId: currentGroup.id,\n      replyTo: replyToId || undefined,\n      reactions: []\n    }; // Add optimistic message to local state immediately\n\n    const currentMessages = this.messagesSubject.value;\n    const updatedMessages = [...currentMessages, optimisticMessage];\n    this.messagesSubject.next(updatedMessages); // Send message with acknowledgment and retry logic\n\n    this.sendMessageWithRetry(text, currentGroup.id, replyToId, optimisticMessage);\n  }\n\n  sendMessageWithRetry(_x3, _x4, _x5, _x6) {\n    var _this4 = this;\n\n    return _asyncToGenerator(function* (text, groupId, replyToId, optimisticMessage, retryCount = 0) {\n      const maxRetries = 3;\n      const retryDelay = 1000 * Math.pow(2, retryCount); // Exponential backoff\n\n      try {\n        if (_this4.socketService.isConnected()) {\n          // Try sending via socket with acknowledgment\n          const response = yield _this4.socketService.emitWithAck('sendMessage', {\n            text,\n            groupId,\n            replyTo: replyToId,\n            messageId: optimisticMessage.id\n          });\n\n          if (response.success) {\n            // Replace optimistic message with real message\n            _this4.replaceOptimisticMessage(optimisticMessage.id, response.message);\n\n            return;\n          } else {\n            throw new Error(response.error || 'Unknown error');\n          }\n        } else {\n          throw new Error('Socket not connected');\n        }\n      } catch (error) {\n        console.error(`❌ Failed to send message (attempt ${retryCount + 1}):`, error);\n\n        if (retryCount < maxRetries) {\n          // Retry with exponential backoff\n          setTimeout(() => {\n            _this4.sendMessageWithRetry(text, groupId, replyToId, optimisticMessage, retryCount + 1);\n          }, retryDelay);\n        } else {\n          // All retries failed, try HTTP API as final fallback\n          _this4.sendMessageViaHttpFallback(text, groupId, replyToId, optimisticMessage);\n        }\n      }\n    }).apply(this, arguments);\n  }\n\n  sendMessageViaHttpFallback(text, groupId, replyToId, optimisticMessage) {\n    const currentUser = this.userSubject.value;\n\n    if (!currentUser) {\n      this.removeOptimisticMessage(optimisticMessage.id);\n      return;\n    }\n\n    this.apiService.sendMessage(text, currentUser, groupId, replyToId).subscribe({\n      next: response => {\n        this.replaceOptimisticMessage(optimisticMessage.id, response);\n      },\n      error: error => {\n        console.error('❌ Failed to send message via HTTP API:', error);\n        this.removeOptimisticMessage(optimisticMessage.id); // Show error notification to user\n\n        this.showMessageError('Failed to send message. Please try again.');\n      }\n    });\n  }\n\n  showMessageError(message) {\n    // You can implement a toast notification service here\n    console.error('💬 Message Error:', message); // For now, just log the error. In a real app, you'd show a toast/snackbar\n  } // Typing indicator methods\n\n\n  startTyping() {\n    const currentGroup = this.currentGroupSubject.value;\n\n    if (currentGroup && this.socketService.isConnected()) {\n      this.socketService.sendTypingIndicator(currentGroup.id, true); // Clear existing timeout\n\n      if (this.typingTimeout) {\n        clearTimeout(this.typingTimeout);\n      } // Set timeout to stop typing after 3 seconds of inactivity\n\n\n      this.typingTimeout = setTimeout(() => {\n        this.stopTyping();\n      }, 3000);\n    }\n  }\n\n  stopTyping() {\n    const currentGroup = this.currentGroupSubject.value;\n\n    if (currentGroup && this.socketService.isConnected()) {\n      this.socketService.sendTypingIndicator(currentGroup.id, false); // Clear timeout\n\n      if (this.typingTimeout) {\n        clearTimeout(this.typingTimeout);\n        this.typingTimeout = null;\n      }\n    }\n  }\n\n  replyToMessage(message) {\n    this.replyToSubject.next(message);\n  }\n\n  cancelReply() {\n    this.replyToSubject.next(null);\n  }\n\n  addReaction(messageId, emoji) {\n    var _this5 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        if (_this5.socketService.isConnected()) {\n          // Try with acknowledgment first\n          const response = yield _this5.socketService.emitWithAck('addReaction', {\n            messageId,\n            emoji\n          });\n\n          if (response.success) {} else {\n            throw new Error(response.error || 'Failed to add reaction');\n          }\n        } else {\n          // Fallback to HTTP API\n          const currentUser = _this5.userSubject.value;\n\n          if (currentUser) {\n            _this5.apiService.addReaction(messageId, emoji, currentUser).subscribe({\n              next: response => {},\n              error: error => {\n                console.error('❌ Failed to add reaction via HTTP API:', error);\n              }\n            });\n          }\n        }\n      } catch (error) {\n        console.error('❌ Failed to add reaction:', error); // Fallback to old method\n\n        _this5.socketService.addReaction(messageId, emoji);\n      }\n    })();\n  }\n\n  removeReaction(data) {\n    this.socketService.removeReaction(data);\n  }\n\n  updateMessage(messageId, newText) {\n    var _this6 = this;\n\n    return _asyncToGenerator(function* () {\n      const currentUser = _this6.userSubject.value;\n\n      if (!currentUser) {\n        console.error('Cannot update message - user not logged in');\n        return;\n      }\n\n      try {\n        // Update the message in the local state immediately for better UX\n        const currentMessages = _this6.messagesSubject.value;\n\n        _this6.messagesSubject.next(currentMessages.map(msg => msg.id === messageId ? { ...msg,\n          text: newText,\n          updated_at: new Date().toISOString()\n        } : msg)); // Emit socket event for real-time updates\n\n\n        if (_this6.socketService.isConnectedSubject.value) {\n          _this6.socketService.updateMessage(messageId, newText);\n        } else {\n          // Fallback to HTTP API if socket not connected\n          const updatedMessage = yield _this6.apiService.updateMessage(messageId, newText, currentUser).toPromise();\n\n          _this6.messagesSubject.next(currentMessages.map(msg => msg.id === messageId ? { ...msg,\n            text: newText,\n            updated_at: updatedMessage.updated_at\n          } : msg));\n        }\n      } catch (error) {\n        console.error('Failed to update message:', error);\n        throw error;\n      }\n    })();\n  }\n\n  deleteMessage(messageId) {\n    var _this7 = this;\n\n    return _asyncToGenerator(function* () {\n      const currentUser = _this7.userSubject.value;\n\n      if (!currentUser) {\n        console.error('Cannot delete message - user not logged in');\n        return;\n      }\n\n      try {\n        // Remove the message from local state immediately for better UX\n        const currentMessages = _this7.messagesSubject.value;\n\n        _this7.messagesSubject.next(currentMessages.filter(msg => msg.id !== messageId)); // Emit socket event for real-time updates\n\n\n        if (_this7.socketService.isConnectedSubject.value) {\n          _this7.socketService.deleteMessage(messageId);\n        } else {\n          // Fallback to HTTP API if socket not connected\n          yield _this7.apiService.deleteMessage(messageId, currentUser).toPromise();\n        }\n      } catch (error) {\n        console.error('Failed to delete message:', error);\n        throw error;\n      }\n    })();\n  }\n\n  showAdminPanel() {\n    this.showAdminPanelSubject.next(true);\n  }\n\n  hideAdminPanel() {\n    this.showAdminPanelSubject.next(false);\n  }\n\n  startEditingMessage(message) {\n    this.editingMessageSubject.next(message);\n  }\n\n  cancelEditingMessage() {\n    this.editingMessageSubject.next(null);\n  }\n\n  logout() {\n    try {\n      // Disconnect socket first\n      this.socketService.disconnect(); // Clear interval\n\n      if (this.messageRefreshInterval) {\n        clearInterval(this.messageRefreshInterval);\n        this.messageRefreshInterval = null;\n      } // Reset all subjects\n\n\n      this.userSubject.next(null);\n      this.messagesSubject.next([]);\n      this.onlineUsersSubject.next([]);\n      this.groupsSubject.next([]);\n      this.currentGroupSubject.next(null);\n      this.replyToSubject.next(null);\n      this.loadingSubject.next(false);\n      this.isAdminSubject.next(false);\n      this.showAdminPanelSubject.next(false);\n      this.securityInfoSubject.next(null);\n      this.editingMessageSubject.next(null);\n    } catch (error) {\n      console.error('Error during logout:', error);\n    }\n  } // Method to refresh security info for all components\n\n\n  refreshSecurityInfo() {\n    this.apiService.getSecurityInfo().subscribe({\n      next: data => {\n        this.securityInfoSubject.next(data);\n      },\n      error: error => {\n        console.error('Failed to refresh security info:', error);\n      }\n    });\n  }\n\n}\n\nChatService.ɵfac = function ChatService_Factory(t) {\n  return new (t || ChatService)(i0.ɵɵinject(i1.ApiService), i0.ɵɵinject(i2.SocketService), i0.ɵɵinject(i3.NotificationService));\n};\n\nChatService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: ChatService,\n  factory: ChatService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "mappings": ";AACA,SAASA,eAAT,QAA2D,MAA3D;AACA,SAASC,GAAT,QAAoB,gBAApB;AACA,SAASC,UAAT,EAAqBC,OAArB,EAA8BC,KAA9B,EAAqCC,IAArC,EAA2CC,YAA3C,QAA8E,eAA9E;;;;;AAOA,OAAM,MAAOC,WAAP,CAAkB;EAyCtBC,YACUC,UADV,EAEUC,aAFV,EAGUC,mBAHV,EAGkD;IAFxC;IACA;IACA;IA3CF,mBAAc,IAAIX,eAAJ,CAAmC,IAAnC,CAAd;IACA,uBAAkB,IAAIA,eAAJ,CAA+B,EAA/B,CAAlB;IACA,0BAAqB,IAAIA,eAAJ,CAA4B,EAA5B,CAArB;IACA,qBAAgB,IAAIA,eAAJ,CAA6B,EAA7B,CAAhB;IACA,2BAAsB,IAAIA,eAAJ,CAAkC,IAAlC,CAAtB;IACA,sBAAiB,IAAIA,eAAJ,CAAoC,IAApC,CAAjB;IACA,sBAAiB,IAAIA,eAAJ,CAA6B,KAA7B,CAAjB;IACA,sBAAiB,IAAIA,eAAJ,CAA6B,KAA7B,CAAjB;IACA,6BAAwB,IAAIA,eAAJ,CAA6B,KAA7B,CAAxB;IACA,2BAAsB,IAAIA,eAAJ,CAAyC,IAAzC,CAAtB;IACA,6BAAwB,IAAIA,eAAJ,CAAoC,IAApC,CAAxB,CAiC0C,CA/BlD;;IACO,aAAQ,KAAKY,WAAL,CAAiBC,YAAjB,EAAR;IACA,iBAAY,KAAKC,eAAL,CAAqBD,YAArB,EAAZ;IACA,oBAAe,KAAKE,kBAAL,CAAwBF,YAAxB,EAAf;IACA,eAAU,KAAKG,aAAL,CAAmBH,YAAnB,EAAV;IACA,qBAAgB,KAAKI,mBAAL,CAAyBJ,YAAzB,EAAhB;IACA,gBAAW,KAAKK,cAAL,CAAoBL,YAApB,EAAX;IACA,gBAAW,KAAKM,cAAL,CAAoBN,YAApB,EAAX;IACA,gBAAW,KAAKO,cAAL,CAAoBP,YAApB,EAAX;IACA,uBAAkB,KAAKQ,qBAAL,CAA2BR,YAA3B,EAAlB;IACA,qBAAgB,KAAKS,mBAAL,CAAyBT,YAAzB,EAAhB;IACA,uBAAkB,KAAKU,qBAAL,CAA2BV,YAA3B,EAAlB;IACA,kBAAa,KAAKH,aAAL,CAAmBc,YAAhC,CAmB2C,CAjBlD;;IACQ,0BAAqB,IAAIxB,eAAJ,CAA8B,EAA9B,CAArB;IACD,oBAAe,KAAKyB,kBAAL,CAAwBZ,YAAxB,EAAf;IACC,qBAAqB,IAArB,CAc0C,CAZlD;;IACO,mBAAc,KAAKa,KAAL,CAAWC,IAAX,CAAgB1B,GAAG,CAAC2B,IAAI,IAAI,CAAC,CAACA,IAAX,CAAnB,CAAd;IAGC,uBAAkB,KAAlB;IACA,yBAAoB,KAApB;IACA,2BAAsB,CAAtB;IACS,6BAAwB,IAAxB,CAKiC,CALH;;IAO7C,IAAI;MACF,KAAKC,oBAAL;MACA,KAAKC,mBAAL;IAGD,CALD,CAKE,OAAOC,KAAP,EAAc;MACdC,OAAO,CAACD,KAAR,CAAc,iCAAd,EAAiDA,KAAjD;IACD;EACF;;EAEOF,oBAAoB;IAC1B,KAAKnB,aAAL,CAAmBuB,EAAnB,CAAsB,SAAtB,EAAiC,MAAK;MACpC;MACA,KAAKd,cAAL,CAAoBe,IAApB,CAAyB,KAAzB;IACD,CAHD;IAKA,KAAKxB,aAAL,CAAmBuB,EAAnB,CAAsB,YAAtB,EAAoC,MAAK,CACvC;IACD,CAFD;IAIA,KAAKvB,aAAL,CAAmBuB,EAAnB,CAAsB,eAAtB,EAAwCF,KAAD,IAAU;MAC/CC,OAAO,CAACD,KAAR,CAAc,0BAAd,EAA0CA,KAA1C;MACA,KAAKZ,cAAL,CAAoBe,IAApB,CAAyB,KAAzB;IACD,CAHD;IAKA,KAAKxB,aAAL,CAAmBuB,EAAnB,CAAsB,YAAtB,EAAqCE,UAAD,IAAwB;MAC1D,KAAKnB,aAAL,CAAmBkB,IAAnB,CAAwBC,UAAU,IAAI,EAAtC;;MACA,IAAIA,UAAU,IAAIA,UAAU,CAACC,MAAX,GAAoB,CAAtC,EAAyC;QACvC,KAAKnB,mBAAL,CAAyBiB,IAAzB,CAA8BC,UAAU,CAAC,CAAD,CAAxC,EADuC,CAEvC;;QACA,KAAKE,kBAAL,CAAwBF,UAAU,CAAC,CAAD,CAAV,CAAcG,EAAtC;MACD,CAJD,MAIO;QACL;QACA,KAAKrB,mBAAL,CAAyBiB,IAAzB,CAA8B,IAA9B;QACA,KAAKpB,eAAL,CAAqBoB,IAArB,CAA0B,EAA1B;MACD;IACF,CAXD;IAaA,KAAKxB,aAAL,CAAmBuB,EAAnB,CAAsB,gBAAtB,EAAyCM,QAAD,IAAwB;MAC9D,KAAKzB,eAAL,CAAqBoB,IAArB,CAA0BK,QAAQ,IAAI,EAAtC;IACD,CAFD;IAIA,KAAK7B,aAAL,CAAmBuB,EAAnB,CAAsB,aAAtB,EAAqC,CAAC;MAAEO;IAAF,CAAD,KAAqC;MACxE,MAAMC,MAAM,GAAG,KAAKzB,aAAL,CAAmB0B,KAAlC;MACA,MAAMC,KAAK,GAAGF,MAAM,CAACG,IAAP,CAAYC,CAAC,IAAIA,CAAC,CAACP,EAAF,KAASE,OAA1B,CAAd;;MACA,IAAIG,KAAJ,EAAW;QACT,KAAK1B,mBAAL,CAAyBiB,IAAzB,CAA8BS,KAA9B;MACD;IACF,CAND;IAQA,KAAKjC,aAAL,CAAmBuB,EAAnB,CAAsB,YAAtB,EAAqCa,OAAD,IAAqB;MACvD,MAAMC,eAAe,GAAG,KAAKjC,eAAL,CAAqB4B,KAA7C;MACA,MAAMM,WAAW,GAAG,KAAKpC,WAAL,CAAiB8B,KAArC;MACA,MAAMO,YAAY,GAAG,KAAKhC,mBAAL,CAAyByB,KAA9C,CAHuD,CAKvD;;MACA,IAAIO,YAAY,IAAIH,OAAO,CAACN,OAAR,KAAoBS,YAAY,CAACX,EAArD,EAAyD;QACvD;MACD,CARsD,CAUvD;;;MACA,MAAMY,eAAe,GAAGH,eAAe,CAACH,IAAhB,CAAqBO,GAAG,IAC9CA,GAAG,CAACb,EAAJ,KAAWQ,OAAO,CAACR,EAAnB,IACCa,GAAG,CAACC,IAAJ,KAAaN,OAAO,CAACM,IAArB,IACAD,GAAG,CAACE,QAAJ,KAAiBP,OAAO,CAACO,QADzB,IAEAC,IAAI,CAACC,GAAL,CAAS,IAAIC,IAAJ,CAASL,GAAG,CAACM,SAAb,EAAwBC,OAAxB,KAAoC,IAAIF,IAAJ,CAASV,OAAO,CAACW,SAAjB,EAA4BC,OAA5B,EAA7C,IAAsF,IAJjE,CAAxB;;MAOA,IAAIR,eAAe,IAAI,CAACA,eAAe,CAACZ,EAAhB,CAAmBqB,UAAnB,CAA8B,OAA9B,CAAxB,EAAgE;QAC9D;MACD,CApBsD,CAsBvD;;;MACA,IAAIb,OAAO,CAACO,QAAR,KAAqBL,WAAzB,EAAsC;QACpC;QACA,MAAMY,eAAe,GAAGb,eAAe,CAACc,SAAhB,CAA0BV,GAAG,IACnDA,GAAG,CAACb,EAAJ,CAAOqB,UAAP,CAAkB,OAAlB,KACAR,GAAG,CAACC,IAAJ,KAAaN,OAAO,CAACM,IADrB,IAEAD,GAAG,CAACE,QAAJ,KAAiBL,WAFjB,IAGAG,GAAG,CAACX,OAAJ,KAAgBM,OAAO,CAACN,OAJF,CAAxB;;QAOA,IAAIoB,eAAe,KAAK,CAAC,CAAzB,EAA4B;UAC1B;UACA,MAAME,eAAe,GAAG,CAAC,GAAGf,eAAJ,CAAxB;UACAe,eAAe,CAACF,eAAD,CAAf,GAAmCd,OAAnC;UACA,KAAKhC,eAAL,CAAqBoB,IAArB,CAA0B,KAAK6B,uBAAL,CAA6BD,eAA7B,CAA1B;QACD,CALD,MAKO;UACL;UACA,MAAMA,eAAe,GAAG,CAAC,GAAGf,eAAJ,EAAqBD,OAArB,CAAxB;UACA,KAAKhC,eAAL,CAAqBoB,IAArB,CAA0B,KAAK6B,uBAAL,CAA6BD,eAA7B,CAA1B;QACD;MACF,CAnBD,MAmBO;QACL;QACA,MAAMA,eAAe,GAAG,CAAC,GAAGf,eAAJ,EAAqBD,OAArB,CAAxB;QACA,KAAKhC,eAAL,CAAqBoB,IAArB,CAA0B,KAAK6B,uBAAL,CAA6BD,eAA7B,CAA1B;MACD,CA9CsD,CAgDvD;;;MACA,IAAIhB,OAAO,CAACO,QAAR,KAAqBL,WAArB,IAAoCgB,QAAQ,CAACC,MAAjD,EAAyD;QACvD,KAAKtD,mBAAL,CAAyBuD,uBAAzB,CAAiDpB,OAAO,CAACO,QAAzD,EAAmEP,OAAO,CAACM,IAA3E;MACD;IACF,CApDD;IAsDA,KAAK1C,aAAL,CAAmBuB,EAAnB,CAAsB,gBAAtB,EAAwC,CAAC;MAAEkC,SAAF;MAAaC;IAAb,CAAD,KAAsE;MAC5G,MAAMrB,eAAe,GAAG,KAAKjC,eAAL,CAAqB4B,KAA7C;MACA,KAAK5B,eAAL,CAAqBoB,IAArB,CACEa,eAAe,CAAC9C,GAAhB,CAAoBkD,GAAG,IACrBA,GAAG,CAACb,EAAJ,KAAW6B,SAAX,GACI,EAAE,GAAGhB,GAAL;QAAUiB;MAAV,CADJ,GAEIjB,GAHN,CADF;IAOD,CATD;IAWA,KAAKzC,aAAL,CAAmBuB,EAAnB,CAAsB,mBAAtB,EAA4CoC,KAAD,IAAkB;MAC3D,KAAKtD,kBAAL,CAAwBmB,IAAxB,CAA6BmC,KAAK,IAAI,EAAtC;IACD,CAFD;IAIA,KAAK3D,aAAL,CAAmBuB,EAAnB,CAAsB,YAAtB,EAAoC,CAAC;MAAEoB;IAAF,CAAD,KAAuC,CACzE;IACD,CAFD;IAIA,KAAK3C,aAAL,CAAmBuB,EAAnB,CAAsB,UAAtB,EAAkC,CAAC;MAAEoB;IAAF,CAAD,KAAuC,CACvE;IACD,CAFD,EAjH0B,CAqH1B;;IACA,KAAK3C,aAAL,CAAmBuB,EAAnB,CAAsB,YAAtB,EAAoC,CAAC;MAAEoB,QAAF;MAAYb,OAAZ;MAAqB8B;IAArB,CAAD,KAA8F;MAGhI,MAAMrB,YAAY,GAAG,KAAKhC,mBAAL,CAAyByB,KAA9C;;MACA,IAAIO,YAAY,IAAIT,OAAO,KAAKS,YAAY,CAACX,EAA7C,EAAiD;QAC/C,MAAMiC,kBAAkB,GAAG,KAAK9C,kBAAL,CAAwBiB,KAAnD;;QAEA,IAAI4B,QAAJ,EAAc;UACZ;UACA,IAAI,CAACC,kBAAkB,CAACC,QAAnB,CAA4BnB,QAA5B,CAAL,EAA4C;YAC1C,KAAK5B,kBAAL,CAAwBS,IAAxB,CAA6B,CAAC,GAAGqC,kBAAJ,EAAwBlB,QAAxB,CAA7B;UACD;QACF,CALD,MAKO;UACL;UACA,KAAK5B,kBAAL,CAAwBS,IAAxB,CAA6BqC,kBAAkB,CAACE,MAAnB,CAA0B7C,IAAI,IAAIA,IAAI,KAAKyB,QAA3C,CAA7B;QACD;MACF;IACF,CAjBD;IAmBA,KAAK3C,aAAL,CAAmBuB,EAAnB,CAAsB,gBAAtB,EAAwC,CAAC;MAAEkC,SAAF;MAAaO,OAAb;MAAsBC;IAAtB,CAAD,KAAiG;MAEvI,MAAM5B,eAAe,GAAG,KAAKjC,eAAL,CAAqB4B,KAA7C;MACA,KAAK5B,eAAL,CAAqBoB,IAArB,CACEa,eAAe,CAAC9C,GAAhB,CAAoBkD,GAAG,IACrBA,GAAG,CAACb,EAAJ,KAAW6B,SAAX,GACI,EAAE,GAAGhB,GAAL;QAAUC,IAAI,EAAEsB,OAAhB;QAAyBE,UAAU,EAAED;MAArC,CADJ,GAEIxB,GAHN,CADF;IAOD,CAVD;IAYA,KAAKzC,aAAL,CAAmBuB,EAAnB,CAAsB,gBAAtB,EAAwC,CAAC;MAAEkC;IAAF,CAAD,KAAyC;MAE/E,MAAMpB,eAAe,GAAG,KAAKjC,eAAL,CAAqB4B,KAA7C;MACA,KAAK5B,eAAL,CAAqBoB,IAArB,CACEa,eAAe,CAAC0B,MAAhB,CAAuBtB,GAAG,IAAIA,GAAG,CAACb,EAAJ,KAAW6B,SAAzC,CADF;IAGD,CAND;IAQA,KAAKzD,aAAL,CAAmBuB,EAAnB,CAAsB,OAAtB,EAAgCF,KAAD,IAAe;MAC5CC,OAAO,CAACD,KAAR,CAAc,eAAd,EAA+BA,KAA/B;IACD,CAFD;EAGD;;EAEOD,mBAAmB;IACzB;IACA,IAAI,KAAK+C,sBAAT,EAAiC;MAC/BC,aAAa,CAAC,KAAKD,sBAAN,CAAb;IACD,CAJwB,CAMzB;IACA;;IACA;;;;;;;;;;;;;;;;;;;;;EAoBD;;EAEKE,KAAK,UAAsE;IAAA;;IAAA,oCAArE1B,QAAqE,EAAnD2B,QAAmD,EAAhCC,aAA4B,IAAI;MAC/E,IAAI;QACF,KAAI,CAAC9D,cAAL,CAAoBe,IAApB,CAAyB,IAAzB;;QACA,MAAMgD,QAAQ,SAAS,KAAI,CAACzE,UAAL,CAAgB0E,SAAhB,CAA0B9B,QAA1B,EAAoC2B,QAApC,EAA8CC,UAAU,IAAIG,SAA5D,EAAuEC,SAAvE,EAAvB,CAFE,CAIF;;QACA,IAAIH,QAAQ,EAAEI,gBAAd,EAAgC;UAC9B,MAAM,IAAIC,KAAJ,CAAU,iCAAV,CAAN;QACD,CAPC,CASF;;;QACA,KAAI,CAAC7E,aAAL,CAAmB8E,OAAnB,CAA2BnC,QAA3B,EAAqC2B,QAArC,EAA+CC,UAA/C;;QAEA,KAAI,CAACrE,WAAL,CAAiBsB,IAAjB,CAAsBmB,QAAtB;;QACA,KAAI,CAACjC,cAAL,CAAoBc,IAApB,CAAyBgD,QAAQ,EAAEO,OAAV,IAAqB,KAA9C;;QACA,KAAI,CAACnE,mBAAL,CAAyBY,IAAzB,CAA8BgD,QAAQ,EAAEQ,YAAV,IAA0B,IAAxD,EAdE,CAgBF;QACA;QACA;QAEA;;;QACAC,UAAU,CAAC,MAAK;UACd,IAAI,KAAI,CAAC3E,aAAL,CAAmB0B,KAAnB,CAAyBN,MAAzB,KAAoC,CAAxC,EAA2C;YAEzC,KAAI,CAACwD,cAAL,CAAoBvC,QAApB;UACD;QACF,CALS,EAKP,IALO,CAAV;MAOD,CA5BD,CA4BE,OAAOtB,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,eAAd,EAA+BA,KAA/B;QACA,MAAMA,KAAN;MACD,CA/BD,SA+BU;QACR,KAAI,CAACZ,cAAL,CAAoBe,IAApB,CAAyB,KAAzB;MACD;IAlC8E;EAmChF;;EAEKG,kBAAkB,CAACG,OAAD,EAAgB;IAAA;;IAAA;MACtC;MACA,IAAI,MAAI,CAACqD,iBAAT,EAA4B;QAC1B;MACD,CAJqC,CAMtC;;;MACA,MAAMC,GAAG,GAAGtC,IAAI,CAACsC,GAAL,EAAZ;;MACA,IAAIA,GAAG,GAAG,MAAI,CAACC,mBAAX,GAAiC,MAAI,CAACC,qBAA1C,EAAiE;QAC/D;MACD;;MACD,MAAI,CAACD,mBAAL,GAA2BD,GAA3B;MAEA,MAAI,CAACD,iBAAL,GAAyB,IAAzB;;MACA,IAAI;QACF,MAAMtD,QAAQ,SAAS,MAAI,CAAC9B,UAAL,CAAgBwF,WAAhB,CAA4BzD,OAA5B,EAAqC,EAArC,EAAyC6C,SAAzC,EAAvB,CADE,CAGF;QACA;;QACA,MAAI,CAACvE,eAAL,CAAqBoB,IAArB,CAA0BK,QAAQ,IAAI,EAAtC;MACD,CAND,CAME,OAAOR,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,0BAAd,EAA0CA,KAA1C,EADc,CAEd;;QACA,MAAI,CAACjB,eAAL,CAAqBoB,IAArB,CAA0B,EAA1B;MACD,CAVD,SAUU;QACR,MAAI,CAAC2D,iBAAL,GAAyB,KAAzB;MACD;IA1BqC;EA2BvC;;EAEKD,cAAc,CAACvC,QAAD,EAAiB;IAAA;;IAAA;MACnC;MACA,IAAI,MAAI,CAAC6C,eAAT,EAA0B;QACxB;MACD;;MAED,MAAI,CAACA,eAAL,GAAuB,IAAvB;;MACA,IAAI;QACF,MAAMzD,MAAM,SAAS,MAAI,CAAChC,UAAL,CAAgB0F,aAAhB,CAA8B9C,QAA9B,EAAwCgC,SAAxC,EAArB;;QACA,MAAI,CAACrE,aAAL,CAAmBkB,IAAnB,CAAwBO,MAAM,IAAI,EAAlC;;QACA,IAAIA,MAAM,IAAIA,MAAM,CAACL,MAAP,GAAgB,CAA9B,EAAiC;UAC/B,MAAI,CAACnB,mBAAL,CAAyBiB,IAAzB,CAA8BO,MAAM,CAAC,CAAD,CAApC;;UACA,MAAI,CAACJ,kBAAL,CAAwBI,MAAM,CAAC,CAAD,CAAN,CAAUH,EAAlC;QACD;MACF,CAPD,CAOE,OAAOP,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,6BAAd,EAA6CA,KAA7C;MACD,CATD,SASU;QACR,MAAI,CAACmE,eAAL,GAAuB,KAAvB;MACD;IAlBkC;EAmBpC,CA7UqB,CAmVtB;;;EACQnC,uBAAuB,CAACxB,QAAD,EAAoB;IACjD,OAAOA,QAAQ,CAAC6D,IAAT,CAAc,CAACC,CAAD,EAAIC,CAAJ,KAAS;MAC5B,MAAMC,KAAK,GAAG,IAAI/C,IAAJ,CAAS6C,CAAC,CAAC5C,SAAX,EAAsBC,OAAtB,EAAd;MACA,MAAM8C,KAAK,GAAG,IAAIhD,IAAJ,CAAS8C,CAAC,CAAC7C,SAAX,EAAsBC,OAAtB,EAAd;MACA,OAAO6C,KAAK,GAAGC,KAAf;IACD,CAJM,CAAP;EAKD,CA1VqB,CA4VtB;;;EACQC,wBAAwB,CAACC,MAAD,EAAiBC,WAAjB,EAAqC;IACnE,MAAM5D,eAAe,GAAG,KAAKjC,eAAL,CAAqB4B,KAA7C;IACA,MAAMoB,eAAe,GAAGf,eAAe,CAAC9C,GAAhB,CAAoBkD,GAAG,IAC7CA,GAAG,CAACb,EAAJ,KAAWoE,MAAX,GAAoBC,WAApB,GAAkCxD,GADZ,CAAxB;IAGA,KAAKrC,eAAL,CAAqBoB,IAArB,CAA0B,KAAK6B,uBAAL,CAA6BD,eAA7B,CAA1B;EAED,CApWqB,CAsWtB;;;EACQ8C,uBAAuB,CAACF,MAAD,EAAe;IAC5C,MAAM3D,eAAe,GAAG,KAAKjC,eAAL,CAAqB4B,KAA7C;IACA,MAAMoB,eAAe,GAAGf,eAAe,CAAC0B,MAAhB,CAAuBtB,GAAG,IAAIA,GAAG,CAACb,EAAJ,KAAWoE,MAAzC,CAAxB;IACA,KAAK5F,eAAL,CAAqBoB,IAArB,CAA0B4B,eAA1B;EAED,CA5WqB,CA8WtB;;;EACQ+C,aAAa,CAAC/D,OAAD,EAAmBP,QAAnB,EAAsC;IACzD,OAAOA,QAAQ,CAACuE,IAAT,CAAc3D,GAAG,IACtBA,GAAG,CAACb,EAAJ,KAAWQ,OAAO,CAACR,EAAnB,IACCa,GAAG,CAACC,IAAJ,KAAaN,OAAO,CAACM,IAArB,IACAD,GAAG,CAACE,QAAJ,KAAiBP,OAAO,CAACO,QADzB,IAEAC,IAAI,CAACC,GAAL,CAAS,IAAIC,IAAJ,CAASL,GAAG,CAACM,SAAb,EAAwBC,OAAxB,KAAoC,IAAIF,IAAJ,CAASV,OAAO,CAACW,SAAjB,EAA4BC,OAA5B,EAA7C,IAAsF,IAJlF,CAAP;EAMD;;EAEDqD,SAAS,CAACvE,OAAD,EAAgB;IACvB,MAAMQ,WAAW,GAAG,KAAKpC,WAAL,CAAiB8B,KAArC;;IACA,IAAI,CAACF,OAAD,IAAY,CAACQ,WAAjB,EAA8B;MAC5BhB,OAAO,CAACD,KAAR,CAAc,6CAAd;MACA;IACD,CALsB,CASvB;;;IACA,IAAI,CAAC,KAAKrB,aAAL,CAAmBsG,kBAAnB,CAAsCtE,KAA3C,EAAkD;MAChDV,OAAO,CAACD,KAAR,CAAc,0CAAd;MACA;IACD;;IAED,KAAKrB,aAAL,CAAmBqG,SAAnB,CAA6BvE,OAA7B;IACA,MAAMC,MAAM,GAAG,KAAKzB,aAAL,CAAmB0B,KAAlC;IACA,MAAMC,KAAK,GAAGF,MAAM,CAACG,IAAP,CAAYC,CAAC,IAAIA,CAAC,CAACP,EAAF,KAASE,OAA1B,CAAd;;IACA,IAAIG,KAAJ,EAAW;MACT,KAAK1B,mBAAL,CAAyBiB,IAAzB,CAA8BS,KAA9B,EADS,CAET;;MACA,KAAKN,kBAAL,CAAwBG,OAAxB;IACD,CAJD,MAIO;MACLR,OAAO,CAACD,KAAR,CAAc,iCAAd,EAAiDS,OAAjD;IACD;EACF;;EAEDyE,WAAW,CAAC7D,IAAD,EAAe8D,YAA2B,IAA1C,EAA8C;IACvD,MAAMlE,WAAW,GAAG,KAAKpC,WAAL,CAAiB8B,KAArC;IACA,MAAMO,YAAY,GAAG,KAAKhC,mBAAL,CAAyByB,KAA9C;;IAEA,IAAI,CAACU,IAAI,CAAC+D,IAAL,EAAL,EAAkB;MAChBnF,OAAO,CAACD,KAAR,CAAc,kCAAd;MACA;IACD;;IAED,IAAI,CAACiB,WAAL,EAAkB;MAChBhB,OAAO,CAACD,KAAR,CAAc,0CAAd;MACA;IACD;;IAED,IAAI,CAACkB,YAAL,EAAmB;MACjBjB,OAAO,CAACD,KAAR,CAAc,4DAAd,EAA4E,KAAKf,aAAL,CAAmB0B,KAA/F,EADiB,CAEjB;;MACA,MAAMD,MAAM,GAAG,KAAKzB,aAAL,CAAmB0B,KAAlC;;MACA,IAAID,MAAM,IAAIA,MAAM,CAACL,MAAP,GAAgB,CAA9B,EAAiC;QAE/B,KAAKnB,mBAAL,CAAyBiB,IAAzB,CAA8BO,MAAM,CAAC,CAAD,CAApC;QACA,KAAKJ,kBAAL,CAAwBI,MAAM,CAAC,CAAD,CAAN,CAAUH,EAAlC,EAH+B,CAI/B;QACA;;QACA;MACD,CAPD,MAOO;QACLN,OAAO,CAACD,KAAR,CAAc,8BAAd;QACA;MACD;IACF,CA7BsD,CA+BvD;;;IACA,MAAMqF,iBAAiB,GAAY;MACjC9E,EAAE,EAAE,QAAQkB,IAAI,CAACsC,GAAL,EAAU,IAAIxC,IAAI,CAAC+D,MAAL,GAAcC,QAAd,CAAuB,EAAvB,EAA2BC,MAA3B,CAAkC,CAAlC,EAAqC,CAArC,CAAuC,EADhC;MAEjCnE,IAAI,EAAEA,IAAI,CAAC+D,IAAL,EAF2B;MAGjC9D,QAAQ,EAAEL,WAHuB;MAIjCS,SAAS,EAAE,IAAID,IAAJ,GAAWgE,WAAX,EAJsB;MAKjChF,OAAO,EAAES,YAAY,CAACX,EALW;MAMjCmF,OAAO,EAAEP,SAAS,IAAI9B,SANW;MAOjChB,SAAS,EAAE;IAPsB,CAAnC,CAhCuD,CA0CvD;;IACA,MAAMrB,eAAe,GAAG,KAAKjC,eAAL,CAAqB4B,KAA7C;IACA,MAAMoB,eAAe,GAAG,CAAC,GAAGf,eAAJ,EAAqBqE,iBAArB,CAAxB;IACA,KAAKtG,eAAL,CAAqBoB,IAArB,CAA0B4B,eAA1B,EA7CuD,CAgDvD;;IACA,KAAK4D,oBAAL,CAA0BtE,IAA1B,EAAgCH,YAAY,CAACX,EAA7C,EAAiD4E,SAAjD,EAA4DE,iBAA5D;EACD;;EAEaM,oBAAoB,qBAKV;IAAA;;IAAA,oCAJtBtE,IAIsB,EAHtBZ,OAGsB,EAFtB0E,SAEsB,EADtBE,iBACsB,EAAtBO,aAAqB,CAAC;MAEtB,MAAMC,UAAU,GAAG,CAAnB;MACA,MAAMC,UAAU,GAAG,OAAOvE,IAAI,CAACwE,GAAL,CAAS,CAAT,EAAYH,UAAZ,CAA1B,CAHsB,CAG6B;;MAEnD,IAAI;QACF,IAAI,MAAI,CAACjH,aAAL,CAAmBqH,WAAnB,EAAJ,EAAsC;UACpC;UACA,MAAMC,QAAQ,SAAS,MAAI,CAACtH,aAAL,CAAmBuH,WAAnB,CAA+B,aAA/B,EAA8C;YACnE7E,IADmE;YAEnEZ,OAFmE;YAGnEiF,OAAO,EAAEP,SAH0D;YAInE/C,SAAS,EAAEiD,iBAAiB,CAAC9E;UAJsC,CAA9C,CAAvB;;UAOA,IAAI0F,QAAQ,CAACE,OAAb,EAAsB;YAEpB;YACA,MAAI,CAACzB,wBAAL,CAA8BW,iBAAiB,CAAC9E,EAAhD,EAAoD0F,QAAQ,CAAClF,OAA7D;;YACA;UACD,CALD,MAKO;YACL,MAAM,IAAIyC,KAAJ,CAAUyC,QAAQ,CAACjG,KAAT,IAAkB,eAA5B,CAAN;UACD;QACF,CAjBD,MAiBO;UACL,MAAM,IAAIwD,KAAJ,CAAU,sBAAV,CAAN;QACD;MACF,CArBD,CAqBE,OAAOxD,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,qCAAqC4F,UAAU,GAAG,CAAC,IAAjE,EAAuE5F,KAAvE;;QAEA,IAAI4F,UAAU,GAAGC,UAAjB,EAA6B;UAC3B;UAEAjC,UAAU,CAAC,MAAK;YACd,MAAI,CAAC+B,oBAAL,CAA0BtE,IAA1B,EAAgCZ,OAAhC,EAAyC0E,SAAzC,EAAoDE,iBAApD,EAAuEO,UAAU,GAAG,CAApF;UACD,CAFS,EAEPE,UAFO,CAAV;QAGD,CAND,MAMO;UACL;UAEA,MAAI,CAACM,0BAAL,CAAgC/E,IAAhC,EAAsCZ,OAAtC,EAA+C0E,SAA/C,EAA0DE,iBAA1D;QACD;MACF;IAxCqB;EAyCvB;;EAEOe,0BAA0B,CAChC/E,IADgC,EAEhCZ,OAFgC,EAGhC0E,SAHgC,EAIhCE,iBAJgC,EAIN;IAE1B,MAAMpE,WAAW,GAAG,KAAKpC,WAAL,CAAiB8B,KAArC;;IACA,IAAI,CAACM,WAAL,EAAkB;MAChB,KAAK4D,uBAAL,CAA6BQ,iBAAiB,CAAC9E,EAA/C;MACA;IACD;;IAED,KAAK7B,UAAL,CAAgBwG,WAAhB,CAA4B7D,IAA5B,EAAkCJ,WAAlC,EAA+CR,OAA/C,EAAwD0E,SAAxD,EAAmEkB,SAAnE,CAA6E;MAC3ElG,IAAI,EAAG8F,QAAD,IAAa;QAEjB,KAAKvB,wBAAL,CAA8BW,iBAAiB,CAAC9E,EAAhD,EAAoD0F,QAApD;MACD,CAJ0E;MAK3EjG,KAAK,EAAGA,KAAD,IAAU;QACfC,OAAO,CAACD,KAAR,CAAc,wCAAd,EAAwDA,KAAxD;QACA,KAAK6E,uBAAL,CAA6BQ,iBAAiB,CAAC9E,EAA/C,EAFe,CAGf;;QACA,KAAK+F,gBAAL,CAAsB,2CAAtB;MACD;IAV0E,CAA7E;EAYD;;EAEOA,gBAAgB,CAACvF,OAAD,EAAgB;IACtC;IACAd,OAAO,CAACD,KAAR,CAAc,mBAAd,EAAmCe,OAAnC,EAFsC,CAGtC;EACD,CArhBqB,CAuhBtB;;;EACAwF,WAAW;IACT,MAAMrF,YAAY,GAAG,KAAKhC,mBAAL,CAAyByB,KAA9C;;IACA,IAAIO,YAAY,IAAI,KAAKvC,aAAL,CAAmBqH,WAAnB,EAApB,EAAsD;MACpD,KAAKrH,aAAL,CAAmB6H,mBAAnB,CAAuCtF,YAAY,CAACX,EAApD,EAAwD,IAAxD,EADoD,CAGpD;;MACA,IAAI,KAAKkG,aAAT,EAAwB;QACtBC,YAAY,CAAC,KAAKD,aAAN,CAAZ;MACD,CANmD,CAQpD;;;MACA,KAAKA,aAAL,GAAqB7C,UAAU,CAAC,MAAK;QACnC,KAAK+C,UAAL;MACD,CAF8B,EAE5B,IAF4B,CAA/B;IAGD;EACF;;EAEDA,UAAU;IACR,MAAMzF,YAAY,GAAG,KAAKhC,mBAAL,CAAyByB,KAA9C;;IACA,IAAIO,YAAY,IAAI,KAAKvC,aAAL,CAAmBqH,WAAnB,EAApB,EAAsD;MACpD,KAAKrH,aAAL,CAAmB6H,mBAAnB,CAAuCtF,YAAY,CAACX,EAApD,EAAwD,KAAxD,EADoD,CAGpD;;MACA,IAAI,KAAKkG,aAAT,EAAwB;QACtBC,YAAY,CAAC,KAAKD,aAAN,CAAZ;QACA,KAAKA,aAAL,GAAqB,IAArB;MACD;IACF;EACF;;EAEDG,cAAc,CAAC7F,OAAD,EAAiB;IAC7B,KAAK5B,cAAL,CAAoBgB,IAApB,CAAyBY,OAAzB;EACD;;EAED8F,WAAW;IACT,KAAK1H,cAAL,CAAoBgB,IAApB,CAAyB,IAAzB;EACD;;EAEK2G,WAAW,CAAC1E,SAAD,EAAoB2E,KAApB,EAAiC;IAAA;;IAAA;MAChD,IAAI;QACF,IAAI,MAAI,CAACpI,aAAL,CAAmBqH,WAAnB,EAAJ,EAAsC;UACpC;UACA,MAAMC,QAAQ,SAAS,MAAI,CAACtH,aAAL,CAAmBuH,WAAnB,CAA+B,aAA/B,EAA8C;YACnE9D,SADmE;YAEnE2E;UAFmE,CAA9C,CAAvB;;UAKA,IAAId,QAAQ,CAACE,OAAb,EAAsB,CAErB,CAFD,MAEO;YACL,MAAM,IAAI3C,KAAJ,CAAUyC,QAAQ,CAACjG,KAAT,IAAkB,wBAA5B,CAAN;UACD;QACF,CAZD,MAYO;UACL;UACA,MAAMiB,WAAW,GAAG,MAAI,CAACpC,WAAL,CAAiB8B,KAArC;;UACA,IAAIM,WAAJ,EAAiB;YACf,MAAI,CAACvC,UAAL,CAAgBoI,WAAhB,CAA4B1E,SAA5B,EAAuC2E,KAAvC,EAA8C9F,WAA9C,EAA2DoF,SAA3D,CAAqE;cACnElG,IAAI,EAAG8F,QAAD,IAAa,CAElB,CAHkE;cAInEjG,KAAK,EAAGA,KAAD,IAAU;gBACfC,OAAO,CAACD,KAAR,CAAc,wCAAd,EAAwDA,KAAxD;cACD;YANkE,CAArE;UAQD;QACF;MACF,CA3BD,CA2BE,OAAOA,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,2BAAd,EAA2CA,KAA3C,EADc,CAEd;;QACA,MAAI,CAACrB,aAAL,CAAmBmI,WAAnB,CAA+B1E,SAA/B,EAA0C2E,KAA1C;MACD;IAhC+C;EAiCjD;;EAEDC,cAAc,CAACC,IAAD,EAA4C;IACxD,KAAKtI,aAAL,CAAmBqI,cAAnB,CAAkCC,IAAlC;EACD;;EAEKC,aAAa,CAAC9E,SAAD,EAAoBO,OAApB,EAAmC;IAAA;;IAAA;MACpD,MAAM1B,WAAW,GAAG,MAAI,CAACpC,WAAL,CAAiB8B,KAArC;;MACA,IAAI,CAACM,WAAL,EAAkB;QAChBhB,OAAO,CAACD,KAAR,CAAc,4CAAd;QACA;MACD;;MAED,IAAI;QACF;QACA,MAAMgB,eAAe,GAAG,MAAI,CAACjC,eAAL,CAAqB4B,KAA7C;;QACA,MAAI,CAAC5B,eAAL,CAAqBoB,IAArB,CACEa,eAAe,CAAC9C,GAAhB,CAAoBkD,GAAG,IACrBA,GAAG,CAACb,EAAJ,KAAW6B,SAAX,GACI,EAAE,GAAGhB,GAAL;UAAUC,IAAI,EAAEsB,OAAhB;UAAyBE,UAAU,EAAE,IAAIpB,IAAJ,GAAWgE,WAAX;QAArC,CADJ,GAEIrE,GAHN,CADF,EAHE,CAWF;;;QACA,IAAI,MAAI,CAACzC,aAAL,CAAmBsG,kBAAnB,CAAsCtE,KAA1C,EAAiD;UAC/C,MAAI,CAAChC,aAAL,CAAmBuI,aAAnB,CAAiC9E,SAAjC,EAA4CO,OAA5C;QACD,CAFD,MAEO;UACL;UACA,MAAMwE,cAAc,SAAS,MAAI,CAACzI,UAAL,CAAgBwI,aAAhB,CAA8B9E,SAA9B,EAAyCO,OAAzC,EAAkD1B,WAAlD,EAA+DqC,SAA/D,EAA7B;;UACA,MAAI,CAACvE,eAAL,CAAqBoB,IAArB,CACEa,eAAe,CAAC9C,GAAhB,CAAoBkD,GAAG,IACrBA,GAAG,CAACb,EAAJ,KAAW6B,SAAX,GACI,EAAE,GAAGhB,GAAL;YAAUC,IAAI,EAAEsB,OAAhB;YAAyBE,UAAU,EAAEsE,cAAc,CAACtE;UAApD,CADJ,GAEIzB,GAHN,CADF;QAOD;MACF,CAzBD,CAyBE,OAAOpB,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,2BAAd,EAA2CA,KAA3C;QACA,MAAMA,KAAN;MACD;IAnCmD;EAoCrD;;EAEKoH,aAAa,CAAChF,SAAD,EAAkB;IAAA;;IAAA;MACnC,MAAMnB,WAAW,GAAG,MAAI,CAACpC,WAAL,CAAiB8B,KAArC;;MACA,IAAI,CAACM,WAAL,EAAkB;QAChBhB,OAAO,CAACD,KAAR,CAAc,4CAAd;QACA;MACD;;MAED,IAAI;QACF;QACA,MAAMgB,eAAe,GAAG,MAAI,CAACjC,eAAL,CAAqB4B,KAA7C;;QACA,MAAI,CAAC5B,eAAL,CAAqBoB,IAArB,CACEa,eAAe,CAAC0B,MAAhB,CAAuBtB,GAAG,IAAIA,GAAG,CAACb,EAAJ,KAAW6B,SAAzC,CADF,EAHE,CAOF;;;QACA,IAAI,MAAI,CAACzD,aAAL,CAAmBsG,kBAAnB,CAAsCtE,KAA1C,EAAiD;UAC/C,MAAI,CAAChC,aAAL,CAAmByI,aAAnB,CAAiChF,SAAjC;QACD,CAFD,MAEO;UACL;UACA,MAAM,MAAI,CAAC1D,UAAL,CAAgB0I,aAAhB,CAA8BhF,SAA9B,EAAyCnB,WAAzC,EAAsDqC,SAAtD,EAAN;QACD;MACF,CAdD,CAcE,OAAOtD,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,2BAAd,EAA2CA,KAA3C;QACA,MAAMA,KAAN;MACD;IAxBkC;EAyBpC;;EAEDqH,cAAc;IACZ,KAAK/H,qBAAL,CAA2Ba,IAA3B,CAAgC,IAAhC;EACD;;EAEDmH,cAAc;IACZ,KAAKhI,qBAAL,CAA2Ba,IAA3B,CAAgC,KAAhC;EACD;;EAEDoH,mBAAmB,CAACxG,OAAD,EAAiB;IAClC,KAAKvB,qBAAL,CAA2BW,IAA3B,CAAgCY,OAAhC;EACD;;EAEDyG,oBAAoB;IAClB,KAAKhI,qBAAL,CAA2BW,IAA3B,CAAgC,IAAhC;EACD;;EAEDsH,MAAM;IACJ,IAAI;MACF;MACA,KAAK9I,aAAL,CAAmB+I,UAAnB,GAFE,CAIF;;MACA,IAAI,KAAK5E,sBAAT,EAAiC;QAC/BC,aAAa,CAAC,KAAKD,sBAAN,CAAb;QACA,KAAKA,sBAAL,GAA8B,IAA9B;MACD,CARC,CAUF;;;MACA,KAAKjE,WAAL,CAAiBsB,IAAjB,CAAsB,IAAtB;MACA,KAAKpB,eAAL,CAAqBoB,IAArB,CAA0B,EAA1B;MACA,KAAKnB,kBAAL,CAAwBmB,IAAxB,CAA6B,EAA7B;MACA,KAAKlB,aAAL,CAAmBkB,IAAnB,CAAwB,EAAxB;MACA,KAAKjB,mBAAL,CAAyBiB,IAAzB,CAA8B,IAA9B;MACA,KAAKhB,cAAL,CAAoBgB,IAApB,CAAyB,IAAzB;MACA,KAAKf,cAAL,CAAoBe,IAApB,CAAyB,KAAzB;MACA,KAAKd,cAAL,CAAoBc,IAApB,CAAyB,KAAzB;MACA,KAAKb,qBAAL,CAA2Ba,IAA3B,CAAgC,KAAhC;MACA,KAAKZ,mBAAL,CAAyBY,IAAzB,CAA8B,IAA9B;MACA,KAAKX,qBAAL,CAA2BW,IAA3B,CAAgC,IAAhC;IAGD,CAxBD,CAwBE,OAAOH,KAAP,EAAc;MACdC,OAAO,CAACD,KAAR,CAAc,sBAAd,EAAsCA,KAAtC;IACD;EACF,CAltBqB,CAotBtB;;;EACA2H,mBAAmB;IACjB,KAAKjJ,UAAL,CAAgBkJ,eAAhB,GAAkCvB,SAAlC,CAA4C;MAC1ClG,IAAI,EAAG8G,IAAD,IAAS;QACb,KAAK1H,mBAAL,CAAyBY,IAAzB,CAA8B8G,IAA9B;MACD,CAHyC;MAI1CjH,KAAK,EAAGA,KAAD,IAAU;QACfC,OAAO,CAACD,KAAR,CAAc,kCAAd,EAAkDA,KAAlD;MACD;IANyC,CAA5C;EAQD;;AA9tBqB;;;mBAAXxB,aAAWqJ;AAAA;;;SAAXrJ;EAAWsJ,SAAXtJ,WAAW;EAAAuJ,YAFV", "names": ["BehaviorSubject", "map", "ApiService", "Message", "Group", "User", "SecurityInfo", "ChatService", "constructor", "apiService", "socketService", "notificationService", "userSubject", "asObservable", "messagesSubject", "onlineUsersSubject", "groupsSubject", "currentGroupSubject", "replyToSubject", "loadingSubject", "isAdminSubject", "showAdminPanelSubject", "securityInfoSubject", "editingMessageSubject", "isConnected$", "typingUsersSubject", "user$", "pipe", "user", "setupSocketListeners", "setupMessageRefresh", "error", "console", "on", "next", "userGroups", "length", "loadRecentMessages", "id", "messages", "groupId", "groups", "value", "group", "find", "g", "message", "currentMessages", "currentUser", "currentGroup", "existingMessage", "msg", "text", "username", "Math", "abs", "Date", "timestamp", "getTime", "startsWith", "optimisticIndex", "findIndex", "updatedMessages", "sortMessagesByTimestamp", "document", "hidden", "showMessageNotification", "messageId", "reactions", "users", "isTyping", "currentTypingUsers", "includes", "filter", "newText", "updatedAt", "updated_at", "messageRefreshInterval", "clearInterval", "login", "password", "inviteCode", "userData", "loginUser", "undefined", "to<PERSON>romise", "requiresPassword", "Error", "connect", "isAdmin", "securityInfo", "setTimeout", "loadUserGroups", "isLoadingMessages", "now", "lastMessageLoadTime", "MESSAGE_LOAD_THROTTLE", "getMessages", "isLoadingGroups", "getUserGroups", "sort", "a", "b", "timeA", "timeB", "replaceOptimisticMessage", "tempId", "realMessage", "removeOptimisticMessage", "messageExists", "some", "joinGroup", "isConnectedSubject", "sendMessage", "replyToId", "trim", "optimisticMessage", "random", "toString", "substr", "toISOString", "replyTo", "sendMessageWithRetry", "retryCount", "maxRetries", "retry<PERSON><PERSON><PERSON>", "pow", "isConnected", "response", "emitWithAck", "success", "sendMessageViaHttpFallback", "subscribe", "showMessageError", "startTyping", "sendTypingIndicator", "typingTimeout", "clearTimeout", "stopTyping", "replyToMessage", "cancelReply", "addReaction", "emoji", "removeReaction", "data", "updateMessage", "updatedMessage", "deleteMessage", "showAdminPanel", "hideAdminPanel", "startEditingMessage", "cancelEditingMessage", "logout", "disconnect", "refreshSecurityInfo", "getSecurityInfo", "i0", "factory", "providedIn"], "sourceRoot": "", "sources": ["R:\\chateye\\FrontendAngular\\src\\app\\services\\chat.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable, combineLatest } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\nimport { ApiService, Message, Group, User, SecurityInfo, LoginResponse } from './api.service';\r\nimport { SocketService } from './socket.service';\r\nimport { NotificationService } from './notification.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ChatService {\r\n  private userSubject = new BehaviorSubject<string | null>(null);\r\n  private messagesSubject = new BehaviorSubject<Message[]>([]);\r\n  private onlineUsersSubject = new BehaviorSubject<User[]>([]);\r\n  private groupsSubject = new BehaviorSubject<Group[]>([]);\r\n  private currentGroupSubject = new BehaviorSubject<Group | null>(null);\r\n  private replyToSubject = new BehaviorSubject<Message | null>(null);\r\n  private loadingSubject = new BehaviorSubject<boolean>(false);\r\n  private isAdminSubject = new BehaviorSubject<boolean>(false);\r\n  private showAdminPanelSubject = new BehaviorSubject<boolean>(false);\r\n  private securityInfoSubject = new BehaviorSubject<SecurityInfo | null>(null);\r\n  private editingMessageSubject = new BehaviorSubject<Message | null>(null);\r\n\r\n  // Public observables\r\n  public user$ = this.userSubject.asObservable();\r\n  public messages$ = this.messagesSubject.asObservable();\r\n  public onlineUsers$ = this.onlineUsersSubject.asObservable();\r\n  public groups$ = this.groupsSubject.asObservable();\r\n  public currentGroup$ = this.currentGroupSubject.asObservable();\r\n  public replyTo$ = this.replyToSubject.asObservable();\r\n  public loading$ = this.loadingSubject.asObservable();\r\n  public isAdmin$ = this.isAdminSubject.asObservable();\r\n  public showAdminPanel$ = this.showAdminPanelSubject.asObservable();\r\n  public securityInfo$ = this.securityInfoSubject.asObservable();\r\n  public editingMessage$ = this.editingMessageSubject.asObservable();\r\n  public connected$ = this.socketService.isConnected$;\r\n\r\n  // Typing indicators\r\n  private typingUsersSubject = new BehaviorSubject<string[]>([]);\r\n  public typingUsers$ = this.typingUsersSubject.asObservable();\r\n  private typingTimeout: any = null;\r\n\r\n  // Computed observables\r\n  public isLoggedIn$ = this.user$.pipe(map(user => !!user))\r\n\r\n  private messageRefreshInterval: any;\r\n  private isLoadingGroups = false;\r\n  private isLoadingMessages = false;\r\n  private lastMessageLoadTime = 0;\r\n  private readonly MESSAGE_LOAD_THROTTLE = 1000; // 1 second throttle\r\n\r\n  constructor(\r\n    private apiService: ApiService,\r\n    private socketService: SocketService,\r\n    private notificationService: NotificationService\r\n  ) {\r\n    try {\r\n      this.setupSocketListeners();\r\n      this.setupMessageRefresh();\r\n      \r\n\r\n    } catch (error) {\r\n      console.error('Error initializing ChatService:', error);\r\n    }\r\n  }\r\n\r\n  private setupSocketListeners(): void {\r\n    this.socketService.on('connect', () => {\r\n      // Clear any loading states when connected\r\n      this.loadingSubject.next(false);\r\n    });\r\n\r\n    this.socketService.on('disconnect', () => {\r\n      // Don't set loading to true on disconnect to avoid UI freeze\r\n    });\r\n\r\n    this.socketService.on('connect_error', (error) => {\r\n      console.error('Socket connection error:', error);\r\n      this.loadingSubject.next(false);\r\n    });\r\n\r\n    this.socketService.on('userGroups', (userGroups: Group[]) => {\r\n      this.groupsSubject.next(userGroups || []);\r\n      if (userGroups && userGroups.length > 0) {\r\n        this.currentGroupSubject.next(userGroups[0]);\r\n        // Load messages for the selected group\r\n        this.loadRecentMessages(userGroups[0].id);\r\n      } else {\r\n        // Clear current group and messages if no groups\r\n        this.currentGroupSubject.next(null);\r\n        this.messagesSubject.next([]);\r\n      }\r\n    });\r\n\r\n    this.socketService.on('recentMessages', (messages: Message[]) => {\r\n      this.messagesSubject.next(messages || []);\r\n    });\r\n\r\n    this.socketService.on('groupJoined', ({ groupId }: { groupId: string }) => {\r\n      const groups = this.groupsSubject.value;\r\n      const group = groups.find(g => g.id === groupId);\r\n      if (group) {\r\n        this.currentGroupSubject.next(group);\r\n      }\r\n    });\r\n\r\n    this.socketService.on('newMessage', (message: Message) => {\r\n      const currentMessages = this.messagesSubject.value;\r\n      const currentUser = this.userSubject.value;\r\n      const currentGroup = this.currentGroupSubject.value;\r\n\r\n      // Only process messages for the current group to avoid confusion\r\n      if (currentGroup && message.groupId !== currentGroup.id) {\r\n        return;\r\n      }\r\n\r\n      // Check for duplicate messages to prevent double-display\r\n      const existingMessage = currentMessages.find(msg =>\r\n        msg.id === message.id ||\r\n        (msg.text === message.text &&\r\n         msg.username === message.username &&\r\n         Math.abs(new Date(msg.timestamp).getTime() - new Date(message.timestamp).getTime()) < 5000)\r\n      );\r\n\r\n      if (existingMessage && !existingMessage.id.startsWith('temp_')) {\r\n        return;\r\n      }\r\n\r\n      // Check if this is our own message (to replace optimistic message)\r\n      if (message.username === currentUser) {\r\n        // Find and replace optimistic message with real message\r\n        const optimisticIndex = currentMessages.findIndex(msg =>\r\n          msg.id.startsWith('temp_') &&\r\n          msg.text === message.text &&\r\n          msg.username === currentUser &&\r\n          msg.groupId === message.groupId\r\n        );\r\n\r\n        if (optimisticIndex !== -1) {\r\n          // Replace optimistic message\r\n          const updatedMessages = [...currentMessages];\r\n          updatedMessages[optimisticIndex] = message;\r\n          this.messagesSubject.next(this.sortMessagesByTimestamp(updatedMessages));\r\n        } else {\r\n          // Add new message if no optimistic message found (e.g., from another tab)\r\n          const updatedMessages = [...currentMessages, message];\r\n          this.messagesSubject.next(this.sortMessagesByTimestamp(updatedMessages));\r\n        }\r\n      } else {\r\n        // Add message from other users\r\n        const updatedMessages = [...currentMessages, message];\r\n        this.messagesSubject.next(this.sortMessagesByTimestamp(updatedMessages));\r\n      }\r\n\r\n      // Show notification if not current user and window not focused\r\n      if (message.username !== currentUser && document.hidden) {\r\n        this.notificationService.showMessageNotification(message.username, message.text);\r\n      }\r\n    });\r\n\r\n    this.socketService.on('reactionUpdate', ({ messageId, reactions }: { messageId: string; reactions: any[] }) => {\r\n      const currentMessages = this.messagesSubject.value;\r\n      this.messagesSubject.next(\r\n        currentMessages.map(msg =>\r\n          msg.id === messageId\r\n            ? { ...msg, reactions }\r\n            : msg\r\n        )\r\n      );\r\n    });\r\n\r\n    this.socketService.on('onlineUsersUpdate', (users: User[]) => {\r\n      this.onlineUsersSubject.next(users || []);\r\n    });\r\n\r\n    this.socketService.on('userJoined', ({ username }: { username: string }) => {\r\n      // Online users will be updated via onlineUsersUpdate event\r\n    });\r\n\r\n    this.socketService.on('userLeft', ({ username }: { username: string }) => {\r\n      // Online users will be updated via onlineUsersUpdate event\r\n    });\r\n\r\n    // Handle typing indicators\r\n    this.socketService.on('userTyping', ({ username, groupId, isTyping }: { username: string; groupId: string; isTyping: boolean }) => {\r\n\r\n\r\n      const currentGroup = this.currentGroupSubject.value;\r\n      if (currentGroup && groupId === currentGroup.id) {\r\n        const currentTypingUsers = this.typingUsersSubject.value;\r\n\r\n        if (isTyping) {\r\n          // Add user to typing list if not already there\r\n          if (!currentTypingUsers.includes(username)) {\r\n            this.typingUsersSubject.next([...currentTypingUsers, username]);\r\n          }\r\n        } else {\r\n          // Remove user from typing list\r\n          this.typingUsersSubject.next(currentTypingUsers.filter(user => user !== username));\r\n        }\r\n      }\r\n    });\r\n\r\n    this.socketService.on('messageUpdated', ({ messageId, newText, updatedAt }: { messageId: string; newText: string; updatedAt: string }) => {\r\n\r\n      const currentMessages = this.messagesSubject.value;\r\n      this.messagesSubject.next(\r\n        currentMessages.map(msg => \r\n          msg.id === messageId \r\n            ? { ...msg, text: newText, updated_at: updatedAt }\r\n            : msg\r\n        )\r\n      );\r\n    });\r\n\r\n    this.socketService.on('messageDeleted', ({ messageId }: { messageId: string }) => {\r\n\r\n      const currentMessages = this.messagesSubject.value;\r\n      this.messagesSubject.next(\r\n        currentMessages.filter(msg => msg.id !== messageId)\r\n      );\r\n    });\r\n\r\n    this.socketService.on('error', (error: any) => {\r\n      console.error('Socket error:', error);\r\n    });\r\n  }\r\n\r\n  private setupMessageRefresh(): void {\r\n    // Clear any existing interval first\r\n    if (this.messageRefreshInterval) {\r\n      clearInterval(this.messageRefreshInterval);\r\n    }\r\n    \r\n    // Temporarily disable message refresh to prevent browser freezing\r\n    // TODO: Re-enable once socket connection issues are resolved\r\n    /*\r\n    this.messageRefreshInterval = setInterval(() => {\r\n      try {\r\n        if (!this.socketService.isConnectedSubject.value) {\r\n          const currentGroup = this.currentGroupSubject.value;\r\n          if (currentGroup) {\r\n            console.log('Refreshing messages via HTTP API');\r\n            this.loadRecentMessages(currentGroup.id);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Error in message refresh interval:', error);\r\n        // Clear the interval if there's an error to prevent crashes\r\n        if (this.messageRefreshInterval) {\r\n          clearInterval(this.messageRefreshInterval);\r\n          this.messageRefreshInterval = null;\r\n        }\r\n      }\r\n    }, 5000);\r\n    */\r\n  }\r\n\r\n  async login(username: string, password?: string, inviteCode: string | null = null): Promise<void> {\r\n    try {\r\n      this.loadingSubject.next(true);\r\n      const userData = await this.apiService.loginUser(username, password, inviteCode || undefined).toPromise();\r\n      \r\n      // Check if password is required\r\n      if (userData?.requiresPassword) {\r\n        throw new Error('Password required for this user');\r\n      }\r\n      \r\n      // Connect to socket with auth data\r\n      this.socketService.connect(username, password, inviteCode);\r\n\r\n      this.userSubject.next(username);\r\n      this.isAdminSubject.next(userData?.isAdmin || false);\r\n      this.securityInfoSubject.next(userData?.securityInfo || null);\r\n      \r\n      // Wait for socket connection before loading groups\r\n      // The socket will emit 'userGroups' event which will handle group loading\r\n      // This prevents race conditions between API and socket calls\r\n      \r\n      // Add fallback: if socket doesn't load groups within 3 seconds, try API\r\n      setTimeout(() => {\r\n        if (this.groupsSubject.value.length === 0) {\r\n\r\n          this.loadUserGroups(username);\r\n        }\r\n      }, 3000);\r\n      \r\n    } catch (error) {\r\n      console.error('Login failed:', error);\r\n      throw error;\r\n    } finally {\r\n      this.loadingSubject.next(false);\r\n    }\r\n  }\r\n\r\n  async loadRecentMessages(groupId: string): Promise<void> {\r\n    // Safety guard to prevent infinite loops\r\n    if (this.isLoadingMessages) {\r\n      return;\r\n    }\r\n\r\n    // Throttle message loading to prevent excessive API calls\r\n    const now = Date.now();\r\n    if (now - this.lastMessageLoadTime < this.MESSAGE_LOAD_THROTTLE) {\r\n      return;\r\n    }\r\n    this.lastMessageLoadTime = now;\r\n\r\n    this.isLoadingMessages = true;\r\n    try {\r\n      const messages = await this.apiService.getMessages(groupId, 50).toPromise();\r\n\r\n      // For group switching, replace messages with only the current group's messages\r\n      // This ensures we only show messages for the current group\r\n      this.messagesSubject.next(messages || []);\r\n    } catch (error) {\r\n      console.error('Failed to load messages:', error);\r\n      // Set empty array to prevent UI from showing stale data\r\n      this.messagesSubject.next([]);\r\n    } finally {\r\n      this.isLoadingMessages = false;\r\n    }\r\n  }\r\n\r\n  async loadUserGroups(username: string): Promise<void> {\r\n    // Safety guard to prevent infinite loops\r\n    if (this.isLoadingGroups) {\r\n      return;\r\n    }\r\n\r\n    this.isLoadingGroups = true;\r\n    try {\r\n      const groups = await this.apiService.getUserGroups(username).toPromise();\r\n      this.groupsSubject.next(groups || []);\r\n      if (groups && groups.length > 0) {\r\n        this.currentGroupSubject.next(groups[0]);\r\n        this.loadRecentMessages(groups[0].id);\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to load user groups:', error);\r\n    } finally {\r\n      this.isLoadingGroups = false;\r\n    }\r\n  }\r\n\r\n\r\n\r\n\r\n\r\n  // Helper method to sort messages by timestamp\r\n  private sortMessagesByTimestamp(messages: Message[]): Message[] {\r\n    return messages.sort((a, b) => {\r\n      const timeA = new Date(a.timestamp).getTime();\r\n      const timeB = new Date(b.timestamp).getTime();\r\n      return timeA - timeB;\r\n    });\r\n  }\r\n\r\n  // Helper method to replace optimistic message with real message\r\n  private replaceOptimisticMessage(tempId: string, realMessage: Message): void {\r\n    const currentMessages = this.messagesSubject.value;\r\n    const updatedMessages = currentMessages.map(msg =>\r\n      msg.id === tempId ? realMessage : msg\r\n    );\r\n    this.messagesSubject.next(this.sortMessagesByTimestamp(updatedMessages));\r\n\r\n  }\r\n\r\n  // Helper method to remove optimistic message (on error)\r\n  private removeOptimisticMessage(tempId: string): void {\r\n    const currentMessages = this.messagesSubject.value;\r\n    const updatedMessages = currentMessages.filter(msg => msg.id !== tempId);\r\n    this.messagesSubject.next(updatedMessages);\r\n\r\n  }\r\n\r\n  // Helper method to check if message already exists\r\n  private messageExists(message: Message, messages: Message[]): boolean {\r\n    return messages.some(msg =>\r\n      msg.id === message.id ||\r\n      (msg.text === message.text &&\r\n       msg.username === message.username &&\r\n       Math.abs(new Date(msg.timestamp).getTime() - new Date(message.timestamp).getTime()) < 5000)\r\n    );\r\n  }\r\n\r\n  joinGroup(groupId: string): void {\r\n    const currentUser = this.userSubject.value;\r\n    if (!groupId || !currentUser) {\r\n      console.error('Cannot join group - missing groupId or user');\r\n      return;\r\n    }\r\n    \r\n\r\n    \r\n    // Check if socket is connected before joining\r\n    if (!this.socketService.isConnectedSubject.value) {\r\n      console.error('Cannot join group - socket not connected');\r\n      return;\r\n    }\r\n    \r\n    this.socketService.joinGroup(groupId);\r\n    const groups = this.groupsSubject.value;\r\n    const group = groups.find(g => g.id === groupId);\r\n    if (group) {\r\n      this.currentGroupSubject.next(group);\r\n      // Load recent messages for the group\r\n      this.loadRecentMessages(groupId);\r\n    } else {\r\n      console.error('Group not found in user groups:', groupId);\r\n    }\r\n  }\r\n\r\n  sendMessage(text: string, replyToId: string | null = null): void {\r\n    const currentUser = this.userSubject.value;\r\n    const currentGroup = this.currentGroupSubject.value;\r\n    \r\n    if (!text.trim()) {\r\n      console.error('Cannot send message - empty text');\r\n      return;\r\n    }\r\n    \r\n    if (!currentUser) {\r\n      console.error('Cannot send message - user not logged in');\r\n      return;\r\n    }\r\n    \r\n    if (!currentGroup) {\r\n      console.error('Cannot send message - no group selected. Available groups:', this.groupsSubject.value);\r\n      // Try to auto-select the first available group\r\n      const groups = this.groupsSubject.value;\r\n      if (groups && groups.length > 0) {\r\n\r\n        this.currentGroupSubject.next(groups[0]);\r\n        this.loadRecentMessages(groups[0].id);\r\n        // Retry sending the message - DISABLED to prevent infinite loops\r\n        // setTimeout(() => this.sendMessage(text, replyToId), 100);\r\n        return;\r\n      } else {\r\n        console.error('No groups available for user');\r\n        return;\r\n      }\r\n    }\r\n\r\n    // Create optimistic message for immediate display\r\n    const optimisticMessage: Message = {\r\n      id: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`, // Unique temporary ID\r\n      text: text.trim(),\r\n      username: currentUser,\r\n      timestamp: new Date().toISOString(),\r\n      groupId: currentGroup.id,\r\n      replyTo: replyToId || undefined,\r\n      reactions: []\r\n    };\r\n\r\n    // Add optimistic message to local state immediately\r\n    const currentMessages = this.messagesSubject.value;\r\n    const updatedMessages = [...currentMessages, optimisticMessage];\r\n    this.messagesSubject.next(updatedMessages);\r\n\r\n\r\n    // Send message with acknowledgment and retry logic\r\n    this.sendMessageWithRetry(text, currentGroup.id, replyToId, optimisticMessage);\r\n  }\r\n\r\n  private async sendMessageWithRetry(\r\n    text: string,\r\n    groupId: string,\r\n    replyToId: string | null,\r\n    optimisticMessage: Message,\r\n    retryCount: number = 0\r\n  ): Promise<void> {\r\n    const maxRetries = 3;\r\n    const retryDelay = 1000 * Math.pow(2, retryCount); // Exponential backoff\r\n\r\n    try {\r\n      if (this.socketService.isConnected()) {\r\n        // Try sending via socket with acknowledgment\r\n        const response = await this.socketService.emitWithAck('sendMessage', {\r\n          text,\r\n          groupId,\r\n          replyTo: replyToId,\r\n          messageId: optimisticMessage.id\r\n        });\r\n\r\n        if (response.success) {\r\n\r\n          // Replace optimistic message with real message\r\n          this.replaceOptimisticMessage(optimisticMessage.id, response.message);\r\n          return;\r\n        } else {\r\n          throw new Error(response.error || 'Unknown error');\r\n        }\r\n      } else {\r\n        throw new Error('Socket not connected');\r\n      }\r\n    } catch (error) {\r\n      console.error(`❌ Failed to send message (attempt ${retryCount + 1}):`, error);\r\n\r\n      if (retryCount < maxRetries) {\r\n        // Retry with exponential backoff\r\n\r\n        setTimeout(() => {\r\n          this.sendMessageWithRetry(text, groupId, replyToId, optimisticMessage, retryCount + 1);\r\n        }, retryDelay);\r\n      } else {\r\n        // All retries failed, try HTTP API as final fallback\r\n\r\n        this.sendMessageViaHttpFallback(text, groupId, replyToId, optimisticMessage);\r\n      }\r\n    }\r\n  }\r\n\r\n  private sendMessageViaHttpFallback(\r\n    text: string,\r\n    groupId: string,\r\n    replyToId: string | null,\r\n    optimisticMessage: Message\r\n  ): void {\r\n    const currentUser = this.userSubject.value;\r\n    if (!currentUser) {\r\n      this.removeOptimisticMessage(optimisticMessage.id);\r\n      return;\r\n    }\r\n\r\n    this.apiService.sendMessage(text, currentUser, groupId, replyToId).subscribe({\r\n      next: (response) => {\r\n\r\n        this.replaceOptimisticMessage(optimisticMessage.id, response);\r\n      },\r\n      error: (error) => {\r\n        console.error('❌ Failed to send message via HTTP API:', error);\r\n        this.removeOptimisticMessage(optimisticMessage.id);\r\n        // Show error notification to user\r\n        this.showMessageError('Failed to send message. Please try again.');\r\n      }\r\n    });\r\n  }\r\n\r\n  private showMessageError(message: string): void {\r\n    // You can implement a toast notification service here\r\n    console.error('💬 Message Error:', message);\r\n    // For now, just log the error. In a real app, you'd show a toast/snackbar\r\n  }\r\n\r\n  // Typing indicator methods\r\n  startTyping(): void {\r\n    const currentGroup = this.currentGroupSubject.value;\r\n    if (currentGroup && this.socketService.isConnected()) {\r\n      this.socketService.sendTypingIndicator(currentGroup.id, true);\r\n\r\n      // Clear existing timeout\r\n      if (this.typingTimeout) {\r\n        clearTimeout(this.typingTimeout);\r\n      }\r\n\r\n      // Set timeout to stop typing after 3 seconds of inactivity\r\n      this.typingTimeout = setTimeout(() => {\r\n        this.stopTyping();\r\n      }, 3000);\r\n    }\r\n  }\r\n\r\n  stopTyping(): void {\r\n    const currentGroup = this.currentGroupSubject.value;\r\n    if (currentGroup && this.socketService.isConnected()) {\r\n      this.socketService.sendTypingIndicator(currentGroup.id, false);\r\n\r\n      // Clear timeout\r\n      if (this.typingTimeout) {\r\n        clearTimeout(this.typingTimeout);\r\n        this.typingTimeout = null;\r\n      }\r\n    }\r\n  }\r\n\r\n  replyToMessage(message: Message): void {\r\n    this.replyToSubject.next(message);\r\n  }\r\n\r\n  cancelReply(): void {\r\n    this.replyToSubject.next(null);\r\n  }\r\n\r\n  async addReaction(messageId: string, emoji: string): Promise<void> {\r\n    try {\r\n      if (this.socketService.isConnected()) {\r\n        // Try with acknowledgment first\r\n        const response = await this.socketService.emitWithAck('addReaction', {\r\n          messageId,\r\n          emoji\r\n        });\r\n\r\n        if (response.success) {\r\n\r\n        } else {\r\n          throw new Error(response.error || 'Failed to add reaction');\r\n        }\r\n      } else {\r\n        // Fallback to HTTP API\r\n        const currentUser = this.userSubject.value;\r\n        if (currentUser) {\r\n          this.apiService.addReaction(messageId, emoji, currentUser).subscribe({\r\n            next: (response) => {\r\n\r\n            },\r\n            error: (error) => {\r\n              console.error('❌ Failed to add reaction via HTTP API:', error);\r\n            }\r\n          });\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Failed to add reaction:', error);\r\n      // Fallback to old method\r\n      this.socketService.addReaction(messageId, emoji);\r\n    }\r\n  }\r\n\r\n  removeReaction(data: { messageId: string; emoji?: string }): void {\r\n    this.socketService.removeReaction(data);\r\n  }\r\n\r\n  async updateMessage(messageId: string, newText: string): Promise<void> {\r\n    const currentUser = this.userSubject.value;\r\n    if (!currentUser) {\r\n      console.error('Cannot update message - user not logged in');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Update the message in the local state immediately for better UX\r\n      const currentMessages = this.messagesSubject.value;\r\n      this.messagesSubject.next(\r\n        currentMessages.map(msg => \r\n          msg.id === messageId \r\n            ? { ...msg, text: newText, updated_at: new Date().toISOString() }\r\n            : msg\r\n        )\r\n      );\r\n\r\n      // Emit socket event for real-time updates\r\n      if (this.socketService.isConnectedSubject.value) {\r\n        this.socketService.updateMessage(messageId, newText);\r\n      } else {\r\n        // Fallback to HTTP API if socket not connected\r\n        const updatedMessage = await this.apiService.updateMessage(messageId, newText, currentUser).toPromise();\r\n        this.messagesSubject.next(\r\n          currentMessages.map(msg => \r\n            msg.id === messageId \r\n              ? { ...msg, text: newText, updated_at: updatedMessage.updated_at }\r\n              : msg\r\n          )\r\n        );\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to update message:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async deleteMessage(messageId: string): Promise<void> {\r\n    const currentUser = this.userSubject.value;\r\n    if (!currentUser) {\r\n      console.error('Cannot delete message - user not logged in');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Remove the message from local state immediately for better UX\r\n      const currentMessages = this.messagesSubject.value;\r\n      this.messagesSubject.next(\r\n        currentMessages.filter(msg => msg.id !== messageId)\r\n      );\r\n\r\n      // Emit socket event for real-time updates\r\n      if (this.socketService.isConnectedSubject.value) {\r\n        this.socketService.deleteMessage(messageId);\r\n      } else {\r\n        // Fallback to HTTP API if socket not connected\r\n        await this.apiService.deleteMessage(messageId, currentUser).toPromise();\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to delete message:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  showAdminPanel(): void {\r\n    this.showAdminPanelSubject.next(true);\r\n  }\r\n\r\n  hideAdminPanel(): void {\r\n    this.showAdminPanelSubject.next(false);\r\n  }\r\n\r\n  startEditingMessage(message: Message): void {\r\n    this.editingMessageSubject.next(message);\r\n  }\r\n\r\n  cancelEditingMessage(): void {\r\n    this.editingMessageSubject.next(null);\r\n  }\r\n\r\n  logout(): void {\r\n    try {\r\n      // Disconnect socket first\r\n      this.socketService.disconnect();\r\n      \r\n      // Clear interval\r\n      if (this.messageRefreshInterval) {\r\n        clearInterval(this.messageRefreshInterval);\r\n        this.messageRefreshInterval = null;\r\n      }\r\n      \r\n      // Reset all subjects\r\n      this.userSubject.next(null);\r\n      this.messagesSubject.next([]);\r\n      this.onlineUsersSubject.next([]);\r\n      this.groupsSubject.next([]);\r\n      this.currentGroupSubject.next(null);\r\n      this.replyToSubject.next(null);\r\n      this.loadingSubject.next(false);\r\n      this.isAdminSubject.next(false);\r\n      this.showAdminPanelSubject.next(false);\r\n      this.securityInfoSubject.next(null);\r\n      this.editingMessageSubject.next(null);\r\n      \r\n\r\n    } catch (error) {\r\n      console.error('Error during logout:', error);\r\n    }\r\n  }\r\n\r\n  // Method to refresh security info for all components\r\n  refreshSecurityInfo(): void {\r\n    this.apiService.getSecurityInfo().subscribe({\r\n      next: (data) => {\r\n        this.securityInfoSubject.next(data);\r\n      },\r\n      error: (error) => {\r\n        console.error('Failed to refresh security info:', error);\r\n      }\r\n    });\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module"}