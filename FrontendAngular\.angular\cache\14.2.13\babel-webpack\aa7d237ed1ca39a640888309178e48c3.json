{"ast": null, "code": "import { operate } from '../util/lift';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { mergeAll } from './mergeAll';\nimport { popNumber, popScheduler } from '../util/args';\nimport { from } from '../observable/from';\nexport function merge(...args) {\n  const scheduler = popScheduler(args);\n  const concurrent = popNumber(args, Infinity);\n  args = argsOrArgArray(args);\n  return operate((source, subscriber) => {\n    mergeAll(concurrent)(from([source, ...args], scheduler)).subscribe(subscriber);\n  });\n} //# sourceMappingURL=merge.js.map", "map": null, "metadata": {}, "sourceType": "module"}