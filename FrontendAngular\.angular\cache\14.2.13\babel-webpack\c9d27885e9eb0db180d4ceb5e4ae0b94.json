{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { timer } from './timer';\nexport function interval(period = 0, scheduler = asyncScheduler) {\n  if (period < 0) {\n    period = 0;\n  }\n\n  return timer(period, period, scheduler);\n}", "map": {"version": 3, "names": ["asyncScheduler", "timer", "interval", "period", "scheduler"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/rxjs/dist/esm/internal/observable/interval.js"], "sourcesContent": ["import { asyncScheduler } from '../scheduler/async';\nimport { timer } from './timer';\nexport function interval(period = 0, scheduler = asyncScheduler) {\n    if (period < 0) {\n        period = 0;\n    }\n    return timer(period, period, scheduler);\n}\n"], "mappings": "AAAA,SAASA,cAAT,QAA+B,oBAA/B;AACA,SAASC,KAAT,QAAsB,SAAtB;AACA,OAAO,SAASC,QAAT,CAAkBC,MAAM,GAAG,CAA3B,EAA8BC,SAAS,GAAGJ,cAA1C,EAA0D;EAC7D,IAAIG,MAAM,GAAG,CAAb,EAAgB;IACZA,MAAM,GAAG,CAAT;EACH;;EACD,OAAOF,KAAK,CAACE,MAAD,EAASA,MAAT,EAAiBC,SAAjB,CAAZ;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}