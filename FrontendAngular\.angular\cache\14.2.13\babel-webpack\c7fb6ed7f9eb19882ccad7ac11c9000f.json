{"ast": null, "code": "import { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let ApiService = /*#__PURE__*/(() => {\n  class ApiService {\n    constructor(http) {\n      this.http = http;\n      this.baseUrl = `${environment.backendUrl}/api`;\n    } // Messages API\n\n\n    getMessages(groupId, limit = 50) {\n      return this.http.get(`${this.baseUrl}/messages/${groupId}?limit=${limit}`);\n    }\n\n    sendMessage(text, username, groupId, replyTo = null) {\n      return this.http.post(`${this.baseUrl}/messages`, {\n        text,\n        username,\n        groupId,\n        replyTo\n      });\n    }\n\n    addReaction(messageId, emoji, username) {\n      return this.http.post(`${this.baseUrl}/messages/${messageId}/reactions`, {\n        emoji,\n        username\n      });\n    }\n\n    removeReaction(messageId, username) {\n      return this.http.delete(`${this.baseUrl}/messages/${messageId}/reactions`, {\n        body: {\n          username\n        }\n      });\n    }\n\n    getUserGroups(username) {\n      return this.http.get(`${this.baseUrl}/messages/groups/${username}`);\n    } // Users API\n\n\n    getUsers() {\n      return this.http.get(`${this.baseUrl}/users`);\n    }\n\n    getOnlineUsers() {\n      return this.http.get(`${this.baseUrl}/users/online`);\n    }\n\n    loginUser(username, inviteCode) {\n      const body = {\n        username\n      };\n\n      if (inviteCode) {\n        body.inviteCode = inviteCode;\n      }\n\n      return this.http.post(`${this.baseUrl}/users`, body);\n    }\n\n    getSecurityInfo() {\n      return this.http.get(`${this.baseUrl}/users/security-info`);\n    } // Admin API\n\n\n    getAllowedUsers(username) {\n      return this.http.get(`${this.baseUrl}/admin/allowed-users`, {\n        params: {\n          username\n        }\n      });\n    }\n\n    addAllowedUser(username, targetUsername) {\n      return this.http.post(`${this.baseUrl}/admin/allowed-users`, {\n        username,\n        targetUsername\n      });\n    }\n\n    removeAllowedUser(username, targetUsername) {\n      return this.http.delete(`${this.baseUrl}/admin/allowed-users/${targetUsername}`, {\n        body: {\n          username\n        }\n      });\n    }\n\n    getInviteCodes(username) {\n      return this.http.get(`${this.baseUrl}/admin/invite-codes`, {\n        params: {\n          username\n        }\n      });\n    }\n\n    createInviteCode(username, config) {\n      return this.http.post(`${this.baseUrl}/admin/invite-codes`, {\n        username,\n        ...config\n      });\n    }\n\n    deactivateInviteCode(username, code) {\n      return this.http.delete(`${this.baseUrl}/admin/invite-codes/${code}`, {\n        body: {\n          username\n        }\n      });\n    }\n\n    getGroups(username) {\n      return this.http.get(`${this.baseUrl}/admin/groups`, {\n        params: {\n          username\n        }\n      });\n    }\n\n    createGroup(username, name, description) {\n      return this.http.post(`${this.baseUrl}/admin/groups`, {\n        username,\n        name,\n        description\n      });\n    }\n\n    deleteGroup(username, groupId) {\n      return this.http.delete(`${this.baseUrl}/admin/groups/${groupId}`, {\n        body: {\n          username\n        }\n      });\n    }\n\n    getGroupUsers(username, groupId) {\n      return this.http.get(`${this.baseUrl}/admin/groups/${groupId}/users`, {\n        params: {\n          username\n        }\n      });\n    }\n\n    grantUserAccess(username, groupId, targetUsername) {\n      return this.http.post(`${this.baseUrl}/admin/groups/${groupId}/users`, {\n        username,\n        targetUsername\n      });\n    }\n\n    revokeUserAccess(username, groupId, targetUsername) {\n      return this.http.delete(`${this.baseUrl}/admin/groups/${groupId}/users/${targetUsername}`, {\n        body: {\n          username\n        }\n      });\n    }\n\n  }\n\n  ApiService.ɵfac = function ApiService_Factory(t) {\n    return new (t || ApiService)(i0.ɵɵinject(i1.HttpClient));\n  };\n\n  ApiService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ApiService,\n    factory: ApiService.ɵfac,\n    providedIn: 'root'\n  });\n  return ApiService;\n})();", "map": null, "metadata": {}, "sourceType": "module"}