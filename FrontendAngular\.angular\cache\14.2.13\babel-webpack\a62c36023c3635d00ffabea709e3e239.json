{"ast": null, "code": "import _asyncToGenerator from \"R:/chateye/FrontendAngular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { ApiService, Message, Group, User, SecurityInfo } from './api.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./api.service\";\nimport * as i2 from \"./socket.service\";\nimport * as i3 from \"./notification.service\";\nexport class ChatService {\n  constructor(apiService, socketService, notificationService) {\n    this.apiService = apiService;\n    this.socketService = socketService;\n    this.notificationService = notificationService;\n    this.userSubject = new BehaviorSubject(null);\n    this.messagesSubject = new BehaviorSubject([]);\n    this.onlineUsersSubject = new BehaviorSubject([]);\n    this.groupsSubject = new BehaviorSubject([]);\n    this.currentGroupSubject = new BehaviorSubject(null);\n    this.replyToSubject = new BehaviorSubject(null);\n    this.loadingSubject = new BehaviorSubject(false);\n    this.isAdminSubject = new BehaviorSubject(false);\n    this.showAdminPanelSubject = new BehaviorSubject(false);\n    this.securityInfoSubject = new BehaviorSubject(null);\n    this.editingMessageSubject = new BehaviorSubject(null); // Public observables\n\n    this.user$ = this.userSubject.asObservable();\n    this.messages$ = this.messagesSubject.asObservable();\n    this.onlineUsers$ = this.onlineUsersSubject.asObservable();\n    this.groups$ = this.groupsSubject.asObservable();\n    this.currentGroup$ = this.currentGroupSubject.asObservable();\n    this.replyTo$ = this.replyToSubject.asObservable();\n    this.loading$ = this.loadingSubject.asObservable();\n    this.isAdmin$ = this.isAdminSubject.asObservable();\n    this.showAdminPanel$ = this.showAdminPanelSubject.asObservable();\n    this.securityInfo$ = this.securityInfoSubject.asObservable();\n    this.editingMessage$ = this.editingMessageSubject.asObservable();\n    this.connected$ = this.socketService.isConnected$; // Typing indicators\n\n    this.typingUsersSubject = new BehaviorSubject([]);\n    this.typingUsers$ = this.typingUsersSubject.asObservable();\n    this.typingTimeout = null; // Computed observables\n\n    this.isLoggedIn$ = this.user$.pipe(map(user => !!user));\n    this.isLoadingGroups = false;\n    this.isLoadingMessages = false;\n    this.lastMessageLoadTime = 0;\n    this.MESSAGE_LOAD_THROTTLE = 1000; // 1 second throttle\n\n    try {\n      this.setupSocketListeners();\n      this.setupMessageRefresh(); // Expose manual group loading globally for debugging\n\n      window.manualLoadGroups = () => this.manualLoadGroups();\n\n      window.debugChatState = () => this.debugState();\n\n      window.debugScrollState = () => {\n        // This will be called from the component\n        console.log('Use debugScrollState() from the message list component');\n      };\n    } catch (error) {\n      console.error('Error initializing ChatService:', error);\n    }\n  }\n\n  setupSocketListeners() {\n    this.socketService.on('connect', () => {\n      console.log('Connected to server'); // Clear any loading states when connected\n\n      this.loadingSubject.next(false);\n    });\n    this.socketService.on('disconnect', () => {\n      console.log('Disconnected from server'); // Don't set loading to true on disconnect to avoid UI freeze\n    });\n    this.socketService.on('connect_error', error => {\n      console.error('Socket connection error:', error);\n      this.loadingSubject.next(false);\n    });\n    this.socketService.on('userGroups', userGroups => {\n      console.log('Received user groups:', userGroups?.length || 0, 'groups');\n      this.groupsSubject.next(userGroups || []);\n\n      if (userGroups && userGroups.length > 0) {\n        console.log('Auto-selecting first group:', userGroups[0].name);\n        this.currentGroupSubject.next(userGroups[0]); // Load messages for the selected group\n\n        this.loadRecentMessages(userGroups[0].id);\n      } else {\n        console.log('No groups available for user'); // Clear current group and messages if no groups\n\n        this.currentGroupSubject.next(null);\n        this.messagesSubject.next([]);\n      }\n    });\n    this.socketService.on('recentMessages', messages => {\n      console.log('Received recent messages:', messages.length);\n      console.log('Recent messages data:', messages);\n      this.messagesSubject.next(messages || []);\n      console.log('Recent messages subject updated, current value:', this.messagesSubject.value.length);\n    });\n    this.socketService.on('groupJoined', ({\n      groupId\n    }) => {\n      console.log('Joined group:', groupId);\n      const groups = this.groupsSubject.value;\n      const group = groups.find(g => g.id === groupId);\n\n      if (group) {\n        this.currentGroupSubject.next(group);\n      }\n    });\n    this.socketService.on('newMessage', message => {\n      console.log('📨 New message received:', message);\n      const currentMessages = this.messagesSubject.value;\n      const currentUser = this.userSubject.value;\n      const currentGroup = this.currentGroupSubject.value; // Only process messages for the current group to avoid confusion\n\n      if (currentGroup && message.groupId !== currentGroup.id) {\n        console.log('🚫 Ignoring message from different group:', message.groupId, 'vs current:', currentGroup.id);\n        return;\n      } // Check for duplicate messages to prevent double-display\n\n\n      const existingMessage = currentMessages.find(msg => msg.id === message.id || msg.text === message.text && msg.username === message.username && Math.abs(new Date(msg.timestamp).getTime() - new Date(message.timestamp).getTime()) < 5000);\n\n      if (existingMessage && !existingMessage.id.startsWith('temp_')) {\n        console.log('🔄 Duplicate message detected, ignoring:', message);\n        return;\n      } // Check if this is our own message (to replace optimistic message)\n\n\n      if (message.username === currentUser) {\n        // Find and replace optimistic message with real message\n        const optimisticIndex = currentMessages.findIndex(msg => msg.id.startsWith('temp_') && msg.text === message.text && msg.username === currentUser && msg.groupId === message.groupId);\n\n        if (optimisticIndex !== -1) {\n          // Replace optimistic message\n          const updatedMessages = [...currentMessages];\n          updatedMessages[optimisticIndex] = message;\n          this.messagesSubject.next(this.sortMessagesByTimestamp(updatedMessages));\n          console.log('✅ Replaced optimistic message with real message:', message);\n        } else {\n          // Add new message if no optimistic message found (e.g., from another tab)\n          const updatedMessages = [...currentMessages, message];\n          this.messagesSubject.next(this.sortMessagesByTimestamp(updatedMessages));\n          console.log('➕ Added own message from socket (no optimistic message found):', message);\n        }\n      } else {\n        // Add message from other users\n        const updatedMessages = [...currentMessages, message];\n        this.messagesSubject.next(this.sortMessagesByTimestamp(updatedMessages));\n        console.log('👥 Added message from other user:', message);\n      } // Show notification if not current user and window not focused\n\n\n      if (message.username !== currentUser && document.hidden) {\n        this.notificationService.showMessageNotification(message.username, message.text);\n      }\n    });\n    this.socketService.on('reactionUpdate', ({\n      messageId,\n      reactions\n    }) => {\n      console.log('Reaction update:', messageId, reactions);\n      const currentMessages = this.messagesSubject.value;\n      this.messagesSubject.next(currentMessages.map(msg => msg.id === messageId ? { ...msg,\n        reactions\n      } : msg));\n    });\n    this.socketService.on('onlineUsersUpdate', users => {\n      console.log('Online users updated:', users);\n      this.onlineUsersSubject.next(users || []);\n    });\n    this.socketService.on('userJoined', ({\n      username\n    }) => {\n      console.log('User joined:', username); // Online users will be updated via onlineUsersUpdate event\n    });\n    this.socketService.on('userLeft', ({\n      username\n    }) => {\n      console.log('User left:', username); // Online users will be updated via onlineUsersUpdate event\n    }); // Handle typing indicators\n\n    this.socketService.on('userTyping', ({\n      username,\n      groupId,\n      isTyping\n    }) => {\n      console.log(`⌨️ ${username} is ${isTyping ? 'typing' : 'stopped typing'} in group ${groupId}`);\n      const currentGroup = this.currentGroupSubject.value;\n\n      if (currentGroup && groupId === currentGroup.id) {\n        const currentTypingUsers = this.typingUsersSubject.value;\n\n        if (isTyping) {\n          // Add user to typing list if not already there\n          if (!currentTypingUsers.includes(username)) {\n            this.typingUsersSubject.next([...currentTypingUsers, username]);\n          }\n        } else {\n          // Remove user from typing list\n          this.typingUsersSubject.next(currentTypingUsers.filter(user => user !== username));\n        }\n      }\n    });\n    this.socketService.on('messageUpdated', ({\n      messageId,\n      newText,\n      updatedAt\n    }) => {\n      console.log('Message updated:', messageId, newText);\n      const currentMessages = this.messagesSubject.value;\n      this.messagesSubject.next(currentMessages.map(msg => msg.id === messageId ? { ...msg,\n        text: newText,\n        updated_at: updatedAt\n      } : msg));\n    });\n    this.socketService.on('messageDeleted', ({\n      messageId\n    }) => {\n      console.log('Message deleted:', messageId);\n      const currentMessages = this.messagesSubject.value;\n      this.messagesSubject.next(currentMessages.filter(msg => msg.id !== messageId));\n    });\n    this.socketService.on('error', error => {\n      console.error('Socket error:', error);\n    });\n  }\n\n  setupMessageRefresh() {\n    // Clear any existing interval first\n    if (this.messageRefreshInterval) {\n      clearInterval(this.messageRefreshInterval);\n    } // Temporarily disable message refresh to prevent browser freezing\n    // TODO: Re-enable once socket connection issues are resolved\n\n    /*\r\n    this.messageRefreshInterval = setInterval(() => {\r\n      try {\r\n        if (!this.socketService.isConnectedSubject.value) {\r\n          const currentGroup = this.currentGroupSubject.value;\r\n          if (currentGroup) {\r\n            console.log('Refreshing messages via HTTP API');\r\n            this.loadRecentMessages(currentGroup.id);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Error in message refresh interval:', error);\r\n        // Clear the interval if there's an error to prevent crashes\r\n        if (this.messageRefreshInterval) {\r\n          clearInterval(this.messageRefreshInterval);\r\n          this.messageRefreshInterval = null;\r\n        }\r\n      }\r\n    }, 5000);\r\n    */\n\n  }\n\n  login(_x, _x2) {\n    var _this = this;\n\n    return _asyncToGenerator(function* (username, password, inviteCode = null) {\n      try {\n        _this.loadingSubject.next(true);\n\n        console.log('Starting login process for:', username);\n        const userData = yield _this.apiService.loginUser(username, password, inviteCode || undefined).toPromise();\n        console.log('Login API response:', userData); // Check if password is required\n\n        if (userData?.requiresPassword) {\n          throw new Error('Password required for this user');\n        } // Connect to socket with auth data\n\n\n        console.log('Connecting to socket...');\n\n        _this.socketService.connect(username, password, inviteCode);\n\n        _this.userSubject.next(username);\n\n        _this.isAdminSubject.next(userData?.isAdmin || false);\n\n        _this.securityInfoSubject.next(userData?.securityInfo || null);\n\n        console.log('Login completed successfully for:', username, '(Admin:', userData?.isAdmin || false, ')'); // Wait for socket connection before loading groups\n        // The socket will emit 'userGroups' event which will handle group loading\n        // This prevents race conditions between API and socket calls\n        // Add fallback: if socket doesn't load groups within 3 seconds, try API\n\n        setTimeout(() => {\n          if (_this.groupsSubject.value.length === 0) {\n            console.log('Socket groups not loaded, trying API fallback...');\n\n            _this.loadUserGroups(username);\n          }\n        }, 3000);\n      } catch (error) {\n        console.error('Login failed:', error);\n        throw error;\n      } finally {\n        _this.loadingSubject.next(false);\n      }\n    }).apply(this, arguments);\n  }\n\n  loadRecentMessages(groupId) {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      // Safety guard to prevent infinite loops\n      if (_this2.isLoadingMessages) {\n        console.log('Already loading messages, skipping...');\n        return;\n      } // Throttle message loading to prevent excessive API calls\n\n\n      const now = Date.now();\n\n      if (now - _this2.lastMessageLoadTime < _this2.MESSAGE_LOAD_THROTTLE) {\n        console.log('Throttling message load request');\n        return;\n      }\n\n      _this2.lastMessageLoadTime = now;\n      _this2.isLoadingMessages = true;\n\n      try {\n        console.log('Loading recent messages for group:', groupId);\n        const messages = yield _this2.apiService.getMessages(groupId, 50).toPromise();\n        console.log('Loaded messages:', messages?.length || 0);\n        console.log('Messages data:', messages); // For group switching, replace messages with only the current group's messages\n        // This ensures we only show messages for the current group\n\n        _this2.messagesSubject.next(messages || []);\n\n        console.log('Messages subject updated, current value:', _this2.messagesSubject.value.length);\n      } catch (error) {\n        console.error('Failed to load messages:', error); // Set empty array to prevent UI from showing stale data\n\n        _this2.messagesSubject.next([]);\n      } finally {\n        _this2.isLoadingMessages = false;\n      }\n    })();\n  }\n\n  loadUserGroups(username) {\n    var _this3 = this;\n\n    return _asyncToGenerator(function* () {\n      // Safety guard to prevent infinite loops\n      if (_this3.isLoadingGroups) {\n        console.log('Already loading groups, skipping...');\n        return;\n      }\n\n      _this3.isLoadingGroups = true;\n\n      try {\n        console.log('Loading user groups for:', username);\n        const groups = yield _this3.apiService.getUserGroups(username).toPromise();\n        console.log('Loaded user groups:', groups);\n\n        _this3.groupsSubject.next(groups || []);\n\n        if (groups && groups.length > 0) {\n          console.log('Auto-selecting first group:', groups[0]);\n\n          _this3.currentGroupSubject.next(groups[0]);\n\n          _this3.loadRecentMessages(groups[0].id);\n        }\n      } catch (error) {\n        console.error('Failed to load user groups:', error);\n      } finally {\n        _this3.isLoadingGroups = false;\n      }\n    })();\n  } // Safe method to manually load groups (call this from browser console)\n\n\n  manualLoadGroups() {\n    var _this4 = this;\n\n    return _asyncToGenerator(function* () {\n      const currentUser = _this4.userSubject.value;\n\n      if (currentUser) {\n        console.log('Manually loading groups for:', currentUser);\n        yield _this4.loadUserGroups(currentUser);\n      } else {\n        console.error('No user logged in');\n      }\n    })();\n  } // Debug method to check current state\n\n\n  debugState() {\n    console.log('=== Chat Service Debug State ===');\n    console.log('User:', this.userSubject.value);\n    console.log('Groups:', this.groupsSubject.value);\n    console.log('Current Group:', this.currentGroupSubject.value);\n    console.log('Socket Connected:', this.socketService.isConnectedSubject.value);\n    console.log('Loading Groups:', this.isLoadingGroups);\n    console.log('Loading Messages:', this.isLoadingMessages);\n    console.log('================================');\n  } // Helper method to sort messages by timestamp\n\n\n  sortMessagesByTimestamp(messages) {\n    return messages.sort((a, b) => {\n      const timeA = new Date(a.timestamp).getTime();\n      const timeB = new Date(b.timestamp).getTime();\n      return timeA - timeB;\n    });\n  } // Helper method to replace optimistic message with real message\n\n\n  replaceOptimisticMessage(tempId, realMessage) {\n    const currentMessages = this.messagesSubject.value;\n    const updatedMessages = currentMessages.map(msg => msg.id === tempId ? realMessage : msg);\n    this.messagesSubject.next(this.sortMessagesByTimestamp(updatedMessages));\n    console.log('✅ Replaced optimistic message with real message:', realMessage);\n  } // Helper method to remove optimistic message (on error)\n\n\n  removeOptimisticMessage(tempId) {\n    const currentMessages = this.messagesSubject.value;\n    const updatedMessages = currentMessages.filter(msg => msg.id !== tempId);\n    this.messagesSubject.next(updatedMessages);\n    console.log('❌ Removed optimistic message due to error:', tempId);\n  } // Helper method to check if message already exists\n\n\n  messageExists(message, messages) {\n    return messages.some(msg => msg.id === message.id || msg.text === message.text && msg.username === message.username && Math.abs(new Date(msg.timestamp).getTime() - new Date(message.timestamp).getTime()) < 5000);\n  }\n\n  joinGroup(groupId) {\n    const currentUser = this.userSubject.value;\n\n    if (!groupId || !currentUser) {\n      console.error('Cannot join group - missing groupId or user');\n      return;\n    }\n\n    console.log('Joining group:', groupId); // Check if socket is connected before joining\n\n    if (!this.socketService.isConnectedSubject.value) {\n      console.error('Cannot join group - socket not connected');\n      return;\n    }\n\n    this.socketService.joinGroup(groupId);\n    const groups = this.groupsSubject.value;\n    const group = groups.find(g => g.id === groupId);\n\n    if (group) {\n      this.currentGroupSubject.next(group); // Load recent messages for the group\n\n      this.loadRecentMessages(groupId);\n    } else {\n      console.error('Group not found in user groups:', groupId);\n    }\n  }\n\n  sendMessage(text, replyToId = null) {\n    const currentUser = this.userSubject.value;\n    const currentGroup = this.currentGroupSubject.value;\n\n    if (!text.trim()) {\n      console.error('Cannot send message - empty text');\n      return;\n    }\n\n    if (!currentUser) {\n      console.error('Cannot send message - user not logged in');\n      return;\n    }\n\n    if (!currentGroup) {\n      console.error('Cannot send message - no group selected. Available groups:', this.groupsSubject.value); // Try to auto-select the first available group\n\n      const groups = this.groupsSubject.value;\n\n      if (groups && groups.length > 0) {\n        console.log('Auto-selecting first available group:', groups[0]);\n        this.currentGroupSubject.next(groups[0]);\n        this.loadRecentMessages(groups[0].id); // Retry sending the message - DISABLED to prevent infinite loops\n        // setTimeout(() => this.sendMessage(text, replyToId), 100);\n\n        return;\n      } else {\n        console.error('No groups available for user');\n        return;\n      }\n    } // Create optimistic message for immediate display\n\n\n    const optimisticMessage = {\n      id: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n      text: text.trim(),\n      username: currentUser,\n      timestamp: new Date().toISOString(),\n      groupId: currentGroup.id,\n      replyTo: replyToId || undefined,\n      reactions: []\n    }; // Add optimistic message to local state immediately\n\n    const currentMessages = this.messagesSubject.value;\n    const updatedMessages = [...currentMessages, optimisticMessage];\n    this.messagesSubject.next(updatedMessages);\n    console.log('📤 Added optimistic message:', optimisticMessage); // Send message with acknowledgment and retry logic\n\n    this.sendMessageWithRetry(text, currentGroup.id, replyToId, optimisticMessage);\n  }\n\n  sendMessageWithRetry(_x3, _x4, _x5, _x6) {\n    var _this5 = this;\n\n    return _asyncToGenerator(function* (text, groupId, replyToId, optimisticMessage, retryCount = 0) {\n      const maxRetries = 3;\n      const retryDelay = 1000 * Math.pow(2, retryCount); // Exponential backoff\n\n      try {\n        if (_this5.socketService.isConnected()) {\n          // Try sending via socket with acknowledgment\n          const response = yield _this5.socketService.emitWithAck('sendMessage', {\n            text,\n            groupId,\n            replyTo: replyToId,\n            messageId: optimisticMessage.id\n          });\n\n          if (response.success) {\n            console.log('✅ Message sent successfully via socket:', response); // Replace optimistic message with real message\n\n            _this5.replaceOptimisticMessage(optimisticMessage.id, response.message);\n\n            return;\n          } else {\n            throw new Error(response.error || 'Unknown error');\n          }\n        } else {\n          throw new Error('Socket not connected');\n        }\n      } catch (error) {\n        console.error(`❌ Failed to send message (attempt ${retryCount + 1}):`, error);\n\n        if (retryCount < maxRetries) {\n          // Retry with exponential backoff\n          console.log(`🔄 Retrying in ${retryDelay}ms...`);\n          setTimeout(() => {\n            _this5.sendMessageWithRetry(text, groupId, replyToId, optimisticMessage, retryCount + 1);\n          }, retryDelay);\n        } else {\n          // All retries failed, try HTTP API as final fallback\n          console.log('🌐 All socket retries failed, trying HTTP API fallback');\n\n          _this5.sendMessageViaHttpFallback(text, groupId, replyToId, optimisticMessage);\n        }\n      }\n    }).apply(this, arguments);\n  }\n\n  sendMessageViaHttpFallback(text, groupId, replyToId, optimisticMessage) {\n    const currentUser = this.userSubject.value;\n\n    if (!currentUser) {\n      this.removeOptimisticMessage(optimisticMessage.id);\n      return;\n    }\n\n    this.apiService.sendMessage(text, currentUser, groupId, replyToId).subscribe({\n      next: response => {\n        console.log('✅ Message sent via HTTP API:', response);\n        this.replaceOptimisticMessage(optimisticMessage.id, response);\n      },\n      error: error => {\n        console.error('❌ Failed to send message via HTTP API:', error);\n        this.removeOptimisticMessage(optimisticMessage.id); // Show error notification to user\n\n        this.showMessageError('Failed to send message. Please try again.');\n      }\n    });\n  }\n\n  showMessageError(message) {\n    // You can implement a toast notification service here\n    console.error('💬 Message Error:', message); // For now, just log the error. In a real app, you'd show a toast/snackbar\n  } // Typing indicator methods\n\n\n  startTyping() {\n    const currentGroup = this.currentGroupSubject.value;\n\n    if (currentGroup && this.socketService.isConnected()) {\n      this.socketService.sendTypingIndicator(currentGroup.id, true); // Clear existing timeout\n\n      if (this.typingTimeout) {\n        clearTimeout(this.typingTimeout);\n      } // Set timeout to stop typing after 3 seconds of inactivity\n\n\n      this.typingTimeout = setTimeout(() => {\n        this.stopTyping();\n      }, 3000);\n    }\n  }\n\n  stopTyping() {\n    const currentGroup = this.currentGroupSubject.value;\n\n    if (currentGroup && this.socketService.isConnected()) {\n      this.socketService.sendTypingIndicator(currentGroup.id, false); // Clear timeout\n\n      if (this.typingTimeout) {\n        clearTimeout(this.typingTimeout);\n        this.typingTimeout = null;\n      }\n    }\n  }\n\n  replyToMessage(message) {\n    this.replyToSubject.next(message);\n  }\n\n  cancelReply() {\n    this.replyToSubject.next(null);\n  }\n\n  addReaction(messageId, emoji) {\n    var _this6 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        if (_this6.socketService.isConnected()) {\n          // Try with acknowledgment first\n          const response = yield _this6.socketService.emitWithAck('addReaction', {\n            messageId,\n            emoji\n          });\n\n          if (response.success) {\n            console.log('✅ Reaction added successfully:', response);\n          } else {\n            throw new Error(response.error || 'Failed to add reaction');\n          }\n        } else {\n          // Fallback to HTTP API\n          const currentUser = _this6.userSubject.value;\n\n          if (currentUser) {\n            _this6.apiService.addReaction(messageId, emoji, currentUser).subscribe({\n              next: response => {\n                console.log('✅ Reaction added via HTTP API:', response);\n              },\n              error: error => {\n                console.error('❌ Failed to add reaction via HTTP API:', error);\n              }\n            });\n          }\n        }\n      } catch (error) {\n        console.error('❌ Failed to add reaction:', error); // Fallback to old method\n\n        _this6.socketService.addReaction(messageId, emoji);\n      }\n    })();\n  }\n\n  removeReaction(data) {\n    this.socketService.removeReaction(data);\n  }\n\n  updateMessage(messageId, newText) {\n    var _this7 = this;\n\n    return _asyncToGenerator(function* () {\n      const currentUser = _this7.userSubject.value;\n\n      if (!currentUser) {\n        console.error('Cannot update message - user not logged in');\n        return;\n      }\n\n      try {\n        // Update the message in the local state immediately for better UX\n        const currentMessages = _this7.messagesSubject.value;\n\n        _this7.messagesSubject.next(currentMessages.map(msg => msg.id === messageId ? { ...msg,\n          text: newText,\n          updated_at: new Date().toISOString()\n        } : msg)); // Emit socket event for real-time updates\n\n\n        if (_this7.socketService.isConnectedSubject.value) {\n          _this7.socketService.updateMessage(messageId, newText);\n        } else {\n          // Fallback to HTTP API if socket not connected\n          const updatedMessage = yield _this7.apiService.updateMessage(messageId, newText, currentUser).toPromise();\n\n          _this7.messagesSubject.next(currentMessages.map(msg => msg.id === messageId ? { ...msg,\n            text: newText,\n            updated_at: updatedMessage.updated_at\n          } : msg));\n        }\n      } catch (error) {\n        console.error('Failed to update message:', error);\n        throw error;\n      }\n    })();\n  }\n\n  deleteMessage(messageId) {\n    var _this8 = this;\n\n    return _asyncToGenerator(function* () {\n      const currentUser = _this8.userSubject.value;\n\n      if (!currentUser) {\n        console.error('Cannot delete message - user not logged in');\n        return;\n      }\n\n      try {\n        // Remove the message from local state immediately for better UX\n        const currentMessages = _this8.messagesSubject.value;\n\n        _this8.messagesSubject.next(currentMessages.filter(msg => msg.id !== messageId)); // Emit socket event for real-time updates\n\n\n        if (_this8.socketService.isConnectedSubject.value) {\n          _this8.socketService.emit('messageDeleted', {\n            messageId\n          });\n        } else {\n          // Fallback to HTTP API if socket not connected\n          yield _this8.apiService.deleteMessage(messageId, currentUser).toPromise();\n        }\n      } catch (error) {\n        console.error('Failed to delete message:', error);\n        throw error;\n      }\n    })();\n  }\n\n  showAdminPanel() {\n    this.showAdminPanelSubject.next(true);\n  }\n\n  hideAdminPanel() {\n    this.showAdminPanelSubject.next(false);\n  }\n\n  startEditingMessage(message) {\n    this.editingMessageSubject.next(message);\n  }\n\n  cancelEditingMessage() {\n    this.editingMessageSubject.next(null);\n  }\n\n  logout() {\n    try {\n      // Disconnect socket first\n      this.socketService.disconnect(); // Clear interval\n\n      if (this.messageRefreshInterval) {\n        clearInterval(this.messageRefreshInterval);\n        this.messageRefreshInterval = null;\n      } // Reset all subjects\n\n\n      this.userSubject.next(null);\n      this.messagesSubject.next([]);\n      this.onlineUsersSubject.next([]);\n      this.groupsSubject.next([]);\n      this.currentGroupSubject.next(null);\n      this.replyToSubject.next(null);\n      this.loadingSubject.next(false);\n      this.isAdminSubject.next(false);\n      this.showAdminPanelSubject.next(false);\n      this.securityInfoSubject.next(null);\n      this.editingMessageSubject.next(null);\n      console.log('Logout completed successfully');\n    } catch (error) {\n      console.error('Error during logout:', error);\n    }\n  } // Method to refresh security info for all components\n\n\n  refreshSecurityInfo() {\n    this.apiService.getSecurityInfo().subscribe({\n      next: data => {\n        this.securityInfoSubject.next(data);\n      },\n      error: error => {\n        console.error('Failed to refresh security info:', error);\n      }\n    });\n  }\n\n}\n\nChatService.ɵfac = function ChatService_Factory(t) {\n  return new (t || ChatService)(i0.ɵɵinject(i1.ApiService), i0.ɵɵinject(i2.SocketService), i0.ɵɵinject(i3.NotificationService));\n};\n\nChatService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: ChatService,\n  factory: ChatService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "mappings": ";AACA,SAASA,eAAT,QAA2D,MAA3D;AACA,SAASC,GAAT,QAAoB,gBAApB;AACA,SAASC,UAAT,EAAqBC,OAArB,EAA8BC,KAA9B,EAAqCC,IAArC,EAA2CC,YAA3C,QAA8E,eAA9E;;;;;AAOA,OAAM,MAAOC,WAAP,CAAkB;EAyCtBC,YACUC,UADV,EAEUC,aAFV,EAGUC,mBAHV,EAGkD;IAFxC;IACA;IACA;IA3CF,mBAAc,IAAIX,eAAJ,CAAmC,IAAnC,CAAd;IACA,uBAAkB,IAAIA,eAAJ,CAA+B,EAA/B,CAAlB;IACA,0BAAqB,IAAIA,eAAJ,CAA4B,EAA5B,CAArB;IACA,qBAAgB,IAAIA,eAAJ,CAA6B,EAA7B,CAAhB;IACA,2BAAsB,IAAIA,eAAJ,CAAkC,IAAlC,CAAtB;IACA,sBAAiB,IAAIA,eAAJ,CAAoC,IAApC,CAAjB;IACA,sBAAiB,IAAIA,eAAJ,CAA6B,KAA7B,CAAjB;IACA,sBAAiB,IAAIA,eAAJ,CAA6B,KAA7B,CAAjB;IACA,6BAAwB,IAAIA,eAAJ,CAA6B,KAA7B,CAAxB;IACA,2BAAsB,IAAIA,eAAJ,CAAyC,IAAzC,CAAtB;IACA,6BAAwB,IAAIA,eAAJ,CAAoC,IAApC,CAAxB,CAiC0C,CA/BlD;;IACO,aAAQ,KAAKY,WAAL,CAAiBC,YAAjB,EAAR;IACA,iBAAY,KAAKC,eAAL,CAAqBD,YAArB,EAAZ;IACA,oBAAe,KAAKE,kBAAL,CAAwBF,YAAxB,EAAf;IACA,eAAU,KAAKG,aAAL,CAAmBH,YAAnB,EAAV;IACA,qBAAgB,KAAKI,mBAAL,CAAyBJ,YAAzB,EAAhB;IACA,gBAAW,KAAKK,cAAL,CAAoBL,YAApB,EAAX;IACA,gBAAW,KAAKM,cAAL,CAAoBN,YAApB,EAAX;IACA,gBAAW,KAAKO,cAAL,CAAoBP,YAApB,EAAX;IACA,uBAAkB,KAAKQ,qBAAL,CAA2BR,YAA3B,EAAlB;IACA,qBAAgB,KAAKS,mBAAL,CAAyBT,YAAzB,EAAhB;IACA,uBAAkB,KAAKU,qBAAL,CAA2BV,YAA3B,EAAlB;IACA,kBAAa,KAAKH,aAAL,CAAmBc,YAAhC,CAmB2C,CAjBlD;;IACQ,0BAAqB,IAAIxB,eAAJ,CAA8B,EAA9B,CAArB;IACD,oBAAe,KAAKyB,kBAAL,CAAwBZ,YAAxB,EAAf;IACC,qBAAqB,IAArB,CAc0C,CAZlD;;IACO,mBAAc,KAAKa,KAAL,CAAWC,IAAX,CAAgB1B,GAAG,CAAC2B,IAAI,IAAI,CAAC,CAACA,IAAX,CAAnB,CAAd;IAGC,uBAAkB,KAAlB;IACA,yBAAoB,KAApB;IACA,2BAAsB,CAAtB;IACS,6BAAwB,IAAxB,CAKiC,CALH;;IAO7C,IAAI;MACF,KAAKC,oBAAL;MACA,KAAKC,mBAAL,GAFE,CAIF;;MACCC,MAAc,CAACC,gBAAf,GAAkC,MAAM,KAAKA,gBAAL,EAAxC;;MACAD,MAAc,CAACE,cAAf,GAAgC,MAAM,KAAKC,UAAL,EAAtC;;MACAH,MAAc,CAACI,gBAAf,GAAkC,MAAK;QACtC;QACAC,OAAO,CAACC,GAAR,CAAY,wDAAZ;MACD,CAHA;IAIF,CAXD,CAWE,OAAOC,KAAP,EAAc;MACdF,OAAO,CAACE,KAAR,CAAc,iCAAd,EAAiDA,KAAjD;IACD;EACF;;EAEOT,oBAAoB;IAC1B,KAAKnB,aAAL,CAAmB6B,EAAnB,CAAsB,SAAtB,EAAiC,MAAK;MACpCH,OAAO,CAACC,GAAR,CAAY,qBAAZ,EADoC,CAEpC;;MACA,KAAKlB,cAAL,CAAoBqB,IAApB,CAAyB,KAAzB;IACD,CAJD;IAMA,KAAK9B,aAAL,CAAmB6B,EAAnB,CAAsB,YAAtB,EAAoC,MAAK;MACvCH,OAAO,CAACC,GAAR,CAAY,0BAAZ,EADuC,CAEvC;IACD,CAHD;IAKA,KAAK3B,aAAL,CAAmB6B,EAAnB,CAAsB,eAAtB,EAAwCD,KAAD,IAAU;MAC/CF,OAAO,CAACE,KAAR,CAAc,0BAAd,EAA0CA,KAA1C;MACA,KAAKnB,cAAL,CAAoBqB,IAApB,CAAyB,KAAzB;IACD,CAHD;IAKA,KAAK9B,aAAL,CAAmB6B,EAAnB,CAAsB,YAAtB,EAAqCE,UAAD,IAAwB;MAC1DL,OAAO,CAACC,GAAR,CAAY,uBAAZ,EAAqCI,UAAU,EAAEC,MAAZ,IAAsB,CAA3D,EAA8D,QAA9D;MACA,KAAK1B,aAAL,CAAmBwB,IAAnB,CAAwBC,UAAU,IAAI,EAAtC;;MACA,IAAIA,UAAU,IAAIA,UAAU,CAACC,MAAX,GAAoB,CAAtC,EAAyC;QACvCN,OAAO,CAACC,GAAR,CAAY,6BAAZ,EAA2CI,UAAU,CAAC,CAAD,CAAV,CAAcE,IAAzD;QACA,KAAK1B,mBAAL,CAAyBuB,IAAzB,CAA8BC,UAAU,CAAC,CAAD,CAAxC,EAFuC,CAGvC;;QACA,KAAKG,kBAAL,CAAwBH,UAAU,CAAC,CAAD,CAAV,CAAcI,EAAtC;MACD,CALD,MAKO;QACLT,OAAO,CAACC,GAAR,CAAY,8BAAZ,EADK,CAEL;;QACA,KAAKpB,mBAAL,CAAyBuB,IAAzB,CAA8B,IAA9B;QACA,KAAK1B,eAAL,CAAqB0B,IAArB,CAA0B,EAA1B;MACD;IACF,CAdD;IAgBA,KAAK9B,aAAL,CAAmB6B,EAAnB,CAAsB,gBAAtB,EAAyCO,QAAD,IAAwB;MAC9DV,OAAO,CAACC,GAAR,CAAY,2BAAZ,EAAyCS,QAAQ,CAACJ,MAAlD;MACAN,OAAO,CAACC,GAAR,CAAY,uBAAZ,EAAqCS,QAArC;MACA,KAAKhC,eAAL,CAAqB0B,IAArB,CAA0BM,QAAQ,IAAI,EAAtC;MACAV,OAAO,CAACC,GAAR,CAAY,iDAAZ,EAA+D,KAAKvB,eAAL,CAAqBiC,KAArB,CAA2BL,MAA1F;IACD,CALD;IAOA,KAAKhC,aAAL,CAAmB6B,EAAnB,CAAsB,aAAtB,EAAqC,CAAC;MAAES;IAAF,CAAD,KAAqC;MACxEZ,OAAO,CAACC,GAAR,CAAY,eAAZ,EAA6BW,OAA7B;MACA,MAAMC,MAAM,GAAG,KAAKjC,aAAL,CAAmB+B,KAAlC;MACA,MAAMG,KAAK,GAAGD,MAAM,CAACE,IAAP,CAAYC,CAAC,IAAIA,CAAC,CAACP,EAAF,KAASG,OAA1B,CAAd;;MACA,IAAIE,KAAJ,EAAW;QACT,KAAKjC,mBAAL,CAAyBuB,IAAzB,CAA8BU,KAA9B;MACD;IACF,CAPD;IASA,KAAKxC,aAAL,CAAmB6B,EAAnB,CAAsB,YAAtB,EAAqCc,OAAD,IAAqB;MACvDjB,OAAO,CAACC,GAAR,CAAY,0BAAZ,EAAwCgB,OAAxC;MAEA,MAAMC,eAAe,GAAG,KAAKxC,eAAL,CAAqBiC,KAA7C;MACA,MAAMQ,WAAW,GAAG,KAAK3C,WAAL,CAAiBmC,KAArC;MACA,MAAMS,YAAY,GAAG,KAAKvC,mBAAL,CAAyB8B,KAA9C,CALuD,CAOvD;;MACA,IAAIS,YAAY,IAAIH,OAAO,CAACL,OAAR,KAAoBQ,YAAY,CAACX,EAArD,EAAyD;QACvDT,OAAO,CAACC,GAAR,CAAY,2CAAZ,EAAyDgB,OAAO,CAACL,OAAjE,EAA0E,aAA1E,EAAyFQ,YAAY,CAACX,EAAtG;QACA;MACD,CAXsD,CAavD;;;MACA,MAAMY,eAAe,GAAGH,eAAe,CAACH,IAAhB,CAAqBO,GAAG,IAC9CA,GAAG,CAACb,EAAJ,KAAWQ,OAAO,CAACR,EAAnB,IACCa,GAAG,CAACC,IAAJ,KAAaN,OAAO,CAACM,IAArB,IACAD,GAAG,CAACE,QAAJ,KAAiBP,OAAO,CAACO,QADzB,IAEAC,IAAI,CAACC,GAAL,CAAS,IAAIC,IAAJ,CAASL,GAAG,CAACM,SAAb,EAAwBC,OAAxB,KAAoC,IAAIF,IAAJ,CAASV,OAAO,CAACW,SAAjB,EAA4BC,OAA5B,EAA7C,IAAsF,IAJjE,CAAxB;;MAOA,IAAIR,eAAe,IAAI,CAACA,eAAe,CAACZ,EAAhB,CAAmBqB,UAAnB,CAA8B,OAA9B,CAAxB,EAAgE;QAC9D9B,OAAO,CAACC,GAAR,CAAY,0CAAZ,EAAwDgB,OAAxD;QACA;MACD,CAxBsD,CA0BvD;;;MACA,IAAIA,OAAO,CAACO,QAAR,KAAqBL,WAAzB,EAAsC;QACpC;QACA,MAAMY,eAAe,GAAGb,eAAe,CAACc,SAAhB,CAA0BV,GAAG,IACnDA,GAAG,CAACb,EAAJ,CAAOqB,UAAP,CAAkB,OAAlB,KACAR,GAAG,CAACC,IAAJ,KAAaN,OAAO,CAACM,IADrB,IAEAD,GAAG,CAACE,QAAJ,KAAiBL,WAFjB,IAGAG,GAAG,CAACV,OAAJ,KAAgBK,OAAO,CAACL,OAJF,CAAxB;;QAOA,IAAImB,eAAe,KAAK,CAAC,CAAzB,EAA4B;UAC1B;UACA,MAAME,eAAe,GAAG,CAAC,GAAGf,eAAJ,CAAxB;UACAe,eAAe,CAACF,eAAD,CAAf,GAAmCd,OAAnC;UACA,KAAKvC,eAAL,CAAqB0B,IAArB,CAA0B,KAAK8B,uBAAL,CAA6BD,eAA7B,CAA1B;UACAjC,OAAO,CAACC,GAAR,CAAY,kDAAZ,EAAgEgB,OAAhE;QACD,CAND,MAMO;UACL;UACA,MAAMgB,eAAe,GAAG,CAAC,GAAGf,eAAJ,EAAqBD,OAArB,CAAxB;UACA,KAAKvC,eAAL,CAAqB0B,IAArB,CAA0B,KAAK8B,uBAAL,CAA6BD,eAA7B,CAA1B;UACAjC,OAAO,CAACC,GAAR,CAAY,gEAAZ,EAA8EgB,OAA9E;QACD;MACF,CArBD,MAqBO;QACL;QACA,MAAMgB,eAAe,GAAG,CAAC,GAAGf,eAAJ,EAAqBD,OAArB,CAAxB;QACA,KAAKvC,eAAL,CAAqB0B,IAArB,CAA0B,KAAK8B,uBAAL,CAA6BD,eAA7B,CAA1B;QACAjC,OAAO,CAACC,GAAR,CAAY,mCAAZ,EAAiDgB,OAAjD;MACD,CArDsD,CAuDvD;;;MACA,IAAIA,OAAO,CAACO,QAAR,KAAqBL,WAArB,IAAoCgB,QAAQ,CAACC,MAAjD,EAAyD;QACvD,KAAK7D,mBAAL,CAAyB8D,uBAAzB,CAAiDpB,OAAO,CAACO,QAAzD,EAAmEP,OAAO,CAACM,IAA3E;MACD;IACF,CA3DD;IA6DA,KAAKjD,aAAL,CAAmB6B,EAAnB,CAAsB,gBAAtB,EAAwC,CAAC;MAAEmC,SAAF;MAAaC;IAAb,CAAD,KAAsE;MAC5GvC,OAAO,CAACC,GAAR,CAAY,kBAAZ,EAAgCqC,SAAhC,EAA2CC,SAA3C;MACA,MAAMrB,eAAe,GAAG,KAAKxC,eAAL,CAAqBiC,KAA7C;MACA,KAAKjC,eAAL,CAAqB0B,IAArB,CACEc,eAAe,CAACrD,GAAhB,CAAoByD,GAAG,IACrBA,GAAG,CAACb,EAAJ,KAAW6B,SAAX,GACI,EAAE,GAAGhB,GAAL;QAAUiB;MAAV,CADJ,GAEIjB,GAHN,CADF;IAOD,CAVD;IAYA,KAAKhD,aAAL,CAAmB6B,EAAnB,CAAsB,mBAAtB,EAA4CqC,KAAD,IAAkB;MAC3DxC,OAAO,CAACC,GAAR,CAAY,uBAAZ,EAAqCuC,KAArC;MACA,KAAK7D,kBAAL,CAAwByB,IAAxB,CAA6BoC,KAAK,IAAI,EAAtC;IACD,CAHD;IAKA,KAAKlE,aAAL,CAAmB6B,EAAnB,CAAsB,YAAtB,EAAoC,CAAC;MAAEqB;IAAF,CAAD,KAAuC;MACzExB,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA4BuB,QAA5B,EADyE,CAEzE;IACD,CAHD;IAKA,KAAKlD,aAAL,CAAmB6B,EAAnB,CAAsB,UAAtB,EAAkC,CAAC;MAAEqB;IAAF,CAAD,KAAuC;MACvExB,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0BuB,QAA1B,EADuE,CAEvE;IACD,CAHD,EApI0B,CAyI1B;;IACA,KAAKlD,aAAL,CAAmB6B,EAAnB,CAAsB,YAAtB,EAAoC,CAAC;MAAEqB,QAAF;MAAYZ,OAAZ;MAAqB6B;IAArB,CAAD,KAA8F;MAChIzC,OAAO,CAACC,GAAR,CAAY,MAAMuB,QAAQ,OAAOiB,QAAQ,GAAG,QAAH,GAAc,gBAAgB,aAAa7B,OAAO,EAA3F;MAEA,MAAMQ,YAAY,GAAG,KAAKvC,mBAAL,CAAyB8B,KAA9C;;MACA,IAAIS,YAAY,IAAIR,OAAO,KAAKQ,YAAY,CAACX,EAA7C,EAAiD;QAC/C,MAAMiC,kBAAkB,GAAG,KAAKrD,kBAAL,CAAwBsB,KAAnD;;QAEA,IAAI8B,QAAJ,EAAc;UACZ;UACA,IAAI,CAACC,kBAAkB,CAACC,QAAnB,CAA4BnB,QAA5B,CAAL,EAA4C;YAC1C,KAAKnC,kBAAL,CAAwBe,IAAxB,CAA6B,CAAC,GAAGsC,kBAAJ,EAAwBlB,QAAxB,CAA7B;UACD;QACF,CALD,MAKO;UACL;UACA,KAAKnC,kBAAL,CAAwBe,IAAxB,CAA6BsC,kBAAkB,CAACE,MAAnB,CAA0BpD,IAAI,IAAIA,IAAI,KAAKgC,QAA3C,CAA7B;QACD;MACF;IACF,CAjBD;IAmBA,KAAKlD,aAAL,CAAmB6B,EAAnB,CAAsB,gBAAtB,EAAwC,CAAC;MAAEmC,SAAF;MAAaO,OAAb;MAAsBC;IAAtB,CAAD,KAAiG;MACvI9C,OAAO,CAACC,GAAR,CAAY,kBAAZ,EAAgCqC,SAAhC,EAA2CO,OAA3C;MACA,MAAM3B,eAAe,GAAG,KAAKxC,eAAL,CAAqBiC,KAA7C;MACA,KAAKjC,eAAL,CAAqB0B,IAArB,CACEc,eAAe,CAACrD,GAAhB,CAAoByD,GAAG,IACrBA,GAAG,CAACb,EAAJ,KAAW6B,SAAX,GACI,EAAE,GAAGhB,GAAL;QAAUC,IAAI,EAAEsB,OAAhB;QAAyBE,UAAU,EAAED;MAArC,CADJ,GAEIxB,GAHN,CADF;IAOD,CAVD;IAYA,KAAKhD,aAAL,CAAmB6B,EAAnB,CAAsB,gBAAtB,EAAwC,CAAC;MAAEmC;IAAF,CAAD,KAAyC;MAC/EtC,OAAO,CAACC,GAAR,CAAY,kBAAZ,EAAgCqC,SAAhC;MACA,MAAMpB,eAAe,GAAG,KAAKxC,eAAL,CAAqBiC,KAA7C;MACA,KAAKjC,eAAL,CAAqB0B,IAArB,CACEc,eAAe,CAAC0B,MAAhB,CAAuBtB,GAAG,IAAIA,GAAG,CAACb,EAAJ,KAAW6B,SAAzC,CADF;IAGD,CAND;IAQA,KAAKhE,aAAL,CAAmB6B,EAAnB,CAAsB,OAAtB,EAAgCD,KAAD,IAAe;MAC5CF,OAAO,CAACE,KAAR,CAAc,eAAd,EAA+BA,KAA/B;IACD,CAFD;EAGD;;EAEOR,mBAAmB;IACzB;IACA,IAAI,KAAKsD,sBAAT,EAAiC;MAC/BC,aAAa,CAAC,KAAKD,sBAAN,CAAb;IACD,CAJwB,CAMzB;IACA;;IACA;;;;;;;;;;;;;;;;;;;;;EAoBD;;EAEKE,KAAK,UAAsE;IAAA;;IAAA,oCAArE1B,QAAqE,EAAnD2B,QAAmD,EAAhCC,aAA4B,IAAI;MAC/E,IAAI;QACF,KAAI,CAACrE,cAAL,CAAoBqB,IAApB,CAAyB,IAAzB;;QACAJ,OAAO,CAACC,GAAR,CAAY,6BAAZ,EAA2CuB,QAA3C;QAEA,MAAM6B,QAAQ,SAAS,KAAI,CAAChF,UAAL,CAAgBiF,SAAhB,CAA0B9B,QAA1B,EAAoC2B,QAApC,EAA8CC,UAAU,IAAIG,SAA5D,EAAuEC,SAAvE,EAAvB;QACAxD,OAAO,CAACC,GAAR,CAAY,qBAAZ,EAAmCoD,QAAnC,EALE,CAOF;;QACA,IAAIA,QAAQ,EAAEI,gBAAd,EAAgC;UAC9B,MAAM,IAAIC,KAAJ,CAAU,iCAAV,CAAN;QACD,CAVC,CAYF;;;QACA1D,OAAO,CAACC,GAAR,CAAY,yBAAZ;;QACA,KAAI,CAAC3B,aAAL,CAAmBqF,OAAnB,CAA2BnC,QAA3B,EAAqC2B,QAArC,EAA+CC,UAA/C;;QAEA,KAAI,CAAC5E,WAAL,CAAiB4B,IAAjB,CAAsBoB,QAAtB;;QACA,KAAI,CAACxC,cAAL,CAAoBoB,IAApB,CAAyBiD,QAAQ,EAAEO,OAAV,IAAqB,KAA9C;;QACA,KAAI,CAAC1E,mBAAL,CAAyBkB,IAAzB,CAA8BiD,QAAQ,EAAEQ,YAAV,IAA0B,IAAxD;;QAEA7D,OAAO,CAACC,GAAR,CAAY,mCAAZ,EAAiDuB,QAAjD,EAA2D,SAA3D,EAAsE6B,QAAQ,EAAEO,OAAV,IAAqB,KAA3F,EAAkG,GAAlG,EApBE,CAsBF;QACA;QACA;QAEA;;QACAE,UAAU,CAAC,MAAK;UACd,IAAI,KAAI,CAAClF,aAAL,CAAmB+B,KAAnB,CAAyBL,MAAzB,KAAoC,CAAxC,EAA2C;YACzCN,OAAO,CAACC,GAAR,CAAY,kDAAZ;;YACA,KAAI,CAAC8D,cAAL,CAAoBvC,QAApB;UACD;QACF,CALS,EAKP,IALO,CAAV;MAOD,CAlCD,CAkCE,OAAOtB,KAAP,EAAc;QACdF,OAAO,CAACE,KAAR,CAAc,eAAd,EAA+BA,KAA/B;QACA,MAAMA,KAAN;MACD,CArCD,SAqCU;QACR,KAAI,CAACnB,cAAL,CAAoBqB,IAApB,CAAyB,KAAzB;MACD;IAxC8E;EAyChF;;EAEKI,kBAAkB,CAACI,OAAD,EAAgB;IAAA;;IAAA;MACtC;MACA,IAAI,MAAI,CAACoD,iBAAT,EAA4B;QAC1BhE,OAAO,CAACC,GAAR,CAAY,uCAAZ;QACA;MACD,CALqC,CAOtC;;;MACA,MAAMgE,GAAG,GAAGtC,IAAI,CAACsC,GAAL,EAAZ;;MACA,IAAIA,GAAG,GAAG,MAAI,CAACC,mBAAX,GAAiC,MAAI,CAACC,qBAA1C,EAAiE;QAC/DnE,OAAO,CAACC,GAAR,CAAY,iCAAZ;QACA;MACD;;MACD,MAAI,CAACiE,mBAAL,GAA2BD,GAA3B;MAEA,MAAI,CAACD,iBAAL,GAAyB,IAAzB;;MACA,IAAI;QACFhE,OAAO,CAACC,GAAR,CAAY,oCAAZ,EAAkDW,OAAlD;QACA,MAAMF,QAAQ,SAAS,MAAI,CAACrC,UAAL,CAAgB+F,WAAhB,CAA4BxD,OAA5B,EAAqC,EAArC,EAAyC4C,SAAzC,EAAvB;QACAxD,OAAO,CAACC,GAAR,CAAY,kBAAZ,EAAgCS,QAAQ,EAAEJ,MAAV,IAAoB,CAApD;QACAN,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA8BS,QAA9B,EAJE,CAMF;QACA;;QACA,MAAI,CAAChC,eAAL,CAAqB0B,IAArB,CAA0BM,QAAQ,IAAI,EAAtC;;QACAV,OAAO,CAACC,GAAR,CAAY,0CAAZ,EAAwD,MAAI,CAACvB,eAAL,CAAqBiC,KAArB,CAA2BL,MAAnF;MACD,CAVD,CAUE,OAAOJ,KAAP,EAAc;QACdF,OAAO,CAACE,KAAR,CAAc,0BAAd,EAA0CA,KAA1C,EADc,CAEd;;QACA,MAAI,CAACxB,eAAL,CAAqB0B,IAArB,CAA0B,EAA1B;MACD,CAdD,SAcU;QACR,MAAI,CAAC4D,iBAAL,GAAyB,KAAzB;MACD;IAhCqC;EAiCvC;;EAEKD,cAAc,CAACvC,QAAD,EAAiB;IAAA;;IAAA;MACnC;MACA,IAAI,MAAI,CAAC6C,eAAT,EAA0B;QACxBrE,OAAO,CAACC,GAAR,CAAY,qCAAZ;QACA;MACD;;MAED,MAAI,CAACoE,eAAL,GAAuB,IAAvB;;MACA,IAAI;QACFrE,OAAO,CAACC,GAAR,CAAY,0BAAZ,EAAwCuB,QAAxC;QACA,MAAMX,MAAM,SAAS,MAAI,CAACxC,UAAL,CAAgBiG,aAAhB,CAA8B9C,QAA9B,EAAwCgC,SAAxC,EAArB;QACAxD,OAAO,CAACC,GAAR,CAAY,qBAAZ,EAAmCY,MAAnC;;QACA,MAAI,CAACjC,aAAL,CAAmBwB,IAAnB,CAAwBS,MAAM,IAAI,EAAlC;;QACA,IAAIA,MAAM,IAAIA,MAAM,CAACP,MAAP,GAAgB,CAA9B,EAAiC;UAC/BN,OAAO,CAACC,GAAR,CAAY,6BAAZ,EAA2CY,MAAM,CAAC,CAAD,CAAjD;;UACA,MAAI,CAAChC,mBAAL,CAAyBuB,IAAzB,CAA8BS,MAAM,CAAC,CAAD,CAApC;;UACA,MAAI,CAACL,kBAAL,CAAwBK,MAAM,CAAC,CAAD,CAAN,CAAUJ,EAAlC;QACD;MACF,CAVD,CAUE,OAAOP,KAAP,EAAc;QACdF,OAAO,CAACE,KAAR,CAAc,6BAAd,EAA6CA,KAA7C;MACD,CAZD,SAYU;QACR,MAAI,CAACmE,eAAL,GAAuB,KAAvB;MACD;IAtBkC;EAuBpC,CAvXqB,CAyXtB;;;EACMzE,gBAAgB;IAAA;;IAAA;MACpB,MAAMuB,WAAW,GAAG,MAAI,CAAC3C,WAAL,CAAiBmC,KAArC;;MACA,IAAIQ,WAAJ,EAAiB;QACfnB,OAAO,CAACC,GAAR,CAAY,8BAAZ,EAA4CkB,WAA5C;QACA,MAAM,MAAI,CAAC4C,cAAL,CAAoB5C,WAApB,CAAN;MACD,CAHD,MAGO;QACLnB,OAAO,CAACE,KAAR,CAAc,mBAAd;MACD;IAPmB;EAQrB,CAlYqB,CAoYtB;;;EACAJ,UAAU;IACRE,OAAO,CAACC,GAAR,CAAY,kCAAZ;IACAD,OAAO,CAACC,GAAR,CAAY,OAAZ,EAAqB,KAAKzB,WAAL,CAAiBmC,KAAtC;IACAX,OAAO,CAACC,GAAR,CAAY,SAAZ,EAAuB,KAAKrB,aAAL,CAAmB+B,KAA1C;IACAX,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA8B,KAAKpB,mBAAL,CAAyB8B,KAAvD;IACAX,OAAO,CAACC,GAAR,CAAY,mBAAZ,EAAiC,KAAK3B,aAAL,CAAmBiG,kBAAnB,CAAsC5D,KAAvE;IACAX,OAAO,CAACC,GAAR,CAAY,iBAAZ,EAA+B,KAAKoE,eAApC;IACArE,OAAO,CAACC,GAAR,CAAY,mBAAZ,EAAiC,KAAK+D,iBAAtC;IACAhE,OAAO,CAACC,GAAR,CAAY,kCAAZ;EACD,CA9YqB,CAgZtB;;;EACQiC,uBAAuB,CAACxB,QAAD,EAAoB;IACjD,OAAOA,QAAQ,CAAC8D,IAAT,CAAc,CAACC,CAAD,EAAIC,CAAJ,KAAS;MAC5B,MAAMC,KAAK,GAAG,IAAIhD,IAAJ,CAAS8C,CAAC,CAAC7C,SAAX,EAAsBC,OAAtB,EAAd;MACA,MAAM+C,KAAK,GAAG,IAAIjD,IAAJ,CAAS+C,CAAC,CAAC9C,SAAX,EAAsBC,OAAtB,EAAd;MACA,OAAO8C,KAAK,GAAGC,KAAf;IACD,CAJM,CAAP;EAKD,CAvZqB,CAyZtB;;;EACQC,wBAAwB,CAACC,MAAD,EAAiBC,WAAjB,EAAqC;IACnE,MAAM7D,eAAe,GAAG,KAAKxC,eAAL,CAAqBiC,KAA7C;IACA,MAAMsB,eAAe,GAAGf,eAAe,CAACrD,GAAhB,CAAoByD,GAAG,IAC7CA,GAAG,CAACb,EAAJ,KAAWqE,MAAX,GAAoBC,WAApB,GAAkCzD,GADZ,CAAxB;IAGA,KAAK5C,eAAL,CAAqB0B,IAArB,CAA0B,KAAK8B,uBAAL,CAA6BD,eAA7B,CAA1B;IACAjC,OAAO,CAACC,GAAR,CAAY,kDAAZ,EAAgE8E,WAAhE;EACD,CAjaqB,CAmatB;;;EACQC,uBAAuB,CAACF,MAAD,EAAe;IAC5C,MAAM5D,eAAe,GAAG,KAAKxC,eAAL,CAAqBiC,KAA7C;IACA,MAAMsB,eAAe,GAAGf,eAAe,CAAC0B,MAAhB,CAAuBtB,GAAG,IAAIA,GAAG,CAACb,EAAJ,KAAWqE,MAAzC,CAAxB;IACA,KAAKpG,eAAL,CAAqB0B,IAArB,CAA0B6B,eAA1B;IACAjC,OAAO,CAACC,GAAR,CAAY,4CAAZ,EAA0D6E,MAA1D;EACD,CAzaqB,CA2atB;;;EACQG,aAAa,CAAChE,OAAD,EAAmBP,QAAnB,EAAsC;IACzD,OAAOA,QAAQ,CAACwE,IAAT,CAAc5D,GAAG,IACtBA,GAAG,CAACb,EAAJ,KAAWQ,OAAO,CAACR,EAAnB,IACCa,GAAG,CAACC,IAAJ,KAAaN,OAAO,CAACM,IAArB,IACAD,GAAG,CAACE,QAAJ,KAAiBP,OAAO,CAACO,QADzB,IAEAC,IAAI,CAACC,GAAL,CAAS,IAAIC,IAAJ,CAASL,GAAG,CAACM,SAAb,EAAwBC,OAAxB,KAAoC,IAAIF,IAAJ,CAASV,OAAO,CAACW,SAAjB,EAA4BC,OAA5B,EAA7C,IAAsF,IAJlF,CAAP;EAMD;;EAEDsD,SAAS,CAACvE,OAAD,EAAgB;IACvB,MAAMO,WAAW,GAAG,KAAK3C,WAAL,CAAiBmC,KAArC;;IACA,IAAI,CAACC,OAAD,IAAY,CAACO,WAAjB,EAA8B;MAC5BnB,OAAO,CAACE,KAAR,CAAc,6CAAd;MACA;IACD;;IAEDF,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA8BW,OAA9B,EAPuB,CASvB;;IACA,IAAI,CAAC,KAAKtC,aAAL,CAAmBiG,kBAAnB,CAAsC5D,KAA3C,EAAkD;MAChDX,OAAO,CAACE,KAAR,CAAc,0CAAd;MACA;IACD;;IAED,KAAK5B,aAAL,CAAmB6G,SAAnB,CAA6BvE,OAA7B;IACA,MAAMC,MAAM,GAAG,KAAKjC,aAAL,CAAmB+B,KAAlC;IACA,MAAMG,KAAK,GAAGD,MAAM,CAACE,IAAP,CAAYC,CAAC,IAAIA,CAAC,CAACP,EAAF,KAASG,OAA1B,CAAd;;IACA,IAAIE,KAAJ,EAAW;MACT,KAAKjC,mBAAL,CAAyBuB,IAAzB,CAA8BU,KAA9B,EADS,CAET;;MACA,KAAKN,kBAAL,CAAwBI,OAAxB;IACD,CAJD,MAIO;MACLZ,OAAO,CAACE,KAAR,CAAc,iCAAd,EAAiDU,OAAjD;IACD;EACF;;EAEDwE,WAAW,CAAC7D,IAAD,EAAe8D,YAA2B,IAA1C,EAA8C;IACvD,MAAMlE,WAAW,GAAG,KAAK3C,WAAL,CAAiBmC,KAArC;IACA,MAAMS,YAAY,GAAG,KAAKvC,mBAAL,CAAyB8B,KAA9C;;IAEA,IAAI,CAACY,IAAI,CAAC+D,IAAL,EAAL,EAAkB;MAChBtF,OAAO,CAACE,KAAR,CAAc,kCAAd;MACA;IACD;;IAED,IAAI,CAACiB,WAAL,EAAkB;MAChBnB,OAAO,CAACE,KAAR,CAAc,0CAAd;MACA;IACD;;IAED,IAAI,CAACkB,YAAL,EAAmB;MACjBpB,OAAO,CAACE,KAAR,CAAc,4DAAd,EAA4E,KAAKtB,aAAL,CAAmB+B,KAA/F,EADiB,CAEjB;;MACA,MAAME,MAAM,GAAG,KAAKjC,aAAL,CAAmB+B,KAAlC;;MACA,IAAIE,MAAM,IAAIA,MAAM,CAACP,MAAP,GAAgB,CAA9B,EAAiC;QAC/BN,OAAO,CAACC,GAAR,CAAY,uCAAZ,EAAqDY,MAAM,CAAC,CAAD,CAA3D;QACA,KAAKhC,mBAAL,CAAyBuB,IAAzB,CAA8BS,MAAM,CAAC,CAAD,CAApC;QACA,KAAKL,kBAAL,CAAwBK,MAAM,CAAC,CAAD,CAAN,CAAUJ,EAAlC,EAH+B,CAI/B;QACA;;QACA;MACD,CAPD,MAOO;QACLT,OAAO,CAACE,KAAR,CAAc,8BAAd;QACA;MACD;IACF,CA7BsD,CA+BvD;;;IACA,MAAMqF,iBAAiB,GAAY;MACjC9E,EAAE,EAAE,QAAQkB,IAAI,CAACsC,GAAL,EAAU,IAAIxC,IAAI,CAAC+D,MAAL,GAAcC,QAAd,CAAuB,EAAvB,EAA2BC,MAA3B,CAAkC,CAAlC,EAAqC,CAArC,CAAuC,EADhC;MAEjCnE,IAAI,EAAEA,IAAI,CAAC+D,IAAL,EAF2B;MAGjC9D,QAAQ,EAAEL,WAHuB;MAIjCS,SAAS,EAAE,IAAID,IAAJ,GAAWgE,WAAX,EAJsB;MAKjC/E,OAAO,EAAEQ,YAAY,CAACX,EALW;MAMjCmF,OAAO,EAAEP,SAAS,IAAI9B,SANW;MAOjChB,SAAS,EAAE;IAPsB,CAAnC,CAhCuD,CA0CvD;;IACA,MAAMrB,eAAe,GAAG,KAAKxC,eAAL,CAAqBiC,KAA7C;IACA,MAAMsB,eAAe,GAAG,CAAC,GAAGf,eAAJ,EAAqBqE,iBAArB,CAAxB;IACA,KAAK7G,eAAL,CAAqB0B,IAArB,CAA0B6B,eAA1B;IACAjC,OAAO,CAACC,GAAR,CAAY,8BAAZ,EAA4CsF,iBAA5C,EA9CuD,CAgDvD;;IACA,KAAKM,oBAAL,CAA0BtE,IAA1B,EAAgCH,YAAY,CAACX,EAA7C,EAAiD4E,SAAjD,EAA4DE,iBAA5D;EACD;;EAEaM,oBAAoB,qBAKV;IAAA;;IAAA,oCAJtBtE,IAIsB,EAHtBX,OAGsB,EAFtByE,SAEsB,EADtBE,iBACsB,EAAtBO,aAAqB,CAAC;MAEtB,MAAMC,UAAU,GAAG,CAAnB;MACA,MAAMC,UAAU,GAAG,OAAOvE,IAAI,CAACwE,GAAL,CAAS,CAAT,EAAYH,UAAZ,CAA1B,CAHsB,CAG6B;;MAEnD,IAAI;QACF,IAAI,MAAI,CAACxH,aAAL,CAAmB4H,WAAnB,EAAJ,EAAsC;UACpC;UACA,MAAMC,QAAQ,SAAS,MAAI,CAAC7H,aAAL,CAAmB8H,WAAnB,CAA+B,aAA/B,EAA8C;YACnE7E,IADmE;YAEnEX,OAFmE;YAGnEgF,OAAO,EAAEP,SAH0D;YAInE/C,SAAS,EAAEiD,iBAAiB,CAAC9E;UAJsC,CAA9C,CAAvB;;UAOA,IAAI0F,QAAQ,CAACE,OAAb,EAAsB;YACpBrG,OAAO,CAACC,GAAR,CAAY,yCAAZ,EAAuDkG,QAAvD,EADoB,CAEpB;;YACA,MAAI,CAACtB,wBAAL,CAA8BU,iBAAiB,CAAC9E,EAAhD,EAAoD0F,QAAQ,CAAClF,OAA7D;;YACA;UACD,CALD,MAKO;YACL,MAAM,IAAIyC,KAAJ,CAAUyC,QAAQ,CAACjG,KAAT,IAAkB,eAA5B,CAAN;UACD;QACF,CAjBD,MAiBO;UACL,MAAM,IAAIwD,KAAJ,CAAU,sBAAV,CAAN;QACD;MACF,CArBD,CAqBE,OAAOxD,KAAP,EAAc;QACdF,OAAO,CAACE,KAAR,CAAc,qCAAqC4F,UAAU,GAAG,CAAC,IAAjE,EAAuE5F,KAAvE;;QAEA,IAAI4F,UAAU,GAAGC,UAAjB,EAA6B;UAC3B;UACA/F,OAAO,CAACC,GAAR,CAAY,kBAAkB+F,UAAU,OAAxC;UACAlC,UAAU,CAAC,MAAK;YACd,MAAI,CAAC+B,oBAAL,CAA0BtE,IAA1B,EAAgCX,OAAhC,EAAyCyE,SAAzC,EAAoDE,iBAApD,EAAuEO,UAAU,GAAG,CAApF;UACD,CAFS,EAEPE,UAFO,CAAV;QAGD,CAND,MAMO;UACL;UACAhG,OAAO,CAACC,GAAR,CAAY,wDAAZ;;UACA,MAAI,CAACqG,0BAAL,CAAgC/E,IAAhC,EAAsCX,OAAtC,EAA+CyE,SAA/C,EAA0DE,iBAA1D;QACD;MACF;IAxCqB;EAyCvB;;EAEOe,0BAA0B,CAChC/E,IADgC,EAEhCX,OAFgC,EAGhCyE,SAHgC,EAIhCE,iBAJgC,EAIN;IAE1B,MAAMpE,WAAW,GAAG,KAAK3C,WAAL,CAAiBmC,KAArC;;IACA,IAAI,CAACQ,WAAL,EAAkB;MAChB,KAAK6D,uBAAL,CAA6BO,iBAAiB,CAAC9E,EAA/C;MACA;IACD;;IAED,KAAKpC,UAAL,CAAgB+G,WAAhB,CAA4B7D,IAA5B,EAAkCJ,WAAlC,EAA+CP,OAA/C,EAAwDyE,SAAxD,EAAmEkB,SAAnE,CAA6E;MAC3EnG,IAAI,EAAG+F,QAAD,IAAa;QACjBnG,OAAO,CAACC,GAAR,CAAY,8BAAZ,EAA4CkG,QAA5C;QACA,KAAKtB,wBAAL,CAA8BU,iBAAiB,CAAC9E,EAAhD,EAAoD0F,QAApD;MACD,CAJ0E;MAK3EjG,KAAK,EAAGA,KAAD,IAAU;QACfF,OAAO,CAACE,KAAR,CAAc,wCAAd,EAAwDA,KAAxD;QACA,KAAK8E,uBAAL,CAA6BO,iBAAiB,CAAC9E,EAA/C,EAFe,CAGf;;QACA,KAAK+F,gBAAL,CAAsB,2CAAtB;MACD;IAV0E,CAA7E;EAYD;;EAEOA,gBAAgB,CAACvF,OAAD,EAAgB;IACtC;IACAjB,OAAO,CAACE,KAAR,CAAc,mBAAd,EAAmCe,OAAnC,EAFsC,CAGtC;EACD,CAllBqB,CAolBtB;;;EACAwF,WAAW;IACT,MAAMrF,YAAY,GAAG,KAAKvC,mBAAL,CAAyB8B,KAA9C;;IACA,IAAIS,YAAY,IAAI,KAAK9C,aAAL,CAAmB4H,WAAnB,EAApB,EAAsD;MACpD,KAAK5H,aAAL,CAAmBoI,mBAAnB,CAAuCtF,YAAY,CAACX,EAApD,EAAwD,IAAxD,EADoD,CAGpD;;MACA,IAAI,KAAKkG,aAAT,EAAwB;QACtBC,YAAY,CAAC,KAAKD,aAAN,CAAZ;MACD,CANmD,CAQpD;;;MACA,KAAKA,aAAL,GAAqB7C,UAAU,CAAC,MAAK;QACnC,KAAK+C,UAAL;MACD,CAF8B,EAE5B,IAF4B,CAA/B;IAGD;EACF;;EAEDA,UAAU;IACR,MAAMzF,YAAY,GAAG,KAAKvC,mBAAL,CAAyB8B,KAA9C;;IACA,IAAIS,YAAY,IAAI,KAAK9C,aAAL,CAAmB4H,WAAnB,EAApB,EAAsD;MACpD,KAAK5H,aAAL,CAAmBoI,mBAAnB,CAAuCtF,YAAY,CAACX,EAApD,EAAwD,KAAxD,EADoD,CAGpD;;MACA,IAAI,KAAKkG,aAAT,EAAwB;QACtBC,YAAY,CAAC,KAAKD,aAAN,CAAZ;QACA,KAAKA,aAAL,GAAqB,IAArB;MACD;IACF;EACF;;EAEDG,cAAc,CAAC7F,OAAD,EAAiB;IAC7B,KAAKnC,cAAL,CAAoBsB,IAApB,CAAyBa,OAAzB;EACD;;EAED8F,WAAW;IACT,KAAKjI,cAAL,CAAoBsB,IAApB,CAAyB,IAAzB;EACD;;EAEK4G,WAAW,CAAC1E,SAAD,EAAoB2E,KAApB,EAAiC;IAAA;;IAAA;MAChD,IAAI;QACF,IAAI,MAAI,CAAC3I,aAAL,CAAmB4H,WAAnB,EAAJ,EAAsC;UACpC;UACA,MAAMC,QAAQ,SAAS,MAAI,CAAC7H,aAAL,CAAmB8H,WAAnB,CAA+B,aAA/B,EAA8C;YACnE9D,SADmE;YAEnE2E;UAFmE,CAA9C,CAAvB;;UAKA,IAAId,QAAQ,CAACE,OAAb,EAAsB;YACpBrG,OAAO,CAACC,GAAR,CAAY,gCAAZ,EAA8CkG,QAA9C;UACD,CAFD,MAEO;YACL,MAAM,IAAIzC,KAAJ,CAAUyC,QAAQ,CAACjG,KAAT,IAAkB,wBAA5B,CAAN;UACD;QACF,CAZD,MAYO;UACL;UACA,MAAMiB,WAAW,GAAG,MAAI,CAAC3C,WAAL,CAAiBmC,KAArC;;UACA,IAAIQ,WAAJ,EAAiB;YACf,MAAI,CAAC9C,UAAL,CAAgB2I,WAAhB,CAA4B1E,SAA5B,EAAuC2E,KAAvC,EAA8C9F,WAA9C,EAA2DoF,SAA3D,CAAqE;cACnEnG,IAAI,EAAG+F,QAAD,IAAa;gBACjBnG,OAAO,CAACC,GAAR,CAAY,gCAAZ,EAA8CkG,QAA9C;cACD,CAHkE;cAInEjG,KAAK,EAAGA,KAAD,IAAU;gBACfF,OAAO,CAACE,KAAR,CAAc,wCAAd,EAAwDA,KAAxD;cACD;YANkE,CAArE;UAQD;QACF;MACF,CA3BD,CA2BE,OAAOA,KAAP,EAAc;QACdF,OAAO,CAACE,KAAR,CAAc,2BAAd,EAA2CA,KAA3C,EADc,CAEd;;QACA,MAAI,CAAC5B,aAAL,CAAmB0I,WAAnB,CAA+B1E,SAA/B,EAA0C2E,KAA1C;MACD;IAhC+C;EAiCjD;;EAEDC,cAAc,CAACC,IAAD,EAA4C;IACxD,KAAK7I,aAAL,CAAmB4I,cAAnB,CAAkCC,IAAlC;EACD;;EAEKC,aAAa,CAAC9E,SAAD,EAAoBO,OAApB,EAAmC;IAAA;;IAAA;MACpD,MAAM1B,WAAW,GAAG,MAAI,CAAC3C,WAAL,CAAiBmC,KAArC;;MACA,IAAI,CAACQ,WAAL,EAAkB;QAChBnB,OAAO,CAACE,KAAR,CAAc,4CAAd;QACA;MACD;;MAED,IAAI;QACF;QACA,MAAMgB,eAAe,GAAG,MAAI,CAACxC,eAAL,CAAqBiC,KAA7C;;QACA,MAAI,CAACjC,eAAL,CAAqB0B,IAArB,CACEc,eAAe,CAACrD,GAAhB,CAAoByD,GAAG,IACrBA,GAAG,CAACb,EAAJ,KAAW6B,SAAX,GACI,EAAE,GAAGhB,GAAL;UAAUC,IAAI,EAAEsB,OAAhB;UAAyBE,UAAU,EAAE,IAAIpB,IAAJ,GAAWgE,WAAX;QAArC,CADJ,GAEIrE,GAHN,CADF,EAHE,CAWF;;;QACA,IAAI,MAAI,CAAChD,aAAL,CAAmBiG,kBAAnB,CAAsC5D,KAA1C,EAAiD;UAC/C,MAAI,CAACrC,aAAL,CAAmB8I,aAAnB,CAAiC9E,SAAjC,EAA4CO,OAA5C;QACD,CAFD,MAEO;UACL;UACA,MAAMwE,cAAc,SAAS,MAAI,CAAChJ,UAAL,CAAgB+I,aAAhB,CAA8B9E,SAA9B,EAAyCO,OAAzC,EAAkD1B,WAAlD,EAA+DqC,SAA/D,EAA7B;;UACA,MAAI,CAAC9E,eAAL,CAAqB0B,IAArB,CACEc,eAAe,CAACrD,GAAhB,CAAoByD,GAAG,IACrBA,GAAG,CAACb,EAAJ,KAAW6B,SAAX,GACI,EAAE,GAAGhB,GAAL;YAAUC,IAAI,EAAEsB,OAAhB;YAAyBE,UAAU,EAAEsE,cAAc,CAACtE;UAApD,CADJ,GAEIzB,GAHN,CADF;QAOD;MACF,CAzBD,CAyBE,OAAOpB,KAAP,EAAc;QACdF,OAAO,CAACE,KAAR,CAAc,2BAAd,EAA2CA,KAA3C;QACA,MAAMA,KAAN;MACD;IAnCmD;EAoCrD;;EAEKoH,aAAa,CAAChF,SAAD,EAAkB;IAAA;;IAAA;MACnC,MAAMnB,WAAW,GAAG,MAAI,CAAC3C,WAAL,CAAiBmC,KAArC;;MACA,IAAI,CAACQ,WAAL,EAAkB;QAChBnB,OAAO,CAACE,KAAR,CAAc,4CAAd;QACA;MACD;;MAED,IAAI;QACF;QACA,MAAMgB,eAAe,GAAG,MAAI,CAACxC,eAAL,CAAqBiC,KAA7C;;QACA,MAAI,CAACjC,eAAL,CAAqB0B,IAArB,CACEc,eAAe,CAAC0B,MAAhB,CAAuBtB,GAAG,IAAIA,GAAG,CAACb,EAAJ,KAAW6B,SAAzC,CADF,EAHE,CAOF;;;QACA,IAAI,MAAI,CAAChE,aAAL,CAAmBiG,kBAAnB,CAAsC5D,KAA1C,EAAiD;UAC/C,MAAI,CAACrC,aAAL,CAAmBiJ,IAAnB,CAAwB,gBAAxB,EAA0C;YAAEjF;UAAF,CAA1C;QACD,CAFD,MAEO;UACL;UACA,MAAM,MAAI,CAACjE,UAAL,CAAgBiJ,aAAhB,CAA8BhF,SAA9B,EAAyCnB,WAAzC,EAAsDqC,SAAtD,EAAN;QACD;MACF,CAdD,CAcE,OAAOtD,KAAP,EAAc;QACdF,OAAO,CAACE,KAAR,CAAc,2BAAd,EAA2CA,KAA3C;QACA,MAAMA,KAAN;MACD;IAxBkC;EAyBpC;;EAEDsH,cAAc;IACZ,KAAKvI,qBAAL,CAA2BmB,IAA3B,CAAgC,IAAhC;EACD;;EAEDqH,cAAc;IACZ,KAAKxI,qBAAL,CAA2BmB,IAA3B,CAAgC,KAAhC;EACD;;EAEDsH,mBAAmB,CAACzG,OAAD,EAAiB;IAClC,KAAK9B,qBAAL,CAA2BiB,IAA3B,CAAgCa,OAAhC;EACD;;EAED0G,oBAAoB;IAClB,KAAKxI,qBAAL,CAA2BiB,IAA3B,CAAgC,IAAhC;EACD;;EAEDwH,MAAM;IACJ,IAAI;MACF;MACA,KAAKtJ,aAAL,CAAmBuJ,UAAnB,GAFE,CAIF;;MACA,IAAI,KAAK7E,sBAAT,EAAiC;QAC/BC,aAAa,CAAC,KAAKD,sBAAN,CAAb;QACA,KAAKA,sBAAL,GAA8B,IAA9B;MACD,CARC,CAUF;;;MACA,KAAKxE,WAAL,CAAiB4B,IAAjB,CAAsB,IAAtB;MACA,KAAK1B,eAAL,CAAqB0B,IAArB,CAA0B,EAA1B;MACA,KAAKzB,kBAAL,CAAwByB,IAAxB,CAA6B,EAA7B;MACA,KAAKxB,aAAL,CAAmBwB,IAAnB,CAAwB,EAAxB;MACA,KAAKvB,mBAAL,CAAyBuB,IAAzB,CAA8B,IAA9B;MACA,KAAKtB,cAAL,CAAoBsB,IAApB,CAAyB,IAAzB;MACA,KAAKrB,cAAL,CAAoBqB,IAApB,CAAyB,KAAzB;MACA,KAAKpB,cAAL,CAAoBoB,IAApB,CAAyB,KAAzB;MACA,KAAKnB,qBAAL,CAA2BmB,IAA3B,CAAgC,KAAhC;MACA,KAAKlB,mBAAL,CAAyBkB,IAAzB,CAA8B,IAA9B;MACA,KAAKjB,qBAAL,CAA2BiB,IAA3B,CAAgC,IAAhC;MAEAJ,OAAO,CAACC,GAAR,CAAY,+BAAZ;IACD,CAxBD,CAwBE,OAAOC,KAAP,EAAc;MACdF,OAAO,CAACE,KAAR,CAAc,sBAAd,EAAsCA,KAAtC;IACD;EACF,CA/wBqB,CAixBtB;;;EACA4H,mBAAmB;IACjB,KAAKzJ,UAAL,CAAgB0J,eAAhB,GAAkCxB,SAAlC,CAA4C;MAC1CnG,IAAI,EAAG+G,IAAD,IAAS;QACb,KAAKjI,mBAAL,CAAyBkB,IAAzB,CAA8B+G,IAA9B;MACD,CAHyC;MAI1CjH,KAAK,EAAGA,KAAD,IAAU;QACfF,OAAO,CAACE,KAAR,CAAc,kCAAd,EAAkDA,KAAlD;MACD;IANyC,CAA5C;EAQD;;AA3xBqB;;;mBAAX/B,aAAW6J;AAAA;;;SAAX7J;EAAW8J,SAAX9J,WAAW;EAAA+J,YAFV", "names": ["BehaviorSubject", "map", "ApiService", "Message", "Group", "User", "SecurityInfo", "ChatService", "constructor", "apiService", "socketService", "notificationService", "userSubject", "asObservable", "messagesSubject", "onlineUsersSubject", "groupsSubject", "currentGroupSubject", "replyToSubject", "loadingSubject", "isAdminSubject", "showAdminPanelSubject", "securityInfoSubject", "editingMessageSubject", "isConnected$", "typingUsersSubject", "user$", "pipe", "user", "setupSocketListeners", "setupMessageRefresh", "window", "manualLoadGroups", "debugChatState", "debugState", "debugScrollState", "console", "log", "error", "on", "next", "userGroups", "length", "name", "loadRecentMessages", "id", "messages", "value", "groupId", "groups", "group", "find", "g", "message", "currentMessages", "currentUser", "currentGroup", "existingMessage", "msg", "text", "username", "Math", "abs", "Date", "timestamp", "getTime", "startsWith", "optimisticIndex", "findIndex", "updatedMessages", "sortMessagesByTimestamp", "document", "hidden", "showMessageNotification", "messageId", "reactions", "users", "isTyping", "currentTypingUsers", "includes", "filter", "newText", "updatedAt", "updated_at", "messageRefreshInterval", "clearInterval", "login", "password", "inviteCode", "userData", "loginUser", "undefined", "to<PERSON>romise", "requiresPassword", "Error", "connect", "isAdmin", "securityInfo", "setTimeout", "loadUserGroups", "isLoadingMessages", "now", "lastMessageLoadTime", "MESSAGE_LOAD_THROTTLE", "getMessages", "isLoadingGroups", "getUserGroups", "isConnectedSubject", "sort", "a", "b", "timeA", "timeB", "replaceOptimisticMessage", "tempId", "realMessage", "removeOptimisticMessage", "messageExists", "some", "joinGroup", "sendMessage", "replyToId", "trim", "optimisticMessage", "random", "toString", "substr", "toISOString", "replyTo", "sendMessageWithRetry", "retryCount", "maxRetries", "retry<PERSON><PERSON><PERSON>", "pow", "isConnected", "response", "emitWithAck", "success", "sendMessageViaHttpFallback", "subscribe", "showMessageError", "startTyping", "sendTypingIndicator", "typingTimeout", "clearTimeout", "stopTyping", "replyToMessage", "cancelReply", "addReaction", "emoji", "removeReaction", "data", "updateMessage", "updatedMessage", "deleteMessage", "emit", "showAdminPanel", "hideAdminPanel", "startEditingMessage", "cancelEditingMessage", "logout", "disconnect", "refreshSecurityInfo", "getSecurityInfo", "i0", "factory", "providedIn"], "sourceRoot": "", "sources": ["R:\\chateye\\FrontendAngular\\src\\app\\services\\chat.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable, combineLatest } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\nimport { ApiService, Message, Group, User, SecurityInfo, LoginResponse } from './api.service';\r\nimport { SocketService } from './socket.service';\r\nimport { NotificationService } from './notification.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ChatService {\r\n  private userSubject = new BehaviorSubject<string | null>(null);\r\n  private messagesSubject = new BehaviorSubject<Message[]>([]);\r\n  private onlineUsersSubject = new BehaviorSubject<User[]>([]);\r\n  private groupsSubject = new BehaviorSubject<Group[]>([]);\r\n  private currentGroupSubject = new BehaviorSubject<Group | null>(null);\r\n  private replyToSubject = new BehaviorSubject<Message | null>(null);\r\n  private loadingSubject = new BehaviorSubject<boolean>(false);\r\n  private isAdminSubject = new BehaviorSubject<boolean>(false);\r\n  private showAdminPanelSubject = new BehaviorSubject<boolean>(false);\r\n  private securityInfoSubject = new BehaviorSubject<SecurityInfo | null>(null);\r\n  private editingMessageSubject = new BehaviorSubject<Message | null>(null);\r\n\r\n  // Public observables\r\n  public user$ = this.userSubject.asObservable();\r\n  public messages$ = this.messagesSubject.asObservable();\r\n  public onlineUsers$ = this.onlineUsersSubject.asObservable();\r\n  public groups$ = this.groupsSubject.asObservable();\r\n  public currentGroup$ = this.currentGroupSubject.asObservable();\r\n  public replyTo$ = this.replyToSubject.asObservable();\r\n  public loading$ = this.loadingSubject.asObservable();\r\n  public isAdmin$ = this.isAdminSubject.asObservable();\r\n  public showAdminPanel$ = this.showAdminPanelSubject.asObservable();\r\n  public securityInfo$ = this.securityInfoSubject.asObservable();\r\n  public editingMessage$ = this.editingMessageSubject.asObservable();\r\n  public connected$ = this.socketService.isConnected$;\r\n\r\n  // Typing indicators\r\n  private typingUsersSubject = new BehaviorSubject<string[]>([]);\r\n  public typingUsers$ = this.typingUsersSubject.asObservable();\r\n  private typingTimeout: any = null;\r\n\r\n  // Computed observables\r\n  public isLoggedIn$ = this.user$.pipe(map(user => !!user))\r\n\r\n  private messageRefreshInterval: any;\r\n  private isLoadingGroups = false;\r\n  private isLoadingMessages = false;\r\n  private lastMessageLoadTime = 0;\r\n  private readonly MESSAGE_LOAD_THROTTLE = 1000; // 1 second throttle\r\n\r\n  constructor(\r\n    private apiService: ApiService,\r\n    private socketService: SocketService,\r\n    private notificationService: NotificationService\r\n  ) {\r\n    try {\r\n      this.setupSocketListeners();\r\n      this.setupMessageRefresh();\r\n      \r\n      // Expose manual group loading globally for debugging\r\n      (window as any).manualLoadGroups = () => this.manualLoadGroups();\r\n      (window as any).debugChatState = () => this.debugState();\r\n      (window as any).debugScrollState = () => {\r\n        // This will be called from the component\r\n        console.log('Use debugScrollState() from the message list component');\r\n      };\r\n    } catch (error) {\r\n      console.error('Error initializing ChatService:', error);\r\n    }\r\n  }\r\n\r\n  private setupSocketListeners(): void {\r\n    this.socketService.on('connect', () => {\r\n      console.log('Connected to server');\r\n      // Clear any loading states when connected\r\n      this.loadingSubject.next(false);\r\n    });\r\n\r\n    this.socketService.on('disconnect', () => {\r\n      console.log('Disconnected from server');\r\n      // Don't set loading to true on disconnect to avoid UI freeze\r\n    });\r\n\r\n    this.socketService.on('connect_error', (error) => {\r\n      console.error('Socket connection error:', error);\r\n      this.loadingSubject.next(false);\r\n    });\r\n\r\n    this.socketService.on('userGroups', (userGroups: Group[]) => {\r\n      console.log('Received user groups:', userGroups?.length || 0, 'groups');\r\n      this.groupsSubject.next(userGroups || []);\r\n      if (userGroups && userGroups.length > 0) {\r\n        console.log('Auto-selecting first group:', userGroups[0].name);\r\n        this.currentGroupSubject.next(userGroups[0]);\r\n        // Load messages for the selected group\r\n        this.loadRecentMessages(userGroups[0].id);\r\n      } else {\r\n        console.log('No groups available for user');\r\n        // Clear current group and messages if no groups\r\n        this.currentGroupSubject.next(null);\r\n        this.messagesSubject.next([]);\r\n      }\r\n    });\r\n\r\n    this.socketService.on('recentMessages', (messages: Message[]) => {\r\n      console.log('Received recent messages:', messages.length);\r\n      console.log('Recent messages data:', messages);\r\n      this.messagesSubject.next(messages || []);\r\n      console.log('Recent messages subject updated, current value:', this.messagesSubject.value.length);\r\n    });\r\n\r\n    this.socketService.on('groupJoined', ({ groupId }: { groupId: string }) => {\r\n      console.log('Joined group:', groupId);\r\n      const groups = this.groupsSubject.value;\r\n      const group = groups.find(g => g.id === groupId);\r\n      if (group) {\r\n        this.currentGroupSubject.next(group);\r\n      }\r\n    });\r\n\r\n    this.socketService.on('newMessage', (message: Message) => {\r\n      console.log('📨 New message received:', message);\r\n\r\n      const currentMessages = this.messagesSubject.value;\r\n      const currentUser = this.userSubject.value;\r\n      const currentGroup = this.currentGroupSubject.value;\r\n\r\n      // Only process messages for the current group to avoid confusion\r\n      if (currentGroup && message.groupId !== currentGroup.id) {\r\n        console.log('🚫 Ignoring message from different group:', message.groupId, 'vs current:', currentGroup.id);\r\n        return;\r\n      }\r\n\r\n      // Check for duplicate messages to prevent double-display\r\n      const existingMessage = currentMessages.find(msg =>\r\n        msg.id === message.id ||\r\n        (msg.text === message.text &&\r\n         msg.username === message.username &&\r\n         Math.abs(new Date(msg.timestamp).getTime() - new Date(message.timestamp).getTime()) < 5000)\r\n      );\r\n\r\n      if (existingMessage && !existingMessage.id.startsWith('temp_')) {\r\n        console.log('🔄 Duplicate message detected, ignoring:', message);\r\n        return;\r\n      }\r\n\r\n      // Check if this is our own message (to replace optimistic message)\r\n      if (message.username === currentUser) {\r\n        // Find and replace optimistic message with real message\r\n        const optimisticIndex = currentMessages.findIndex(msg =>\r\n          msg.id.startsWith('temp_') &&\r\n          msg.text === message.text &&\r\n          msg.username === currentUser &&\r\n          msg.groupId === message.groupId\r\n        );\r\n\r\n        if (optimisticIndex !== -1) {\r\n          // Replace optimistic message\r\n          const updatedMessages = [...currentMessages];\r\n          updatedMessages[optimisticIndex] = message;\r\n          this.messagesSubject.next(this.sortMessagesByTimestamp(updatedMessages));\r\n          console.log('✅ Replaced optimistic message with real message:', message);\r\n        } else {\r\n          // Add new message if no optimistic message found (e.g., from another tab)\r\n          const updatedMessages = [...currentMessages, message];\r\n          this.messagesSubject.next(this.sortMessagesByTimestamp(updatedMessages));\r\n          console.log('➕ Added own message from socket (no optimistic message found):', message);\r\n        }\r\n      } else {\r\n        // Add message from other users\r\n        const updatedMessages = [...currentMessages, message];\r\n        this.messagesSubject.next(this.sortMessagesByTimestamp(updatedMessages));\r\n        console.log('👥 Added message from other user:', message);\r\n      }\r\n\r\n      // Show notification if not current user and window not focused\r\n      if (message.username !== currentUser && document.hidden) {\r\n        this.notificationService.showMessageNotification(message.username, message.text);\r\n      }\r\n    });\r\n\r\n    this.socketService.on('reactionUpdate', ({ messageId, reactions }: { messageId: string; reactions: any[] }) => {\r\n      console.log('Reaction update:', messageId, reactions);\r\n      const currentMessages = this.messagesSubject.value;\r\n      this.messagesSubject.next(\r\n        currentMessages.map(msg => \r\n          msg.id === messageId \r\n            ? { ...msg, reactions }\r\n            : msg\r\n        )\r\n      );\r\n    });\r\n\r\n    this.socketService.on('onlineUsersUpdate', (users: User[]) => {\r\n      console.log('Online users updated:', users);\r\n      this.onlineUsersSubject.next(users || []);\r\n    });\r\n\r\n    this.socketService.on('userJoined', ({ username }: { username: string }) => {\r\n      console.log('User joined:', username);\r\n      // Online users will be updated via onlineUsersUpdate event\r\n    });\r\n\r\n    this.socketService.on('userLeft', ({ username }: { username: string }) => {\r\n      console.log('User left:', username);\r\n      // Online users will be updated via onlineUsersUpdate event\r\n    });\r\n\r\n    // Handle typing indicators\r\n    this.socketService.on('userTyping', ({ username, groupId, isTyping }: { username: string; groupId: string; isTyping: boolean }) => {\r\n      console.log(`⌨️ ${username} is ${isTyping ? 'typing' : 'stopped typing'} in group ${groupId}`);\r\n\r\n      const currentGroup = this.currentGroupSubject.value;\r\n      if (currentGroup && groupId === currentGroup.id) {\r\n        const currentTypingUsers = this.typingUsersSubject.value;\r\n\r\n        if (isTyping) {\r\n          // Add user to typing list if not already there\r\n          if (!currentTypingUsers.includes(username)) {\r\n            this.typingUsersSubject.next([...currentTypingUsers, username]);\r\n          }\r\n        } else {\r\n          // Remove user from typing list\r\n          this.typingUsersSubject.next(currentTypingUsers.filter(user => user !== username));\r\n        }\r\n      }\r\n    });\r\n\r\n    this.socketService.on('messageUpdated', ({ messageId, newText, updatedAt }: { messageId: string; newText: string; updatedAt: string }) => {\r\n      console.log('Message updated:', messageId, newText);\r\n      const currentMessages = this.messagesSubject.value;\r\n      this.messagesSubject.next(\r\n        currentMessages.map(msg => \r\n          msg.id === messageId \r\n            ? { ...msg, text: newText, updated_at: updatedAt }\r\n            : msg\r\n        )\r\n      );\r\n    });\r\n\r\n    this.socketService.on('messageDeleted', ({ messageId }: { messageId: string }) => {\r\n      console.log('Message deleted:', messageId);\r\n      const currentMessages = this.messagesSubject.value;\r\n      this.messagesSubject.next(\r\n        currentMessages.filter(msg => msg.id !== messageId)\r\n      );\r\n    });\r\n\r\n    this.socketService.on('error', (error: any) => {\r\n      console.error('Socket error:', error);\r\n    });\r\n  }\r\n\r\n  private setupMessageRefresh(): void {\r\n    // Clear any existing interval first\r\n    if (this.messageRefreshInterval) {\r\n      clearInterval(this.messageRefreshInterval);\r\n    }\r\n    \r\n    // Temporarily disable message refresh to prevent browser freezing\r\n    // TODO: Re-enable once socket connection issues are resolved\r\n    /*\r\n    this.messageRefreshInterval = setInterval(() => {\r\n      try {\r\n        if (!this.socketService.isConnectedSubject.value) {\r\n          const currentGroup = this.currentGroupSubject.value;\r\n          if (currentGroup) {\r\n            console.log('Refreshing messages via HTTP API');\r\n            this.loadRecentMessages(currentGroup.id);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Error in message refresh interval:', error);\r\n        // Clear the interval if there's an error to prevent crashes\r\n        if (this.messageRefreshInterval) {\r\n          clearInterval(this.messageRefreshInterval);\r\n          this.messageRefreshInterval = null;\r\n        }\r\n      }\r\n    }, 5000);\r\n    */\r\n  }\r\n\r\n  async login(username: string, password?: string, inviteCode: string | null = null): Promise<void> {\r\n    try {\r\n      this.loadingSubject.next(true);\r\n      console.log('Starting login process for:', username);\r\n      \r\n      const userData = await this.apiService.loginUser(username, password, inviteCode || undefined).toPromise();\r\n      console.log('Login API response:', userData);\r\n      \r\n      // Check if password is required\r\n      if (userData?.requiresPassword) {\r\n        throw new Error('Password required for this user');\r\n      }\r\n      \r\n      // Connect to socket with auth data\r\n      console.log('Connecting to socket...');\r\n      this.socketService.connect(username, password, inviteCode);\r\n      \r\n      this.userSubject.next(username);\r\n      this.isAdminSubject.next(userData?.isAdmin || false);\r\n      this.securityInfoSubject.next(userData?.securityInfo || null);\r\n      \r\n      console.log('Login completed successfully for:', username, '(Admin:', userData?.isAdmin || false, ')');\r\n      \r\n      // Wait for socket connection before loading groups\r\n      // The socket will emit 'userGroups' event which will handle group loading\r\n      // This prevents race conditions between API and socket calls\r\n      \r\n      // Add fallback: if socket doesn't load groups within 3 seconds, try API\r\n      setTimeout(() => {\r\n        if (this.groupsSubject.value.length === 0) {\r\n          console.log('Socket groups not loaded, trying API fallback...');\r\n          this.loadUserGroups(username);\r\n        }\r\n      }, 3000);\r\n      \r\n    } catch (error) {\r\n      console.error('Login failed:', error);\r\n      throw error;\r\n    } finally {\r\n      this.loadingSubject.next(false);\r\n    }\r\n  }\r\n\r\n  async loadRecentMessages(groupId: string): Promise<void> {\r\n    // Safety guard to prevent infinite loops\r\n    if (this.isLoadingMessages) {\r\n      console.log('Already loading messages, skipping...');\r\n      return;\r\n    }\r\n    \r\n    // Throttle message loading to prevent excessive API calls\r\n    const now = Date.now();\r\n    if (now - this.lastMessageLoadTime < this.MESSAGE_LOAD_THROTTLE) {\r\n      console.log('Throttling message load request');\r\n      return;\r\n    }\r\n    this.lastMessageLoadTime = now;\r\n    \r\n    this.isLoadingMessages = true;\r\n    try {\r\n      console.log('Loading recent messages for group:', groupId);\r\n      const messages = await this.apiService.getMessages(groupId, 50).toPromise();\r\n      console.log('Loaded messages:', messages?.length || 0);\r\n      console.log('Messages data:', messages);\r\n      \r\n      // For group switching, replace messages with only the current group's messages\r\n      // This ensures we only show messages for the current group\r\n      this.messagesSubject.next(messages || []);\r\n      console.log('Messages subject updated, current value:', this.messagesSubject.value.length);\r\n    } catch (error) {\r\n      console.error('Failed to load messages:', error);\r\n      // Set empty array to prevent UI from showing stale data\r\n      this.messagesSubject.next([]);\r\n    } finally {\r\n      this.isLoadingMessages = false;\r\n    }\r\n  }\r\n\r\n  async loadUserGroups(username: string): Promise<void> {\r\n    // Safety guard to prevent infinite loops\r\n    if (this.isLoadingGroups) {\r\n      console.log('Already loading groups, skipping...');\r\n      return;\r\n    }\r\n    \r\n    this.isLoadingGroups = true;\r\n    try {\r\n      console.log('Loading user groups for:', username);\r\n      const groups = await this.apiService.getUserGroups(username).toPromise();\r\n      console.log('Loaded user groups:', groups);\r\n      this.groupsSubject.next(groups || []);\r\n      if (groups && groups.length > 0) {\r\n        console.log('Auto-selecting first group:', groups[0]);\r\n        this.currentGroupSubject.next(groups[0]);\r\n        this.loadRecentMessages(groups[0].id);\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to load user groups:', error);\r\n    } finally {\r\n      this.isLoadingGroups = false;\r\n    }\r\n  }\r\n\r\n  // Safe method to manually load groups (call this from browser console)\r\n  async manualLoadGroups(): Promise<void> {\r\n    const currentUser = this.userSubject.value;\r\n    if (currentUser) {\r\n      console.log('Manually loading groups for:', currentUser);\r\n      await this.loadUserGroups(currentUser);\r\n    } else {\r\n      console.error('No user logged in');\r\n    }\r\n  }\r\n\r\n  // Debug method to check current state\r\n  debugState(): void {\r\n    console.log('=== Chat Service Debug State ===');\r\n    console.log('User:', this.userSubject.value);\r\n    console.log('Groups:', this.groupsSubject.value);\r\n    console.log('Current Group:', this.currentGroupSubject.value);\r\n    console.log('Socket Connected:', this.socketService.isConnectedSubject.value);\r\n    console.log('Loading Groups:', this.isLoadingGroups);\r\n    console.log('Loading Messages:', this.isLoadingMessages);\r\n    console.log('================================');\r\n  }\r\n\r\n  // Helper method to sort messages by timestamp\r\n  private sortMessagesByTimestamp(messages: Message[]): Message[] {\r\n    return messages.sort((a, b) => {\r\n      const timeA = new Date(a.timestamp).getTime();\r\n      const timeB = new Date(b.timestamp).getTime();\r\n      return timeA - timeB;\r\n    });\r\n  }\r\n\r\n  // Helper method to replace optimistic message with real message\r\n  private replaceOptimisticMessage(tempId: string, realMessage: Message): void {\r\n    const currentMessages = this.messagesSubject.value;\r\n    const updatedMessages = currentMessages.map(msg =>\r\n      msg.id === tempId ? realMessage : msg\r\n    );\r\n    this.messagesSubject.next(this.sortMessagesByTimestamp(updatedMessages));\r\n    console.log('✅ Replaced optimistic message with real message:', realMessage);\r\n  }\r\n\r\n  // Helper method to remove optimistic message (on error)\r\n  private removeOptimisticMessage(tempId: string): void {\r\n    const currentMessages = this.messagesSubject.value;\r\n    const updatedMessages = currentMessages.filter(msg => msg.id !== tempId);\r\n    this.messagesSubject.next(updatedMessages);\r\n    console.log('❌ Removed optimistic message due to error:', tempId);\r\n  }\r\n\r\n  // Helper method to check if message already exists\r\n  private messageExists(message: Message, messages: Message[]): boolean {\r\n    return messages.some(msg =>\r\n      msg.id === message.id ||\r\n      (msg.text === message.text &&\r\n       msg.username === message.username &&\r\n       Math.abs(new Date(msg.timestamp).getTime() - new Date(message.timestamp).getTime()) < 5000)\r\n    );\r\n  }\r\n\r\n  joinGroup(groupId: string): void {\r\n    const currentUser = this.userSubject.value;\r\n    if (!groupId || !currentUser) {\r\n      console.error('Cannot join group - missing groupId or user');\r\n      return;\r\n    }\r\n    \r\n    console.log('Joining group:', groupId);\r\n    \r\n    // Check if socket is connected before joining\r\n    if (!this.socketService.isConnectedSubject.value) {\r\n      console.error('Cannot join group - socket not connected');\r\n      return;\r\n    }\r\n    \r\n    this.socketService.joinGroup(groupId);\r\n    const groups = this.groupsSubject.value;\r\n    const group = groups.find(g => g.id === groupId);\r\n    if (group) {\r\n      this.currentGroupSubject.next(group);\r\n      // Load recent messages for the group\r\n      this.loadRecentMessages(groupId);\r\n    } else {\r\n      console.error('Group not found in user groups:', groupId);\r\n    }\r\n  }\r\n\r\n  sendMessage(text: string, replyToId: string | null = null): void {\r\n    const currentUser = this.userSubject.value;\r\n    const currentGroup = this.currentGroupSubject.value;\r\n    \r\n    if (!text.trim()) {\r\n      console.error('Cannot send message - empty text');\r\n      return;\r\n    }\r\n    \r\n    if (!currentUser) {\r\n      console.error('Cannot send message - user not logged in');\r\n      return;\r\n    }\r\n    \r\n    if (!currentGroup) {\r\n      console.error('Cannot send message - no group selected. Available groups:', this.groupsSubject.value);\r\n      // Try to auto-select the first available group\r\n      const groups = this.groupsSubject.value;\r\n      if (groups && groups.length > 0) {\r\n        console.log('Auto-selecting first available group:', groups[0]);\r\n        this.currentGroupSubject.next(groups[0]);\r\n        this.loadRecentMessages(groups[0].id);\r\n        // Retry sending the message - DISABLED to prevent infinite loops\r\n        // setTimeout(() => this.sendMessage(text, replyToId), 100);\r\n        return;\r\n      } else {\r\n        console.error('No groups available for user');\r\n        return;\r\n      }\r\n    }\r\n\r\n    // Create optimistic message for immediate display\r\n    const optimisticMessage: Message = {\r\n      id: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`, // Unique temporary ID\r\n      text: text.trim(),\r\n      username: currentUser,\r\n      timestamp: new Date().toISOString(),\r\n      groupId: currentGroup.id,\r\n      replyTo: replyToId || undefined,\r\n      reactions: []\r\n    };\r\n\r\n    // Add optimistic message to local state immediately\r\n    const currentMessages = this.messagesSubject.value;\r\n    const updatedMessages = [...currentMessages, optimisticMessage];\r\n    this.messagesSubject.next(updatedMessages);\r\n    console.log('📤 Added optimistic message:', optimisticMessage);\r\n\r\n    // Send message with acknowledgment and retry logic\r\n    this.sendMessageWithRetry(text, currentGroup.id, replyToId, optimisticMessage);\r\n  }\r\n\r\n  private async sendMessageWithRetry(\r\n    text: string,\r\n    groupId: string,\r\n    replyToId: string | null,\r\n    optimisticMessage: Message,\r\n    retryCount: number = 0\r\n  ): Promise<void> {\r\n    const maxRetries = 3;\r\n    const retryDelay = 1000 * Math.pow(2, retryCount); // Exponential backoff\r\n\r\n    try {\r\n      if (this.socketService.isConnected()) {\r\n        // Try sending via socket with acknowledgment\r\n        const response = await this.socketService.emitWithAck('sendMessage', {\r\n          text,\r\n          groupId,\r\n          replyTo: replyToId,\r\n          messageId: optimisticMessage.id\r\n        });\r\n\r\n        if (response.success) {\r\n          console.log('✅ Message sent successfully via socket:', response);\r\n          // Replace optimistic message with real message\r\n          this.replaceOptimisticMessage(optimisticMessage.id, response.message);\r\n          return;\r\n        } else {\r\n          throw new Error(response.error || 'Unknown error');\r\n        }\r\n      } else {\r\n        throw new Error('Socket not connected');\r\n      }\r\n    } catch (error) {\r\n      console.error(`❌ Failed to send message (attempt ${retryCount + 1}):`, error);\r\n\r\n      if (retryCount < maxRetries) {\r\n        // Retry with exponential backoff\r\n        console.log(`🔄 Retrying in ${retryDelay}ms...`);\r\n        setTimeout(() => {\r\n          this.sendMessageWithRetry(text, groupId, replyToId, optimisticMessage, retryCount + 1);\r\n        }, retryDelay);\r\n      } else {\r\n        // All retries failed, try HTTP API as final fallback\r\n        console.log('🌐 All socket retries failed, trying HTTP API fallback');\r\n        this.sendMessageViaHttpFallback(text, groupId, replyToId, optimisticMessage);\r\n      }\r\n    }\r\n  }\r\n\r\n  private sendMessageViaHttpFallback(\r\n    text: string,\r\n    groupId: string,\r\n    replyToId: string | null,\r\n    optimisticMessage: Message\r\n  ): void {\r\n    const currentUser = this.userSubject.value;\r\n    if (!currentUser) {\r\n      this.removeOptimisticMessage(optimisticMessage.id);\r\n      return;\r\n    }\r\n\r\n    this.apiService.sendMessage(text, currentUser, groupId, replyToId).subscribe({\r\n      next: (response) => {\r\n        console.log('✅ Message sent via HTTP API:', response);\r\n        this.replaceOptimisticMessage(optimisticMessage.id, response);\r\n      },\r\n      error: (error) => {\r\n        console.error('❌ Failed to send message via HTTP API:', error);\r\n        this.removeOptimisticMessage(optimisticMessage.id);\r\n        // Show error notification to user\r\n        this.showMessageError('Failed to send message. Please try again.');\r\n      }\r\n    });\r\n  }\r\n\r\n  private showMessageError(message: string): void {\r\n    // You can implement a toast notification service here\r\n    console.error('💬 Message Error:', message);\r\n    // For now, just log the error. In a real app, you'd show a toast/snackbar\r\n  }\r\n\r\n  // Typing indicator methods\r\n  startTyping(): void {\r\n    const currentGroup = this.currentGroupSubject.value;\r\n    if (currentGroup && this.socketService.isConnected()) {\r\n      this.socketService.sendTypingIndicator(currentGroup.id, true);\r\n\r\n      // Clear existing timeout\r\n      if (this.typingTimeout) {\r\n        clearTimeout(this.typingTimeout);\r\n      }\r\n\r\n      // Set timeout to stop typing after 3 seconds of inactivity\r\n      this.typingTimeout = setTimeout(() => {\r\n        this.stopTyping();\r\n      }, 3000);\r\n    }\r\n  }\r\n\r\n  stopTyping(): void {\r\n    const currentGroup = this.currentGroupSubject.value;\r\n    if (currentGroup && this.socketService.isConnected()) {\r\n      this.socketService.sendTypingIndicator(currentGroup.id, false);\r\n\r\n      // Clear timeout\r\n      if (this.typingTimeout) {\r\n        clearTimeout(this.typingTimeout);\r\n        this.typingTimeout = null;\r\n      }\r\n    }\r\n  }\r\n\r\n  replyToMessage(message: Message): void {\r\n    this.replyToSubject.next(message);\r\n  }\r\n\r\n  cancelReply(): void {\r\n    this.replyToSubject.next(null);\r\n  }\r\n\r\n  async addReaction(messageId: string, emoji: string): Promise<void> {\r\n    try {\r\n      if (this.socketService.isConnected()) {\r\n        // Try with acknowledgment first\r\n        const response = await this.socketService.emitWithAck('addReaction', {\r\n          messageId,\r\n          emoji\r\n        });\r\n\r\n        if (response.success) {\r\n          console.log('✅ Reaction added successfully:', response);\r\n        } else {\r\n          throw new Error(response.error || 'Failed to add reaction');\r\n        }\r\n      } else {\r\n        // Fallback to HTTP API\r\n        const currentUser = this.userSubject.value;\r\n        if (currentUser) {\r\n          this.apiService.addReaction(messageId, emoji, currentUser).subscribe({\r\n            next: (response) => {\r\n              console.log('✅ Reaction added via HTTP API:', response);\r\n            },\r\n            error: (error) => {\r\n              console.error('❌ Failed to add reaction via HTTP API:', error);\r\n            }\r\n          });\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Failed to add reaction:', error);\r\n      // Fallback to old method\r\n      this.socketService.addReaction(messageId, emoji);\r\n    }\r\n  }\r\n\r\n  removeReaction(data: { messageId: string; emoji?: string }): void {\r\n    this.socketService.removeReaction(data);\r\n  }\r\n\r\n  async updateMessage(messageId: string, newText: string): Promise<void> {\r\n    const currentUser = this.userSubject.value;\r\n    if (!currentUser) {\r\n      console.error('Cannot update message - user not logged in');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Update the message in the local state immediately for better UX\r\n      const currentMessages = this.messagesSubject.value;\r\n      this.messagesSubject.next(\r\n        currentMessages.map(msg => \r\n          msg.id === messageId \r\n            ? { ...msg, text: newText, updated_at: new Date().toISOString() }\r\n            : msg\r\n        )\r\n      );\r\n\r\n      // Emit socket event for real-time updates\r\n      if (this.socketService.isConnectedSubject.value) {\r\n        this.socketService.updateMessage(messageId, newText);\r\n      } else {\r\n        // Fallback to HTTP API if socket not connected\r\n        const updatedMessage = await this.apiService.updateMessage(messageId, newText, currentUser).toPromise();\r\n        this.messagesSubject.next(\r\n          currentMessages.map(msg => \r\n            msg.id === messageId \r\n              ? { ...msg, text: newText, updated_at: updatedMessage.updated_at }\r\n              : msg\r\n          )\r\n        );\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to update message:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async deleteMessage(messageId: string): Promise<void> {\r\n    const currentUser = this.userSubject.value;\r\n    if (!currentUser) {\r\n      console.error('Cannot delete message - user not logged in');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Remove the message from local state immediately for better UX\r\n      const currentMessages = this.messagesSubject.value;\r\n      this.messagesSubject.next(\r\n        currentMessages.filter(msg => msg.id !== messageId)\r\n      );\r\n\r\n      // Emit socket event for real-time updates\r\n      if (this.socketService.isConnectedSubject.value) {\r\n        this.socketService.emit('messageDeleted', { messageId });\r\n      } else {\r\n        // Fallback to HTTP API if socket not connected\r\n        await this.apiService.deleteMessage(messageId, currentUser).toPromise();\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to delete message:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  showAdminPanel(): void {\r\n    this.showAdminPanelSubject.next(true);\r\n  }\r\n\r\n  hideAdminPanel(): void {\r\n    this.showAdminPanelSubject.next(false);\r\n  }\r\n\r\n  startEditingMessage(message: Message): void {\r\n    this.editingMessageSubject.next(message);\r\n  }\r\n\r\n  cancelEditingMessage(): void {\r\n    this.editingMessageSubject.next(null);\r\n  }\r\n\r\n  logout(): void {\r\n    try {\r\n      // Disconnect socket first\r\n      this.socketService.disconnect();\r\n      \r\n      // Clear interval\r\n      if (this.messageRefreshInterval) {\r\n        clearInterval(this.messageRefreshInterval);\r\n        this.messageRefreshInterval = null;\r\n      }\r\n      \r\n      // Reset all subjects\r\n      this.userSubject.next(null);\r\n      this.messagesSubject.next([]);\r\n      this.onlineUsersSubject.next([]);\r\n      this.groupsSubject.next([]);\r\n      this.currentGroupSubject.next(null);\r\n      this.replyToSubject.next(null);\r\n      this.loadingSubject.next(false);\r\n      this.isAdminSubject.next(false);\r\n      this.showAdminPanelSubject.next(false);\r\n      this.securityInfoSubject.next(null);\r\n      this.editingMessageSubject.next(null);\r\n      \r\n      console.log('Logout completed successfully');\r\n    } catch (error) {\r\n      console.error('Error during logout:', error);\r\n    }\r\n  }\r\n\r\n  // Method to refresh security info for all components\r\n  refreshSecurityInfo(): void {\r\n    this.apiService.getSecurityInfo().subscribe({\r\n      next: (data) => {\r\n        this.securityInfoSubject.next(data);\r\n      },\r\n      error: (error) => {\r\n        console.error('Failed to refresh security info:', error);\r\n      }\r\n    });\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module"}