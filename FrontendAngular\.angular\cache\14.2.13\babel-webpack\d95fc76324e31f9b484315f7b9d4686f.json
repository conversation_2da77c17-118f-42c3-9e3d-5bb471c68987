{"ast": null, "code": "import { PacketType } from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */\n\nconst RESERVED_EVENTS = Object.freeze({\n  connect: 1,\n  connect_error: 1,\n  disconnect: 1,\n  disconnecting: 1,\n  // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n  newListener: 1,\n  removeListener: 1\n});\n/**\n * A Socket is the fundamental class for interacting with the server.\n *\n * A Socket belongs to a certain Namespace (by default /) and uses an underlying {@link Manager} to communicate.\n *\n * @example\n * const socket = io();\n *\n * socket.on(\"connect\", () => {\n *   console.log(\"connected\");\n * });\n *\n * // send an event to the server\n * socket.emit(\"foo\", \"bar\");\n *\n * socket.on(\"foobar\", () => {\n *   // an event was received from the server\n * });\n *\n * // upon disconnection\n * socket.on(\"disconnect\", (reason) => {\n *   console.log(`disconnected due to ${reason}`);\n * });\n */\n\nexport class Socket extends Emitter {\n  /**\n   * `Socket` constructor.\n   */\n  constructor(io, nsp, opts) {\n    super();\n    /**\n     * Whether the socket is currently connected to the server.\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"connect\", () => {\n     *   console.log(socket.connected); // true\n     * });\n     *\n     * socket.on(\"disconnect\", () => {\n     *   console.log(socket.connected); // false\n     * });\n     */\n\n    this.connected = false;\n    /**\n     * Whether the connection state was recovered after a temporary disconnection. In that case, any missed packets will\n     * be transmitted by the server.\n     */\n\n    this.recovered = false;\n    /**\n     * Buffer for packets received before the CONNECT packet\n     */\n\n    this.receiveBuffer = [];\n    /**\n     * Buffer for packets that will be sent once the socket is connected\n     */\n\n    this.sendBuffer = [];\n    /**\n     * The queue of packets to be sent with retry in case of failure.\n     *\n     * Packets are sent one by one, each waiting for the server acknowledgement, in order to guarantee the delivery order.\n     * @private\n     */\n\n    this._queue = [];\n    /**\n     * A sequence to generate the ID of the {@link QueuedPacket}.\n     * @private\n     */\n\n    this._queueSeq = 0;\n    this.ids = 0;\n    /**\n     * A map containing acknowledgement handlers.\n     *\n     * The `withError` attribute is used to differentiate handlers that accept an error as first argument:\n     *\n     * - `socket.emit(\"test\", (err, value) => { ... })` with `ackTimeout` option\n     * - `socket.timeout(5000).emit(\"test\", (err, value) => { ... })`\n     * - `const value = await socket.emitWithAck(\"test\")`\n     *\n     * From those that don't:\n     *\n     * - `socket.emit(\"test\", (value) => { ... });`\n     *\n     * In the first case, the handlers will be called with an error when:\n     *\n     * - the timeout is reached\n     * - the socket gets disconnected\n     *\n     * In the second case, the handlers will be simply discarded upon disconnection, since the client will never receive\n     * an acknowledgement from the server.\n     *\n     * @private\n     */\n\n    this.acks = {};\n    this.flags = {};\n    this.io = io;\n    this.nsp = nsp;\n\n    if (opts && opts.auth) {\n      this.auth = opts.auth;\n    }\n\n    this._opts = Object.assign({}, opts);\n    if (this.io._autoConnect) this.open();\n  }\n  /**\n   * Whether the socket is currently disconnected\n   *\n   * @example\n   * const socket = io();\n   *\n   * socket.on(\"connect\", () => {\n   *   console.log(socket.disconnected); // false\n   * });\n   *\n   * socket.on(\"disconnect\", () => {\n   *   console.log(socket.disconnected); // true\n   * });\n   */\n\n\n  get disconnected() {\n    return !this.connected;\n  }\n  /**\n   * Subscribe to open, close and packet events\n   *\n   * @private\n   */\n\n\n  subEvents() {\n    if (this.subs) return;\n    const io = this.io;\n    this.subs = [on(io, \"open\", this.onopen.bind(this)), on(io, \"packet\", this.onpacket.bind(this)), on(io, \"error\", this.onerror.bind(this)), on(io, \"close\", this.onclose.bind(this))];\n  }\n  /**\n   * Whether the Socket will try to reconnect when its Manager connects or reconnects.\n   *\n   * @example\n   * const socket = io();\n   *\n   * console.log(socket.active); // true\n   *\n   * socket.on(\"disconnect\", (reason) => {\n   *   if (reason === \"io server disconnect\") {\n   *     // the disconnection was initiated by the server, you need to manually reconnect\n   *     console.log(socket.active); // false\n   *   }\n   *   // else the socket will automatically try to reconnect\n   *   console.log(socket.active); // true\n   * });\n   */\n\n\n  get active() {\n    return !!this.subs;\n  }\n  /**\n   * \"Opens\" the socket.\n   *\n   * @example\n   * const socket = io({\n   *   autoConnect: false\n   * });\n   *\n   * socket.connect();\n   */\n\n\n  connect() {\n    if (this.connected) return this;\n    this.subEvents();\n    if (!this.io[\"_reconnecting\"]) this.io.open(); // ensure open\n\n    if (\"open\" === this.io._readyState) this.onopen();\n    return this;\n  }\n  /**\n   * Alias for {@link connect()}.\n   */\n\n\n  open() {\n    return this.connect();\n  }\n  /**\n   * Sends a `message` event.\n   *\n   * This method mimics the WebSocket.send() method.\n   *\n   * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send\n   *\n   * @example\n   * socket.send(\"hello\");\n   *\n   * // this is equivalent to\n   * socket.emit(\"message\", \"hello\");\n   *\n   * @return self\n   */\n\n\n  send(...args) {\n    args.unshift(\"message\");\n    this.emit.apply(this, args);\n    return this;\n  }\n  /**\n   * Override `emit`.\n   * If the event is in `events`, it's emitted normally.\n   *\n   * @example\n   * socket.emit(\"hello\", \"world\");\n   *\n   * // all serializable datastructures are supported (no need to call JSON.stringify)\n   * socket.emit(\"hello\", 1, \"2\", { 3: [\"4\"], 5: Uint8Array.from([6]) });\n   *\n   * // with an acknowledgement from the server\n   * socket.emit(\"hello\", \"world\", (val) => {\n   *   // ...\n   * });\n   *\n   * @return self\n   */\n\n\n  emit(ev, ...args) {\n    var _a, _b, _c;\n\n    if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n      throw new Error('\"' + ev.toString() + '\" is a reserved event name');\n    }\n\n    args.unshift(ev);\n\n    if (this._opts.retries && !this.flags.fromQueue && !this.flags.volatile) {\n      this._addToQueue(args);\n\n      return this;\n    }\n\n    const packet = {\n      type: PacketType.EVENT,\n      data: args\n    };\n    packet.options = {};\n    packet.options.compress = this.flags.compress !== false; // event ack callback\n\n    if (\"function\" === typeof args[args.length - 1]) {\n      const id = this.ids++;\n      const ack = args.pop();\n\n      this._registerAckCallback(id, ack);\n\n      packet.id = id;\n    }\n\n    const isTransportWritable = (_b = (_a = this.io.engine) === null || _a === void 0 ? void 0 : _a.transport) === null || _b === void 0 ? void 0 : _b.writable;\n    const isConnected = this.connected && !((_c = this.io.engine) === null || _c === void 0 ? void 0 : _c._hasPingExpired());\n    const discardPacket = this.flags.volatile && !isTransportWritable;\n\n    if (discardPacket) {} else if (isConnected) {\n      this.notifyOutgoingListeners(packet);\n      this.packet(packet);\n    } else {\n      this.sendBuffer.push(packet);\n    }\n\n    this.flags = {};\n    return this;\n  }\n  /**\n   * @private\n   */\n\n\n  _registerAckCallback(id, ack) {\n    var _a;\n\n    const timeout = (_a = this.flags.timeout) !== null && _a !== void 0 ? _a : this._opts.ackTimeout;\n\n    if (timeout === undefined) {\n      this.acks[id] = ack;\n      return;\n    } // @ts-ignore\n\n\n    const timer = this.io.setTimeoutFn(() => {\n      delete this.acks[id];\n\n      for (let i = 0; i < this.sendBuffer.length; i++) {\n        if (this.sendBuffer[i].id === id) {\n          this.sendBuffer.splice(i, 1);\n        }\n      }\n\n      ack.call(this, new Error(\"operation has timed out\"));\n    }, timeout);\n\n    const fn = (...args) => {\n      // @ts-ignore\n      this.io.clearTimeoutFn(timer);\n      ack.apply(this, args);\n    };\n\n    fn.withError = true;\n    this.acks[id] = fn;\n  }\n  /**\n   * Emits an event and waits for an acknowledgement\n   *\n   * @example\n   * // without timeout\n   * const response = await socket.emitWithAck(\"hello\", \"world\");\n   *\n   * // with a specific timeout\n   * try {\n   *   const response = await socket.timeout(1000).emitWithAck(\"hello\", \"world\");\n   * } catch (err) {\n   *   // the server did not acknowledge the event in the given delay\n   * }\n   *\n   * @return a Promise that will be fulfilled when the server acknowledges the event\n   */\n\n\n  emitWithAck(ev, ...args) {\n    return new Promise((resolve, reject) => {\n      const fn = (arg1, arg2) => {\n        return arg1 ? reject(arg1) : resolve(arg2);\n      };\n\n      fn.withError = true;\n      args.push(fn);\n      this.emit(ev, ...args);\n    });\n  }\n  /**\n   * Add the packet to the queue.\n   * @param args\n   * @private\n   */\n\n\n  _addToQueue(args) {\n    let ack;\n\n    if (typeof args[args.length - 1] === \"function\") {\n      ack = args.pop();\n    }\n\n    const packet = {\n      id: this._queueSeq++,\n      tryCount: 0,\n      pending: false,\n      args,\n      flags: Object.assign({\n        fromQueue: true\n      }, this.flags)\n    };\n    args.push((err, ...responseArgs) => {\n      if (packet !== this._queue[0]) {\n        // the packet has already been acknowledged\n        return;\n      }\n\n      const hasError = err !== null;\n\n      if (hasError) {\n        if (packet.tryCount > this._opts.retries) {\n          this._queue.shift();\n\n          if (ack) {\n            ack(err);\n          }\n        }\n      } else {\n        this._queue.shift();\n\n        if (ack) {\n          ack(null, ...responseArgs);\n        }\n      }\n\n      packet.pending = false;\n      return this._drainQueue();\n    });\n\n    this._queue.push(packet);\n\n    this._drainQueue();\n  }\n  /**\n   * Send the first packet of the queue, and wait for an acknowledgement from the server.\n   * @param force - whether to resend a packet that has not been acknowledged yet\n   *\n   * @private\n   */\n\n\n  _drainQueue(force = false) {\n    if (!this.connected || this._queue.length === 0) {\n      return;\n    }\n\n    const packet = this._queue[0];\n\n    if (packet.pending && !force) {\n      return;\n    }\n\n    packet.pending = true;\n    packet.tryCount++;\n    this.flags = packet.flags;\n    this.emit.apply(this, packet.args);\n  }\n  /**\n   * Sends a packet.\n   *\n   * @param packet\n   * @private\n   */\n\n\n  packet(packet) {\n    packet.nsp = this.nsp;\n\n    this.io._packet(packet);\n  }\n  /**\n   * Called upon engine `open`.\n   *\n   * @private\n   */\n\n\n  onopen() {\n    if (typeof this.auth == \"function\") {\n      this.auth(data => {\n        this._sendConnectPacket(data);\n      });\n    } else {\n      this._sendConnectPacket(this.auth);\n    }\n  }\n  /**\n   * Sends a CONNECT packet to initiate the Socket.IO session.\n   *\n   * @param data\n   * @private\n   */\n\n\n  _sendConnectPacket(data) {\n    this.packet({\n      type: PacketType.CONNECT,\n      data: this._pid ? Object.assign({\n        pid: this._pid,\n        offset: this._lastOffset\n      }, data) : data\n    });\n  }\n  /**\n   * Called upon engine or manager `error`.\n   *\n   * @param err\n   * @private\n   */\n\n\n  onerror(err) {\n    if (!this.connected) {\n      this.emitReserved(\"connect_error\", err);\n    }\n  }\n  /**\n   * Called upon engine `close`.\n   *\n   * @param reason\n   * @param description\n   * @private\n   */\n\n\n  onclose(reason, description) {\n    this.connected = false;\n    delete this.id;\n    this.emitReserved(\"disconnect\", reason, description);\n\n    this._clearAcks();\n  }\n  /**\n   * Clears the acknowledgement handlers upon disconnection, since the client will never receive an acknowledgement from\n   * the server.\n   *\n   * @private\n   */\n\n\n  _clearAcks() {\n    Object.keys(this.acks).forEach(id => {\n      const isBuffered = this.sendBuffer.some(packet => String(packet.id) === id);\n\n      if (!isBuffered) {\n        // note: handlers that do not accept an error as first argument are ignored here\n        const ack = this.acks[id];\n        delete this.acks[id];\n\n        if (ack.withError) {\n          ack.call(this, new Error(\"socket has been disconnected\"));\n        }\n      }\n    });\n  }\n  /**\n   * Called with socket packet.\n   *\n   * @param packet\n   * @private\n   */\n\n\n  onpacket(packet) {\n    const sameNamespace = packet.nsp === this.nsp;\n    if (!sameNamespace) return;\n\n    switch (packet.type) {\n      case PacketType.CONNECT:\n        if (packet.data && packet.data.sid) {\n          this.onconnect(packet.data.sid, packet.data.pid);\n        } else {\n          this.emitReserved(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n        }\n\n        break;\n\n      case PacketType.EVENT:\n      case PacketType.BINARY_EVENT:\n        this.onevent(packet);\n        break;\n\n      case PacketType.ACK:\n      case PacketType.BINARY_ACK:\n        this.onack(packet);\n        break;\n\n      case PacketType.DISCONNECT:\n        this.ondisconnect();\n        break;\n\n      case PacketType.CONNECT_ERROR:\n        this.destroy();\n        const err = new Error(packet.data.message); // @ts-ignore\n\n        err.data = packet.data.data;\n        this.emitReserved(\"connect_error\", err);\n        break;\n    }\n  }\n  /**\n   * Called upon a server event.\n   *\n   * @param packet\n   * @private\n   */\n\n\n  onevent(packet) {\n    const args = packet.data || [];\n\n    if (null != packet.id) {\n      args.push(this.ack(packet.id));\n    }\n\n    if (this.connected) {\n      this.emitEvent(args);\n    } else {\n      this.receiveBuffer.push(Object.freeze(args));\n    }\n  }\n\n  emitEvent(args) {\n    if (this._anyListeners && this._anyListeners.length) {\n      const listeners = this._anyListeners.slice();\n\n      for (const listener of listeners) {\n        listener.apply(this, args);\n      }\n    }\n\n    super.emit.apply(this, args);\n\n    if (this._pid && args.length && typeof args[args.length - 1] === \"string\") {\n      this._lastOffset = args[args.length - 1];\n    }\n  }\n  /**\n   * Produces an ack callback to emit with an event.\n   *\n   * @private\n   */\n\n\n  ack(id) {\n    const self = this;\n    let sent = false;\n    return function (...args) {\n      // prevent double callbacks\n      if (sent) return;\n      sent = true;\n      self.packet({\n        type: PacketType.ACK,\n        id: id,\n        data: args\n      });\n    };\n  }\n  /**\n   * Called upon a server acknowledgement.\n   *\n   * @param packet\n   * @private\n   */\n\n\n  onack(packet) {\n    const ack = this.acks[packet.id];\n\n    if (typeof ack !== \"function\") {\n      return;\n    }\n\n    delete this.acks[packet.id]; // @ts-ignore FIXME ack is incorrectly inferred as 'never'\n\n    if (ack.withError) {\n      packet.data.unshift(null);\n    } // @ts-ignore\n\n\n    ack.apply(this, packet.data);\n  }\n  /**\n   * Called upon server connect.\n   *\n   * @private\n   */\n\n\n  onconnect(id, pid) {\n    this.id = id;\n    this.recovered = pid && this._pid === pid;\n    this._pid = pid; // defined only if connection state recovery is enabled\n\n    this.connected = true;\n    this.emitBuffered();\n    this.emitReserved(\"connect\");\n\n    this._drainQueue(true);\n  }\n  /**\n   * Emit buffered events (received and emitted).\n   *\n   * @private\n   */\n\n\n  emitBuffered() {\n    this.receiveBuffer.forEach(args => this.emitEvent(args));\n    this.receiveBuffer = [];\n    this.sendBuffer.forEach(packet => {\n      this.notifyOutgoingListeners(packet);\n      this.packet(packet);\n    });\n    this.sendBuffer = [];\n  }\n  /**\n   * Called upon server disconnect.\n   *\n   * @private\n   */\n\n\n  ondisconnect() {\n    this.destroy();\n    this.onclose(\"io server disconnect\");\n  }\n  /**\n   * Called upon forced client/server side disconnections,\n   * this method ensures the manager stops tracking us and\n   * that reconnections don't get triggered for this.\n   *\n   * @private\n   */\n\n\n  destroy() {\n    if (this.subs) {\n      // clean subscriptions to avoid reconnections\n      this.subs.forEach(subDestroy => subDestroy());\n      this.subs = undefined;\n    }\n\n    this.io[\"_destroy\"](this);\n  }\n  /**\n   * Disconnects the socket manually. In that case, the socket will not try to reconnect.\n   *\n   * If this is the last active Socket instance of the {@link Manager}, the low-level connection will be closed.\n   *\n   * @example\n   * const socket = io();\n   *\n   * socket.on(\"disconnect\", (reason) => {\n   *   // console.log(reason); prints \"io client disconnect\"\n   * });\n   *\n   * socket.disconnect();\n   *\n   * @return self\n   */\n\n\n  disconnect() {\n    if (this.connected) {\n      this.packet({\n        type: PacketType.DISCONNECT\n      });\n    } // remove socket from pool\n\n\n    this.destroy();\n\n    if (this.connected) {\n      // fire events\n      this.onclose(\"io client disconnect\");\n    }\n\n    return this;\n  }\n  /**\n   * Alias for {@link disconnect()}.\n   *\n   * @return self\n   */\n\n\n  close() {\n    return this.disconnect();\n  }\n  /**\n   * Sets the compress flag.\n   *\n   * @example\n   * socket.compress(false).emit(\"hello\");\n   *\n   * @param compress - if `true`, compresses the sending data\n   * @return self\n   */\n\n\n  compress(compress) {\n    this.flags.compress = compress;\n    return this;\n  }\n  /**\n   * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n   * ready to send messages.\n   *\n   * @example\n   * socket.volatile.emit(\"hello\"); // the server may or may not receive it\n   *\n   * @returns self\n   */\n\n\n  get volatile() {\n    this.flags.volatile = true;\n    return this;\n  }\n  /**\n   * Sets a modifier for a subsequent event emission that the callback will be called with an error when the\n   * given number of milliseconds have elapsed without an acknowledgement from the server:\n   *\n   * @example\n   * socket.timeout(5000).emit(\"my-event\", (err) => {\n   *   if (err) {\n   *     // the server did not acknowledge the event in the given delay\n   *   }\n   * });\n   *\n   * @returns self\n   */\n\n\n  timeout(timeout) {\n    this.flags.timeout = timeout;\n    return this;\n  }\n  /**\n   * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n   * callback.\n   *\n   * @example\n   * socket.onAny((event, ...args) => {\n   *   console.log(`got ${event}`);\n   * });\n   *\n   * @param listener\n   */\n\n\n  onAny(listener) {\n    this._anyListeners = this._anyListeners || [];\n\n    this._anyListeners.push(listener);\n\n    return this;\n  }\n  /**\n   * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n   * callback. The listener is added to the beginning of the listeners array.\n   *\n   * @example\n   * socket.prependAny((event, ...args) => {\n   *   console.log(`got event ${event}`);\n   * });\n   *\n   * @param listener\n   */\n\n\n  prependAny(listener) {\n    this._anyListeners = this._anyListeners || [];\n\n    this._anyListeners.unshift(listener);\n\n    return this;\n  }\n  /**\n   * Removes the listener that will be fired when any event is emitted.\n   *\n   * @example\n   * const catchAllListener = (event, ...args) => {\n   *   console.log(`got event ${event}`);\n   * }\n   *\n   * socket.onAny(catchAllListener);\n   *\n   * // remove a specific listener\n   * socket.offAny(catchAllListener);\n   *\n   * // or remove all listeners\n   * socket.offAny();\n   *\n   * @param listener\n   */\n\n\n  offAny(listener) {\n    if (!this._anyListeners) {\n      return this;\n    }\n\n    if (listener) {\n      const listeners = this._anyListeners;\n\n      for (let i = 0; i < listeners.length; i++) {\n        if (listener === listeners[i]) {\n          listeners.splice(i, 1);\n          return this;\n        }\n      }\n    } else {\n      this._anyListeners = [];\n    }\n\n    return this;\n  }\n  /**\n   * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n   * e.g. to remove listeners.\n   */\n\n\n  listenersAny() {\n    return this._anyListeners || [];\n  }\n  /**\n   * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n   * callback.\n   *\n   * Note: acknowledgements sent to the server are not included.\n   *\n   * @example\n   * socket.onAnyOutgoing((event, ...args) => {\n   *   console.log(`sent event ${event}`);\n   * });\n   *\n   * @param listener\n   */\n\n\n  onAnyOutgoing(listener) {\n    this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n\n    this._anyOutgoingListeners.push(listener);\n\n    return this;\n  }\n  /**\n   * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n   * callback. The listener is added to the beginning of the listeners array.\n   *\n   * Note: acknowledgements sent to the server are not included.\n   *\n   * @example\n   * socket.prependAnyOutgoing((event, ...args) => {\n   *   console.log(`sent event ${event}`);\n   * });\n   *\n   * @param listener\n   */\n\n\n  prependAnyOutgoing(listener) {\n    this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n\n    this._anyOutgoingListeners.unshift(listener);\n\n    return this;\n  }\n  /**\n   * Removes the listener that will be fired when any event is emitted.\n   *\n   * @example\n   * const catchAllListener = (event, ...args) => {\n   *   console.log(`sent event ${event}`);\n   * }\n   *\n   * socket.onAnyOutgoing(catchAllListener);\n   *\n   * // remove a specific listener\n   * socket.offAnyOutgoing(catchAllListener);\n   *\n   * // or remove all listeners\n   * socket.offAnyOutgoing();\n   *\n   * @param [listener] - the catch-all listener (optional)\n   */\n\n\n  offAnyOutgoing(listener) {\n    if (!this._anyOutgoingListeners) {\n      return this;\n    }\n\n    if (listener) {\n      const listeners = this._anyOutgoingListeners;\n\n      for (let i = 0; i < listeners.length; i++) {\n        if (listener === listeners[i]) {\n          listeners.splice(i, 1);\n          return this;\n        }\n      }\n    } else {\n      this._anyOutgoingListeners = [];\n    }\n\n    return this;\n  }\n  /**\n   * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n   * e.g. to remove listeners.\n   */\n\n\n  listenersAnyOutgoing() {\n    return this._anyOutgoingListeners || [];\n  }\n  /**\n   * Notify the listeners for each packet sent\n   *\n   * @param packet\n   *\n   * @private\n   */\n\n\n  notifyOutgoingListeners(packet) {\n    if (this._anyOutgoingListeners && this._anyOutgoingListeners.length) {\n      const listeners = this._anyOutgoingListeners.slice();\n\n      for (const listener of listeners) {\n        listener.apply(this, packet.data);\n      }\n    }\n  }\n\n}", "map": {"version": 3, "names": ["PacketType", "on", "Emitter", "RESERVED_EVENTS", "Object", "freeze", "connect", "connect_error", "disconnect", "disconnecting", "newListener", "removeListener", "Socket", "constructor", "io", "nsp", "opts", "connected", "recovered", "<PERSON><PERSON><PERSON><PERSON>", "send<PERSON><PERSON><PERSON>", "_queue", "_queueSeq", "ids", "acks", "flags", "auth", "_opts", "assign", "_autoConnect", "open", "disconnected", "subEvents", "subs", "onopen", "bind", "onpacket", "onerror", "onclose", "active", "_readyState", "send", "args", "unshift", "emit", "apply", "ev", "_a", "_b", "_c", "hasOwnProperty", "Error", "toString", "retries", "fromQueue", "volatile", "_addToQueue", "packet", "type", "EVENT", "data", "options", "compress", "length", "id", "ack", "pop", "_registerAckCallback", "isTransportWritable", "engine", "transport", "writable", "isConnected", "_hasPingExpired", "discardPacket", "notifyOutgoingListeners", "push", "timeout", "ackTimeout", "undefined", "timer", "setTimeoutFn", "i", "splice", "call", "fn", "clearTimeoutFn", "with<PERSON><PERSON><PERSON>", "emitWithAck", "Promise", "resolve", "reject", "arg1", "arg2", "tryCount", "pending", "err", "responseArgs", "<PERSON><PERSON><PERSON><PERSON>", "shift", "_drainQueue", "force", "_packet", "_sendConnectPacket", "CONNECT", "_pid", "pid", "offset", "_lastOffset", "emit<PERSON><PERSON><PERSON><PERSON>", "reason", "description", "_clearAcks", "keys", "for<PERSON>ach", "isBuffered", "some", "String", "sameNamespace", "sid", "onconnect", "BINARY_EVENT", "onevent", "ACK", "BINARY_ACK", "onack", "DISCONNECT", "ondisconnect", "CONNECT_ERROR", "destroy", "message", "emitEvent", "_anyListeners", "listeners", "slice", "listener", "self", "sent", "emitBuffered", "subDestroy", "close", "onAny", "prependAny", "offAny", "listenersAny", "onAnyOutgoing", "_anyOutgoingListeners", "prependAnyOutgoing", "offAnyOutgoing", "listenersAnyOutgoing"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/socket.io-client/build/esm/socket.js"], "sourcesContent": ["import { PacketType } from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */\nconst RESERVED_EVENTS = Object.freeze({\n    connect: 1,\n    connect_error: 1,\n    disconnect: 1,\n    disconnecting: 1,\n    // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n    newListener: 1,\n    removeListener: 1,\n});\n/**\n * A Socket is the fundamental class for interacting with the server.\n *\n * A Socket belongs to a certain Namespace (by default /) and uses an underlying {@link Manager} to communicate.\n *\n * @example\n * const socket = io();\n *\n * socket.on(\"connect\", () => {\n *   console.log(\"connected\");\n * });\n *\n * // send an event to the server\n * socket.emit(\"foo\", \"bar\");\n *\n * socket.on(\"foobar\", () => {\n *   // an event was received from the server\n * });\n *\n * // upon disconnection\n * socket.on(\"disconnect\", (reason) => {\n *   console.log(`disconnected due to ${reason}`);\n * });\n */\nexport class Socket extends Emitter {\n    /**\n     * `Socket` constructor.\n     */\n    constructor(io, nsp, opts) {\n        super();\n        /**\n         * Whether the socket is currently connected to the server.\n         *\n         * @example\n         * const socket = io();\n         *\n         * socket.on(\"connect\", () => {\n         *   console.log(socket.connected); // true\n         * });\n         *\n         * socket.on(\"disconnect\", () => {\n         *   console.log(socket.connected); // false\n         * });\n         */\n        this.connected = false;\n        /**\n         * Whether the connection state was recovered after a temporary disconnection. In that case, any missed packets will\n         * be transmitted by the server.\n         */\n        this.recovered = false;\n        /**\n         * Buffer for packets received before the CONNECT packet\n         */\n        this.receiveBuffer = [];\n        /**\n         * Buffer for packets that will be sent once the socket is connected\n         */\n        this.sendBuffer = [];\n        /**\n         * The queue of packets to be sent with retry in case of failure.\n         *\n         * Packets are sent one by one, each waiting for the server acknowledgement, in order to guarantee the delivery order.\n         * @private\n         */\n        this._queue = [];\n        /**\n         * A sequence to generate the ID of the {@link QueuedPacket}.\n         * @private\n         */\n        this._queueSeq = 0;\n        this.ids = 0;\n        /**\n         * A map containing acknowledgement handlers.\n         *\n         * The `withError` attribute is used to differentiate handlers that accept an error as first argument:\n         *\n         * - `socket.emit(\"test\", (err, value) => { ... })` with `ackTimeout` option\n         * - `socket.timeout(5000).emit(\"test\", (err, value) => { ... })`\n         * - `const value = await socket.emitWithAck(\"test\")`\n         *\n         * From those that don't:\n         *\n         * - `socket.emit(\"test\", (value) => { ... });`\n         *\n         * In the first case, the handlers will be called with an error when:\n         *\n         * - the timeout is reached\n         * - the socket gets disconnected\n         *\n         * In the second case, the handlers will be simply discarded upon disconnection, since the client will never receive\n         * an acknowledgement from the server.\n         *\n         * @private\n         */\n        this.acks = {};\n        this.flags = {};\n        this.io = io;\n        this.nsp = nsp;\n        if (opts && opts.auth) {\n            this.auth = opts.auth;\n        }\n        this._opts = Object.assign({}, opts);\n        if (this.io._autoConnect)\n            this.open();\n    }\n    /**\n     * Whether the socket is currently disconnected\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"connect\", () => {\n     *   console.log(socket.disconnected); // false\n     * });\n     *\n     * socket.on(\"disconnect\", () => {\n     *   console.log(socket.disconnected); // true\n     * });\n     */\n    get disconnected() {\n        return !this.connected;\n    }\n    /**\n     * Subscribe to open, close and packet events\n     *\n     * @private\n     */\n    subEvents() {\n        if (this.subs)\n            return;\n        const io = this.io;\n        this.subs = [\n            on(io, \"open\", this.onopen.bind(this)),\n            on(io, \"packet\", this.onpacket.bind(this)),\n            on(io, \"error\", this.onerror.bind(this)),\n            on(io, \"close\", this.onclose.bind(this)),\n        ];\n    }\n    /**\n     * Whether the Socket will try to reconnect when its Manager connects or reconnects.\n     *\n     * @example\n     * const socket = io();\n     *\n     * console.log(socket.active); // true\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   if (reason === \"io server disconnect\") {\n     *     // the disconnection was initiated by the server, you need to manually reconnect\n     *     console.log(socket.active); // false\n     *   }\n     *   // else the socket will automatically try to reconnect\n     *   console.log(socket.active); // true\n     * });\n     */\n    get active() {\n        return !!this.subs;\n    }\n    /**\n     * \"Opens\" the socket.\n     *\n     * @example\n     * const socket = io({\n     *   autoConnect: false\n     * });\n     *\n     * socket.connect();\n     */\n    connect() {\n        if (this.connected)\n            return this;\n        this.subEvents();\n        if (!this.io[\"_reconnecting\"])\n            this.io.open(); // ensure open\n        if (\"open\" === this.io._readyState)\n            this.onopen();\n        return this;\n    }\n    /**\n     * Alias for {@link connect()}.\n     */\n    open() {\n        return this.connect();\n    }\n    /**\n     * Sends a `message` event.\n     *\n     * This method mimics the WebSocket.send() method.\n     *\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send\n     *\n     * @example\n     * socket.send(\"hello\");\n     *\n     * // this is equivalent to\n     * socket.emit(\"message\", \"hello\");\n     *\n     * @return self\n     */\n    send(...args) {\n        args.unshift(\"message\");\n        this.emit.apply(this, args);\n        return this;\n    }\n    /**\n     * Override `emit`.\n     * If the event is in `events`, it's emitted normally.\n     *\n     * @example\n     * socket.emit(\"hello\", \"world\");\n     *\n     * // all serializable datastructures are supported (no need to call JSON.stringify)\n     * socket.emit(\"hello\", 1, \"2\", { 3: [\"4\"], 5: Uint8Array.from([6]) });\n     *\n     * // with an acknowledgement from the server\n     * socket.emit(\"hello\", \"world\", (val) => {\n     *   // ...\n     * });\n     *\n     * @return self\n     */\n    emit(ev, ...args) {\n        var _a, _b, _c;\n        if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n            throw new Error('\"' + ev.toString() + '\" is a reserved event name');\n        }\n        args.unshift(ev);\n        if (this._opts.retries && !this.flags.fromQueue && !this.flags.volatile) {\n            this._addToQueue(args);\n            return this;\n        }\n        const packet = {\n            type: PacketType.EVENT,\n            data: args,\n        };\n        packet.options = {};\n        packet.options.compress = this.flags.compress !== false;\n        // event ack callback\n        if (\"function\" === typeof args[args.length - 1]) {\n            const id = this.ids++;\n            const ack = args.pop();\n            this._registerAckCallback(id, ack);\n            packet.id = id;\n        }\n        const isTransportWritable = (_b = (_a = this.io.engine) === null || _a === void 0 ? void 0 : _a.transport) === null || _b === void 0 ? void 0 : _b.writable;\n        const isConnected = this.connected && !((_c = this.io.engine) === null || _c === void 0 ? void 0 : _c._hasPingExpired());\n        const discardPacket = this.flags.volatile && !isTransportWritable;\n        if (discardPacket) {\n        }\n        else if (isConnected) {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        }\n        else {\n            this.sendBuffer.push(packet);\n        }\n        this.flags = {};\n        return this;\n    }\n    /**\n     * @private\n     */\n    _registerAckCallback(id, ack) {\n        var _a;\n        const timeout = (_a = this.flags.timeout) !== null && _a !== void 0 ? _a : this._opts.ackTimeout;\n        if (timeout === undefined) {\n            this.acks[id] = ack;\n            return;\n        }\n        // @ts-ignore\n        const timer = this.io.setTimeoutFn(() => {\n            delete this.acks[id];\n            for (let i = 0; i < this.sendBuffer.length; i++) {\n                if (this.sendBuffer[i].id === id) {\n                    this.sendBuffer.splice(i, 1);\n                }\n            }\n            ack.call(this, new Error(\"operation has timed out\"));\n        }, timeout);\n        const fn = (...args) => {\n            // @ts-ignore\n            this.io.clearTimeoutFn(timer);\n            ack.apply(this, args);\n        };\n        fn.withError = true;\n        this.acks[id] = fn;\n    }\n    /**\n     * Emits an event and waits for an acknowledgement\n     *\n     * @example\n     * // without timeout\n     * const response = await socket.emitWithAck(\"hello\", \"world\");\n     *\n     * // with a specific timeout\n     * try {\n     *   const response = await socket.timeout(1000).emitWithAck(\"hello\", \"world\");\n     * } catch (err) {\n     *   // the server did not acknowledge the event in the given delay\n     * }\n     *\n     * @return a Promise that will be fulfilled when the server acknowledges the event\n     */\n    emitWithAck(ev, ...args) {\n        return new Promise((resolve, reject) => {\n            const fn = (arg1, arg2) => {\n                return arg1 ? reject(arg1) : resolve(arg2);\n            };\n            fn.withError = true;\n            args.push(fn);\n            this.emit(ev, ...args);\n        });\n    }\n    /**\n     * Add the packet to the queue.\n     * @param args\n     * @private\n     */\n    _addToQueue(args) {\n        let ack;\n        if (typeof args[args.length - 1] === \"function\") {\n            ack = args.pop();\n        }\n        const packet = {\n            id: this._queueSeq++,\n            tryCount: 0,\n            pending: false,\n            args,\n            flags: Object.assign({ fromQueue: true }, this.flags),\n        };\n        args.push((err, ...responseArgs) => {\n            if (packet !== this._queue[0]) {\n                // the packet has already been acknowledged\n                return;\n            }\n            const hasError = err !== null;\n            if (hasError) {\n                if (packet.tryCount > this._opts.retries) {\n                    this._queue.shift();\n                    if (ack) {\n                        ack(err);\n                    }\n                }\n            }\n            else {\n                this._queue.shift();\n                if (ack) {\n                    ack(null, ...responseArgs);\n                }\n            }\n            packet.pending = false;\n            return this._drainQueue();\n        });\n        this._queue.push(packet);\n        this._drainQueue();\n    }\n    /**\n     * Send the first packet of the queue, and wait for an acknowledgement from the server.\n     * @param force - whether to resend a packet that has not been acknowledged yet\n     *\n     * @private\n     */\n    _drainQueue(force = false) {\n        if (!this.connected || this._queue.length === 0) {\n            return;\n        }\n        const packet = this._queue[0];\n        if (packet.pending && !force) {\n            return;\n        }\n        packet.pending = true;\n        packet.tryCount++;\n        this.flags = packet.flags;\n        this.emit.apply(this, packet.args);\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param packet\n     * @private\n     */\n    packet(packet) {\n        packet.nsp = this.nsp;\n        this.io._packet(packet);\n    }\n    /**\n     * Called upon engine `open`.\n     *\n     * @private\n     */\n    onopen() {\n        if (typeof this.auth == \"function\") {\n            this.auth((data) => {\n                this._sendConnectPacket(data);\n            });\n        }\n        else {\n            this._sendConnectPacket(this.auth);\n        }\n    }\n    /**\n     * Sends a CONNECT packet to initiate the Socket.IO session.\n     *\n     * @param data\n     * @private\n     */\n    _sendConnectPacket(data) {\n        this.packet({\n            type: PacketType.CONNECT,\n            data: this._pid\n                ? Object.assign({ pid: this._pid, offset: this._lastOffset }, data)\n                : data,\n        });\n    }\n    /**\n     * Called upon engine or manager `error`.\n     *\n     * @param err\n     * @private\n     */\n    onerror(err) {\n        if (!this.connected) {\n            this.emitReserved(\"connect_error\", err);\n        }\n    }\n    /**\n     * Called upon engine `close`.\n     *\n     * @param reason\n     * @param description\n     * @private\n     */\n    onclose(reason, description) {\n        this.connected = false;\n        delete this.id;\n        this.emitReserved(\"disconnect\", reason, description);\n        this._clearAcks();\n    }\n    /**\n     * Clears the acknowledgement handlers upon disconnection, since the client will never receive an acknowledgement from\n     * the server.\n     *\n     * @private\n     */\n    _clearAcks() {\n        Object.keys(this.acks).forEach((id) => {\n            const isBuffered = this.sendBuffer.some((packet) => String(packet.id) === id);\n            if (!isBuffered) {\n                // note: handlers that do not accept an error as first argument are ignored here\n                const ack = this.acks[id];\n                delete this.acks[id];\n                if (ack.withError) {\n                    ack.call(this, new Error(\"socket has been disconnected\"));\n                }\n            }\n        });\n    }\n    /**\n     * Called with socket packet.\n     *\n     * @param packet\n     * @private\n     */\n    onpacket(packet) {\n        const sameNamespace = packet.nsp === this.nsp;\n        if (!sameNamespace)\n            return;\n        switch (packet.type) {\n            case PacketType.CONNECT:\n                if (packet.data && packet.data.sid) {\n                    this.onconnect(packet.data.sid, packet.data.pid);\n                }\n                else {\n                    this.emitReserved(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n                }\n                break;\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                this.onevent(packet);\n                break;\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                this.onack(packet);\n                break;\n            case PacketType.DISCONNECT:\n                this.ondisconnect();\n                break;\n            case PacketType.CONNECT_ERROR:\n                this.destroy();\n                const err = new Error(packet.data.message);\n                // @ts-ignore\n                err.data = packet.data.data;\n                this.emitReserved(\"connect_error\", err);\n                break;\n        }\n    }\n    /**\n     * Called upon a server event.\n     *\n     * @param packet\n     * @private\n     */\n    onevent(packet) {\n        const args = packet.data || [];\n        if (null != packet.id) {\n            args.push(this.ack(packet.id));\n        }\n        if (this.connected) {\n            this.emitEvent(args);\n        }\n        else {\n            this.receiveBuffer.push(Object.freeze(args));\n        }\n    }\n    emitEvent(args) {\n        if (this._anyListeners && this._anyListeners.length) {\n            const listeners = this._anyListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, args);\n            }\n        }\n        super.emit.apply(this, args);\n        if (this._pid && args.length && typeof args[args.length - 1] === \"string\") {\n            this._lastOffset = args[args.length - 1];\n        }\n    }\n    /**\n     * Produces an ack callback to emit with an event.\n     *\n     * @private\n     */\n    ack(id) {\n        const self = this;\n        let sent = false;\n        return function (...args) {\n            // prevent double callbacks\n            if (sent)\n                return;\n            sent = true;\n            self.packet({\n                type: PacketType.ACK,\n                id: id,\n                data: args,\n            });\n        };\n    }\n    /**\n     * Called upon a server acknowledgement.\n     *\n     * @param packet\n     * @private\n     */\n    onack(packet) {\n        const ack = this.acks[packet.id];\n        if (typeof ack !== \"function\") {\n            return;\n        }\n        delete this.acks[packet.id];\n        // @ts-ignore FIXME ack is incorrectly inferred as 'never'\n        if (ack.withError) {\n            packet.data.unshift(null);\n        }\n        // @ts-ignore\n        ack.apply(this, packet.data);\n    }\n    /**\n     * Called upon server connect.\n     *\n     * @private\n     */\n    onconnect(id, pid) {\n        this.id = id;\n        this.recovered = pid && this._pid === pid;\n        this._pid = pid; // defined only if connection state recovery is enabled\n        this.connected = true;\n        this.emitBuffered();\n        this.emitReserved(\"connect\");\n        this._drainQueue(true);\n    }\n    /**\n     * Emit buffered events (received and emitted).\n     *\n     * @private\n     */\n    emitBuffered() {\n        this.receiveBuffer.forEach((args) => this.emitEvent(args));\n        this.receiveBuffer = [];\n        this.sendBuffer.forEach((packet) => {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        });\n        this.sendBuffer = [];\n    }\n    /**\n     * Called upon server disconnect.\n     *\n     * @private\n     */\n    ondisconnect() {\n        this.destroy();\n        this.onclose(\"io server disconnect\");\n    }\n    /**\n     * Called upon forced client/server side disconnections,\n     * this method ensures the manager stops tracking us and\n     * that reconnections don't get triggered for this.\n     *\n     * @private\n     */\n    destroy() {\n        if (this.subs) {\n            // clean subscriptions to avoid reconnections\n            this.subs.forEach((subDestroy) => subDestroy());\n            this.subs = undefined;\n        }\n        this.io[\"_destroy\"](this);\n    }\n    /**\n     * Disconnects the socket manually. In that case, the socket will not try to reconnect.\n     *\n     * If this is the last active Socket instance of the {@link Manager}, the low-level connection will be closed.\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   // console.log(reason); prints \"io client disconnect\"\n     * });\n     *\n     * socket.disconnect();\n     *\n     * @return self\n     */\n    disconnect() {\n        if (this.connected) {\n            this.packet({ type: PacketType.DISCONNECT });\n        }\n        // remove socket from pool\n        this.destroy();\n        if (this.connected) {\n            // fire events\n            this.onclose(\"io client disconnect\");\n        }\n        return this;\n    }\n    /**\n     * Alias for {@link disconnect()}.\n     *\n     * @return self\n     */\n    close() {\n        return this.disconnect();\n    }\n    /**\n     * Sets the compress flag.\n     *\n     * @example\n     * socket.compress(false).emit(\"hello\");\n     *\n     * @param compress - if `true`, compresses the sending data\n     * @return self\n     */\n    compress(compress) {\n        this.flags.compress = compress;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n     * ready to send messages.\n     *\n     * @example\n     * socket.volatile.emit(\"hello\"); // the server may or may not receive it\n     *\n     * @returns self\n     */\n    get volatile() {\n        this.flags.volatile = true;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the callback will be called with an error when the\n     * given number of milliseconds have elapsed without an acknowledgement from the server:\n     *\n     * @example\n     * socket.timeout(5000).emit(\"my-event\", (err) => {\n     *   if (err) {\n     *     // the server did not acknowledge the event in the given delay\n     *   }\n     * });\n     *\n     * @returns self\n     */\n    timeout(timeout) {\n        this.flags.timeout = timeout;\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @example\n     * socket.onAny((event, ...args) => {\n     *   console.log(`got ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @example\n     * socket.prependAny((event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * }\n     *\n     * socket.onAny(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAny(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAny();\n     *\n     * @param listener\n     */\n    offAny(listener) {\n        if (!this._anyListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAny() {\n        return this._anyListeners || [];\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.onAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.prependAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * }\n     *\n     * socket.onAnyOutgoing(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAnyOutgoing(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAnyOutgoing();\n     *\n     * @param [listener] - the catch-all listener (optional)\n     */\n    offAnyOutgoing(listener) {\n        if (!this._anyOutgoingListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyOutgoingListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyOutgoingListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAnyOutgoing() {\n        return this._anyOutgoingListeners || [];\n    }\n    /**\n     * Notify the listeners for each packet sent\n     *\n     * @param packet\n     *\n     * @private\n     */\n    notifyOutgoingListeners(packet) {\n        if (this._anyOutgoingListeners && this._anyOutgoingListeners.length) {\n            const listeners = this._anyOutgoingListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, packet.data);\n            }\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,kBAA3B;AACA,SAASC,EAAT,QAAmB,SAAnB;AACA,SAASC,OAAT,QAAyB,8BAAzB;AACA;AACA;AACA;AACA;;AACA,MAAMC,eAAe,GAAGC,MAAM,CAACC,MAAP,CAAc;EAClCC,OAAO,EAAE,CADyB;EAElCC,aAAa,EAAE,CAFmB;EAGlCC,UAAU,EAAE,CAHsB;EAIlCC,aAAa,EAAE,CAJmB;EAKlC;EACAC,WAAW,EAAE,CANqB;EAOlCC,cAAc,EAAE;AAPkB,CAAd,CAAxB;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,MAAMC,MAAN,SAAqBV,OAArB,CAA6B;EAChC;AACJ;AACA;EACIW,WAAW,CAACC,EAAD,EAAKC,GAAL,EAAUC,IAAV,EAAgB;IACvB;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IACQ,KAAKC,SAAL,GAAiB,KAAjB;IACA;AACR;AACA;AACA;;IACQ,KAAKC,SAAL,GAAiB,KAAjB;IACA;AACR;AACA;;IACQ,KAAKC,aAAL,GAAqB,EAArB;IACA;AACR;AACA;;IACQ,KAAKC,UAAL,GAAkB,EAAlB;IACA;AACR;AACA;AACA;AACA;AACA;;IACQ,KAAKC,MAAL,GAAc,EAAd;IACA;AACR;AACA;AACA;;IACQ,KAAKC,SAAL,GAAiB,CAAjB;IACA,KAAKC,GAAL,GAAW,CAAX;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IACQ,KAAKC,IAAL,GAAY,EAAZ;IACA,KAAKC,KAAL,GAAa,EAAb;IACA,KAAKX,EAAL,GAAUA,EAAV;IACA,KAAKC,GAAL,GAAWA,GAAX;;IACA,IAAIC,IAAI,IAAIA,IAAI,CAACU,IAAjB,EAAuB;MACnB,KAAKA,IAAL,GAAYV,IAAI,CAACU,IAAjB;IACH;;IACD,KAAKC,KAAL,GAAavB,MAAM,CAACwB,MAAP,CAAc,EAAd,EAAkBZ,IAAlB,CAAb;IACA,IAAI,KAAKF,EAAL,CAAQe,YAAZ,EACI,KAAKC,IAAL;EACP;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACoB,IAAZC,YAAY,GAAG;IACf,OAAO,CAAC,KAAKd,SAAb;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIe,SAAS,GAAG;IACR,IAAI,KAAKC,IAAT,EACI;IACJ,MAAMnB,EAAE,GAAG,KAAKA,EAAhB;IACA,KAAKmB,IAAL,GAAY,CACRhC,EAAE,CAACa,EAAD,EAAK,MAAL,EAAa,KAAKoB,MAAL,CAAYC,IAAZ,CAAiB,IAAjB,CAAb,CADM,EAERlC,EAAE,CAACa,EAAD,EAAK,QAAL,EAAe,KAAKsB,QAAL,CAAcD,IAAd,CAAmB,IAAnB,CAAf,CAFM,EAGRlC,EAAE,CAACa,EAAD,EAAK,OAAL,EAAc,KAAKuB,OAAL,CAAaF,IAAb,CAAkB,IAAlB,CAAd,CAHM,EAIRlC,EAAE,CAACa,EAAD,EAAK,OAAL,EAAc,KAAKwB,OAAL,CAAaH,IAAb,CAAkB,IAAlB,CAAd,CAJM,CAAZ;EAMH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACc,IAANI,MAAM,GAAG;IACT,OAAO,CAAC,CAAC,KAAKN,IAAd;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACI3B,OAAO,GAAG;IACN,IAAI,KAAKW,SAAT,EACI,OAAO,IAAP;IACJ,KAAKe,SAAL;IACA,IAAI,CAAC,KAAKlB,EAAL,CAAQ,eAAR,CAAL,EACI,KAAKA,EAAL,CAAQgB,IAAR,GALE,CAKc;;IACpB,IAAI,WAAW,KAAKhB,EAAL,CAAQ0B,WAAvB,EACI,KAAKN,MAAL;IACJ,OAAO,IAAP;EACH;EACD;AACJ;AACA;;;EACIJ,IAAI,GAAG;IACH,OAAO,KAAKxB,OAAL,EAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACImC,IAAI,CAAC,GAAGC,IAAJ,EAAU;IACVA,IAAI,CAACC,OAAL,CAAa,SAAb;IACA,KAAKC,IAAL,CAAUC,KAAV,CAAgB,IAAhB,EAAsBH,IAAtB;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIE,IAAI,CAACE,EAAD,EAAK,GAAGJ,IAAR,EAAc;IACd,IAAIK,EAAJ,EAAQC,EAAR,EAAYC,EAAZ;;IACA,IAAI9C,eAAe,CAAC+C,cAAhB,CAA+BJ,EAA/B,CAAJ,EAAwC;MACpC,MAAM,IAAIK,KAAJ,CAAU,MAAML,EAAE,CAACM,QAAH,EAAN,GAAsB,4BAAhC,CAAN;IACH;;IACDV,IAAI,CAACC,OAAL,CAAaG,EAAb;;IACA,IAAI,KAAKnB,KAAL,CAAW0B,OAAX,IAAsB,CAAC,KAAK5B,KAAL,CAAW6B,SAAlC,IAA+C,CAAC,KAAK7B,KAAL,CAAW8B,QAA/D,EAAyE;MACrE,KAAKC,WAAL,CAAiBd,IAAjB;;MACA,OAAO,IAAP;IACH;;IACD,MAAMe,MAAM,GAAG;MACXC,IAAI,EAAE1D,UAAU,CAAC2D,KADN;MAEXC,IAAI,EAAElB;IAFK,CAAf;IAIAe,MAAM,CAACI,OAAP,GAAiB,EAAjB;IACAJ,MAAM,CAACI,OAAP,CAAeC,QAAf,GAA0B,KAAKrC,KAAL,CAAWqC,QAAX,KAAwB,KAAlD,CAfc,CAgBd;;IACA,IAAI,eAAe,OAAOpB,IAAI,CAACA,IAAI,CAACqB,MAAL,GAAc,CAAf,CAA9B,EAAiD;MAC7C,MAAMC,EAAE,GAAG,KAAKzC,GAAL,EAAX;MACA,MAAM0C,GAAG,GAAGvB,IAAI,CAACwB,GAAL,EAAZ;;MACA,KAAKC,oBAAL,CAA0BH,EAA1B,EAA8BC,GAA9B;;MACAR,MAAM,CAACO,EAAP,GAAYA,EAAZ;IACH;;IACD,MAAMI,mBAAmB,GAAG,CAACpB,EAAE,GAAG,CAACD,EAAE,GAAG,KAAKjC,EAAL,CAAQuD,MAAd,MAA0B,IAA1B,IAAkCtB,EAAE,KAAK,KAAK,CAA9C,GAAkD,KAAK,CAAvD,GAA2DA,EAAE,CAACuB,SAApE,MAAmF,IAAnF,IAA2FtB,EAAE,KAAK,KAAK,CAAvG,GAA2G,KAAK,CAAhH,GAAoHA,EAAE,CAACuB,QAAnJ;IACA,MAAMC,WAAW,GAAG,KAAKvD,SAAL,IAAkB,EAAE,CAACgC,EAAE,GAAG,KAAKnC,EAAL,CAAQuD,MAAd,MAA0B,IAA1B,IAAkCpB,EAAE,KAAK,KAAK,CAA9C,GAAkD,KAAK,CAAvD,GAA2DA,EAAE,CAACwB,eAAH,EAA7D,CAAtC;IACA,MAAMC,aAAa,GAAG,KAAKjD,KAAL,CAAW8B,QAAX,IAAuB,CAACa,mBAA9C;;IACA,IAAIM,aAAJ,EAAmB,CAClB,CADD,MAEK,IAAIF,WAAJ,EAAiB;MAClB,KAAKG,uBAAL,CAA6BlB,MAA7B;MACA,KAAKA,MAAL,CAAYA,MAAZ;IACH,CAHI,MAIA;MACD,KAAKrC,UAAL,CAAgBwD,IAAhB,CAAqBnB,MAArB;IACH;;IACD,KAAKhC,KAAL,GAAa,EAAb;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;;;EACI0C,oBAAoB,CAACH,EAAD,EAAKC,GAAL,EAAU;IAC1B,IAAIlB,EAAJ;;IACA,MAAM8B,OAAO,GAAG,CAAC9B,EAAE,GAAG,KAAKtB,KAAL,CAAWoD,OAAjB,MAA8B,IAA9B,IAAsC9B,EAAE,KAAK,KAAK,CAAlD,GAAsDA,EAAtD,GAA2D,KAAKpB,KAAL,CAAWmD,UAAtF;;IACA,IAAID,OAAO,KAAKE,SAAhB,EAA2B;MACvB,KAAKvD,IAAL,CAAUwC,EAAV,IAAgBC,GAAhB;MACA;IACH,CANyB,CAO1B;;;IACA,MAAMe,KAAK,GAAG,KAAKlE,EAAL,CAAQmE,YAAR,CAAqB,MAAM;MACrC,OAAO,KAAKzD,IAAL,CAAUwC,EAAV,CAAP;;MACA,KAAK,IAAIkB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK9D,UAAL,CAAgB2C,MAApC,EAA4CmB,CAAC,EAA7C,EAAiD;QAC7C,IAAI,KAAK9D,UAAL,CAAgB8D,CAAhB,EAAmBlB,EAAnB,KAA0BA,EAA9B,EAAkC;UAC9B,KAAK5C,UAAL,CAAgB+D,MAAhB,CAAuBD,CAAvB,EAA0B,CAA1B;QACH;MACJ;;MACDjB,GAAG,CAACmB,IAAJ,CAAS,IAAT,EAAe,IAAIjC,KAAJ,CAAU,yBAAV,CAAf;IACH,CARa,EAQX0B,OARW,CAAd;;IASA,MAAMQ,EAAE,GAAG,CAAC,GAAG3C,IAAJ,KAAa;MACpB;MACA,KAAK5B,EAAL,CAAQwE,cAAR,CAAuBN,KAAvB;MACAf,GAAG,CAACpB,KAAJ,CAAU,IAAV,EAAgBH,IAAhB;IACH,CAJD;;IAKA2C,EAAE,CAACE,SAAH,GAAe,IAAf;IACA,KAAK/D,IAAL,CAAUwC,EAAV,IAAgBqB,EAAhB;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIG,WAAW,CAAC1C,EAAD,EAAK,GAAGJ,IAAR,EAAc;IACrB,OAAO,IAAI+C,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;MACpC,MAAMN,EAAE,GAAG,CAACO,IAAD,EAAOC,IAAP,KAAgB;QACvB,OAAOD,IAAI,GAAGD,MAAM,CAACC,IAAD,CAAT,GAAkBF,OAAO,CAACG,IAAD,CAApC;MACH,CAFD;;MAGAR,EAAE,CAACE,SAAH,GAAe,IAAf;MACA7C,IAAI,CAACkC,IAAL,CAAUS,EAAV;MACA,KAAKzC,IAAL,CAAUE,EAAV,EAAc,GAAGJ,IAAjB;IACH,CAPM,CAAP;EAQH;EACD;AACJ;AACA;AACA;AACA;;;EACIc,WAAW,CAACd,IAAD,EAAO;IACd,IAAIuB,GAAJ;;IACA,IAAI,OAAOvB,IAAI,CAACA,IAAI,CAACqB,MAAL,GAAc,CAAf,CAAX,KAAiC,UAArC,EAAiD;MAC7CE,GAAG,GAAGvB,IAAI,CAACwB,GAAL,EAAN;IACH;;IACD,MAAMT,MAAM,GAAG;MACXO,EAAE,EAAE,KAAK1C,SAAL,EADO;MAEXwE,QAAQ,EAAE,CAFC;MAGXC,OAAO,EAAE,KAHE;MAIXrD,IAJW;MAKXjB,KAAK,EAAErB,MAAM,CAACwB,MAAP,CAAc;QAAE0B,SAAS,EAAE;MAAb,CAAd,EAAmC,KAAK7B,KAAxC;IALI,CAAf;IAOAiB,IAAI,CAACkC,IAAL,CAAU,CAACoB,GAAD,EAAM,GAAGC,YAAT,KAA0B;MAChC,IAAIxC,MAAM,KAAK,KAAKpC,MAAL,CAAY,CAAZ,CAAf,EAA+B;QAC3B;QACA;MACH;;MACD,MAAM6E,QAAQ,GAAGF,GAAG,KAAK,IAAzB;;MACA,IAAIE,QAAJ,EAAc;QACV,IAAIzC,MAAM,CAACqC,QAAP,GAAkB,KAAKnE,KAAL,CAAW0B,OAAjC,EAA0C;UACtC,KAAKhC,MAAL,CAAY8E,KAAZ;;UACA,IAAIlC,GAAJ,EAAS;YACLA,GAAG,CAAC+B,GAAD,CAAH;UACH;QACJ;MACJ,CAPD,MAQK;QACD,KAAK3E,MAAL,CAAY8E,KAAZ;;QACA,IAAIlC,GAAJ,EAAS;UACLA,GAAG,CAAC,IAAD,EAAO,GAAGgC,YAAV,CAAH;QACH;MACJ;;MACDxC,MAAM,CAACsC,OAAP,GAAiB,KAAjB;MACA,OAAO,KAAKK,WAAL,EAAP;IACH,CAtBD;;IAuBA,KAAK/E,MAAL,CAAYuD,IAAZ,CAAiBnB,MAAjB;;IACA,KAAK2C,WAAL;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIA,WAAW,CAACC,KAAK,GAAG,KAAT,EAAgB;IACvB,IAAI,CAAC,KAAKpF,SAAN,IAAmB,KAAKI,MAAL,CAAY0C,MAAZ,KAAuB,CAA9C,EAAiD;MAC7C;IACH;;IACD,MAAMN,MAAM,GAAG,KAAKpC,MAAL,CAAY,CAAZ,CAAf;;IACA,IAAIoC,MAAM,CAACsC,OAAP,IAAkB,CAACM,KAAvB,EAA8B;MAC1B;IACH;;IACD5C,MAAM,CAACsC,OAAP,GAAiB,IAAjB;IACAtC,MAAM,CAACqC,QAAP;IACA,KAAKrE,KAAL,GAAagC,MAAM,CAAChC,KAApB;IACA,KAAKmB,IAAL,CAAUC,KAAV,CAAgB,IAAhB,EAAsBY,MAAM,CAACf,IAA7B;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIe,MAAM,CAACA,MAAD,EAAS;IACXA,MAAM,CAAC1C,GAAP,GAAa,KAAKA,GAAlB;;IACA,KAAKD,EAAL,CAAQwF,OAAR,CAAgB7C,MAAhB;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIvB,MAAM,GAAG;IACL,IAAI,OAAO,KAAKR,IAAZ,IAAoB,UAAxB,EAAoC;MAChC,KAAKA,IAAL,CAAWkC,IAAD,IAAU;QAChB,KAAK2C,kBAAL,CAAwB3C,IAAxB;MACH,CAFD;IAGH,CAJD,MAKK;MACD,KAAK2C,kBAAL,CAAwB,KAAK7E,IAA7B;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACI6E,kBAAkB,CAAC3C,IAAD,EAAO;IACrB,KAAKH,MAAL,CAAY;MACRC,IAAI,EAAE1D,UAAU,CAACwG,OADT;MAER5C,IAAI,EAAE,KAAK6C,IAAL,GACArG,MAAM,CAACwB,MAAP,CAAc;QAAE8E,GAAG,EAAE,KAAKD,IAAZ;QAAkBE,MAAM,EAAE,KAAKC;MAA/B,CAAd,EAA4DhD,IAA5D,CADA,GAEAA;IAJE,CAAZ;EAMH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIvB,OAAO,CAAC2D,GAAD,EAAM;IACT,IAAI,CAAC,KAAK/E,SAAV,EAAqB;MACjB,KAAK4F,YAAL,CAAkB,eAAlB,EAAmCb,GAAnC;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACI1D,OAAO,CAACwE,MAAD,EAASC,WAAT,EAAsB;IACzB,KAAK9F,SAAL,GAAiB,KAAjB;IACA,OAAO,KAAK+C,EAAZ;IACA,KAAK6C,YAAL,CAAkB,YAAlB,EAAgCC,MAAhC,EAAwCC,WAAxC;;IACA,KAAKC,UAAL;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIA,UAAU,GAAG;IACT5G,MAAM,CAAC6G,IAAP,CAAY,KAAKzF,IAAjB,EAAuB0F,OAAvB,CAAgClD,EAAD,IAAQ;MACnC,MAAMmD,UAAU,GAAG,KAAK/F,UAAL,CAAgBgG,IAAhB,CAAsB3D,MAAD,IAAY4D,MAAM,CAAC5D,MAAM,CAACO,EAAR,CAAN,KAAsBA,EAAvD,CAAnB;;MACA,IAAI,CAACmD,UAAL,EAAiB;QACb;QACA,MAAMlD,GAAG,GAAG,KAAKzC,IAAL,CAAUwC,EAAV,CAAZ;QACA,OAAO,KAAKxC,IAAL,CAAUwC,EAAV,CAAP;;QACA,IAAIC,GAAG,CAACsB,SAAR,EAAmB;UACftB,GAAG,CAACmB,IAAJ,CAAS,IAAT,EAAe,IAAIjC,KAAJ,CAAU,8BAAV,CAAf;QACH;MACJ;IACJ,CAVD;EAWH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIf,QAAQ,CAACqB,MAAD,EAAS;IACb,MAAM6D,aAAa,GAAG7D,MAAM,CAAC1C,GAAP,KAAe,KAAKA,GAA1C;IACA,IAAI,CAACuG,aAAL,EACI;;IACJ,QAAQ7D,MAAM,CAACC,IAAf;MACI,KAAK1D,UAAU,CAACwG,OAAhB;QACI,IAAI/C,MAAM,CAACG,IAAP,IAAeH,MAAM,CAACG,IAAP,CAAY2D,GAA/B,EAAoC;UAChC,KAAKC,SAAL,CAAe/D,MAAM,CAACG,IAAP,CAAY2D,GAA3B,EAAgC9D,MAAM,CAACG,IAAP,CAAY8C,GAA5C;QACH,CAFD,MAGK;UACD,KAAKG,YAAL,CAAkB,eAAlB,EAAmC,IAAI1D,KAAJ,CAAU,2LAAV,CAAnC;QACH;;QACD;;MACJ,KAAKnD,UAAU,CAAC2D,KAAhB;MACA,KAAK3D,UAAU,CAACyH,YAAhB;QACI,KAAKC,OAAL,CAAajE,MAAb;QACA;;MACJ,KAAKzD,UAAU,CAAC2H,GAAhB;MACA,KAAK3H,UAAU,CAAC4H,UAAhB;QACI,KAAKC,KAAL,CAAWpE,MAAX;QACA;;MACJ,KAAKzD,UAAU,CAAC8H,UAAhB;QACI,KAAKC,YAAL;QACA;;MACJ,KAAK/H,UAAU,CAACgI,aAAhB;QACI,KAAKC,OAAL;QACA,MAAMjC,GAAG,GAAG,IAAI7C,KAAJ,CAAUM,MAAM,CAACG,IAAP,CAAYsE,OAAtB,CAAZ,CAFJ,CAGI;;QACAlC,GAAG,CAACpC,IAAJ,GAAWH,MAAM,CAACG,IAAP,CAAYA,IAAvB;QACA,KAAKiD,YAAL,CAAkB,eAAlB,EAAmCb,GAAnC;QACA;IA1BR;EA4BH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACI0B,OAAO,CAACjE,MAAD,EAAS;IACZ,MAAMf,IAAI,GAAGe,MAAM,CAACG,IAAP,IAAe,EAA5B;;IACA,IAAI,QAAQH,MAAM,CAACO,EAAnB,EAAuB;MACnBtB,IAAI,CAACkC,IAAL,CAAU,KAAKX,GAAL,CAASR,MAAM,CAACO,EAAhB,CAAV;IACH;;IACD,IAAI,KAAK/C,SAAT,EAAoB;MAChB,KAAKkH,SAAL,CAAezF,IAAf;IACH,CAFD,MAGK;MACD,KAAKvB,aAAL,CAAmByD,IAAnB,CAAwBxE,MAAM,CAACC,MAAP,CAAcqC,IAAd,CAAxB;IACH;EACJ;;EACDyF,SAAS,CAACzF,IAAD,EAAO;IACZ,IAAI,KAAK0F,aAAL,IAAsB,KAAKA,aAAL,CAAmBrE,MAA7C,EAAqD;MACjD,MAAMsE,SAAS,GAAG,KAAKD,aAAL,CAAmBE,KAAnB,EAAlB;;MACA,KAAK,MAAMC,QAAX,IAAuBF,SAAvB,EAAkC;QAC9BE,QAAQ,CAAC1F,KAAT,CAAe,IAAf,EAAqBH,IAArB;MACH;IACJ;;IACD,MAAME,IAAN,CAAWC,KAAX,CAAiB,IAAjB,EAAuBH,IAAvB;;IACA,IAAI,KAAK+D,IAAL,IAAa/D,IAAI,CAACqB,MAAlB,IAA4B,OAAOrB,IAAI,CAACA,IAAI,CAACqB,MAAL,GAAc,CAAf,CAAX,KAAiC,QAAjE,EAA2E;MACvE,KAAK6C,WAAL,GAAmBlE,IAAI,CAACA,IAAI,CAACqB,MAAL,GAAc,CAAf,CAAvB;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;;;EACIE,GAAG,CAACD,EAAD,EAAK;IACJ,MAAMwE,IAAI,GAAG,IAAb;IACA,IAAIC,IAAI,GAAG,KAAX;IACA,OAAO,UAAU,GAAG/F,IAAb,EAAmB;MACtB;MACA,IAAI+F,IAAJ,EACI;MACJA,IAAI,GAAG,IAAP;MACAD,IAAI,CAAC/E,MAAL,CAAY;QACRC,IAAI,EAAE1D,UAAU,CAAC2H,GADT;QAER3D,EAAE,EAAEA,EAFI;QAGRJ,IAAI,EAAElB;MAHE,CAAZ;IAKH,CAVD;EAWH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACImF,KAAK,CAACpE,MAAD,EAAS;IACV,MAAMQ,GAAG,GAAG,KAAKzC,IAAL,CAAUiC,MAAM,CAACO,EAAjB,CAAZ;;IACA,IAAI,OAAOC,GAAP,KAAe,UAAnB,EAA+B;MAC3B;IACH;;IACD,OAAO,KAAKzC,IAAL,CAAUiC,MAAM,CAACO,EAAjB,CAAP,CALU,CAMV;;IACA,IAAIC,GAAG,CAACsB,SAAR,EAAmB;MACf9B,MAAM,CAACG,IAAP,CAAYjB,OAAZ,CAAoB,IAApB;IACH,CATS,CAUV;;;IACAsB,GAAG,CAACpB,KAAJ,CAAU,IAAV,EAAgBY,MAAM,CAACG,IAAvB;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACI4D,SAAS,CAACxD,EAAD,EAAK0C,GAAL,EAAU;IACf,KAAK1C,EAAL,GAAUA,EAAV;IACA,KAAK9C,SAAL,GAAiBwF,GAAG,IAAI,KAAKD,IAAL,KAAcC,GAAtC;IACA,KAAKD,IAAL,GAAYC,GAAZ,CAHe,CAGE;;IACjB,KAAKzF,SAAL,GAAiB,IAAjB;IACA,KAAKyH,YAAL;IACA,KAAK7B,YAAL,CAAkB,SAAlB;;IACA,KAAKT,WAAL,CAAiB,IAAjB;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIsC,YAAY,GAAG;IACX,KAAKvH,aAAL,CAAmB+F,OAAnB,CAA4BxE,IAAD,IAAU,KAAKyF,SAAL,CAAezF,IAAf,CAArC;IACA,KAAKvB,aAAL,GAAqB,EAArB;IACA,KAAKC,UAAL,CAAgB8F,OAAhB,CAAyBzD,MAAD,IAAY;MAChC,KAAKkB,uBAAL,CAA6BlB,MAA7B;MACA,KAAKA,MAAL,CAAYA,MAAZ;IACH,CAHD;IAIA,KAAKrC,UAAL,GAAkB,EAAlB;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACI2G,YAAY,GAAG;IACX,KAAKE,OAAL;IACA,KAAK3F,OAAL,CAAa,sBAAb;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACI2F,OAAO,GAAG;IACN,IAAI,KAAKhG,IAAT,EAAe;MACX;MACA,KAAKA,IAAL,CAAUiF,OAAV,CAAmByB,UAAD,IAAgBA,UAAU,EAA5C;MACA,KAAK1G,IAAL,GAAY8C,SAAZ;IACH;;IACD,KAAKjE,EAAL,CAAQ,UAAR,EAAoB,IAApB;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIN,UAAU,GAAG;IACT,IAAI,KAAKS,SAAT,EAAoB;MAChB,KAAKwC,MAAL,CAAY;QAAEC,IAAI,EAAE1D,UAAU,CAAC8H;MAAnB,CAAZ;IACH,CAHQ,CAIT;;;IACA,KAAKG,OAAL;;IACA,IAAI,KAAKhH,SAAT,EAAoB;MAChB;MACA,KAAKqB,OAAL,CAAa,sBAAb;IACH;;IACD,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIsG,KAAK,GAAG;IACJ,OAAO,KAAKpI,UAAL,EAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIsD,QAAQ,CAACA,QAAD,EAAW;IACf,KAAKrC,KAAL,CAAWqC,QAAX,GAAsBA,QAAtB;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACgB,IAARP,QAAQ,GAAG;IACX,KAAK9B,KAAL,CAAW8B,QAAX,GAAsB,IAAtB;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIsB,OAAO,CAACA,OAAD,EAAU;IACb,KAAKpD,KAAL,CAAWoD,OAAX,GAAqBA,OAArB;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIgE,KAAK,CAACN,QAAD,EAAW;IACZ,KAAKH,aAAL,GAAqB,KAAKA,aAAL,IAAsB,EAA3C;;IACA,KAAKA,aAAL,CAAmBxD,IAAnB,CAAwB2D,QAAxB;;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIO,UAAU,CAACP,QAAD,EAAW;IACjB,KAAKH,aAAL,GAAqB,KAAKA,aAAL,IAAsB,EAA3C;;IACA,KAAKA,aAAL,CAAmBzF,OAAnB,CAA2B4F,QAA3B;;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIQ,MAAM,CAACR,QAAD,EAAW;IACb,IAAI,CAAC,KAAKH,aAAV,EAAyB;MACrB,OAAO,IAAP;IACH;;IACD,IAAIG,QAAJ,EAAc;MACV,MAAMF,SAAS,GAAG,KAAKD,aAAvB;;MACA,KAAK,IAAIlD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGmD,SAAS,CAACtE,MAA9B,EAAsCmB,CAAC,EAAvC,EAA2C;QACvC,IAAIqD,QAAQ,KAAKF,SAAS,CAACnD,CAAD,CAA1B,EAA+B;UAC3BmD,SAAS,CAAClD,MAAV,CAAiBD,CAAjB,EAAoB,CAApB;UACA,OAAO,IAAP;QACH;MACJ;IACJ,CARD,MASK;MACD,KAAKkD,aAAL,GAAqB,EAArB;IACH;;IACD,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIY,YAAY,GAAG;IACX,OAAO,KAAKZ,aAAL,IAAsB,EAA7B;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIa,aAAa,CAACV,QAAD,EAAW;IACpB,KAAKW,qBAAL,GAA6B,KAAKA,qBAAL,IAA8B,EAA3D;;IACA,KAAKA,qBAAL,CAA2BtE,IAA3B,CAAgC2D,QAAhC;;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIY,kBAAkB,CAACZ,QAAD,EAAW;IACzB,KAAKW,qBAAL,GAA6B,KAAKA,qBAAL,IAA8B,EAA3D;;IACA,KAAKA,qBAAL,CAA2BvG,OAA3B,CAAmC4F,QAAnC;;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIa,cAAc,CAACb,QAAD,EAAW;IACrB,IAAI,CAAC,KAAKW,qBAAV,EAAiC;MAC7B,OAAO,IAAP;IACH;;IACD,IAAIX,QAAJ,EAAc;MACV,MAAMF,SAAS,GAAG,KAAKa,qBAAvB;;MACA,KAAK,IAAIhE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGmD,SAAS,CAACtE,MAA9B,EAAsCmB,CAAC,EAAvC,EAA2C;QACvC,IAAIqD,QAAQ,KAAKF,SAAS,CAACnD,CAAD,CAA1B,EAA+B;UAC3BmD,SAAS,CAAClD,MAAV,CAAiBD,CAAjB,EAAoB,CAApB;UACA,OAAO,IAAP;QACH;MACJ;IACJ,CARD,MASK;MACD,KAAKgE,qBAAL,GAA6B,EAA7B;IACH;;IACD,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIG,oBAAoB,GAAG;IACnB,OAAO,KAAKH,qBAAL,IAA8B,EAArC;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACIvE,uBAAuB,CAAClB,MAAD,EAAS;IAC5B,IAAI,KAAKyF,qBAAL,IAA8B,KAAKA,qBAAL,CAA2BnF,MAA7D,EAAqE;MACjE,MAAMsE,SAAS,GAAG,KAAKa,qBAAL,CAA2BZ,KAA3B,EAAlB;;MACA,KAAK,MAAMC,QAAX,IAAuBF,SAAvB,EAAkC;QAC9BE,QAAQ,CAAC1F,KAAT,CAAe,IAAf,EAAqBY,MAAM,CAACG,IAA5B;MACH;IACJ;EACJ;;AAx0B+B", "ignoreList": []}, "metadata": {}, "sourceType": "module"}