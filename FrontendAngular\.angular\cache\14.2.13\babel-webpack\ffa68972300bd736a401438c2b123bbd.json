{"ast": null, "code": "import { parse } from \"engine.io-client\";\n/**\n * URL parser.\n *\n * @param uri - url\n * @param path - the request path of the connection\n * @param loc - An object meant to mimic window.location.\n *        Defaults to window.location.\n * @public\n */\n\nexport function url(uri, path = \"\", loc) {\n  let obj = uri; // default to window.location\n\n  loc = loc || typeof location !== \"undefined\" && location;\n  if (null == uri) uri = loc.protocol + \"//\" + loc.host; // relative path support\n\n  if (typeof uri === \"string\") {\n    if (\"/\" === uri.charAt(0)) {\n      if (\"/\" === uri.charAt(1)) {\n        uri = loc.protocol + uri;\n      } else {\n        uri = loc.host + uri;\n      }\n    }\n\n    if (!/^(https?|wss?):\\/\\//.test(uri)) {\n      if (\"undefined\" !== typeof loc) {\n        uri = loc.protocol + \"//\" + uri;\n      } else {\n        uri = \"https://\" + uri;\n      }\n    } // parse\n\n\n    obj = parse(uri);\n  } // make sure we treat `localhost:80` and `localhost` equally\n\n\n  if (!obj.port) {\n    if (/^(http|ws)$/.test(obj.protocol)) {\n      obj.port = \"80\";\n    } else if (/^(http|ws)s$/.test(obj.protocol)) {\n      obj.port = \"443\";\n    }\n  }\n\n  obj.path = obj.path || \"/\";\n  const ipv6 = obj.host.indexOf(\":\") !== -1;\n  const host = ipv6 ? \"[\" + obj.host + \"]\" : obj.host; // define unique id\n\n  obj.id = obj.protocol + \"://\" + host + \":\" + obj.port + path; // define href\n\n  obj.href = obj.protocol + \"://\" + host + (loc && loc.port === obj.port ? \"\" : \":\" + obj.port);\n  return obj;\n}", "map": {"version": 3, "names": ["parse", "url", "uri", "path", "loc", "obj", "location", "protocol", "host", "char<PERSON>t", "test", "port", "ipv6", "indexOf", "id", "href"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/socket.io-client/build/esm/url.js"], "sourcesContent": ["import { parse } from \"engine.io-client\";\n/**\n * URL parser.\n *\n * @param uri - url\n * @param path - the request path of the connection\n * @param loc - An object meant to mimic window.location.\n *        Defaults to window.location.\n * @public\n */\nexport function url(uri, path = \"\", loc) {\n    let obj = uri;\n    // default to window.location\n    loc = loc || (typeof location !== \"undefined\" && location);\n    if (null == uri)\n        uri = loc.protocol + \"//\" + loc.host;\n    // relative path support\n    if (typeof uri === \"string\") {\n        if (\"/\" === uri.charAt(0)) {\n            if (\"/\" === uri.charAt(1)) {\n                uri = loc.protocol + uri;\n            }\n            else {\n                uri = loc.host + uri;\n            }\n        }\n        if (!/^(https?|wss?):\\/\\//.test(uri)) {\n            if (\"undefined\" !== typeof loc) {\n                uri = loc.protocol + \"//\" + uri;\n            }\n            else {\n                uri = \"https://\" + uri;\n            }\n        }\n        // parse\n        obj = parse(uri);\n    }\n    // make sure we treat `localhost:80` and `localhost` equally\n    if (!obj.port) {\n        if (/^(http|ws)$/.test(obj.protocol)) {\n            obj.port = \"80\";\n        }\n        else if (/^(http|ws)s$/.test(obj.protocol)) {\n            obj.port = \"443\";\n        }\n    }\n    obj.path = obj.path || \"/\";\n    const ipv6 = obj.host.indexOf(\":\") !== -1;\n    const host = ipv6 ? \"[\" + obj.host + \"]\" : obj.host;\n    // define unique id\n    obj.id = obj.protocol + \"://\" + host + \":\" + obj.port + path;\n    // define href\n    obj.href =\n        obj.protocol +\n            \"://\" +\n            host +\n            (loc && loc.port === obj.port ? \"\" : \":\" + obj.port);\n    return obj;\n}\n"], "mappings": "AAAA,SAASA,KAAT,QAAsB,kBAAtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,SAASC,GAAT,CAAaC,GAAb,EAAkBC,IAAI,GAAG,EAAzB,EAA6BC,GAA7B,EAAkC;EACrC,IAAIC,GAAG,GAAGH,GAAV,CADqC,CAErC;;EACAE,GAAG,GAAGA,GAAG,IAAK,OAAOE,QAAP,KAAoB,WAApB,IAAmCA,QAAjD;EACA,IAAI,QAAQJ,GAAZ,EACIA,GAAG,GAAGE,GAAG,CAACG,QAAJ,GAAe,IAAf,GAAsBH,GAAG,CAACI,IAAhC,CALiC,CAMrC;;EACA,IAAI,OAAON,GAAP,KAAe,QAAnB,EAA6B;IACzB,IAAI,QAAQA,GAAG,CAACO,MAAJ,CAAW,CAAX,CAAZ,EAA2B;MACvB,IAAI,QAAQP,GAAG,CAACO,MAAJ,CAAW,CAAX,CAAZ,EAA2B;QACvBP,GAAG,GAAGE,GAAG,CAACG,QAAJ,GAAeL,GAArB;MACH,CAFD,MAGK;QACDA,GAAG,GAAGE,GAAG,CAACI,IAAJ,GAAWN,GAAjB;MACH;IACJ;;IACD,IAAI,CAAC,sBAAsBQ,IAAtB,CAA2BR,GAA3B,CAAL,EAAsC;MAClC,IAAI,gBAAgB,OAAOE,GAA3B,EAAgC;QAC5BF,GAAG,GAAGE,GAAG,CAACG,QAAJ,GAAe,IAAf,GAAsBL,GAA5B;MACH,CAFD,MAGK;QACDA,GAAG,GAAG,aAAaA,GAAnB;MACH;IACJ,CAhBwB,CAiBzB;;;IACAG,GAAG,GAAGL,KAAK,CAACE,GAAD,CAAX;EACH,CA1BoC,CA2BrC;;;EACA,IAAI,CAACG,GAAG,CAACM,IAAT,EAAe;IACX,IAAI,cAAcD,IAAd,CAAmBL,GAAG,CAACE,QAAvB,CAAJ,EAAsC;MAClCF,GAAG,CAACM,IAAJ,GAAW,IAAX;IACH,CAFD,MAGK,IAAI,eAAeD,IAAf,CAAoBL,GAAG,CAACE,QAAxB,CAAJ,EAAuC;MACxCF,GAAG,CAACM,IAAJ,GAAW,KAAX;IACH;EACJ;;EACDN,GAAG,CAACF,IAAJ,GAAWE,GAAG,CAACF,IAAJ,IAAY,GAAvB;EACA,MAAMS,IAAI,GAAGP,GAAG,CAACG,IAAJ,CAASK,OAAT,CAAiB,GAAjB,MAA0B,CAAC,CAAxC;EACA,MAAML,IAAI,GAAGI,IAAI,GAAG,MAAMP,GAAG,CAACG,IAAV,GAAiB,GAApB,GAA0BH,GAAG,CAACG,IAA/C,CAtCqC,CAuCrC;;EACAH,GAAG,CAACS,EAAJ,GAAST,GAAG,CAACE,QAAJ,GAAe,KAAf,GAAuBC,IAAvB,GAA8B,GAA9B,GAAoCH,GAAG,CAACM,IAAxC,GAA+CR,IAAxD,CAxCqC,CAyCrC;;EACAE,GAAG,CAACU,IAAJ,GACIV,GAAG,CAACE,QAAJ,GACI,KADJ,GAEIC,IAFJ,IAGKJ,GAAG,IAAIA,GAAG,CAACO,IAAJ,KAAaN,GAAG,CAACM,IAAxB,GAA+B,EAA/B,GAAoC,MAAMN,GAAG,CAACM,IAHnD,CADJ;EAKA,OAAON,GAAP;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}