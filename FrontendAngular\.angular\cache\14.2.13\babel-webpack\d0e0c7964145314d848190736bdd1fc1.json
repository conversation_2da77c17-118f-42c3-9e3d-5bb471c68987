{"ast": null, "code": "import _asyncToGenerator from \"R:/chateye/FrontendAngular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { ApiService, Message, Group, User, SecurityInfo } from './api.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./api.service\";\nimport * as i2 from \"./socket.service\";\nimport * as i3 from \"./notification.service\";\nexport class ChatService {\n  constructor(apiService, socketService, notificationService) {\n    this.apiService = apiService;\n    this.socketService = socketService;\n    this.notificationService = notificationService;\n    this.userSubject = new BehaviorSubject(null);\n    this.messagesSubject = new BehaviorSubject([]);\n    this.onlineUsersSubject = new BehaviorSubject([]);\n    this.groupsSubject = new BehaviorSubject([]);\n    this.currentGroupSubject = new BehaviorSubject(null);\n    this.replyToSubject = new BehaviorSubject(null);\n    this.loadingSubject = new BehaviorSubject(false);\n    this.isAdminSubject = new BehaviorSubject(false);\n    this.showAdminPanelSubject = new BehaviorSubject(false);\n    this.securityInfoSubject = new BehaviorSubject(null);\n    this.editingMessageSubject = new BehaviorSubject(null); // Public observables\n\n    this.user$ = this.userSubject.asObservable();\n    this.messages$ = this.messagesSubject.asObservable();\n    this.onlineUsers$ = this.onlineUsersSubject.asObservable();\n    this.groups$ = this.groupsSubject.asObservable();\n    this.currentGroup$ = this.currentGroupSubject.asObservable();\n    this.replyTo$ = this.replyToSubject.asObservable();\n    this.loading$ = this.loadingSubject.asObservable();\n    this.isAdmin$ = this.isAdminSubject.asObservable();\n    this.showAdminPanel$ = this.showAdminPanelSubject.asObservable();\n    this.securityInfo$ = this.securityInfoSubject.asObservable();\n    this.editingMessage$ = this.editingMessageSubject.asObservable();\n    this.connected$ = this.socketService.isConnected$; // Computed observables\n\n    this.isLoggedIn$ = this.user$.pipe(map(user => !!user));\n    this.setupSocketListeners();\n    this.setupMessageRefresh();\n  }\n\n  setupSocketListeners() {\n    this.socketService.on('connect', () => {\n      console.log('Connected to server'); // Clear any loading states when connected\n\n      this.loadingSubject.next(false);\n    });\n    this.socketService.on('disconnect', () => {\n      console.log('Disconnected from server'); // Don't set loading to true on disconnect to avoid UI freeze\n    });\n    this.socketService.on('connect_error', error => {\n      console.error('Socket connection error:', error);\n      this.loadingSubject.next(false);\n    });\n    this.socketService.on('userGroups', userGroups => {\n      console.log('Received user groups:', userGroups);\n      this.groupsSubject.next(userGroups || []);\n\n      if (userGroups && userGroups.length > 0) {\n        console.log('Auto-selecting first group:', userGroups[0]);\n        this.currentGroupSubject.next(userGroups[0]); // Load messages for the selected group\n\n        this.loadRecentMessages(userGroups[0].id);\n      } else {\n        console.log('No groups available for user'); // Clear current group and messages if no groups\n\n        this.currentGroupSubject.next(null);\n        this.messagesSubject.next([]);\n      }\n    });\n    this.socketService.on('recentMessages', messages => {\n      console.log('Received recent messages:', messages.length);\n      this.messagesSubject.next(messages || []);\n    });\n    this.socketService.on('groupJoined', ({\n      groupId\n    }) => {\n      console.log('Joined group:', groupId);\n      const groups = this.groupsSubject.value;\n      const group = groups.find(g => g.id === groupId);\n\n      if (group) {\n        this.currentGroupSubject.next(group);\n      }\n    });\n    this.socketService.on('newMessage', message => {\n      console.log('New message received:', message);\n      const currentMessages = this.messagesSubject.value;\n      this.messagesSubject.next([...currentMessages, message]); // Show notification if not current user and window not focused\n\n      const currentUser = this.userSubject.value;\n\n      if (message.username !== currentUser && document.hidden) {\n        this.notificationService.showMessageNotification(message.username, message.text);\n      }\n    });\n    this.socketService.on('reactionUpdate', ({\n      messageId,\n      reactions\n    }) => {\n      console.log('Reaction update:', messageId, reactions);\n      const currentMessages = this.messagesSubject.value;\n      this.messagesSubject.next(currentMessages.map(msg => msg.id === messageId ? { ...msg,\n        reactions\n      } : msg));\n    });\n    this.socketService.on('onlineUsersUpdate', users => {\n      console.log('Online users updated:', users);\n      this.onlineUsersSubject.next(users || []);\n    });\n    this.socketService.on('userJoined', ({\n      username\n    }) => {\n      console.log('User joined:', username); // Online users will be updated via onlineUsersUpdate event\n    });\n    this.socketService.on('userLeft', ({\n      username\n    }) => {\n      console.log('User left:', username); // Online users will be updated via onlineUsersUpdate event\n    });\n    this.socketService.on('messageUpdated', ({\n      messageId,\n      newText,\n      updatedAt\n    }) => {\n      console.log('Message updated:', messageId, newText);\n      const currentMessages = this.messagesSubject.value;\n      this.messagesSubject.next(currentMessages.map(msg => msg.id === messageId ? { ...msg,\n        text: newText,\n        updated_at: updatedAt\n      } : msg));\n    });\n    this.socketService.on('messageDeleted', ({\n      messageId\n    }) => {\n      console.log('Message deleted:', messageId);\n      const currentMessages = this.messagesSubject.value;\n      this.messagesSubject.next(currentMessages.filter(msg => msg.id !== messageId));\n    });\n    this.socketService.on('error', error => {\n      console.error('Socket error:', error);\n    });\n  }\n\n  setupMessageRefresh() {\n    // Refresh messages every 5 seconds if socket is not connected\n    this.messageRefreshInterval = setInterval(() => {\n      if (!this.socketService.isConnectedSubject.value) {\n        const currentGroup = this.currentGroupSubject.value;\n\n        if (currentGroup) {\n          console.log('Refreshing messages via HTTP API');\n          this.loadRecentMessages(currentGroup.id);\n        }\n      }\n    }, 5000);\n  }\n\n  login(_x, _x2) {\n    var _this = this;\n\n    return _asyncToGenerator(function* (username, password, inviteCode = null) {\n      try {\n        _this.loadingSubject.next(true);\n\n        console.log('Starting login process for:', username);\n        const userData = yield _this.apiService.loginUser(username, password, inviteCode || undefined).toPromise();\n        console.log('Login API response:', userData); // Check if password is required\n\n        if (userData?.requiresPassword) {\n          throw new Error('Password required for this user');\n        } // Connect to socket with auth data\n\n\n        console.log('Connecting to socket...');\n\n        _this.socketService.connect(username, password, inviteCode);\n\n        _this.userSubject.next(username);\n\n        _this.isAdminSubject.next(userData?.isAdmin || false);\n\n        _this.securityInfoSubject.next(userData?.securityInfo || null);\n\n        console.log('Login completed successfully'); // Wait for socket connection before loading groups\n        // The socket will emit 'userGroups' event which will handle group loading\n        // This prevents race conditions between API and socket calls\n      } catch (error) {\n        console.error('Login failed:', error);\n        throw error;\n      } finally {\n        _this.loadingSubject.next(false);\n      }\n    }).apply(this, arguments);\n  }\n\n  loadRecentMessages(groupId) {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        console.log('Loading recent messages for group:', groupId);\n        const messages = yield _this2.apiService.getMessages(groupId, 50).toPromise();\n        console.log('Loaded messages:', messages?.length || 0);\n\n        _this2.messagesSubject.next(messages || []);\n      } catch (error) {\n        console.error('Failed to load messages:', error); // Set empty array to prevent UI from showing stale data\n\n        _this2.messagesSubject.next([]);\n      }\n    })();\n  }\n\n  loadUserGroups(username) {\n    var _this3 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        console.log('Loading user groups for:', username);\n        const groups = yield _this3.apiService.getUserGroups(username).toPromise();\n        console.log('Loaded user groups:', groups);\n\n        _this3.groupsSubject.next(groups || []);\n\n        if (groups && groups.length > 0) {\n          console.log('Auto-selecting first group:', groups[0]);\n\n          _this3.currentGroupSubject.next(groups[0]);\n\n          _this3.loadRecentMessages(groups[0].id);\n        }\n      } catch (error) {\n        console.error('Failed to load user groups:', error);\n      }\n    })();\n  }\n\n  joinGroup(groupId) {\n    const currentUser = this.userSubject.value;\n\n    if (!groupId || !currentUser) {\n      console.error('Cannot join group - missing groupId or user');\n      return;\n    }\n\n    console.log('Joining group:', groupId); // Check if socket is connected before joining\n\n    if (!this.socketService.isConnectedSubject.value) {\n      console.error('Cannot join group - socket not connected');\n      return;\n    }\n\n    this.socketService.joinGroup(groupId);\n    const groups = this.groupsSubject.value;\n    const group = groups.find(g => g.id === groupId);\n\n    if (group) {\n      this.currentGroupSubject.next(group); // Load recent messages for the group\n\n      this.loadRecentMessages(groupId);\n    } else {\n      console.error('Group not found in user groups:', groupId);\n    }\n  }\n\n  sendMessage(text, replyToId = null) {\n    const currentUser = this.userSubject.value;\n    const currentGroup = this.currentGroupSubject.value;\n\n    if (!text.trim()) {\n      console.error('Cannot send message - empty text');\n      return;\n    }\n\n    if (!currentUser) {\n      console.error('Cannot send message - user not logged in');\n      return;\n    }\n\n    if (!currentGroup) {\n      console.error('Cannot send message - no group selected. Available groups:', this.groupsSubject.value); // Try to auto-select the first available group\n\n      const groups = this.groupsSubject.value;\n\n      if (groups && groups.length > 0) {\n        console.log('Auto-selecting first available group:', groups[0]);\n        this.currentGroupSubject.next(groups[0]);\n        this.loadRecentMessages(groups[0].id); // Retry sending the message\n\n        setTimeout(() => this.sendMessage(text, replyToId), 100);\n        return;\n      } else {\n        console.error('No groups available for user');\n        return;\n      }\n    }\n\n    console.log('Sending message via socket:', {\n      text,\n      groupId: currentGroup.id,\n      replyToId\n    });\n    this.socketService.sendMessage(text, currentGroup.id, replyToId); // Fallback: If socket is not connected, try HTTP API\n\n    if (!this.socketService.isConnectedSubject.value) {\n      console.log('Socket not connected, trying HTTP API fallback');\n      this.apiService.sendMessage(text, currentUser, currentGroup.id, replyToId).subscribe({\n        next: response => {\n          console.log('Message sent via HTTP API:', response);\n        },\n        error: error => {\n          console.error('Failed to send message via HTTP API:', error);\n        }\n      });\n    }\n  }\n\n  replyToMessage(message) {\n    this.replyToSubject.next(message);\n  }\n\n  cancelReply() {\n    this.replyToSubject.next(null);\n  }\n\n  addReaction(messageId, emoji) {\n    this.socketService.addReaction(messageId, emoji);\n  }\n\n  removeReaction(data) {\n    this.socketService.removeReaction(data);\n  }\n\n  updateMessage(messageId, newText) {\n    var _this4 = this;\n\n    return _asyncToGenerator(function* () {\n      const currentUser = _this4.userSubject.value;\n\n      if (!currentUser) {\n        console.error('Cannot update message - user not logged in');\n        return;\n      }\n\n      try {\n        // Update the message in the local state immediately for better UX\n        const currentMessages = _this4.messagesSubject.value;\n\n        _this4.messagesSubject.next(currentMessages.map(msg => msg.id === messageId ? { ...msg,\n          text: newText,\n          updated_at: new Date().toISOString()\n        } : msg)); // Emit socket event for real-time updates\n\n\n        if (_this4.socketService.isConnectedSubject.value) {\n          _this4.socketService.emit('messageUpdated', {\n            messageId,\n            newText\n          });\n        } else {\n          // Fallback to HTTP API if socket not connected\n          const updatedMessage = yield _this4.apiService.updateMessage(messageId, newText, currentUser).toPromise();\n\n          _this4.messagesSubject.next(currentMessages.map(msg => msg.id === messageId ? { ...msg,\n            text: newText,\n            updated_at: updatedMessage.updated_at\n          } : msg));\n        }\n      } catch (error) {\n        console.error('Failed to update message:', error);\n        throw error;\n      }\n    })();\n  }\n\n  deleteMessage(messageId) {\n    var _this5 = this;\n\n    return _asyncToGenerator(function* () {\n      const currentUser = _this5.userSubject.value;\n\n      if (!currentUser) {\n        console.error('Cannot delete message - user not logged in');\n        return;\n      }\n\n      try {\n        // Remove the message from local state immediately for better UX\n        const currentMessages = _this5.messagesSubject.value;\n\n        _this5.messagesSubject.next(currentMessages.filter(msg => msg.id !== messageId)); // Emit socket event for real-time updates\n\n\n        if (_this5.socketService.isConnectedSubject.value) {\n          _this5.socketService.emit('messageDeleted', {\n            messageId\n          });\n        } else {\n          // Fallback to HTTP API if socket not connected\n          yield _this5.apiService.deleteMessage(messageId, currentUser).toPromise();\n        }\n      } catch (error) {\n        console.error('Failed to delete message:', error);\n        throw error;\n      }\n    })();\n  }\n\n  showAdminPanel() {\n    this.showAdminPanelSubject.next(true);\n  }\n\n  hideAdminPanel() {\n    this.showAdminPanelSubject.next(false);\n  }\n\n  startEditingMessage(message) {\n    this.editingMessageSubject.next(message);\n  }\n\n  cancelEditingMessage() {\n    this.editingMessageSubject.next(null);\n  }\n\n  logout() {\n    this.socketService.disconnect();\n\n    if (this.messageRefreshInterval) {\n      clearInterval(this.messageRefreshInterval);\n    }\n\n    this.userSubject.next(null);\n    this.messagesSubject.next([]);\n    this.onlineUsersSubject.next([]);\n    this.groupsSubject.next([]);\n    this.currentGroupSubject.next(null);\n    this.replyToSubject.next(null);\n    this.loadingSubject.next(false);\n    this.isAdminSubject.next(false);\n    this.showAdminPanelSubject.next(false);\n    this.securityInfoSubject.next(null);\n    this.editingMessageSubject.next(null);\n  }\n\n}\n\nChatService.ɵfac = function ChatService_Factory(t) {\n  return new (t || ChatService)(i0.ɵɵinject(i1.ApiService), i0.ɵɵinject(i2.SocketService), i0.ɵɵinject(i3.NotificationService));\n};\n\nChatService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: ChatService,\n  factory: ChatService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "mappings": ";AACA,SAASA,eAAT,QAA2D,MAA3D;AACA,SAASC,GAAT,QAAoB,gBAApB;AACA,SAASC,UAAT,EAAqBC,OAArB,EAA8BC,KAA9B,EAAqCC,IAArC,EAA2CC,YAA3C,QAA8E,eAA9E;;;;;AAOA,OAAM,MAAOC,WAAP,CAAkB;EAgCtBC,YACUC,UADV,EAEUC,aAFV,EAGUC,mBAHV,EAGkD;IAFxC;IACA;IACA;IAlCF,mBAAc,IAAIX,eAAJ,CAAmC,IAAnC,CAAd;IACA,uBAAkB,IAAIA,eAAJ,CAA+B,EAA/B,CAAlB;IACA,0BAAqB,IAAIA,eAAJ,CAA4B,EAA5B,CAArB;IACA,qBAAgB,IAAIA,eAAJ,CAA6B,EAA7B,CAAhB;IACA,2BAAsB,IAAIA,eAAJ,CAAkC,IAAlC,CAAtB;IACA,sBAAiB,IAAIA,eAAJ,CAAoC,IAApC,CAAjB;IACA,sBAAiB,IAAIA,eAAJ,CAA6B,KAA7B,CAAjB;IACA,sBAAiB,IAAIA,eAAJ,CAA6B,KAA7B,CAAjB;IACA,6BAAwB,IAAIA,eAAJ,CAA6B,KAA7B,CAAxB;IACA,2BAAsB,IAAIA,eAAJ,CAAyC,IAAzC,CAAtB;IACA,6BAAwB,IAAIA,eAAJ,CAAoC,IAApC,CAAxB,CAwB0C,CAtBlD;;IACO,aAAQ,KAAKY,WAAL,CAAiBC,YAAjB,EAAR;IACA,iBAAY,KAAKC,eAAL,CAAqBD,YAArB,EAAZ;IACA,oBAAe,KAAKE,kBAAL,CAAwBF,YAAxB,EAAf;IACA,eAAU,KAAKG,aAAL,CAAmBH,YAAnB,EAAV;IACA,qBAAgB,KAAKI,mBAAL,CAAyBJ,YAAzB,EAAhB;IACA,gBAAW,KAAKK,cAAL,CAAoBL,YAApB,EAAX;IACA,gBAAW,KAAKM,cAAL,CAAoBN,YAApB,EAAX;IACA,gBAAW,KAAKO,cAAL,CAAoBP,YAApB,EAAX;IACA,uBAAkB,KAAKQ,qBAAL,CAA2BR,YAA3B,EAAlB;IACA,qBAAgB,KAAKS,mBAAL,CAAyBT,YAAzB,EAAhB;IACA,uBAAkB,KAAKU,qBAAL,CAA2BV,YAA3B,EAAlB;IACA,kBAAa,KAAKH,aAAL,CAAmBc,YAAhC,CAU2C,CARlD;;IACO,mBAAc,KAAKC,KAAL,CAAWC,IAAX,CAAgBzB,GAAG,CAAC0B,IAAI,IAAI,CAAC,CAACA,IAAX,CAAnB,CAAd;IASL,KAAKC,oBAAL;IACA,KAAKC,mBAAL;EACD;;EAEOD,oBAAoB;IAC1B,KAAKlB,aAAL,CAAmBoB,EAAnB,CAAsB,SAAtB,EAAiC,MAAK;MACpCC,OAAO,CAACC,GAAR,CAAY,qBAAZ,EADoC,CAEpC;;MACA,KAAKb,cAAL,CAAoBc,IAApB,CAAyB,KAAzB;IACD,CAJD;IAMA,KAAKvB,aAAL,CAAmBoB,EAAnB,CAAsB,YAAtB,EAAoC,MAAK;MACvCC,OAAO,CAACC,GAAR,CAAY,0BAAZ,EADuC,CAEvC;IACD,CAHD;IAKA,KAAKtB,aAAL,CAAmBoB,EAAnB,CAAsB,eAAtB,EAAwCI,KAAD,IAAU;MAC/CH,OAAO,CAACG,KAAR,CAAc,0BAAd,EAA0CA,KAA1C;MACA,KAAKf,cAAL,CAAoBc,IAApB,CAAyB,KAAzB;IACD,CAHD;IAKA,KAAKvB,aAAL,CAAmBoB,EAAnB,CAAsB,YAAtB,EAAqCK,UAAD,IAAwB;MAC1DJ,OAAO,CAACC,GAAR,CAAY,uBAAZ,EAAqCG,UAArC;MACA,KAAKnB,aAAL,CAAmBiB,IAAnB,CAAwBE,UAAU,IAAI,EAAtC;;MACA,IAAIA,UAAU,IAAIA,UAAU,CAACC,MAAX,GAAoB,CAAtC,EAAyC;QACvCL,OAAO,CAACC,GAAR,CAAY,6BAAZ,EAA2CG,UAAU,CAAC,CAAD,CAArD;QACA,KAAKlB,mBAAL,CAAyBgB,IAAzB,CAA8BE,UAAU,CAAC,CAAD,CAAxC,EAFuC,CAGvC;;QACA,KAAKE,kBAAL,CAAwBF,UAAU,CAAC,CAAD,CAAV,CAAcG,EAAtC;MACD,CALD,MAKO;QACLP,OAAO,CAACC,GAAR,CAAY,8BAAZ,EADK,CAEL;;QACA,KAAKf,mBAAL,CAAyBgB,IAAzB,CAA8B,IAA9B;QACA,KAAKnB,eAAL,CAAqBmB,IAArB,CAA0B,EAA1B;MACD;IACF,CAdD;IAgBA,KAAKvB,aAAL,CAAmBoB,EAAnB,CAAsB,gBAAtB,EAAyCS,QAAD,IAAwB;MAC9DR,OAAO,CAACC,GAAR,CAAY,2BAAZ,EAAyCO,QAAQ,CAACH,MAAlD;MACA,KAAKtB,eAAL,CAAqBmB,IAArB,CAA0BM,QAAQ,IAAI,EAAtC;IACD,CAHD;IAKA,KAAK7B,aAAL,CAAmBoB,EAAnB,CAAsB,aAAtB,EAAqC,CAAC;MAAEU;IAAF,CAAD,KAAqC;MACxET,OAAO,CAACC,GAAR,CAAY,eAAZ,EAA6BQ,OAA7B;MACA,MAAMC,MAAM,GAAG,KAAKzB,aAAL,CAAmB0B,KAAlC;MACA,MAAMC,KAAK,GAAGF,MAAM,CAACG,IAAP,CAAYC,CAAC,IAAIA,CAAC,CAACP,EAAF,KAASE,OAA1B,CAAd;;MACA,IAAIG,KAAJ,EAAW;QACT,KAAK1B,mBAAL,CAAyBgB,IAAzB,CAA8BU,KAA9B;MACD;IACF,CAPD;IASA,KAAKjC,aAAL,CAAmBoB,EAAnB,CAAsB,YAAtB,EAAqCgB,OAAD,IAAqB;MACvDf,OAAO,CAACC,GAAR,CAAY,uBAAZ,EAAqCc,OAArC;MACA,MAAMC,eAAe,GAAG,KAAKjC,eAAL,CAAqB4B,KAA7C;MACA,KAAK5B,eAAL,CAAqBmB,IAArB,CAA0B,CAAC,GAAGc,eAAJ,EAAqBD,OAArB,CAA1B,EAHuD,CAKvD;;MACA,MAAME,WAAW,GAAG,KAAKpC,WAAL,CAAiB8B,KAArC;;MACA,IAAII,OAAO,CAACG,QAAR,KAAqBD,WAArB,IAAoCE,QAAQ,CAACC,MAAjD,EAAyD;QACvD,KAAKxC,mBAAL,CAAyByC,uBAAzB,CAAiDN,OAAO,CAACG,QAAzD,EAAmEH,OAAO,CAACO,IAA3E;MACD;IACF,CAVD;IAYA,KAAK3C,aAAL,CAAmBoB,EAAnB,CAAsB,gBAAtB,EAAwC,CAAC;MAAEwB,SAAF;MAAaC;IAAb,CAAD,KAAsE;MAC5GxB,OAAO,CAACC,GAAR,CAAY,kBAAZ,EAAgCsB,SAAhC,EAA2CC,SAA3C;MACA,MAAMR,eAAe,GAAG,KAAKjC,eAAL,CAAqB4B,KAA7C;MACA,KAAK5B,eAAL,CAAqBmB,IAArB,CACEc,eAAe,CAAC9C,GAAhB,CAAoBuD,GAAG,IACrBA,GAAG,CAAClB,EAAJ,KAAWgB,SAAX,GACI,EAAE,GAAGE,GAAL;QAAUD;MAAV,CADJ,GAEIC,GAHN,CADF;IAOD,CAVD;IAYA,KAAK9C,aAAL,CAAmBoB,EAAnB,CAAsB,mBAAtB,EAA4C2B,KAAD,IAAkB;MAC3D1B,OAAO,CAACC,GAAR,CAAY,uBAAZ,EAAqCyB,KAArC;MACA,KAAK1C,kBAAL,CAAwBkB,IAAxB,CAA6BwB,KAAK,IAAI,EAAtC;IACD,CAHD;IAKA,KAAK/C,aAAL,CAAmBoB,EAAnB,CAAsB,YAAtB,EAAoC,CAAC;MAAEmB;IAAF,CAAD,KAAuC;MACzElB,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA4BiB,QAA5B,EADyE,CAEzE;IACD,CAHD;IAKA,KAAKvC,aAAL,CAAmBoB,EAAnB,CAAsB,UAAtB,EAAkC,CAAC;MAAEmB;IAAF,CAAD,KAAuC;MACvElB,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0BiB,QAA1B,EADuE,CAEvE;IACD,CAHD;IAKA,KAAKvC,aAAL,CAAmBoB,EAAnB,CAAsB,gBAAtB,EAAwC,CAAC;MAAEwB,SAAF;MAAaI,OAAb;MAAsBC;IAAtB,CAAD,KAAiG;MACvI5B,OAAO,CAACC,GAAR,CAAY,kBAAZ,EAAgCsB,SAAhC,EAA2CI,OAA3C;MACA,MAAMX,eAAe,GAAG,KAAKjC,eAAL,CAAqB4B,KAA7C;MACA,KAAK5B,eAAL,CAAqBmB,IAArB,CACEc,eAAe,CAAC9C,GAAhB,CAAoBuD,GAAG,IACrBA,GAAG,CAAClB,EAAJ,KAAWgB,SAAX,GACI,EAAE,GAAGE,GAAL;QAAUH,IAAI,EAAEK,OAAhB;QAAyBE,UAAU,EAAED;MAArC,CADJ,GAEIH,GAHN,CADF;IAOD,CAVD;IAYA,KAAK9C,aAAL,CAAmBoB,EAAnB,CAAsB,gBAAtB,EAAwC,CAAC;MAAEwB;IAAF,CAAD,KAAyC;MAC/EvB,OAAO,CAACC,GAAR,CAAY,kBAAZ,EAAgCsB,SAAhC;MACA,MAAMP,eAAe,GAAG,KAAKjC,eAAL,CAAqB4B,KAA7C;MACA,KAAK5B,eAAL,CAAqBmB,IAArB,CACEc,eAAe,CAACc,MAAhB,CAAuBL,GAAG,IAAIA,GAAG,CAAClB,EAAJ,KAAWgB,SAAzC,CADF;IAGD,CAND;IAQA,KAAK5C,aAAL,CAAmBoB,EAAnB,CAAsB,OAAtB,EAAgCI,KAAD,IAAe;MAC5CH,OAAO,CAACG,KAAR,CAAc,eAAd,EAA+BA,KAA/B;IACD,CAFD;EAGD;;EAEOL,mBAAmB;IACzB;IACA,KAAKiC,sBAAL,GAA8BC,WAAW,CAAC,MAAK;MAC7C,IAAI,CAAC,KAAKrD,aAAL,CAAmBsD,kBAAnB,CAAsCtB,KAA3C,EAAkD;QAChD,MAAMuB,YAAY,GAAG,KAAKhD,mBAAL,CAAyByB,KAA9C;;QACA,IAAIuB,YAAJ,EAAkB;UAChBlC,OAAO,CAACC,GAAR,CAAY,kCAAZ;UACA,KAAKK,kBAAL,CAAwB4B,YAAY,CAAC3B,EAArC;QACD;MACF;IACF,CARwC,EAQtC,IARsC,CAAzC;EASD;;EAEK4B,KAAK,UAAsE;IAAA;;IAAA,oCAArEjB,QAAqE,EAAnDkB,QAAmD,EAAhCC,aAA4B,IAAI;MAC/E,IAAI;QACF,KAAI,CAACjD,cAAL,CAAoBc,IAApB,CAAyB,IAAzB;;QACAF,OAAO,CAACC,GAAR,CAAY,6BAAZ,EAA2CiB,QAA3C;QAEA,MAAMoB,QAAQ,SAAS,KAAI,CAAC5D,UAAL,CAAgB6D,SAAhB,CAA0BrB,QAA1B,EAAoCkB,QAApC,EAA8CC,UAAU,IAAIG,SAA5D,EAAuEC,SAAvE,EAAvB;QACAzC,OAAO,CAACC,GAAR,CAAY,qBAAZ,EAAmCqC,QAAnC,EALE,CAOF;;QACA,IAAIA,QAAQ,EAAEI,gBAAd,EAAgC;UAC9B,MAAM,IAAIC,KAAJ,CAAU,iCAAV,CAAN;QACD,CAVC,CAYF;;;QACA3C,OAAO,CAACC,GAAR,CAAY,yBAAZ;;QACA,KAAI,CAACtB,aAAL,CAAmBiE,OAAnB,CAA2B1B,QAA3B,EAAqCkB,QAArC,EAA+CC,UAA/C;;QAEA,KAAI,CAACxD,WAAL,CAAiBqB,IAAjB,CAAsBgB,QAAtB;;QACA,KAAI,CAAC7B,cAAL,CAAoBa,IAApB,CAAyBoC,QAAQ,EAAEO,OAAV,IAAqB,KAA9C;;QACA,KAAI,CAACtD,mBAAL,CAAyBW,IAAzB,CAA8BoC,QAAQ,EAAEQ,YAAV,IAA0B,IAAxD;;QAEA9C,OAAO,CAACC,GAAR,CAAY,8BAAZ,EApBE,CAsBF;QACA;QACA;MAED,CA1BD,CA0BE,OAAOE,KAAP,EAAc;QACdH,OAAO,CAACG,KAAR,CAAc,eAAd,EAA+BA,KAA/B;QACA,MAAMA,KAAN;MACD,CA7BD,SA6BU;QACR,KAAI,CAACf,cAAL,CAAoBc,IAApB,CAAyB,KAAzB;MACD;IAhC8E;EAiChF;;EAEKI,kBAAkB,CAACG,OAAD,EAAgB;IAAA;;IAAA;MACtC,IAAI;QACFT,OAAO,CAACC,GAAR,CAAY,oCAAZ,EAAkDQ,OAAlD;QACA,MAAMD,QAAQ,SAAS,MAAI,CAAC9B,UAAL,CAAgBqE,WAAhB,CAA4BtC,OAA5B,EAAqC,EAArC,EAAyCgC,SAAzC,EAAvB;QACAzC,OAAO,CAACC,GAAR,CAAY,kBAAZ,EAAgCO,QAAQ,EAAEH,MAAV,IAAoB,CAApD;;QACA,MAAI,CAACtB,eAAL,CAAqBmB,IAArB,CAA0BM,QAAQ,IAAI,EAAtC;MACD,CALD,CAKE,OAAOL,KAAP,EAAc;QACdH,OAAO,CAACG,KAAR,CAAc,0BAAd,EAA0CA,KAA1C,EADc,CAEd;;QACA,MAAI,CAACpB,eAAL,CAAqBmB,IAArB,CAA0B,EAA1B;MACD;IAVqC;EAWvC;;EAEK8C,cAAc,CAAC9B,QAAD,EAAiB;IAAA;;IAAA;MACnC,IAAI;QACFlB,OAAO,CAACC,GAAR,CAAY,0BAAZ,EAAwCiB,QAAxC;QACA,MAAMR,MAAM,SAAS,MAAI,CAAChC,UAAL,CAAgBuE,aAAhB,CAA8B/B,QAA9B,EAAwCuB,SAAxC,EAArB;QACAzC,OAAO,CAACC,GAAR,CAAY,qBAAZ,EAAmCS,MAAnC;;QACA,MAAI,CAACzB,aAAL,CAAmBiB,IAAnB,CAAwBQ,MAAM,IAAI,EAAlC;;QACA,IAAIA,MAAM,IAAIA,MAAM,CAACL,MAAP,GAAgB,CAA9B,EAAiC;UAC/BL,OAAO,CAACC,GAAR,CAAY,6BAAZ,EAA2CS,MAAM,CAAC,CAAD,CAAjD;;UACA,MAAI,CAACxB,mBAAL,CAAyBgB,IAAzB,CAA8BQ,MAAM,CAAC,CAAD,CAApC;;UACA,MAAI,CAACJ,kBAAL,CAAwBI,MAAM,CAAC,CAAD,CAAN,CAAUH,EAAlC;QACD;MACF,CAVD,CAUE,OAAOJ,KAAP,EAAc;QACdH,OAAO,CAACG,KAAR,CAAc,6BAAd,EAA6CA,KAA7C;MACD;IAbkC;EAcpC;;EAED+C,SAAS,CAACzC,OAAD,EAAgB;IACvB,MAAMQ,WAAW,GAAG,KAAKpC,WAAL,CAAiB8B,KAArC;;IACA,IAAI,CAACF,OAAD,IAAY,CAACQ,WAAjB,EAA8B;MAC5BjB,OAAO,CAACG,KAAR,CAAc,6CAAd;MACA;IACD;;IAEDH,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA8BQ,OAA9B,EAPuB,CASvB;;IACA,IAAI,CAAC,KAAK9B,aAAL,CAAmBsD,kBAAnB,CAAsCtB,KAA3C,EAAkD;MAChDX,OAAO,CAACG,KAAR,CAAc,0CAAd;MACA;IACD;;IAED,KAAKxB,aAAL,CAAmBuE,SAAnB,CAA6BzC,OAA7B;IACA,MAAMC,MAAM,GAAG,KAAKzB,aAAL,CAAmB0B,KAAlC;IACA,MAAMC,KAAK,GAAGF,MAAM,CAACG,IAAP,CAAYC,CAAC,IAAIA,CAAC,CAACP,EAAF,KAASE,OAA1B,CAAd;;IACA,IAAIG,KAAJ,EAAW;MACT,KAAK1B,mBAAL,CAAyBgB,IAAzB,CAA8BU,KAA9B,EADS,CAET;;MACA,KAAKN,kBAAL,CAAwBG,OAAxB;IACD,CAJD,MAIO;MACLT,OAAO,CAACG,KAAR,CAAc,iCAAd,EAAiDM,OAAjD;IACD;EACF;;EAED0C,WAAW,CAAC7B,IAAD,EAAe8B,YAA2B,IAA1C,EAA8C;IACvD,MAAMnC,WAAW,GAAG,KAAKpC,WAAL,CAAiB8B,KAArC;IACA,MAAMuB,YAAY,GAAG,KAAKhD,mBAAL,CAAyByB,KAA9C;;IAEA,IAAI,CAACW,IAAI,CAAC+B,IAAL,EAAL,EAAkB;MAChBrD,OAAO,CAACG,KAAR,CAAc,kCAAd;MACA;IACD;;IAED,IAAI,CAACc,WAAL,EAAkB;MAChBjB,OAAO,CAACG,KAAR,CAAc,0CAAd;MACA;IACD;;IAED,IAAI,CAAC+B,YAAL,EAAmB;MACjBlC,OAAO,CAACG,KAAR,CAAc,4DAAd,EAA4E,KAAKlB,aAAL,CAAmB0B,KAA/F,EADiB,CAEjB;;MACA,MAAMD,MAAM,GAAG,KAAKzB,aAAL,CAAmB0B,KAAlC;;MACA,IAAID,MAAM,IAAIA,MAAM,CAACL,MAAP,GAAgB,CAA9B,EAAiC;QAC/BL,OAAO,CAACC,GAAR,CAAY,uCAAZ,EAAqDS,MAAM,CAAC,CAAD,CAA3D;QACA,KAAKxB,mBAAL,CAAyBgB,IAAzB,CAA8BQ,MAAM,CAAC,CAAD,CAApC;QACA,KAAKJ,kBAAL,CAAwBI,MAAM,CAAC,CAAD,CAAN,CAAUH,EAAlC,EAH+B,CAI/B;;QACA+C,UAAU,CAAC,MAAM,KAAKH,WAAL,CAAiB7B,IAAjB,EAAuB8B,SAAvB,CAAP,EAA0C,GAA1C,CAAV;QACA;MACD,CAPD,MAOO;QACLpD,OAAO,CAACG,KAAR,CAAc,8BAAd;QACA;MACD;IACF;;IAEDH,OAAO,CAACC,GAAR,CAAY,6BAAZ,EAA2C;MAAEqB,IAAF;MAAQb,OAAO,EAAEyB,YAAY,CAAC3B,EAA9B;MAAkC6C;IAAlC,CAA3C;IACA,KAAKzE,aAAL,CAAmBwE,WAAnB,CAA+B7B,IAA/B,EAAqCY,YAAY,CAAC3B,EAAlD,EAAsD6C,SAAtD,EAhCuD,CAkCvD;;IACA,IAAI,CAAC,KAAKzE,aAAL,CAAmBsD,kBAAnB,CAAsCtB,KAA3C,EAAkD;MAChDX,OAAO,CAACC,GAAR,CAAY,gDAAZ;MACA,KAAKvB,UAAL,CAAgByE,WAAhB,CAA4B7B,IAA5B,EAAkCL,WAAlC,EAA+CiB,YAAY,CAAC3B,EAA5D,EAAgE6C,SAAhE,EAA2EG,SAA3E,CAAqF;QACnFrD,IAAI,EAAGsD,QAAD,IAAa;UACjBxD,OAAO,CAACC,GAAR,CAAY,4BAAZ,EAA0CuD,QAA1C;QACD,CAHkF;QAInFrD,KAAK,EAAGA,KAAD,IAAU;UACfH,OAAO,CAACG,KAAR,CAAc,sCAAd,EAAsDA,KAAtD;QACD;MANkF,CAArF;IAQD;EACF;;EAEDsD,cAAc,CAAC1C,OAAD,EAAiB;IAC7B,KAAK5B,cAAL,CAAoBe,IAApB,CAAyBa,OAAzB;EACD;;EAED2C,WAAW;IACT,KAAKvE,cAAL,CAAoBe,IAApB,CAAyB,IAAzB;EACD;;EAEDyD,WAAW,CAACpC,SAAD,EAAoBqC,KAApB,EAAiC;IAC1C,KAAKjF,aAAL,CAAmBgF,WAAnB,CAA+BpC,SAA/B,EAA0CqC,KAA1C;EACD;;EAEDC,cAAc,CAACC,IAAD,EAA4C;IACxD,KAAKnF,aAAL,CAAmBkF,cAAnB,CAAkCC,IAAlC;EACD;;EAEKC,aAAa,CAACxC,SAAD,EAAoBI,OAApB,EAAmC;IAAA;;IAAA;MACpD,MAAMV,WAAW,GAAG,MAAI,CAACpC,WAAL,CAAiB8B,KAArC;;MACA,IAAI,CAACM,WAAL,EAAkB;QAChBjB,OAAO,CAACG,KAAR,CAAc,4CAAd;QACA;MACD;;MAED,IAAI;QACF;QACA,MAAMa,eAAe,GAAG,MAAI,CAACjC,eAAL,CAAqB4B,KAA7C;;QACA,MAAI,CAAC5B,eAAL,CAAqBmB,IAArB,CACEc,eAAe,CAAC9C,GAAhB,CAAoBuD,GAAG,IACrBA,GAAG,CAAClB,EAAJ,KAAWgB,SAAX,GACI,EAAE,GAAGE,GAAL;UAAUH,IAAI,EAAEK,OAAhB;UAAyBE,UAAU,EAAE,IAAImC,IAAJ,GAAWC,WAAX;QAArC,CADJ,GAEIxC,GAHN,CADF,EAHE,CAWF;;;QACA,IAAI,MAAI,CAAC9C,aAAL,CAAmBsD,kBAAnB,CAAsCtB,KAA1C,EAAiD;UAC/C,MAAI,CAAChC,aAAL,CAAmBuF,IAAnB,CAAwB,gBAAxB,EAA0C;YAAE3C,SAAF;YAAaI;UAAb,CAA1C;QACD,CAFD,MAEO;UACL;UACA,MAAMwC,cAAc,SAAS,MAAI,CAACzF,UAAL,CAAgBqF,aAAhB,CAA8BxC,SAA9B,EAAyCI,OAAzC,EAAkDV,WAAlD,EAA+DwB,SAA/D,EAA7B;;UACA,MAAI,CAAC1D,eAAL,CAAqBmB,IAArB,CACEc,eAAe,CAAC9C,GAAhB,CAAoBuD,GAAG,IACrBA,GAAG,CAAClB,EAAJ,KAAWgB,SAAX,GACI,EAAE,GAAGE,GAAL;YAAUH,IAAI,EAAEK,OAAhB;YAAyBE,UAAU,EAAEsC,cAAc,CAACtC;UAApD,CADJ,GAEIJ,GAHN,CADF;QAOD;MACF,CAzBD,CAyBE,OAAOtB,KAAP,EAAc;QACdH,OAAO,CAACG,KAAR,CAAc,2BAAd,EAA2CA,KAA3C;QACA,MAAMA,KAAN;MACD;IAnCmD;EAoCrD;;EAEKiE,aAAa,CAAC7C,SAAD,EAAkB;IAAA;;IAAA;MACnC,MAAMN,WAAW,GAAG,MAAI,CAACpC,WAAL,CAAiB8B,KAArC;;MACA,IAAI,CAACM,WAAL,EAAkB;QAChBjB,OAAO,CAACG,KAAR,CAAc,4CAAd;QACA;MACD;;MAED,IAAI;QACF;QACA,MAAMa,eAAe,GAAG,MAAI,CAACjC,eAAL,CAAqB4B,KAA7C;;QACA,MAAI,CAAC5B,eAAL,CAAqBmB,IAArB,CACEc,eAAe,CAACc,MAAhB,CAAuBL,GAAG,IAAIA,GAAG,CAAClB,EAAJ,KAAWgB,SAAzC,CADF,EAHE,CAOF;;;QACA,IAAI,MAAI,CAAC5C,aAAL,CAAmBsD,kBAAnB,CAAsCtB,KAA1C,EAAiD;UAC/C,MAAI,CAAChC,aAAL,CAAmBuF,IAAnB,CAAwB,gBAAxB,EAA0C;YAAE3C;UAAF,CAA1C;QACD,CAFD,MAEO;UACL;UACA,MAAM,MAAI,CAAC7C,UAAL,CAAgB0F,aAAhB,CAA8B7C,SAA9B,EAAyCN,WAAzC,EAAsDwB,SAAtD,EAAN;QACD;MACF,CAdD,CAcE,OAAOtC,KAAP,EAAc;QACdH,OAAO,CAACG,KAAR,CAAc,2BAAd,EAA2CA,KAA3C;QACA,MAAMA,KAAN;MACD;IAxBkC;EAyBpC;;EAEDkE,cAAc;IACZ,KAAK/E,qBAAL,CAA2BY,IAA3B,CAAgC,IAAhC;EACD;;EAEDoE,cAAc;IACZ,KAAKhF,qBAAL,CAA2BY,IAA3B,CAAgC,KAAhC;EACD;;EAEDqE,mBAAmB,CAACxD,OAAD,EAAiB;IAClC,KAAKvB,qBAAL,CAA2BU,IAA3B,CAAgCa,OAAhC;EACD;;EAEDyD,oBAAoB;IAClB,KAAKhF,qBAAL,CAA2BU,IAA3B,CAAgC,IAAhC;EACD;;EAEDuE,MAAM;IACJ,KAAK9F,aAAL,CAAmB+F,UAAnB;;IACA,IAAI,KAAK3C,sBAAT,EAAiC;MAC/B4C,aAAa,CAAC,KAAK5C,sBAAN,CAAb;IACD;;IACD,KAAKlD,WAAL,CAAiBqB,IAAjB,CAAsB,IAAtB;IACA,KAAKnB,eAAL,CAAqBmB,IAArB,CAA0B,EAA1B;IACA,KAAKlB,kBAAL,CAAwBkB,IAAxB,CAA6B,EAA7B;IACA,KAAKjB,aAAL,CAAmBiB,IAAnB,CAAwB,EAAxB;IACA,KAAKhB,mBAAL,CAAyBgB,IAAzB,CAA8B,IAA9B;IACA,KAAKf,cAAL,CAAoBe,IAApB,CAAyB,IAAzB;IACA,KAAKd,cAAL,CAAoBc,IAApB,CAAyB,KAAzB;IACA,KAAKb,cAAL,CAAoBa,IAApB,CAAyB,KAAzB;IACA,KAAKZ,qBAAL,CAA2BY,IAA3B,CAAgC,KAAhC;IACA,KAAKX,mBAAL,CAAyBW,IAAzB,CAA8B,IAA9B;IACA,KAAKV,qBAAL,CAA2BU,IAA3B,CAAgC,IAAhC;EACD;;AAjaqB;;;mBAAX1B,aAAWoG;AAAA;;;SAAXpG;EAAWqG,SAAXrG,WAAW;EAAAsG,YAFV", "names": ["BehaviorSubject", "map", "ApiService", "Message", "Group", "User", "SecurityInfo", "ChatService", "constructor", "apiService", "socketService", "notificationService", "userSubject", "asObservable", "messagesSubject", "onlineUsersSubject", "groupsSubject", "currentGroupSubject", "replyToSubject", "loadingSubject", "isAdminSubject", "showAdminPanelSubject", "securityInfoSubject", "editingMessageSubject", "isConnected$", "user$", "pipe", "user", "setupSocketListeners", "setupMessageRefresh", "on", "console", "log", "next", "error", "userGroups", "length", "loadRecentMessages", "id", "messages", "groupId", "groups", "value", "group", "find", "g", "message", "currentMessages", "currentUser", "username", "document", "hidden", "showMessageNotification", "text", "messageId", "reactions", "msg", "users", "newText", "updatedAt", "updated_at", "filter", "messageRefreshInterval", "setInterval", "isConnectedSubject", "currentGroup", "login", "password", "inviteCode", "userData", "loginUser", "undefined", "to<PERSON>romise", "requiresPassword", "Error", "connect", "isAdmin", "securityInfo", "getMessages", "loadUserGroups", "getUserGroups", "joinGroup", "sendMessage", "replyToId", "trim", "setTimeout", "subscribe", "response", "replyToMessage", "cancelReply", "addReaction", "emoji", "removeReaction", "data", "updateMessage", "Date", "toISOString", "emit", "updatedMessage", "deleteMessage", "showAdminPanel", "hideAdminPanel", "startEditingMessage", "cancelEditingMessage", "logout", "disconnect", "clearInterval", "i0", "factory", "providedIn"], "sourceRoot": "", "sources": ["R:\\chateye\\FrontendAngular\\src\\app\\services\\chat.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable, combineLatest } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\nimport { ApiService, Message, Group, User, SecurityInfo, LoginResponse } from './api.service';\r\nimport { SocketService } from './socket.service';\r\nimport { NotificationService } from './notification.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ChatService {\r\n  private userSubject = new BehaviorSubject<string | null>(null);\r\n  private messagesSubject = new BehaviorSubject<Message[]>([]);\r\n  private onlineUsersSubject = new BehaviorSubject<User[]>([]);\r\n  private groupsSubject = new BehaviorSubject<Group[]>([]);\r\n  private currentGroupSubject = new BehaviorSubject<Group | null>(null);\r\n  private replyToSubject = new BehaviorSubject<Message | null>(null);\r\n  private loadingSubject = new BehaviorSubject<boolean>(false);\r\n  private isAdminSubject = new BehaviorSubject<boolean>(false);\r\n  private showAdminPanelSubject = new BehaviorSubject<boolean>(false);\r\n  private securityInfoSubject = new BehaviorSubject<SecurityInfo | null>(null);\r\n  private editingMessageSubject = new BehaviorSubject<Message | null>(null);\r\n\r\n  // Public observables\r\n  public user$ = this.userSubject.asObservable();\r\n  public messages$ = this.messagesSubject.asObservable();\r\n  public onlineUsers$ = this.onlineUsersSubject.asObservable();\r\n  public groups$ = this.groupsSubject.asObservable();\r\n  public currentGroup$ = this.currentGroupSubject.asObservable();\r\n  public replyTo$ = this.replyToSubject.asObservable();\r\n  public loading$ = this.loadingSubject.asObservable();\r\n  public isAdmin$ = this.isAdminSubject.asObservable();\r\n  public showAdminPanel$ = this.showAdminPanelSubject.asObservable();\r\n  public securityInfo$ = this.securityInfoSubject.asObservable();\r\n  public editingMessage$ = this.editingMessageSubject.asObservable();\r\n  public connected$ = this.socketService.isConnected$;\r\n\r\n  // Computed observables\r\n  public isLoggedIn$ = this.user$.pipe(map(user => !!user));\r\n\r\n  private messageRefreshInterval: any;\r\n\r\n  constructor(\r\n    private apiService: ApiService,\r\n    private socketService: SocketService,\r\n    private notificationService: NotificationService\r\n  ) {\r\n    this.setupSocketListeners();\r\n    this.setupMessageRefresh();\r\n  }\r\n\r\n  private setupSocketListeners(): void {\r\n    this.socketService.on('connect', () => {\r\n      console.log('Connected to server');\r\n      // Clear any loading states when connected\r\n      this.loadingSubject.next(false);\r\n    });\r\n\r\n    this.socketService.on('disconnect', () => {\r\n      console.log('Disconnected from server');\r\n      // Don't set loading to true on disconnect to avoid UI freeze\r\n    });\r\n\r\n    this.socketService.on('connect_error', (error) => {\r\n      console.error('Socket connection error:', error);\r\n      this.loadingSubject.next(false);\r\n    });\r\n\r\n    this.socketService.on('userGroups', (userGroups: Group[]) => {\r\n      console.log('Received user groups:', userGroups);\r\n      this.groupsSubject.next(userGroups || []);\r\n      if (userGroups && userGroups.length > 0) {\r\n        console.log('Auto-selecting first group:', userGroups[0]);\r\n        this.currentGroupSubject.next(userGroups[0]);\r\n        // Load messages for the selected group\r\n        this.loadRecentMessages(userGroups[0].id);\r\n      } else {\r\n        console.log('No groups available for user');\r\n        // Clear current group and messages if no groups\r\n        this.currentGroupSubject.next(null);\r\n        this.messagesSubject.next([]);\r\n      }\r\n    });\r\n\r\n    this.socketService.on('recentMessages', (messages: Message[]) => {\r\n      console.log('Received recent messages:', messages.length);\r\n      this.messagesSubject.next(messages || []);\r\n    });\r\n\r\n    this.socketService.on('groupJoined', ({ groupId }: { groupId: string }) => {\r\n      console.log('Joined group:', groupId);\r\n      const groups = this.groupsSubject.value;\r\n      const group = groups.find(g => g.id === groupId);\r\n      if (group) {\r\n        this.currentGroupSubject.next(group);\r\n      }\r\n    });\r\n\r\n    this.socketService.on('newMessage', (message: Message) => {\r\n      console.log('New message received:', message);\r\n      const currentMessages = this.messagesSubject.value;\r\n      this.messagesSubject.next([...currentMessages, message]);\r\n      \r\n      // Show notification if not current user and window not focused\r\n      const currentUser = this.userSubject.value;\r\n      if (message.username !== currentUser && document.hidden) {\r\n        this.notificationService.showMessageNotification(message.username, message.text);\r\n      }\r\n    });\r\n\r\n    this.socketService.on('reactionUpdate', ({ messageId, reactions }: { messageId: string; reactions: any[] }) => {\r\n      console.log('Reaction update:', messageId, reactions);\r\n      const currentMessages = this.messagesSubject.value;\r\n      this.messagesSubject.next(\r\n        currentMessages.map(msg => \r\n          msg.id === messageId \r\n            ? { ...msg, reactions }\r\n            : msg\r\n        )\r\n      );\r\n    });\r\n\r\n    this.socketService.on('onlineUsersUpdate', (users: User[]) => {\r\n      console.log('Online users updated:', users);\r\n      this.onlineUsersSubject.next(users || []);\r\n    });\r\n\r\n    this.socketService.on('userJoined', ({ username }: { username: string }) => {\r\n      console.log('User joined:', username);\r\n      // Online users will be updated via onlineUsersUpdate event\r\n    });\r\n\r\n    this.socketService.on('userLeft', ({ username }: { username: string }) => {\r\n      console.log('User left:', username);\r\n      // Online users will be updated via onlineUsersUpdate event\r\n    });\r\n\r\n    this.socketService.on('messageUpdated', ({ messageId, newText, updatedAt }: { messageId: string; newText: string; updatedAt: string }) => {\r\n      console.log('Message updated:', messageId, newText);\r\n      const currentMessages = this.messagesSubject.value;\r\n      this.messagesSubject.next(\r\n        currentMessages.map(msg => \r\n          msg.id === messageId \r\n            ? { ...msg, text: newText, updated_at: updatedAt }\r\n            : msg\r\n        )\r\n      );\r\n    });\r\n\r\n    this.socketService.on('messageDeleted', ({ messageId }: { messageId: string }) => {\r\n      console.log('Message deleted:', messageId);\r\n      const currentMessages = this.messagesSubject.value;\r\n      this.messagesSubject.next(\r\n        currentMessages.filter(msg => msg.id !== messageId)\r\n      );\r\n    });\r\n\r\n    this.socketService.on('error', (error: any) => {\r\n      console.error('Socket error:', error);\r\n    });\r\n  }\r\n\r\n  private setupMessageRefresh(): void {\r\n    // Refresh messages every 5 seconds if socket is not connected\r\n    this.messageRefreshInterval = setInterval(() => {\r\n      if (!this.socketService.isConnectedSubject.value) {\r\n        const currentGroup = this.currentGroupSubject.value;\r\n        if (currentGroup) {\r\n          console.log('Refreshing messages via HTTP API');\r\n          this.loadRecentMessages(currentGroup.id);\r\n        }\r\n      }\r\n    }, 5000);\r\n  }\r\n\r\n  async login(username: string, password?: string, inviteCode: string | null = null): Promise<void> {\r\n    try {\r\n      this.loadingSubject.next(true);\r\n      console.log('Starting login process for:', username);\r\n      \r\n      const userData = await this.apiService.loginUser(username, password, inviteCode || undefined).toPromise();\r\n      console.log('Login API response:', userData);\r\n      \r\n      // Check if password is required\r\n      if (userData?.requiresPassword) {\r\n        throw new Error('Password required for this user');\r\n      }\r\n      \r\n      // Connect to socket with auth data\r\n      console.log('Connecting to socket...');\r\n      this.socketService.connect(username, password, inviteCode);\r\n      \r\n      this.userSubject.next(username);\r\n      this.isAdminSubject.next(userData?.isAdmin || false);\r\n      this.securityInfoSubject.next(userData?.securityInfo || null);\r\n      \r\n      console.log('Login completed successfully');\r\n      \r\n      // Wait for socket connection before loading groups\r\n      // The socket will emit 'userGroups' event which will handle group loading\r\n      // This prevents race conditions between API and socket calls\r\n      \r\n    } catch (error) {\r\n      console.error('Login failed:', error);\r\n      throw error;\r\n    } finally {\r\n      this.loadingSubject.next(false);\r\n    }\r\n  }\r\n\r\n  async loadRecentMessages(groupId: string): Promise<void> {\r\n    try {\r\n      console.log('Loading recent messages for group:', groupId);\r\n      const messages = await this.apiService.getMessages(groupId, 50).toPromise();\r\n      console.log('Loaded messages:', messages?.length || 0);\r\n      this.messagesSubject.next(messages || []);\r\n    } catch (error) {\r\n      console.error('Failed to load messages:', error);\r\n      // Set empty array to prevent UI from showing stale data\r\n      this.messagesSubject.next([]);\r\n    }\r\n  }\r\n\r\n  async loadUserGroups(username: string): Promise<void> {\r\n    try {\r\n      console.log('Loading user groups for:', username);\r\n      const groups = await this.apiService.getUserGroups(username).toPromise();\r\n      console.log('Loaded user groups:', groups);\r\n      this.groupsSubject.next(groups || []);\r\n      if (groups && groups.length > 0) {\r\n        console.log('Auto-selecting first group:', groups[0]);\r\n        this.currentGroupSubject.next(groups[0]);\r\n        this.loadRecentMessages(groups[0].id);\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to load user groups:', error);\r\n    }\r\n  }\r\n\r\n  joinGroup(groupId: string): void {\r\n    const currentUser = this.userSubject.value;\r\n    if (!groupId || !currentUser) {\r\n      console.error('Cannot join group - missing groupId or user');\r\n      return;\r\n    }\r\n    \r\n    console.log('Joining group:', groupId);\r\n    \r\n    // Check if socket is connected before joining\r\n    if (!this.socketService.isConnectedSubject.value) {\r\n      console.error('Cannot join group - socket not connected');\r\n      return;\r\n    }\r\n    \r\n    this.socketService.joinGroup(groupId);\r\n    const groups = this.groupsSubject.value;\r\n    const group = groups.find(g => g.id === groupId);\r\n    if (group) {\r\n      this.currentGroupSubject.next(group);\r\n      // Load recent messages for the group\r\n      this.loadRecentMessages(groupId);\r\n    } else {\r\n      console.error('Group not found in user groups:', groupId);\r\n    }\r\n  }\r\n\r\n  sendMessage(text: string, replyToId: string | null = null): void {\r\n    const currentUser = this.userSubject.value;\r\n    const currentGroup = this.currentGroupSubject.value;\r\n    \r\n    if (!text.trim()) {\r\n      console.error('Cannot send message - empty text');\r\n      return;\r\n    }\r\n    \r\n    if (!currentUser) {\r\n      console.error('Cannot send message - user not logged in');\r\n      return;\r\n    }\r\n    \r\n    if (!currentGroup) {\r\n      console.error('Cannot send message - no group selected. Available groups:', this.groupsSubject.value);\r\n      // Try to auto-select the first available group\r\n      const groups = this.groupsSubject.value;\r\n      if (groups && groups.length > 0) {\r\n        console.log('Auto-selecting first available group:', groups[0]);\r\n        this.currentGroupSubject.next(groups[0]);\r\n        this.loadRecentMessages(groups[0].id);\r\n        // Retry sending the message\r\n        setTimeout(() => this.sendMessage(text, replyToId), 100);\r\n        return;\r\n      } else {\r\n        console.error('No groups available for user');\r\n        return;\r\n      }\r\n    }\r\n\r\n    console.log('Sending message via socket:', { text, groupId: currentGroup.id, replyToId });\r\n    this.socketService.sendMessage(text, currentGroup.id, replyToId);\r\n    \r\n    // Fallback: If socket is not connected, try HTTP API\r\n    if (!this.socketService.isConnectedSubject.value) {\r\n      console.log('Socket not connected, trying HTTP API fallback');\r\n      this.apiService.sendMessage(text, currentUser, currentGroup.id, replyToId).subscribe({\r\n        next: (response) => {\r\n          console.log('Message sent via HTTP API:', response);\r\n        },\r\n        error: (error) => {\r\n          console.error('Failed to send message via HTTP API:', error);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  replyToMessage(message: Message): void {\r\n    this.replyToSubject.next(message);\r\n  }\r\n\r\n  cancelReply(): void {\r\n    this.replyToSubject.next(null);\r\n  }\r\n\r\n  addReaction(messageId: string, emoji: string): void {\r\n    this.socketService.addReaction(messageId, emoji);\r\n  }\r\n\r\n  removeReaction(data: { messageId: string; emoji?: string }): void {\r\n    this.socketService.removeReaction(data);\r\n  }\r\n\r\n  async updateMessage(messageId: string, newText: string): Promise<void> {\r\n    const currentUser = this.userSubject.value;\r\n    if (!currentUser) {\r\n      console.error('Cannot update message - user not logged in');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Update the message in the local state immediately for better UX\r\n      const currentMessages = this.messagesSubject.value;\r\n      this.messagesSubject.next(\r\n        currentMessages.map(msg => \r\n          msg.id === messageId \r\n            ? { ...msg, text: newText, updated_at: new Date().toISOString() }\r\n            : msg\r\n        )\r\n      );\r\n\r\n      // Emit socket event for real-time updates\r\n      if (this.socketService.isConnectedSubject.value) {\r\n        this.socketService.emit('messageUpdated', { messageId, newText });\r\n      } else {\r\n        // Fallback to HTTP API if socket not connected\r\n        const updatedMessage = await this.apiService.updateMessage(messageId, newText, currentUser).toPromise();\r\n        this.messagesSubject.next(\r\n          currentMessages.map(msg => \r\n            msg.id === messageId \r\n              ? { ...msg, text: newText, updated_at: updatedMessage.updated_at }\r\n              : msg\r\n          )\r\n        );\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to update message:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async deleteMessage(messageId: string): Promise<void> {\r\n    const currentUser = this.userSubject.value;\r\n    if (!currentUser) {\r\n      console.error('Cannot delete message - user not logged in');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Remove the message from local state immediately for better UX\r\n      const currentMessages = this.messagesSubject.value;\r\n      this.messagesSubject.next(\r\n        currentMessages.filter(msg => msg.id !== messageId)\r\n      );\r\n\r\n      // Emit socket event for real-time updates\r\n      if (this.socketService.isConnectedSubject.value) {\r\n        this.socketService.emit('messageDeleted', { messageId });\r\n      } else {\r\n        // Fallback to HTTP API if socket not connected\r\n        await this.apiService.deleteMessage(messageId, currentUser).toPromise();\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to delete message:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  showAdminPanel(): void {\r\n    this.showAdminPanelSubject.next(true);\r\n  }\r\n\r\n  hideAdminPanel(): void {\r\n    this.showAdminPanelSubject.next(false);\r\n  }\r\n\r\n  startEditingMessage(message: Message): void {\r\n    this.editingMessageSubject.next(message);\r\n  }\r\n\r\n  cancelEditingMessage(): void {\r\n    this.editingMessageSubject.next(null);\r\n  }\r\n\r\n  logout(): void {\r\n    this.socketService.disconnect();\r\n    if (this.messageRefreshInterval) {\r\n      clearInterval(this.messageRefreshInterval);\r\n    }\r\n    this.userSubject.next(null);\r\n    this.messagesSubject.next([]);\r\n    this.onlineUsersSubject.next([]);\r\n    this.groupsSubject.next([]);\r\n    this.currentGroupSubject.next(null);\r\n    this.replyToSubject.next(null);\r\n    this.loadingSubject.next(false);\r\n    this.isAdminSubject.next(false);\r\n    this.showAdminPanelSubject.next(false);\r\n    this.securityInfoSubject.next(null);\r\n    this.editingMessageSubject.next(null);\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module"}