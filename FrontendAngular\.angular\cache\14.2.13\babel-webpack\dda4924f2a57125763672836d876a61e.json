{"ast": null, "code": "export const intervalProvider = {\n  setInterval(handler, timeout, ...args) {\n    const {\n      delegate\n    } = intervalProvider;\n\n    if (delegate === null || delegate === void 0 ? void 0 : delegate.setInterval) {\n      return delegate.setInterval(handler, timeout, ...args);\n    }\n\n    return setInterval(handler, timeout, ...args);\n  },\n\n  clearInterval(handle) {\n    const {\n      delegate\n    } = intervalProvider;\n    return ((delegate === null || delegate === void 0 ? void 0 : delegate.clearInterval) || clearInterval)(handle);\n  },\n\n  delegate: undefined\n};", "map": {"version": 3, "names": ["intervalProvider", "setInterval", "handler", "timeout", "args", "delegate", "clearInterval", "handle", "undefined"], "sources": ["R:/chateye/FrontendAngular/node_modules/rxjs/dist/esm/internal/scheduler/intervalProvider.js"], "sourcesContent": ["export const intervalProvider = {\n    setInterval(handler, timeout, ...args) {\n        const { delegate } = intervalProvider;\n        if (delegate === null || delegate === void 0 ? void 0 : delegate.setInterval) {\n            return delegate.setInterval(handler, timeout, ...args);\n        }\n        return setInterval(handler, timeout, ...args);\n    },\n    clearInterval(handle) {\n        const { delegate } = intervalProvider;\n        return ((delegate === null || delegate === void 0 ? void 0 : delegate.clearInterval) || clearInterval)(handle);\n    },\n    delegate: undefined,\n};\n"], "mappings": "AAAA,OAAO,MAAMA,gBAAgB,GAAG;EAC5BC,WAAW,CAACC,OAAD,EAAUC,OAAV,EAAmB,GAAGC,IAAtB,EAA4B;IACnC,MAAM;MAAEC;IAAF,IAAeL,gBAArB;;IACA,IAAIK,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,KAAK,KAAK,CAAvC,GAA2C,KAAK,CAAhD,GAAoDA,QAAQ,CAACJ,WAAjE,EAA8E;MAC1E,OAAOI,QAAQ,CAACJ,WAAT,CAAqBC,OAArB,EAA8BC,OAA9B,EAAuC,GAAGC,IAA1C,CAAP;IACH;;IACD,OAAOH,WAAW,CAACC,OAAD,EAAUC,OAAV,EAAmB,GAAGC,IAAtB,CAAlB;EACH,CAP2B;;EAQ5BE,aAAa,CAACC,MAAD,EAAS;IAClB,MAAM;MAAEF;IAAF,IAAeL,gBAArB;IACA,OAAO,CAAC,CAACK,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,KAAK,KAAK,CAAvC,GAA2C,KAAK,CAAhD,GAAoDA,QAAQ,CAACC,aAA9D,KAAgFA,aAAjF,EAAgGC,MAAhG,CAAP;EACH,CAX2B;;EAY5BF,QAAQ,EAAEG;AAZkB,CAAzB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}