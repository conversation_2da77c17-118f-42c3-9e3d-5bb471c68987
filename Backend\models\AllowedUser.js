const { query } = require('../database/db');

class AllowedUser {
  static async addUser(username, addedBy = 'system') {
    try {
      const result = await query(
        'INSERT INTO allowed_users (username, added_by) VALUES ($1, $2) RETURNING *',
        [username, addedBy]
      );
      return result.rows[0];
    } catch (error) {
      if (error.code === '23505') { // Unique constraint violation
        // User already in whitelist
        return await this.getUser(username);
      }
      throw error;
    }
  }

  static async removeUser(username) {
    const result = await query(
      'UPDATE allowed_users SET is_active = false WHERE username = $1 RETURNING *',
      [username]
    );
    return result.rows[0];
  }

  static async getUser(username) {
    const result = await query(
      'SELECT * FROM allowed_users WHERE username = $1',
      [username]
    );
    return result.rows[0];
  }

  static async isUserAllowed(username) {
    const result = await query(
      'SELECT * FROM allowed_users WHERE username = $1 AND is_active = true',
      [username]
    );
    return result.rows.length > 0;
  }

  static async getAllowedUsers() {
    const result = await query(
      'SELECT username, added_by, added_at, is_active FROM allowed_users ORDER BY added_at DESC'
    );
    return result.rows;
  }

  static async getActiveUsers() {
    const result = await query(
      'SELECT username, added_by, added_at FROM allowed_users WHERE is_active = true ORDER BY username'
    );
    return result.rows;
  }

  // Bulk add users
  static async addMultipleUsers(usernames, addedBy = 'system') {
    const results = [];
    for (const username of usernames) {
      try {
        const user = await this.addUser(username, addedBy);
        results.push({ username, success: true, user });
      } catch (error) {
        results.push({ username, success: false, error: error.message });
      }
    }
    return results;
  }

  // Initialize with default admin users
  static async initializeDefaults() {
    const adminUsers = process.env.ADMIN_USERS ? process.env.ADMIN_USERS.split(',') : ['admin'];
    
    for (const adminUser of adminUsers) {
      try {
        await this.addUser(adminUser.trim(), 'system');
        console.log(`Added admin user: ${adminUser}`);
      } catch (error) {
        console.log(`Admin user ${adminUser} already exists`);
      }
    }
  }
}

module.exports = AllowedUser;
