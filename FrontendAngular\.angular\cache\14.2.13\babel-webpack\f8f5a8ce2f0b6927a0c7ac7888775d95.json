{"ast": null, "code": "import { mergeMap } from './mergeMap';\nexport const flatMap = mergeMap;", "map": {"version": 3, "names": ["mergeMap", "flatMap"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/rxjs/dist/esm/internal/operators/flatMap.js"], "sourcesContent": ["import { mergeMap } from './mergeMap';\nexport const flatMap = mergeMap;\n"], "mappings": "AAAA,SAASA,QAAT,QAAyB,YAAzB;AACA,OAAO,MAAMC,OAAO,GAAGD,QAAhB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}