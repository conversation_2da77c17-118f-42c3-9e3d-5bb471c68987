{"ast": null, "code": "import { globalThisShim as globalThis } from \"./globals.node.js\";\nexport function pick(obj, ...attr) {\n  return attr.reduce((acc, k) => {\n    if (obj.hasOwnProperty(k)) {\n      acc[k] = obj[k];\n    }\n\n    return acc;\n  }, {});\n} // Keep a reference to the real timeout functions so they can be used when overridden\n\nconst NATIVE_SET_TIMEOUT = globalThis.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = globalThis.clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n  if (opts.useNativeTimers) {\n    obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n    obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n  } else {\n    obj.setTimeoutFn = globalThis.setTimeout.bind(globalThis);\n    obj.clearTimeoutFn = globalThis.clearTimeout.bind(globalThis);\n  }\n} // base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\n\nconst BASE64_OVERHEAD = 1.33; // we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\n\nexport function byteLength(obj) {\n  if (typeof obj === \"string\") {\n    return utf8Length(obj);\n  } // arraybuffer or blob\n\n\n  return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\n\nfunction utf8Length(str) {\n  let c = 0,\n      length = 0;\n\n  for (let i = 0, l = str.length; i < l; i++) {\n    c = str.charCodeAt(i);\n\n    if (c < 0x80) {\n      length += 1;\n    } else if (c < 0x800) {\n      length += 2;\n    } else if (c < 0xd800 || c >= 0xe000) {\n      length += 3;\n    } else {\n      i++;\n      length += 4;\n    }\n  }\n\n  return length;\n}\n/**\n * Generates a random 8-characters string.\n */\n\n\nexport function randomString() {\n  return Date.now().toString(36).substring(3) + Math.random().toString(36).substring(2, 5);\n}", "map": {"version": 3, "names": ["globalThisShim", "globalThis", "pick", "obj", "attr", "reduce", "acc", "k", "hasOwnProperty", "NATIVE_SET_TIMEOUT", "setTimeout", "NATIVE_CLEAR_TIMEOUT", "clearTimeout", "installTimerFunctions", "opts", "useNativeTimers", "setTimeoutFn", "bind", "clearTimeoutFn", "BASE64_OVERHEAD", "byteLength", "utf8Length", "Math", "ceil", "size", "str", "c", "length", "i", "l", "charCodeAt", "randomString", "Date", "now", "toString", "substring", "random"], "sources": ["R:/chateye/FrontendAngular/node_modules/engine.io-client/build/esm/util.js"], "sourcesContent": ["import { globalThisShim as globalThis } from \"./globals.node.js\";\nexport function pick(obj, ...attr) {\n    return attr.reduce((acc, k) => {\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = globalThis.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = globalThis.clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n    }\n    else {\n        obj.setTimeoutFn = globalThis.setTimeout.bind(globalThis);\n        obj.clearTimeoutFn = globalThis.clearTimeout.bind(globalThis);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for (let i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n/**\n * Generates a random 8-characters string.\n */\nexport function randomString() {\n    return (Date.now().toString(36).substring(3) +\n        Math.random().toString(36).substring(2, 5));\n}\n"], "mappings": "AAAA,SAASA,cAAc,IAAIC,UAA3B,QAA6C,mBAA7C;AACA,OAAO,SAASC,IAAT,CAAcC,GAAd,EAAmB,GAAGC,IAAtB,EAA4B;EAC/B,OAAOA,IAAI,CAACC,MAAL,CAAY,CAACC,GAAD,EAAMC,CAAN,KAAY;IAC3B,IAAIJ,GAAG,CAACK,cAAJ,CAAmBD,CAAnB,CAAJ,EAA2B;MACvBD,GAAG,CAACC,CAAD,CAAH,GAASJ,GAAG,CAACI,CAAD,CAAZ;IACH;;IACD,OAAOD,GAAP;EACH,CALM,EAKJ,EALI,CAAP;AAMH,C,CACD;;AACA,MAAMG,kBAAkB,GAAGR,UAAU,CAACS,UAAtC;AACA,MAAMC,oBAAoB,GAAGV,UAAU,CAACW,YAAxC;AACA,OAAO,SAASC,qBAAT,CAA+BV,GAA/B,EAAoCW,IAApC,EAA0C;EAC7C,IAAIA,IAAI,CAACC,eAAT,EAA0B;IACtBZ,GAAG,CAACa,YAAJ,GAAmBP,kBAAkB,CAACQ,IAAnB,CAAwBhB,UAAxB,CAAnB;IACAE,GAAG,CAACe,cAAJ,GAAqBP,oBAAoB,CAACM,IAArB,CAA0BhB,UAA1B,CAArB;EACH,CAHD,MAIK;IACDE,GAAG,CAACa,YAAJ,GAAmBf,UAAU,CAACS,UAAX,CAAsBO,IAAtB,CAA2BhB,UAA3B,CAAnB;IACAE,GAAG,CAACe,cAAJ,GAAqBjB,UAAU,CAACW,YAAX,CAAwBK,IAAxB,CAA6BhB,UAA7B,CAArB;EACH;AACJ,C,CACD;;AACA,MAAMkB,eAAe,GAAG,IAAxB,C,CACA;;AACA,OAAO,SAASC,UAAT,CAAoBjB,GAApB,EAAyB;EAC5B,IAAI,OAAOA,GAAP,KAAe,QAAnB,EAA6B;IACzB,OAAOkB,UAAU,CAAClB,GAAD,CAAjB;EACH,CAH2B,CAI5B;;;EACA,OAAOmB,IAAI,CAACC,IAAL,CAAU,CAACpB,GAAG,CAACiB,UAAJ,IAAkBjB,GAAG,CAACqB,IAAvB,IAA+BL,eAAzC,CAAP;AACH;;AACD,SAASE,UAAT,CAAoBI,GAApB,EAAyB;EACrB,IAAIC,CAAC,GAAG,CAAR;EAAA,IAAWC,MAAM,GAAG,CAApB;;EACA,KAAK,IAAIC,CAAC,GAAG,CAAR,EAAWC,CAAC,GAAGJ,GAAG,CAACE,MAAxB,EAAgCC,CAAC,GAAGC,CAApC,EAAuCD,CAAC,EAAxC,EAA4C;IACxCF,CAAC,GAAGD,GAAG,CAACK,UAAJ,CAAeF,CAAf,CAAJ;;IACA,IAAIF,CAAC,GAAG,IAAR,EAAc;MACVC,MAAM,IAAI,CAAV;IACH,CAFD,MAGK,IAAID,CAAC,GAAG,KAAR,EAAe;MAChBC,MAAM,IAAI,CAAV;IACH,CAFI,MAGA,IAAID,CAAC,GAAG,MAAJ,IAAcA,CAAC,IAAI,MAAvB,EAA+B;MAChCC,MAAM,IAAI,CAAV;IACH,CAFI,MAGA;MACDC,CAAC;MACDD,MAAM,IAAI,CAAV;IACH;EACJ;;EACD,OAAOA,MAAP;AACH;AACD;AACA;AACA;;;AACA,OAAO,SAASI,YAAT,GAAwB;EAC3B,OAAQC,IAAI,CAACC,GAAL,GAAWC,QAAX,CAAoB,EAApB,EAAwBC,SAAxB,CAAkC,CAAlC,IACJb,IAAI,CAACc,MAAL,GAAcF,QAAd,CAAuB,EAAvB,EAA2BC,SAA3B,CAAqC,CAArC,EAAwC,CAAxC,CADJ;AAEH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}