{"ast": null, "code": "// imported from https://github.com/component/has-cors\nlet value = false;\n\ntry {\n  value = typeof XMLHttpRequest !== 'undefined' && 'withCredentials' in new XMLHttpRequest();\n} catch (err) {// if XMLHttp support is disabled in IE then it will throw\n  // when trying to create\n}\n\nexport const hasCORS = value;", "map": {"version": 3, "names": ["value", "XMLHttpRequest", "err", "hasCORS"], "sources": ["R:/chateye/FrontendAngular/node_modules/engine.io-client/build/esm/contrib/has-cors.js"], "sourcesContent": ["// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== 'undefined' &&\n        'withCredentials' in new XMLHttpRequest();\n}\ncatch (err) {\n    // if XMLHttp support is disabled in IE then it will throw\n    // when trying to create\n}\nexport const hasCORS = value;\n"], "mappings": "AAAA;AACA,IAAIA,KAAK,GAAG,KAAZ;;AACA,IAAI;EACAA,KAAK,GAAG,OAAOC,cAAP,KAA0B,WAA1B,IACJ,qBAAqB,IAAIA,cAAJ,EADzB;AAEH,CAHD,CAIA,OAAOC,GAAP,EAAY,CACR;EACA;AACH;;AACD,OAAO,MAAMC,OAAO,GAAGH,KAAhB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}