{"ast": null, "code": "import { encodePacket, encodePacketToBinary } from \"./encodePacket.js\";\nimport { decodePacket } from \"./decodePacket.js\";\nimport { ERROR_PACKET } from \"./commons.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\n\nconst encodePayload = (packets, callback) => {\n  // some packets may be added to the array while encoding, so the initial length must be saved\n  const length = packets.length;\n  const encodedPackets = new Array(length);\n  let count = 0;\n  packets.forEach((packet, i) => {\n    // force base64 encoding for binary packets\n    encodePacket(packet, false, encodedPacket => {\n      encodedPackets[i] = encodedPacket;\n\n      if (++count === length) {\n        callback(encodedPackets.join(SEPARATOR));\n      }\n    });\n  });\n};\n\nconst decodePayload = (encodedPayload, binaryType) => {\n  const encodedPackets = encodedPayload.split(SEPARATOR);\n  const packets = [];\n\n  for (let i = 0; i < encodedPackets.length; i++) {\n    const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n    packets.push(decodedPacket);\n\n    if (decodedPacket.type === \"error\") {\n      break;\n    }\n  }\n\n  return packets;\n};\n\nexport function createPacketEncoderStream() {\n  return new TransformStream({\n    transform(packet, controller) {\n      encodePacketToBinary(packet, encodedPacket => {\n        const payloadLength = encodedPacket.length;\n        let header; // inspired by the WebSocket format: https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API/Writing_WebSocket_servers#decoding_payload_length\n\n        if (payloadLength < 126) {\n          header = new Uint8Array(1);\n          new DataView(header.buffer).setUint8(0, payloadLength);\n        } else if (payloadLength < 65536) {\n          header = new Uint8Array(3);\n          const view = new DataView(header.buffer);\n          view.setUint8(0, 126);\n          view.setUint16(1, payloadLength);\n        } else {\n          header = new Uint8Array(9);\n          const view = new DataView(header.buffer);\n          view.setUint8(0, 127);\n          view.setBigUint64(1, BigInt(payloadLength));\n        } // first bit indicates whether the payload is plain text (0) or binary (1)\n\n\n        if (packet.data && typeof packet.data !== \"string\") {\n          header[0] |= 0x80;\n        }\n\n        controller.enqueue(header);\n        controller.enqueue(encodedPacket);\n      });\n    }\n\n  });\n}\nlet TEXT_DECODER;\n\nfunction totalLength(chunks) {\n  return chunks.reduce((acc, chunk) => acc + chunk.length, 0);\n}\n\nfunction concatChunks(chunks, size) {\n  if (chunks[0].length === size) {\n    return chunks.shift();\n  }\n\n  const buffer = new Uint8Array(size);\n  let j = 0;\n\n  for (let i = 0; i < size; i++) {\n    buffer[i] = chunks[0][j++];\n\n    if (j === chunks[0].length) {\n      chunks.shift();\n      j = 0;\n    }\n  }\n\n  if (chunks.length && j < chunks[0].length) {\n    chunks[0] = chunks[0].slice(j);\n  }\n\n  return buffer;\n}\n\nexport function createPacketDecoderStream(maxPayload, binaryType) {\n  if (!TEXT_DECODER) {\n    TEXT_DECODER = new TextDecoder();\n  }\n\n  const chunks = [];\n  let state = 0\n  /* State.READ_HEADER */\n  ;\n  let expectedLength = -1;\n  let isBinary = false;\n  return new TransformStream({\n    transform(chunk, controller) {\n      chunks.push(chunk);\n\n      while (true) {\n        if (state === 0\n        /* State.READ_HEADER */\n        ) {\n          if (totalLength(chunks) < 1) {\n            break;\n          }\n\n          const header = concatChunks(chunks, 1);\n          isBinary = (header[0] & 0x80) === 0x80;\n          expectedLength = header[0] & 0x7f;\n\n          if (expectedLength < 126) {\n            state = 3\n            /* State.READ_PAYLOAD */\n            ;\n          } else if (expectedLength === 126) {\n            state = 1\n            /* State.READ_EXTENDED_LENGTH_16 */\n            ;\n          } else {\n            state = 2\n            /* State.READ_EXTENDED_LENGTH_64 */\n            ;\n          }\n        } else if (state === 1\n        /* State.READ_EXTENDED_LENGTH_16 */\n        ) {\n          if (totalLength(chunks) < 2) {\n            break;\n          }\n\n          const headerArray = concatChunks(chunks, 2);\n          expectedLength = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length).getUint16(0);\n          state = 3\n          /* State.READ_PAYLOAD */\n          ;\n        } else if (state === 2\n        /* State.READ_EXTENDED_LENGTH_64 */\n        ) {\n          if (totalLength(chunks) < 8) {\n            break;\n          }\n\n          const headerArray = concatChunks(chunks, 8);\n          const view = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length);\n          const n = view.getUint32(0);\n\n          if (n > Math.pow(2, 53 - 32) - 1) {\n            // the maximum safe integer in JavaScript is 2^53 - 1\n            controller.enqueue(ERROR_PACKET);\n            break;\n          }\n\n          expectedLength = n * Math.pow(2, 32) + view.getUint32(4);\n          state = 3\n          /* State.READ_PAYLOAD */\n          ;\n        } else {\n          if (totalLength(chunks) < expectedLength) {\n            break;\n          }\n\n          const data = concatChunks(chunks, expectedLength);\n          controller.enqueue(decodePacket(isBinary ? data : TEXT_DECODER.decode(data), binaryType));\n          state = 0\n          /* State.READ_HEADER */\n          ;\n        }\n\n        if (expectedLength === 0 || expectedLength > maxPayload) {\n          controller.enqueue(ERROR_PACKET);\n          break;\n        }\n      }\n    }\n\n  });\n}\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload };", "map": {"version": 3, "names": ["encodePacket", "encodePacketToBinary", "decodePacket", "ERROR_PACKET", "SEPARATOR", "String", "fromCharCode", "encodePayload", "packets", "callback", "length", "encodedPackets", "Array", "count", "for<PERSON>ach", "packet", "i", "encodedPacket", "join", "decodePayload", "encodedPayload", "binaryType", "split", "decodedPacket", "push", "type", "createPacketEncoderStream", "TransformStream", "transform", "controller", "payloadLength", "header", "Uint8Array", "DataView", "buffer", "setUint8", "view", "setUint16", "setBigUint64", "BigInt", "data", "enqueue", "TEXT_DECODER", "totalLength", "chunks", "reduce", "acc", "chunk", "concatChunks", "size", "shift", "j", "slice", "createPacketDecoderStream", "maxPayload", "TextDecoder", "state", "<PERSON><PERSON><PERSON><PERSON>", "isBinary", "headerArray", "byteOffset", "getUint16", "n", "getUint32", "Math", "pow", "decode", "protocol"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/engine.io-parser/build/esm/index.js"], "sourcesContent": ["import { encodePacket, encodePacketToBinary } from \"./encodePacket.js\";\nimport { decodePacket } from \"./decodePacket.js\";\nimport { ERROR_PACKET, } from \"./commons.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n    // some packets may be added to the array while encoding, so the initial length must be saved\n    const length = packets.length;\n    const encodedPackets = new Array(length);\n    let count = 0;\n    packets.forEach((packet, i) => {\n        // force base64 encoding for binary packets\n        encodePacket(packet, false, (encodedPacket) => {\n            encodedPackets[i] = encodedPacket;\n            if (++count === length) {\n                callback(encodedPackets.join(SEPARATOR));\n            }\n        });\n    });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n    const encodedPackets = encodedPayload.split(SEPARATOR);\n    const packets = [];\n    for (let i = 0; i < encodedPackets.length; i++) {\n        const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n        packets.push(decodedPacket);\n        if (decodedPacket.type === \"error\") {\n            break;\n        }\n    }\n    return packets;\n};\nexport function createPacketEncoderStream() {\n    return new TransformStream({\n        transform(packet, controller) {\n            encodePacketToBinary(packet, (encodedPacket) => {\n                const payloadLength = encodedPacket.length;\n                let header;\n                // inspired by the WebSocket format: https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API/Writing_WebSocket_servers#decoding_payload_length\n                if (payloadLength < 126) {\n                    header = new Uint8Array(1);\n                    new DataView(header.buffer).setUint8(0, payloadLength);\n                }\n                else if (payloadLength < 65536) {\n                    header = new Uint8Array(3);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 126);\n                    view.setUint16(1, payloadLength);\n                }\n                else {\n                    header = new Uint8Array(9);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 127);\n                    view.setBigUint64(1, BigInt(payloadLength));\n                }\n                // first bit indicates whether the payload is plain text (0) or binary (1)\n                if (packet.data && typeof packet.data !== \"string\") {\n                    header[0] |= 0x80;\n                }\n                controller.enqueue(header);\n                controller.enqueue(encodedPacket);\n            });\n        },\n    });\n}\nlet TEXT_DECODER;\nfunction totalLength(chunks) {\n    return chunks.reduce((acc, chunk) => acc + chunk.length, 0);\n}\nfunction concatChunks(chunks, size) {\n    if (chunks[0].length === size) {\n        return chunks.shift();\n    }\n    const buffer = new Uint8Array(size);\n    let j = 0;\n    for (let i = 0; i < size; i++) {\n        buffer[i] = chunks[0][j++];\n        if (j === chunks[0].length) {\n            chunks.shift();\n            j = 0;\n        }\n    }\n    if (chunks.length && j < chunks[0].length) {\n        chunks[0] = chunks[0].slice(j);\n    }\n    return buffer;\n}\nexport function createPacketDecoderStream(maxPayload, binaryType) {\n    if (!TEXT_DECODER) {\n        TEXT_DECODER = new TextDecoder();\n    }\n    const chunks = [];\n    let state = 0 /* State.READ_HEADER */;\n    let expectedLength = -1;\n    let isBinary = false;\n    return new TransformStream({\n        transform(chunk, controller) {\n            chunks.push(chunk);\n            while (true) {\n                if (state === 0 /* State.READ_HEADER */) {\n                    if (totalLength(chunks) < 1) {\n                        break;\n                    }\n                    const header = concatChunks(chunks, 1);\n                    isBinary = (header[0] & 0x80) === 0x80;\n                    expectedLength = header[0] & 0x7f;\n                    if (expectedLength < 126) {\n                        state = 3 /* State.READ_PAYLOAD */;\n                    }\n                    else if (expectedLength === 126) {\n                        state = 1 /* State.READ_EXTENDED_LENGTH_16 */;\n                    }\n                    else {\n                        state = 2 /* State.READ_EXTENDED_LENGTH_64 */;\n                    }\n                }\n                else if (state === 1 /* State.READ_EXTENDED_LENGTH_16 */) {\n                    if (totalLength(chunks) < 2) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 2);\n                    expectedLength = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length).getUint16(0);\n                    state = 3 /* State.READ_PAYLOAD */;\n                }\n                else if (state === 2 /* State.READ_EXTENDED_LENGTH_64 */) {\n                    if (totalLength(chunks) < 8) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 8);\n                    const view = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length);\n                    const n = view.getUint32(0);\n                    if (n > Math.pow(2, 53 - 32) - 1) {\n                        // the maximum safe integer in JavaScript is 2^53 - 1\n                        controller.enqueue(ERROR_PACKET);\n                        break;\n                    }\n                    expectedLength = n * Math.pow(2, 32) + view.getUint32(4);\n                    state = 3 /* State.READ_PAYLOAD */;\n                }\n                else {\n                    if (totalLength(chunks) < expectedLength) {\n                        break;\n                    }\n                    const data = concatChunks(chunks, expectedLength);\n                    controller.enqueue(decodePacket(isBinary ? data : TEXT_DECODER.decode(data), binaryType));\n                    state = 0 /* State.READ_HEADER */;\n                }\n                if (expectedLength === 0 || expectedLength > maxPayload) {\n                    controller.enqueue(ERROR_PACKET);\n                    break;\n                }\n            }\n        },\n    });\n}\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload, };\n"], "mappings": "AAAA,SAASA,YAAT,EAAuBC,oBAAvB,QAAmD,mBAAnD;AACA,SAASC,YAAT,QAA6B,mBAA7B;AACA,SAASC,YAAT,QAA8B,cAA9B;AACA,MAAMC,SAAS,GAAGC,MAAM,CAACC,YAAP,CAAoB,EAApB,CAAlB,C,CAA2C;;AAC3C,MAAMC,aAAa,GAAG,CAACC,OAAD,EAAUC,QAAV,KAAuB;EACzC;EACA,MAAMC,MAAM,GAAGF,OAAO,CAACE,MAAvB;EACA,MAAMC,cAAc,GAAG,IAAIC,KAAJ,CAAUF,MAAV,CAAvB;EACA,IAAIG,KAAK,GAAG,CAAZ;EACAL,OAAO,CAACM,OAAR,CAAgB,CAACC,MAAD,EAASC,CAAT,KAAe;IAC3B;IACAhB,YAAY,CAACe,MAAD,EAAS,KAAT,EAAiBE,aAAD,IAAmB;MAC3CN,cAAc,CAACK,CAAD,CAAd,GAAoBC,aAApB;;MACA,IAAI,EAAEJ,KAAF,KAAYH,MAAhB,EAAwB;QACpBD,QAAQ,CAACE,cAAc,CAACO,IAAf,CAAoBd,SAApB,CAAD,CAAR;MACH;IACJ,CALW,CAAZ;EAMH,CARD;AASH,CAdD;;AAeA,MAAMe,aAAa,GAAG,CAACC,cAAD,EAAiBC,UAAjB,KAAgC;EAClD,MAAMV,cAAc,GAAGS,cAAc,CAACE,KAAf,CAAqBlB,SAArB,CAAvB;EACA,MAAMI,OAAO,GAAG,EAAhB;;EACA,KAAK,IAAIQ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGL,cAAc,CAACD,MAAnC,EAA2CM,CAAC,EAA5C,EAAgD;IAC5C,MAAMO,aAAa,GAAGrB,YAAY,CAACS,cAAc,CAACK,CAAD,CAAf,EAAoBK,UAApB,CAAlC;IACAb,OAAO,CAACgB,IAAR,CAAaD,aAAb;;IACA,IAAIA,aAAa,CAACE,IAAd,KAAuB,OAA3B,EAAoC;MAChC;IACH;EACJ;;EACD,OAAOjB,OAAP;AACH,CAXD;;AAYA,OAAO,SAASkB,yBAAT,GAAqC;EACxC,OAAO,IAAIC,eAAJ,CAAoB;IACvBC,SAAS,CAACb,MAAD,EAASc,UAAT,EAAqB;MAC1B5B,oBAAoB,CAACc,MAAD,EAAUE,aAAD,IAAmB;QAC5C,MAAMa,aAAa,GAAGb,aAAa,CAACP,MAApC;QACA,IAAIqB,MAAJ,CAF4C,CAG5C;;QACA,IAAID,aAAa,GAAG,GAApB,EAAyB;UACrBC,MAAM,GAAG,IAAIC,UAAJ,CAAe,CAAf,CAAT;UACA,IAAIC,QAAJ,CAAaF,MAAM,CAACG,MAApB,EAA4BC,QAA5B,CAAqC,CAArC,EAAwCL,aAAxC;QACH,CAHD,MAIK,IAAIA,aAAa,GAAG,KAApB,EAA2B;UAC5BC,MAAM,GAAG,IAAIC,UAAJ,CAAe,CAAf,CAAT;UACA,MAAMI,IAAI,GAAG,IAAIH,QAAJ,CAAaF,MAAM,CAACG,MAApB,CAAb;UACAE,IAAI,CAACD,QAAL,CAAc,CAAd,EAAiB,GAAjB;UACAC,IAAI,CAACC,SAAL,CAAe,CAAf,EAAkBP,aAAlB;QACH,CALI,MAMA;UACDC,MAAM,GAAG,IAAIC,UAAJ,CAAe,CAAf,CAAT;UACA,MAAMI,IAAI,GAAG,IAAIH,QAAJ,CAAaF,MAAM,CAACG,MAApB,CAAb;UACAE,IAAI,CAACD,QAAL,CAAc,CAAd,EAAiB,GAAjB;UACAC,IAAI,CAACE,YAAL,CAAkB,CAAlB,EAAqBC,MAAM,CAACT,aAAD,CAA3B;QACH,CAnB2C,CAoB5C;;;QACA,IAAIf,MAAM,CAACyB,IAAP,IAAe,OAAOzB,MAAM,CAACyB,IAAd,KAAuB,QAA1C,EAAoD;UAChDT,MAAM,CAAC,CAAD,CAAN,IAAa,IAAb;QACH;;QACDF,UAAU,CAACY,OAAX,CAAmBV,MAAnB;QACAF,UAAU,CAACY,OAAX,CAAmBxB,aAAnB;MACH,CA1BmB,CAApB;IA2BH;;EA7BsB,CAApB,CAAP;AA+BH;AACD,IAAIyB,YAAJ;;AACA,SAASC,WAAT,CAAqBC,MAArB,EAA6B;EACzB,OAAOA,MAAM,CAACC,MAAP,CAAc,CAACC,GAAD,EAAMC,KAAN,KAAgBD,GAAG,GAAGC,KAAK,CAACrC,MAA1C,EAAkD,CAAlD,CAAP;AACH;;AACD,SAASsC,YAAT,CAAsBJ,MAAtB,EAA8BK,IAA9B,EAAoC;EAChC,IAAIL,MAAM,CAAC,CAAD,CAAN,CAAUlC,MAAV,KAAqBuC,IAAzB,EAA+B;IAC3B,OAAOL,MAAM,CAACM,KAAP,EAAP;EACH;;EACD,MAAMhB,MAAM,GAAG,IAAIF,UAAJ,CAAeiB,IAAf,CAAf;EACA,IAAIE,CAAC,GAAG,CAAR;;EACA,KAAK,IAAInC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGiC,IAApB,EAA0BjC,CAAC,EAA3B,EAA+B;IAC3BkB,MAAM,CAAClB,CAAD,CAAN,GAAY4B,MAAM,CAAC,CAAD,CAAN,CAAUO,CAAC,EAAX,CAAZ;;IACA,IAAIA,CAAC,KAAKP,MAAM,CAAC,CAAD,CAAN,CAAUlC,MAApB,EAA4B;MACxBkC,MAAM,CAACM,KAAP;MACAC,CAAC,GAAG,CAAJ;IACH;EACJ;;EACD,IAAIP,MAAM,CAAClC,MAAP,IAAiByC,CAAC,GAAGP,MAAM,CAAC,CAAD,CAAN,CAAUlC,MAAnC,EAA2C;IACvCkC,MAAM,CAAC,CAAD,CAAN,GAAYA,MAAM,CAAC,CAAD,CAAN,CAAUQ,KAAV,CAAgBD,CAAhB,CAAZ;EACH;;EACD,OAAOjB,MAAP;AACH;;AACD,OAAO,SAASmB,yBAAT,CAAmCC,UAAnC,EAA+CjC,UAA/C,EAA2D;EAC9D,IAAI,CAACqB,YAAL,EAAmB;IACfA,YAAY,GAAG,IAAIa,WAAJ,EAAf;EACH;;EACD,MAAMX,MAAM,GAAG,EAAf;EACA,IAAIY,KAAK,GAAG;EAAE;EAAd;EACA,IAAIC,cAAc,GAAG,CAAC,CAAtB;EACA,IAAIC,QAAQ,GAAG,KAAf;EACA,OAAO,IAAI/B,eAAJ,CAAoB;IACvBC,SAAS,CAACmB,KAAD,EAAQlB,UAAR,EAAoB;MACzBe,MAAM,CAACpB,IAAP,CAAYuB,KAAZ;;MACA,OAAO,IAAP,EAAa;QACT,IAAIS,KAAK,KAAK;QAAE;QAAhB,EAAyC;UACrC,IAAIb,WAAW,CAACC,MAAD,CAAX,GAAsB,CAA1B,EAA6B;YACzB;UACH;;UACD,MAAMb,MAAM,GAAGiB,YAAY,CAACJ,MAAD,EAAS,CAAT,CAA3B;UACAc,QAAQ,GAAG,CAAC3B,MAAM,CAAC,CAAD,CAAN,GAAY,IAAb,MAAuB,IAAlC;UACA0B,cAAc,GAAG1B,MAAM,CAAC,CAAD,CAAN,GAAY,IAA7B;;UACA,IAAI0B,cAAc,GAAG,GAArB,EAA0B;YACtBD,KAAK,GAAG;YAAE;YAAV;UACH,CAFD,MAGK,IAAIC,cAAc,KAAK,GAAvB,EAA4B;YAC7BD,KAAK,GAAG;YAAE;YAAV;UACH,CAFI,MAGA;YACDA,KAAK,GAAG;YAAE;YAAV;UACH;QACJ,CAhBD,MAiBK,IAAIA,KAAK,KAAK;QAAE;QAAhB,EAAqD;UACtD,IAAIb,WAAW,CAACC,MAAD,CAAX,GAAsB,CAA1B,EAA6B;YACzB;UACH;;UACD,MAAMe,WAAW,GAAGX,YAAY,CAACJ,MAAD,EAAS,CAAT,CAAhC;UACAa,cAAc,GAAG,IAAIxB,QAAJ,CAAa0B,WAAW,CAACzB,MAAzB,EAAiCyB,WAAW,CAACC,UAA7C,EAAyDD,WAAW,CAACjD,MAArE,EAA6EmD,SAA7E,CAAuF,CAAvF,CAAjB;UACAL,KAAK,GAAG;UAAE;UAAV;QACH,CAPI,MAQA,IAAIA,KAAK,KAAK;QAAE;QAAhB,EAAqD;UACtD,IAAIb,WAAW,CAACC,MAAD,CAAX,GAAsB,CAA1B,EAA6B;YACzB;UACH;;UACD,MAAMe,WAAW,GAAGX,YAAY,CAACJ,MAAD,EAAS,CAAT,CAAhC;UACA,MAAMR,IAAI,GAAG,IAAIH,QAAJ,CAAa0B,WAAW,CAACzB,MAAzB,EAAiCyB,WAAW,CAACC,UAA7C,EAAyDD,WAAW,CAACjD,MAArE,CAAb;UACA,MAAMoD,CAAC,GAAG1B,IAAI,CAAC2B,SAAL,CAAe,CAAf,CAAV;;UACA,IAAID,CAAC,GAAGE,IAAI,CAACC,GAAL,CAAS,CAAT,EAAY,KAAK,EAAjB,IAAuB,CAA/B,EAAkC;YAC9B;YACApC,UAAU,CAACY,OAAX,CAAmBtC,YAAnB;YACA;UACH;;UACDsD,cAAc,GAAGK,CAAC,GAAGE,IAAI,CAACC,GAAL,CAAS,CAAT,EAAY,EAAZ,CAAJ,GAAsB7B,IAAI,CAAC2B,SAAL,CAAe,CAAf,CAAvC;UACAP,KAAK,GAAG;UAAE;UAAV;QACH,CAdI,MAeA;UACD,IAAIb,WAAW,CAACC,MAAD,CAAX,GAAsBa,cAA1B,EAA0C;YACtC;UACH;;UACD,MAAMjB,IAAI,GAAGQ,YAAY,CAACJ,MAAD,EAASa,cAAT,CAAzB;UACA5B,UAAU,CAACY,OAAX,CAAmBvC,YAAY,CAACwD,QAAQ,GAAGlB,IAAH,GAAUE,YAAY,CAACwB,MAAb,CAAoB1B,IAApB,CAAnB,EAA8CnB,UAA9C,CAA/B;UACAmC,KAAK,GAAG;UAAE;UAAV;QACH;;QACD,IAAIC,cAAc,KAAK,CAAnB,IAAwBA,cAAc,GAAGH,UAA7C,EAAyD;UACrDzB,UAAU,CAACY,OAAX,CAAmBtC,YAAnB;UACA;QACH;MACJ;IACJ;;EAzDsB,CAApB,CAAP;AA2DH;AACD,OAAO,MAAMgE,QAAQ,GAAG,CAAjB;AACP,SAASnE,YAAT,EAAuBO,aAAvB,EAAsCL,YAAtC,EAAoDiB,aAApD", "ignoreList": []}, "metadata": {}, "sourceType": "module"}