{"ast": null, "code": "import { Subject } from './Subject';\nimport { dateTimestampProvider } from './scheduler/dateTimestampProvider';\nexport class ReplaySubject extends Subject {\n  constructor(_bufferSize = Infinity, _windowTime = Infinity, _timestampProvider = dateTimestampProvider) {\n    super();\n    this._bufferSize = _bufferSize;\n    this._windowTime = _windowTime;\n    this._timestampProvider = _timestampProvider;\n    this._buffer = [];\n    this._infiniteTimeWindow = true;\n    this._infiniteTimeWindow = _windowTime === Infinity;\n    this._bufferSize = Math.max(1, _bufferSize);\n    this._windowTime = Math.max(1, _windowTime);\n  }\n\n  next(value) {\n    const {\n      isStopped,\n      _buffer,\n      _infiniteTimeWindow,\n      _timestampProvider,\n      _windowTime\n    } = this;\n\n    if (!isStopped) {\n      _buffer.push(value);\n\n      !_infiniteTimeWindow && _buffer.push(_timestampProvider.now() + _windowTime);\n    }\n\n    this._trimBuffer();\n\n    super.next(value);\n  }\n\n  _subscribe(subscriber) {\n    this._throwIfClosed();\n\n    this._trimBuffer();\n\n    const subscription = this._innerSubscribe(subscriber);\n\n    const {\n      _infiniteTimeWindow,\n      _buffer\n    } = this;\n\n    const copy = _buffer.slice();\n\n    for (let i = 0; i < copy.length && !subscriber.closed; i += _infiniteTimeWindow ? 1 : 2) {\n      subscriber.next(copy[i]);\n    }\n\n    this._checkFinalizedStatuses(subscriber);\n\n    return subscription;\n  }\n\n  _trimBuffer() {\n    const {\n      _bufferSize,\n      _timestampProvider,\n      _buffer,\n      _infiniteTimeWindow\n    } = this;\n    const adjustedBufferSize = (_infiniteTimeWindow ? 1 : 2) * _bufferSize;\n    _bufferSize < Infinity && adjustedBufferSize < _buffer.length && _buffer.splice(0, _buffer.length - adjustedBufferSize);\n\n    if (!_infiniteTimeWindow) {\n      const now = _timestampProvider.now();\n\n      let last = 0;\n\n      for (let i = 1; i < _buffer.length && _buffer[i] <= now; i += 2) {\n        last = i;\n      }\n\n      last && _buffer.splice(0, last + 1);\n    }\n  }\n\n}", "map": {"version": 3, "names": ["Subject", "dateTimestampProvider", "ReplaySubject", "constructor", "_bufferSize", "Infinity", "_windowTime", "_timestampProvider", "_buffer", "_infiniteTimeWindow", "Math", "max", "next", "value", "isStopped", "push", "now", "_trimBuffer", "_subscribe", "subscriber", "_throwIfClosed", "subscription", "_innerSubscribe", "copy", "slice", "i", "length", "closed", "_checkFinalizedStatuses", "adjustedBufferSize", "splice", "last"], "sources": ["R:/chateye/FrontendAngular/node_modules/rxjs/dist/esm/internal/ReplaySubject.js"], "sourcesContent": ["import { Subject } from './Subject';\nimport { dateTimestampProvider } from './scheduler/dateTimestampProvider';\nexport class ReplaySubject extends Subject {\n    constructor(_bufferSize = Infinity, _windowTime = Infinity, _timestampProvider = dateTimestampProvider) {\n        super();\n        this._bufferSize = _bufferSize;\n        this._windowTime = _windowTime;\n        this._timestampProvider = _timestampProvider;\n        this._buffer = [];\n        this._infiniteTimeWindow = true;\n        this._infiniteTimeWindow = _windowTime === Infinity;\n        this._bufferSize = Math.max(1, _bufferSize);\n        this._windowTime = Math.max(1, _windowTime);\n    }\n    next(value) {\n        const { isStopped, _buffer, _infiniteTimeWindow, _timestampProvider, _windowTime } = this;\n        if (!isStopped) {\n            _buffer.push(value);\n            !_infiniteTimeWindow && _buffer.push(_timestampProvider.now() + _windowTime);\n        }\n        this._trimBuffer();\n        super.next(value);\n    }\n    _subscribe(subscriber) {\n        this._throwIfClosed();\n        this._trimBuffer();\n        const subscription = this._innerSubscribe(subscriber);\n        const { _infiniteTimeWindow, _buffer } = this;\n        const copy = _buffer.slice();\n        for (let i = 0; i < copy.length && !subscriber.closed; i += _infiniteTimeWindow ? 1 : 2) {\n            subscriber.next(copy[i]);\n        }\n        this._checkFinalizedStatuses(subscriber);\n        return subscription;\n    }\n    _trimBuffer() {\n        const { _bufferSize, _timestampProvider, _buffer, _infiniteTimeWindow } = this;\n        const adjustedBufferSize = (_infiniteTimeWindow ? 1 : 2) * _bufferSize;\n        _bufferSize < Infinity && adjustedBufferSize < _buffer.length && _buffer.splice(0, _buffer.length - adjustedBufferSize);\n        if (!_infiniteTimeWindow) {\n            const now = _timestampProvider.now();\n            let last = 0;\n            for (let i = 1; i < _buffer.length && _buffer[i] <= now; i += 2) {\n                last = i;\n            }\n            last && _buffer.splice(0, last + 1);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,WAAxB;AACA,SAASC,qBAAT,QAAsC,mCAAtC;AACA,OAAO,MAAMC,aAAN,SAA4BF,OAA5B,CAAoC;EACvCG,WAAW,CAACC,WAAW,GAAGC,QAAf,EAAyBC,WAAW,GAAGD,QAAvC,EAAiDE,kBAAkB,GAAGN,qBAAtE,EAA6F;IACpG;IACA,KAAKG,WAAL,GAAmBA,WAAnB;IACA,KAAKE,WAAL,GAAmBA,WAAnB;IACA,KAAKC,kBAAL,GAA0BA,kBAA1B;IACA,KAAKC,OAAL,GAAe,EAAf;IACA,KAAKC,mBAAL,GAA2B,IAA3B;IACA,KAAKA,mBAAL,GAA2BH,WAAW,KAAKD,QAA3C;IACA,KAAKD,WAAL,GAAmBM,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYP,WAAZ,CAAnB;IACA,KAAKE,WAAL,GAAmBI,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYL,WAAZ,CAAnB;EACH;;EACDM,IAAI,CAACC,KAAD,EAAQ;IACR,MAAM;MAAEC,SAAF;MAAaN,OAAb;MAAsBC,mBAAtB;MAA2CF,kBAA3C;MAA+DD;IAA/D,IAA+E,IAArF;;IACA,IAAI,CAACQ,SAAL,EAAgB;MACZN,OAAO,CAACO,IAAR,CAAaF,KAAb;;MACA,CAACJ,mBAAD,IAAwBD,OAAO,CAACO,IAAR,CAAaR,kBAAkB,CAACS,GAAnB,KAA2BV,WAAxC,CAAxB;IACH;;IACD,KAAKW,WAAL;;IACA,MAAML,IAAN,CAAWC,KAAX;EACH;;EACDK,UAAU,CAACC,UAAD,EAAa;IACnB,KAAKC,cAAL;;IACA,KAAKH,WAAL;;IACA,MAAMI,YAAY,GAAG,KAAKC,eAAL,CAAqBH,UAArB,CAArB;;IACA,MAAM;MAAEV,mBAAF;MAAuBD;IAAvB,IAAmC,IAAzC;;IACA,MAAMe,IAAI,GAAGf,OAAO,CAACgB,KAAR,EAAb;;IACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,IAAI,CAACG,MAAT,IAAmB,CAACP,UAAU,CAACQ,MAA/C,EAAuDF,CAAC,IAAIhB,mBAAmB,GAAG,CAAH,GAAO,CAAtF,EAAyF;MACrFU,UAAU,CAACP,IAAX,CAAgBW,IAAI,CAACE,CAAD,CAApB;IACH;;IACD,KAAKG,uBAAL,CAA6BT,UAA7B;;IACA,OAAOE,YAAP;EACH;;EACDJ,WAAW,GAAG;IACV,MAAM;MAAEb,WAAF;MAAeG,kBAAf;MAAmCC,OAAnC;MAA4CC;IAA5C,IAAoE,IAA1E;IACA,MAAMoB,kBAAkB,GAAG,CAACpB,mBAAmB,GAAG,CAAH,GAAO,CAA3B,IAAgCL,WAA3D;IACAA,WAAW,GAAGC,QAAd,IAA0BwB,kBAAkB,GAAGrB,OAAO,CAACkB,MAAvD,IAAiElB,OAAO,CAACsB,MAAR,CAAe,CAAf,EAAkBtB,OAAO,CAACkB,MAAR,GAAiBG,kBAAnC,CAAjE;;IACA,IAAI,CAACpB,mBAAL,EAA0B;MACtB,MAAMO,GAAG,GAAGT,kBAAkB,CAACS,GAAnB,EAAZ;;MACA,IAAIe,IAAI,GAAG,CAAX;;MACA,KAAK,IAAIN,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGjB,OAAO,CAACkB,MAAZ,IAAsBlB,OAAO,CAACiB,CAAD,CAAP,IAAcT,GAApD,EAAyDS,CAAC,IAAI,CAA9D,EAAiE;QAC7DM,IAAI,GAAGN,CAAP;MACH;;MACDM,IAAI,IAAIvB,OAAO,CAACsB,MAAR,CAAe,CAAf,EAAkBC,IAAI,GAAG,CAAzB,CAAR;IACH;EACJ;;AA7CsC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}