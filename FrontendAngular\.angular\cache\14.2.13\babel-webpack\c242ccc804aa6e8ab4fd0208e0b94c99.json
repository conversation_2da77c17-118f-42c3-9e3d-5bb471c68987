{"ast": null, "code": "export var numericUnicodeMap = {\n  0: 65533,\n  128: 8364,\n  130: 8218,\n  131: 402,\n  132: 8222,\n  133: 8230,\n  134: 8224,\n  135: 8225,\n  136: 710,\n  137: 8240,\n  138: 352,\n  139: 8249,\n  140: 338,\n  142: 381,\n  145: 8216,\n  146: 8217,\n  147: 8220,\n  148: 8221,\n  149: 8226,\n  150: 8211,\n  151: 8212,\n  152: 732,\n  153: 8482,\n  154: 353,\n  155: 8250,\n  156: 339,\n  158: 382,\n  159: 376\n};", "map": {"version": 3, "names": ["numericUnicodeMap"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/html-entities/dist/esm/numeric-unicode-map.js"], "sourcesContent": ["export var numericUnicodeMap = {\n    0: 65533,\n    128: 8364,\n    130: 8218,\n    131: 402,\n    132: 8222,\n    133: 8230,\n    134: 8224,\n    135: 8225,\n    136: 710,\n    137: 8240,\n    138: 352,\n    139: 8249,\n    140: 338,\n    142: 381,\n    145: 8216,\n    146: 8217,\n    147: 8220,\n    148: 8221,\n    149: 8226,\n    150: 8211,\n    151: 8212,\n    152: 732,\n    153: 8482,\n    154: 353,\n    155: 8250,\n    156: 339,\n    158: 382,\n    159: 376\n};\n"], "mappings": "AAAA,OAAO,IAAIA,iBAAiB,GAAG;EAC3B,GAAG,KADwB;EAE3B,KAAK,IAFsB;EAG3B,KAAK,IAHsB;EAI3B,KAAK,GAJsB;EAK3B,KAAK,IALsB;EAM3B,KAAK,IANsB;EAO3B,KAAK,IAPsB;EAQ3B,KAAK,IARsB;EAS3B,KAAK,GATsB;EAU3B,KAAK,IAVsB;EAW3B,KAAK,GAXsB;EAY3B,KAAK,IAZsB;EAa3B,KAAK,GAbsB;EAc3B,KAAK,GAdsB;EAe3B,KAAK,IAfsB;EAgB3B,KAAK,IAhBsB;EAiB3B,KAAK,IAjBsB;EAkB3B,KAAK,IAlBsB;EAmB3B,KAAK,IAnBsB;EAoB3B,KAAK,IApBsB;EAqB3B,KAAK,IArBsB;EAsB3B,KAAK,GAtBsB;EAuB3B,KAAK,IAvBsB;EAwB3B,KAAK,GAxBsB;EAyB3B,KAAK,IAzBsB;EA0B3B,KAAK,GA1BsB;EA2B3B,KAAK,GA3BsB;EA4B3B,KAAK;AA5BsB,CAAxB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}