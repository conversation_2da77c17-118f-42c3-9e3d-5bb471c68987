{"ast": null, "code": "import _asyncToGenerator from \"R:/chateye/FrontendAngular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { ApiService, Message, Group, User, SecurityInfo } from './api.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./api.service\";\nimport * as i2 from \"./socket.service\";\nimport * as i3 from \"./notification.service\";\nexport let ChatService = /*#__PURE__*/(() => {\n  class ChatService {\n    constructor(apiService, socketService, notificationService) {\n      this.apiService = apiService;\n      this.socketService = socketService;\n      this.notificationService = notificationService;\n      this.userSubject = new BehaviorSubject(null);\n      this.messagesSubject = new BehaviorSubject([]);\n      this.onlineUsersSubject = new BehaviorSubject([]);\n      this.groupsSubject = new BehaviorSubject([]);\n      this.currentGroupSubject = new BehaviorSubject(null);\n      this.replyToSubject = new BehaviorSubject(null);\n      this.loadingSubject = new BehaviorSubject(false);\n      this.isAdminSubject = new BehaviorSubject(false);\n      this.showAdminPanelSubject = new BehaviorSubject(false);\n      this.securityInfoSubject = new BehaviorSubject(null);\n      this.editingMessageSubject = new BehaviorSubject(null); // Public observables\n\n      this.user$ = this.userSubject.asObservable();\n      this.messages$ = this.messagesSubject.asObservable();\n      this.onlineUsers$ = this.onlineUsersSubject.asObservable();\n      this.groups$ = this.groupsSubject.asObservable();\n      this.currentGroup$ = this.currentGroupSubject.asObservable();\n      this.replyTo$ = this.replyToSubject.asObservable();\n      this.loading$ = this.loadingSubject.asObservable();\n      this.isAdmin$ = this.isAdminSubject.asObservable();\n      this.showAdminPanel$ = this.showAdminPanelSubject.asObservable();\n      this.securityInfo$ = this.securityInfoSubject.asObservable();\n      this.editingMessage$ = this.editingMessageSubject.asObservable();\n      this.connected$ = this.socketService.isConnected$; // Computed observables\n\n      this.isLoggedIn$ = this.user$.pipe(map(user => !!user));\n      this.isLoadingGroups = false;\n      this.isLoadingMessages = false;\n      this.lastMessageLoadTime = 0;\n      this.MESSAGE_LOAD_THROTTLE = 1000; // 1 second throttle\n\n      try {\n        this.setupSocketListeners();\n        this.setupMessageRefresh(); // Expose manual group loading globally for debugging\n\n        window.manualLoadGroups = () => this.manualLoadGroups();\n      } catch (error) {\n        console.error('Error initializing ChatService:', error);\n      }\n    }\n\n    setupSocketListeners() {\n      this.socketService.on('connect', () => {\n        console.log('Connected to server'); // Clear any loading states when connected\n\n        this.loadingSubject.next(false);\n      });\n      this.socketService.on('disconnect', () => {\n        console.log('Disconnected from server'); // Don't set loading to true on disconnect to avoid UI freeze\n      });\n      this.socketService.on('connect_error', error => {\n        console.error('Socket connection error:', error);\n        this.loadingSubject.next(false);\n      });\n      this.socketService.on('userGroups', userGroups => {\n        console.log('Received user groups:', userGroups?.length || 0, 'groups');\n        this.groupsSubject.next(userGroups || []);\n\n        if (userGroups && userGroups.length > 0) {\n          console.log('Auto-selecting first group:', userGroups[0].name);\n          this.currentGroupSubject.next(userGroups[0]); // Load messages for the selected group\n\n          this.loadRecentMessages(userGroups[0].id);\n        } else {\n          console.log('No groups available for user'); // Clear current group and messages if no groups\n\n          this.currentGroupSubject.next(null);\n          this.messagesSubject.next([]);\n        }\n      });\n      this.socketService.on('recentMessages', messages => {\n        console.log('Received recent messages:', messages.length);\n        console.log('Recent messages data:', messages);\n        this.messagesSubject.next(messages || []);\n        console.log('Recent messages subject updated, current value:', this.messagesSubject.value.length);\n      });\n      this.socketService.on('groupJoined', ({\n        groupId\n      }) => {\n        console.log('Joined group:', groupId);\n        const groups = this.groupsSubject.value;\n        const group = groups.find(g => g.id === groupId);\n\n        if (group) {\n          this.currentGroupSubject.next(group);\n        }\n      });\n      this.socketService.on('newMessage', message => {\n        console.log('New message received:', message);\n        const currentMessages = this.messagesSubject.value;\n        this.messagesSubject.next([...currentMessages, message]); // Show notification if not current user and window not focused\n\n        const currentUser = this.userSubject.value;\n\n        if (message.username !== currentUser && document.hidden) {\n          this.notificationService.showMessageNotification(message.username, message.text);\n        }\n      });\n      this.socketService.on('reactionUpdate', ({\n        messageId,\n        reactions\n      }) => {\n        console.log('Reaction update:', messageId, reactions);\n        const currentMessages = this.messagesSubject.value;\n        this.messagesSubject.next(currentMessages.map(msg => msg.id === messageId ? { ...msg,\n          reactions\n        } : msg));\n      });\n      this.socketService.on('onlineUsersUpdate', users => {\n        console.log('Online users updated:', users);\n        this.onlineUsersSubject.next(users || []);\n      });\n      this.socketService.on('userJoined', ({\n        username\n      }) => {\n        console.log('User joined:', username); // Online users will be updated via onlineUsersUpdate event\n      });\n      this.socketService.on('userLeft', ({\n        username\n      }) => {\n        console.log('User left:', username); // Online users will be updated via onlineUsersUpdate event\n      });\n      this.socketService.on('messageUpdated', ({\n        messageId,\n        newText,\n        updatedAt\n      }) => {\n        console.log('Message updated:', messageId, newText);\n        const currentMessages = this.messagesSubject.value;\n        this.messagesSubject.next(currentMessages.map(msg => msg.id === messageId ? { ...msg,\n          text: newText,\n          updated_at: updatedAt\n        } : msg));\n      });\n      this.socketService.on('messageDeleted', ({\n        messageId\n      }) => {\n        console.log('Message deleted:', messageId);\n        const currentMessages = this.messagesSubject.value;\n        this.messagesSubject.next(currentMessages.filter(msg => msg.id !== messageId));\n      });\n      this.socketService.on('error', error => {\n        console.error('Socket error:', error);\n      });\n    }\n\n    setupMessageRefresh() {\n      // Clear any existing interval first\n      if (this.messageRefreshInterval) {\n        clearInterval(this.messageRefreshInterval);\n      } // Temporarily disable message refresh to prevent browser freezing\n      // TODO: Re-enable once socket connection issues are resolved\n\n      /*\r\n      this.messageRefreshInterval = setInterval(() => {\r\n        try {\r\n          if (!this.socketService.isConnectedSubject.value) {\r\n            const currentGroup = this.currentGroupSubject.value;\r\n            if (currentGroup) {\r\n              console.log('Refreshing messages via HTTP API');\r\n              this.loadRecentMessages(currentGroup.id);\r\n            }\r\n          }\r\n        } catch (error) {\r\n          console.error('Error in message refresh interval:', error);\r\n          // Clear the interval if there's an error to prevent crashes\r\n          if (this.messageRefreshInterval) {\r\n            clearInterval(this.messageRefreshInterval);\r\n            this.messageRefreshInterval = null;\r\n          }\r\n        }\r\n      }, 5000);\r\n      */\n\n    }\n\n    login(_x, _x2) {\n      var _this = this;\n\n      return _asyncToGenerator(function* (username, password, inviteCode = null) {\n        try {\n          _this.loadingSubject.next(true);\n\n          console.log('Starting login process for:', username);\n          const userData = yield _this.apiService.loginUser(username, password, inviteCode || undefined).toPromise();\n          console.log('Login API response:', userData); // Check if password is required\n\n          if (userData?.requiresPassword) {\n            throw new Error('Password required for this user');\n          } // Connect to socket with auth data\n\n\n          console.log('Connecting to socket...');\n\n          _this.socketService.connect(username, password, inviteCode);\n\n          _this.userSubject.next(username);\n\n          _this.isAdminSubject.next(userData?.isAdmin || false);\n\n          _this.securityInfoSubject.next(userData?.securityInfo || null);\n\n          console.log('Login completed successfully for:', username, '(Admin:', userData?.isAdmin || false, ')'); // Wait for socket connection before loading groups\n          // The socket will emit 'userGroups' event which will handle group loading\n          // This prevents race conditions between API and socket calls\n          // DISABLED: setTimeout causes infinite loops and browser crashes\n          // Groups will be loaded via socket events or manual API calls only\n        } catch (error) {\n          console.error('Login failed:', error);\n          throw error;\n        } finally {\n          _this.loadingSubject.next(false);\n        }\n      }).apply(this, arguments);\n    }\n\n    loadRecentMessages(groupId) {\n      var _this2 = this;\n\n      return _asyncToGenerator(function* () {\n        // Safety guard to prevent infinite loops\n        if (_this2.isLoadingMessages) {\n          console.log('Already loading messages, skipping...');\n          return;\n        } // Throttle message loading to prevent excessive API calls\n\n\n        const now = Date.now();\n\n        if (now - _this2.lastMessageLoadTime < _this2.MESSAGE_LOAD_THROTTLE) {\n          console.log('Throttling message load request');\n          return;\n        }\n\n        _this2.lastMessageLoadTime = now;\n        _this2.isLoadingMessages = true;\n\n        try {\n          console.log('Loading recent messages for group:', groupId);\n          const messages = yield _this2.apiService.getMessages(groupId, 50).toPromise();\n          console.log('Loaded messages:', messages?.length || 0);\n          console.log('Messages data:', messages);\n\n          _this2.messagesSubject.next(messages || []);\n\n          console.log('Messages subject updated, current value:', _this2.messagesSubject.value.length);\n        } catch (error) {\n          console.error('Failed to load messages:', error); // Set empty array to prevent UI from showing stale data\n\n          _this2.messagesSubject.next([]);\n        } finally {\n          _this2.isLoadingMessages = false;\n        }\n      })();\n    }\n\n    loadUserGroups(username) {\n      var _this3 = this;\n\n      return _asyncToGenerator(function* () {\n        // Safety guard to prevent infinite loops\n        if (_this3.isLoadingGroups) {\n          console.log('Already loading groups, skipping...');\n          return;\n        }\n\n        _this3.isLoadingGroups = true;\n\n        try {\n          console.log('Loading user groups for:', username);\n          const groups = yield _this3.apiService.getUserGroups(username).toPromise();\n          console.log('Loaded user groups:', groups);\n\n          _this3.groupsSubject.next(groups || []);\n\n          if (groups && groups.length > 0) {\n            console.log('Auto-selecting first group:', groups[0]);\n\n            _this3.currentGroupSubject.next(groups[0]);\n\n            _this3.loadRecentMessages(groups[0].id);\n          }\n        } catch (error) {\n          console.error('Failed to load user groups:', error);\n        } finally {\n          _this3.isLoadingGroups = false;\n        }\n      })();\n    } // Safe method to manually load groups (call this from browser console)\n\n\n    manualLoadGroups() {\n      var _this4 = this;\n\n      return _asyncToGenerator(function* () {\n        const currentUser = _this4.userSubject.value;\n\n        if (currentUser) {\n          console.log('Manually loading groups for:', currentUser);\n          yield _this4.loadUserGroups(currentUser);\n        } else {\n          console.error('No user logged in');\n        }\n      })();\n    }\n\n    joinGroup(groupId) {\n      const currentUser = this.userSubject.value;\n\n      if (!groupId || !currentUser) {\n        console.error('Cannot join group - missing groupId or user');\n        return;\n      }\n\n      console.log('Joining group:', groupId); // Check if socket is connected before joining\n\n      if (!this.socketService.isConnectedSubject.value) {\n        console.error('Cannot join group - socket not connected');\n        return;\n      }\n\n      this.socketService.joinGroup(groupId);\n      const groups = this.groupsSubject.value;\n      const group = groups.find(g => g.id === groupId);\n\n      if (group) {\n        this.currentGroupSubject.next(group); // Load recent messages for the group\n\n        this.loadRecentMessages(groupId);\n      } else {\n        console.error('Group not found in user groups:', groupId);\n      }\n    }\n\n    sendMessage(text, replyToId = null) {\n      const currentUser = this.userSubject.value;\n      const currentGroup = this.currentGroupSubject.value;\n\n      if (!text.trim()) {\n        console.error('Cannot send message - empty text');\n        return;\n      }\n\n      if (!currentUser) {\n        console.error('Cannot send message - user not logged in');\n        return;\n      }\n\n      if (!currentGroup) {\n        console.error('Cannot send message - no group selected. Available groups:', this.groupsSubject.value); // Try to auto-select the first available group\n\n        const groups = this.groupsSubject.value;\n\n        if (groups && groups.length > 0) {\n          console.log('Auto-selecting first available group:', groups[0]);\n          this.currentGroupSubject.next(groups[0]);\n          this.loadRecentMessages(groups[0].id); // Retry sending the message - DISABLED to prevent infinite loops\n          // setTimeout(() => this.sendMessage(text, replyToId), 100);\n\n          return;\n        } else {\n          console.error('No groups available for user');\n          return;\n        }\n      }\n\n      console.log('Sending message via socket:', {\n        text,\n        groupId: currentGroup.id,\n        replyToId\n      });\n      this.socketService.sendMessage(text, currentGroup.id, replyToId); // Fallback: If socket is not connected, try HTTP API\n\n      if (!this.socketService.isConnectedSubject.value) {\n        console.log('Socket not connected, trying HTTP API fallback');\n        this.apiService.sendMessage(text, currentUser, currentGroup.id, replyToId).subscribe({\n          next: response => {\n            console.log('Message sent via HTTP API:', response);\n          },\n          error: error => {\n            console.error('Failed to send message via HTTP API:', error);\n          }\n        });\n      }\n    }\n\n    replyToMessage(message) {\n      this.replyToSubject.next(message);\n    }\n\n    cancelReply() {\n      this.replyToSubject.next(null);\n    }\n\n    addReaction(messageId, emoji) {\n      this.socketService.addReaction(messageId, emoji);\n    }\n\n    removeReaction(data) {\n      this.socketService.removeReaction(data);\n    }\n\n    updateMessage(messageId, newText) {\n      var _this5 = this;\n\n      return _asyncToGenerator(function* () {\n        const currentUser = _this5.userSubject.value;\n\n        if (!currentUser) {\n          console.error('Cannot update message - user not logged in');\n          return;\n        }\n\n        try {\n          // Update the message in the local state immediately for better UX\n          const currentMessages = _this5.messagesSubject.value;\n\n          _this5.messagesSubject.next(currentMessages.map(msg => msg.id === messageId ? { ...msg,\n            text: newText,\n            updated_at: new Date().toISOString()\n          } : msg)); // Emit socket event for real-time updates\n\n\n          if (_this5.socketService.isConnectedSubject.value) {\n            _this5.socketService.emit('messageUpdated', {\n              messageId,\n              newText\n            });\n          } else {\n            // Fallback to HTTP API if socket not connected\n            const updatedMessage = yield _this5.apiService.updateMessage(messageId, newText, currentUser).toPromise();\n\n            _this5.messagesSubject.next(currentMessages.map(msg => msg.id === messageId ? { ...msg,\n              text: newText,\n              updated_at: updatedMessage.updated_at\n            } : msg));\n          }\n        } catch (error) {\n          console.error('Failed to update message:', error);\n          throw error;\n        }\n      })();\n    }\n\n    deleteMessage(messageId) {\n      var _this6 = this;\n\n      return _asyncToGenerator(function* () {\n        const currentUser = _this6.userSubject.value;\n\n        if (!currentUser) {\n          console.error('Cannot delete message - user not logged in');\n          return;\n        }\n\n        try {\n          // Remove the message from local state immediately for better UX\n          const currentMessages = _this6.messagesSubject.value;\n\n          _this6.messagesSubject.next(currentMessages.filter(msg => msg.id !== messageId)); // Emit socket event for real-time updates\n\n\n          if (_this6.socketService.isConnectedSubject.value) {\n            _this6.socketService.emit('messageDeleted', {\n              messageId\n            });\n          } else {\n            // Fallback to HTTP API if socket not connected\n            yield _this6.apiService.deleteMessage(messageId, currentUser).toPromise();\n          }\n        } catch (error) {\n          console.error('Failed to delete message:', error);\n          throw error;\n        }\n      })();\n    }\n\n    showAdminPanel() {\n      this.showAdminPanelSubject.next(true);\n    }\n\n    hideAdminPanel() {\n      this.showAdminPanelSubject.next(false);\n    }\n\n    startEditingMessage(message) {\n      this.editingMessageSubject.next(message);\n    }\n\n    cancelEditingMessage() {\n      this.editingMessageSubject.next(null);\n    }\n\n    logout() {\n      try {\n        // Disconnect socket first\n        this.socketService.disconnect(); // Clear interval\n\n        if (this.messageRefreshInterval) {\n          clearInterval(this.messageRefreshInterval);\n          this.messageRefreshInterval = null;\n        } // Reset all subjects\n\n\n        this.userSubject.next(null);\n        this.messagesSubject.next([]);\n        this.onlineUsersSubject.next([]);\n        this.groupsSubject.next([]);\n        this.currentGroupSubject.next(null);\n        this.replyToSubject.next(null);\n        this.loadingSubject.next(false);\n        this.isAdminSubject.next(false);\n        this.showAdminPanelSubject.next(false);\n        this.securityInfoSubject.next(null);\n        this.editingMessageSubject.next(null);\n        console.log('Logout completed successfully');\n      } catch (error) {\n        console.error('Error during logout:', error);\n      }\n    } // Method to refresh security info for all components\n\n\n    refreshSecurityInfo() {\n      this.apiService.getSecurityInfo().subscribe({\n        next: data => {\n          this.securityInfoSubject.next(data);\n        },\n        error: error => {\n          console.error('Failed to refresh security info:', error);\n        }\n      });\n    }\n\n  }\n\n  ChatService.ɵfac = function ChatService_Factory(t) {\n    return new (t || ChatService)(i0.ɵɵinject(i1.ApiService), i0.ɵɵinject(i2.SocketService), i0.ɵɵinject(i3.NotificationService));\n  };\n\n  ChatService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ChatService,\n    factory: ChatService.ɵfac,\n    providedIn: 'root'\n  });\n  return ChatService;\n})();", "map": null, "metadata": {}, "sourceType": "module"}