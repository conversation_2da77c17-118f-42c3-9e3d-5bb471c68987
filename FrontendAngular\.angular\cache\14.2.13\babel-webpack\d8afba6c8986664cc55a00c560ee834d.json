{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function debounceTime(dueTime, scheduler = asyncScheduler) {\n  return operate((source, subscriber) => {\n    let activeTask = null;\n    let lastValue = null;\n    let lastTime = null;\n\n    const emit = () => {\n      if (activeTask) {\n        activeTask.unsubscribe();\n        activeTask = null;\n        const value = lastValue;\n        lastValue = null;\n        subscriber.next(value);\n      }\n    };\n\n    function emitWhenIdle() {\n      const targetTime = lastTime + dueTime;\n      const now = scheduler.now();\n\n      if (now < targetTime) {\n        activeTask = this.schedule(undefined, targetTime - now);\n        subscriber.add(activeTask);\n        return;\n      }\n\n      emit();\n    }\n\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      lastValue = value;\n      lastTime = scheduler.now();\n\n      if (!activeTask) {\n        activeTask = scheduler.schedule(emitWhenIdle, dueTime);\n        subscriber.add(activeTask);\n      }\n    }, () => {\n      emit();\n      subscriber.complete();\n    }, undefined, () => {\n      lastValue = activeTask = null;\n    }));\n  });\n}", "map": {"version": 3, "names": ["asyncScheduler", "operate", "createOperatorSubscriber", "debounceTime", "dueTime", "scheduler", "source", "subscriber", "activeTask", "lastValue", "lastTime", "emit", "unsubscribe", "value", "next", "emitWhenIdle", "targetTime", "now", "schedule", "undefined", "add", "subscribe", "complete"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/rxjs/dist/esm/internal/operators/debounceTime.js"], "sourcesContent": ["import { asyncScheduler } from '../scheduler/async';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function debounceTime(dueTime, scheduler = asyncScheduler) {\n    return operate((source, subscriber) => {\n        let activeTask = null;\n        let lastValue = null;\n        let lastTime = null;\n        const emit = () => {\n            if (activeTask) {\n                activeTask.unsubscribe();\n                activeTask = null;\n                const value = lastValue;\n                lastValue = null;\n                subscriber.next(value);\n            }\n        };\n        function emitWhenIdle() {\n            const targetTime = lastTime + dueTime;\n            const now = scheduler.now();\n            if (now < targetTime) {\n                activeTask = this.schedule(undefined, targetTime - now);\n                subscriber.add(activeTask);\n                return;\n            }\n            emit();\n        }\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            lastValue = value;\n            lastTime = scheduler.now();\n            if (!activeTask) {\n                activeTask = scheduler.schedule(emitWhenIdle, dueTime);\n                subscriber.add(activeTask);\n            }\n        }, () => {\n            emit();\n            subscriber.complete();\n        }, undefined, () => {\n            lastValue = activeTask = null;\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,cAAT,QAA+B,oBAA/B;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,OAAO,SAASC,YAAT,CAAsBC,OAAtB,EAA+BC,SAAS,GAAGL,cAA3C,EAA2D;EAC9D,OAAOC,OAAO,CAAC,CAACK,MAAD,EAASC,UAAT,KAAwB;IACnC,IAAIC,UAAU,GAAG,IAAjB;IACA,IAAIC,SAAS,GAAG,IAAhB;IACA,IAAIC,QAAQ,GAAG,IAAf;;IACA,MAAMC,IAAI,GAAG,MAAM;MACf,IAAIH,UAAJ,EAAgB;QACZA,UAAU,CAACI,WAAX;QACAJ,UAAU,GAAG,IAAb;QACA,MAAMK,KAAK,GAAGJ,SAAd;QACAA,SAAS,GAAG,IAAZ;QACAF,UAAU,CAACO,IAAX,CAAgBD,KAAhB;MACH;IACJ,CARD;;IASA,SAASE,YAAT,GAAwB;MACpB,MAAMC,UAAU,GAAGN,QAAQ,GAAGN,OAA9B;MACA,MAAMa,GAAG,GAAGZ,SAAS,CAACY,GAAV,EAAZ;;MACA,IAAIA,GAAG,GAAGD,UAAV,EAAsB;QAClBR,UAAU,GAAG,KAAKU,QAAL,CAAcC,SAAd,EAAyBH,UAAU,GAAGC,GAAtC,CAAb;QACAV,UAAU,CAACa,GAAX,CAAeZ,UAAf;QACA;MACH;;MACDG,IAAI;IACP;;IACDL,MAAM,CAACe,SAAP,CAAiBnB,wBAAwB,CAACK,UAAD,EAAcM,KAAD,IAAW;MAC7DJ,SAAS,GAAGI,KAAZ;MACAH,QAAQ,GAAGL,SAAS,CAACY,GAAV,EAAX;;MACA,IAAI,CAACT,UAAL,EAAiB;QACbA,UAAU,GAAGH,SAAS,CAACa,QAAV,CAAmBH,YAAnB,EAAiCX,OAAjC,CAAb;QACAG,UAAU,CAACa,GAAX,CAAeZ,UAAf;MACH;IACJ,CAPwC,EAOtC,MAAM;MACLG,IAAI;MACJJ,UAAU,CAACe,QAAX;IACH,CAVwC,EAUtCH,SAVsC,EAU3B,MAAM;MAChBV,SAAS,GAAGD,UAAU,GAAG,IAAzB;IACH,CAZwC,CAAzC;EAaH,CApCa,CAAd;AAqCH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}