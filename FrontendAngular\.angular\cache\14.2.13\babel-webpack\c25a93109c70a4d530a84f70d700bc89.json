{"ast": null, "code": "import { operate } from '../util/lift';\nimport { noop } from '../util/noop';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nexport function debounce(durationSelector) {\n  return operate((source, subscriber) => {\n    let hasValue = false;\n    let lastValue = null;\n    let durationSubscriber = null;\n\n    const emit = () => {\n      durationSubscriber === null || durationSubscriber === void 0 ? void 0 : durationSubscriber.unsubscribe();\n      durationSubscriber = null;\n\n      if (hasValue) {\n        hasValue = false;\n        const value = lastValue;\n        lastValue = null;\n        subscriber.next(value);\n      }\n    };\n\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      durationSubscriber === null || durationSubscriber === void 0 ? void 0 : durationSubscriber.unsubscribe();\n      hasValue = true;\n      lastValue = value;\n      durationSubscriber = createOperatorSubscriber(subscriber, emit, noop);\n      innerFrom(durationSelector(value)).subscribe(durationSubscriber);\n    }, () => {\n      emit();\n      subscriber.complete();\n    }, undefined, () => {\n      lastValue = durationSubscriber = null;\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "noop", "createOperatorSubscriber", "innerFrom", "debounce", "durationSelector", "source", "subscriber", "hasValue", "lastValue", "durationSubscriber", "emit", "unsubscribe", "value", "next", "subscribe", "complete", "undefined"], "sources": ["R:/chateye/FrontendAngular/node_modules/rxjs/dist/esm/internal/operators/debounce.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { noop } from '../util/noop';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nexport function debounce(durationSelector) {\n    return operate((source, subscriber) => {\n        let hasValue = false;\n        let lastValue = null;\n        let durationSubscriber = null;\n        const emit = () => {\n            durationSubscriber === null || durationSubscriber === void 0 ? void 0 : durationSubscriber.unsubscribe();\n            durationSubscriber = null;\n            if (hasValue) {\n                hasValue = false;\n                const value = lastValue;\n                lastValue = null;\n                subscriber.next(value);\n            }\n        };\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            durationSubscriber === null || durationSubscriber === void 0 ? void 0 : durationSubscriber.unsubscribe();\n            hasValue = true;\n            lastValue = value;\n            durationSubscriber = createOperatorSubscriber(subscriber, emit, noop);\n            innerFrom(durationSelector(value)).subscribe(durationSubscriber);\n        }, () => {\n            emit();\n            subscriber.complete();\n        }, undefined, () => {\n            lastValue = durationSubscriber = null;\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,cAAxB;AACA,SAASC,IAAT,QAAqB,cAArB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,SAASC,SAAT,QAA0B,yBAA1B;AACA,OAAO,SAASC,QAAT,CAAkBC,gBAAlB,EAAoC;EACvC,OAAOL,OAAO,CAAC,CAACM,MAAD,EAASC,UAAT,KAAwB;IACnC,IAAIC,QAAQ,GAAG,KAAf;IACA,IAAIC,SAAS,GAAG,IAAhB;IACA,IAAIC,kBAAkB,GAAG,IAAzB;;IACA,MAAMC,IAAI,GAAG,MAAM;MACfD,kBAAkB,KAAK,IAAvB,IAA+BA,kBAAkB,KAAK,KAAK,CAA3D,GAA+D,KAAK,CAApE,GAAwEA,kBAAkB,CAACE,WAAnB,EAAxE;MACAF,kBAAkB,GAAG,IAArB;;MACA,IAAIF,QAAJ,EAAc;QACVA,QAAQ,GAAG,KAAX;QACA,MAAMK,KAAK,GAAGJ,SAAd;QACAA,SAAS,GAAG,IAAZ;QACAF,UAAU,CAACO,IAAX,CAAgBD,KAAhB;MACH;IACJ,CATD;;IAUAP,MAAM,CAACS,SAAP,CAAiBb,wBAAwB,CAACK,UAAD,EAAcM,KAAD,IAAW;MAC7DH,kBAAkB,KAAK,IAAvB,IAA+BA,kBAAkB,KAAK,KAAK,CAA3D,GAA+D,KAAK,CAApE,GAAwEA,kBAAkB,CAACE,WAAnB,EAAxE;MACAJ,QAAQ,GAAG,IAAX;MACAC,SAAS,GAAGI,KAAZ;MACAH,kBAAkB,GAAGR,wBAAwB,CAACK,UAAD,EAAaI,IAAb,EAAmBV,IAAnB,CAA7C;MACAE,SAAS,CAACE,gBAAgB,CAACQ,KAAD,CAAjB,CAAT,CAAmCE,SAAnC,CAA6CL,kBAA7C;IACH,CANwC,EAMtC,MAAM;MACLC,IAAI;MACJJ,UAAU,CAACS,QAAX;IACH,CATwC,EAStCC,SATsC,EAS3B,MAAM;MAChBR,SAAS,GAAGC,kBAAkB,GAAG,IAAjC;IACH,CAXwC,CAAzC;EAYH,CA1Ba,CAAd;AA2BH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}