const express = require('express');
const router = express.Router();
const Message = require('../models/Message');
const User = require('../models/User');
const Group = require('../models/Group');

// GET /messages/:groupId - Fetch recent messages for a group
router.get('/:groupId', async (req, res) => {
  try {
    const { groupId } = req.params;
    const limit = parseInt(req.query.limit) || 50;
    
    // Check if group exists
    const group = await Group.findById(groupId);
    if (!group) {
      return res.status(404).json({ error: 'Group not found' });
    }
    
    const messages = await Message.getMessagesWithReactions(groupId, limit);
    res.json(messages);
  } catch (error) {
    console.error('Error fetching messages:', error);
    res.status(500).json({ error: 'Failed to fetch messages' });
  }
});

// POST /messages - Create a new message
router.post('/', async (req, res) => {
  try {
    const { text, username, groupId, replyTo } = req.body;
    
    if (!text || !username || !groupId) {
      return res.status(400).json({ error: 'Text, username, and groupId are required' });
    }

    // Find or create user
    const user = await User.findOrCreate(username);
    
    // Check if user has access to the group
    const hasAccess = await Group.checkUserAccess(user.id, groupId);
    if (!hasAccess) {
      return res.status(403).json({ error: 'Access denied to this group' });
    }
    
    // Create message
    const message = await Message.create(text, user.id, groupId, replyTo);
    
    // Get message with details
    const messageWithDetails = await Message.getMessageWithDetails(message.id);
    messageWithDetails.reactions = await Message.getReactions(message.id);
    
    res.status(201).json(messageWithDetails);
  } catch (error) {
    console.error('Error creating message:', error);
    res.status(500).json({ error: 'Failed to create message' });
  }
});

// POST /messages/:id/reactions - Add reaction to message
router.post('/:id/reactions', async (req, res) => {
  try {
    const messageId = parseInt(req.params.id);
    const { emoji, username } = req.body;
    
    if (!emoji || !username) {
      return res.status(400).json({ error: 'Emoji and username are required' });
    }

    // Find or create user
    const user = await User.findOrCreate(username);
    
    // Add reaction
    await Message.addReaction(messageId, user.id, emoji);
    
    // Get updated reactions
    const reactions = await Message.getReactions(messageId);
    
    res.json(reactions);
  } catch (error) {
    console.error('Error adding reaction:', error);
    res.status(500).json({ error: 'Failed to add reaction' });
  }
});

// DELETE /messages/:id/reactions - Remove reaction from message
router.delete('/:id/reactions', async (req, res) => {
  try {
    const messageId = parseInt(req.params.id);
    const { username } = req.body;
    
    if (!username) {
      return res.status(400).json({ error: 'Username is required' });
    }

    // Find user
    const user = await User.findByUsername(username);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    // Remove reaction
    await Message.removeReaction(messageId, user.id);
    
    // Get updated reactions
    const reactions = await Message.getReactions(messageId);
    
    res.json(reactions);
  } catch (error) {
    console.error('Error removing reaction:', error);
    res.status(500).json({ error: 'Failed to remove reaction' });
  }
});

// PUT /messages/:id - Update a message
router.put('/:id', async (req, res) => {
  try {
    const messageId = parseInt(req.params.id);
    const { text, username } = req.body;
    
    if (!text || !username) {
      return res.status(400).json({ error: 'Text and username are required' });
    }

    // Find user
    const user = await User.findByUsername(username);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Check if message exists and belongs to user
    const existingMessage = await Message.getMessageById(messageId);
    if (!existingMessage) {
      return res.status(404).json({ error: 'Message not found' });
    }

    if (existingMessage.user_id !== user.id) {
      return res.status(403).json({ error: 'You can only edit your own messages' });
    }

    // Update message
    const updatedMessage = await Message.updateMessage(messageId, text, user.id);
    
    if (!updatedMessage) {
      return res.status(404).json({ error: 'Message not found or not owned by user' });
    }

    // Get updated message with details
    const messageWithDetails = await Message.getMessageWithDetails(messageId);
    messageWithDetails.reactions = await Message.getReactions(messageId);
    
    res.json(messageWithDetails);
  } catch (error) {
    console.error('Error updating message:', error);
    res.status(500).json({ error: 'Failed to update message' });
  }
});

// DELETE /messages/:id - Delete a message
router.delete('/:id', async (req, res) => {
  try {
    const messageId = parseInt(req.params.id);
    const { username } = req.body;
    
    if (!username) {
      return res.status(400).json({ error: 'Username is required' });
    }

    // Find user
    const user = await User.findByUsername(username);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Check if message exists and belongs to user
    const existingMessage = await Message.getMessageById(messageId);
    if (!existingMessage) {
      return res.status(404).json({ error: 'Message not found' });
    }

    if (existingMessage.user_id !== user.id) {
      return res.status(403).json({ error: 'You can only delete your own messages' });
    }

    // Delete message
    const deletedMessage = await Message.deleteMessage(messageId, user.id);
    
    if (!deletedMessage) {
      return res.status(404).json({ error: 'Message not found or not owned by user' });
    }

    res.json({ success: true, message: 'Message deleted successfully' });
  } catch (error) {
    console.error('Error deleting message:', error);
    res.status(500).json({ error: 'Failed to delete message' });
  }
});

// GET /groups - Get user's accessible groups
router.get('/groups/:username', async (req, res) => {
  try {
    const { username } = req.params;
    
    const user = await User.findByUsername(username);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    const groups = await Group.getGroupsForUser(user.id);
    res.json(groups);
  } catch (error) {
    console.error('Error fetching user groups:', error);
    res.status(500).json({ error: 'Failed to fetch user groups' });
  }
});

module.exports = router;
