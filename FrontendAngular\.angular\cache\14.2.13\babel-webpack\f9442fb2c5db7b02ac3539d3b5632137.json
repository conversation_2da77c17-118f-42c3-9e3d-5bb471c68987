{"ast": null, "code": "import _asyncToGenerator from \"R:/chateye/FrontendAngular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/chat.service\";\nimport * as i2 from \"../../services/api.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\n\nfunction LoginFormComponent__svg_svg_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 22);\n    i0.ɵɵelement(1, \"path\", 23);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LoginFormComponent__svg_svg_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 22);\n    i0.ɵɵelement(1, \"path\", 24);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LoginFormComponent__svg_svg_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 22);\n    i0.ɵɵelement(1, \"path\", 25);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LoginFormComponent_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Enter username and invite code to join\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LoginFormComponent_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Only approved users can join\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LoginFormComponent_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Enter your username to join the chat\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LoginFormComponent_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Loading...\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LoginFormComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 27);\n    i0.ɵɵelement(2, \"path\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Private chat - Access restricted\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction LoginFormComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\")(1, \"label\", 29);\n    i0.ɵɵtext(2, \" Invite Code \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"input\", 30);\n    i0.ɵɵlistener(\"ngModelChange\", function LoginFormComponent_div_28_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.inviteCode = $event);\n    })(\"input\", function LoginFormComponent_div_28_Template_input_input_3_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.onInviteCodeInput($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"required\", (ctx_r8.securityInfo == null ? null : ctx_r8.securityInfo.requiresInvite) || false)(\"ngModel\", ctx_r8.inviteCode)(\"disabled\", ctx_r8.isLoading);\n  }\n}\n\nfunction LoginFormComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.error, \" \");\n  }\n}\n\nfunction LoginFormComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"div\", 33);\n    i0.ɵɵtext(2, \" Joining... \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LoginFormComponent_span_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Join Chat\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nexport class LoginFormComponent {\n  constructor(chatService, apiService) {\n    this.chatService = chatService;\n    this.apiService = apiService;\n    this.username = 'testuser';\n    this.password = '';\n    this.inviteCode = '';\n    this.isLoading = false;\n    this.securityInfo = null;\n    this.error = '';\n    this.requiresPassword = false;\n  }\n\n  ngOnInit() {\n    this.fetchSecurityInfo(); // Subscribe to security info changes from ChatService\n\n    this.chatService.securityInfo$.subscribe(securityInfo => {\n      if (securityInfo) {\n        this.securityInfo = securityInfo;\n        console.log('Security info updated from ChatService:', securityInfo);\n      }\n    });\n  }\n\n  fetchSecurityInfo() {\n    this.apiService.getSecurityInfo().subscribe({\n      next: data => {\n        this.securityInfo = data;\n        console.log('Security info updated:', data);\n      },\n      error: error => {\n        console.error('Failed to fetch security info:', error);\n        this.securityInfo = {\n          mode: 'open',\n          requiresInvite: false,\n          requiresWhitelist: false\n        };\n      }\n    });\n  }\n\n  refreshSecurityInfo() {\n    this.fetchSecurityInfo();\n  }\n\n  onSubmit() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this.username.trim()) return; // Validate invite code if required\n\n      if (_this.securityInfo?.requiresInvite && !_this.inviteCode.trim()) {\n        _this.error = 'Invite code is required';\n        return;\n      }\n\n      _this.isLoading = true;\n      _this.error = '';\n\n      try {\n        yield _this.chatService.login(_this.username.trim(), _this.password.trim() || undefined, _this.inviteCode.trim() || null);\n      } catch (error) {\n        console.error('Login failed:', error);\n        _this.error = error.error?.error || error.message || 'Login failed'; // Check if password is required\n\n        if (error.error?.requiresPassword) {\n          _this.requiresPassword = true;\n        }\n      } finally {\n        _this.isLoading = false;\n      }\n    })();\n  }\n\n  onInviteCodeInput(event) {\n    const target = event.target;\n    this.inviteCode = target.value.toUpperCase();\n  }\n\n}\n\nLoginFormComponent.ɵfac = function LoginFormComponent_Factory(t) {\n  return new (t || LoginFormComponent)(i0.ɵɵdirectiveInject(i1.ChatService), i0.ɵɵdirectiveInject(i2.ApiService));\n};\n\nLoginFormComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: LoginFormComponent,\n  selectors: [[\"app-login-form\"]],\n  decls: 39,\n  vars: 18,\n  consts: [[1, \"min-h-screen\", \"flex\", \"items-center\", \"justify-center\", \"bg-gradient-to-br\", \"from-primary-50\", \"to-primary-100\"], [1, \"max-w-md\", \"w-full\", \"space-y-8\", \"p-8\"], [1, \"text-center\"], [1, \"mx-auto\", \"h-12\", \"w-12\", \"flex\", \"items-center\", \"justify-center\", \"rounded-full\", \"bg-primary-500\", \"text-white\"], [\"class\", \"w-6 h-6\", \"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 4, \"ngIf\"], [1, \"mt-6\", \"text-3xl\", \"font-extrabold\", \"text-gray-900\"], [1, \"mt-2\", \"space-y-1\"], [1, \"text-sm\", \"text-gray-600\"], [4, \"ngIf\"], [\"class\", \"flex items-center justify-center space-x-2 text-xs text-gray-500\", 4, \"ngIf\"], [1, \"mt-8\", \"space-y-6\", 3, \"ngSubmit\"], [1, \"space-y-4\"], [\"for\", \"username\", 1, \"sr-only\"], [\"id\", \"username\", \"name\", \"username\", \"type\", \"text\", \"required\", \"\", \"placeholder\", \"Enter your username\", \"maxlength\", \"50\", 1, \"relative\", \"block\", \"w-full\", \"px-3\", \"py-3\", \"border\", \"border-gray-300\", \"placeholder-gray-500\", \"text-gray-900\", \"rounded-lg\", \"focus:outline-none\", \"focus:ring-primary-500\", \"focus:border-primary-500\", \"focus:z-10\", \"sm:text-sm\", 3, \"ngModel\", \"disabled\", \"ngModelChange\"], [\"for\", \"password\", 1, \"sr-only\"], [\"id\", \"password\", \"name\", \"password\", \"type\", \"password\", \"placeholder\", \"Enter your password (optional)\", \"maxlength\", \"100\", 1, \"relative\", \"block\", \"w-full\", \"px-3\", \"py-3\", \"border\", \"border-gray-300\", \"placeholder-gray-500\", \"text-gray-900\", \"rounded-lg\", \"focus:outline-none\", \"focus:ring-primary-500\", \"focus:border-primary-500\", \"focus:z-10\", \"sm:text-sm\", 3, \"ngModel\", \"disabled\", \"ngModelChange\"], [1, \"mt-1\", \"text-xs\", \"text-gray-500\"], [\"class\", \"text-sm text-red-600 text-center bg-red-50 p-2 rounded-lg\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"group\", \"relative\", \"w-full\", \"flex\", \"justify-center\", \"py-3\", \"px-4\", \"border\", \"border-transparent\", \"text-sm\", \"font-medium\", \"rounded-lg\", \"text-white\", \"bg-primary-600\", \"hover:bg-primary-700\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-offset-2\", \"focus:ring-primary-500\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"transition-colors\", \"duration-200\", 3, \"disabled\"], [\"class\", \"flex items-center\", 4, \"ngIf\"], [1, \"text-xs\", \"text-gray-500\"], [\"type\", \"button\", 1, \"mt-2\", \"text-xs\", \"text-blue-600\", \"hover:text-blue-800\", \"underline\", 3, \"disabled\", \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-2\", \"text-xs\", \"text-gray-500\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-3\", \"h-3\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"], [\"for\", \"inviteCode\", 1, \"sr-only\"], [\"id\", \"inviteCode\", \"name\", \"inviteCode\", \"type\", \"text\", \"placeholder\", \"Enter invite code\", \"maxlength\", \"20\", 1, \"relative\", \"block\", \"w-full\", \"px-3\", \"py-3\", \"border\", \"border-gray-300\", \"placeholder-gray-500\", \"text-gray-900\", \"rounded-lg\", \"focus:outline-none\", \"focus:ring-primary-500\", \"focus:border-primary-500\", \"focus:z-10\", \"sm:text-sm\", 3, \"required\", \"ngModel\", \"disabled\", \"ngModelChange\", \"input\"], [1, \"text-sm\", \"text-red-600\", \"text-center\", \"bg-red-50\", \"p-2\", \"rounded-lg\"], [1, \"flex\", \"items-center\"], [1, \"animate-spin\", \"-ml-1\", \"mr-3\", \"h-5\", \"w-5\", \"border-2\", \"border-white\", \"border-t-transparent\", \"rounded-full\"]],\n  template: function LoginFormComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n      i0.ɵɵtemplate(4, LoginFormComponent__svg_svg_4_Template, 2, 0, \"svg\", 4);\n      i0.ɵɵtemplate(5, LoginFormComponent__svg_svg_5_Template, 2, 0, \"svg\", 4);\n      i0.ɵɵtemplate(6, LoginFormComponent__svg_svg_6_Template, 2, 0, \"svg\", 4);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(7, \"h2\", 5);\n      i0.ɵɵtext(8, \" Welcome to Chateye \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(9, \"div\", 6)(10, \"p\", 7);\n      i0.ɵɵtemplate(11, LoginFormComponent_span_11_Template, 2, 0, \"span\", 8);\n      i0.ɵɵtemplate(12, LoginFormComponent_span_12_Template, 2, 0, \"span\", 8);\n      i0.ɵɵtemplate(13, LoginFormComponent_span_13_Template, 2, 0, \"span\", 8);\n      i0.ɵɵtemplate(14, LoginFormComponent_span_14_Template, 2, 0, \"span\", 8);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(15, LoginFormComponent_div_15_Template, 5, 0, \"div\", 9);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(16, \"form\", 10);\n      i0.ɵɵlistener(\"ngSubmit\", function LoginFormComponent_Template_form_ngSubmit_16_listener() {\n        return ctx.onSubmit();\n      });\n      i0.ɵɵelementStart(17, \"div\", 11)(18, \"div\")(19, \"label\", 12);\n      i0.ɵɵtext(20, \" Username \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(21, \"input\", 13);\n      i0.ɵɵlistener(\"ngModelChange\", function LoginFormComponent_Template_input_ngModelChange_21_listener($event) {\n        return ctx.username = $event;\n      });\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(22, \"div\")(23, \"label\", 14);\n      i0.ɵɵtext(24, \" Password \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(25, \"input\", 15);\n      i0.ɵɵlistener(\"ngModelChange\", function LoginFormComponent_Template_input_ngModelChange_25_listener($event) {\n        return ctx.password = $event;\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(26, \"p\", 16);\n      i0.ɵɵtext(27, \" Password is optional unless required by your account \");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵtemplate(28, LoginFormComponent_div_28_Template, 4, 3, \"div\", 8);\n      i0.ɵɵtemplate(29, LoginFormComponent_div_29_Template, 2, 1, \"div\", 17);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(30, \"div\")(31, \"button\", 18);\n      i0.ɵɵtemplate(32, LoginFormComponent_div_32_Template, 3, 0, \"div\", 19);\n      i0.ɵɵtemplate(33, LoginFormComponent_span_33_Template, 2, 0, \"span\", 8);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(34, \"div\", 2)(35, \"p\", 20);\n      i0.ɵɵtext(36, \" This is a private chat for friends only \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(37, \"button\", 21);\n      i0.ɵɵlistener(\"click\", function LoginFormComponent_Template_button_click_37_listener() {\n        return ctx.refreshSecurityInfo();\n      });\n      i0.ɵɵtext(38, \" Refresh Security Settings \");\n      i0.ɵɵelementEnd()()()();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngIf\", (ctx.securityInfo == null ? null : ctx.securityInfo.mode) === \"invite\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", (ctx.securityInfo == null ? null : ctx.securityInfo.mode) === \"whitelist\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", (ctx.securityInfo == null ? null : ctx.securityInfo.mode) === \"open\" || !ctx.securityInfo);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngIf\", (ctx.securityInfo == null ? null : ctx.securityInfo.mode) === \"invite\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", (ctx.securityInfo == null ? null : ctx.securityInfo.mode) === \"whitelist\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", (ctx.securityInfo == null ? null : ctx.securityInfo.mode) === \"open\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.securityInfo);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", (ctx.securityInfo == null ? null : ctx.securityInfo.mode) !== \"open\");\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"ngModel\", ctx.username)(\"disabled\", ctx.isLoading);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngModel\", ctx.password)(\"disabled\", ctx.isLoading);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.securityInfo == null ? null : ctx.securityInfo.requiresInvite);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.error);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"disabled\", !ctx.username.trim() || ctx.isLoading || (ctx.securityInfo == null ? null : ctx.securityInfo.requiresInvite) && !ctx.inviteCode.trim());\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n    }\n  },\n  dependencies: [i3.NgIf, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.MaxLengthValidator, i4.NgModel, i4.NgForm],\n  styles: [\"\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImxvZ2luLWZvcm0uY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSx3Q0FBd0MiLCJmaWxlIjoibG9naW4tZm9ybS5jb21wb25lbnQuY3NzIiwic291cmNlc0NvbnRlbnQiOlsiLyogQ29tcG9uZW50LXNwZWNpZmljIHN0eWxlcyBpZiBuZWVkZWQgKi9cclxuIl19 */\"]\n});", "map": {"version": 3, "mappings": ";;;;;;;;;IAIQA;IAAAA;IACEA;IACFA;;;;;;IACAA;IAAAA;IACEA;IACFA;;;;;;IACAA;IAAAA;IACEA;IACFA;;;;;;IAOEA;IAA8CA;IAAsCA;;;;;;IACpFA;IAAiDA;IAA4BA;;;;;;IAC7EA;IAA4CA;IAAoCA;;;;;;IAChFA;IAA4BA;IAAUA;;;;;;IAExCA;IACEA;IAAAA;IACEA;IACFA;IACAA;IAAAA;IAAMA;IAAgCA;;;;;;;;IA2CxCA,4BAA0C,CAA1C,EAA0C,OAA1C,EAA0C,EAA1C;IAEIA;IACFA;IACAA;IAOEA;MAAAA;MAAA;MAAA;IAAA,GAAwB,OAAxB,EAAwB;MAAAA;MAAA;MAAA,OAGfA,iDAHe;IAGU,CAHlC;IAPFA;;;;;IAIEA;IAAAA,8GAAkD,SAAlD,EAAkDC,iBAAlD,EAAkD,UAAlD,EAAkDA,gBAAlD;;;;;;IAUJD;IACEA;IACFA;;;;;IADEA;IAAAA;;;;;;IAUAA;IACEA;IACAA;IACFA;;;;;;IACAA;IAAyBA;IAASA;;;;AC/F5C,OAAM,MAAOE,kBAAP,CAAyB;EAS7BC,YACUC,WADV,EAEUC,UAFV,EAEgC;IADtB;IACA;IAVV,gBAAW,UAAX;IACA,gBAAW,EAAX;IACA,kBAAa,EAAb;IACA,iBAAY,KAAZ;IACA,oBAAoC,IAApC;IACA,aAAQ,EAAR;IACA,wBAAmB,KAAnB;EAKI;;EAEJC,QAAQ;IACN,KAAKC,iBAAL,GADM,CAGN;;IACA,KAAKH,WAAL,CAAiBI,aAAjB,CAA+BC,SAA/B,CAAyCC,YAAY,IAAG;MACtD,IAAIA,YAAJ,EAAkB;QAChB,KAAKA,YAAL,GAAoBA,YAApB;QACAC,OAAO,CAACC,GAAR,CAAY,yCAAZ,EAAuDF,YAAvD;MACD;IACF,CALD;EAMD;;EAEOH,iBAAiB;IACvB,KAAKF,UAAL,CAAgBQ,eAAhB,GAAkCJ,SAAlC,CAA4C;MAC1CK,IAAI,EAAGC,IAAD,IAAS;QACb,KAAKL,YAAL,GAAoBK,IAApB;QACAJ,OAAO,CAACC,GAAR,CAAY,wBAAZ,EAAsCG,IAAtC;MACD,CAJyC;MAK1CC,KAAK,EAAGA,KAAD,IAAU;QACfL,OAAO,CAACK,KAAR,CAAc,gCAAd,EAAgDA,KAAhD;QACA,KAAKN,YAAL,GAAoB;UAAEO,IAAI,EAAE,MAAR;UAAgBC,cAAc,EAAE,KAAhC;UAAuCC,iBAAiB,EAAE;QAA1D,CAApB;MACD;IARyC,CAA5C;EAUD;;EAEDC,mBAAmB;IACjB,KAAKb,iBAAL;EACD;;EAEKc,QAAQ;IAAA;;IAAA;MACZ,IAAI,CAAC,KAAI,CAACC,QAAL,CAAcC,IAAd,EAAL,EAA2B,OADf,CAGZ;;MACA,IAAI,KAAI,CAACb,YAAL,EAAmBQ,cAAnB,IAAqC,CAAC,KAAI,CAACM,UAAL,CAAgBD,IAAhB,EAA1C,EAAkE;QAChE,KAAI,CAACP,KAAL,GAAa,yBAAb;QACA;MACD;;MAED,KAAI,CAACS,SAAL,GAAiB,IAAjB;MACA,KAAI,CAACT,KAAL,GAAa,EAAb;;MAEA,IAAI;QACF,MAAM,KAAI,CAACZ,WAAL,CAAiBsB,KAAjB,CACJ,KAAI,CAACJ,QAAL,CAAcC,IAAd,EADI,EAEJ,KAAI,CAACI,QAAL,CAAcJ,IAAd,MAAwBK,SAFpB,EAGJ,KAAI,CAACJ,UAAL,CAAgBD,IAAhB,MAA0B,IAHtB,CAAN;MAKD,CAND,CAME,OAAOP,KAAP,EAAmB;QACnBL,OAAO,CAACK,KAAR,CAAc,eAAd,EAA+BA,KAA/B;QACA,KAAI,CAACA,KAAL,GAAaA,KAAK,CAACA,KAAN,EAAaA,KAAb,IAAsBA,KAAK,CAACa,OAA5B,IAAuC,cAApD,CAFmB,CAInB;;QACA,IAAIb,KAAK,CAACA,KAAN,EAAac,gBAAjB,EAAmC;UACjC,KAAI,CAACA,gBAAL,GAAwB,IAAxB;QACD;MACF,CAdD,SAcU;QACR,KAAI,CAACL,SAAL,GAAiB,KAAjB;MACD;IA5BW;EA6Bb;;EAEDM,iBAAiB,CAACC,KAAD,EAAa;IAC5B,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAArB;IACA,KAAKT,UAAL,GAAkBS,MAAM,CAACC,KAAP,CAAaC,WAAb,EAAlB;EACD;;AA7E4B;;;mBAAlBjC,oBAAkBF;AAAA;;;QAAlBE;EAAkBkC;EAAAC;EAAAC;EAAAC;EAAAC;IAAA;MDT/BxC,+BAA4G,CAA5G,EAA4G,KAA5G,EAA4G,CAA5G,EAA4G,CAA5G,EAA4G,KAA5G,EAA4G,CAA5G,EAA4G,CAA5G,EAA4G,KAA5G,EAA4G,CAA5G;MAIQA;MAGAA;MAGAA;MAGFA;MACAA;MACEA;MACFA;MACAA,+BAA4B,EAA5B,EAA4B,GAA5B,EAA4B,CAA5B;MAEIA;MACAA;MACAA;MACAA;MACFA;MACAA;MAMFA;MAGFA;MAAMA;QAAA,OAAYyC,cAAZ;MAAsB,CAAtB;MACJzC,iCAAuB,EAAvB,EAAuB,KAAvB,EAAuB,EAAvB,EAAuB,OAAvB,EAAuB,EAAvB;MAGMA;MACFA;MACAA;MAOEA;QAAA;MAAA;MAPFA;MAaFA,6BAAK,EAAL,EAAK,OAAL,EAAK,EAAL;MAEIA;MACFA;MACAA;MAMEA;QAAA;MAAA;MANFA;MAUAA;MACEA;MACFA;MAGFA;MAkBAA;MAGFA;MAEAA,6BAAK,EAAL,EAAK,QAAL,EAAK,EAAL;MAMIA;MAIAA;MACFA;MAIJA,gCAAyB,EAAzB,EAAyB,GAAzB,EAAyB,EAAzB;MAEIA;MACFA;MACAA;MAEEA;QAAA,OAASyC,yBAAT;MAA8B,CAA9B;MAIAzC;MACFA;;;;MApHQA;MAAAA;MAGAA;MAAAA;MAGAA;MAAAA;MASGA;MAAAA;MACAA;MAAAA;MACAA;MAAAA;MACAA;MAAAA;MAEHA;MAAAA;MAsBFA;MAAAA,uCAAsB,UAAtB,EAAsByC,aAAtB;MAgBAzC;MAAAA,uCAAsB,UAAtB,EAAsByC,aAAtB;MASEzC;MAAAA;MAkBAA;MAAAA;MAQJA;MAAAA;MAGMA;MAAAA;MAICA;MAAAA;MAaTA;MAAAA", "names": ["i0", "ctx_r8", "LoginFormComponent", "constructor", "chatService", "apiService", "ngOnInit", "fetchSecurityInfo", "securityInfo$", "subscribe", "securityInfo", "console", "log", "getSecurityInfo", "next", "data", "error", "mode", "requiresInvite", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "refreshSecurityInfo", "onSubmit", "username", "trim", "inviteCode", "isLoading", "login", "password", "undefined", "message", "requiresPassword", "onInviteCodeInput", "event", "target", "value", "toUpperCase", "selectors", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["R:\\chateye\\FrontendAngular\\src\\app\\components\\login-form\\login-form.component.html", "R:\\chateye\\FrontendAngular\\src\\app\\components\\login-form\\login-form.component.ts"], "sourcesContent": ["<div class=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-primary-100\">\r\n  <div class=\"max-w-md w-full space-y-8 p-8\">\r\n    <div class=\"text-center\">\r\n      <div class=\"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-500 text-white\">\r\n        <svg *ngIf=\"securityInfo?.mode === 'invite'\" class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z\"></path>\r\n        </svg>\r\n        <svg *ngIf=\"securityInfo?.mode === 'whitelist'\" class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\"></path>\r\n        </svg>\r\n        <svg *ngIf=\"securityInfo?.mode === 'open' || !securityInfo\" class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"></path>\r\n        </svg>\r\n      </div>\r\n      <h2 class=\"mt-6 text-3xl font-extrabold text-gray-900\">\r\n        Welcome to Chateye\r\n      </h2>\r\n      <div class=\"mt-2 space-y-1\">\r\n        <p class=\"text-sm text-gray-600\">\r\n          <span *ngIf=\"securityInfo?.mode === 'invite'\">Enter username and invite code to join</span>\r\n          <span *ngIf=\"securityInfo?.mode === 'whitelist'\">Only approved users can join</span>\r\n          <span *ngIf=\"securityInfo?.mode === 'open'\">Enter your username to join the chat</span>\r\n          <span *ngIf=\"!securityInfo\">Loading...</span>\r\n        </p>\r\n        <div *ngIf=\"securityInfo?.mode !== 'open'\" class=\"flex items-center justify-center space-x-2 text-xs text-gray-500\">\r\n          <svg class=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"></path>\r\n          </svg>\r\n          <span>Private chat - Access restricted</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <form (ngSubmit)=\"onSubmit()\" class=\"mt-8 space-y-6\">\r\n      <div class=\"space-y-4\">\r\n        <div>\r\n          <label for=\"username\" class=\"sr-only\">\r\n            Username\r\n          </label>\r\n          <input\r\n            id=\"username\"\r\n            name=\"username\"\r\n            type=\"text\"\r\n            required\r\n            class=\"relative block w-full px-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm\"\r\n            placeholder=\"Enter your username\"\r\n            [(ngModel)]=\"username\"\r\n            [disabled]=\"isLoading\"\r\n            maxlength=\"50\"\r\n          />\r\n        </div>\r\n\r\n        <div>\r\n          <label for=\"password\" class=\"sr-only\">\r\n            Password\r\n          </label>\r\n          <input\r\n            id=\"password\"\r\n            name=\"password\"\r\n            type=\"password\"\r\n            class=\"relative block w-full px-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm\"\r\n            placeholder=\"Enter your password (optional)\"\r\n            [(ngModel)]=\"password\"\r\n            [disabled]=\"isLoading\"\r\n            maxlength=\"100\"\r\n          />\r\n          <p class=\"mt-1 text-xs text-gray-500\">\r\n            Password is optional unless required by your account\r\n          </p>\r\n        </div>\r\n\r\n        <div *ngIf=\"securityInfo?.requiresInvite\">\r\n          <label for=\"inviteCode\" class=\"sr-only\">\r\n            Invite Code\r\n          </label>\r\n          <input\r\n            id=\"inviteCode\"\r\n            name=\"inviteCode\"\r\n            type=\"text\"\r\n            [required]=\"securityInfo?.requiresInvite || false\"\r\n            class=\"relative block w-full px-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm\"\r\n            placeholder=\"Enter invite code\"\r\n            [(ngModel)]=\"inviteCode\"\r\n            [disabled]=\"isLoading\"\r\n            maxlength=\"20\"\r\n            (input)=\"onInviteCodeInput($event)\"\r\n          />\r\n        </div>\r\n\r\n        <div *ngIf=\"error\" class=\"text-sm text-red-600 text-center bg-red-50 p-2 rounded-lg\">\r\n          {{ error }}\r\n        </div>\r\n      </div>\r\n\r\n      <div>\r\n        <button\r\n          type=\"submit\"\r\n          [disabled]=\"!username.trim() || isLoading || (securityInfo?.requiresInvite && !inviteCode.trim())\"\r\n          class=\"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200\"\r\n        >\r\n          <div *ngIf=\"isLoading\" class=\"flex items-center\">\r\n            <div class=\"animate-spin -ml-1 mr-3 h-5 w-5 border-2 border-white border-t-transparent rounded-full\"></div>\r\n            Joining...\r\n          </div>\r\n          <span *ngIf=\"!isLoading\">Join Chat</span>\r\n        </button>\r\n      </div>\r\n    </form>\r\n    \r\n    <div class=\"text-center\">\r\n      <p class=\"text-xs text-gray-500\">\r\n        This is a private chat for friends only\r\n      </p>\r\n      <button \r\n        type=\"button\"\r\n        (click)=\"refreshSecurityInfo()\"\r\n        class=\"mt-2 text-xs text-blue-600 hover:text-blue-800 underline\"\r\n        [disabled]=\"isLoading\"\r\n      >\r\n        Refresh Security Settings\r\n      </button>\r\n    </div>\r\n  </div>\r\n</div>\r\n", "import { Component, OnInit } from '@angular/core';\r\nimport { ChatService } from '../../services/chat.service';\r\nimport { ApiService, SecurityInfo } from '../../services/api.service';\r\n\r\n@Component({\r\n  selector: 'app-login-form',\r\n  templateUrl: './login-form.component.html',\r\n  styleUrls: ['./login-form.component.css']\r\n})\r\nexport class LoginFormComponent implements OnInit {\r\n  username = 'testuser';\r\n  password = '';\r\n  inviteCode = '';\r\n  isLoading = false;\r\n  securityInfo: SecurityInfo | null = null;\r\n  error = '';\r\n  requiresPassword = false;\r\n\r\n  constructor(\r\n    private chatService: ChatService,\r\n    private apiService: ApiService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.fetchSecurityInfo();\r\n    \r\n    // Subscribe to security info changes from ChatService\r\n    this.chatService.securityInfo$.subscribe(securityInfo => {\r\n      if (securityInfo) {\r\n        this.securityInfo = securityInfo;\r\n        console.log('Security info updated from ChatService:', securityInfo);\r\n      }\r\n    });\r\n  }\r\n\r\n  private fetchSecurityInfo(): void {\r\n    this.apiService.getSecurityInfo().subscribe({\r\n      next: (data) => {\r\n        this.securityInfo = data;\r\n        console.log('Security info updated:', data);\r\n      },\r\n      error: (error) => {\r\n        console.error('Failed to fetch security info:', error);\r\n        this.securityInfo = { mode: 'open', requiresInvite: false, requiresWhitelist: false };\r\n      }\r\n    });\r\n  }\r\n\r\n  refreshSecurityInfo(): void {\r\n    this.fetchSecurityInfo();\r\n  }\r\n\r\n  async onSubmit(): Promise<void> {\r\n    if (!this.username.trim()) return;\r\n    \r\n    // Validate invite code if required\r\n    if (this.securityInfo?.requiresInvite && !this.inviteCode.trim()) {\r\n      this.error = 'Invite code is required';\r\n      return;\r\n    }\r\n\r\n    this.isLoading = true;\r\n    this.error = '';\r\n    \r\n    try {\r\n      await this.chatService.login(\r\n        this.username.trim(), \r\n        this.password.trim() || undefined, \r\n        this.inviteCode.trim() || null\r\n      );\r\n    } catch (error: any) {\r\n      console.error('Login failed:', error);\r\n      this.error = error.error?.error || error.message || 'Login failed';\r\n      \r\n      // Check if password is required\r\n      if (error.error?.requiresPassword) {\r\n        this.requiresPassword = true;\r\n      }\r\n    } finally {\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  onInviteCodeInput(event: Event): void {\r\n    const target = event.target as HTMLInputElement;\r\n    this.inviteCode = target.value.toUpperCase();\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module"}