const express = require('express');
const router = express.Router();
const User = require('../models/User');
const Configuration = require('../models/Configuration');

// Middleware to check admin access
const requireAdmin = async (req, res, next) => {
  try {
    // For GET requests, check query params; for POST requests, check body
    const username = req.method === 'GET' ? req.query.username : req.body.username;
    
    if (!username) {
      return res.status(400).json({ error: 'Username required' });
    }
    
    const isAdmin = await Configuration.isUserAdmin(username);
    
    if (!isAdmin) {
      return res.status(403).json({ error: 'Admin access required' });
    }
    
    next();
  } catch (error) {
    console.error('Admin check error:', error);
    res.status(500).json({ error: 'Admin check failed' });
  }
};

// Middleware to verify user's own password
const verifyOwnPassword = async (req, res, next) => {
  try {
    const { username, currentPassword } = req.body;
    
    if (!username || !currentPassword) {
      return res.status(400).json({ error: 'Username and current password required' });
    }
    
    const isValidPassword = await User.verifyPassword(username, currentPassword);
    if (!isValidPassword) {
      return res.status(403).json({ error: 'Invalid current password' });
    }
    
    next();
  } catch (error) {
    console.error('Password verification error:', error);
    res.status(500).json({ error: 'Password verification failed' });
  }
};

// POST /passwords/change - User changes their own password
router.post('/change', verifyOwnPassword, async (req, res) => {
  try {
    const { username, newPassword } = req.body;
    
    if (!newPassword || newPassword.length < 6) {
      return res.status(400).json({ error: 'New password must be at least 6 characters long' });
    }
    
    const user = await User.findByUsername(username);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    await User.setPassword(user.id, newPassword);
    
    res.json({ message: 'Password changed successfully' });
  } catch (error) {
    console.error('Error changing password:', error);
    res.status(500).json({ error: 'Failed to change password' });
  }
});

// POST /passwords/set - Set password for user (admin only)
router.post('/set', requireAdmin, async (req, res) => {
  try {
    const { username: adminUser, targetUsername, password } = req.body;
    
    if (!targetUsername || !password) {
      return res.status(400).json({ error: 'Target username and password required' });
    }
    
    if (password.length < 6) {
      return res.status(400).json({ error: 'Password must be at least 6 characters long' });
    }
    
    const user = await User.findByUsername(targetUsername);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    await User.setPassword(user.id, password);
    
    res.json({ message: 'Password set successfully' });
  } catch (error) {
    console.error('Error setting password:', error);
    res.status(500).json({ error: 'Failed to set password' });
  }
});

// POST /passwords/reset - Reset user password (admin only)
router.post('/reset', requireAdmin, async (req, res) => {
  try {
    const { username: adminUser, targetUsername, newPassword } = req.body;
    
    if (!targetUsername || !newPassword) {
      return res.status(400).json({ error: 'Target username and new password required' });
    }
    
    if (newPassword.length < 6) {
      return res.status(400).json({ error: 'New password must be at least 6 characters long' });
    }
    
    await User.resetPassword(targetUsername, newPassword);
    
    res.json({ message: 'Password reset successfully' });
  } catch (error) {
    console.error('Error resetting password:', error);
    res.status(500).json({ error: 'Failed to reset password' });
  }
});

// POST /passwords/create-user - Create user with password (admin only)
router.post('/create-user', requireAdmin, async (req, res) => {
  try {
    const { username: adminUser, targetUsername, password } = req.body;
    
    if (!targetUsername || !password) {
      return res.status(400).json({ error: 'Username and password required' });
    }
    
    if (password.length < 6) {
      return res.status(400).json({ error: 'Password must be at least 6 characters long' });
    }
    
    const user = await User.createUserWithPassword(targetUsername, password);
    
    res.json({ message: 'User created successfully', user: { id: user.id, username: user.username } });
  } catch (error) {
    console.error('Error creating user:', error);
    if (error.message === 'User already exists') {
      res.status(400).json({ error: 'User already exists' });
    } else {
      res.status(500).json({ error: 'Failed to create user' });
    }
  }
});

// GET /passwords/check/:username - Check if user has password set
router.get('/check/:username', async (req, res) => {
  try {
    const { username } = req.params;
    
    const hasPassword = await User.hasPassword(username);
    
    res.json({ hasPassword });
  } catch (error) {
    console.error('Error checking password status:', error);
    res.status(500).json({ error: 'Failed to check password status' });
  }
});

module.exports = router;
