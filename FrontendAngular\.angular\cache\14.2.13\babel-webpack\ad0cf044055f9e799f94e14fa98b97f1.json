{"ast": null, "code": "import { isBinary } from \"./is-binary.js\";\n/**\n * Replaces every Buffer | ArrayBuffer | Blob | File in packet with a numbered placeholder.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @public\n */\n\nexport function deconstructPacket(packet) {\n  const buffers = [];\n  const packetData = packet.data;\n  const pack = packet;\n  pack.data = _deconstructPacket(packetData, buffers);\n  pack.attachments = buffers.length; // number of binary 'attachments'\n\n  return {\n    packet: pack,\n    buffers: buffers\n  };\n}\n\nfunction _deconstructPacket(data, buffers) {\n  if (!data) return data;\n\n  if (isBinary(data)) {\n    const placeholder = {\n      _placeholder: true,\n      num: buffers.length\n    };\n    buffers.push(data);\n    return placeholder;\n  } else if (Array.isArray(data)) {\n    const newData = new Array(data.length);\n\n    for (let i = 0; i < data.length; i++) {\n      newData[i] = _deconstructPacket(data[i], buffers);\n    }\n\n    return newData;\n  } else if (typeof data === \"object\" && !(data instanceof Date)) {\n    const newData = {};\n\n    for (const key in data) {\n      if (Object.prototype.hasOwnProperty.call(data, key)) {\n        newData[key] = _deconstructPacket(data[key], buffers);\n      }\n    }\n\n    return newData;\n  }\n\n  return data;\n}\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @public\n */\n\n\nexport function reconstructPacket(packet, buffers) {\n  packet.data = _reconstructPacket(packet.data, buffers);\n  delete packet.attachments; // no longer useful\n\n  return packet;\n}\n\nfunction _reconstructPacket(data, buffers) {\n  if (!data) return data;\n\n  if (data && data._placeholder === true) {\n    const isIndexValid = typeof data.num === \"number\" && data.num >= 0 && data.num < buffers.length;\n\n    if (isIndexValid) {\n      return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n    } else {\n      throw new Error(\"illegal attachments\");\n    }\n  } else if (Array.isArray(data)) {\n    for (let i = 0; i < data.length; i++) {\n      data[i] = _reconstructPacket(data[i], buffers);\n    }\n  } else if (typeof data === \"object\") {\n    for (const key in data) {\n      if (Object.prototype.hasOwnProperty.call(data, key)) {\n        data[key] = _reconstructPacket(data[key], buffers);\n      }\n    }\n  }\n\n  return data;\n}", "map": null, "metadata": {}, "sourceType": "module"}