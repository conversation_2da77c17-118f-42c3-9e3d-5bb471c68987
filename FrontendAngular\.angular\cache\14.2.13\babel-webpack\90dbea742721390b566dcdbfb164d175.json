{"ast": null, "code": "import { Event<PERSON><PERSON><PERSON>, ElementRef, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectorRef } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/divider\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/tooltip\";\nimport * as i6 from \"@angular/material/progress-spinner\";\nimport * as i7 from \"../message/message.component\";\nconst _c0 = [\"scrollContainer\"];\nconst _c1 = [\"scrollAnchor\"];\n\nfunction MessageListVirtualComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"mat-spinner\", 5);\n    i0.ɵɵelementStart(2, \"p\", 6);\n    i0.ɵɵtext(3, \"Loading messages...\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction MessageListVirtualComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"mat-icon\", 8);\n    i0.ɵɵtext(2, \"chat_bubble_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 9);\n    i0.ɵɵtext(4, \"No messages yet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 10);\n    i0.ɵɵtext(6, \"Start the conversation!\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction MessageListVirtualComponent_div_3_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 20);\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"height\", ctx_r4.startIndex * ctx_r4.ITEM_HEIGHT, \"px\");\n  }\n}\n\nfunction MessageListVirtualComponent_div_3_div_5_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵelement(1, \"mat-divider\");\n    i0.ɵɵelementStart(2, \"span\", 26);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"mat-divider\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const message_r9 = i0.ɵɵnextContext().$implicit;\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r11.formatDate(message_r9.timestamp));\n  }\n}\n\nfunction MessageListVirtualComponent_div_3_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtemplate(1, MessageListVirtualComponent_div_3_div_5_div_1_Template, 5, 1, \"div\", 22);\n    i0.ɵɵelementStart(2, \"div\", 23)(3, \"app-message\", 24);\n    i0.ɵɵlistener(\"onReply\", function MessageListVirtualComponent_div_3_div_5_Template_app_message_onReply_3_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r13.onMessageReply($event));\n    })(\"onAddReaction\", function MessageListVirtualComponent_div_3_div_5_Template_app_message_onAddReaction_3_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.onMessageAddReaction($event));\n    })(\"onRemoveReaction\", function MessageListVirtualComponent_div_3_div_5_Template_app_message_onRemoveReaction_3_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.onMessageRemoveReaction($event));\n    })(\"onEdit\", function MessageListVirtualComponent_div_3_div_5_Template_app_message_onEdit_3_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.onMessageEdit($event));\n    })(\"onDelete\", function MessageListVirtualComponent_div_3_div_5_Template_app_message_onDelete_3_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r18 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r18.onMessageDelete($event));\n    })(\"onMessageClick\", function MessageListVirtualComponent_div_3_div_5_Template_app_message_onMessageClick_3_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r19 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r19.onMessageClickHandler($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const message_r9 = ctx.$implicit;\n    const i_r10 = ctx.index;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"own-message\", ctx_r5.isOwnMessage(message_r9));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.shouldShowDateSeparator(message_r9, i_r10));\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"data-message-id\", message_r9.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"message\", message_r9)(\"currentUser\", ctx_r5.currentUser)(\"highlightedMessageId\", ctx_r5.highlightedMessageId);\n  }\n}\n\nfunction MessageListVirtualComponent_div_3_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 20);\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"height\", (ctx_r7.messages.length - ctx_r7.endIndex - 1) * ctx_r7.ITEM_HEIGHT, \"px\");\n  }\n}\n\nfunction MessageListVirtualComponent_div_3_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function MessageListVirtualComponent_div_3_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r20.scrollToBottom());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"keyboard_arrow_down\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction MessageListVirtualComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12, 13);\n    i0.ɵɵlistener(\"scroll\", function MessageListVirtualComponent_div_3_Template_div_scroll_1_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.onScroll($event));\n    });\n    i0.ɵɵtemplate(3, MessageListVirtualComponent_div_3_div_3_Template, 1, 2, \"div\", 14);\n    i0.ɵɵelementStart(4, \"div\", 15);\n    i0.ɵɵtemplate(5, MessageListVirtualComponent_div_3_div_5_Template, 4, 7, \"div\", 16);\n    i0.ɵɵelement(6, \"div\", 17, 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, MessageListVirtualComponent_div_3_div_8_Template, 1, 2, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, MessageListVirtualComponent_div_3_button_9_Template, 3, 0, \"button\", 19);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.startIndex > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.visibleMessages)(\"ngForTrackBy\", ctx_r2.trackByMessageId);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.endIndex < ctx_r2.messages.length - 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showScrollButton);\n  }\n}\n\nexport let MessageListVirtualComponent = /*#__PURE__*/(() => {\n  class MessageListVirtualComponent {\n    constructor(cdr) {\n      this.cdr = cdr;\n      this.messages = [];\n      this.currentUser = null;\n      this.loading = false;\n      this.highlightedMessageId = null;\n      this.onReply = new EventEmitter();\n      this.onAddReaction = new EventEmitter();\n      this.onRemoveReaction = new EventEmitter();\n      this.onEdit = new EventEmitter();\n      this.onDelete = new EventEmitter();\n      this.onMessageClick = new EventEmitter();\n      this.showScrollButton = false;\n      this.isNearBottom = true;\n      this.dateCache = new Map(); // Virtual scrolling properties\n\n      this.ITEM_HEIGHT = 80; // Estimated height per message\n\n      this.BUFFER_SIZE = 5; // Extra items to render outside viewport\n\n      this.visibleMessages = [];\n      this.startIndex = 0;\n      this.endIndex = 0;\n      this.containerHeight = 0;\n    }\n\n    ngAfterViewInit() {\n      this.scrollToBottom(false);\n      this.setupScrollListener();\n      this.updateVisibleMessages();\n    }\n\n    ngOnChanges(changes) {\n      if (changes['messages']) {\n        console.log('MessageListVirtual: Messages changed, count:', this.messages.length);\n        const previousLength = changes['messages'].previousValue?.length || 0;\n        const currentLength = this.messages.length; // Check if new messages were added (not just initial load)\n\n        const newMessagesAdded = currentLength > previousLength && previousLength > 0;\n        this.updateVisibleMessages();\n\n        if (!changes['messages'].firstChange) {\n          // Auto-scroll only if user is near bottom or new messages were added\n          if (this.isNearBottom || newMessagesAdded) {\n            setTimeout(() => {\n              this.scrollToBottom(); // Force update visible messages after scrolling\n\n              setTimeout(() => {\n                this.updateVisibleMessages();\n                this.cdr.markForCheck(); // Force change detection\n              }, 50);\n            }, 100);\n          }\n        } // Force change detection for virtual scrolling updates\n\n\n        this.cdr.markForCheck();\n      }\n    }\n\n    ngOnDestroy() {\n      if (this.scrollTimeout) {\n        clearTimeout(this.scrollTimeout);\n      }\n\n      if (this.highlightTimeout) {\n        clearTimeout(this.highlightTimeout);\n      }\n\n      if (this.scrollListener && this.scrollContainer) {\n        this.scrollContainer.nativeElement.removeEventListener('scroll', this.scrollListener);\n      }\n\n      this.dateCache.clear();\n    }\n\n    setupScrollListener() {\n      if (this.scrollContainer) {\n        this.scrollListener = () => {\n          this.checkScrollPosition();\n          this.updateVisibleMessages();\n        };\n\n        this.scrollContainer.nativeElement.addEventListener('scroll', this.scrollListener);\n      }\n    }\n\n    onScroll(event) {\n      this.checkScrollPosition();\n      this.updateVisibleMessages();\n    }\n\n    checkScrollPosition() {\n      if (!this.scrollContainer) return;\n      const element = this.scrollContainer.nativeElement;\n      const threshold = 100;\n      const isNearBottom = element.scrollTop + element.clientHeight >= element.scrollHeight - threshold;\n      this.isNearBottom = isNearBottom;\n      this.showScrollButton = !isNearBottom && this.messages.length > 0; // Update visible messages when scroll position changes\n\n      this.updateVisibleMessages();\n    }\n\n    updateVisibleMessages() {\n      if (!this.scrollContainer || this.messages.length === 0) {\n        this.visibleMessages = this.messages;\n        return;\n      }\n\n      const element = this.scrollContainer.nativeElement;\n      const scrollTop = element.scrollTop;\n      const containerHeight = element.clientHeight;\n      const scrollHeight = element.scrollHeight;\n      this.containerHeight = containerHeight; // Calculate visible range\n\n      this.startIndex = Math.max(0, Math.floor(scrollTop / this.ITEM_HEIGHT) - this.BUFFER_SIZE);\n      this.endIndex = Math.min(this.messages.length - 1, Math.ceil((scrollTop + containerHeight) / this.ITEM_HEIGHT) + this.BUFFER_SIZE); // If user is near bottom (within 100px), show all messages from a certain point\n\n      const isNearBottom = scrollTop + containerHeight >= scrollHeight - 100;\n\n      if (isNearBottom && this.messages.length > 0) {\n        // Show more messages when near bottom to ensure new messages are visible\n        const messagesToShow = Math.min(20, this.messages.length); // Show last 20 messages\n\n        this.startIndex = Math.max(0, this.messages.length - messagesToShow);\n        this.endIndex = this.messages.length - 1;\n      } // Update visible messages\n\n\n      this.visibleMessages = this.messages.slice(this.startIndex, this.endIndex + 1);\n      this.cdr.markForCheck();\n    }\n\n    scrollToBottom(smooth = true) {\n      if (this.scrollAnchor) {\n        this.scrollAnchor.nativeElement.scrollIntoView({\n          behavior: smooth ? 'smooth' : 'auto',\n          block: 'end'\n        }); // Update visible messages after scrolling\n\n        setTimeout(() => {\n          this.updateVisibleMessages();\n          this.checkScrollPosition();\n        }, 100);\n      }\n    }\n\n    shouldShowDateSeparator(message, index) {\n      const actualIndex = this.startIndex + index;\n      if (actualIndex === 0) return true;\n      const prevMessage = this.messages[actualIndex - 1];\n      return new Date(message.timestamp).toDateString() !== new Date(prevMessage.timestamp).toDateString();\n    }\n\n    formatDate(timestamp) {\n      // Use cache to avoid repeated date calculations\n      if (this.dateCache.has(timestamp)) {\n        return this.dateCache.get(timestamp);\n      }\n\n      const date = new Date(timestamp);\n      const today = new Date();\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n      let formattedDate;\n\n      if (date.toDateString() === today.toDateString()) {\n        formattedDate = 'Today';\n      } else if (date.toDateString() === yesterday.toDateString()) {\n        formattedDate = 'Yesterday';\n      } else {\n        formattedDate = date.toLocaleDateString('en-US', {\n          weekday: 'long',\n          year: 'numeric',\n          month: 'long',\n          day: 'numeric'\n        });\n      }\n\n      this.dateCache.set(timestamp, formattedDate);\n      return formattedDate;\n    }\n\n    isOwnMessage(message) {\n      return message.username === this.currentUser;\n    }\n\n    trackByMessageId(index, message) {\n      return message.id || index.toString();\n    }\n\n    onMessageReply(message) {\n      this.onReply.emit(message);\n    }\n\n    onMessageAddReaction(event) {\n      this.onAddReaction.emit(event);\n    }\n\n    onMessageRemoveReaction(data) {\n      this.onRemoveReaction.emit(data);\n    }\n\n    onMessageEdit(message) {\n      this.onEdit.emit(message);\n    }\n\n    onMessageDelete(message) {\n      this.onDelete.emit(message);\n    }\n\n    onMessageClickHandler(message) {\n      this.onMessageClick.emit(message); // If this is a request to scroll to a specific message (from reply indicator)\n\n      if (message.id && message.id !== message.id) {\n        this.scrollToMessage(message.id);\n      }\n    }\n\n    scrollToMessage(messageId) {\n      if (!this.scrollContainer) return;\n      const messageIndex = this.messages.findIndex(msg => msg.id === messageId);\n      if (messageIndex === -1) return;\n      const element = this.scrollContainer.nativeElement;\n      const scrollTop = messageIndex * this.ITEM_HEIGHT; // Smooth scroll to the message\n\n      element.scrollTo({\n        top: scrollTop,\n        behavior: 'smooth'\n      }); // Update visible messages after scrolling\n\n      setTimeout(() => {\n        this.updateVisibleMessages();\n        this.cdr.markForCheck();\n      }, 500);\n    } // Debug method to check scroll state\n\n\n    debugScrollState() {\n      if (!this.scrollContainer) {\n        console.log('Scroll container not available');\n        return;\n      }\n\n      const element = this.scrollContainer.nativeElement;\n      console.log('=== Virtual Scroll Debug ===');\n      console.log('Total messages:', this.messages.length);\n      console.log('Visible messages:', this.visibleMessages.length);\n      console.log('Start index:', this.startIndex);\n      console.log('End index:', this.endIndex);\n      console.log('Scroll top:', element.scrollTop);\n      console.log('Container height:', element.clientHeight);\n      console.log('Scroll height:', element.scrollHeight);\n      console.log('Is near bottom:', this.isNearBottom);\n      console.log('Show scroll button:', this.showScrollButton);\n      console.log('==========================');\n    }\n\n  }\n\n  MessageListVirtualComponent.ɵfac = function MessageListVirtualComponent_Factory(t) {\n    return new (t || MessageListVirtualComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n\n  MessageListVirtualComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: MessageListVirtualComponent,\n    selectors: [[\"app-message-list-virtual\"]],\n    viewQuery: function MessageListVirtualComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollContainer = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollAnchor = _t.first);\n      }\n    },\n    inputs: {\n      messages: \"messages\",\n      currentUser: \"currentUser\",\n      loading: \"loading\",\n      highlightedMessageId: \"highlightedMessageId\"\n    },\n    outputs: {\n      onReply: \"onReply\",\n      onAddReaction: \"onAddReaction\",\n      onRemoveReaction: \"onRemoveReaction\",\n      onEdit: \"onEdit\",\n      onDelete: \"onDelete\",\n      onMessageClick: \"onMessageClick\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 4,\n    vars: 3,\n    consts: [[1, \"messages-container\"], [\"class\", \"loading-state\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"messages-wrapper\", 4, \"ngIf\"], [1, \"loading-state\"], [\"diameter\", \"40\"], [1, \"loading-text\"], [1, \"empty-state\"], [1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-description\"], [1, \"messages-wrapper\"], [1, \"scroll-container\", 3, \"scroll\"], [\"scrollContainer\", \"\"], [\"class\", \"virtual-spacer\", 3, \"height\", 4, \"ngIf\"], [1, \"messages-list\"], [\"class\", \"message-wrapper\", 3, \"own-message\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"scroll-anchor\"], [\"scrollAnchor\", \"\"], [\"mat-fab\", \"\", \"class\", \"scroll-button\", \"matTooltip\", \"Scroll to bottom\", 3, \"click\", 4, \"ngIf\"], [1, \"virtual-spacer\"], [1, \"message-wrapper\"], [\"class\", \"date-separator\", 4, \"ngIf\"], [1, \"message-item\"], [3, \"message\", \"currentUser\", \"highlightedMessageId\", \"onReply\", \"onAddReaction\", \"onRemoveReaction\", \"onEdit\", \"onDelete\", \"onMessageClick\"], [1, \"date-separator\"], [1, \"date-text\"], [\"mat-fab\", \"\", \"matTooltip\", \"Scroll to bottom\", 1, \"scroll-button\", 3, \"click\"]],\n    template: function MessageListVirtualComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵtemplate(1, MessageListVirtualComponent_div_1_Template, 4, 0, \"div\", 1);\n        i0.ɵɵtemplate(2, MessageListVirtualComponent_div_2_Template, 7, 0, \"div\", 2);\n        i0.ɵɵtemplate(3, MessageListVirtualComponent_div_3_Template, 10, 5, \"div\", 3);\n        i0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.messages.length === 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.messages.length > 0);\n      }\n    },\n    dependencies: [i1.NgForOf, i1.NgIf, i2.MatDivider, i3.MatButton, i4.MatIcon, i5.MatTooltip, i6.MatProgressSpinner, i7.MessageComponent],\n    styles: [\".messages-container[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column}.loading-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:200px;color:#666}.loading-text[_ngcontent-%COMP%]{margin-top:16px;font-size:14px}.empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:200px;color:#666}.empty-icon[_ngcontent-%COMP%]{font-size:48px;margin-bottom:16px;opacity:.5}.empty-title[_ngcontent-%COMP%]{margin:0 0 8px;font-size:18px;font-weight:500}.empty-description[_ngcontent-%COMP%]{margin:0;font-size:14px;opacity:.7}.messages-wrapper[_ngcontent-%COMP%]{flex:1;position:relative;height:100%}.scroll-container[_ngcontent-%COMP%]{height:100%;overflow-y:auto;overflow-x:hidden;scroll-behavior:smooth}.messages-list[_ngcontent-%COMP%]{padding:16px}.virtual-spacer[_ngcontent-%COMP%]{width:100%}.message-wrapper[_ngcontent-%COMP%]{margin-bottom:8px;display:flex;flex-direction:column}.message-wrapper.own-message[_ngcontent-%COMP%]{align-items:flex-end}.date-separator[_ngcontent-%COMP%]{display:flex;align-items:center;margin:16px 0;color:#666;font-size:12px;font-weight:500}.date-separator[_ngcontent-%COMP%]   mat-divider[_ngcontent-%COMP%]{flex:1;margin:0 16px}.date-text[_ngcontent-%COMP%]{white-space:nowrap;text-transform:uppercase;letter-spacing:.5px}.message-item[_ngcontent-%COMP%]{max-width:70%;min-width:200px}.scroll-button[_ngcontent-%COMP%]{position:absolute;bottom:20px;right:20px;z-index:10}@media (max-width: 768px){.message-item[_ngcontent-%COMP%]{max-width:85%;min-width:150px}.scroll-button[_ngcontent-%COMP%]{bottom:16px;right:16px}}.scroll-container[_ngcontent-%COMP%]{contain:layout style paint}.message-wrapper[_ngcontent-%COMP%]{contain:layout style}.scroll-container[_ngcontent-%COMP%]{scroll-behavior:smooth}.scroll-container[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px}.scroll-container[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#f1f1f1;border-radius:3px}.scroll-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#c1c1c1;border-radius:3px}.scroll-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#a8a8a8}.highlight-flash[_ngcontent-%COMP%]{animation:highlightFlash 2s ease-in-out}@keyframes highlightFlash{0%{background-color:#ffc1074d;transform:scale(1.02)}50%{background-color:#ffc10799;transform:scale(1.05)}to{background-color:transparent;transform:scale(1)}}\"],\n    changeDetection: 0\n  });\n  return MessageListVirtualComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}