{"ast": null, "code": "import _asyncToGenerator from \"R:/chateye/FrontendAngular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { ApiService, Message, Group, User, SecurityInfo } from './api.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./api.service\";\nimport * as i2 from \"./socket.service\";\nimport * as i3 from \"./notification.service\";\nexport class ChatService {\n  constructor(apiService, socketService, notificationService) {\n    this.apiService = apiService;\n    this.socketService = socketService;\n    this.notificationService = notificationService;\n    this.userSubject = new BehaviorSubject(null);\n    this.messagesSubject = new BehaviorSubject([]);\n    this.onlineUsersSubject = new BehaviorSubject([]);\n    this.groupsSubject = new BehaviorSubject([]);\n    this.currentGroupSubject = new BehaviorSubject(null);\n    this.replyToSubject = new BehaviorSubject(null);\n    this.loadingSubject = new BehaviorSubject(false);\n    this.isAdminSubject = new BehaviorSubject(false);\n    this.showAdminPanelSubject = new BehaviorSubject(false);\n    this.securityInfoSubject = new BehaviorSubject(null);\n    this.editingMessageSubject = new BehaviorSubject(null); // Public observables\n\n    this.user$ = this.userSubject.asObservable();\n    this.messages$ = this.messagesSubject.asObservable();\n    this.onlineUsers$ = this.onlineUsersSubject.asObservable();\n    this.groups$ = this.groupsSubject.asObservable();\n    this.currentGroup$ = this.currentGroupSubject.asObservable();\n    this.replyTo$ = this.replyToSubject.asObservable();\n    this.loading$ = this.loadingSubject.asObservable();\n    this.isAdmin$ = this.isAdminSubject.asObservable();\n    this.showAdminPanel$ = this.showAdminPanelSubject.asObservable();\n    this.securityInfo$ = this.securityInfoSubject.asObservable();\n    this.editingMessage$ = this.editingMessageSubject.asObservable();\n    this.connected$ = this.socketService.isConnected$; // Computed observables\n\n    this.isLoggedIn$ = this.user$.pipe(map(user => !!user));\n    this.isLoadingGroups = false;\n    this.isLoadingMessages = false;\n    this.lastMessageLoadTime = 0;\n    this.MESSAGE_LOAD_THROTTLE = 1000; // 1 second throttle\n\n    try {\n      this.setupSocketListeners();\n      this.setupMessageRefresh(); // Expose manual group loading globally for debugging\n\n      window.manualLoadGroups = () => this.manualLoadGroups();\n\n      window.debugChatState = () => this.debugState();\n\n      window.debugScrollState = () => {\n        // This will be called from the component\n        console.log('Use debugScrollState() from the message list component');\n      };\n    } catch (error) {\n      console.error('Error initializing ChatService:', error);\n    }\n  }\n\n  setupSocketListeners() {\n    this.socketService.on('connect', () => {\n      console.log('Connected to server'); // Clear any loading states when connected\n\n      this.loadingSubject.next(false);\n    });\n    this.socketService.on('disconnect', () => {\n      console.log('Disconnected from server'); // Don't set loading to true on disconnect to avoid UI freeze\n    });\n    this.socketService.on('connect_error', error => {\n      console.error('Socket connection error:', error);\n      this.loadingSubject.next(false);\n    });\n    this.socketService.on('userGroups', userGroups => {\n      console.log('Received user groups:', userGroups?.length || 0, 'groups');\n      this.groupsSubject.next(userGroups || []);\n\n      if (userGroups && userGroups.length > 0) {\n        console.log('Auto-selecting first group:', userGroups[0].name);\n        this.currentGroupSubject.next(userGroups[0]); // Load messages for the selected group\n\n        this.loadRecentMessages(userGroups[0].id);\n      } else {\n        console.log('No groups available for user'); // Clear current group and messages if no groups\n\n        this.currentGroupSubject.next(null);\n        this.messagesSubject.next([]);\n      }\n    });\n    this.socketService.on('recentMessages', messages => {\n      console.log('Received recent messages:', messages.length);\n      console.log('Recent messages data:', messages);\n      this.messagesSubject.next(messages || []);\n      console.log('Recent messages subject updated, current value:', this.messagesSubject.value.length);\n    });\n    this.socketService.on('groupJoined', ({\n      groupId\n    }) => {\n      console.log('Joined group:', groupId);\n      const groups = this.groupsSubject.value;\n      const group = groups.find(g => g.id === groupId);\n\n      if (group) {\n        this.currentGroupSubject.next(group);\n      }\n    });\n    this.socketService.on('newMessage', message => {\n      console.log('New message received:', message);\n      console.log('Current user:', this.userSubject.value);\n      console.log('Current group:', this.currentGroupSubject.value?.id);\n      console.log('Message group ID:', message.groupId);\n      const currentMessages = this.messagesSubject.value;\n      const currentUser = this.userSubject.value; // Check if this is our own message (to replace optimistic message)\n\n      if (message.username === currentUser) {\n        // Find and replace optimistic message with real message\n        const optimisticIndex = currentMessages.findIndex(msg => msg.id.startsWith('temp_') && msg.text === message.text && msg.username === currentUser && msg.groupId === message.groupId);\n\n        if (optimisticIndex !== -1) {\n          // Replace optimistic message\n          const updatedMessages = [...currentMessages];\n          updatedMessages[optimisticIndex] = message;\n          this.messagesSubject.next(updatedMessages);\n          console.log('Replaced optimistic message with real message from socket:', message);\n        } else {\n          // Add new message if no optimistic message found\n          this.messagesSubject.next([...currentMessages, message]);\n          console.log('Added new message from socket (no optimistic message found):', message);\n        }\n      } else {\n        // Add message from other users\n        this.messagesSubject.next([...currentMessages, message]);\n        console.log('Added message from other user:', message);\n      } // Show notification if not current user and window not focused\n\n\n      if (message.username !== currentUser && document.hidden) {\n        this.notificationService.showMessageNotification(message.username, message.text);\n      }\n    });\n    this.socketService.on('reactionUpdate', ({\n      messageId,\n      reactions\n    }) => {\n      console.log('Reaction update:', messageId, reactions);\n      const currentMessages = this.messagesSubject.value;\n      this.messagesSubject.next(currentMessages.map(msg => msg.id === messageId ? { ...msg,\n        reactions\n      } : msg));\n    });\n    this.socketService.on('onlineUsersUpdate', users => {\n      console.log('Online users updated:', users);\n      this.onlineUsersSubject.next(users || []);\n    });\n    this.socketService.on('userJoined', ({\n      username\n    }) => {\n      console.log('User joined:', username); // Online users will be updated via onlineUsersUpdate event\n    });\n    this.socketService.on('userLeft', ({\n      username\n    }) => {\n      console.log('User left:', username); // Online users will be updated via onlineUsersUpdate event\n    });\n    this.socketService.on('messageUpdated', ({\n      messageId,\n      newText,\n      updatedAt\n    }) => {\n      console.log('Message updated:', messageId, newText);\n      const currentMessages = this.messagesSubject.value;\n      this.messagesSubject.next(currentMessages.map(msg => msg.id === messageId ? { ...msg,\n        text: newText,\n        updated_at: updatedAt\n      } : msg));\n    });\n    this.socketService.on('messageDeleted', ({\n      messageId\n    }) => {\n      console.log('Message deleted:', messageId);\n      const currentMessages = this.messagesSubject.value;\n      this.messagesSubject.next(currentMessages.filter(msg => msg.id !== messageId));\n    });\n    this.socketService.on('error', error => {\n      console.error('Socket error:', error);\n    });\n  }\n\n  setupMessageRefresh() {\n    // Clear any existing interval first\n    if (this.messageRefreshInterval) {\n      clearInterval(this.messageRefreshInterval);\n    } // Temporarily disable message refresh to prevent browser freezing\n    // TODO: Re-enable once socket connection issues are resolved\n\n    /*\r\n    this.messageRefreshInterval = setInterval(() => {\r\n      try {\r\n        if (!this.socketService.isConnectedSubject.value) {\r\n          const currentGroup = this.currentGroupSubject.value;\r\n          if (currentGroup) {\r\n            console.log('Refreshing messages via HTTP API');\r\n            this.loadRecentMessages(currentGroup.id);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Error in message refresh interval:', error);\r\n        // Clear the interval if there's an error to prevent crashes\r\n        if (this.messageRefreshInterval) {\r\n          clearInterval(this.messageRefreshInterval);\r\n          this.messageRefreshInterval = null;\r\n        }\r\n      }\r\n    }, 5000);\r\n    */\n\n  }\n\n  login(_x, _x2) {\n    var _this = this;\n\n    return _asyncToGenerator(function* (username, password, inviteCode = null) {\n      try {\n        _this.loadingSubject.next(true);\n\n        console.log('Starting login process for:', username);\n        const userData = yield _this.apiService.loginUser(username, password, inviteCode || undefined).toPromise();\n        console.log('Login API response:', userData); // Check if password is required\n\n        if (userData?.requiresPassword) {\n          throw new Error('Password required for this user');\n        } // Connect to socket with auth data\n\n\n        console.log('Connecting to socket...');\n\n        _this.socketService.connect(username, password, inviteCode);\n\n        _this.userSubject.next(username);\n\n        _this.isAdminSubject.next(userData?.isAdmin || false);\n\n        _this.securityInfoSubject.next(userData?.securityInfo || null);\n\n        console.log('Login completed successfully for:', username, '(Admin:', userData?.isAdmin || false, ')'); // Wait for socket connection before loading groups\n        // The socket will emit 'userGroups' event which will handle group loading\n        // This prevents race conditions between API and socket calls\n        // Add fallback: if socket doesn't load groups within 3 seconds, try API\n\n        setTimeout(() => {\n          if (_this.groupsSubject.value.length === 0) {\n            console.log('Socket groups not loaded, trying API fallback...');\n\n            _this.loadUserGroups(username);\n          }\n        }, 3000);\n      } catch (error) {\n        console.error('Login failed:', error);\n        throw error;\n      } finally {\n        _this.loadingSubject.next(false);\n      }\n    }).apply(this, arguments);\n  }\n\n  loadRecentMessages(groupId) {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      // Safety guard to prevent infinite loops\n      if (_this2.isLoadingMessages) {\n        console.log('Already loading messages, skipping...');\n        return;\n      } // Throttle message loading to prevent excessive API calls\n\n\n      const now = Date.now();\n\n      if (now - _this2.lastMessageLoadTime < _this2.MESSAGE_LOAD_THROTTLE) {\n        console.log('Throttling message load request');\n        return;\n      }\n\n      _this2.lastMessageLoadTime = now;\n      _this2.isLoadingMessages = true;\n\n      try {\n        console.log('Loading recent messages for group:', groupId);\n        const messages = yield _this2.apiService.getMessages(groupId, 50).toPromise();\n        console.log('Loaded messages:', messages?.length || 0);\n        console.log('Messages data:', messages); // For group switching, replace messages with only the current group's messages\n        // This ensures we only show messages for the current group\n\n        _this2.messagesSubject.next(messages || []);\n\n        console.log('Messages subject updated, current value:', _this2.messagesSubject.value.length);\n      } catch (error) {\n        console.error('Failed to load messages:', error); // Set empty array to prevent UI from showing stale data\n\n        _this2.messagesSubject.next([]);\n      } finally {\n        _this2.isLoadingMessages = false;\n      }\n    })();\n  }\n\n  loadUserGroups(username) {\n    var _this3 = this;\n\n    return _asyncToGenerator(function* () {\n      // Safety guard to prevent infinite loops\n      if (_this3.isLoadingGroups) {\n        console.log('Already loading groups, skipping...');\n        return;\n      }\n\n      _this3.isLoadingGroups = true;\n\n      try {\n        console.log('Loading user groups for:', username);\n        const groups = yield _this3.apiService.getUserGroups(username).toPromise();\n        console.log('Loaded user groups:', groups);\n\n        _this3.groupsSubject.next(groups || []);\n\n        if (groups && groups.length > 0) {\n          console.log('Auto-selecting first group:', groups[0]);\n\n          _this3.currentGroupSubject.next(groups[0]);\n\n          _this3.loadRecentMessages(groups[0].id);\n        }\n      } catch (error) {\n        console.error('Failed to load user groups:', error);\n      } finally {\n        _this3.isLoadingGroups = false;\n      }\n    })();\n  } // Safe method to manually load groups (call this from browser console)\n\n\n  manualLoadGroups() {\n    var _this4 = this;\n\n    return _asyncToGenerator(function* () {\n      const currentUser = _this4.userSubject.value;\n\n      if (currentUser) {\n        console.log('Manually loading groups for:', currentUser);\n        yield _this4.loadUserGroups(currentUser);\n      } else {\n        console.error('No user logged in');\n      }\n    })();\n  } // Debug method to check current state\n\n\n  debugState() {\n    console.log('=== Chat Service Debug State ===');\n    console.log('User:', this.userSubject.value);\n    console.log('Groups:', this.groupsSubject.value);\n    console.log('Current Group:', this.currentGroupSubject.value);\n    console.log('Socket Connected:', this.socketService.isConnectedSubject.value);\n    console.log('Loading Groups:', this.isLoadingGroups);\n    console.log('Loading Messages:', this.isLoadingMessages);\n    console.log('================================');\n  } // Helper method to replace optimistic message with real message\n\n\n  replaceOptimisticMessage(tempId, realMessage) {\n    const currentMessages = this.messagesSubject.value;\n    const updatedMessages = currentMessages.map(msg => msg.id === tempId ? realMessage : msg);\n    this.messagesSubject.next(updatedMessages);\n    console.log('Replaced optimistic message with real message:', realMessage);\n  } // Helper method to remove optimistic message (on error)\n\n\n  removeOptimisticMessage(tempId) {\n    const currentMessages = this.messagesSubject.value;\n    const updatedMessages = currentMessages.filter(msg => msg.id !== tempId);\n    this.messagesSubject.next(updatedMessages);\n    console.log('Removed optimistic message due to error:', tempId);\n  }\n\n  joinGroup(groupId) {\n    const currentUser = this.userSubject.value;\n\n    if (!groupId || !currentUser) {\n      console.error('Cannot join group - missing groupId or user');\n      return;\n    }\n\n    console.log('Joining group:', groupId); // Check if socket is connected before joining\n\n    if (!this.socketService.isConnectedSubject.value) {\n      console.error('Cannot join group - socket not connected');\n      return;\n    }\n\n    this.socketService.joinGroup(groupId);\n    const groups = this.groupsSubject.value;\n    const group = groups.find(g => g.id === groupId);\n\n    if (group) {\n      this.currentGroupSubject.next(group); // Load recent messages for the group\n\n      this.loadRecentMessages(groupId);\n    } else {\n      console.error('Group not found in user groups:', groupId);\n    }\n  }\n\n  sendMessage(text, replyToId = null) {\n    const currentUser = this.userSubject.value;\n    const currentGroup = this.currentGroupSubject.value;\n\n    if (!text.trim()) {\n      console.error('Cannot send message - empty text');\n      return;\n    }\n\n    if (!currentUser) {\n      console.error('Cannot send message - user not logged in');\n      return;\n    }\n\n    if (!currentGroup) {\n      console.error('Cannot send message - no group selected. Available groups:', this.groupsSubject.value); // Try to auto-select the first available group\n\n      const groups = this.groupsSubject.value;\n\n      if (groups && groups.length > 0) {\n        console.log('Auto-selecting first available group:', groups[0]);\n        this.currentGroupSubject.next(groups[0]);\n        this.loadRecentMessages(groups[0].id); // Retry sending the message - DISABLED to prevent infinite loops\n        // setTimeout(() => this.sendMessage(text, replyToId), 100);\n\n        return;\n      } else {\n        console.error('No groups available for user');\n        return;\n      }\n    } // Create optimistic message for immediate display\n\n\n    const optimisticMessage = {\n      id: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n      text: text.trim(),\n      username: currentUser,\n      timestamp: new Date().toISOString(),\n      groupId: currentGroup.id,\n      replyTo: replyToId || undefined,\n      reactions: []\n    }; // Add optimistic message to local state immediately\n\n    const currentMessages = this.messagesSubject.value;\n    const updatedMessages = [...currentMessages, optimisticMessage];\n    this.messagesSubject.next(updatedMessages);\n    console.log('📤 Added optimistic message:', optimisticMessage); // Send message with acknowledgment and retry logic\n\n    this.sendMessageWithRetry(text, currentGroup.id, replyToId, optimisticMessage);\n  }\n\n  sendMessageWithRetry(_x3, _x4, _x5, _x6) {\n    var _this5 = this;\n\n    return _asyncToGenerator(function* (text, groupId, replyToId, optimisticMessage, retryCount = 0) {\n      const maxRetries = 3;\n      const retryDelay = 1000 * Math.pow(2, retryCount); // Exponential backoff\n\n      try {\n        if (_this5.socketService.isConnected()) {\n          // Try sending via socket with acknowledgment\n          const response = yield _this5.socketService.emitWithAck('sendMessage', {\n            text,\n            groupId,\n            replyTo: replyToId,\n            messageId: optimisticMessage.id\n          });\n\n          if (response.success) {\n            console.log('✅ Message sent successfully via socket:', response); // Replace optimistic message with real message\n\n            _this5.replaceOptimisticMessage(optimisticMessage.id, response.message);\n\n            return;\n          } else {\n            throw new Error(response.error || 'Unknown error');\n          }\n        } else {\n          throw new Error('Socket not connected');\n        }\n      } catch (error) {\n        console.error(`❌ Failed to send message (attempt ${retryCount + 1}):`, error);\n\n        if (retryCount < maxRetries) {\n          // Retry with exponential backoff\n          console.log(`🔄 Retrying in ${retryDelay}ms...`);\n          setTimeout(() => {\n            _this5.sendMessageWithRetry(text, groupId, replyToId, optimisticMessage, retryCount + 1);\n          }, retryDelay);\n        } else {\n          // All retries failed, try HTTP API as final fallback\n          console.log('🌐 All socket retries failed, trying HTTP API fallback');\n\n          _this5.sendMessageViaHttpFallback(text, groupId, replyToId, optimisticMessage);\n        }\n      }\n    }).apply(this, arguments);\n  }\n\n  sendMessageViaHttpFallback(text, groupId, replyToId, optimisticMessage) {\n    const currentUser = this.userSubject.value;\n\n    if (!currentUser) {\n      this.removeOptimisticMessage(optimisticMessage.id);\n      return;\n    }\n\n    this.apiService.sendMessage(text, currentUser, groupId, replyToId).subscribe({\n      next: response => {\n        console.log('✅ Message sent via HTTP API:', response);\n        this.replaceOptimisticMessage(optimisticMessage.id, response);\n      },\n      error: error => {\n        console.error('❌ Failed to send message via HTTP API:', error);\n        this.removeOptimisticMessage(optimisticMessage.id); // Show error notification to user\n\n        this.showMessageError('Failed to send message. Please try again.');\n      }\n    });\n  }\n\n  showMessageError(message) {\n    // You can implement a toast notification service here\n    console.error('💬 Message Error:', message); // For now, just log the error. In a real app, you'd show a toast/snackbar\n  }\n\n  replyToMessage(message) {\n    this.replyToSubject.next(message);\n  }\n\n  cancelReply() {\n    this.replyToSubject.next(null);\n  }\n\n  addReaction(messageId, emoji) {\n    this.socketService.addReaction(messageId, emoji);\n  }\n\n  removeReaction(data) {\n    this.socketService.removeReaction(data);\n  }\n\n  updateMessage(messageId, newText) {\n    var _this6 = this;\n\n    return _asyncToGenerator(function* () {\n      const currentUser = _this6.userSubject.value;\n\n      if (!currentUser) {\n        console.error('Cannot update message - user not logged in');\n        return;\n      }\n\n      try {\n        // Update the message in the local state immediately for better UX\n        const currentMessages = _this6.messagesSubject.value;\n\n        _this6.messagesSubject.next(currentMessages.map(msg => msg.id === messageId ? { ...msg,\n          text: newText,\n          updated_at: new Date().toISOString()\n        } : msg)); // Emit socket event for real-time updates\n\n\n        if (_this6.socketService.isConnectedSubject.value) {\n          _this6.socketService.emit('messageUpdated', {\n            messageId,\n            newText\n          });\n        } else {\n          // Fallback to HTTP API if socket not connected\n          const updatedMessage = yield _this6.apiService.updateMessage(messageId, newText, currentUser).toPromise();\n\n          _this6.messagesSubject.next(currentMessages.map(msg => msg.id === messageId ? { ...msg,\n            text: newText,\n            updated_at: updatedMessage.updated_at\n          } : msg));\n        }\n      } catch (error) {\n        console.error('Failed to update message:', error);\n        throw error;\n      }\n    })();\n  }\n\n  deleteMessage(messageId) {\n    var _this7 = this;\n\n    return _asyncToGenerator(function* () {\n      const currentUser = _this7.userSubject.value;\n\n      if (!currentUser) {\n        console.error('Cannot delete message - user not logged in');\n        return;\n      }\n\n      try {\n        // Remove the message from local state immediately for better UX\n        const currentMessages = _this7.messagesSubject.value;\n\n        _this7.messagesSubject.next(currentMessages.filter(msg => msg.id !== messageId)); // Emit socket event for real-time updates\n\n\n        if (_this7.socketService.isConnectedSubject.value) {\n          _this7.socketService.emit('messageDeleted', {\n            messageId\n          });\n        } else {\n          // Fallback to HTTP API if socket not connected\n          yield _this7.apiService.deleteMessage(messageId, currentUser).toPromise();\n        }\n      } catch (error) {\n        console.error('Failed to delete message:', error);\n        throw error;\n      }\n    })();\n  }\n\n  showAdminPanel() {\n    this.showAdminPanelSubject.next(true);\n  }\n\n  hideAdminPanel() {\n    this.showAdminPanelSubject.next(false);\n  }\n\n  startEditingMessage(message) {\n    this.editingMessageSubject.next(message);\n  }\n\n  cancelEditingMessage() {\n    this.editingMessageSubject.next(null);\n  }\n\n  logout() {\n    try {\n      // Disconnect socket first\n      this.socketService.disconnect(); // Clear interval\n\n      if (this.messageRefreshInterval) {\n        clearInterval(this.messageRefreshInterval);\n        this.messageRefreshInterval = null;\n      } // Reset all subjects\n\n\n      this.userSubject.next(null);\n      this.messagesSubject.next([]);\n      this.onlineUsersSubject.next([]);\n      this.groupsSubject.next([]);\n      this.currentGroupSubject.next(null);\n      this.replyToSubject.next(null);\n      this.loadingSubject.next(false);\n      this.isAdminSubject.next(false);\n      this.showAdminPanelSubject.next(false);\n      this.securityInfoSubject.next(null);\n      this.editingMessageSubject.next(null);\n      console.log('Logout completed successfully');\n    } catch (error) {\n      console.error('Error during logout:', error);\n    }\n  } // Method to refresh security info for all components\n\n\n  refreshSecurityInfo() {\n    this.apiService.getSecurityInfo().subscribe({\n      next: data => {\n        this.securityInfoSubject.next(data);\n      },\n      error: error => {\n        console.error('Failed to refresh security info:', error);\n      }\n    });\n  }\n\n}\n\nChatService.ɵfac = function ChatService_Factory(t) {\n  return new (t || ChatService)(i0.ɵɵinject(i1.ApiService), i0.ɵɵinject(i2.SocketService), i0.ɵɵinject(i3.NotificationService));\n};\n\nChatService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: ChatService,\n  factory: ChatService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "mappings": ";AACA,SAASA,eAAT,QAA2D,MAA3D;AACA,SAASC,GAAT,QAAoB,gBAApB;AACA,SAASC,UAAT,EAAqBC,OAArB,EAA8BC,KAA9B,EAAqCC,IAArC,EAA2CC,YAA3C,QAA8E,eAA9E;;;;;AAOA,OAAM,MAAOC,WAAP,CAAkB;EAoCtBC,YACUC,UADV,EAEUC,aAFV,EAGUC,mBAHV,EAGkD;IAFxC;IACA;IACA;IAtCF,mBAAc,IAAIX,eAAJ,CAAmC,IAAnC,CAAd;IACA,uBAAkB,IAAIA,eAAJ,CAA+B,EAA/B,CAAlB;IACA,0BAAqB,IAAIA,eAAJ,CAA4B,EAA5B,CAArB;IACA,qBAAgB,IAAIA,eAAJ,CAA6B,EAA7B,CAAhB;IACA,2BAAsB,IAAIA,eAAJ,CAAkC,IAAlC,CAAtB;IACA,sBAAiB,IAAIA,eAAJ,CAAoC,IAApC,CAAjB;IACA,sBAAiB,IAAIA,eAAJ,CAA6B,KAA7B,CAAjB;IACA,sBAAiB,IAAIA,eAAJ,CAA6B,KAA7B,CAAjB;IACA,6BAAwB,IAAIA,eAAJ,CAA6B,KAA7B,CAAxB;IACA,2BAAsB,IAAIA,eAAJ,CAAyC,IAAzC,CAAtB;IACA,6BAAwB,IAAIA,eAAJ,CAAoC,IAApC,CAAxB,CA4B0C,CA1BlD;;IACO,aAAQ,KAAKY,WAAL,CAAiBC,YAAjB,EAAR;IACA,iBAAY,KAAKC,eAAL,CAAqBD,YAArB,EAAZ;IACA,oBAAe,KAAKE,kBAAL,CAAwBF,YAAxB,EAAf;IACA,eAAU,KAAKG,aAAL,CAAmBH,YAAnB,EAAV;IACA,qBAAgB,KAAKI,mBAAL,CAAyBJ,YAAzB,EAAhB;IACA,gBAAW,KAAKK,cAAL,CAAoBL,YAApB,EAAX;IACA,gBAAW,KAAKM,cAAL,CAAoBN,YAApB,EAAX;IACA,gBAAW,KAAKO,cAAL,CAAoBP,YAApB,EAAX;IACA,uBAAkB,KAAKQ,qBAAL,CAA2BR,YAA3B,EAAlB;IACA,qBAAgB,KAAKS,mBAAL,CAAyBT,YAAzB,EAAhB;IACA,uBAAkB,KAAKU,qBAAL,CAA2BV,YAA3B,EAAlB;IACA,kBAAa,KAAKH,aAAL,CAAmBc,YAAhC,CAc2C,CAZlD;;IACO,mBAAc,KAAKC,KAAL,CAAWC,IAAX,CAAgBzB,GAAG,CAAC0B,IAAI,IAAI,CAAC,CAACA,IAAX,CAAnB,CAAd;IAGC,uBAAkB,KAAlB;IACA,yBAAoB,KAApB;IACA,2BAAsB,CAAtB;IACS,6BAAwB,IAAxB,CAKiC,CALH;;IAO7C,IAAI;MACF,KAAKC,oBAAL;MACA,KAAKC,mBAAL,GAFE,CAIF;;MACCC,MAAc,CAACC,gBAAf,GAAkC,MAAM,KAAKA,gBAAL,EAAxC;;MACAD,MAAc,CAACE,cAAf,GAAgC,MAAM,KAAKC,UAAL,EAAtC;;MACAH,MAAc,CAACI,gBAAf,GAAkC,MAAK;QACtC;QACAC,OAAO,CAACC,GAAR,CAAY,wDAAZ;MACD,CAHA;IAIF,CAXD,CAWE,OAAOC,KAAP,EAAc;MACdF,OAAO,CAACE,KAAR,CAAc,iCAAd,EAAiDA,KAAjD;IACD;EACF;;EAEOT,oBAAoB;IAC1B,KAAKlB,aAAL,CAAmB4B,EAAnB,CAAsB,SAAtB,EAAiC,MAAK;MACpCH,OAAO,CAACC,GAAR,CAAY,qBAAZ,EADoC,CAEpC;;MACA,KAAKjB,cAAL,CAAoBoB,IAApB,CAAyB,KAAzB;IACD,CAJD;IAMA,KAAK7B,aAAL,CAAmB4B,EAAnB,CAAsB,YAAtB,EAAoC,MAAK;MACvCH,OAAO,CAACC,GAAR,CAAY,0BAAZ,EADuC,CAEvC;IACD,CAHD;IAKA,KAAK1B,aAAL,CAAmB4B,EAAnB,CAAsB,eAAtB,EAAwCD,KAAD,IAAU;MAC/CF,OAAO,CAACE,KAAR,CAAc,0BAAd,EAA0CA,KAA1C;MACA,KAAKlB,cAAL,CAAoBoB,IAApB,CAAyB,KAAzB;IACD,CAHD;IAKA,KAAK7B,aAAL,CAAmB4B,EAAnB,CAAsB,YAAtB,EAAqCE,UAAD,IAAwB;MAC1DL,OAAO,CAACC,GAAR,CAAY,uBAAZ,EAAqCI,UAAU,EAAEC,MAAZ,IAAsB,CAA3D,EAA8D,QAA9D;MACA,KAAKzB,aAAL,CAAmBuB,IAAnB,CAAwBC,UAAU,IAAI,EAAtC;;MACA,IAAIA,UAAU,IAAIA,UAAU,CAACC,MAAX,GAAoB,CAAtC,EAAyC;QACvCN,OAAO,CAACC,GAAR,CAAY,6BAAZ,EAA2CI,UAAU,CAAC,CAAD,CAAV,CAAcE,IAAzD;QACA,KAAKzB,mBAAL,CAAyBsB,IAAzB,CAA8BC,UAAU,CAAC,CAAD,CAAxC,EAFuC,CAGvC;;QACA,KAAKG,kBAAL,CAAwBH,UAAU,CAAC,CAAD,CAAV,CAAcI,EAAtC;MACD,CALD,MAKO;QACLT,OAAO,CAACC,GAAR,CAAY,8BAAZ,EADK,CAEL;;QACA,KAAKnB,mBAAL,CAAyBsB,IAAzB,CAA8B,IAA9B;QACA,KAAKzB,eAAL,CAAqByB,IAArB,CAA0B,EAA1B;MACD;IACF,CAdD;IAgBA,KAAK7B,aAAL,CAAmB4B,EAAnB,CAAsB,gBAAtB,EAAyCO,QAAD,IAAwB;MAC9DV,OAAO,CAACC,GAAR,CAAY,2BAAZ,EAAyCS,QAAQ,CAACJ,MAAlD;MACAN,OAAO,CAACC,GAAR,CAAY,uBAAZ,EAAqCS,QAArC;MACA,KAAK/B,eAAL,CAAqByB,IAArB,CAA0BM,QAAQ,IAAI,EAAtC;MACAV,OAAO,CAACC,GAAR,CAAY,iDAAZ,EAA+D,KAAKtB,eAAL,CAAqBgC,KAArB,CAA2BL,MAA1F;IACD,CALD;IAOA,KAAK/B,aAAL,CAAmB4B,EAAnB,CAAsB,aAAtB,EAAqC,CAAC;MAAES;IAAF,CAAD,KAAqC;MACxEZ,OAAO,CAACC,GAAR,CAAY,eAAZ,EAA6BW,OAA7B;MACA,MAAMC,MAAM,GAAG,KAAKhC,aAAL,CAAmB8B,KAAlC;MACA,MAAMG,KAAK,GAAGD,MAAM,CAACE,IAAP,CAAYC,CAAC,IAAIA,CAAC,CAACP,EAAF,KAASG,OAA1B,CAAd;;MACA,IAAIE,KAAJ,EAAW;QACT,KAAKhC,mBAAL,CAAyBsB,IAAzB,CAA8BU,KAA9B;MACD;IACF,CAPD;IASA,KAAKvC,aAAL,CAAmB4B,EAAnB,CAAsB,YAAtB,EAAqCc,OAAD,IAAqB;MACvDjB,OAAO,CAACC,GAAR,CAAY,uBAAZ,EAAqCgB,OAArC;MACAjB,OAAO,CAACC,GAAR,CAAY,eAAZ,EAA6B,KAAKxB,WAAL,CAAiBkC,KAA9C;MACAX,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA8B,KAAKnB,mBAAL,CAAyB6B,KAAzB,EAAgCF,EAA9D;MACAT,OAAO,CAACC,GAAR,CAAY,mBAAZ,EAAiCgB,OAAO,CAACL,OAAzC;MAEA,MAAMM,eAAe,GAAG,KAAKvC,eAAL,CAAqBgC,KAA7C;MACA,MAAMQ,WAAW,GAAG,KAAK1C,WAAL,CAAiBkC,KAArC,CAPuD,CASvD;;MACA,IAAIM,OAAO,CAACG,QAAR,KAAqBD,WAAzB,EAAsC;QACpC;QACA,MAAME,eAAe,GAAGH,eAAe,CAACI,SAAhB,CAA0BC,GAAG,IACnDA,GAAG,CAACd,EAAJ,CAAOe,UAAP,CAAkB,OAAlB,KACAD,GAAG,CAACE,IAAJ,KAAaR,OAAO,CAACQ,IADrB,IAEAF,GAAG,CAACH,QAAJ,KAAiBD,WAFjB,IAGAI,GAAG,CAACX,OAAJ,KAAgBK,OAAO,CAACL,OAJF,CAAxB;;QAOA,IAAIS,eAAe,KAAK,CAAC,CAAzB,EAA4B;UAC1B;UACA,MAAMK,eAAe,GAAG,CAAC,GAAGR,eAAJ,CAAxB;UACAQ,eAAe,CAACL,eAAD,CAAf,GAAmCJ,OAAnC;UACA,KAAKtC,eAAL,CAAqByB,IAArB,CAA0BsB,eAA1B;UACA1B,OAAO,CAACC,GAAR,CAAY,4DAAZ,EAA0EgB,OAA1E;QACD,CAND,MAMO;UACL;UACA,KAAKtC,eAAL,CAAqByB,IAArB,CAA0B,CAAC,GAAGc,eAAJ,EAAqBD,OAArB,CAA1B;UACAjB,OAAO,CAACC,GAAR,CAAY,8DAAZ,EAA4EgB,OAA5E;QACD;MACF,CApBD,MAoBO;QACL;QACA,KAAKtC,eAAL,CAAqByB,IAArB,CAA0B,CAAC,GAAGc,eAAJ,EAAqBD,OAArB,CAA1B;QACAjB,OAAO,CAACC,GAAR,CAAY,gCAAZ,EAA8CgB,OAA9C;MACD,CAlCsD,CAoCvD;;;MACA,IAAIA,OAAO,CAACG,QAAR,KAAqBD,WAArB,IAAoCQ,QAAQ,CAACC,MAAjD,EAAyD;QACvD,KAAKpD,mBAAL,CAAyBqD,uBAAzB,CAAiDZ,OAAO,CAACG,QAAzD,EAAmEH,OAAO,CAACQ,IAA3E;MACD;IACF,CAxCD;IA0CA,KAAKlD,aAAL,CAAmB4B,EAAnB,CAAsB,gBAAtB,EAAwC,CAAC;MAAE2B,SAAF;MAAaC;IAAb,CAAD,KAAsE;MAC5G/B,OAAO,CAACC,GAAR,CAAY,kBAAZ,EAAgC6B,SAAhC,EAA2CC,SAA3C;MACA,MAAMb,eAAe,GAAG,KAAKvC,eAAL,CAAqBgC,KAA7C;MACA,KAAKhC,eAAL,CAAqByB,IAArB,CACEc,eAAe,CAACpD,GAAhB,CAAoByD,GAAG,IACrBA,GAAG,CAACd,EAAJ,KAAWqB,SAAX,GACI,EAAE,GAAGP,GAAL;QAAUQ;MAAV,CADJ,GAEIR,GAHN,CADF;IAOD,CAVD;IAYA,KAAKhD,aAAL,CAAmB4B,EAAnB,CAAsB,mBAAtB,EAA4C6B,KAAD,IAAkB;MAC3DhC,OAAO,CAACC,GAAR,CAAY,uBAAZ,EAAqC+B,KAArC;MACA,KAAKpD,kBAAL,CAAwBwB,IAAxB,CAA6B4B,KAAK,IAAI,EAAtC;IACD,CAHD;IAKA,KAAKzD,aAAL,CAAmB4B,EAAnB,CAAsB,YAAtB,EAAoC,CAAC;MAAEiB;IAAF,CAAD,KAAuC;MACzEpB,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA4BmB,QAA5B,EADyE,CAEzE;IACD,CAHD;IAKA,KAAK7C,aAAL,CAAmB4B,EAAnB,CAAsB,UAAtB,EAAkC,CAAC;MAAEiB;IAAF,CAAD,KAAuC;MACvEpB,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0BmB,QAA1B,EADuE,CAEvE;IACD,CAHD;IAKA,KAAK7C,aAAL,CAAmB4B,EAAnB,CAAsB,gBAAtB,EAAwC,CAAC;MAAE2B,SAAF;MAAaG,OAAb;MAAsBC;IAAtB,CAAD,KAAiG;MACvIlC,OAAO,CAACC,GAAR,CAAY,kBAAZ,EAAgC6B,SAAhC,EAA2CG,OAA3C;MACA,MAAMf,eAAe,GAAG,KAAKvC,eAAL,CAAqBgC,KAA7C;MACA,KAAKhC,eAAL,CAAqByB,IAArB,CACEc,eAAe,CAACpD,GAAhB,CAAoByD,GAAG,IACrBA,GAAG,CAACd,EAAJ,KAAWqB,SAAX,GACI,EAAE,GAAGP,GAAL;QAAUE,IAAI,EAAEQ,OAAhB;QAAyBE,UAAU,EAAED;MAArC,CADJ,GAEIX,GAHN,CADF;IAOD,CAVD;IAYA,KAAKhD,aAAL,CAAmB4B,EAAnB,CAAsB,gBAAtB,EAAwC,CAAC;MAAE2B;IAAF,CAAD,KAAyC;MAC/E9B,OAAO,CAACC,GAAR,CAAY,kBAAZ,EAAgC6B,SAAhC;MACA,MAAMZ,eAAe,GAAG,KAAKvC,eAAL,CAAqBgC,KAA7C;MACA,KAAKhC,eAAL,CAAqByB,IAArB,CACEc,eAAe,CAACkB,MAAhB,CAAuBb,GAAG,IAAIA,GAAG,CAACd,EAAJ,KAAWqB,SAAzC,CADF;IAGD,CAND;IAQA,KAAKvD,aAAL,CAAmB4B,EAAnB,CAAsB,OAAtB,EAAgCD,KAAD,IAAe;MAC5CF,OAAO,CAACE,KAAR,CAAc,eAAd,EAA+BA,KAA/B;IACD,CAFD;EAGD;;EAEOR,mBAAmB;IACzB;IACA,IAAI,KAAK2C,sBAAT,EAAiC;MAC/BC,aAAa,CAAC,KAAKD,sBAAN,CAAb;IACD,CAJwB,CAMzB;IACA;;IACA;;;;;;;;;;;;;;;;;;;;;EAoBD;;EAEKE,KAAK,UAAsE;IAAA;;IAAA,oCAArEnB,QAAqE,EAAnDoB,QAAmD,EAAhCC,aAA4B,IAAI;MAC/E,IAAI;QACF,KAAI,CAACzD,cAAL,CAAoBoB,IAApB,CAAyB,IAAzB;;QACAJ,OAAO,CAACC,GAAR,CAAY,6BAAZ,EAA2CmB,QAA3C;QAEA,MAAMsB,QAAQ,SAAS,KAAI,CAACpE,UAAL,CAAgBqE,SAAhB,CAA0BvB,QAA1B,EAAoCoB,QAApC,EAA8CC,UAAU,IAAIG,SAA5D,EAAuEC,SAAvE,EAAvB;QACA7C,OAAO,CAACC,GAAR,CAAY,qBAAZ,EAAmCyC,QAAnC,EALE,CAOF;;QACA,IAAIA,QAAQ,EAAEI,gBAAd,EAAgC;UAC9B,MAAM,IAAIC,KAAJ,CAAU,iCAAV,CAAN;QACD,CAVC,CAYF;;;QACA/C,OAAO,CAACC,GAAR,CAAY,yBAAZ;;QACA,KAAI,CAAC1B,aAAL,CAAmByE,OAAnB,CAA2B5B,QAA3B,EAAqCoB,QAArC,EAA+CC,UAA/C;;QAEA,KAAI,CAAChE,WAAL,CAAiB2B,IAAjB,CAAsBgB,QAAtB;;QACA,KAAI,CAACnC,cAAL,CAAoBmB,IAApB,CAAyBsC,QAAQ,EAAEO,OAAV,IAAqB,KAA9C;;QACA,KAAI,CAAC9D,mBAAL,CAAyBiB,IAAzB,CAA8BsC,QAAQ,EAAEQ,YAAV,IAA0B,IAAxD;;QAEAlD,OAAO,CAACC,GAAR,CAAY,mCAAZ,EAAiDmB,QAAjD,EAA2D,SAA3D,EAAsEsB,QAAQ,EAAEO,OAAV,IAAqB,KAA3F,EAAkG,GAAlG,EApBE,CAsBF;QACA;QACA;QAEA;;QACAE,UAAU,CAAC,MAAK;UACd,IAAI,KAAI,CAACtE,aAAL,CAAmB8B,KAAnB,CAAyBL,MAAzB,KAAoC,CAAxC,EAA2C;YACzCN,OAAO,CAACC,GAAR,CAAY,kDAAZ;;YACA,KAAI,CAACmD,cAAL,CAAoBhC,QAApB;UACD;QACF,CALS,EAKP,IALO,CAAV;MAOD,CAlCD,CAkCE,OAAOlB,KAAP,EAAc;QACdF,OAAO,CAACE,KAAR,CAAc,eAAd,EAA+BA,KAA/B;QACA,MAAMA,KAAN;MACD,CArCD,SAqCU;QACR,KAAI,CAAClB,cAAL,CAAoBoB,IAApB,CAAyB,KAAzB;MACD;IAxC8E;EAyChF;;EAEKI,kBAAkB,CAACI,OAAD,EAAgB;IAAA;;IAAA;MACtC;MACA,IAAI,MAAI,CAACyC,iBAAT,EAA4B;QAC1BrD,OAAO,CAACC,GAAR,CAAY,uCAAZ;QACA;MACD,CALqC,CAOtC;;;MACA,MAAMqD,GAAG,GAAGC,IAAI,CAACD,GAAL,EAAZ;;MACA,IAAIA,GAAG,GAAG,MAAI,CAACE,mBAAX,GAAiC,MAAI,CAACC,qBAA1C,EAAiE;QAC/DzD,OAAO,CAACC,GAAR,CAAY,iCAAZ;QACA;MACD;;MACD,MAAI,CAACuD,mBAAL,GAA2BF,GAA3B;MAEA,MAAI,CAACD,iBAAL,GAAyB,IAAzB;;MACA,IAAI;QACFrD,OAAO,CAACC,GAAR,CAAY,oCAAZ,EAAkDW,OAAlD;QACA,MAAMF,QAAQ,SAAS,MAAI,CAACpC,UAAL,CAAgBoF,WAAhB,CAA4B9C,OAA5B,EAAqC,EAArC,EAAyCiC,SAAzC,EAAvB;QACA7C,OAAO,CAACC,GAAR,CAAY,kBAAZ,EAAgCS,QAAQ,EAAEJ,MAAV,IAAoB,CAApD;QACAN,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA8BS,QAA9B,EAJE,CAMF;QACA;;QACA,MAAI,CAAC/B,eAAL,CAAqByB,IAArB,CAA0BM,QAAQ,IAAI,EAAtC;;QACAV,OAAO,CAACC,GAAR,CAAY,0CAAZ,EAAwD,MAAI,CAACtB,eAAL,CAAqBgC,KAArB,CAA2BL,MAAnF;MACD,CAVD,CAUE,OAAOJ,KAAP,EAAc;QACdF,OAAO,CAACE,KAAR,CAAc,0BAAd,EAA0CA,KAA1C,EADc,CAEd;;QACA,MAAI,CAACvB,eAAL,CAAqByB,IAArB,CAA0B,EAA1B;MACD,CAdD,SAcU;QACR,MAAI,CAACiD,iBAAL,GAAyB,KAAzB;MACD;IAhCqC;EAiCvC;;EAEKD,cAAc,CAAChC,QAAD,EAAiB;IAAA;;IAAA;MACnC;MACA,IAAI,MAAI,CAACuC,eAAT,EAA0B;QACxB3D,OAAO,CAACC,GAAR,CAAY,qCAAZ;QACA;MACD;;MAED,MAAI,CAAC0D,eAAL,GAAuB,IAAvB;;MACA,IAAI;QACF3D,OAAO,CAACC,GAAR,CAAY,0BAAZ,EAAwCmB,QAAxC;QACA,MAAMP,MAAM,SAAS,MAAI,CAACvC,UAAL,CAAgBsF,aAAhB,CAA8BxC,QAA9B,EAAwCyB,SAAxC,EAArB;QACA7C,OAAO,CAACC,GAAR,CAAY,qBAAZ,EAAmCY,MAAnC;;QACA,MAAI,CAAChC,aAAL,CAAmBuB,IAAnB,CAAwBS,MAAM,IAAI,EAAlC;;QACA,IAAIA,MAAM,IAAIA,MAAM,CAACP,MAAP,GAAgB,CAA9B,EAAiC;UAC/BN,OAAO,CAACC,GAAR,CAAY,6BAAZ,EAA2CY,MAAM,CAAC,CAAD,CAAjD;;UACA,MAAI,CAAC/B,mBAAL,CAAyBsB,IAAzB,CAA8BS,MAAM,CAAC,CAAD,CAApC;;UACA,MAAI,CAACL,kBAAL,CAAwBK,MAAM,CAAC,CAAD,CAAN,CAAUJ,EAAlC;QACD;MACF,CAVD,CAUE,OAAOP,KAAP,EAAc;QACdF,OAAO,CAACE,KAAR,CAAc,6BAAd,EAA6CA,KAA7C;MACD,CAZD,SAYU;QACR,MAAI,CAACyD,eAAL,GAAuB,KAAvB;MACD;IAtBkC;EAuBpC,CA3UqB,CA6UtB;;;EACM/D,gBAAgB;IAAA;;IAAA;MACpB,MAAMuB,WAAW,GAAG,MAAI,CAAC1C,WAAL,CAAiBkC,KAArC;;MACA,IAAIQ,WAAJ,EAAiB;QACfnB,OAAO,CAACC,GAAR,CAAY,8BAAZ,EAA4CkB,WAA5C;QACA,MAAM,MAAI,CAACiC,cAAL,CAAoBjC,WAApB,CAAN;MACD,CAHD,MAGO;QACLnB,OAAO,CAACE,KAAR,CAAc,mBAAd;MACD;IAPmB;EAQrB,CAtVqB,CAwVtB;;;EACAJ,UAAU;IACRE,OAAO,CAACC,GAAR,CAAY,kCAAZ;IACAD,OAAO,CAACC,GAAR,CAAY,OAAZ,EAAqB,KAAKxB,WAAL,CAAiBkC,KAAtC;IACAX,OAAO,CAACC,GAAR,CAAY,SAAZ,EAAuB,KAAKpB,aAAL,CAAmB8B,KAA1C;IACAX,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA8B,KAAKnB,mBAAL,CAAyB6B,KAAvD;IACAX,OAAO,CAACC,GAAR,CAAY,mBAAZ,EAAiC,KAAK1B,aAAL,CAAmBsF,kBAAnB,CAAsClD,KAAvE;IACAX,OAAO,CAACC,GAAR,CAAY,iBAAZ,EAA+B,KAAK0D,eAApC;IACA3D,OAAO,CAACC,GAAR,CAAY,mBAAZ,EAAiC,KAAKoD,iBAAtC;IACArD,OAAO,CAACC,GAAR,CAAY,kCAAZ;EACD,CAlWqB,CAoWtB;;;EACQ6D,wBAAwB,CAACC,MAAD,EAAiBC,WAAjB,EAAqC;IACnE,MAAM9C,eAAe,GAAG,KAAKvC,eAAL,CAAqBgC,KAA7C;IACA,MAAMe,eAAe,GAAGR,eAAe,CAACpD,GAAhB,CAAoByD,GAAG,IAC7CA,GAAG,CAACd,EAAJ,KAAWsD,MAAX,GAAoBC,WAApB,GAAkCzC,GADZ,CAAxB;IAGA,KAAK5C,eAAL,CAAqByB,IAArB,CAA0BsB,eAA1B;IACA1B,OAAO,CAACC,GAAR,CAAY,gDAAZ,EAA8D+D,WAA9D;EACD,CA5WqB,CA8WtB;;;EACQC,uBAAuB,CAACF,MAAD,EAAe;IAC5C,MAAM7C,eAAe,GAAG,KAAKvC,eAAL,CAAqBgC,KAA7C;IACA,MAAMe,eAAe,GAAGR,eAAe,CAACkB,MAAhB,CAAuBb,GAAG,IAAIA,GAAG,CAACd,EAAJ,KAAWsD,MAAzC,CAAxB;IACA,KAAKpF,eAAL,CAAqByB,IAArB,CAA0BsB,eAA1B;IACA1B,OAAO,CAACC,GAAR,CAAY,0CAAZ,EAAwD8D,MAAxD;EACD;;EAEDG,SAAS,CAACtD,OAAD,EAAgB;IACvB,MAAMO,WAAW,GAAG,KAAK1C,WAAL,CAAiBkC,KAArC;;IACA,IAAI,CAACC,OAAD,IAAY,CAACO,WAAjB,EAA8B;MAC5BnB,OAAO,CAACE,KAAR,CAAc,6CAAd;MACA;IACD;;IAEDF,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA8BW,OAA9B,EAPuB,CASvB;;IACA,IAAI,CAAC,KAAKrC,aAAL,CAAmBsF,kBAAnB,CAAsClD,KAA3C,EAAkD;MAChDX,OAAO,CAACE,KAAR,CAAc,0CAAd;MACA;IACD;;IAED,KAAK3B,aAAL,CAAmB2F,SAAnB,CAA6BtD,OAA7B;IACA,MAAMC,MAAM,GAAG,KAAKhC,aAAL,CAAmB8B,KAAlC;IACA,MAAMG,KAAK,GAAGD,MAAM,CAACE,IAAP,CAAYC,CAAC,IAAIA,CAAC,CAACP,EAAF,KAASG,OAA1B,CAAd;;IACA,IAAIE,KAAJ,EAAW;MACT,KAAKhC,mBAAL,CAAyBsB,IAAzB,CAA8BU,KAA9B,EADS,CAET;;MACA,KAAKN,kBAAL,CAAwBI,OAAxB;IACD,CAJD,MAIO;MACLZ,OAAO,CAACE,KAAR,CAAc,iCAAd,EAAiDU,OAAjD;IACD;EACF;;EAEDuD,WAAW,CAAC1C,IAAD,EAAe2C,YAA2B,IAA1C,EAA8C;IACvD,MAAMjD,WAAW,GAAG,KAAK1C,WAAL,CAAiBkC,KAArC;IACA,MAAM0D,YAAY,GAAG,KAAKvF,mBAAL,CAAyB6B,KAA9C;;IAEA,IAAI,CAACc,IAAI,CAAC6C,IAAL,EAAL,EAAkB;MAChBtE,OAAO,CAACE,KAAR,CAAc,kCAAd;MACA;IACD;;IAED,IAAI,CAACiB,WAAL,EAAkB;MAChBnB,OAAO,CAACE,KAAR,CAAc,0CAAd;MACA;IACD;;IAED,IAAI,CAACmE,YAAL,EAAmB;MACjBrE,OAAO,CAACE,KAAR,CAAc,4DAAd,EAA4E,KAAKrB,aAAL,CAAmB8B,KAA/F,EADiB,CAEjB;;MACA,MAAME,MAAM,GAAG,KAAKhC,aAAL,CAAmB8B,KAAlC;;MACA,IAAIE,MAAM,IAAIA,MAAM,CAACP,MAAP,GAAgB,CAA9B,EAAiC;QAC/BN,OAAO,CAACC,GAAR,CAAY,uCAAZ,EAAqDY,MAAM,CAAC,CAAD,CAA3D;QACA,KAAK/B,mBAAL,CAAyBsB,IAAzB,CAA8BS,MAAM,CAAC,CAAD,CAApC;QACA,KAAKL,kBAAL,CAAwBK,MAAM,CAAC,CAAD,CAAN,CAAUJ,EAAlC,EAH+B,CAI/B;QACA;;QACA;MACD,CAPD,MAOO;QACLT,OAAO,CAACE,KAAR,CAAc,8BAAd;QACA;MACD;IACF,CA7BsD,CA+BvD;;;IACA,MAAMqE,iBAAiB,GAAY;MACjC9D,EAAE,EAAE,QAAQ8C,IAAI,CAACD,GAAL,EAAU,IAAIkB,IAAI,CAACC,MAAL,GAAcC,QAAd,CAAuB,EAAvB,EAA2BC,MAA3B,CAAkC,CAAlC,EAAqC,CAArC,CAAuC,EADhC;MAEjClD,IAAI,EAAEA,IAAI,CAAC6C,IAAL,EAF2B;MAGjClD,QAAQ,EAAED,WAHuB;MAIjCyD,SAAS,EAAE,IAAIrB,IAAJ,GAAWsB,WAAX,EAJsB;MAKjCjE,OAAO,EAAEyD,YAAY,CAAC5D,EALW;MAMjCqE,OAAO,EAAEV,SAAS,IAAIxB,SANW;MAOjCb,SAAS,EAAE;IAPsB,CAAnC,CAhCuD,CA0CvD;;IACA,MAAMb,eAAe,GAAG,KAAKvC,eAAL,CAAqBgC,KAA7C;IACA,MAAMe,eAAe,GAAG,CAAC,GAAGR,eAAJ,EAAqBqD,iBAArB,CAAxB;IACA,KAAK5F,eAAL,CAAqByB,IAArB,CAA0BsB,eAA1B;IACA1B,OAAO,CAACC,GAAR,CAAY,8BAAZ,EAA4CsE,iBAA5C,EA9CuD,CAgDvD;;IACA,KAAKQ,oBAAL,CAA0BtD,IAA1B,EAAgC4C,YAAY,CAAC5D,EAA7C,EAAiD2D,SAAjD,EAA4DG,iBAA5D;EACD;;EAEaQ,oBAAoB,qBAKV;IAAA;;IAAA,oCAJtBtD,IAIsB,EAHtBb,OAGsB,EAFtBwD,SAEsB,EADtBG,iBACsB,EAAtBS,aAAqB,CAAC;MAEtB,MAAMC,UAAU,GAAG,CAAnB;MACA,MAAMC,UAAU,GAAG,OAAOV,IAAI,CAACW,GAAL,CAAS,CAAT,EAAYH,UAAZ,CAA1B,CAHsB,CAG6B;;MAEnD,IAAI;QACF,IAAI,MAAI,CAACzG,aAAL,CAAmB6G,WAAnB,EAAJ,EAAsC;UACpC;UACA,MAAMC,QAAQ,SAAS,MAAI,CAAC9G,aAAL,CAAmB+G,WAAnB,CAA+B,aAA/B,EAA8C;YACnE7D,IADmE;YAEnEb,OAFmE;YAGnEkE,OAAO,EAAEV,SAH0D;YAInEtC,SAAS,EAAEyC,iBAAiB,CAAC9D;UAJsC,CAA9C,CAAvB;;UAOA,IAAI4E,QAAQ,CAACE,OAAb,EAAsB;YACpBvF,OAAO,CAACC,GAAR,CAAY,yCAAZ,EAAuDoF,QAAvD,EADoB,CAEpB;;YACA,MAAI,CAACvB,wBAAL,CAA8BS,iBAAiB,CAAC9D,EAAhD,EAAoD4E,QAAQ,CAACpE,OAA7D;;YACA;UACD,CALD,MAKO;YACL,MAAM,IAAI8B,KAAJ,CAAUsC,QAAQ,CAACnF,KAAT,IAAkB,eAA5B,CAAN;UACD;QACF,CAjBD,MAiBO;UACL,MAAM,IAAI6C,KAAJ,CAAU,sBAAV,CAAN;QACD;MACF,CArBD,CAqBE,OAAO7C,KAAP,EAAc;QACdF,OAAO,CAACE,KAAR,CAAc,qCAAqC8E,UAAU,GAAG,CAAC,IAAjE,EAAuE9E,KAAvE;;QAEA,IAAI8E,UAAU,GAAGC,UAAjB,EAA6B;UAC3B;UACAjF,OAAO,CAACC,GAAR,CAAY,kBAAkBiF,UAAU,OAAxC;UACA/B,UAAU,CAAC,MAAK;YACd,MAAI,CAAC4B,oBAAL,CAA0BtD,IAA1B,EAAgCb,OAAhC,EAAyCwD,SAAzC,EAAoDG,iBAApD,EAAuES,UAAU,GAAG,CAApF;UACD,CAFS,EAEPE,UAFO,CAAV;QAGD,CAND,MAMO;UACL;UACAlF,OAAO,CAACC,GAAR,CAAY,wDAAZ;;UACA,MAAI,CAACuF,0BAAL,CAAgC/D,IAAhC,EAAsCb,OAAtC,EAA+CwD,SAA/C,EAA0DG,iBAA1D;QACD;MACF;IAxCqB;EAyCvB;;EAEOiB,0BAA0B,CAChC/D,IADgC,EAEhCb,OAFgC,EAGhCwD,SAHgC,EAIhCG,iBAJgC,EAIN;IAE1B,MAAMpD,WAAW,GAAG,KAAK1C,WAAL,CAAiBkC,KAArC;;IACA,IAAI,CAACQ,WAAL,EAAkB;MAChB,KAAK8C,uBAAL,CAA6BM,iBAAiB,CAAC9D,EAA/C;MACA;IACD;;IAED,KAAKnC,UAAL,CAAgB6F,WAAhB,CAA4B1C,IAA5B,EAAkCN,WAAlC,EAA+CP,OAA/C,EAAwDwD,SAAxD,EAAmEqB,SAAnE,CAA6E;MAC3ErF,IAAI,EAAGiF,QAAD,IAAa;QACjBrF,OAAO,CAACC,GAAR,CAAY,8BAAZ,EAA4CoF,QAA5C;QACA,KAAKvB,wBAAL,CAA8BS,iBAAiB,CAAC9D,EAAhD,EAAoD4E,QAApD;MACD,CAJ0E;MAK3EnF,KAAK,EAAGA,KAAD,IAAU;QACfF,OAAO,CAACE,KAAR,CAAc,wCAAd,EAAwDA,KAAxD;QACA,KAAK+D,uBAAL,CAA6BM,iBAAiB,CAAC9D,EAA/C,EAFe,CAGf;;QACA,KAAKiF,gBAAL,CAAsB,2CAAtB;MACD;IAV0E,CAA7E;EAYD;;EAEOA,gBAAgB,CAACzE,OAAD,EAAgB;IACtC;IACAjB,OAAO,CAACE,KAAR,CAAc,mBAAd,EAAmCe,OAAnC,EAFsC,CAGtC;EACD;;EAED0E,cAAc,CAAC1E,OAAD,EAAiB;IAC7B,KAAKlC,cAAL,CAAoBqB,IAApB,CAAyBa,OAAzB;EACD;;EAED2E,WAAW;IACT,KAAK7G,cAAL,CAAoBqB,IAApB,CAAyB,IAAzB;EACD;;EAEDyF,WAAW,CAAC/D,SAAD,EAAoBgE,KAApB,EAAiC;IAC1C,KAAKvH,aAAL,CAAmBsH,WAAnB,CAA+B/D,SAA/B,EAA0CgE,KAA1C;EACD;;EAEDC,cAAc,CAACC,IAAD,EAA4C;IACxD,KAAKzH,aAAL,CAAmBwH,cAAnB,CAAkCC,IAAlC;EACD;;EAEKC,aAAa,CAACnE,SAAD,EAAoBG,OAApB,EAAmC;IAAA;;IAAA;MACpD,MAAMd,WAAW,GAAG,MAAI,CAAC1C,WAAL,CAAiBkC,KAArC;;MACA,IAAI,CAACQ,WAAL,EAAkB;QAChBnB,OAAO,CAACE,KAAR,CAAc,4CAAd;QACA;MACD;;MAED,IAAI;QACF;QACA,MAAMgB,eAAe,GAAG,MAAI,CAACvC,eAAL,CAAqBgC,KAA7C;;QACA,MAAI,CAAChC,eAAL,CAAqByB,IAArB,CACEc,eAAe,CAACpD,GAAhB,CAAoByD,GAAG,IACrBA,GAAG,CAACd,EAAJ,KAAWqB,SAAX,GACI,EAAE,GAAGP,GAAL;UAAUE,IAAI,EAAEQ,OAAhB;UAAyBE,UAAU,EAAE,IAAIoB,IAAJ,GAAWsB,WAAX;QAArC,CADJ,GAEItD,GAHN,CADF,EAHE,CAWF;;;QACA,IAAI,MAAI,CAAChD,aAAL,CAAmBsF,kBAAnB,CAAsClD,KAA1C,EAAiD;UAC/C,MAAI,CAACpC,aAAL,CAAmB2H,IAAnB,CAAwB,gBAAxB,EAA0C;YAAEpE,SAAF;YAAaG;UAAb,CAA1C;QACD,CAFD,MAEO;UACL;UACA,MAAMkE,cAAc,SAAS,MAAI,CAAC7H,UAAL,CAAgB2H,aAAhB,CAA8BnE,SAA9B,EAAyCG,OAAzC,EAAkDd,WAAlD,EAA+D0B,SAA/D,EAA7B;;UACA,MAAI,CAAClE,eAAL,CAAqByB,IAArB,CACEc,eAAe,CAACpD,GAAhB,CAAoByD,GAAG,IACrBA,GAAG,CAACd,EAAJ,KAAWqB,SAAX,GACI,EAAE,GAAGP,GAAL;YAAUE,IAAI,EAAEQ,OAAhB;YAAyBE,UAAU,EAAEgE,cAAc,CAAChE;UAApD,CADJ,GAEIZ,GAHN,CADF;QAOD;MACF,CAzBD,CAyBE,OAAOrB,KAAP,EAAc;QACdF,OAAO,CAACE,KAAR,CAAc,2BAAd,EAA2CA,KAA3C;QACA,MAAMA,KAAN;MACD;IAnCmD;EAoCrD;;EAEKkG,aAAa,CAACtE,SAAD,EAAkB;IAAA;;IAAA;MACnC,MAAMX,WAAW,GAAG,MAAI,CAAC1C,WAAL,CAAiBkC,KAArC;;MACA,IAAI,CAACQ,WAAL,EAAkB;QAChBnB,OAAO,CAACE,KAAR,CAAc,4CAAd;QACA;MACD;;MAED,IAAI;QACF;QACA,MAAMgB,eAAe,GAAG,MAAI,CAACvC,eAAL,CAAqBgC,KAA7C;;QACA,MAAI,CAAChC,eAAL,CAAqByB,IAArB,CACEc,eAAe,CAACkB,MAAhB,CAAuBb,GAAG,IAAIA,GAAG,CAACd,EAAJ,KAAWqB,SAAzC,CADF,EAHE,CAOF;;;QACA,IAAI,MAAI,CAACvD,aAAL,CAAmBsF,kBAAnB,CAAsClD,KAA1C,EAAiD;UAC/C,MAAI,CAACpC,aAAL,CAAmB2H,IAAnB,CAAwB,gBAAxB,EAA0C;YAAEpE;UAAF,CAA1C;QACD,CAFD,MAEO;UACL;UACA,MAAM,MAAI,CAACxD,UAAL,CAAgB8H,aAAhB,CAA8BtE,SAA9B,EAAyCX,WAAzC,EAAsD0B,SAAtD,EAAN;QACD;MACF,CAdD,CAcE,OAAO3C,KAAP,EAAc;QACdF,OAAO,CAACE,KAAR,CAAc,2BAAd,EAA2CA,KAA3C;QACA,MAAMA,KAAN;MACD;IAxBkC;EAyBpC;;EAEDmG,cAAc;IACZ,KAAKnH,qBAAL,CAA2BkB,IAA3B,CAAgC,IAAhC;EACD;;EAEDkG,cAAc;IACZ,KAAKpH,qBAAL,CAA2BkB,IAA3B,CAAgC,KAAhC;EACD;;EAEDmG,mBAAmB,CAACtF,OAAD,EAAiB;IAClC,KAAK7B,qBAAL,CAA2BgB,IAA3B,CAAgCa,OAAhC;EACD;;EAEDuF,oBAAoB;IAClB,KAAKpH,qBAAL,CAA2BgB,IAA3B,CAAgC,IAAhC;EACD;;EAEDqG,MAAM;IACJ,IAAI;MACF;MACA,KAAKlI,aAAL,CAAmBmI,UAAnB,GAFE,CAIF;;MACA,IAAI,KAAKrE,sBAAT,EAAiC;QAC/BC,aAAa,CAAC,KAAKD,sBAAN,CAAb;QACA,KAAKA,sBAAL,GAA8B,IAA9B;MACD,CARC,CAUF;;;MACA,KAAK5D,WAAL,CAAiB2B,IAAjB,CAAsB,IAAtB;MACA,KAAKzB,eAAL,CAAqByB,IAArB,CAA0B,EAA1B;MACA,KAAKxB,kBAAL,CAAwBwB,IAAxB,CAA6B,EAA7B;MACA,KAAKvB,aAAL,CAAmBuB,IAAnB,CAAwB,EAAxB;MACA,KAAKtB,mBAAL,CAAyBsB,IAAzB,CAA8B,IAA9B;MACA,KAAKrB,cAAL,CAAoBqB,IAApB,CAAyB,IAAzB;MACA,KAAKpB,cAAL,CAAoBoB,IAApB,CAAyB,KAAzB;MACA,KAAKnB,cAAL,CAAoBmB,IAApB,CAAyB,KAAzB;MACA,KAAKlB,qBAAL,CAA2BkB,IAA3B,CAAgC,KAAhC;MACA,KAAKjB,mBAAL,CAAyBiB,IAAzB,CAA8B,IAA9B;MACA,KAAKhB,qBAAL,CAA2BgB,IAA3B,CAAgC,IAAhC;MAEAJ,OAAO,CAACC,GAAR,CAAY,+BAAZ;IACD,CAxBD,CAwBE,OAAOC,KAAP,EAAc;MACdF,OAAO,CAACE,KAAR,CAAc,sBAAd,EAAsCA,KAAtC;IACD;EACF,CAlpBqB,CAopBtB;;;EACAyG,mBAAmB;IACjB,KAAKrI,UAAL,CAAgBsI,eAAhB,GAAkCnB,SAAlC,CAA4C;MAC1CrF,IAAI,EAAG4F,IAAD,IAAS;QACb,KAAK7G,mBAAL,CAAyBiB,IAAzB,CAA8B4F,IAA9B;MACD,CAHyC;MAI1C9F,KAAK,EAAGA,KAAD,IAAU;QACfF,OAAO,CAACE,KAAR,CAAc,kCAAd,EAAkDA,KAAlD;MACD;IANyC,CAA5C;EAQD;;AA9pBqB;;;mBAAX9B,aAAWyI;AAAA;;;SAAXzI;EAAW0I,SAAX1I,WAAW;EAAA2I,YAFV", "names": ["BehaviorSubject", "map", "ApiService", "Message", "Group", "User", "SecurityInfo", "ChatService", "constructor", "apiService", "socketService", "notificationService", "userSubject", "asObservable", "messagesSubject", "onlineUsersSubject", "groupsSubject", "currentGroupSubject", "replyToSubject", "loadingSubject", "isAdminSubject", "showAdminPanelSubject", "securityInfoSubject", "editingMessageSubject", "isConnected$", "user$", "pipe", "user", "setupSocketListeners", "setupMessageRefresh", "window", "manualLoadGroups", "debugChatState", "debugState", "debugScrollState", "console", "log", "error", "on", "next", "userGroups", "length", "name", "loadRecentMessages", "id", "messages", "value", "groupId", "groups", "group", "find", "g", "message", "currentMessages", "currentUser", "username", "optimisticIndex", "findIndex", "msg", "startsWith", "text", "updatedMessages", "document", "hidden", "showMessageNotification", "messageId", "reactions", "users", "newText", "updatedAt", "updated_at", "filter", "messageRefreshInterval", "clearInterval", "login", "password", "inviteCode", "userData", "loginUser", "undefined", "to<PERSON>romise", "requiresPassword", "Error", "connect", "isAdmin", "securityInfo", "setTimeout", "loadUserGroups", "isLoadingMessages", "now", "Date", "lastMessageLoadTime", "MESSAGE_LOAD_THROTTLE", "getMessages", "isLoadingGroups", "getUserGroups", "isConnectedSubject", "replaceOptimisticMessage", "tempId", "realMessage", "removeOptimisticMessage", "joinGroup", "sendMessage", "replyToId", "currentGroup", "trim", "optimisticMessage", "Math", "random", "toString", "substr", "timestamp", "toISOString", "replyTo", "sendMessageWithRetry", "retryCount", "maxRetries", "retry<PERSON><PERSON><PERSON>", "pow", "isConnected", "response", "emitWithAck", "success", "sendMessageViaHttpFallback", "subscribe", "showMessageError", "replyToMessage", "cancelReply", "addReaction", "emoji", "removeReaction", "data", "updateMessage", "emit", "updatedMessage", "deleteMessage", "showAdminPanel", "hideAdminPanel", "startEditingMessage", "cancelEditingMessage", "logout", "disconnect", "refreshSecurityInfo", "getSecurityInfo", "i0", "factory", "providedIn"], "sourceRoot": "", "sources": ["R:\\chateye\\FrontendAngular\\src\\app\\services\\chat.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable, combineLatest } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\nimport { ApiService, Message, Group, User, SecurityInfo, LoginResponse } from './api.service';\r\nimport { SocketService } from './socket.service';\r\nimport { NotificationService } from './notification.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ChatService {\r\n  private userSubject = new BehaviorSubject<string | null>(null);\r\n  private messagesSubject = new BehaviorSubject<Message[]>([]);\r\n  private onlineUsersSubject = new BehaviorSubject<User[]>([]);\r\n  private groupsSubject = new BehaviorSubject<Group[]>([]);\r\n  private currentGroupSubject = new BehaviorSubject<Group | null>(null);\r\n  private replyToSubject = new BehaviorSubject<Message | null>(null);\r\n  private loadingSubject = new BehaviorSubject<boolean>(false);\r\n  private isAdminSubject = new BehaviorSubject<boolean>(false);\r\n  private showAdminPanelSubject = new BehaviorSubject<boolean>(false);\r\n  private securityInfoSubject = new BehaviorSubject<SecurityInfo | null>(null);\r\n  private editingMessageSubject = new BehaviorSubject<Message | null>(null);\r\n\r\n  // Public observables\r\n  public user$ = this.userSubject.asObservable();\r\n  public messages$ = this.messagesSubject.asObservable();\r\n  public onlineUsers$ = this.onlineUsersSubject.asObservable();\r\n  public groups$ = this.groupsSubject.asObservable();\r\n  public currentGroup$ = this.currentGroupSubject.asObservable();\r\n  public replyTo$ = this.replyToSubject.asObservable();\r\n  public loading$ = this.loadingSubject.asObservable();\r\n  public isAdmin$ = this.isAdminSubject.asObservable();\r\n  public showAdminPanel$ = this.showAdminPanelSubject.asObservable();\r\n  public securityInfo$ = this.securityInfoSubject.asObservable();\r\n  public editingMessage$ = this.editingMessageSubject.asObservable();\r\n  public connected$ = this.socketService.isConnected$;\r\n\r\n  // Computed observables\r\n  public isLoggedIn$ = this.user$.pipe(map(user => !!user))\r\n\r\n  private messageRefreshInterval: any;\r\n  private isLoadingGroups = false;\r\n  private isLoadingMessages = false;\r\n  private lastMessageLoadTime = 0;\r\n  private readonly MESSAGE_LOAD_THROTTLE = 1000; // 1 second throttle\r\n\r\n  constructor(\r\n    private apiService: ApiService,\r\n    private socketService: SocketService,\r\n    private notificationService: NotificationService\r\n  ) {\r\n    try {\r\n      this.setupSocketListeners();\r\n      this.setupMessageRefresh();\r\n      \r\n      // Expose manual group loading globally for debugging\r\n      (window as any).manualLoadGroups = () => this.manualLoadGroups();\r\n      (window as any).debugChatState = () => this.debugState();\r\n      (window as any).debugScrollState = () => {\r\n        // This will be called from the component\r\n        console.log('Use debugScrollState() from the message list component');\r\n      };\r\n    } catch (error) {\r\n      console.error('Error initializing ChatService:', error);\r\n    }\r\n  }\r\n\r\n  private setupSocketListeners(): void {\r\n    this.socketService.on('connect', () => {\r\n      console.log('Connected to server');\r\n      // Clear any loading states when connected\r\n      this.loadingSubject.next(false);\r\n    });\r\n\r\n    this.socketService.on('disconnect', () => {\r\n      console.log('Disconnected from server');\r\n      // Don't set loading to true on disconnect to avoid UI freeze\r\n    });\r\n\r\n    this.socketService.on('connect_error', (error) => {\r\n      console.error('Socket connection error:', error);\r\n      this.loadingSubject.next(false);\r\n    });\r\n\r\n    this.socketService.on('userGroups', (userGroups: Group[]) => {\r\n      console.log('Received user groups:', userGroups?.length || 0, 'groups');\r\n      this.groupsSubject.next(userGroups || []);\r\n      if (userGroups && userGroups.length > 0) {\r\n        console.log('Auto-selecting first group:', userGroups[0].name);\r\n        this.currentGroupSubject.next(userGroups[0]);\r\n        // Load messages for the selected group\r\n        this.loadRecentMessages(userGroups[0].id);\r\n      } else {\r\n        console.log('No groups available for user');\r\n        // Clear current group and messages if no groups\r\n        this.currentGroupSubject.next(null);\r\n        this.messagesSubject.next([]);\r\n      }\r\n    });\r\n\r\n    this.socketService.on('recentMessages', (messages: Message[]) => {\r\n      console.log('Received recent messages:', messages.length);\r\n      console.log('Recent messages data:', messages);\r\n      this.messagesSubject.next(messages || []);\r\n      console.log('Recent messages subject updated, current value:', this.messagesSubject.value.length);\r\n    });\r\n\r\n    this.socketService.on('groupJoined', ({ groupId }: { groupId: string }) => {\r\n      console.log('Joined group:', groupId);\r\n      const groups = this.groupsSubject.value;\r\n      const group = groups.find(g => g.id === groupId);\r\n      if (group) {\r\n        this.currentGroupSubject.next(group);\r\n      }\r\n    });\r\n\r\n    this.socketService.on('newMessage', (message: Message) => {\r\n      console.log('New message received:', message);\r\n      console.log('Current user:', this.userSubject.value);\r\n      console.log('Current group:', this.currentGroupSubject.value?.id);\r\n      console.log('Message group ID:', message.groupId);\r\n      \r\n      const currentMessages = this.messagesSubject.value;\r\n      const currentUser = this.userSubject.value;\r\n      \r\n      // Check if this is our own message (to replace optimistic message)\r\n      if (message.username === currentUser) {\r\n        // Find and replace optimistic message with real message\r\n        const optimisticIndex = currentMessages.findIndex(msg => \r\n          msg.id.startsWith('temp_') && \r\n          msg.text === message.text && \r\n          msg.username === currentUser &&\r\n          msg.groupId === message.groupId\r\n        );\r\n        \r\n        if (optimisticIndex !== -1) {\r\n          // Replace optimistic message\r\n          const updatedMessages = [...currentMessages];\r\n          updatedMessages[optimisticIndex] = message;\r\n          this.messagesSubject.next(updatedMessages);\r\n          console.log('Replaced optimistic message with real message from socket:', message);\r\n        } else {\r\n          // Add new message if no optimistic message found\r\n          this.messagesSubject.next([...currentMessages, message]);\r\n          console.log('Added new message from socket (no optimistic message found):', message);\r\n        }\r\n      } else {\r\n        // Add message from other users\r\n        this.messagesSubject.next([...currentMessages, message]);\r\n        console.log('Added message from other user:', message);\r\n      }\r\n      \r\n      // Show notification if not current user and window not focused\r\n      if (message.username !== currentUser && document.hidden) {\r\n        this.notificationService.showMessageNotification(message.username, message.text);\r\n      }\r\n    });\r\n\r\n    this.socketService.on('reactionUpdate', ({ messageId, reactions }: { messageId: string; reactions: any[] }) => {\r\n      console.log('Reaction update:', messageId, reactions);\r\n      const currentMessages = this.messagesSubject.value;\r\n      this.messagesSubject.next(\r\n        currentMessages.map(msg => \r\n          msg.id === messageId \r\n            ? { ...msg, reactions }\r\n            : msg\r\n        )\r\n      );\r\n    });\r\n\r\n    this.socketService.on('onlineUsersUpdate', (users: User[]) => {\r\n      console.log('Online users updated:', users);\r\n      this.onlineUsersSubject.next(users || []);\r\n    });\r\n\r\n    this.socketService.on('userJoined', ({ username }: { username: string }) => {\r\n      console.log('User joined:', username);\r\n      // Online users will be updated via onlineUsersUpdate event\r\n    });\r\n\r\n    this.socketService.on('userLeft', ({ username }: { username: string }) => {\r\n      console.log('User left:', username);\r\n      // Online users will be updated via onlineUsersUpdate event\r\n    });\r\n\r\n    this.socketService.on('messageUpdated', ({ messageId, newText, updatedAt }: { messageId: string; newText: string; updatedAt: string }) => {\r\n      console.log('Message updated:', messageId, newText);\r\n      const currentMessages = this.messagesSubject.value;\r\n      this.messagesSubject.next(\r\n        currentMessages.map(msg => \r\n          msg.id === messageId \r\n            ? { ...msg, text: newText, updated_at: updatedAt }\r\n            : msg\r\n        )\r\n      );\r\n    });\r\n\r\n    this.socketService.on('messageDeleted', ({ messageId }: { messageId: string }) => {\r\n      console.log('Message deleted:', messageId);\r\n      const currentMessages = this.messagesSubject.value;\r\n      this.messagesSubject.next(\r\n        currentMessages.filter(msg => msg.id !== messageId)\r\n      );\r\n    });\r\n\r\n    this.socketService.on('error', (error: any) => {\r\n      console.error('Socket error:', error);\r\n    });\r\n  }\r\n\r\n  private setupMessageRefresh(): void {\r\n    // Clear any existing interval first\r\n    if (this.messageRefreshInterval) {\r\n      clearInterval(this.messageRefreshInterval);\r\n    }\r\n    \r\n    // Temporarily disable message refresh to prevent browser freezing\r\n    // TODO: Re-enable once socket connection issues are resolved\r\n    /*\r\n    this.messageRefreshInterval = setInterval(() => {\r\n      try {\r\n        if (!this.socketService.isConnectedSubject.value) {\r\n          const currentGroup = this.currentGroupSubject.value;\r\n          if (currentGroup) {\r\n            console.log('Refreshing messages via HTTP API');\r\n            this.loadRecentMessages(currentGroup.id);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Error in message refresh interval:', error);\r\n        // Clear the interval if there's an error to prevent crashes\r\n        if (this.messageRefreshInterval) {\r\n          clearInterval(this.messageRefreshInterval);\r\n          this.messageRefreshInterval = null;\r\n        }\r\n      }\r\n    }, 5000);\r\n    */\r\n  }\r\n\r\n  async login(username: string, password?: string, inviteCode: string | null = null): Promise<void> {\r\n    try {\r\n      this.loadingSubject.next(true);\r\n      console.log('Starting login process for:', username);\r\n      \r\n      const userData = await this.apiService.loginUser(username, password, inviteCode || undefined).toPromise();\r\n      console.log('Login API response:', userData);\r\n      \r\n      // Check if password is required\r\n      if (userData?.requiresPassword) {\r\n        throw new Error('Password required for this user');\r\n      }\r\n      \r\n      // Connect to socket with auth data\r\n      console.log('Connecting to socket...');\r\n      this.socketService.connect(username, password, inviteCode);\r\n      \r\n      this.userSubject.next(username);\r\n      this.isAdminSubject.next(userData?.isAdmin || false);\r\n      this.securityInfoSubject.next(userData?.securityInfo || null);\r\n      \r\n      console.log('Login completed successfully for:', username, '(Admin:', userData?.isAdmin || false, ')');\r\n      \r\n      // Wait for socket connection before loading groups\r\n      // The socket will emit 'userGroups' event which will handle group loading\r\n      // This prevents race conditions between API and socket calls\r\n      \r\n      // Add fallback: if socket doesn't load groups within 3 seconds, try API\r\n      setTimeout(() => {\r\n        if (this.groupsSubject.value.length === 0) {\r\n          console.log('Socket groups not loaded, trying API fallback...');\r\n          this.loadUserGroups(username);\r\n        }\r\n      }, 3000);\r\n      \r\n    } catch (error) {\r\n      console.error('Login failed:', error);\r\n      throw error;\r\n    } finally {\r\n      this.loadingSubject.next(false);\r\n    }\r\n  }\r\n\r\n  async loadRecentMessages(groupId: string): Promise<void> {\r\n    // Safety guard to prevent infinite loops\r\n    if (this.isLoadingMessages) {\r\n      console.log('Already loading messages, skipping...');\r\n      return;\r\n    }\r\n    \r\n    // Throttle message loading to prevent excessive API calls\r\n    const now = Date.now();\r\n    if (now - this.lastMessageLoadTime < this.MESSAGE_LOAD_THROTTLE) {\r\n      console.log('Throttling message load request');\r\n      return;\r\n    }\r\n    this.lastMessageLoadTime = now;\r\n    \r\n    this.isLoadingMessages = true;\r\n    try {\r\n      console.log('Loading recent messages for group:', groupId);\r\n      const messages = await this.apiService.getMessages(groupId, 50).toPromise();\r\n      console.log('Loaded messages:', messages?.length || 0);\r\n      console.log('Messages data:', messages);\r\n      \r\n      // For group switching, replace messages with only the current group's messages\r\n      // This ensures we only show messages for the current group\r\n      this.messagesSubject.next(messages || []);\r\n      console.log('Messages subject updated, current value:', this.messagesSubject.value.length);\r\n    } catch (error) {\r\n      console.error('Failed to load messages:', error);\r\n      // Set empty array to prevent UI from showing stale data\r\n      this.messagesSubject.next([]);\r\n    } finally {\r\n      this.isLoadingMessages = false;\r\n    }\r\n  }\r\n\r\n  async loadUserGroups(username: string): Promise<void> {\r\n    // Safety guard to prevent infinite loops\r\n    if (this.isLoadingGroups) {\r\n      console.log('Already loading groups, skipping...');\r\n      return;\r\n    }\r\n    \r\n    this.isLoadingGroups = true;\r\n    try {\r\n      console.log('Loading user groups for:', username);\r\n      const groups = await this.apiService.getUserGroups(username).toPromise();\r\n      console.log('Loaded user groups:', groups);\r\n      this.groupsSubject.next(groups || []);\r\n      if (groups && groups.length > 0) {\r\n        console.log('Auto-selecting first group:', groups[0]);\r\n        this.currentGroupSubject.next(groups[0]);\r\n        this.loadRecentMessages(groups[0].id);\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to load user groups:', error);\r\n    } finally {\r\n      this.isLoadingGroups = false;\r\n    }\r\n  }\r\n\r\n  // Safe method to manually load groups (call this from browser console)\r\n  async manualLoadGroups(): Promise<void> {\r\n    const currentUser = this.userSubject.value;\r\n    if (currentUser) {\r\n      console.log('Manually loading groups for:', currentUser);\r\n      await this.loadUserGroups(currentUser);\r\n    } else {\r\n      console.error('No user logged in');\r\n    }\r\n  }\r\n\r\n  // Debug method to check current state\r\n  debugState(): void {\r\n    console.log('=== Chat Service Debug State ===');\r\n    console.log('User:', this.userSubject.value);\r\n    console.log('Groups:', this.groupsSubject.value);\r\n    console.log('Current Group:', this.currentGroupSubject.value);\r\n    console.log('Socket Connected:', this.socketService.isConnectedSubject.value);\r\n    console.log('Loading Groups:', this.isLoadingGroups);\r\n    console.log('Loading Messages:', this.isLoadingMessages);\r\n    console.log('================================');\r\n  }\r\n\r\n  // Helper method to replace optimistic message with real message\r\n  private replaceOptimisticMessage(tempId: string, realMessage: Message): void {\r\n    const currentMessages = this.messagesSubject.value;\r\n    const updatedMessages = currentMessages.map(msg => \r\n      msg.id === tempId ? realMessage : msg\r\n    );\r\n    this.messagesSubject.next(updatedMessages);\r\n    console.log('Replaced optimistic message with real message:', realMessage);\r\n  }\r\n\r\n  // Helper method to remove optimistic message (on error)\r\n  private removeOptimisticMessage(tempId: string): void {\r\n    const currentMessages = this.messagesSubject.value;\r\n    const updatedMessages = currentMessages.filter(msg => msg.id !== tempId);\r\n    this.messagesSubject.next(updatedMessages);\r\n    console.log('Removed optimistic message due to error:', tempId);\r\n  }\r\n\r\n  joinGroup(groupId: string): void {\r\n    const currentUser = this.userSubject.value;\r\n    if (!groupId || !currentUser) {\r\n      console.error('Cannot join group - missing groupId or user');\r\n      return;\r\n    }\r\n    \r\n    console.log('Joining group:', groupId);\r\n    \r\n    // Check if socket is connected before joining\r\n    if (!this.socketService.isConnectedSubject.value) {\r\n      console.error('Cannot join group - socket not connected');\r\n      return;\r\n    }\r\n    \r\n    this.socketService.joinGroup(groupId);\r\n    const groups = this.groupsSubject.value;\r\n    const group = groups.find(g => g.id === groupId);\r\n    if (group) {\r\n      this.currentGroupSubject.next(group);\r\n      // Load recent messages for the group\r\n      this.loadRecentMessages(groupId);\r\n    } else {\r\n      console.error('Group not found in user groups:', groupId);\r\n    }\r\n  }\r\n\r\n  sendMessage(text: string, replyToId: string | null = null): void {\r\n    const currentUser = this.userSubject.value;\r\n    const currentGroup = this.currentGroupSubject.value;\r\n    \r\n    if (!text.trim()) {\r\n      console.error('Cannot send message - empty text');\r\n      return;\r\n    }\r\n    \r\n    if (!currentUser) {\r\n      console.error('Cannot send message - user not logged in');\r\n      return;\r\n    }\r\n    \r\n    if (!currentGroup) {\r\n      console.error('Cannot send message - no group selected. Available groups:', this.groupsSubject.value);\r\n      // Try to auto-select the first available group\r\n      const groups = this.groupsSubject.value;\r\n      if (groups && groups.length > 0) {\r\n        console.log('Auto-selecting first available group:', groups[0]);\r\n        this.currentGroupSubject.next(groups[0]);\r\n        this.loadRecentMessages(groups[0].id);\r\n        // Retry sending the message - DISABLED to prevent infinite loops\r\n        // setTimeout(() => this.sendMessage(text, replyToId), 100);\r\n        return;\r\n      } else {\r\n        console.error('No groups available for user');\r\n        return;\r\n      }\r\n    }\r\n\r\n    // Create optimistic message for immediate display\r\n    const optimisticMessage: Message = {\r\n      id: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`, // Unique temporary ID\r\n      text: text.trim(),\r\n      username: currentUser,\r\n      timestamp: new Date().toISOString(),\r\n      groupId: currentGroup.id,\r\n      replyTo: replyToId || undefined,\r\n      reactions: []\r\n    };\r\n\r\n    // Add optimistic message to local state immediately\r\n    const currentMessages = this.messagesSubject.value;\r\n    const updatedMessages = [...currentMessages, optimisticMessage];\r\n    this.messagesSubject.next(updatedMessages);\r\n    console.log('📤 Added optimistic message:', optimisticMessage);\r\n\r\n    // Send message with acknowledgment and retry logic\r\n    this.sendMessageWithRetry(text, currentGroup.id, replyToId, optimisticMessage);\r\n  }\r\n\r\n  private async sendMessageWithRetry(\r\n    text: string,\r\n    groupId: string,\r\n    replyToId: string | null,\r\n    optimisticMessage: Message,\r\n    retryCount: number = 0\r\n  ): Promise<void> {\r\n    const maxRetries = 3;\r\n    const retryDelay = 1000 * Math.pow(2, retryCount); // Exponential backoff\r\n\r\n    try {\r\n      if (this.socketService.isConnected()) {\r\n        // Try sending via socket with acknowledgment\r\n        const response = await this.socketService.emitWithAck('sendMessage', {\r\n          text,\r\n          groupId,\r\n          replyTo: replyToId,\r\n          messageId: optimisticMessage.id\r\n        });\r\n\r\n        if (response.success) {\r\n          console.log('✅ Message sent successfully via socket:', response);\r\n          // Replace optimistic message with real message\r\n          this.replaceOptimisticMessage(optimisticMessage.id, response.message);\r\n          return;\r\n        } else {\r\n          throw new Error(response.error || 'Unknown error');\r\n        }\r\n      } else {\r\n        throw new Error('Socket not connected');\r\n      }\r\n    } catch (error) {\r\n      console.error(`❌ Failed to send message (attempt ${retryCount + 1}):`, error);\r\n\r\n      if (retryCount < maxRetries) {\r\n        // Retry with exponential backoff\r\n        console.log(`🔄 Retrying in ${retryDelay}ms...`);\r\n        setTimeout(() => {\r\n          this.sendMessageWithRetry(text, groupId, replyToId, optimisticMessage, retryCount + 1);\r\n        }, retryDelay);\r\n      } else {\r\n        // All retries failed, try HTTP API as final fallback\r\n        console.log('🌐 All socket retries failed, trying HTTP API fallback');\r\n        this.sendMessageViaHttpFallback(text, groupId, replyToId, optimisticMessage);\r\n      }\r\n    }\r\n  }\r\n\r\n  private sendMessageViaHttpFallback(\r\n    text: string,\r\n    groupId: string,\r\n    replyToId: string | null,\r\n    optimisticMessage: Message\r\n  ): void {\r\n    const currentUser = this.userSubject.value;\r\n    if (!currentUser) {\r\n      this.removeOptimisticMessage(optimisticMessage.id);\r\n      return;\r\n    }\r\n\r\n    this.apiService.sendMessage(text, currentUser, groupId, replyToId).subscribe({\r\n      next: (response) => {\r\n        console.log('✅ Message sent via HTTP API:', response);\r\n        this.replaceOptimisticMessage(optimisticMessage.id, response);\r\n      },\r\n      error: (error) => {\r\n        console.error('❌ Failed to send message via HTTP API:', error);\r\n        this.removeOptimisticMessage(optimisticMessage.id);\r\n        // Show error notification to user\r\n        this.showMessageError('Failed to send message. Please try again.');\r\n      }\r\n    });\r\n  }\r\n\r\n  private showMessageError(message: string): void {\r\n    // You can implement a toast notification service here\r\n    console.error('💬 Message Error:', message);\r\n    // For now, just log the error. In a real app, you'd show a toast/snackbar\r\n  }\r\n\r\n  replyToMessage(message: Message): void {\r\n    this.replyToSubject.next(message);\r\n  }\r\n\r\n  cancelReply(): void {\r\n    this.replyToSubject.next(null);\r\n  }\r\n\r\n  addReaction(messageId: string, emoji: string): void {\r\n    this.socketService.addReaction(messageId, emoji);\r\n  }\r\n\r\n  removeReaction(data: { messageId: string; emoji?: string }): void {\r\n    this.socketService.removeReaction(data);\r\n  }\r\n\r\n  async updateMessage(messageId: string, newText: string): Promise<void> {\r\n    const currentUser = this.userSubject.value;\r\n    if (!currentUser) {\r\n      console.error('Cannot update message - user not logged in');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Update the message in the local state immediately for better UX\r\n      const currentMessages = this.messagesSubject.value;\r\n      this.messagesSubject.next(\r\n        currentMessages.map(msg => \r\n          msg.id === messageId \r\n            ? { ...msg, text: newText, updated_at: new Date().toISOString() }\r\n            : msg\r\n        )\r\n      );\r\n\r\n      // Emit socket event for real-time updates\r\n      if (this.socketService.isConnectedSubject.value) {\r\n        this.socketService.emit('messageUpdated', { messageId, newText });\r\n      } else {\r\n        // Fallback to HTTP API if socket not connected\r\n        const updatedMessage = await this.apiService.updateMessage(messageId, newText, currentUser).toPromise();\r\n        this.messagesSubject.next(\r\n          currentMessages.map(msg => \r\n            msg.id === messageId \r\n              ? { ...msg, text: newText, updated_at: updatedMessage.updated_at }\r\n              : msg\r\n          )\r\n        );\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to update message:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async deleteMessage(messageId: string): Promise<void> {\r\n    const currentUser = this.userSubject.value;\r\n    if (!currentUser) {\r\n      console.error('Cannot delete message - user not logged in');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Remove the message from local state immediately for better UX\r\n      const currentMessages = this.messagesSubject.value;\r\n      this.messagesSubject.next(\r\n        currentMessages.filter(msg => msg.id !== messageId)\r\n      );\r\n\r\n      // Emit socket event for real-time updates\r\n      if (this.socketService.isConnectedSubject.value) {\r\n        this.socketService.emit('messageDeleted', { messageId });\r\n      } else {\r\n        // Fallback to HTTP API if socket not connected\r\n        await this.apiService.deleteMessage(messageId, currentUser).toPromise();\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to delete message:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  showAdminPanel(): void {\r\n    this.showAdminPanelSubject.next(true);\r\n  }\r\n\r\n  hideAdminPanel(): void {\r\n    this.showAdminPanelSubject.next(false);\r\n  }\r\n\r\n  startEditingMessage(message: Message): void {\r\n    this.editingMessageSubject.next(message);\r\n  }\r\n\r\n  cancelEditingMessage(): void {\r\n    this.editingMessageSubject.next(null);\r\n  }\r\n\r\n  logout(): void {\r\n    try {\r\n      // Disconnect socket first\r\n      this.socketService.disconnect();\r\n      \r\n      // Clear interval\r\n      if (this.messageRefreshInterval) {\r\n        clearInterval(this.messageRefreshInterval);\r\n        this.messageRefreshInterval = null;\r\n      }\r\n      \r\n      // Reset all subjects\r\n      this.userSubject.next(null);\r\n      this.messagesSubject.next([]);\r\n      this.onlineUsersSubject.next([]);\r\n      this.groupsSubject.next([]);\r\n      this.currentGroupSubject.next(null);\r\n      this.replyToSubject.next(null);\r\n      this.loadingSubject.next(false);\r\n      this.isAdminSubject.next(false);\r\n      this.showAdminPanelSubject.next(false);\r\n      this.securityInfoSubject.next(null);\r\n      this.editingMessageSubject.next(null);\r\n      \r\n      console.log('Logout completed successfully');\r\n    } catch (error) {\r\n      console.error('Error during logout:', error);\r\n    }\r\n  }\r\n\r\n  // Method to refresh security info for all components\r\n  refreshSecurityInfo(): void {\r\n    this.apiService.getSecurityInfo().subscribe({\r\n      next: (data) => {\r\n        this.securityInfoSubject.next(data);\r\n      },\r\n      error: (error) => {\r\n        console.error('Failed to refresh security info:', error);\r\n      }\r\n    });\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module"}