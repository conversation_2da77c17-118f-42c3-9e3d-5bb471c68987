{"ast": null, "code": "import { EmptyError } from '../util/EmptyError';\nimport { SequenceError } from '../util/SequenceError';\nimport { NotFoundError } from '../util/NotFoundError';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function single(predicate) {\n  return operate((source, subscriber) => {\n    let hasValue = false;\n    let singleValue;\n    let seenValue = false;\n    let index = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      seenValue = true;\n\n      if (!predicate || predicate(value, index++, source)) {\n        hasValue && subscriber.error(new SequenceError('Too many matching values'));\n        hasValue = true;\n        singleValue = value;\n      }\n    }, () => {\n      if (hasValue) {\n        subscriber.next(singleValue);\n        subscriber.complete();\n      } else {\n        subscriber.error(seenValue ? new NotFoundError('No matching values') : new EmptyError());\n      }\n    }));\n  });\n}", "map": {"version": 3, "names": ["EmptyError", "SequenceError", "NotFoundError", "operate", "createOperatorSubscriber", "single", "predicate", "source", "subscriber", "hasValue", "singleValue", "seenValue", "index", "subscribe", "value", "error", "next", "complete"], "sources": ["R:/chateye/FrontendAngular/node_modules/rxjs/dist/esm/internal/operators/single.js"], "sourcesContent": ["import { EmptyError } from '../util/EmptyError';\nimport { SequenceError } from '../util/SequenceError';\nimport { NotFoundError } from '../util/NotFoundError';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function single(predicate) {\n    return operate((source, subscriber) => {\n        let hasValue = false;\n        let singleValue;\n        let seenValue = false;\n        let index = 0;\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            seenValue = true;\n            if (!predicate || predicate(value, index++, source)) {\n                hasValue && subscriber.error(new SequenceError('Too many matching values'));\n                hasValue = true;\n                singleValue = value;\n            }\n        }, () => {\n            if (hasValue) {\n                subscriber.next(singleValue);\n                subscriber.complete();\n            }\n            else {\n                subscriber.error(seenValue ? new NotFoundError('No matching values') : new EmptyError());\n            }\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,oBAA3B;AACA,SAASC,aAAT,QAA8B,uBAA9B;AACA,SAASC,aAAT,QAA8B,uBAA9B;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,OAAO,SAASC,MAAT,CAAgBC,SAAhB,EAA2B;EAC9B,OAAOH,OAAO,CAAC,CAACI,MAAD,EAASC,UAAT,KAAwB;IACnC,IAAIC,QAAQ,GAAG,KAAf;IACA,IAAIC,WAAJ;IACA,IAAIC,SAAS,GAAG,KAAhB;IACA,IAAIC,KAAK,GAAG,CAAZ;IACAL,MAAM,CAACM,SAAP,CAAiBT,wBAAwB,CAACI,UAAD,EAAcM,KAAD,IAAW;MAC7DH,SAAS,GAAG,IAAZ;;MACA,IAAI,CAACL,SAAD,IAAcA,SAAS,CAACQ,KAAD,EAAQF,KAAK,EAAb,EAAiBL,MAAjB,CAA3B,EAAqD;QACjDE,QAAQ,IAAID,UAAU,CAACO,KAAX,CAAiB,IAAId,aAAJ,CAAkB,0BAAlB,CAAjB,CAAZ;QACAQ,QAAQ,GAAG,IAAX;QACAC,WAAW,GAAGI,KAAd;MACH;IACJ,CAPwC,EAOtC,MAAM;MACL,IAAIL,QAAJ,EAAc;QACVD,UAAU,CAACQ,IAAX,CAAgBN,WAAhB;QACAF,UAAU,CAACS,QAAX;MACH,CAHD,MAIK;QACDT,UAAU,CAACO,KAAX,CAAiBJ,SAAS,GAAG,IAAIT,aAAJ,CAAkB,oBAAlB,CAAH,GAA6C,IAAIF,UAAJ,EAAvE;MACH;IACJ,CAfwC,CAAzC;EAgBH,CArBa,CAAd;AAsBH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}