import { Component, OnInit, On<PERSON><PERSON>roy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { Observable, combineLatest, Subscription } from 'rxjs';
import { map, take } from 'rxjs/operators';
import { ChatService } from './services/chat.service';
import { Message, Group, User } from './services/api.service';
import { PERFORMANCE_CONFIG } from './config/performance.config';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AppComponent implements OnInit, OnDestroy {
  // Cached values to reduce async pipe usage
  user: string | null = null;
  messages: Message[] = [];
  onlineUsers: User[] = [];
  groups: Group[] = [];
  currentGroup: Group | null = null;
  replyTo: Message | null = null;
  loading = false;
  isAdmin = false;
  showAdminPanel = false;
  connected = false;
  isLoggedIn = false;
  showPasswordChange = false;
  editingMessage: Message | null = null;
  highlightedMessageId: string | null = null;
  typingUsers: string[] = [];
  
  // Performance configuration
  useVirtualScrolling = PERFORMANCE_CONFIG.enableVirtualScrolling;
  
  private subscriptions: Subscription[] = [];

  constructor(private chatService: ChatService, private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
    // Subscribe to all observables and cache values to reduce async pipe usage
    this.subscriptions.push(
      this.chatService.user$.subscribe(user => {
        this.user = user;
        this.cdr.markForCheck();
      }),
      this.chatService.messages$.subscribe(messages => {
        this.messages = messages;
        this.cdr.markForCheck();
      }),
      this.chatService.onlineUsers$.subscribe(users => {
        this.onlineUsers = users;
        this.cdr.markForCheck();
      }),
      this.chatService.groups$.subscribe(groups => {
        this.groups = groups;
        this.cdr.markForCheck();
      }),
      this.chatService.currentGroup$.subscribe(group => {
        this.currentGroup = group;
        this.cdr.markForCheck();
      }),
      this.chatService.replyTo$.subscribe(replyTo => {
        this.replyTo = replyTo;
        this.cdr.markForCheck();
      }),
      this.chatService.loading$.subscribe(loading => {
        this.loading = loading;
        this.cdr.markForCheck();
      }),
      this.chatService.isAdmin$.subscribe(isAdmin => {
        this.isAdmin = isAdmin;
        this.cdr.markForCheck();
      }),
      this.chatService.showAdminPanel$.subscribe(showPanel => {
        this.showAdminPanel = showPanel;
        this.cdr.markForCheck();
      }),
      this.chatService.connected$.subscribe(connected => {
        this.connected = connected;
        this.cdr.markForCheck();
      }),
      this.chatService.isLoggedIn$.subscribe(isLoggedIn => {
        this.isLoggedIn = isLoggedIn;
        this.cdr.markForCheck();
      }),
      this.chatService.editingMessage$.subscribe(editingMessage => {
        this.editingMessage = editingMessage;
        this.cdr.markForCheck();
      }),
      this.chatService.typingUsers$.subscribe(typingUsers => {
        this.typingUsers = typingUsers;
        this.cdr.markForCheck();
      })
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.chatService.logout();
  }

  onJoinGroup(groupId: string): void {
    this.chatService.joinGroup(groupId);
  }

  onSendMessage(event: { text: string; replyToId: string | null }): void {
    this.chatService.sendMessage(event.text, event.replyToId);
  }

  onReply(message: Message): void {
    this.chatService.replyToMessage(message);
    
    // Highlight the replied-to message
    this.highlightedMessageId = message.id;
    
    // Clear highlight after 3 seconds
    setTimeout(() => {
      this.highlightedMessageId = null;
    }, 3000);
  }

  onCancelReply(): void {
    this.chatService.cancelReply();
  }

  onAddReaction(event: { messageId: string; emoji: string }): void {
    this.chatService.addReaction(event.messageId, event.emoji);
  }

  onRemoveReaction(data: { messageId: string; emoji: string }): void {
    this.chatService.removeReaction(data);
  }

  onEdit(message: Message): void {
    this.chatService.startEditingMessage(message);
  }

  async onSaveEdit(newText: string): Promise<void> {
    try {
      if (this.editingMessage) {
        await this.chatService.updateMessage(this.editingMessage.id, newText);
        this.chatService.cancelEditingMessage();
      }
    } catch (error) {
      console.error('Failed to edit message:', error);
      alert('Failed to edit message. Please try again.');
    }
  }

  onCancelEdit(): void {
    this.chatService.cancelEditingMessage();
  }

  async onDelete(message: Message): Promise<void> {
    try {
      if (confirm('Are you sure you want to delete this message?')) {
        await this.chatService.deleteMessage(message.id);
      }
    } catch (error) {
      console.error('Failed to delete message:', error);
      alert('Failed to delete message. Please try again.');
    }
  }

  onShowAdminPanel(): void {
    this.chatService.showAdminPanel();
  }

  onHideAdminPanel(): void {
    this.chatService.hideAdminPanel();
  }

  onPasswordChange(): void {
    this.showPasswordChange = true;
  }

  onClosePasswordChange(): void {
    this.showPasswordChange = false;
  }

  onLogout(): void {
    this.chatService.logout();
  }

  onMessageClick(message: Message): void {
    this.highlightedMessageId = message.id;
    
    // Clear highlight after 3 seconds
    setTimeout(() => {
      this.highlightedMessageId = null;
    }, 3000);
  }



  getTypingText(typingUsers: string[]): string {
    if (typingUsers.length === 0) return '';

    if (typingUsers.length === 1) {
      return `${typingUsers[0]} is typing...`;
    } else if (typingUsers.length === 2) {
      return `${typingUsers[0]} and ${typingUsers[1]} are typing...`;
    } else {
      return `${typingUsers[0]} and ${typingUsers.length - 1} others are typing...`;
    }
  }

}
