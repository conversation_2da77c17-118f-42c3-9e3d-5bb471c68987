{"ast": null, "code": "import { Socket } from \"./socket.js\";\nexport { Socket };\nexport { SocketWithoutUpgrade, SocketWithUpgrade } from \"./socket.js\";\nexport const protocol = Socket.protocol;\nexport { Transport, TransportError } from \"./transport.js\";\nexport { transports } from \"./transports/index.js\";\nexport { installTimerFunctions } from \"./util.js\";\nexport { parse } from \"./contrib/parseuri.js\";\nexport { nextTick } from \"./globals.node.js\";\nexport { Fetch } from \"./transports/polling-fetch.js\";\nexport { XHR as NodeXHR } from \"./transports/polling-xhr.node.js\";\nexport { XHR } from \"./transports/polling-xhr.js\";\nexport { WS as NodeWebSocket } from \"./transports/websocket.node.js\";\nexport { WS as WebSocket } from \"./transports/websocket.js\";\nexport { WT as WebTransport } from \"./transports/webtransport.js\";", "map": {"version": 3, "names": ["Socket", "SocketWithoutUpgrade", "SocketWithUpgrade", "protocol", "Transport", "TransportError", "transports", "installTimerFunctions", "parse", "nextTick", "<PERSON>tch", "XHR", "NodeXHR", "WS", "NodeWebSocket", "WebSocket", "WT", "WebTransport"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/engine.io-client/build/esm/index.js"], "sourcesContent": ["import { Socket } from \"./socket.js\";\nexport { Socket };\nexport { SocketWithoutUpgrade, SocketWithUpgrade, } from \"./socket.js\";\nexport const protocol = Socket.protocol;\nexport { Transport, TransportError } from \"./transport.js\";\nexport { transports } from \"./transports/index.js\";\nexport { installTimerFunctions } from \"./util.js\";\nexport { parse } from \"./contrib/parseuri.js\";\nexport { nextTick } from \"./globals.node.js\";\nexport { Fetch } from \"./transports/polling-fetch.js\";\nexport { XHR as NodeXHR } from \"./transports/polling-xhr.node.js\";\nexport { XHR } from \"./transports/polling-xhr.js\";\nexport { WS as NodeWebSocket } from \"./transports/websocket.node.js\";\nexport { WS as WebSocket } from \"./transports/websocket.js\";\nexport { WT as WebTransport } from \"./transports/webtransport.js\";\n"], "mappings": "AAAA,SAASA,MAAT,QAAuB,aAAvB;AACA,SAASA,MAAT;AACA,SAASC,oBAAT,EAA+BC,iBAA/B,QAAyD,aAAzD;AACA,OAAO,MAAMC,QAAQ,GAAGH,MAAM,CAACG,QAAxB;AACP,SAASC,SAAT,EAAoBC,cAApB,QAA0C,gBAA1C;AACA,SAASC,UAAT,QAA2B,uBAA3B;AACA,SAASC,qBAAT,QAAsC,WAAtC;AACA,SAASC,KAAT,QAAsB,uBAAtB;AACA,SAASC,QAAT,QAAyB,mBAAzB;AACA,SAASC,KAAT,QAAsB,+BAAtB;AACA,SAASC,GAAG,IAAIC,OAAhB,QAA+B,kCAA/B;AACA,SAASD,GAAT,QAAoB,6BAApB;AACA,SAASE,EAAE,IAAIC,aAAf,QAAoC,gCAApC;AACA,SAASD,EAAE,IAAIE,SAAf,QAAgC,2BAAhC;AACA,SAASC,EAAE,IAAIC,YAAf,QAAmC,8BAAnC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}