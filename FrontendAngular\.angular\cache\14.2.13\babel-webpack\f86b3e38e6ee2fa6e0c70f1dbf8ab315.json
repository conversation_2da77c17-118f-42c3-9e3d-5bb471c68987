{"ast": null, "code": "import * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollable, CdkScrollableModule } from '@angular/cdk/scrolling';\nimport * as i5 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, EventEmitter, Optional, Input, Output, ViewChild, QueryList, ContentChildren, ContentChild, NgModule } from '@angular/core';\nimport { MatCommonModule } from '@angular/material/core';\nimport * as i2 from '@angular/cdk/a11y';\nimport * as i4 from '@angular/cdk/bidi';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport * as i3 from '@angular/cdk/platform';\nimport { Subject, fromEvent, merge } from 'rxjs';\nimport { filter, map, mapTo, takeUntil, distinctUntilChanged, take, startWith, debounceTime } from 'rxjs/operators';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Animations used by the Material drawers.\n * @docs-private\n */\n\nconst _c0 = [\"*\"];\nconst _c1 = [\"content\"];\n\nfunction MatDrawerContainer_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵlistener(\"click\", function MatDrawerContainer_div_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2._onBackdropClicked());\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"mat-drawer-shown\", ctx_r0._isShowingBackdrop());\n  }\n}\n\nfunction MatDrawerContainer_mat_drawer_content_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-drawer-content\");\n    i0.ɵɵprojection(1, 2);\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c2 = [[[\"mat-drawer\"]], [[\"mat-drawer-content\"]], \"*\"];\nconst _c3 = [\"mat-drawer\", \"mat-drawer-content\", \"*\"];\n\nfunction MatSidenavContainer_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵlistener(\"click\", function MatSidenavContainer_div_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2._onBackdropClicked());\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"mat-drawer-shown\", ctx_r0._isShowingBackdrop());\n  }\n}\n\nfunction MatSidenavContainer_mat_sidenav_content_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-sidenav-content\");\n    i0.ɵɵprojection(1, 2);\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c4 = [[[\"mat-sidenav\"]], [[\"mat-sidenav-content\"]], \"*\"];\nconst _c5 = [\"mat-sidenav\", \"mat-sidenav-content\", \"*\"];\nconst _c6 = \".mat-drawer-container{position:relative;z-index:1;box-sizing:border-box;-webkit-overflow-scrolling:touch;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}.cdk-high-contrast-active .mat-drawer-backdrop{opacity:.5}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{position:relative;z-index:4;display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}.cdk-high-contrast-active .mat-drawer,.cdk-high-contrast-active [dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}.cdk-high-contrast-active [dir=rtl] .mat-drawer,.cdk-high-contrast-active .mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer{transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer[style*=\\\"visibility: hidden\\\"]{display:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto;-webkit-overflow-scrolling:touch}.mat-sidenav-fixed{position:fixed}\";\nconst matDrawerAnimations = {\n  /** Animation that slides a drawer in and out. */\n  transformDrawer: /*#__PURE__*/trigger('transform', [\n  /*#__PURE__*/\n  // We remove the `transform` here completely, rather than setting it to zero, because:\n  // 1. Having a transform can cause elements with ripples or an animated\n  //    transform to shift around in Chrome with an RTL layout (see #10023).\n  // 2. 3d transforms causes text to appear blurry on IE and Edge.\n  state('open, open-instant', /*#__PURE__*/style({\n    'transform': 'none',\n    'visibility': 'visible'\n  })), /*#__PURE__*/state('void', /*#__PURE__*/style({\n    // Avoids the shadow showing up when closed in SSR.\n    'box-shadow': 'none',\n    'visibility': 'hidden'\n  })), /*#__PURE__*/transition('void => open-instant', /*#__PURE__*/animate('0ms')), /*#__PURE__*/transition('void <=> open, open-instant => void', /*#__PURE__*/animate('400ms cubic-bezier(0.25, 0.8, 0.25, 1)'))])\n};\n/**\n * Throws an exception when two MatDrawer are matching the same position.\n * @docs-private\n */\n\nfunction throwMatDuplicatedDrawerError(position) {\n  throw Error(`A drawer was already declared for 'position=\"${position}\"'`);\n}\n/** Configures whether drawers should use auto sizing by default. */\n\n\nconst MAT_DRAWER_DEFAULT_AUTOSIZE = /*#__PURE__*/new InjectionToken('MAT_DRAWER_DEFAULT_AUTOSIZE', {\n  providedIn: 'root',\n  factory: MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY\n});\n/**\n * Used to provide a drawer container to a drawer while avoiding circular references.\n * @docs-private\n */\n\nconst MAT_DRAWER_CONTAINER = /*#__PURE__*/new InjectionToken('MAT_DRAWER_CONTAINER');\n/** @docs-private */\n\nfunction MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY() {\n  return false;\n}\n\nlet MatDrawerContent = /*#__PURE__*/(() => {\n  class MatDrawerContent extends CdkScrollable {\n    constructor(_changeDetectorRef, _container, elementRef, scrollDispatcher, ngZone) {\n      super(elementRef, scrollDispatcher, ngZone);\n      this._changeDetectorRef = _changeDetectorRef;\n      this._container = _container;\n    }\n\n    ngAfterContentInit() {\n      this._container._contentMarginChanges.subscribe(() => {\n        this._changeDetectorRef.markForCheck();\n      });\n    }\n\n  }\n\n  MatDrawerContent.ɵfac = function MatDrawerContent_Factory(t) {\n    return new (t || MatDrawerContent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(forwardRef(() => MatDrawerContainer)), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.ScrollDispatcher), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n\n  MatDrawerContent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatDrawerContent,\n    selectors: [[\"mat-drawer-content\"]],\n    hostAttrs: [1, \"mat-drawer-content\"],\n    hostVars: 4,\n    hostBindings: function MatDrawerContent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵstyleProp(\"margin-left\", ctx._container._contentMargins.left, \"px\")(\"margin-right\", ctx._container._contentMargins.right, \"px\");\n      }\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkScrollable,\n      useExisting: MatDrawerContent\n    }]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function MatDrawerContent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return MatDrawerContent;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * This component corresponds to a drawer that can be opened on the drawer container.\n */\n\n\nlet MatDrawer = /*#__PURE__*/(() => {\n  class MatDrawer {\n    constructor(_elementRef, _focusTrapFactory, _focusMonitor, _platform, _ngZone, _interactivityChecker, _doc, _container) {\n      this._elementRef = _elementRef;\n      this._focusTrapFactory = _focusTrapFactory;\n      this._focusMonitor = _focusMonitor;\n      this._platform = _platform;\n      this._ngZone = _ngZone;\n      this._interactivityChecker = _interactivityChecker;\n      this._doc = _doc;\n      this._container = _container;\n      this._elementFocusedBeforeDrawerWasOpened = null;\n      /** Whether the drawer is initialized. Used for disabling the initial animation. */\n\n      this._enableAnimations = false;\n      this._position = 'start';\n      this._mode = 'over';\n      this._disableClose = false;\n      this._opened = false;\n      /** Emits whenever the drawer has started animating. */\n\n      this._animationStarted = new Subject();\n      /** Emits whenever the drawer is done animating. */\n\n      this._animationEnd = new Subject();\n      /** Current state of the sidenav animation. */\n\n      this._animationState = 'void';\n      /** Event emitted when the drawer open state is changed. */\n\n      this.openedChange = // Note this has to be async in order to avoid some issues with two-bindings (see #8872).\n      new EventEmitter(\n      /* isAsync */\n      true);\n      /** Event emitted when the drawer has been opened. */\n\n      this._openedStream = this.openedChange.pipe(filter(o => o), map(() => {}));\n      /** Event emitted when the drawer has started opening. */\n\n      this.openedStart = this._animationStarted.pipe(filter(e => e.fromState !== e.toState && e.toState.indexOf('open') === 0), mapTo(undefined));\n      /** Event emitted when the drawer has been closed. */\n\n      this._closedStream = this.openedChange.pipe(filter(o => !o), map(() => {}));\n      /** Event emitted when the drawer has started closing. */\n\n      this.closedStart = this._animationStarted.pipe(filter(e => e.fromState !== e.toState && e.toState === 'void'), mapTo(undefined));\n      /** Emits when the component is destroyed. */\n\n      this._destroyed = new Subject();\n      /** Event emitted when the drawer's position changes. */\n      // tslint:disable-next-line:no-output-on-prefix\n\n      this.onPositionChanged = new EventEmitter();\n      /**\n       * An observable that emits when the drawer mode changes. This is used by the drawer container to\n       * to know when to when the mode changes so it can adapt the margins on the content.\n       */\n\n      this._modeChanged = new Subject();\n      this.openedChange.subscribe(opened => {\n        if (opened) {\n          if (this._doc) {\n            this._elementFocusedBeforeDrawerWasOpened = this._doc.activeElement;\n          }\n\n          this._takeFocus();\n        } else if (this._isFocusWithinDrawer()) {\n          this._restoreFocus(this._openedVia || 'program');\n        }\n      });\n      /**\n       * Listen to `keydown` events outside the zone so that change detection is not run every\n       * time a key is pressed. Instead we re-enter the zone only if the `ESC` key is pressed\n       * and we don't have close disabled.\n       */\n\n      this._ngZone.runOutsideAngular(() => {\n        fromEvent(this._elementRef.nativeElement, 'keydown').pipe(filter(event => {\n          return event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event);\n        }), takeUntil(this._destroyed)).subscribe(event => this._ngZone.run(() => {\n          this.close();\n          event.stopPropagation();\n          event.preventDefault();\n        }));\n      }); // We need a Subject with distinctUntilChanged, because the `done` event\n      // fires twice on some browsers. See https://github.com/angular/angular/issues/24084\n\n\n      this._animationEnd.pipe(distinctUntilChanged((x, y) => {\n        return x.fromState === y.fromState && x.toState === y.toState;\n      })).subscribe(event => {\n        const {\n          fromState,\n          toState\n        } = event;\n\n        if (toState.indexOf('open') === 0 && fromState === 'void' || toState === 'void' && fromState.indexOf('open') === 0) {\n          this.openedChange.emit(this._opened);\n        }\n      });\n    }\n    /** The side that the drawer is attached to. */\n\n\n    get position() {\n      return this._position;\n    }\n\n    set position(value) {\n      // Make sure we have a valid value.\n      value = value === 'end' ? 'end' : 'start';\n\n      if (value !== this._position) {\n        // Static inputs in Ivy are set before the element is in the DOM.\n        if (this._isAttached) {\n          this._updatePositionInParent(value);\n        }\n\n        this._position = value;\n        this.onPositionChanged.emit();\n      }\n    }\n    /** Mode of the drawer; one of 'over', 'push' or 'side'. */\n\n\n    get mode() {\n      return this._mode;\n    }\n\n    set mode(value) {\n      this._mode = value;\n\n      this._updateFocusTrapState();\n\n      this._modeChanged.next();\n    }\n    /** Whether the drawer can be closed with the escape key or by clicking on the backdrop. */\n\n\n    get disableClose() {\n      return this._disableClose;\n    }\n\n    set disableClose(value) {\n      this._disableClose = coerceBooleanProperty(value);\n    }\n    /**\n     * Whether the drawer should focus the first focusable element automatically when opened.\n     * Defaults to false in when `mode` is set to `side`, otherwise defaults to `true`. If explicitly\n     * enabled, focus will be moved into the sidenav in `side` mode as well.\n     * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or AutoFocusTarget\n     * instead.\n     */\n\n\n    get autoFocus() {\n      const value = this._autoFocus; // Note that usually we don't allow autoFocus to be set to `first-tabbable` in `side` mode,\n      // because we don't know how the sidenav is being used, but in some cases it still makes\n      // sense to do it. The consumer can explicitly set `autoFocus`.\n\n      if (value == null) {\n        if (this.mode === 'side') {\n          return 'dialog';\n        } else {\n          return 'first-tabbable';\n        }\n      }\n\n      return value;\n    }\n\n    set autoFocus(value) {\n      if (value === 'true' || value === 'false' || value == null) {\n        value = coerceBooleanProperty(value);\n      }\n\n      this._autoFocus = value;\n    }\n    /**\n     * Whether the drawer is opened. We overload this because we trigger an event when it\n     * starts or end.\n     */\n\n\n    get opened() {\n      return this._opened;\n    }\n\n    set opened(value) {\n      this.toggle(coerceBooleanProperty(value));\n    }\n    /**\n     * Focuses the provided element. If the element is not focusable, it will add a tabIndex\n     * attribute to forcefully focus it. The attribute is removed after focus is moved.\n     * @param element The element to focus.\n     */\n\n\n    _forceFocus(element, options) {\n      if (!this._interactivityChecker.isFocusable(element)) {\n        element.tabIndex = -1; // The tabindex attribute should be removed to avoid navigating to that element again\n\n        this._ngZone.runOutsideAngular(() => {\n          const callback = () => {\n            element.removeEventListener('blur', callback);\n            element.removeEventListener('mousedown', callback);\n            element.removeAttribute('tabindex');\n          };\n\n          element.addEventListener('blur', callback);\n          element.addEventListener('mousedown', callback);\n        });\n      }\n\n      element.focus(options);\n    }\n    /**\n     * Focuses the first element that matches the given selector within the focus trap.\n     * @param selector The CSS selector for the element to set focus to.\n     */\n\n\n    _focusByCssSelector(selector, options) {\n      let elementToFocus = this._elementRef.nativeElement.querySelector(selector);\n\n      if (elementToFocus) {\n        this._forceFocus(elementToFocus, options);\n      }\n    }\n    /**\n     * Moves focus into the drawer. Note that this works even if\n     * the focus trap is disabled in `side` mode.\n     */\n\n\n    _takeFocus() {\n      if (!this._focusTrap) {\n        return;\n      }\n\n      const element = this._elementRef.nativeElement; // When autoFocus is not on the sidenav, if the element cannot be focused or does\n      // not exist, focus the sidenav itself so the keyboard navigation still works.\n      // We need to check that `focus` is a function due to Universal.\n\n      switch (this.autoFocus) {\n        case false:\n        case 'dialog':\n          return;\n\n        case true:\n        case 'first-tabbable':\n          this._focusTrap.focusInitialElementWhenReady().then(hasMovedFocus => {\n            if (!hasMovedFocus && typeof this._elementRef.nativeElement.focus === 'function') {\n              element.focus();\n            }\n          });\n\n          break;\n\n        case 'first-heading':\n          this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role=\"heading\"]');\n\n          break;\n\n        default:\n          this._focusByCssSelector(this.autoFocus);\n\n          break;\n      }\n    }\n    /**\n     * Restores focus to the element that was originally focused when the drawer opened.\n     * If no element was focused at that time, the focus will be restored to the drawer.\n     */\n\n\n    _restoreFocus(focusOrigin) {\n      if (this.autoFocus === 'dialog') {\n        return;\n      }\n\n      if (this._elementFocusedBeforeDrawerWasOpened) {\n        this._focusMonitor.focusVia(this._elementFocusedBeforeDrawerWasOpened, focusOrigin);\n      } else {\n        this._elementRef.nativeElement.blur();\n      }\n\n      this._elementFocusedBeforeDrawerWasOpened = null;\n    }\n    /** Whether focus is currently within the drawer. */\n\n\n    _isFocusWithinDrawer() {\n      const activeEl = this._doc.activeElement;\n      return !!activeEl && this._elementRef.nativeElement.contains(activeEl);\n    }\n\n    ngAfterViewInit() {\n      this._isAttached = true;\n      this._focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement);\n\n      this._updateFocusTrapState(); // Only update the DOM position when the sidenav is positioned at\n      // the end since we project the sidenav before the content by default.\n\n\n      if (this._position === 'end') {\n        this._updatePositionInParent('end');\n      }\n    }\n\n    ngAfterContentChecked() {\n      // Enable the animations after the lifecycle hooks have run, in order to avoid animating\n      // drawers that are open by default. When we're on the server, we shouldn't enable the\n      // animations, because we don't want the drawer to animate the first time the user sees\n      // the page.\n      if (this._platform.isBrowser) {\n        this._enableAnimations = true;\n      }\n    }\n\n    ngOnDestroy() {\n      if (this._focusTrap) {\n        this._focusTrap.destroy();\n      }\n\n      this._anchor?.remove();\n      this._anchor = null;\n\n      this._animationStarted.complete();\n\n      this._animationEnd.complete();\n\n      this._modeChanged.complete();\n\n      this._destroyed.next();\n\n      this._destroyed.complete();\n    }\n    /**\n     * Open the drawer.\n     * @param openedVia Whether the drawer was opened by a key press, mouse click or programmatically.\n     * Used for focus management after the sidenav is closed.\n     */\n\n\n    open(openedVia) {\n      return this.toggle(true, openedVia);\n    }\n    /** Close the drawer. */\n\n\n    close() {\n      return this.toggle(false);\n    }\n    /** Closes the drawer with context that the backdrop was clicked. */\n\n\n    _closeViaBackdropClick() {\n      // If the drawer is closed upon a backdrop click, we always want to restore focus. We\n      // don't need to check whether focus is currently in the drawer, as clicking on the\n      // backdrop causes blurs the active element.\n      return this._setOpen(\n      /* isOpen */\n      false,\n      /* restoreFocus */\n      true, 'mouse');\n    }\n    /**\n     * Toggle this drawer.\n     * @param isOpen Whether the drawer should be open.\n     * @param openedVia Whether the drawer was opened by a key press, mouse click or programmatically.\n     * Used for focus management after the sidenav is closed.\n     */\n\n\n    toggle(isOpen = !this.opened, openedVia) {\n      // If the focus is currently inside the drawer content and we are closing the drawer,\n      // restore the focus to the initially focused element (when the drawer opened).\n      if (isOpen && openedVia) {\n        this._openedVia = openedVia;\n      }\n\n      const result = this._setOpen(isOpen,\n      /* restoreFocus */\n      !isOpen && this._isFocusWithinDrawer(), this._openedVia || 'program');\n\n      if (!isOpen) {\n        this._openedVia = null;\n      }\n\n      return result;\n    }\n    /**\n     * Toggles the opened state of the drawer.\n     * @param isOpen Whether the drawer should open or close.\n     * @param restoreFocus Whether focus should be restored on close.\n     * @param focusOrigin Origin to use when restoring focus.\n     */\n\n\n    _setOpen(isOpen, restoreFocus, focusOrigin) {\n      this._opened = isOpen;\n\n      if (isOpen) {\n        this._animationState = this._enableAnimations ? 'open' : 'open-instant';\n      } else {\n        this._animationState = 'void';\n\n        if (restoreFocus) {\n          this._restoreFocus(focusOrigin);\n        }\n      }\n\n      this._updateFocusTrapState();\n\n      return new Promise(resolve => {\n        this.openedChange.pipe(take(1)).subscribe(open => resolve(open ? 'open' : 'close'));\n      });\n    }\n\n    _getWidth() {\n      return this._elementRef.nativeElement ? this._elementRef.nativeElement.offsetWidth || 0 : 0;\n    }\n    /** Updates the enabled state of the focus trap. */\n\n\n    _updateFocusTrapState() {\n      if (this._focusTrap) {\n        // The focus trap is only enabled when the drawer is open in any mode other than side.\n        this._focusTrap.enabled = this.opened && this.mode !== 'side';\n      }\n    }\n    /**\n     * Updates the position of the drawer in the DOM. We need to move the element around ourselves\n     * when it's in the `end` position so that it comes after the content and the visual order\n     * matches the tab order. We also need to be able to move it back to `start` if the sidenav\n     * started off as `end` and was changed to `start`.\n     */\n\n\n    _updatePositionInParent(newPosition) {\n      const element = this._elementRef.nativeElement;\n      const parent = element.parentNode;\n\n      if (newPosition === 'end') {\n        if (!this._anchor) {\n          this._anchor = this._doc.createComment('mat-drawer-anchor');\n          parent.insertBefore(this._anchor, element);\n        }\n\n        parent.appendChild(element);\n      } else if (this._anchor) {\n        this._anchor.parentNode.insertBefore(element, this._anchor);\n      }\n    }\n\n  }\n\n  MatDrawer.ɵfac = function MatDrawer_Factory(t) {\n    return new (t || MatDrawer)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.FocusTrapFactory), i0.ɵɵdirectiveInject(i2.FocusMonitor), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.InteractivityChecker), i0.ɵɵdirectiveInject(DOCUMENT, 8), i0.ɵɵdirectiveInject(MAT_DRAWER_CONTAINER, 8));\n  };\n\n  MatDrawer.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatDrawer,\n    selectors: [[\"mat-drawer\"]],\n    viewQuery: function MatDrawer_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c1, 5);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._content = _t.first);\n      }\n    },\n    hostAttrs: [\"tabIndex\", \"-1\", 1, \"mat-drawer\"],\n    hostVars: 12,\n    hostBindings: function MatDrawer_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵsyntheticHostListener(\"@transform.start\", function MatDrawer_animation_transform_start_HostBindingHandler($event) {\n          return ctx._animationStarted.next($event);\n        })(\"@transform.done\", function MatDrawer_animation_transform_done_HostBindingHandler($event) {\n          return ctx._animationEnd.next($event);\n        });\n      }\n\n      if (rf & 2) {\n        i0.ɵɵattribute(\"align\", null);\n        i0.ɵɵsyntheticHostProperty(\"@transform\", ctx._animationState);\n        i0.ɵɵclassProp(\"mat-drawer-end\", ctx.position === \"end\")(\"mat-drawer-over\", ctx.mode === \"over\")(\"mat-drawer-push\", ctx.mode === \"push\")(\"mat-drawer-side\", ctx.mode === \"side\")(\"mat-drawer-opened\", ctx.opened);\n      }\n    },\n    inputs: {\n      position: \"position\",\n      mode: \"mode\",\n      disableClose: \"disableClose\",\n      autoFocus: \"autoFocus\",\n      opened: \"opened\"\n    },\n    outputs: {\n      openedChange: \"openedChange\",\n      _openedStream: \"opened\",\n      openedStart: \"openedStart\",\n      _closedStream: \"closed\",\n      closedStart: \"closedStart\",\n      onPositionChanged: \"positionChanged\"\n    },\n    exportAs: [\"matDrawer\"],\n    ngContentSelectors: _c0,\n    decls: 3,\n    vars: 0,\n    consts: [[\"cdkScrollable\", \"\", 1, \"mat-drawer-inner-container\"], [\"content\", \"\"]],\n    template: function MatDrawer_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 0, 1);\n        i0.ɵɵprojection(2);\n        i0.ɵɵelementEnd();\n      }\n    },\n    dependencies: [i1.CdkScrollable],\n    encapsulation: 2,\n    data: {\n      animation: [matDrawerAnimations.transformDrawer]\n    },\n    changeDetection: 0\n  });\n  return MatDrawer;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * `<mat-drawer-container>` component.\n *\n * This is the parent component to one or two `<mat-drawer>`s that validates the state internally\n * and coordinates the backdrop and content styling.\n */\n\n\nlet MatDrawerContainer = /*#__PURE__*/(() => {\n  class MatDrawerContainer {\n    constructor(_dir, _element, _ngZone, _changeDetectorRef, viewportRuler, defaultAutosize = false, _animationMode) {\n      this._dir = _dir;\n      this._element = _element;\n      this._ngZone = _ngZone;\n      this._changeDetectorRef = _changeDetectorRef;\n      this._animationMode = _animationMode;\n      /** Drawers that belong to this container. */\n\n      this._drawers = new QueryList();\n      /** Event emitted when the drawer backdrop is clicked. */\n\n      this.backdropClick = new EventEmitter();\n      /** Emits when the component is destroyed. */\n\n      this._destroyed = new Subject();\n      /** Emits on every ngDoCheck. Used for debouncing reflows. */\n\n      this._doCheckSubject = new Subject();\n      /**\n       * Margins to be applied to the content. These are used to push / shrink the drawer content when a\n       * drawer is open. We use margin rather than transform even for push mode because transform breaks\n       * fixed position elements inside of the transformed element.\n       */\n\n      this._contentMargins = {\n        left: null,\n        right: null\n      };\n      this._contentMarginChanges = new Subject(); // If a `Dir` directive exists up the tree, listen direction changes\n      // and update the left/right properties to point to the proper start/end.\n\n      if (_dir) {\n        _dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => {\n          this._validateDrawers();\n\n          this.updateContentMargins();\n        });\n      } // Since the minimum width of the sidenav depends on the viewport width,\n      // we need to recompute the margins if the viewport changes.\n\n\n      viewportRuler.change().pipe(takeUntil(this._destroyed)).subscribe(() => this.updateContentMargins());\n      this._autosize = defaultAutosize;\n    }\n    /** The drawer child with the `start` position. */\n\n\n    get start() {\n      return this._start;\n    }\n    /** The drawer child with the `end` position. */\n\n\n    get end() {\n      return this._end;\n    }\n    /**\n     * Whether to automatically resize the container whenever\n     * the size of any of its drawers changes.\n     *\n     * **Use at your own risk!** Enabling this option can cause layout thrashing by measuring\n     * the drawers on every change detection cycle. Can be configured globally via the\n     * `MAT_DRAWER_DEFAULT_AUTOSIZE` token.\n     */\n\n\n    get autosize() {\n      return this._autosize;\n    }\n\n    set autosize(value) {\n      this._autosize = coerceBooleanProperty(value);\n    }\n    /**\n     * Whether the drawer container should have a backdrop while one of the sidenavs is open.\n     * If explicitly set to `true`, the backdrop will be enabled for drawers in the `side`\n     * mode as well.\n     */\n\n\n    get hasBackdrop() {\n      if (this._backdropOverride == null) {\n        return !this._start || this._start.mode !== 'side' || !this._end || this._end.mode !== 'side';\n      }\n\n      return this._backdropOverride;\n    }\n\n    set hasBackdrop(value) {\n      this._backdropOverride = value == null ? null : coerceBooleanProperty(value);\n    }\n    /** Reference to the CdkScrollable instance that wraps the scrollable content. */\n\n\n    get scrollable() {\n      return this._userContent || this._content;\n    }\n\n    ngAfterContentInit() {\n      this._allDrawers.changes.pipe(startWith(this._allDrawers), takeUntil(this._destroyed)).subscribe(drawer => {\n        this._drawers.reset(drawer.filter(item => !item._container || item._container === this));\n\n        this._drawers.notifyOnChanges();\n      });\n\n      this._drawers.changes.pipe(startWith(null)).subscribe(() => {\n        this._validateDrawers();\n\n        this._drawers.forEach(drawer => {\n          this._watchDrawerToggle(drawer);\n\n          this._watchDrawerPosition(drawer);\n\n          this._watchDrawerMode(drawer);\n        });\n\n        if (!this._drawers.length || this._isDrawerOpen(this._start) || this._isDrawerOpen(this._end)) {\n          this.updateContentMargins();\n        }\n\n        this._changeDetectorRef.markForCheck();\n      }); // Avoid hitting the NgZone through the debounce timeout.\n\n\n      this._ngZone.runOutsideAngular(() => {\n        this._doCheckSubject.pipe(debounceTime(10), // Arbitrary debounce time, less than a frame at 60fps\n        takeUntil(this._destroyed)).subscribe(() => this.updateContentMargins());\n      });\n    }\n\n    ngOnDestroy() {\n      this._contentMarginChanges.complete();\n\n      this._doCheckSubject.complete();\n\n      this._drawers.destroy();\n\n      this._destroyed.next();\n\n      this._destroyed.complete();\n    }\n    /** Calls `open` of both start and end drawers */\n\n\n    open() {\n      this._drawers.forEach(drawer => drawer.open());\n    }\n    /** Calls `close` of both start and end drawers */\n\n\n    close() {\n      this._drawers.forEach(drawer => drawer.close());\n    }\n    /**\n     * Recalculates and updates the inline styles for the content. Note that this should be used\n     * sparingly, because it causes a reflow.\n     */\n\n\n    updateContentMargins() {\n      // 1. For drawers in `over` mode, they don't affect the content.\n      // 2. For drawers in `side` mode they should shrink the content. We do this by adding to the\n      //    left margin (for left drawer) or right margin (for right the drawer).\n      // 3. For drawers in `push` mode the should shift the content without resizing it. We do this by\n      //    adding to the left or right margin and simultaneously subtracting the same amount of\n      //    margin from the other side.\n      let left = 0;\n      let right = 0;\n\n      if (this._left && this._left.opened) {\n        if (this._left.mode == 'side') {\n          left += this._left._getWidth();\n        } else if (this._left.mode == 'push') {\n          const width = this._left._getWidth();\n\n          left += width;\n          right -= width;\n        }\n      }\n\n      if (this._right && this._right.opened) {\n        if (this._right.mode == 'side') {\n          right += this._right._getWidth();\n        } else if (this._right.mode == 'push') {\n          const width = this._right._getWidth();\n\n          right += width;\n          left -= width;\n        }\n      } // If either `right` or `left` is zero, don't set a style to the element. This\n      // allows users to specify a custom size via CSS class in SSR scenarios where the\n      // measured widths will always be zero. Note that we reset to `null` here, rather\n      // than below, in order to ensure that the types in the `if` below are consistent.\n\n\n      left = left || null;\n      right = right || null;\n\n      if (left !== this._contentMargins.left || right !== this._contentMargins.right) {\n        this._contentMargins = {\n          left,\n          right\n        }; // Pull back into the NgZone since in some cases we could be outside. We need to be careful\n        // to do it only when something changed, otherwise we can end up hitting the zone too often.\n\n        this._ngZone.run(() => this._contentMarginChanges.next(this._contentMargins));\n      }\n    }\n\n    ngDoCheck() {\n      // If users opted into autosizing, do a check every change detection cycle.\n      if (this._autosize && this._isPushed()) {\n        // Run outside the NgZone, otherwise the debouncer will throw us into an infinite loop.\n        this._ngZone.runOutsideAngular(() => this._doCheckSubject.next());\n      }\n    }\n    /**\n     * Subscribes to drawer events in order to set a class on the main container element when the\n     * drawer is open and the backdrop is visible. This ensures any overflow on the container element\n     * is properly hidden.\n     */\n\n\n    _watchDrawerToggle(drawer) {\n      drawer._animationStarted.pipe(filter(event => event.fromState !== event.toState), takeUntil(this._drawers.changes)).subscribe(event => {\n        // Set the transition class on the container so that the animations occur. This should not\n        // be set initially because animations should only be triggered via a change in state.\n        if (event.toState !== 'open-instant' && this._animationMode !== 'NoopAnimations') {\n          this._element.nativeElement.classList.add('mat-drawer-transition');\n        }\n\n        this.updateContentMargins();\n\n        this._changeDetectorRef.markForCheck();\n      });\n\n      if (drawer.mode !== 'side') {\n        drawer.openedChange.pipe(takeUntil(this._drawers.changes)).subscribe(() => this._setContainerClass(drawer.opened));\n      }\n    }\n    /**\n     * Subscribes to drawer onPositionChanged event in order to\n     * re-validate drawers when the position changes.\n     */\n\n\n    _watchDrawerPosition(drawer) {\n      if (!drawer) {\n        return;\n      } // NOTE: We need to wait for the microtask queue to be empty before validating,\n      // since both drawers may be swapping positions at the same time.\n\n\n      drawer.onPositionChanged.pipe(takeUntil(this._drawers.changes)).subscribe(() => {\n        this._ngZone.onMicrotaskEmpty.pipe(take(1)).subscribe(() => {\n          this._validateDrawers();\n        });\n      });\n    }\n    /** Subscribes to changes in drawer mode so we can run change detection. */\n\n\n    _watchDrawerMode(drawer) {\n      if (drawer) {\n        drawer._modeChanged.pipe(takeUntil(merge(this._drawers.changes, this._destroyed))).subscribe(() => {\n          this.updateContentMargins();\n\n          this._changeDetectorRef.markForCheck();\n        });\n      }\n    }\n    /** Toggles the 'mat-drawer-opened' class on the main 'mat-drawer-container' element. */\n\n\n    _setContainerClass(isAdd) {\n      const classList = this._element.nativeElement.classList;\n      const className = 'mat-drawer-container-has-open';\n\n      if (isAdd) {\n        classList.add(className);\n      } else {\n        classList.remove(className);\n      }\n    }\n    /** Validate the state of the drawer children components. */\n\n\n    _validateDrawers() {\n      this._start = this._end = null; // Ensure that we have at most one start and one end drawer.\n\n      this._drawers.forEach(drawer => {\n        if (drawer.position == 'end') {\n          if (this._end != null && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwMatDuplicatedDrawerError('end');\n          }\n\n          this._end = drawer;\n        } else {\n          if (this._start != null && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwMatDuplicatedDrawerError('start');\n          }\n\n          this._start = drawer;\n        }\n      });\n\n      this._right = this._left = null; // Detect if we're LTR or RTL.\n\n      if (this._dir && this._dir.value === 'rtl') {\n        this._left = this._end;\n        this._right = this._start;\n      } else {\n        this._left = this._start;\n        this._right = this._end;\n      }\n    }\n    /** Whether the container is being pushed to the side by one of the drawers. */\n\n\n    _isPushed() {\n      return this._isDrawerOpen(this._start) && this._start.mode != 'over' || this._isDrawerOpen(this._end) && this._end.mode != 'over';\n    }\n\n    _onBackdropClicked() {\n      this.backdropClick.emit();\n\n      this._closeModalDrawersViaBackdrop();\n    }\n\n    _closeModalDrawersViaBackdrop() {\n      // Close all open drawers where closing is not disabled and the mode is not `side`.\n      [this._start, this._end].filter(drawer => drawer && !drawer.disableClose && this._canHaveBackdrop(drawer)).forEach(drawer => drawer._closeViaBackdropClick());\n    }\n\n    _isShowingBackdrop() {\n      return this._isDrawerOpen(this._start) && this._canHaveBackdrop(this._start) || this._isDrawerOpen(this._end) && this._canHaveBackdrop(this._end);\n    }\n\n    _canHaveBackdrop(drawer) {\n      return drawer.mode !== 'side' || !!this._backdropOverride;\n    }\n\n    _isDrawerOpen(drawer) {\n      return drawer != null && drawer.opened;\n    }\n\n  }\n\n  MatDrawerContainer.ɵfac = function MatDrawerContainer_Factory(t) {\n    return new (t || MatDrawerContainer)(i0.ɵɵdirectiveInject(i4.Directionality, 8), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.ViewportRuler), i0.ɵɵdirectiveInject(MAT_DRAWER_DEFAULT_AUTOSIZE), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n  };\n\n  MatDrawerContainer.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatDrawerContainer,\n    selectors: [[\"mat-drawer-container\"]],\n    contentQueries: function MatDrawerContainer_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatDrawerContent, 5);\n        i0.ɵɵcontentQuery(dirIndex, MatDrawer, 5);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._content = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allDrawers = _t);\n      }\n    },\n    viewQuery: function MatDrawerContainer_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(MatDrawerContent, 5);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._userContent = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-drawer-container\"],\n    hostVars: 2,\n    hostBindings: function MatDrawerContainer_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-drawer-container-explicit-backdrop\", ctx._backdropOverride);\n      }\n    },\n    inputs: {\n      autosize: \"autosize\",\n      hasBackdrop: \"hasBackdrop\"\n    },\n    outputs: {\n      backdropClick: \"backdropClick\"\n    },\n    exportAs: [\"matDrawerContainer\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_DRAWER_CONTAINER,\n      useExisting: MatDrawerContainer\n    }])],\n    ngContentSelectors: _c3,\n    decls: 4,\n    vars: 2,\n    consts: [[\"class\", \"mat-drawer-backdrop\", 3, \"mat-drawer-shown\", \"click\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"mat-drawer-backdrop\", 3, \"click\"]],\n    template: function MatDrawerContainer_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c2);\n        i0.ɵɵtemplate(0, MatDrawerContainer_div_0_Template, 1, 2, \"div\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵprojection(2, 1);\n        i0.ɵɵtemplate(3, MatDrawerContainer_mat_drawer_content_3_Template, 2, 0, \"mat-drawer-content\", 1);\n      }\n\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.hasBackdrop);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", !ctx._content);\n      }\n    },\n    dependencies: [i5.NgIf, MatDrawerContent],\n    styles: [\".mat-drawer-container{position:relative;z-index:1;box-sizing:border-box;-webkit-overflow-scrolling:touch;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}.cdk-high-contrast-active .mat-drawer-backdrop{opacity:.5}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{position:relative;z-index:4;display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}.cdk-high-contrast-active .mat-drawer,.cdk-high-contrast-active [dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}.cdk-high-contrast-active [dir=rtl] .mat-drawer,.cdk-high-contrast-active .mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer{transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer[style*=\\\"visibility: hidden\\\"]{display:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto;-webkit-overflow-scrolling:touch}.mat-sidenav-fixed{position:fixed}\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return MatDrawerContainer;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nlet MatSidenavContent = /*#__PURE__*/(() => {\n  class MatSidenavContent extends MatDrawerContent {\n    constructor(changeDetectorRef, container, elementRef, scrollDispatcher, ngZone) {\n      super(changeDetectorRef, container, elementRef, scrollDispatcher, ngZone);\n    }\n\n  }\n\n  MatSidenavContent.ɵfac = function MatSidenavContent_Factory(t) {\n    return new (t || MatSidenavContent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(forwardRef(() => MatSidenavContainer)), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.ScrollDispatcher), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n\n  MatSidenavContent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatSidenavContent,\n    selectors: [[\"mat-sidenav-content\"]],\n    hostAttrs: [1, \"mat-drawer-content\", \"mat-sidenav-content\"],\n    hostVars: 4,\n    hostBindings: function MatSidenavContent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵstyleProp(\"margin-left\", ctx._container._contentMargins.left, \"px\")(\"margin-right\", ctx._container._contentMargins.right, \"px\");\n      }\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CdkScrollable,\n      useExisting: MatSidenavContent\n    }]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function MatSidenavContent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return MatSidenavContent;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet MatSidenav = /*#__PURE__*/(() => {\n  class MatSidenav extends MatDrawer {\n    constructor() {\n      super(...arguments);\n      this._fixedInViewport = false;\n      this._fixedTopGap = 0;\n      this._fixedBottomGap = 0;\n    }\n    /** Whether the sidenav is fixed in the viewport. */\n\n\n    get fixedInViewport() {\n      return this._fixedInViewport;\n    }\n\n    set fixedInViewport(value) {\n      this._fixedInViewport = coerceBooleanProperty(value);\n    }\n    /**\n     * The gap between the top of the sidenav and the top of the viewport when the sidenav is in fixed\n     * mode.\n     */\n\n\n    get fixedTopGap() {\n      return this._fixedTopGap;\n    }\n\n    set fixedTopGap(value) {\n      this._fixedTopGap = coerceNumberProperty(value);\n    }\n    /**\n     * The gap between the bottom of the sidenav and the bottom of the viewport when the sidenav is in\n     * fixed mode.\n     */\n\n\n    get fixedBottomGap() {\n      return this._fixedBottomGap;\n    }\n\n    set fixedBottomGap(value) {\n      this._fixedBottomGap = coerceNumberProperty(value);\n    }\n\n  }\n\n  MatSidenav.ɵfac = /* @__PURE__ */function () {\n    let ɵMatSidenav_BaseFactory;\n    return function MatSidenav_Factory(t) {\n      return (ɵMatSidenav_BaseFactory || (ɵMatSidenav_BaseFactory = i0.ɵɵgetInheritedFactory(MatSidenav)))(t || MatSidenav);\n    };\n  }();\n\n  MatSidenav.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatSidenav,\n    selectors: [[\"mat-sidenav\"]],\n    hostAttrs: [\"tabIndex\", \"-1\", 1, \"mat-drawer\", \"mat-sidenav\"],\n    hostVars: 17,\n    hostBindings: function MatSidenav_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"align\", null);\n        i0.ɵɵstyleProp(\"top\", ctx.fixedInViewport ? ctx.fixedTopGap : null, \"px\")(\"bottom\", ctx.fixedInViewport ? ctx.fixedBottomGap : null, \"px\");\n        i0.ɵɵclassProp(\"mat-drawer-end\", ctx.position === \"end\")(\"mat-drawer-over\", ctx.mode === \"over\")(\"mat-drawer-push\", ctx.mode === \"push\")(\"mat-drawer-side\", ctx.mode === \"side\")(\"mat-drawer-opened\", ctx.opened)(\"mat-sidenav-fixed\", ctx.fixedInViewport);\n      }\n    },\n    inputs: {\n      fixedInViewport: \"fixedInViewport\",\n      fixedTopGap: \"fixedTopGap\",\n      fixedBottomGap: \"fixedBottomGap\"\n    },\n    exportAs: [\"matSidenav\"],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c0,\n    decls: 3,\n    vars: 0,\n    consts: [[\"cdkScrollable\", \"\", 1, \"mat-drawer-inner-container\"], [\"content\", \"\"]],\n    template: function MatSidenav_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 0, 1);\n        i0.ɵɵprojection(2);\n        i0.ɵɵelementEnd();\n      }\n    },\n    dependencies: [i1.CdkScrollable],\n    encapsulation: 2,\n    data: {\n      animation: [matDrawerAnimations.transformDrawer]\n    },\n    changeDetection: 0\n  });\n  return MatSidenav;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet MatSidenavContainer = /*#__PURE__*/(() => {\n  class MatSidenavContainer extends MatDrawerContainer {}\n\n  MatSidenavContainer.ɵfac = /* @__PURE__ */function () {\n    let ɵMatSidenavContainer_BaseFactory;\n    return function MatSidenavContainer_Factory(t) {\n      return (ɵMatSidenavContainer_BaseFactory || (ɵMatSidenavContainer_BaseFactory = i0.ɵɵgetInheritedFactory(MatSidenavContainer)))(t || MatSidenavContainer);\n    };\n  }();\n\n  MatSidenavContainer.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatSidenavContainer,\n    selectors: [[\"mat-sidenav-container\"]],\n    contentQueries: function MatSidenavContainer_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatSidenavContent, 5);\n        i0.ɵɵcontentQuery(dirIndex, MatSidenav, 5);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._content = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allDrawers = _t);\n      }\n    },\n    hostAttrs: [1, \"mat-drawer-container\", \"mat-sidenav-container\"],\n    hostVars: 2,\n    hostBindings: function MatSidenavContainer_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-drawer-container-explicit-backdrop\", ctx._backdropOverride);\n      }\n    },\n    exportAs: [\"matSidenavContainer\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_DRAWER_CONTAINER,\n      useExisting: MatSidenavContainer\n    }]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c5,\n    decls: 4,\n    vars: 2,\n    consts: [[\"class\", \"mat-drawer-backdrop\", 3, \"mat-drawer-shown\", \"click\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"mat-drawer-backdrop\", 3, \"click\"]],\n    template: function MatSidenavContainer_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c4);\n        i0.ɵɵtemplate(0, MatSidenavContainer_div_0_Template, 1, 2, \"div\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵprojection(2, 1);\n        i0.ɵɵtemplate(3, MatSidenavContainer_mat_sidenav_content_3_Template, 2, 0, \"mat-sidenav-content\", 1);\n      }\n\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.hasBackdrop);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", !ctx._content);\n      }\n    },\n    dependencies: [i5.NgIf, MatSidenavContent],\n    styles: [_c6],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return MatSidenavContainer;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nlet MatSidenavModule = /*#__PURE__*/(() => {\n  class MatSidenavModule {}\n\n  MatSidenavModule.ɵfac = function MatSidenavModule_Factory(t) {\n    return new (t || MatSidenavModule)();\n  };\n\n  MatSidenavModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatSidenavModule\n  });\n  MatSidenavModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, MatCommonModule, CdkScrollableModule, CdkScrollableModule, MatCommonModule]\n  });\n  return MatSidenavModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { MAT_DRAWER_DEFAULT_AUTOSIZE, MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY, MatDrawer, MatDrawerContainer, MatDrawerContent, MatSidenav, MatSidenavContainer, MatSidenavContent, MatSidenavModule, matDrawerAnimations, throwMatDuplicatedDrawerError }; //# sourceMappingURL=sidenav.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}