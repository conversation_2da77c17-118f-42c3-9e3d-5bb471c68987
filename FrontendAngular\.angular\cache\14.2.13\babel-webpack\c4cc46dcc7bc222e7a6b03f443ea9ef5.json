{"ast": null, "code": "import _asyncToGenerator from \"R:/chateye/FrontendAngular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/api.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/material/card\";\nimport * as i5 from \"@angular/material/form-field\";\nimport * as i6 from \"@angular/material/input\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/chips\";\nimport * as i10 from \"@angular/material/tooltip\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nimport * as i12 from \"@angular/material/tabs\";\nimport * as i13 from \"@angular/material/select\";\nimport * as i14 from \"@angular/material/core\";\nimport * as i15 from \"@angular/material/expansion\";\n\nfunction AdminPanelComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"mat-card\", 17)(2, \"mat-card-header\")(3, \"mat-card-title\");\n    i0.ɵɵtext(4, \"Security Configuration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-card-subtitle\");\n    i0.ɵɵtext(6, \"Configure how users can access the chat\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"form\", 18)(9, \"mat-form-field\", 19)(10, \"mat-label\");\n    i0.ɵɵtext(11, \"Security Mode\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"mat-select\", 20);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminPanelComponent_div_28_Template_mat_select_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.securityConfig.securityMode = $event);\n    });\n    i0.ɵɵelementStart(13, \"mat-option\", 21);\n    i0.ɵɵtext(14, \"Open - Anyone can join\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"mat-option\", 22);\n    i0.ɵɵtext(16, \"Whitelist - Only approved users\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"mat-option\", 23);\n    i0.ɵɵtext(18, \"Invite - Users need invite codes\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"mat-hint\");\n    i0.ɵɵtext(20, \"Choose how users can access the chat\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"mat-form-field\", 19)(22, \"mat-label\");\n    i0.ɵɵtext(23, \"Admin Users\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"textarea\", 24);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminPanelComponent_div_28_Template_textarea_ngModelChange_24_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.adminUsersText = $event);\n    })(\"input\", function AdminPanelComponent_div_28_Template_textarea_input_24_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onAdminUsersChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"mat-hint\");\n    i0.ɵɵtext(26, \"Comma-separated list of admin usernames\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 25)(28, \"mat-expansion-panel\")(29, \"mat-expansion-panel-header\")(30, \"mat-panel-title\")(31, \"mat-icon\");\n    i0.ɵɵtext(32, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(33, \" Security Mode Information \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 26)(35, \"div\", 27)(36, \"h4\");\n    i0.ɵɵtext(37, \"Open Mode\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"p\");\n    i0.ɵɵtext(39, \"Anyone can join with just a username. No restrictions. Good for testing and development.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 27)(41, \"h4\");\n    i0.ɵɵtext(42, \"Whitelist Mode\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"p\");\n    i0.ɵɵtext(44, \"Only pre-approved users can join. Admin manages the allowed users list. Most secure for private groups.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"div\", 27)(46, \"h4\");\n    i0.ɵɵtext(47, \"Invite Mode\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"p\");\n    i0.ɵɵtext(49, \"Users need a valid invite code to join. Codes can have usage limits and expiration dates. Good for controlled growth.\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(50, \"div\", 28)(51, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_28_Template_button_click_51_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.updateSecurityConfig());\n    });\n    i0.ɵɵelementStart(52, \"mat-icon\");\n    i0.ɵɵtext(53, \"save\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(54, \" Update Security Configuration \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_28_Template_button_click_55_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.resetSecurityConfig());\n    });\n    i0.ɵɵelementStart(56, \"mat-icon\");\n    i0.ɵɵtext(57, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(58, \" Reset to Current \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(59, \"mat-card\", 31)(60, \"mat-card-header\")(61, \"mat-card-title\");\n    i0.ɵɵtext(62, \"Current Configuration\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(63, \"mat-card-content\")(64, \"div\", 32)(65, \"div\", 33)(66, \"span\", 34);\n    i0.ɵɵtext(67, \"Security Mode:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"span\", 35);\n    i0.ɵɵtext(69);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(70, \"div\", 33)(71, \"span\", 34);\n    i0.ɵɵtext(72, \"Admin Users:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(73, \"span\", 36);\n    i0.ɵɵtext(74);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(75, \"div\", 33)(76, \"span\", 34);\n    i0.ɵɵtext(77, \"Last Updated:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(78, \"span\", 36);\n    i0.ɵɵtext(79);\n    i0.ɵɵpipe(80, \"date\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.securityConfig.securityMode);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.adminUsersText);\n    i0.ɵɵadvance(27);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.loading || !ctx_r0.isSecurityConfigValid());\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.loading);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngClass\", \"mode-\" + ctx_r0.currentSecurityConfig.securityMode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getSecurityModeDisplay(ctx_r0.currentSecurityConfig.securityMode), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.currentSecurityConfig.adminUsers.join(\", \"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(80, 8, ctx_r0.currentSecurityConfig.lastUpdated, \"medium\"));\n  }\n}\n\nfunction AdminPanelComponent_div_29_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵelement(1, \"mat-spinner\", 46);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AdminPanelComponent_div_29_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"people_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No users in whitelist\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AdminPanelComponent_div_29_div_22_div_1_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_29_div_22_div_1_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const user_r16 = i0.ɵɵnextContext().$implicit;\n      const ctx_r18 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r18.removeUserFromWhitelist(user_r16.username));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"delete\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AdminPanelComponent_div_29_div_22_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 51)(2, \"span\", 52);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 53);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 54)(7, \"mat-chip\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, AdminPanelComponent_div_29_div_22_div_1_button_9_Template, 3, 0, \"button\", 55);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const user_r16 = ctx.$implicit;\n    const ctx_r15 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(user_r16.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" Added by \", user_r16.added_by, \" on \", ctx_r15.formatDate(user_r16.added_at), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(user_r16.is_active ? \"active-chip\" : \"inactive-chip\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", user_r16.is_active ? \"Active\" : \"Inactive\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", user_r16.is_active);\n  }\n}\n\nfunction AdminPanelComponent_div_29_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵtemplate(1, AdminPanelComponent_div_29_div_22_div_1_Template, 10, 7, \"div\", 49);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r14.allowedUsers);\n  }\n}\n\nfunction AdminPanelComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"mat-card\", 17)(2, \"mat-card-header\")(3, \"mat-card-title\");\n    i0.ɵɵtext(4, \"Add User to Whitelist\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"mat-card-content\")(6, \"div\", 37)(7, \"mat-form-field\", 38)(8, \"mat-label\");\n    i0.ɵɵtext(9, \"Username\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"input\", 39);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminPanelComponent_div_29_Template_input_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.newUsername = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_29_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.addUserToWhitelist());\n    });\n    i0.ɵɵelementStart(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(14, \" Add User \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(15, \"mat-card\", 41)(16, \"mat-card-header\")(17, \"mat-card-title\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"mat-card-content\");\n    i0.ɵɵtemplate(20, AdminPanelComponent_div_29_div_20_Template, 4, 0, \"div\", 42);\n    i0.ɵɵtemplate(21, AdminPanelComponent_div_29_div_21_Template, 5, 0, \"div\", 43);\n    i0.ɵɵtemplate(22, AdminPanelComponent_div_29_div_22_Template, 2, 1, \"div\", 44);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.newUsername);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.newUsername.trim());\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"Allowed Users (\", ctx_r1.allowedUsers.length, \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading && ctx_r1.allowedUsers.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading && ctx_r1.allowedUsers.length > 0);\n  }\n}\n\nfunction AdminPanelComponent_div_30_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵelement(1, \"mat-spinner\", 46);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AdminPanelComponent_div_30_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"vpn_key\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No invite codes\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AdminPanelComponent_div_30_div_30_div_1_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 73);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const code_r28 = i0.ɵɵnextContext().$implicit;\n    const ctx_r29 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" Expires: \", ctx_r29.formatDate(code_r28.expires_at), \" \");\n  }\n}\n\nfunction AdminPanelComponent_div_30_div_30_div_1_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 74);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_30_div_30_div_1_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const code_r28 = i0.ɵɵnextContext().$implicit;\n      const ctx_r32 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r32.deactivateInviteCode(code_r28.code));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"block\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AdminPanelComponent_div_30_div_30_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"div\", 66)(2, \"span\", 67);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 68);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, AdminPanelComponent_div_30_div_30_div_1_span_6_Template, 2, 1, \"span\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 70)(8, \"mat-chip\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_30_div_30_div_1_Template_button_click_10_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r36);\n      const code_r28 = restoredCtx.$implicit;\n      const ctx_r35 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r35.copyToClipboard(code_r28.code));\n    });\n    i0.ɵɵelementStart(11, \"mat-icon\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, AdminPanelComponent_div_30_div_30_div_1_button_13_Template, 3, 0, \"button\", 72);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const code_r28 = ctx.$implicit;\n    const ctx_r27 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(code_r28.code);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate4(\" Uses: \", code_r28.current_uses, \"/\", code_r28.max_uses, \" | Created by \", code_r28.created_by, \" on \", ctx_r27.formatDate(code_r28.created_at), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", code_r28.expires_at);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(code_r28.status === \"active\" ? \"active-chip\" : \"inactive-chip\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", code_r28.status, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r27.copiedCode === code_r28.code ? \"check\" : \"content_copy\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", code_r28.status === \"active\");\n  }\n}\n\nfunction AdminPanelComponent_div_30_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtemplate(1, AdminPanelComponent_div_30_div_30_div_1_Template, 14, 11, \"div\", 64);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r26 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r26.inviteCodes);\n  }\n}\n\nfunction AdminPanelComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r38 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"mat-card\", 17)(2, \"mat-card-header\")(3, \"mat-card-title\");\n    i0.ɵɵtext(4, \"Create Invite Code\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"mat-card-content\")(6, \"div\", 37)(7, \"mat-form-field\", 57)(8, \"mat-label\");\n    i0.ɵɵtext(9, \"Max Uses\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"input\", 58);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminPanelComponent_div_30_Template_input_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r38);\n      const ctx_r37 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r37.newInviteConfig.maxUses = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"mat-form-field\", 57)(12, \"mat-label\");\n    i0.ɵɵtext(13, \"Expires In (Hours)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"input\", 59);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminPanelComponent_div_30_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r38);\n      const ctx_r39 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r39.newInviteConfig.expiresInHours = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"mat-form-field\", 57)(16, \"mat-label\");\n    i0.ɵɵtext(17, \"Custom Code (Optional)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"input\", 60);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminPanelComponent_div_30_Template_input_ngModelChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r38);\n      const ctx_r40 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r40.newInviteConfig.customCode = $event);\n    })(\"input\", function AdminPanelComponent_div_30_Template_input_input_18_listener($event) {\n      i0.ɵɵrestoreView(_r38);\n      const ctx_r41 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r41.onCustomCodeInput($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_30_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r38);\n      const ctx_r42 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r42.createInviteCode());\n    });\n    i0.ɵɵelementStart(20, \"mat-icon\");\n    i0.ɵɵtext(21, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(22, \" Create Code \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(23, \"mat-card\", 41)(24, \"mat-card-header\")(25, \"mat-card-title\");\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"mat-card-content\");\n    i0.ɵɵtemplate(28, AdminPanelComponent_div_30_div_28_Template, 4, 0, \"div\", 42);\n    i0.ɵɵtemplate(29, AdminPanelComponent_div_30_div_29_Template, 5, 0, \"div\", 43);\n    i0.ɵɵtemplate(30, AdminPanelComponent_div_30_div_30_Template, 2, 1, \"div\", 62);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.newInviteConfig.maxUses);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.newInviteConfig.expiresInHours);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.newInviteConfig.customCode);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"Invite Codes (\", ctx_r2.inviteCodes.length, \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.loading && ctx_r2.inviteCodes.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.loading && ctx_r2.inviteCodes.length > 0);\n  }\n}\n\nfunction AdminPanelComponent_div_31_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵelement(1, \"mat-spinner\", 46);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AdminPanelComponent_div_31_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"group\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No groups\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AdminPanelComponent_div_31_div_26_div_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 88);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const group_r48 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(group_r48.description);\n  }\n}\n\nfunction AdminPanelComponent_div_31_div_26_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r52 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 81)(1, \"div\", 82);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_31_div_26_div_1_Template_div_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r52);\n      const group_r48 = restoredCtx.$implicit;\n      const ctx_r51 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r51.onGroupSelect(group_r48));\n    });\n    i0.ɵɵelementStart(2, \"span\", 83);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AdminPanelComponent_div_31_div_26_div_1_span_4_Template, 2, 1, \"span\", 84);\n    i0.ɵɵelementStart(5, \"span\", 85);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 86)(8, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_31_div_26_div_1_Template_button_click_8_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r52);\n      const group_r48 = restoredCtx.$implicit;\n      const ctx_r53 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r53.deleteGroup(group_r48.id));\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"delete\");\n    i0.ɵɵelementEnd()()()();\n  }\n\n  if (rf & 2) {\n    const group_r48 = ctx.$implicit;\n    const ctx_r47 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"selected\", (ctx_r47.selectedGroup == null ? null : ctx_r47.selectedGroup.id) === group_r48.id);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(group_r48.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", group_r48.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Created by \", group_r48.created_by_username || \"Unknown\", \" \");\n  }\n}\n\nfunction AdminPanelComponent_div_31_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 79);\n    i0.ɵɵtemplate(1, AdminPanelComponent_div_31_div_26_div_1_Template, 11, 5, \"div\", 80);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r45 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r45.groups);\n  }\n}\n\nfunction AdminPanelComponent_div_31_mat_card_27_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵelement(1, \"mat-spinner\", 46);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AdminPanelComponent_div_31_mat_card_27_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"person_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No users in this group\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AdminPanelComponent_div_31_mat_card_27_div_7_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r60 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 51)(2, \"span\", 52);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 53);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 54)(7, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_31_mat_card_27_div_7_div_1_Template_button_click_7_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r60);\n      const user_r58 = restoredCtx.$implicit;\n      const ctx_r59 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r59.revokeUserAccess(user_r58.username));\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"remove_circle\");\n    i0.ɵɵelementEnd()()()();\n  }\n\n  if (rf & 2) {\n    const user_r58 = ctx.$implicit;\n    const ctx_r57 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(user_r58.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Granted access on \", ctx_r57.formatDate(user_r58.granted_at), \" \");\n  }\n}\n\nfunction AdminPanelComponent_div_31_mat_card_27_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵtemplate(1, AdminPanelComponent_div_31_mat_card_27_div_7_div_1_Template, 10, 2, \"div\", 49);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r56 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r56.groupUsers);\n  }\n}\n\nfunction AdminPanelComponent_div_31_mat_card_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 41)(1, \"mat-card-header\")(2, \"mat-card-title\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"mat-card-content\");\n    i0.ɵɵtemplate(5, AdminPanelComponent_div_31_mat_card_27_div_5_Template, 4, 0, \"div\", 42);\n    i0.ɵɵtemplate(6, AdminPanelComponent_div_31_mat_card_27_div_6_Template, 5, 0, \"div\", 43);\n    i0.ɵɵtemplate(7, AdminPanelComponent_div_31_mat_card_27_div_7_Template, 2, 1, \"div\", 44);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r46 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Group Users - \", ctx_r46.selectedGroup.name, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r46.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r46.loading && ctx_r46.groupUsers.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r46.loading && ctx_r46.groupUsers.length > 0);\n  }\n}\n\nfunction AdminPanelComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r62 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"mat-card\", 17)(2, \"mat-card-header\")(3, \"mat-card-title\");\n    i0.ɵɵtext(4, \"Create Group\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"mat-card-content\")(6, \"div\", 37)(7, \"mat-form-field\", 57)(8, \"mat-label\");\n    i0.ɵɵtext(9, \"Group Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"input\", 75);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminPanelComponent_div_31_Template_input_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r62);\n      const ctx_r61 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r61.newGroupName = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"mat-form-field\", 57)(12, \"mat-label\");\n    i0.ɵɵtext(13, \"Description (Optional)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"input\", 76);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminPanelComponent_div_31_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r62);\n      const ctx_r63 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r63.newGroupDescription = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_31_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r62);\n      const ctx_r64 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r64.createGroup());\n    });\n    i0.ɵɵelementStart(16, \"mat-icon\");\n    i0.ɵɵtext(17, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(18, \" Create Group \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(19, \"mat-card\", 41)(20, \"mat-card-header\")(21, \"mat-card-title\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"mat-card-content\");\n    i0.ɵɵtemplate(24, AdminPanelComponent_div_31_div_24_Template, 4, 0, \"div\", 42);\n    i0.ɵɵtemplate(25, AdminPanelComponent_div_31_div_25_Template, 5, 0, \"div\", 43);\n    i0.ɵɵtemplate(26, AdminPanelComponent_div_31_div_26_Template, 2, 1, \"div\", 77);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(27, AdminPanelComponent_div_31_mat_card_27_Template, 8, 4, \"mat-card\", 78);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.newGroupName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.newGroupDescription);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", !ctx_r3.newGroupName.trim());\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"Groups (\", ctx_r3.groups.length, \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.loading && ctx_r3.groups.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.loading && ctx_r3.groups.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.selectedGroup);\n  }\n}\n\nfunction AdminPanelComponent_div_32_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵelement(1, \"mat-spinner\", 46);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AdminPanelComponent_div_32_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No users\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AdminPanelComponent_div_32_div_26_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r71 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 51)(2, \"span\", 52);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 53);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 54)(7, \"mat-chip\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_32_div_26_div_1_Template_button_click_9_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r71);\n      const user_r69 = restoredCtx.$implicit;\n      const ctx_r70 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r70.openUserModal(user_r69));\n    });\n    i0.ɵɵelementStart(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"settings\");\n    i0.ɵɵelementEnd()()()();\n  }\n\n  if (rf & 2) {\n    const user_r69 = ctx.$implicit;\n    const ctx_r68 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(user_r69.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" Last seen: \", ctx_r68.formatDate(user_r69.last_seen), \" | Status: \", user_r69.online_status ? \"Online\" : \"Offline\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(user_r69.hasPassword ? \"active-chip\" : \"inactive-chip\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", user_r69.hasPassword ? \"Has Password\" : \"No Password\", \" \");\n  }\n}\n\nfunction AdminPanelComponent_div_32_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵtemplate(1, AdminPanelComponent_div_32_div_26_div_1_Template, 12, 6, \"div\", 49);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r67 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r67.users);\n  }\n}\n\nfunction AdminPanelComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r73 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"mat-card\", 17)(2, \"mat-card-header\")(3, \"mat-card-title\");\n    i0.ɵɵtext(4, \"Create User\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"mat-card-content\")(6, \"div\", 37)(7, \"mat-form-field\", 57)(8, \"mat-label\");\n    i0.ɵɵtext(9, \"Username\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"input\", 90);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminPanelComponent_div_32_Template_input_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r73);\n      const ctx_r72 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r72.newUsername = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"mat-form-field\", 57)(12, \"mat-label\");\n    i0.ɵɵtext(13, \"Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"input\", 91);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminPanelComponent_div_32_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r73);\n      const ctx_r74 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r74.newUserPassword = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_32_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r73);\n      const ctx_r75 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r75.createUser());\n    });\n    i0.ɵɵelementStart(16, \"mat-icon\");\n    i0.ɵɵtext(17, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(18, \" Create User \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(19, \"mat-card\", 41)(20, \"mat-card-header\")(21, \"mat-card-title\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"mat-card-content\");\n    i0.ɵɵtemplate(24, AdminPanelComponent_div_32_div_24_Template, 4, 0, \"div\", 42);\n    i0.ɵɵtemplate(25, AdminPanelComponent_div_32_div_25_Template, 5, 0, \"div\", 43);\n    i0.ɵɵtemplate(26, AdminPanelComponent_div_32_div_26_Template, 2, 1, \"div\", 44);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.newUsername);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.newUserPassword);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", !ctx_r4.newUsername.trim() || !ctx_r4.newUserPassword.trim());\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"Users (\", ctx_r4.users.length, \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.loading && ctx_r4.users.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.loading && ctx_r4.users.length > 0);\n  }\n}\n\nfunction AdminPanelComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r77 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 93);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_33_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r77);\n      const ctx_r76 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r76.closeUserModal());\n    });\n    i0.ɵɵelementStart(1, \"mat-card\", 94);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_33_Template_mat_card_click_1_listener($event) {\n      return $event.stopPropagation();\n    });\n    i0.ɵɵelementStart(2, \"mat-card-header\", 95)(3, \"div\", 3)(4, \"mat-icon\", 96);\n    i0.ɵɵtext(5, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-card-title\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_33_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r77);\n      const ctx_r79 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r79.closeUserModal());\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"close\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"mat-card-content\", 97)(12, \"div\", 98)(13, \"div\", 99)(14, \"span\", 100);\n    i0.ɵɵtext(15, \"Username:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 101);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 99)(19, \"span\", 100);\n    i0.ɵɵtext(20, \"Status:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 101);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 99)(24, \"span\", 100);\n    i0.ɵɵtext(25, \"Last Seen:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 101);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 99)(29, \"span\", 100);\n    i0.ɵɵtext(30, \"Password Status:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"mat-chip\");\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"div\", 102)(34, \"mat-card\", 103)(35, \"mat-card-header\")(36, \"mat-card-title\");\n    i0.ɵɵtext(37, \"Set Password\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"mat-card-content\")(39, \"mat-form-field\", 19)(40, \"mat-label\");\n    i0.ɵɵtext(41, \"New Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"input\", 104);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminPanelComponent_div_33_Template_input_ngModelChange_42_listener($event) {\n      i0.ɵɵrestoreView(_r77);\n      const ctx_r80 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r80.newPassword = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"button\", 105);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_33_Template_button_click_43_listener() {\n      i0.ɵɵrestoreView(_r77);\n      const ctx_r81 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r81.setUserPassword());\n    });\n    i0.ɵɵelementStart(44, \"mat-icon\");\n    i0.ɵɵtext(45, \"lock\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(46, \" Set Password \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(47, \"mat-card\", 103)(48, \"mat-card-header\")(49, \"mat-card-title\");\n    i0.ɵɵtext(50, \"Reset Password\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(51, \"mat-card-content\")(52, \"mat-form-field\", 19)(53, \"mat-label\");\n    i0.ɵɵtext(54, \"New Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"input\", 104);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminPanelComponent_div_33_Template_input_ngModelChange_55_listener($event) {\n      i0.ɵɵrestoreView(_r77);\n      const ctx_r82 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r82.resetPassword = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(56, \"button\", 106);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_33_Template_button_click_56_listener() {\n      i0.ɵɵrestoreView(_r77);\n      const ctx_r83 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r83.resetUserPassword());\n    });\n    i0.ɵɵelementStart(57, \"mat-icon\");\n    i0.ɵɵtext(58, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(59, \" Reset Password \");\n    i0.ɵɵelementEnd()()()()()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"Manage User: \", ctx_r5.selectedUser == null ? null : ctx_r5.selectedUser.username, \"\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r5.selectedUser == null ? null : ctx_r5.selectedUser.username);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r5.selectedUser == null ? null : ctx_r5.selectedUser.online_status) ? \"Online\" : \"Offline\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r5.formatDate((ctx_r5.selectedUser == null ? null : ctx_r5.selectedUser.last_seen) || \"\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap((ctx_r5.selectedUser == null ? null : ctx_r5.selectedUser.hasPassword) ? \"active-chip\" : \"inactive-chip\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r5.selectedUser == null ? null : ctx_r5.selectedUser.hasPassword) ? \"Has Password\" : \"No Password\", \" \");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngModel\", ctx_r5.newPassword);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", !ctx_r5.newPassword.trim());\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngModel\", ctx_r5.resetPassword);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", !ctx_r5.resetPassword.trim());\n  }\n}\n\nexport class AdminPanelComponent {\n  constructor(apiService) {\n    this.apiService = apiService;\n    this.currentUser = null;\n    this.onClose = new EventEmitter();\n    this.activeTab = 'security';\n    this.activeTabIndex = 0;\n    this.allowedUsers = [];\n    this.inviteCodes = [];\n    this.groups = [];\n    this.selectedGroup = null;\n    this.groupUsers = [];\n    this.users = [];\n    this.newUsername = '';\n    this.newUserPassword = '';\n    this.newGroupName = '';\n    this.newGroupDescription = '';\n    this.newInviteConfig = {\n      maxUses: 1,\n      expiresInHours: 24,\n      customCode: ''\n    };\n    this.loading = false;\n    this.copiedCode = '';\n    this.showUserModal = false;\n    this.selectedUser = null;\n    this.newPassword = '';\n    this.resetPassword = ''; // Security configuration\n\n    this.securityConfig = {\n      securityMode: 'whitelist',\n      adminUsers: []\n    };\n    this.currentSecurityConfig = {\n      securityMode: 'whitelist',\n      adminUsers: [],\n      lastUpdated: new Date()\n    };\n    this.adminUsersText = '';\n  }\n\n  ngOnInit() {\n    this.loadData();\n  }\n\n  ngOnDestroy() {// Cleanup if needed\n  }\n\n  loadData() {\n    if (this.activeTab === 'security') {\n      this.fetchSecurityConfig();\n    } else if (this.activeTab === 'whitelist') {\n      this.fetchAllowedUsers();\n    } else if (this.activeTab === 'invites') {\n      this.fetchInviteCodes();\n    } else if (this.activeTab === 'groups') {\n      this.fetchGroups();\n    } else if (this.activeTab === 'users') {\n      this.fetchUsers();\n    }\n  }\n\n  onTabChange(tabIndex) {\n    const tabs = ['security', 'whitelist', 'invites', 'groups', 'users'];\n    this.activeTab = tabs[tabIndex];\n    this.activeTabIndex = tabIndex;\n    this.loadData();\n  }\n\n  onGroupSelect(group) {\n    this.selectedGroup = group;\n    this.fetchGroupUsers(group.id);\n  }\n\n  fetchAllowedUsers() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this.currentUser) return;\n\n      try {\n        _this.loading = true;\n        _this.allowedUsers = (yield _this.apiService.getAllowedUsers(_this.currentUser).toPromise()) || [];\n      } catch (error) {\n        console.error('Failed to fetch allowed users:', error);\n      } finally {\n        _this.loading = false;\n      }\n    })();\n  }\n\n  fetchInviteCodes() {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this2.currentUser) return;\n\n      try {\n        _this2.loading = true;\n        _this2.inviteCodes = (yield _this2.apiService.getInviteCodes(_this2.currentUser).toPromise()) || [];\n      } catch (error) {\n        console.error('Failed to fetch invite codes:', error);\n      } finally {\n        _this2.loading = false;\n      }\n    })();\n  }\n\n  fetchGroups() {\n    var _this3 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this3.currentUser) return;\n\n      try {\n        _this3.loading = true;\n        _this3.groups = (yield _this3.apiService.getGroups(_this3.currentUser).toPromise()) || [];\n      } catch (error) {\n        console.error('Failed to fetch groups:', error);\n      } finally {\n        _this3.loading = false;\n      }\n    })();\n  }\n\n  fetchGroupUsers(groupId) {\n    var _this4 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this4.currentUser) return;\n\n      try {\n        _this4.groupUsers = (yield _this4.apiService.getGroupUsers(_this4.currentUser, groupId).toPromise()) || [];\n      } catch (error) {\n        console.error('Failed to fetch group users:', error);\n      }\n    })();\n  }\n\n  addUserToWhitelist() {\n    var _this5 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this5.newUsername.trim() || !_this5.currentUser) return;\n\n      try {\n        yield _this5.apiService.addAllowedUser(_this5.currentUser, _this5.newUsername.trim()).toPromise();\n        _this5.newUsername = '';\n\n        _this5.fetchAllowedUsers();\n      } catch (error) {\n        alert(error.error?.error || error.message || 'Failed to add user');\n      }\n    })();\n  }\n\n  removeUserFromWhitelist(username) {\n    var _this6 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this6.currentUser) return;\n\n      try {\n        yield _this6.apiService.removeAllowedUser(_this6.currentUser, username).toPromise();\n\n        _this6.fetchAllowedUsers();\n      } catch (error) {\n        alert(error.error?.error || error.message || 'Failed to remove user');\n      }\n    })();\n  }\n\n  createInviteCode() {\n    var _this7 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this7.currentUser) return;\n\n      try {\n        const result = yield _this7.apiService.createInviteCode(_this7.currentUser, _this7.newInviteConfig).toPromise();\n        _this7.newInviteConfig = {\n          maxUses: 1,\n          expiresInHours: 24,\n          customCode: ''\n        };\n\n        _this7.fetchInviteCodes(); // Auto-copy the new code\n\n\n        if (result?.code) {\n          navigator.clipboard.writeText(result.code);\n          _this7.copiedCode = result.code;\n          setTimeout(() => _this7.copiedCode = '', 3000);\n        }\n      } catch (error) {\n        alert(error.error?.error || error.message || 'Failed to create invite code');\n      }\n    })();\n  }\n\n  deactivateInviteCode(code) {\n    var _this8 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this8.currentUser) return;\n\n      try {\n        yield _this8.apiService.deactivateInviteCode(_this8.currentUser, code).toPromise();\n\n        _this8.fetchInviteCodes();\n      } catch (error) {\n        alert(error.error?.error || error.message || 'Failed to deactivate invite code');\n      }\n    })();\n  }\n\n  createGroup() {\n    var _this9 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this9.newGroupName.trim() || !_this9.currentUser) return;\n\n      try {\n        yield _this9.apiService.createGroup(_this9.currentUser, _this9.newGroupName.trim(), _this9.newGroupDescription.trim() || undefined).toPromise();\n        _this9.newGroupName = '';\n        _this9.newGroupDescription = '';\n\n        _this9.fetchGroups();\n      } catch (error) {\n        alert(error.error?.error || error.message || 'Failed to create group');\n      }\n    })();\n  }\n\n  deleteGroup(groupId) {\n    var _this0 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!confirm('Are you sure you want to delete this group?') || !_this0.currentUser) return;\n\n      try {\n        yield _this0.apiService.deleteGroup(_this0.currentUser, groupId).toPromise();\n\n        _this0.fetchGroups();\n\n        if (_this0.selectedGroup && _this0.selectedGroup.id === groupId) {\n          _this0.selectedGroup = null;\n        }\n      } catch (error) {\n        alert(error.error?.error || error.message || 'Failed to delete group');\n      }\n    })();\n  }\n\n  grantUserAccess(username) {\n    var _this1 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this1.selectedGroup || !_this1.currentUser) return;\n\n      try {\n        yield _this1.apiService.grantUserAccess(_this1.currentUser, _this1.selectedGroup.id, username).toPromise();\n\n        _this1.fetchGroupUsers(_this1.selectedGroup.id);\n      } catch (error) {\n        alert(error.error?.error || error.message || 'Failed to grant user access');\n      }\n    })();\n  }\n\n  revokeUserAccess(username) {\n    var _this10 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this10.selectedGroup || !_this10.currentUser) return;\n\n      try {\n        yield _this10.apiService.revokeUserAccess(_this10.currentUser, _this10.selectedGroup.id, username).toPromise();\n\n        _this10.fetchGroupUsers(_this10.selectedGroup.id);\n      } catch (error) {\n        alert(error.error?.error || error.message || 'Failed to revoke user access');\n      }\n    })();\n  }\n\n  copyToClipboard(text) {\n    navigator.clipboard.writeText(text);\n    this.copiedCode = text;\n    setTimeout(() => this.copiedCode = '', 3000);\n  }\n\n  formatDate(dateString) {\n    return new Date(dateString).toLocaleString();\n  }\n\n  onCloseClick() {\n    this.onClose.emit();\n  }\n\n  onCustomCodeInput(event) {\n    const target = event.target;\n    this.newInviteConfig.customCode = target.value.toUpperCase();\n  } // User management methods\n\n\n  fetchUsers() {\n    var _this11 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this11.currentUser) return;\n\n      try {\n        _this11.loading = true;\n        _this11.users = (yield _this11.apiService.getUsersWithPasswordStatus(_this11.currentUser).toPromise()) || [];\n      } catch (error) {\n        console.error('Failed to fetch users:', error);\n      } finally {\n        _this11.loading = false;\n      }\n    })();\n  }\n\n  createUser() {\n    var _this12 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this12.newUsername.trim() || !_this12.newUserPassword.trim() || !_this12.currentUser) return;\n\n      if (_this12.newUserPassword.length < 6) {\n        alert('Password must be at least 6 characters long');\n        return;\n      }\n\n      try {\n        yield _this12.apiService.createUser(_this12.currentUser, _this12.newUsername.trim(), _this12.newUserPassword.trim()).toPromise();\n        _this12.newUsername = '';\n        _this12.newUserPassword = '';\n\n        _this12.fetchUsers();\n      } catch (error) {\n        alert(error.error?.error || error.message || 'Failed to create user');\n      }\n    })();\n  }\n\n  openUserModal(user) {\n    this.selectedUser = user;\n    this.newPassword = '';\n    this.resetPassword = '';\n    this.showUserModal = true;\n  }\n\n  closeUserModal() {\n    this.showUserModal = false;\n    this.selectedUser = null;\n    this.newPassword = '';\n    this.resetPassword = '';\n  }\n\n  setUserPassword() {\n    var _this13 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this13.selectedUser || !_this13.newPassword.trim() || !_this13.currentUser) return;\n\n      if (_this13.newPassword.length < 6) {\n        alert('Password must be at least 6 characters long');\n        return;\n      }\n\n      try {\n        yield _this13.apiService.setUserPassword(_this13.currentUser, _this13.selectedUser.username, _this13.newPassword.trim()).toPromise();\n        alert('Password set successfully');\n\n        _this13.closeUserModal();\n\n        _this13.fetchUsers();\n      } catch (error) {\n        alert(error.error?.error || error.message || 'Failed to set password');\n      }\n    })();\n  }\n\n  resetUserPassword() {\n    var _this14 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this14.selectedUser || !_this14.resetPassword.trim() || !_this14.currentUser) return;\n\n      if (_this14.resetPassword.length < 6) {\n        alert('Password must be at least 6 characters long');\n        return;\n      }\n\n      try {\n        yield _this14.apiService.resetUserPassword(_this14.currentUser, _this14.selectedUser.username, _this14.resetPassword.trim()).toPromise();\n        alert('Password reset successfully');\n\n        _this14.closeUserModal();\n\n        _this14.fetchUsers();\n      } catch (error) {\n        alert(error.error?.error || error.message || 'Failed to reset password');\n      }\n    })();\n  } // Security Configuration Methods\n\n\n  fetchSecurityConfig() {\n    var _this15 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this15.currentUser) return;\n\n      try {\n        const config = yield _this15.apiService.getSecurityConfig(_this15.currentUser).toPromise();\n\n        if (config) {\n          _this15.currentSecurityConfig = {\n            securityMode: config.securityMode,\n            adminUsers: config.adminUsers,\n            lastUpdated: new Date(config.lastUpdated || Date.now())\n          }; // Initialize form with current values\n\n          _this15.securityConfig = {\n            securityMode: config.securityMode,\n            adminUsers: [...config.adminUsers]\n          };\n          _this15.adminUsersText = config.adminUsers.join(', ');\n        }\n      } catch (error) {\n        console.error('Failed to fetch security configuration:', error);\n      }\n    })();\n  }\n\n  updateSecurityConfig() {\n    var _this16 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this16.currentUser || !_this16.isSecurityConfigValid()) return;\n      _this16.loading = true;\n\n      try {\n        yield _this16.apiService.updateSecurityConfig(_this16.currentUser, _this16.securityConfig).toPromise();\n        alert('Security configuration updated successfully');\n        yield _this16.fetchSecurityConfig();\n      } catch (error) {\n        alert(error.error?.error || error.message || 'Failed to update security configuration');\n      } finally {\n        _this16.loading = false;\n      }\n    })();\n  }\n\n  resetSecurityConfig() {\n    this.securityConfig = {\n      securityMode: this.currentSecurityConfig.securityMode,\n      adminUsers: [...this.currentSecurityConfig.adminUsers]\n    };\n    this.adminUsersText = this.currentSecurityConfig.adminUsers.join(', ');\n  }\n\n  onAdminUsersChange(event) {\n    const target = event.target;\n    this.adminUsersText = target.value;\n    this.securityConfig.adminUsers = target.value.split(',').map(user => user.trim()).filter(user => user.length > 0);\n  }\n\n  isSecurityConfigValid() {\n    return this.securityConfig.securityMode.length > 0 && this.securityConfig.adminUsers.length > 0 && this.securityConfig.adminUsers.every(user => user.trim().length > 0);\n  }\n\n  getSecurityModeDisplay(mode) {\n    switch (mode) {\n      case 'open':\n        return 'Open - Anyone can join';\n\n      case 'whitelist':\n        return 'Whitelist - Only approved users';\n\n      case 'invite':\n        return 'Invite - Users need invite codes';\n\n      default:\n        return mode;\n    }\n  }\n\n}\n\nAdminPanelComponent.ɵfac = function AdminPanelComponent_Factory(t) {\n  return new (t || AdminPanelComponent)(i0.ɵɵdirectiveInject(i1.ApiService));\n};\n\nAdminPanelComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: AdminPanelComponent,\n  selectors: [[\"app-admin-panel\"]],\n  inputs: {\n    currentUser: \"currentUser\"\n  },\n  outputs: {\n    onClose: \"onClose\"\n  },\n  decls: 34,\n  vars: 7,\n  consts: [[1, \"admin-panel-overlay\", 3, \"click\"], [1, \"admin-panel-container\", 3, \"click\"], [1, \"admin-panel-header\"], [1, \"header-content\"], [1, \"admin-icon\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Close\", 1, \"close-button\", 3, \"click\"], [1, \"admin-panel-content\"], [3, \"selectedIndex\", \"selectedIndexChange\"], [\"label\", \"Security Config\"], [\"matPrefix\", \"\"], [\"label\", \"Allowed Users\"], [\"label\", \"Invite Codes\"], [\"label\", \"Groups\"], [\"label\", \"Users\"], [\"class\", \"tab-content\", 4, \"ngIf\"], [\"class\", \"modal-overlay\", 3, \"click\", 4, \"ngIf\"], [1, \"tab-content\"], [1, \"form-card\"], [1, \"security-form\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"name\", \"securityMode\", 3, \"ngModel\", \"ngModelChange\"], [\"value\", \"open\"], [\"value\", \"whitelist\"], [\"value\", \"invite\"], [\"matInput\", \"\", \"name\", \"adminUsers\", \"placeholder\", \"Enter admin usernames separated by commas\", \"rows\", \"3\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [1, \"security-info\"], [1, \"security-mode-info\"], [1, \"mode-description\"], [1, \"form-actions\"], [\"type\", \"button\", \"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"save-button\", 3, \"disabled\", \"click\"], [\"type\", \"button\", \"mat-stroked-button\", \"\", 1, \"reset-button\", 3, \"disabled\", \"click\"], [1, \"info-card\"], [1, \"config-display\"], [1, \"config-item\"], [1, \"config-label\"], [1, \"config-value\", 3, \"ngClass\"], [1, \"config-value\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"username-field\"], [\"matInput\", \"\", \"type\", \"text\", \"name\", \"newUsername\", \"placeholder\", \"Enter username\", 3, \"ngModel\", \"ngModelChange\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"add-button\", 3, \"disabled\", \"click\"], [1, \"list-card\"], [\"class\", \"loading-state\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"user-list\", 4, \"ngIf\"], [1, \"loading-state\"], [\"diameter\", \"40\"], [1, \"empty-state\"], [1, \"user-list\"], [\"class\", \"user-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"user-item\"], [1, \"user-info\"], [1, \"user-name\"], [1, \"user-details\"], [1, \"user-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Remove User\", \"class\", \"remove-button\", 3, \"click\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Remove User\", 1, \"remove-button\", 3, \"click\"], [\"appearance\", \"outline\", 1, \"field\"], [\"matInput\", \"\", \"type\", \"number\", \"min\", \"1\", \"placeholder\", \"1\", 3, \"ngModel\", \"ngModelChange\"], [\"matInput\", \"\", \"type\", \"number\", \"min\", \"1\", \"placeholder\", \"24\", 3, \"ngModel\", \"ngModelChange\"], [\"matInput\", \"\", \"type\", \"text\", \"placeholder\", \"Leave empty for auto-generated\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"add-button\", 3, \"click\"], [\"class\", \"invite-list\", 4, \"ngIf\"], [1, \"invite-list\"], [\"class\", \"invite-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"invite-item\"], [1, \"invite-info\"], [1, \"invite-code\"], [1, \"invite-details\"], [\"class\", \"invite-expiry\", 4, \"ngIf\"], [1, \"invite-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Copy Code\", 1, \"copy-button\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Deactivate\", \"class\", \"remove-button\", 3, \"click\", 4, \"ngIf\"], [1, \"invite-expiry\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Deactivate\", 1, \"remove-button\", 3, \"click\"], [\"matInput\", \"\", \"type\", \"text\", \"placeholder\", \"Enter group name\", 3, \"ngModel\", \"ngModelChange\"], [\"matInput\", \"\", \"type\", \"text\", \"placeholder\", \"Enter group description\", 3, \"ngModel\", \"ngModelChange\"], [\"class\", \"group-list\", 4, \"ngIf\"], [\"class\", \"list-card\", 4, \"ngIf\"], [1, \"group-list\"], [\"class\", \"group-item\", 3, \"selected\", 4, \"ngFor\", \"ngForOf\"], [1, \"group-item\"], [1, \"group-info\", 3, \"click\"], [1, \"group-name\"], [\"class\", \"group-description\", 4, \"ngIf\"], [1, \"group-details\"], [1, \"group-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Delete Group\", 1, \"remove-button\", 3, \"click\"], [1, \"group-description\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Revoke Access\", 1, \"remove-button\", 3, \"click\"], [\"matInput\", \"\", \"type\", \"text\", \"placeholder\", \"Enter username\", 3, \"ngModel\", \"ngModelChange\"], [\"matInput\", \"\", \"type\", \"password\", \"placeholder\", \"Enter password\", 3, \"ngModel\", \"ngModelChange\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Manage User\", 1, \"manage-button\", 3, \"click\"], [1, \"modal-overlay\", 3, \"click\"], [1, \"modal-container\", 3, \"click\"], [1, \"modal-header\"], [1, \"modal-icon\"], [1, \"modal-content\"], [1, \"user-info-section\"], [1, \"info-item\"], [1, \"info-label\"], [1, \"info-value\"], [1, \"password-section\"], [1, \"password-card\"], [\"matInput\", \"\", \"type\", \"password\", \"placeholder\", \"Enter new password\", 3, \"ngModel\", \"ngModelChange\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"full-width\", 3, \"disabled\", \"click\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", 1, \"full-width\", 3, \"disabled\", \"click\"]],\n  template: function AdminPanelComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵlistener(\"click\", function AdminPanelComponent_Template_div_click_0_listener() {\n        return ctx.onCloseClick();\n      });\n      i0.ɵɵelementStart(1, \"mat-card\", 1);\n      i0.ɵɵlistener(\"click\", function AdminPanelComponent_Template_mat_card_click_1_listener($event) {\n        return $event.stopPropagation();\n      });\n      i0.ɵɵelementStart(2, \"mat-card-header\", 2)(3, \"div\", 3)(4, \"mat-icon\", 4);\n      i0.ɵɵtext(5, \"admin_panel_settings\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(6, \"mat-card-title\");\n      i0.ɵɵtext(7, \"Admin Panel\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(8, \"button\", 5);\n      i0.ɵɵlistener(\"click\", function AdminPanelComponent_Template_button_click_8_listener() {\n        return ctx.onCloseClick();\n      });\n      i0.ɵɵelementStart(9, \"mat-icon\");\n      i0.ɵɵtext(10, \"close\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(11, \"mat-card-content\", 6)(12, \"mat-tab-group\", 7);\n      i0.ɵɵlistener(\"selectedIndexChange\", function AdminPanelComponent_Template_mat_tab_group_selectedIndexChange_12_listener($event) {\n        return ctx.activeTabIndex = $event;\n      })(\"selectedIndexChange\", function AdminPanelComponent_Template_mat_tab_group_selectedIndexChange_12_listener($event) {\n        return ctx.onTabChange($event);\n      });\n      i0.ɵɵelementStart(13, \"mat-tab\", 8)(14, \"mat-icon\", 9);\n      i0.ɵɵtext(15, \"security\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(16, \"mat-tab\", 10)(17, \"mat-icon\", 9);\n      i0.ɵɵtext(18, \"people\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(19, \"mat-tab\", 11)(20, \"mat-icon\", 9);\n      i0.ɵɵtext(21, \"vpn_key\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(22, \"mat-tab\", 12)(23, \"mat-icon\", 9);\n      i0.ɵɵtext(24, \"group\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(25, \"mat-tab\", 13)(26, \"mat-icon\", 9);\n      i0.ɵɵtext(27, \"person\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵtemplate(28, AdminPanelComponent_div_28_Template, 81, 11, \"div\", 14);\n      i0.ɵɵtemplate(29, AdminPanelComponent_div_29_Template, 23, 6, \"div\", 14);\n      i0.ɵɵtemplate(30, AdminPanelComponent_div_30_Template, 31, 7, \"div\", 14);\n      i0.ɵɵtemplate(31, AdminPanelComponent_div_31_Template, 28, 8, \"div\", 14);\n      i0.ɵɵtemplate(32, AdminPanelComponent_div_32_Template, 27, 7, \"div\", 14);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵtemplate(33, AdminPanelComponent_div_33_Template, 60, 11, \"div\", 15);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(12);\n      i0.ɵɵproperty(\"selectedIndex\", ctx.activeTabIndex);\n      i0.ɵɵadvance(16);\n      i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"security\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"whitelist\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"invites\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"groups\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"users\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showUserModal);\n    }\n  },\n  dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NumberValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.MinValidator, i3.NgModel, i3.NgForm, i4.MatCard, i4.MatCardHeader, i4.MatCardContent, i4.MatCardTitle, i4.MatCardSubtitle, i5.MatFormField, i5.MatHint, i5.MatLabel, i5.MatPrefix, i6.MatInput, i7.MatButton, i8.MatIcon, i9.MatChip, i10.MatTooltip, i11.MatProgressSpinner, i12.MatTabGroup, i12.MatTab, i13.MatSelect, i14.MatOption, i15.MatExpansionPanel, i15.MatExpansionPanelHeader, i15.MatExpansionPanelTitle, i2.DatePipe],\n  styles: [\".admin-panel-overlay[_ngcontent-%COMP%] {\\r\\n  position: fixed;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  right: 0;\\r\\n  bottom: 0;\\r\\n  background: rgba(0, 0, 0, 0.5);\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  z-index: 1000;\\r\\n}\\r\\n.admin-panel-container[_ngcontent-%COMP%] {\\r\\n  width: 90%;\\r\\n  max-width: 1200px;\\r\\n  max-height: 90vh;\\r\\n  overflow: hidden;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n}\\r\\n.admin-panel-header[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: space-between;\\r\\n  align-items: center;\\r\\n  padding: 16px;\\r\\n  border-bottom: 1px solid #e0e0e0;\\r\\n  background: #f8f9fa;\\r\\n}\\r\\n.header-content[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 12px;\\r\\n}\\r\\n.admin-icon[_ngcontent-%COMP%] {\\r\\n  color: #3f51b5;\\r\\n  font-size: 24px;\\r\\n  width: 24px;\\r\\n  height: 24px;\\r\\n}\\r\\n.close-button[_ngcontent-%COMP%] {\\r\\n  color: #666;\\r\\n  transition: all 0.2s ease;\\r\\n}\\r\\n.close-button[_ngcontent-%COMP%]:hover {\\r\\n  background: #e0e0e0;\\r\\n  color: #333;\\r\\n}\\r\\n.admin-panel-content[_ngcontent-%COMP%] {\\r\\n  flex: 1;\\r\\n  overflow-y: auto;\\r\\n  padding: 16px;\\r\\n}\\r\\n.tab-content[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 16px;\\r\\n}\\r\\n.form-card[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 16px;\\r\\n}\\r\\n.form-row[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: flex-end;\\r\\n  gap: 16px;\\r\\n}\\r\\n.username-field[_ngcontent-%COMP%] {\\r\\n  flex: 1;\\r\\n}\\r\\n.add-button[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 8px;\\r\\n}\\r\\n.list-card[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 16px;\\r\\n}\\r\\n.loading-state[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  padding: 32px;\\r\\n  color: #666;\\r\\n}\\r\\n.empty-state[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  padding: 32px;\\r\\n  color: #666;\\r\\n}\\r\\n.empty-state[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\r\\n  font-size: 48px;\\r\\n  width: 48px;\\r\\n  height: 48px;\\r\\n  margin-bottom: 16px;\\r\\n  opacity: 0.6;\\r\\n}\\r\\n.user-list[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 8px;\\r\\n}\\r\\n.user-item[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: space-between;\\r\\n  align-items: center;\\r\\n  padding: 12px;\\r\\n  border: 1px solid #e0e0e0;\\r\\n  border-radius: 8px;\\r\\n  transition: all 0.2s ease;\\r\\n}\\r\\n.user-item[_ngcontent-%COMP%]:hover {\\r\\n  background: #f8f9fa;\\r\\n  border-color: #3f51b5;\\r\\n}\\r\\n.user-info[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 4px;\\r\\n}\\r\\n.user-name[_ngcontent-%COMP%] {\\r\\n  font-weight: 500;\\r\\n  font-size: 0.9rem;\\r\\n  color: #333;\\r\\n}\\r\\n.user-details[_ngcontent-%COMP%] {\\r\\n  font-size: 0.8rem;\\r\\n  color: #666;\\r\\n}\\r\\n.user-actions[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 8px;\\r\\n}\\r\\n.active-chip[_ngcontent-%COMP%] {\\r\\n  background: #4caf50;\\r\\n  color: white;\\r\\n}\\r\\n.inactive-chip[_ngcontent-%COMP%] {\\r\\n  background: #f44336;\\r\\n  color: white;\\r\\n}\\r\\n.remove-button[_ngcontent-%COMP%] {\\r\\n  color: #f44336;\\r\\n}\\r\\n.remove-button[_ngcontent-%COMP%]:hover {\\r\\n  background: rgba(244, 67, 54, 0.1);\\r\\n}\\r\\n.copy-button[_ngcontent-%COMP%] {\\r\\n  color: #2196f3;\\r\\n}\\r\\n.copy-button[_ngcontent-%COMP%]:hover {\\r\\n  background: rgba(33, 150, 243, 0.1);\\r\\n}\\r\\n.manage-button[_ngcontent-%COMP%] {\\r\\n  color: #ff9800;\\r\\n}\\r\\n.manage-button[_ngcontent-%COMP%]:hover {\\r\\n  background: rgba(255, 152, 0, 0.1);\\r\\n}\\r\\n.field[_ngcontent-%COMP%] {\\r\\n  flex: 1;\\r\\n}\\r\\n\\r\\n.invite-list[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 8px;\\r\\n}\\r\\n.invite-item[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: space-between;\\r\\n  align-items: center;\\r\\n  padding: 12px;\\r\\n  border: 1px solid #e0e0e0;\\r\\n  border-radius: 8px;\\r\\n  transition: all 0.2s ease;\\r\\n}\\r\\n.invite-item[_ngcontent-%COMP%]:hover {\\r\\n  background: #f8f9fa;\\r\\n  border-color: #3f51b5;\\r\\n}\\r\\n.invite-info[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 4px;\\r\\n}\\r\\n.invite-code[_ngcontent-%COMP%] {\\r\\n  font-weight: 500;\\r\\n  font-size: 0.9rem;\\r\\n  color: #333;\\r\\n  font-family: 'Courier New', monospace;\\r\\n}\\r\\n.invite-details[_ngcontent-%COMP%] {\\r\\n  font-size: 0.8rem;\\r\\n  color: #666;\\r\\n}\\r\\n.invite-expiry[_ngcontent-%COMP%] {\\r\\n  font-size: 0.8rem;\\r\\n  color: #ff9800;\\r\\n}\\r\\n.invite-actions[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 8px;\\r\\n}\\r\\n\\r\\n.group-list[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 8px;\\r\\n}\\r\\n.group-item[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: space-between;\\r\\n  align-items: center;\\r\\n  padding: 12px;\\r\\n  border: 1px solid #e0e0e0;\\r\\n  border-radius: 8px;\\r\\n  transition: all 0.2s ease;\\r\\n  cursor: pointer;\\r\\n}\\r\\n.group-item[_ngcontent-%COMP%]:hover {\\r\\n  background: #f8f9fa;\\r\\n  border-color: #3f51b5;\\r\\n}\\r\\n.group-item.selected[_ngcontent-%COMP%] {\\r\\n  background: #e3f2fd;\\r\\n  border-color: #3f51b5;\\r\\n}\\r\\n.group-info[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 4px;\\r\\n  flex: 1;\\r\\n}\\r\\n.group-name[_ngcontent-%COMP%] {\\r\\n  font-weight: 500;\\r\\n  font-size: 0.9rem;\\r\\n  color: #333;\\r\\n}\\r\\n.group-description[_ngcontent-%COMP%] {\\r\\n  font-size: 0.8rem;\\r\\n  color: #666;\\r\\n  font-style: italic;\\r\\n}\\r\\n.group-details[_ngcontent-%COMP%] {\\r\\n  font-size: 0.8rem;\\r\\n  color: #666;\\r\\n}\\r\\n.group-actions[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 8px;\\r\\n}\\r\\n\\r\\n.modal-overlay[_ngcontent-%COMP%] {\\r\\n  position: fixed;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  right: 0;\\r\\n  bottom: 0;\\r\\n  background: rgba(0, 0, 0, 0.7);\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  z-index: 2000;\\r\\n}\\r\\n.modal-container[_ngcontent-%COMP%] {\\r\\n  width: 90%;\\r\\n  max-width: 600px;\\r\\n  max-height: 90vh;\\r\\n  overflow: hidden;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n}\\r\\n.modal-header[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: space-between;\\r\\n  align-items: center;\\r\\n  padding: 16px;\\r\\n  border-bottom: 1px solid #e0e0e0;\\r\\n  background: #f8f9fa;\\r\\n}\\r\\n.modal-icon[_ngcontent-%COMP%] {\\r\\n  color: #3f51b5;\\r\\n  font-size: 24px;\\r\\n  width: 24px;\\r\\n  height: 24px;\\r\\n}\\r\\n.modal-content[_ngcontent-%COMP%] {\\r\\n  flex: 1;\\r\\n  overflow-y: auto;\\r\\n  padding: 16px;\\r\\n}\\r\\n.user-info-section[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 24px;\\r\\n}\\r\\n.info-item[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: space-between;\\r\\n  align-items: center;\\r\\n  padding: 8px 0;\\r\\n  border-bottom: 1px solid #f0f0f0;\\r\\n}\\r\\n.info-item[_ngcontent-%COMP%]:last-child {\\r\\n  border-bottom: none;\\r\\n}\\r\\n.info-label[_ngcontent-%COMP%] {\\r\\n  font-weight: 500;\\r\\n  color: #333;\\r\\n}\\r\\n.info-value[_ngcontent-%COMP%] {\\r\\n  color: #666;\\r\\n}\\r\\n.password-section[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 16px;\\r\\n}\\r\\n.password-card[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 16px;\\r\\n}\\r\\n.full-width[_ngcontent-%COMP%] {\\r\\n  width: 100%;\\r\\n}\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  .admin-panel-container[_ngcontent-%COMP%] {\\r\\n    width: 95%;\\r\\n    max-height: 95vh;\\r\\n  }\\r\\n  \\r\\n  .form-row[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n    align-items: stretch;\\r\\n  }\\r\\n  \\r\\n  .add-button[_ngcontent-%COMP%] {\\r\\n    margin-bottom: 0;\\r\\n  }\\r\\n  \\r\\n  .user-item[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n    align-items: flex-start;\\r\\n    gap: 8px;\\r\\n  }\\r\\n  \\r\\n  .user-actions[_ngcontent-%COMP%] {\\r\\n    width: 100%;\\r\\n    justify-content: space-between;\\r\\n  }\\r\\n}\\r\\n\\r\\n.security-form[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 16px;\\r\\n}\\r\\n.security-info[_ngcontent-%COMP%] {\\r\\n  margin: 16px 0;\\r\\n}\\r\\n.security-mode-info[_ngcontent-%COMP%] {\\r\\n  padding: 16px;\\r\\n}\\r\\n.mode-description[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 16px;\\r\\n}\\r\\n.mode-description[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\r\\n  margin: 0 0 8px 0;\\r\\n  color: #1976d2;\\r\\n  font-weight: 500;\\r\\n}\\r\\n.mode-description[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\r\\n  margin: 0;\\r\\n  color: #666;\\r\\n  line-height: 1.5;\\r\\n}\\r\\n.form-actions[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  gap: 12px;\\r\\n  margin-top: 16px;\\r\\n}\\r\\n.save-button[_ngcontent-%COMP%] {\\r\\n  flex: 1;\\r\\n}\\r\\n.reset-button[_ngcontent-%COMP%] {\\r\\n  flex: 0 0 auto;\\r\\n}\\r\\n.info-card[_ngcontent-%COMP%] {\\r\\n  margin-top: 16px;\\r\\n}\\r\\n.config-display[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 12px;\\r\\n}\\r\\n.config-item[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: space-between;\\r\\n  align-items: center;\\r\\n  padding: 8px 0;\\r\\n  border-bottom: 1px solid #eee;\\r\\n}\\r\\n.config-item[_ngcontent-%COMP%]:last-child {\\r\\n  border-bottom: none;\\r\\n}\\r\\n.config-label[_ngcontent-%COMP%] {\\r\\n  font-weight: 500;\\r\\n  color: #333;\\r\\n}\\r\\n.config-value[_ngcontent-%COMP%] {\\r\\n  color: #666;\\r\\n  font-family: 'Roboto Mono', monospace;\\r\\n  font-size: 0.9em;\\r\\n}\\r\\n.config-value.mode-open[_ngcontent-%COMP%] {\\r\\n  color: #4caf50;\\r\\n  font-weight: 500;\\r\\n}\\r\\n.config-value.mode-whitelist[_ngcontent-%COMP%] {\\r\\n  color: #ff9800;\\r\\n  font-weight: 500;\\r\\n}\\r\\n.config-value.mode-invite[_ngcontent-%COMP%] {\\r\\n  color: #2196f3;\\r\\n  font-weight: 500;\\r\\n}\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  .form-actions[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n  }\\r\\n  \\r\\n  .save-button[_ngcontent-%COMP%], .reset-button[_ngcontent-%COMP%] {\\r\\n    flex: 1;\\r\\n  }\\r\\n  \\r\\n  .config-item[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n    align-items: flex-start;\\r\\n    gap: 4px;\\r\\n  }\\r\\n  \\r\\n  .config-label[_ngcontent-%COMP%] {\\r\\n    font-size: 0.9em;\\r\\n  }\\r\\n  \\r\\n  .config-value[_ngcontent-%COMP%] {\\r\\n    font-size: 0.8em;\\r\\n  }\\r\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\"]\n});", "map": {"version": 3, "mappings": ";AAAA,SAAmCA,YAAnC,QAA0E,eAA1E;;;;;;;;;;;;;;;;;;;;;;ICwCMC,gCAA0D,CAA1D,EAA0D,UAA1D,EAA0D,EAA1D,EAA0D,CAA1D,EAA0D,iBAA1D,EAA0D,CAA1D,EAA0D,gBAA1D;IAGsBA;IAAsBA;IACtCA;IAAmBA;IAAuCA;IAE5DA,yCAAkB,CAAlB,EAAkB,MAAlB,EAAkB,EAAlB,EAAkB,CAAlB,EAAkB,gBAAlB,EAAkB,EAAlB,EAAkB,EAAlB,EAAkB,WAAlB;IAGiBA;IAAaA;IACxBA;IAAYA;MAAAA;MAAA;MAAA,OAAaA,2DAAb;IAAgD,CAAhD;IACVA;IAAyBA;IAAsBA;IAC/CA;IAA8BA;IAA+BA;IAC7DA;IAA2BA;IAAgCA;IAE7DA;IAAUA;IAAoCA;IAGhDA,4CAAwD,EAAxD,EAAwD,WAAxD;IACaA;IAAWA;IACtBA;IAEEA;MAAAA;MAAA;MAAA;IAAA,GAA4B,OAA5B,EAA4B;MAAAA;MAAA;MAAA,OAInBA,iDAJmB;IAIO,CAJnC;IAKDA;IACDA;IAAUA;IAAuCA;IAGnDA,iCAA2B,EAA3B,EAA2B,qBAA3B,EAA2B,EAA3B,EAA2B,4BAA3B,EAA2B,EAA3B,EAA2B,iBAA3B,EAA2B,EAA3B,EAA2B,UAA3B;IAIkBA;IAAIA;IACdA;IACFA;IAEFA,iCAAgC,EAAhC,EAAgC,KAAhC,EAAgC,EAAhC,EAAgC,EAAhC,EAAgC,IAAhC;IAEQA;IAASA;IACbA;IAAGA;IAAwFA;IAE7FA,iCAA8B,EAA9B,EAA8B,IAA9B;IACMA;IAAcA;IAClBA;IAAGA;IAAuGA;IAE5GA,iCAA8B,EAA9B,EAA8B,IAA9B;IACMA;IAAWA;IACfA;IAAGA;IAAqHA;IAMhIA,iCAA0B,EAA1B,EAA0B,QAA1B,EAA0B,EAA1B;IAGIA;MAAAA;MAAA;MAAA,OAASA,8CAAT;IAA+B,CAA/B;IAMAA;IAAUA;IAAIA;IACdA;IACFA;IACAA;IAEEA;MAAAA;MAAA;MAAA,OAASA,6CAAT;IAA8B,CAA9B;IAKAA;IAAUA;IAAOA;IACjBA;IACFA;IAORA,sCAA4B,EAA5B,EAA4B,iBAA5B,EAA4B,EAA5B,EAA4B,gBAA5B;IAEoBA;IAAqBA;IAEvCA,0CAAkB,EAAlB,EAAkB,KAAlB,EAAkB,EAAlB,EAAkB,EAAlB,EAAkB,KAAlB,EAAkB,EAAlB,EAAkB,EAAlB,EAAkB,MAAlB,EAAkB,EAAlB;IAGiCA;IAAcA;IACzCA;IACEA;IACFA;IAEFA,iCAAyB,EAAzB,EAAyB,MAAzB,EAAyB,EAAzB;IAC6BA;IAAYA;IACvCA;IAA2BA;IAAiDA;IAE9EA,iCAAyB,EAAzB,EAAyB,MAAzB,EAAyB,EAAzB;IAC6BA;IAAaA;IACxCA;IAA2BA;;IAAuDA;;;;;IA5FtEA;IAAAA;IAYVA;IAAAA;IAsCAA;IAAAA;IAWAA;IAAAA;IAqByBA;IAAAA;IACzBA;IAAAA;IAKyBA;IAAAA;IAIAA;IAAAA;;;;;;IA8C/BA;IACEA;IACAA;IAAGA;IAAUA;;;;;;IAEfA,gCAAuE,CAAvE,EAAuE,UAAvE;IACYA;IAAcA;IACxBA;IAAGA;IAAqBA;;;;;;;;IAcpBA;IAEEA;MAAAA;MAAA;MAAA;MAAA,OAASA,kEAAT;IAA+C,CAA/C;IAKAA;IAAUA;IAAMA;;;;;;IAlBtBA,gCAAyD,CAAzD,EAAyD,KAAzD,EAAyD,EAAzD,EAAyD,CAAzD,EAAyD,MAAzD,EAAyD,EAAzD;IAE4BA;IAAmBA;IAC3CA;IACEA;IACFA;IAEFA,gCAA0B,CAA1B,EAA0B,UAA1B;IAEIA;IACFA;IACAA;IASFA;;;;;;IAlB0BA;IAAAA;IAEtBA;IAAAA;IAIQA;IAAAA;IACRA;IAAAA;IAGCA;IAAAA;;;;;;IAbTA;IACEA;IAsBFA;;;;;IAtBwBA;IAAAA;;;;;;;;IA/C9BA,gCAA2D,CAA3D,EAA2D,UAA3D,EAA2D,EAA3D,EAA2D,CAA3D,EAA2D,iBAA3D,EAA2D,CAA3D,EAA2D,gBAA3D;IAIsBA;IAAqBA;IAEvCA,yCAAkB,CAAlB,EAAkB,KAAlB,EAAkB,EAAlB,EAAkB,CAAlB,EAAkB,gBAAlB,EAAkB,EAAlB,EAAkB,CAAlB,EAAkB,WAAlB;IAGiBA;IAAQA;IACnBA;IAIEA;MAAAA;MAAA;MAAA;IAAA;IAJFA;IAQFA;IACEA;MAAAA;MAAA;MAAA,OAASA,4CAAT;IAA6B,CAA7B;IAMAA;IAAUA;IAAGA;IACbA;IACFA;IAMNA,sCAA4B,EAA5B,EAA4B,iBAA5B,EAA4B,EAA5B,EAA4B,gBAA5B;IAEoBA;IAAyCA;IAE3DA;IACEA;IAIAA;IAIAA;IAwBFA;;;;;IAxDQA;IAAAA;IAMFA;IAAAA;IAeYA;IAAAA;IAGVA;IAAAA;IAIAA;IAAAA;IAIAA;IAAAA;;;;;;IAsFNA;IACEA;IACAA;IAAGA;IAAUA;;;;;;IAEfA,gCAAsE,CAAtE,EAAsE,UAAtE;IACYA;IAAOA;IACjBA;IAAGA;IAAeA;;;;;;IAUdA;IACEA;IACFA;;;;;;IADEA;IAAAA;;;;;;;;IAeFA;IAEEA;MAAAA;MAAA;MAAA;MAAA,OAASA,2DAAT;IAAwC,CAAxC;IAKAA;IAAUA;IAAKA;;;;;;;;IA9BrBA,gCAA0D,CAA1D,EAA0D,KAA1D,EAA0D,EAA1D,EAA0D,CAA1D,EAA0D,MAA1D,EAA0D,EAA1D;IAE8BA;IAAeA;IACzCA;IACEA;IAEFA;IACAA;IAGFA;IACAA,gCAA4B,CAA5B,EAA4B,UAA5B;IAEIA;IACFA;IACAA;IACEA;MAAA;MAAA;MAAA;MAAA,OAASA,sDAAT;IAAmC,CAAnC;IAKAA;IAAUA;IAAyDA;IAErEA;IASFA;;;;;;IA9B4BA;IAAAA;IAExBA;IAAAA;IAGKA;IAAAA;IAKGA;IAAAA;IACRA;IAAAA;IAQUA;IAAAA;IAGTA;IAAAA;;;;;;IAzBTA;IACEA;IAkCFA;;;;;IAlCwBA;IAAAA;;;;;;;;IAlE9BA,gCAAyD,CAAzD,EAAyD,UAAzD,EAAyD,EAAzD,EAAyD,CAAzD,EAAyD,iBAAzD,EAAyD,CAAzD,EAAyD,gBAAzD;IAIsBA;IAAkBA;IAEpCA,yCAAkB,CAAlB,EAAkB,KAAlB,EAAkB,EAAlB,EAAkB,CAAlB,EAAkB,gBAAlB,EAAkB,EAAlB,EAAkB,CAAlB,EAAkB,WAAlB;IAGiBA;IAAQA;IACnBA;IAGEA;MAAAA;MAAA;MAAA,OAAaA,wDAAb;IACb,CADa;IAHFA;IAQFA,4CAAmD,EAAnD,EAAmD,WAAnD;IACaA;IAAkBA;IAC7BA;IAGEA;MAAAA;MAAA;MAAA,OAAaA,+DAAb;IACb,CADa;IAHFA;IAQFA,4CAAmD,EAAnD,EAAmD,WAAnD;IACaA;IAAsBA;IACjCA;IAGEA;MAAAA;MAAA;MAAA,OAAaA,2DAAb;IACb,CADa,EAAwC,OAAxC,EAAwC;MAAAA;MAAA;MAAA,OAC/BA,iDAD+B;IACN,CADlC;IAHFA;IAQFA;IACEA;MAAAA;MAAA;MAAA,OAASA,0CAAT;IAA2B,CAA3B;IAKAA;IAAUA;IAAGA;IACbA;IACFA;IAMNA,sCAA4B,EAA5B,EAA4B,iBAA5B,EAA4B,EAA5B,EAA4B,gBAA5B;IAEoBA;IAAuCA;IAEzDA;IACEA;IAIAA;IAIAA;IAoCFA;;;;;IAxFQA;IAAAA;IAUAA;IAAAA;IAUAA;IAAAA;IAqBUA;IAAAA;IAGVA;IAAAA;IAIAA;IAAAA;IAIAA;IAAAA;;;;;;IAuFNA;IACEA;IACAA;IAAGA;IAAUA;;;;;;IAEfA,gCAAiE,CAAjE,EAAiE,UAAjE;IACYA;IAAKA;IACfA;IAAGA;IAASA;;;;;;IAMRA;IAA0DA;IAAuBA;;;;;IAAvBA;IAAAA;;;;;;;;IAH9DA,gCAAuG,CAAvG,EAAuG,KAAvG,EAAuG,EAAvG;IAC0BA;MAAA;MAAA;MAAA;MAAA,OAASA,gDAAT;IAA6B,CAA7B;IACtBA;IAAyBA;IAAgBA;IACzCA;IACAA;IACEA;IACFA;IAEFA,gCAA2B,CAA3B,EAA2B,QAA3B,EAA2B,EAA3B;IAEIA;MAAA;MAAA;MAAA;MAAA,OAASA,iDAAT;IAA8B,CAA9B;IAKAA;IAAUA;IAAMA;;;;;;IAf+BA;IAExBA;IAAAA;IAClBA;IAAAA;IAELA;IAAAA;;;;;;IANRA;IACEA;IAmBFA;;;;;IAnByBA;IAAAA;;;;;;IA6BzBA;IACEA;IACAA;IAAGA;IAAUA;;;;;;IAEfA,gCAAqE,CAArE,EAAqE,UAArE;IACYA;IAAcA;IACxBA;IAAGA;IAAsBA;;;;;;;;IAGzBA,gCAAuD,CAAvD,EAAuD,KAAvD,EAAuD,EAAvD,EAAuD,CAAvD,EAAuD,MAAvD,EAAuD,EAAvD;IAE4BA;IAAmBA;IAC3CA;IACEA;IACFA;IAEFA,gCAA0B,CAA1B,EAA0B,QAA1B,EAA0B,EAA1B;IAEIA;MAAA;MAAA;MAAA;MAAA,OAASA,2DAAT;IAAwC,CAAxC;IAKAA;IAAUA;IAAaA;;;;;;IAZDA;IAAAA;IAEtBA;IAAAA;;;;;;IALRA;IACEA;IAkBFA;;;;;IAlBwBA;IAAAA;;;;;;IAd5BA,qCAAkD,CAAlD,EAAkD,iBAAlD,EAAkD,CAAlD,EAAkD,gBAAlD;IAEoBA;IAAsCA;IAExDA;IACEA;IAIAA;IAIAA;IAoBFA;;;;;IA/BkBA;IAAAA;IAGVA;IAAAA;IAIAA;IAAAA;IAIAA;IAAAA;;;;;;;;IA5FZA,gCAAwD,CAAxD,EAAwD,UAAxD,EAAwD,EAAxD,EAAwD,CAAxD,EAAwD,iBAAxD,EAAwD,CAAxD,EAAwD,gBAAxD;IAIsBA;IAAYA;IAE9BA,yCAAkB,CAAlB,EAAkB,KAAlB,EAAkB,EAAlB,EAAkB,CAAlB,EAAkB,gBAAlB,EAAkB,EAAlB,EAAkB,CAAlB,EAAkB,WAAlB;IAGiBA;IAAUA;IACrBA;IAGEA;MAAAA;MAAA;MAAA;IAAA;IAHFA;IAOFA,4CAAmD,EAAnD,EAAmD,WAAnD;IACaA;IAAsBA;IACjCA;IAGEA;MAAAA;MAAA;MAAA;IAAA;IAHFA;IAOFA;IACEA;MAAAA;MAAA;MAAA,OAASA,qCAAT;IAAsB,CAAtB;IAMAA;IAAUA;IAAGA;IACbA;IACFA;IAMNA,sCAA4B,EAA5B,EAA4B,iBAA5B,EAA4B,EAA5B,EAA4B,gBAA5B;IAEoBA;IAA4BA;IAE9CA;IACEA;IAIAA;IAIAA;IAqBFA;IAIFA;IAmCFA;;;;;IArGYA;IAAAA;IASAA;IAAAA;IAMFA;IAAAA;IAeYA;IAAAA;IAGVA;IAAAA;IAIAA;IAAAA;IAIAA;IAAAA;IAyBCA;IAAAA;;;;;;IAoFPA;IACEA;IACAA;IAAGA;IAAUA;;;;;;IAEfA,gCAAgE,CAAhE,EAAgE,UAAhE;IACYA;IAAMA;IAChBA;IAAGA;IAAQA;;;;;;;;IAGXA,gCAAkD,CAAlD,EAAkD,KAAlD,EAAkD,EAAlD,EAAkD,CAAlD,EAAkD,MAAlD,EAAkD,EAAlD;IAE4BA;IAAmBA;IAC3CA;IACEA;IAEFA;IAEFA,gCAA0B,CAA1B,EAA0B,UAA1B;IAEIA;IACFA;IACAA;IACEA;MAAA;MAAA;MAAA;MAAA,OAASA,+CAAT;IAA4B,CAA5B;IAKAA;IAAUA;IAAQA;;;;;;IAhBIA;IAAAA;IAEtBA;IAAAA;IAKQA;IAAAA;IACRA;IAAAA;;;;;;IAXRA;IACEA;IAsBFA;;;;;IAtBwBA;IAAAA;;;;;;;;IAvD9BA,gCAAuD,CAAvD,EAAuD,UAAvD,EAAuD,EAAvD,EAAuD,CAAvD,EAAuD,iBAAvD,EAAuD,CAAvD,EAAuD,gBAAvD;IAIsBA;IAAWA;IAE7BA,yCAAkB,CAAlB,EAAkB,KAAlB,EAAkB,EAAlB,EAAkB,CAAlB,EAAkB,gBAAlB,EAAkB,EAAlB,EAAkB,CAAlB,EAAkB,WAAlB;IAGiBA;IAAQA;IACnBA;IAGEA;MAAAA;MAAA;MAAA;IAAA;IAHFA;IAOFA,4CAAmD,EAAnD,EAAmD,WAAnD;IACaA;IAAQA;IACnBA;IAGEA;MAAAA;MAAA;MAAA;IAAA;IAHFA;IAOFA;IACEA;MAAAA;MAAA;MAAA,OAASA,oCAAT;IAAqB,CAArB;IAMAA;IAAUA;IAAGA;IACbA;IACFA;IAMNA,sCAA4B,EAA5B,EAA4B,iBAA5B,EAA4B,EAA5B,EAA4B,gBAA5B;IAEoBA;IAA0BA;IAE5CA;IACEA;IAIAA;IAIAA;IAwBFA;;;;;IAjEQA;IAAAA;IASAA;IAAAA;IAMFA;IAAAA;IAeYA;IAAAA;IAGVA;IAAAA;IAIAA;IAAAA;IAIAA;IAAAA;;;;;;;;IAgClBA;IAAiDA;MAAAA;MAAA;MAAA,OAASA,wCAAT;IAAyB,CAAzB;IAC/CA;IAAkCA;MAAA,OAASC,wBAAT;IAAiC,CAAjC;IAChCD,4CAAsC,CAAtC,EAAsC,KAAtC,EAAsC,CAAtC,EAAsC,CAAtC,EAAsC,UAAtC,EAAsC,EAAtC;IAEiCA;IAAMA;IACnCA;IAAgBA;IAAyCA;IAE3DA;IACEA;MAAAA;MAAA;MAAA,OAASA,wCAAT;IAAyB,CAAzB;IAKAA;IAAUA;IAAKA;IAInBA,8CAAwC,EAAxC,EAAwC,KAAxC,EAAwC,EAAxC,EAAwC,EAAxC,EAAwC,KAAxC,EAAwC,EAAxC,EAAwC,EAAxC,EAAwC,MAAxC,EAAwC,GAAxC;IAG+BA;IAASA;IAClCA;IAAyBA;IAA4BA;IAEvDA,iCAAuB,EAAvB,EAAuB,MAAvB,EAAuB,GAAvB;IAC2BA;IAAOA;IAChCA;IAAyBA;IAAwDA;IAEnFA,iCAAuB,EAAvB,EAAuB,MAAvB,EAAuB,GAAvB;IAC2BA;IAAUA;IACnCA;IAAyBA;IAA+CA;IAE1EA,iCAAuB,EAAvB,EAAuB,MAAvB,EAAuB,GAAvB;IAC2BA;IAAgBA;IACzCA;IACEA;IACFA;IAIJA,kCAA8B,EAA9B,EAA8B,UAA9B,EAA8B,GAA9B,EAA8B,EAA9B,EAA8B,iBAA9B,EAA8B,EAA9B,EAA8B,gBAA9B;IAGsBA;IAAYA;IAE9BA,0CAAkB,EAAlB,EAAkB,gBAAlB,EAAkB,EAAlB,EAAkB,EAAlB,EAAkB,WAAlB;IAEeA;IAAYA;IACvBA;IAGEA;MAAAA;MAAA;MAAA;IAAA;IAHFA;IAOFA;IACEA;MAAAA;MAAA;MAAA,OAASA,yCAAT;IAA0B,CAA1B;IAMAA;IAAUA;IAAIA;IACdA;IACFA;IAIJA,uCAAgC,EAAhC,EAAgC,iBAAhC,EAAgC,EAAhC,EAAgC,gBAAhC;IAEoBA;IAAcA;IAEhCA,0CAAkB,EAAlB,EAAkB,gBAAlB,EAAkB,EAAlB,EAAkB,EAAlB,EAAkB,WAAlB;IAEeA;IAAYA;IACvBA;IAGEA;MAAAA;MAAA;MAAA;IAAA;IAHFA;IAOFA;IACEA;MAAAA;MAAA;MAAA,OAASA,2CAAT;IAA4B,CAA5B;IAMAA;IAAUA;IAAOA;IACjBA;IACFA;;;;;IArFYA;IAAAA;IAgBWA;IAAAA;IAIAA;IAAAA;IAIAA;IAAAA;IAIfA;IAAAA;IACRA;IAAAA;IAgBIA;IAAAA;IAMFA;IAAAA;IAqBEA;IAAAA;IAMFA;IAAAA;;;;ADjmBd,OAAM,MAAOE,mBAAP,CAA0B;EAwC9BC,YAAoBC,UAApB,EAA0C;IAAtB;IAvCX,mBAA6B,IAA7B;IACC,eAAU,IAAIL,YAAJ,EAAV;IAEV,iBAAY,UAAZ;IACA,sBAAiB,CAAjB;IACA,oBAA8B,EAA9B;IACA,mBAA4B,EAA5B;IACA,cAAkB,EAAlB;IACA,qBAA8B,IAA9B;IACA,kBAA0B,EAA1B;IACA,aAA4B,EAA5B;IACA,mBAAc,EAAd;IACA,uBAAkB,EAAlB;IACA,oBAAe,EAAf;IACA,2BAAsB,EAAtB;IACA,uBAAkB;MAChBM,OAAO,EAAE,CADO;MAEhBC,cAAc,EAAE,EAFA;MAGhBC,UAAU,EAAE;IAHI,CAAlB;IAKA,eAAU,KAAV;IACA,kBAAa,EAAb;IACA,qBAAgB,KAAhB;IACA,oBAAwC,IAAxC;IACA,mBAAc,EAAd;IACA,qBAAgB,EAAhB,CAc0C,CAZ1C;;IACA,sBAAiB;MACfC,YAAY,EAAE,WADC;MAEfC,UAAU,EAAE;IAFG,CAAjB;IAIA,6BAAwB;MACtBD,YAAY,EAAE,WADQ;MAEtBC,UAAU,EAAE,EAFU;MAGtBC,WAAW,EAAE,IAAIC,IAAJ;IAHS,CAAxB;IAKA,sBAAiB,EAAjB;EAE8C;;EAE9CC,QAAQ;IACN,KAAKC,QAAL;EACD;;EAEDC,WAAW,IACT;EACD;;EAEDD,QAAQ;IACN,IAAI,KAAKE,SAAL,KAAmB,UAAvB,EAAmC;MACjC,KAAKC,mBAAL;IACD,CAFD,MAEO,IAAI,KAAKD,SAAL,KAAmB,WAAvB,EAAoC;MACzC,KAAKE,iBAAL;IACD,CAFM,MAEA,IAAI,KAAKF,SAAL,KAAmB,SAAvB,EAAkC;MACvC,KAAKG,gBAAL;IACD,CAFM,MAEA,IAAI,KAAKH,SAAL,KAAmB,QAAvB,EAAiC;MACtC,KAAKI,WAAL;IACD,CAFM,MAEA,IAAI,KAAKJ,SAAL,KAAmB,OAAvB,EAAgC;MACrC,KAAKK,UAAL;IACD;EACF;;EAEDC,WAAW,CAACC,QAAD,EAAiB;IAC1B,MAAMC,IAAI,GAAG,CAAC,UAAD,EAAa,WAAb,EAA0B,SAA1B,EAAqC,QAArC,EAA+C,OAA/C,CAAb;IACA,KAAKR,SAAL,GAAiBQ,IAAI,CAACD,QAAD,CAArB;IACA,KAAKE,cAAL,GAAsBF,QAAtB;IACA,KAAKT,QAAL;EACD;;EAEDY,aAAa,CAACC,KAAD,EAAa;IACxB,KAAKC,aAAL,GAAqBD,KAArB;IACA,KAAKE,eAAL,CAAqBF,KAAK,CAACG,EAA3B;EACD;;EAEaZ,iBAAiB;IAAA;;IAAA;MAC7B,IAAI,CAAC,KAAI,CAACa,WAAV,EAAuB;;MAEvB,IAAI;QACF,KAAI,CAACC,OAAL,GAAe,IAAf;QACA,KAAI,CAACC,YAAL,GAAoB,OAAM,KAAI,CAAC5B,UAAL,CAAgB6B,eAAhB,CAAgC,KAAI,CAACH,WAArC,EAAkDI,SAAlD,EAAN,KAAuE,EAA3F;MACD,CAHD,CAGE,OAAOC,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,gCAAd,EAAgDA,KAAhD;MACD,CALD,SAKU;QACR,KAAI,CAACJ,OAAL,GAAe,KAAf;MACD;IAV4B;EAW9B;;EAEab,gBAAgB;IAAA;;IAAA;MAC5B,IAAI,CAAC,MAAI,CAACY,WAAV,EAAuB;;MAEvB,IAAI;QACF,MAAI,CAACC,OAAL,GAAe,IAAf;QACA,MAAI,CAACM,WAAL,GAAmB,OAAM,MAAI,CAACjC,UAAL,CAAgBkC,cAAhB,CAA+B,MAAI,CAACR,WAApC,EAAiDI,SAAjD,EAAN,KAAsE,EAAzF;MACD,CAHD,CAGE,OAAOC,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,+BAAd,EAA+CA,KAA/C;MACD,CALD,SAKU;QACR,MAAI,CAACJ,OAAL,GAAe,KAAf;MACD;IAV2B;EAW7B;;EAEaZ,WAAW;IAAA;;IAAA;MACvB,IAAI,CAAC,MAAI,CAACW,WAAV,EAAuB;;MAEvB,IAAI;QACF,MAAI,CAACC,OAAL,GAAe,IAAf;QACA,MAAI,CAACQ,MAAL,GAAc,OAAM,MAAI,CAACnC,UAAL,CAAgBoC,SAAhB,CAA0B,MAAI,CAACV,WAA/B,EAA4CI,SAA5C,EAAN,KAAiE,EAA/E;MACD,CAHD,CAGE,OAAOC,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,yBAAd,EAAyCA,KAAzC;MACD,CALD,SAKU;QACR,MAAI,CAACJ,OAAL,GAAe,KAAf;MACD;IAVsB;EAWxB;;EAEaH,eAAe,CAACa,OAAD,EAAgB;IAAA;;IAAA;MAC3C,IAAI,CAAC,MAAI,CAACX,WAAV,EAAuB;;MAEvB,IAAI;QACF,MAAI,CAACY,UAAL,GAAkB,OAAM,MAAI,CAACtC,UAAL,CAAgBuC,aAAhB,CAA8B,MAAI,CAACb,WAAnC,EAAgDW,OAAhD,EAAyDP,SAAzD,EAAN,KAA8E,EAAhG;MACD,CAFD,CAEE,OAAOC,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,8BAAd,EAA8CA,KAA9C;MACD;IAP0C;EAQ5C;;EAEKS,kBAAkB;IAAA;;IAAA;MACtB,IAAI,CAAC,MAAI,CAACC,WAAL,CAAiBC,IAAjB,EAAD,IAA4B,CAAC,MAAI,CAAChB,WAAtC,EAAmD;;MAEnD,IAAI;QACF,MAAM,MAAI,CAAC1B,UAAL,CAAgB2C,cAAhB,CAA+B,MAAI,CAACjB,WAApC,EAAiD,MAAI,CAACe,WAAL,CAAiBC,IAAjB,EAAjD,EAA0EZ,SAA1E,EAAN;QACA,MAAI,CAACW,WAAL,GAAmB,EAAnB;;QACA,MAAI,CAAC5B,iBAAL;MACD,CAJD,CAIE,OAAOkB,KAAP,EAAmB;QACnBa,KAAK,CAACb,KAAK,CAACA,KAAN,EAAaA,KAAb,IAAsBA,KAAK,CAACc,OAA5B,IAAuC,oBAAxC,CAAL;MACD;IATqB;EAUvB;;EAEKC,uBAAuB,CAACC,QAAD,EAAiB;IAAA;;IAAA;MAC5C,IAAI,CAAC,MAAI,CAACrB,WAAV,EAAuB;;MAEvB,IAAI;QACF,MAAM,MAAI,CAAC1B,UAAL,CAAgBgD,iBAAhB,CAAkC,MAAI,CAACtB,WAAvC,EAAoDqB,QAApD,EAA8DjB,SAA9D,EAAN;;QACA,MAAI,CAACjB,iBAAL;MACD,CAHD,CAGE,OAAOkB,KAAP,EAAmB;QACnBa,KAAK,CAACb,KAAK,CAACA,KAAN,EAAaA,KAAb,IAAsBA,KAAK,CAACc,OAA5B,IAAuC,uBAAxC,CAAL;MACD;IAR2C;EAS7C;;EAEKI,gBAAgB;IAAA;;IAAA;MACpB,IAAI,CAAC,MAAI,CAACvB,WAAV,EAAuB;;MAEvB,IAAI;QACF,MAAMwB,MAAM,SAAS,MAAI,CAAClD,UAAL,CAAgBiD,gBAAhB,CAAiC,MAAI,CAACvB,WAAtC,EAAmD,MAAI,CAACyB,eAAxD,EAAyErB,SAAzE,EAArB;QACA,MAAI,CAACqB,eAAL,GAAuB;UACrBlD,OAAO,EAAE,CADY;UAErBC,cAAc,EAAE,EAFK;UAGrBC,UAAU,EAAE;QAHS,CAAvB;;QAKA,MAAI,CAACW,gBAAL,GAPE,CASF;;;QACA,IAAIoC,MAAM,EAAEE,IAAZ,EAAkB;UAChBC,SAAS,CAACC,SAAV,CAAoBC,SAApB,CAA8BL,MAAM,CAACE,IAArC;UACA,MAAI,CAACI,UAAL,GAAkBN,MAAM,CAACE,IAAzB;UACAK,UAAU,CAAC,MAAM,MAAI,CAACD,UAAL,GAAkB,EAAzB,EAA6B,IAA7B,CAAV;QACD;MACF,CAfD,CAeE,OAAOzB,KAAP,EAAmB;QACnBa,KAAK,CAACb,KAAK,CAACA,KAAN,EAAaA,KAAb,IAAsBA,KAAK,CAACc,OAA5B,IAAuC,8BAAxC,CAAL;MACD;IApBmB;EAqBrB;;EAEKa,oBAAoB,CAACN,IAAD,EAAa;IAAA;;IAAA;MACrC,IAAI,CAAC,MAAI,CAAC1B,WAAV,EAAuB;;MAEvB,IAAI;QACF,MAAM,MAAI,CAAC1B,UAAL,CAAgB0D,oBAAhB,CAAqC,MAAI,CAAChC,WAA1C,EAAuD0B,IAAvD,EAA6DtB,SAA7D,EAAN;;QACA,MAAI,CAAChB,gBAAL;MACD,CAHD,CAGE,OAAOiB,KAAP,EAAmB;QACnBa,KAAK,CAACb,KAAK,CAACA,KAAN,EAAaA,KAAb,IAAsBA,KAAK,CAACc,OAA5B,IAAuC,kCAAxC,CAAL;MACD;IARoC;EAStC;;EAEKc,WAAW;IAAA;;IAAA;MACf,IAAI,CAAC,MAAI,CAACC,YAAL,CAAkBlB,IAAlB,EAAD,IAA6B,CAAC,MAAI,CAAChB,WAAvC,EAAoD;;MAEpD,IAAI;QACF,MAAM,MAAI,CAAC1B,UAAL,CAAgB2D,WAAhB,CACJ,MAAI,CAACjC,WADD,EAEJ,MAAI,CAACkC,YAAL,CAAkBlB,IAAlB,EAFI,EAGJ,MAAI,CAACmB,mBAAL,CAAyBnB,IAAzB,MAAmCoB,SAH/B,EAIJhC,SAJI,EAAN;QAKA,MAAI,CAAC8B,YAAL,GAAoB,EAApB;QACA,MAAI,CAACC,mBAAL,GAA2B,EAA3B;;QACA,MAAI,CAAC9C,WAAL;MACD,CATD,CASE,OAAOgB,KAAP,EAAmB;QACnBa,KAAK,CAACb,KAAK,CAACA,KAAN,EAAaA,KAAb,IAAsBA,KAAK,CAACc,OAA5B,IAAuC,wBAAxC,CAAL;MACD;IAdc;EAehB;;EAEKkB,WAAW,CAAC1B,OAAD,EAAgB;IAAA;;IAAA;MAC/B,IAAI,CAAC2B,OAAO,CAAC,6CAAD,CAAR,IAA2D,CAAC,MAAI,CAACtC,WAArE,EAAkF;;MAElF,IAAI;QACF,MAAM,MAAI,CAAC1B,UAAL,CAAgB+D,WAAhB,CAA4B,MAAI,CAACrC,WAAjC,EAA8CW,OAA9C,EAAuDP,SAAvD,EAAN;;QACA,MAAI,CAACf,WAAL;;QACA,IAAI,MAAI,CAACQ,aAAL,IAAsB,MAAI,CAACA,aAAL,CAAmBE,EAAnB,KAA0BY,OAApD,EAA6D;UAC3D,MAAI,CAACd,aAAL,GAAqB,IAArB;QACD;MACF,CAND,CAME,OAAOQ,KAAP,EAAmB;QACnBa,KAAK,CAACb,KAAK,CAACA,KAAN,EAAaA,KAAb,IAAsBA,KAAK,CAACc,OAA5B,IAAuC,wBAAxC,CAAL;MACD;IAX8B;EAYhC;;EAEKoB,eAAe,CAAClB,QAAD,EAAiB;IAAA;;IAAA;MACpC,IAAI,CAAC,MAAI,CAACxB,aAAN,IAAuB,CAAC,MAAI,CAACG,WAAjC,EAA8C;;MAE9C,IAAI;QACF,MAAM,MAAI,CAAC1B,UAAL,CAAgBiE,eAAhB,CAAgC,MAAI,CAACvC,WAArC,EAAkD,MAAI,CAACH,aAAL,CAAmBE,EAArE,EAAyEsB,QAAzE,EAAmFjB,SAAnF,EAAN;;QACA,MAAI,CAACN,eAAL,CAAqB,MAAI,CAACD,aAAL,CAAmBE,EAAxC;MACD,CAHD,CAGE,OAAOM,KAAP,EAAmB;QACnBa,KAAK,CAACb,KAAK,CAACA,KAAN,EAAaA,KAAb,IAAsBA,KAAK,CAACc,OAA5B,IAAuC,6BAAxC,CAAL;MACD;IARmC;EASrC;;EAEKqB,gBAAgB,CAACnB,QAAD,EAAiB;IAAA;;IAAA;MACrC,IAAI,CAAC,OAAI,CAACxB,aAAN,IAAuB,CAAC,OAAI,CAACG,WAAjC,EAA8C;;MAE9C,IAAI;QACF,MAAM,OAAI,CAAC1B,UAAL,CAAgBkE,gBAAhB,CAAiC,OAAI,CAACxC,WAAtC,EAAmD,OAAI,CAACH,aAAL,CAAmBE,EAAtE,EAA0EsB,QAA1E,EAAoFjB,SAApF,EAAN;;QACA,OAAI,CAACN,eAAL,CAAqB,OAAI,CAACD,aAAL,CAAmBE,EAAxC;MACD,CAHD,CAGE,OAAOM,KAAP,EAAmB;QACnBa,KAAK,CAACb,KAAK,CAACA,KAAN,EAAaA,KAAb,IAAsBA,KAAK,CAACc,OAA5B,IAAuC,8BAAxC,CAAL;MACD;IARoC;EAStC;;EAEDsB,eAAe,CAACC,IAAD,EAAa;IAC1Bf,SAAS,CAACC,SAAV,CAAoBC,SAApB,CAA8Ba,IAA9B;IACA,KAAKZ,UAAL,GAAkBY,IAAlB;IACAX,UAAU,CAAC,MAAM,KAAKD,UAAL,GAAkB,EAAzB,EAA6B,IAA7B,CAAV;EACD;;EAEDa,UAAU,CAACC,UAAD,EAAmB;IAC3B,OAAO,IAAI/D,IAAJ,CAAS+D,UAAT,EAAqBC,cAArB,EAAP;EACD;;EAEDC,YAAY;IACV,KAAKC,OAAL,CAAaC,IAAb;EACD;;EAEDC,iBAAiB,CAACC,KAAD,EAAa;IAC5B,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAArB;IACA,KAAK1B,eAAL,CAAqBhD,UAArB,GAAkC0E,MAAM,CAACC,KAAP,CAAaC,WAAb,EAAlC;EACD,CA5P6B,CA8P9B;;;EACc/D,UAAU;IAAA;;IAAA;MACtB,IAAI,CAAC,OAAI,CAACU,WAAV,EAAuB;;MAEvB,IAAI;QACF,OAAI,CAACC,OAAL,GAAe,IAAf;QACA,OAAI,CAACqD,KAAL,GAAa,OAAM,OAAI,CAAChF,UAAL,CAAgBiF,0BAAhB,CAA2C,OAAI,CAACvD,WAAhD,EAA6DI,SAA7D,EAAN,KAAkF,EAA/F;MACD,CAHD,CAGE,OAAOC,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,wBAAd,EAAwCA,KAAxC;MACD,CALD,SAKU;QACR,OAAI,CAACJ,OAAL,GAAe,KAAf;MACD;IAVqB;EAWvB;;EAEKuD,UAAU;IAAA;;IAAA;MACd,IAAI,CAAC,OAAI,CAACzC,WAAL,CAAiBC,IAAjB,EAAD,IAA4B,CAAC,OAAI,CAACyC,eAAL,CAAqBzC,IAArB,EAA7B,IAA4D,CAAC,OAAI,CAAChB,WAAtE,EAAmF;;MAEnF,IAAI,OAAI,CAACyD,eAAL,CAAqBC,MAArB,GAA8B,CAAlC,EAAqC;QACnCxC,KAAK,CAAC,6CAAD,CAAL;QACA;MACD;;MAED,IAAI;QACF,MAAM,OAAI,CAAC5C,UAAL,CAAgBkF,UAAhB,CAA2B,OAAI,CAACxD,WAAhC,EAA6C,OAAI,CAACe,WAAL,CAAiBC,IAAjB,EAA7C,EAAsE,OAAI,CAACyC,eAAL,CAAqBzC,IAArB,EAAtE,EAAmGZ,SAAnG,EAAN;QACA,OAAI,CAACW,WAAL,GAAmB,EAAnB;QACA,OAAI,CAAC0C,eAAL,GAAuB,EAAvB;;QACA,OAAI,CAACnE,UAAL;MACD,CALD,CAKE,OAAOe,KAAP,EAAmB;QACnBa,KAAK,CAACb,KAAK,CAACA,KAAN,EAAaA,KAAb,IAAsBA,KAAK,CAACc,OAA5B,IAAuC,uBAAxC,CAAL;MACD;IAfa;EAgBf;;EAEDwC,aAAa,CAACC,IAAD,EAAuB;IAClC,KAAKC,YAAL,GAAoBD,IAApB;IACA,KAAKE,WAAL,GAAmB,EAAnB;IACA,KAAKC,aAAL,GAAqB,EAArB;IACA,KAAKC,aAAL,GAAqB,IAArB;EACD;;EAEDC,cAAc;IACZ,KAAKD,aAAL,GAAqB,KAArB;IACA,KAAKH,YAAL,GAAoB,IAApB;IACA,KAAKC,WAAL,GAAmB,EAAnB;IACA,KAAKC,aAAL,GAAqB,EAArB;EACD;;EAEKG,eAAe;IAAA;;IAAA;MACnB,IAAI,CAAC,OAAI,CAACL,YAAN,IAAsB,CAAC,OAAI,CAACC,WAAL,CAAiB9C,IAAjB,EAAvB,IAAkD,CAAC,OAAI,CAAChB,WAA5D,EAAyE;;MAEzE,IAAI,OAAI,CAAC8D,WAAL,CAAiBJ,MAAjB,GAA0B,CAA9B,EAAiC;QAC/BxC,KAAK,CAAC,6CAAD,CAAL;QACA;MACD;;MAED,IAAI;QACF,MAAM,OAAI,CAAC5C,UAAL,CAAgB4F,eAAhB,CAAgC,OAAI,CAAClE,WAArC,EAAkD,OAAI,CAAC6D,YAAL,CAAkBxC,QAApE,EAA8E,OAAI,CAACyC,WAAL,CAAiB9C,IAAjB,EAA9E,EAAuGZ,SAAvG,EAAN;QACAc,KAAK,CAAC,2BAAD,CAAL;;QACA,OAAI,CAAC+C,cAAL;;QACA,OAAI,CAAC3E,UAAL;MACD,CALD,CAKE,OAAOe,KAAP,EAAmB;QACnBa,KAAK,CAACb,KAAK,CAACA,KAAN,EAAaA,KAAb,IAAsBA,KAAK,CAACc,OAA5B,IAAuC,wBAAxC,CAAL;MACD;IAfkB;EAgBpB;;EAEKgD,iBAAiB;IAAA;;IAAA;MACrB,IAAI,CAAC,OAAI,CAACN,YAAN,IAAsB,CAAC,OAAI,CAACE,aAAL,CAAmB/C,IAAnB,EAAvB,IAAoD,CAAC,OAAI,CAAChB,WAA9D,EAA2E;;MAE3E,IAAI,OAAI,CAAC+D,aAAL,CAAmBL,MAAnB,GAA4B,CAAhC,EAAmC;QACjCxC,KAAK,CAAC,6CAAD,CAAL;QACA;MACD;;MAED,IAAI;QACF,MAAM,OAAI,CAAC5C,UAAL,CAAgB6F,iBAAhB,CAAkC,OAAI,CAACnE,WAAvC,EAAoD,OAAI,CAAC6D,YAAL,CAAkBxC,QAAtE,EAAgF,OAAI,CAAC0C,aAAL,CAAmB/C,IAAnB,EAAhF,EAA2GZ,SAA3G,EAAN;QACAc,KAAK,CAAC,6BAAD,CAAL;;QACA,OAAI,CAAC+C,cAAL;;QACA,OAAI,CAAC3E,UAAL;MACD,CALD,CAKE,OAAOe,KAAP,EAAmB;QACnBa,KAAK,CAACb,KAAK,CAACA,KAAN,EAAaA,KAAb,IAAsBA,KAAK,CAACc,OAA5B,IAAuC,0BAAxC,CAAL;MACD;IAfoB;EAgBtB,CA9U6B,CAgV9B;;;EACMjC,mBAAmB;IAAA;;IAAA;MACvB,IAAI,CAAC,OAAI,CAACc,WAAV,EAAuB;;MAEvB,IAAI;QACF,MAAMoE,MAAM,SAAS,OAAI,CAAC9F,UAAL,CAAgB+F,iBAAhB,CAAkC,OAAI,CAACrE,WAAvC,EAAoDI,SAApD,EAArB;;QACA,IAAIgE,MAAJ,EAAY;UACV,OAAI,CAACE,qBAAL,GAA6B;YAC3B5F,YAAY,EAAE0F,MAAM,CAAC1F,YADM;YAE3BC,UAAU,EAAEyF,MAAM,CAACzF,UAFQ;YAG3BC,WAAW,EAAE,IAAIC,IAAJ,CAASuF,MAAM,CAACxF,WAAP,IAAsBC,IAAI,CAAC0F,GAAL,EAA/B;UAHc,CAA7B,CADU,CAOV;;UACA,OAAI,CAACC,cAAL,GAAsB;YACpB9F,YAAY,EAAE0F,MAAM,CAAC1F,YADD;YAEpBC,UAAU,EAAE,CAAC,GAAGyF,MAAM,CAACzF,UAAX;UAFQ,CAAtB;UAIA,OAAI,CAAC8F,cAAL,GAAsBL,MAAM,CAACzF,UAAP,CAAkB+F,IAAlB,CAAuB,IAAvB,CAAtB;QACD;MACF,CAhBD,CAgBE,OAAOrE,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,yCAAd,EAAyDA,KAAzD;MACD;IArBsB;EAsBxB;;EAEKsE,oBAAoB;IAAA;;IAAA;MACxB,IAAI,CAAC,OAAI,CAAC3E,WAAN,IAAqB,CAAC,OAAI,CAAC4E,qBAAL,EAA1B,EAAwD;MAExD,OAAI,CAAC3E,OAAL,GAAe,IAAf;;MACA,IAAI;QACF,MAAM,OAAI,CAAC3B,UAAL,CAAgBqG,oBAAhB,CAAqC,OAAI,CAAC3E,WAA1C,EAAuD,OAAI,CAACwE,cAA5D,EAA4EpE,SAA5E,EAAN;QACAc,KAAK,CAAC,6CAAD,CAAL;QACA,MAAM,OAAI,CAAChC,mBAAL,EAAN;MACD,CAJD,CAIE,OAAOmB,KAAP,EAAmB;QACnBa,KAAK,CAACb,KAAK,CAACA,KAAN,EAAaA,KAAb,IAAsBA,KAAK,CAACc,OAA5B,IAAuC,yCAAxC,CAAL;MACD,CAND,SAMU;QACR,OAAI,CAAClB,OAAL,GAAe,KAAf;MACD;IAZuB;EAazB;;EAED4E,mBAAmB;IACjB,KAAKL,cAAL,GAAsB;MACpB9F,YAAY,EAAE,KAAK4F,qBAAL,CAA2B5F,YADrB;MAEpBC,UAAU,EAAE,CAAC,GAAG,KAAK2F,qBAAL,CAA2B3F,UAA/B;IAFQ,CAAtB;IAIA,KAAK8F,cAAL,GAAsB,KAAKH,qBAAL,CAA2B3F,UAA3B,CAAsC+F,IAAtC,CAA2C,IAA3C,CAAtB;EACD;;EAEDI,kBAAkB,CAAC5B,KAAD,EAAa;IAC7B,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAArB;IACA,KAAKsB,cAAL,GAAsBtB,MAAM,CAACC,KAA7B;IACA,KAAKoB,cAAL,CAAoB7F,UAApB,GAAiCwE,MAAM,CAACC,KAAP,CAC9B2B,KAD8B,CACxB,GADwB,EAE9BC,GAF8B,CAE1BpB,IAAI,IAAIA,IAAI,CAAC5C,IAAL,EAFkB,EAG9BiE,MAH8B,CAGvBrB,IAAI,IAAIA,IAAI,CAACF,MAAL,GAAc,CAHC,CAAjC;EAID;;EAEDkB,qBAAqB;IACnB,OAAO,KAAKJ,cAAL,CAAoB9F,YAApB,CAAiCgF,MAAjC,GAA0C,CAA1C,IACA,KAAKc,cAAL,CAAoB7F,UAApB,CAA+B+E,MAA/B,GAAwC,CADxC,IAEA,KAAKc,cAAL,CAAoB7F,UAApB,CAA+BuG,KAA/B,CAAqCtB,IAAI,IAAIA,IAAI,CAAC5C,IAAL,GAAY0C,MAAZ,GAAqB,CAAlE,CAFP;EAGD;;EAEDyB,sBAAsB,CAACC,IAAD,EAAa;IACjC,QAAQA,IAAR;MACE,KAAK,MAAL;QAAa,OAAO,wBAAP;;MACb,KAAK,WAAL;QAAkB,OAAO,iCAAP;;MAClB,KAAK,QAAL;QAAe,OAAO,kCAAP;;MACf;QAAS,OAAOA,IAAP;IAJX;EAMD;;AAtZ6B;;;mBAAnBhH,qBAAmBF;AAAA;;;QAAnBE;EAAmBiH;EAAAC;IAAAtF;EAAA;EAAAuF;IAAAxC;EAAA;EAAAyC;EAAAC;EAAAC;EAAAC;IAAA;MCPhCzH;MAAiCA;QAAA,OAAS0H,kBAAT;MAAuB,CAAvB;MAC/B1H;MAAwCA;QAAA,OAASC,wBAAT;MAAiC,CAAjC;MACtCD,2CAA4C,CAA5C,EAA4C,KAA5C,EAA4C,CAA5C,EAA4C,CAA5C,EAA4C,UAA5C,EAA4C,CAA5C;MAEiCA;MAAoBA;MACjDA;MAAgBA;MAAWA;MAE7BA;MACEA;QAAA,OAAS0H,kBAAT;MAAuB,CAAvB;MAKA1H;MAAUA;MAAKA;MAKnBA,6CAA8C,EAA9C,EAA8C,eAA9C,EAA8C,CAA9C;MAEiBA;QAAA;MAAA,GAAkC,qBAAlC,EAAkC;QAAA,OAAwB0H,uBAAxB;MAA2C,CAA7E;MACb1H,oCAAiC,EAAjC,EAAiC,UAAjC,EAAiC,CAAjC;MACsBA;MAAQA;MAE9BA,qCAA+B,EAA/B,EAA+B,UAA/B,EAA+B,CAA/B;MACsBA;MAAMA;MAE5BA,qCAA8B,EAA9B,EAA8B,UAA9B,EAA8B,CAA9B;MACsBA;MAAOA;MAE7BA,qCAAwB,EAAxB,EAAwB,UAAxB,EAAwB,CAAxB;MACsBA;MAAKA;MAE3BA,qCAAuB,EAAvB,EAAuB,UAAvB,EAAuB,CAAvB;MACsBA;MAAMA;MAK9BA;MA8GAA;MA2EAA;MA0GAA;MAqHAA;MAiFFA;MAKJA;;;;MAjgBqBA;MAAAA;MAmBTA;MAAAA;MA8GAA;MAAAA;MA2EAA;MAAAA;MA0GAA;MAAAA;MAqHAA;MAAAA;MAsFNA;MAAAA", "names": ["EventEmitter", "i0", "$event", "AdminPanelComponent", "constructor", "apiService", "maxUses", "expiresInHours", "customCode", "securityMode", "adminUsers", "lastUpdated", "Date", "ngOnInit", "loadData", "ngOnDestroy", "activeTab", "fetchSecurityConfig", "fetchAllowedUsers", "fetchInviteCodes", "fetchGroups", "fetchUsers", "onTabChange", "tabIndex", "tabs", "activeTabIndex", "onGroupSelect", "group", "selectedGroup", "fetchGroupUsers", "id", "currentUser", "loading", "allowedUsers", "getAllowedUsers", "to<PERSON>romise", "error", "console", "inviteCodes", "getInviteCodes", "groups", "getGroups", "groupId", "groupUsers", "getGroupUsers", "addUser<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "newUsername", "trim", "addAllowedUser", "alert", "message", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "username", "removeAllowedUser", "createInviteCode", "result", "newInviteConfig", "code", "navigator", "clipboard", "writeText", "copiedCode", "setTimeout", "deactivateInviteCode", "createGroup", "newGroupName", "newGroupDescription", "undefined", "deleteGroup", "confirm", "grantUserAccess", "revokeUserAccess", "copyToClipboard", "text", "formatDate", "dateString", "toLocaleString", "onCloseClick", "onClose", "emit", "onCustomCodeInput", "event", "target", "value", "toUpperCase", "users", "getUsersWithPasswordStatus", "createUser", "newUserPassword", "length", "openUserModal", "user", "selected<PERSON>ser", "newPassword", "resetPassword", "showUserModal", "closeUserModal", "setUserPassword", "resetUserPassword", "config", "getSecurityConfig", "currentSecurityConfig", "now", "securityConfig", "adminUsersText", "join", "updateSecurityConfig", "isSecurityConfigValid", "resetSecurityConfig", "onAdminUsersChange", "split", "map", "filter", "every", "getSecurityModeDisplay", "mode", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["R:\\chateye\\FrontendAngular\\src\\app\\components\\admin-panel\\admin-panel.component.ts", "R:\\chateye\\FrontendAngular\\src\\app\\components\\admin-panel\\admin-panel.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';\r\nimport { ApiService, AllowedUser, InviteCode, Group, GroupUser, UserWithPassword } from '../../services/api.service';\r\n\r\n@Component({\r\n  selector: 'app-admin-panel',\r\n  templateUrl: './admin-panel.component.html',\r\n  styleUrls: ['./admin-panel.component.css']\r\n})\r\nexport class AdminPanelComponent implements OnInit, OnDestroy {\r\n  @Input() currentUser: string | null = null;\r\n  @Output() onClose = new EventEmitter<void>();\r\n\r\n  activeTab = 'security';\r\n  activeTabIndex = 0;\r\n  allowedUsers: AllowedUser[] = [];\r\n  inviteCodes: InviteCode[] = [];\r\n  groups: Group[] = [];\r\n  selectedGroup: Group | null = null;\r\n  groupUsers: GroupUser[] = [];\r\n  users: UserWithPassword[] = [];\r\n  newUsername = '';\r\n  newUserPassword = '';\r\n  newGroupName = '';\r\n  newGroupDescription = '';\r\n  newInviteConfig = {\r\n    maxUses: 1,\r\n    expiresInHours: 24,\r\n    customCode: ''\r\n  };\r\n  loading = false;\r\n  copiedCode = '';\r\n  showUserModal = false;\r\n  selectedUser: UserWithPassword | null = null;\r\n  newPassword = '';\r\n  resetPassword = '';\r\n  \r\n  // Security configuration\r\n  securityConfig = {\r\n    securityMode: 'whitelist',\r\n    adminUsers: [] as string[]\r\n  };\r\n  currentSecurityConfig = {\r\n    securityMode: 'whitelist',\r\n    adminUsers: [] as string[],\r\n    lastUpdated: new Date()\r\n  };\r\n  adminUsersText = '';\r\n\r\n  constructor(private apiService: ApiService) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loadData();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Cleanup if needed\r\n  }\r\n\r\n  loadData(): void {\r\n    if (this.activeTab === 'security') {\r\n      this.fetchSecurityConfig();\r\n    } else if (this.activeTab === 'whitelist') {\r\n      this.fetchAllowedUsers();\r\n    } else if (this.activeTab === 'invites') {\r\n      this.fetchInviteCodes();\r\n    } else if (this.activeTab === 'groups') {\r\n      this.fetchGroups();\r\n    } else if (this.activeTab === 'users') {\r\n      this.fetchUsers();\r\n    }\r\n  }\r\n\r\n  onTabChange(tabIndex: number): void {\r\n    const tabs = ['security', 'whitelist', 'invites', 'groups', 'users'];\r\n    this.activeTab = tabs[tabIndex];\r\n    this.activeTabIndex = tabIndex;\r\n    this.loadData();\r\n  }\r\n\r\n  onGroupSelect(group: Group): void {\r\n    this.selectedGroup = group;\r\n    this.fetchGroupUsers(group.id);\r\n  }\r\n\r\n  private async fetchAllowedUsers(): Promise<void> {\r\n    if (!this.currentUser) return;\r\n    \r\n    try {\r\n      this.loading = true;\r\n      this.allowedUsers = await this.apiService.getAllowedUsers(this.currentUser).toPromise() || [];\r\n    } catch (error) {\r\n      console.error('Failed to fetch allowed users:', error);\r\n    } finally {\r\n      this.loading = false;\r\n    }\r\n  }\r\n\r\n  private async fetchInviteCodes(): Promise<void> {\r\n    if (!this.currentUser) return;\r\n    \r\n    try {\r\n      this.loading = true;\r\n      this.inviteCodes = await this.apiService.getInviteCodes(this.currentUser).toPromise() || [];\r\n    } catch (error) {\r\n      console.error('Failed to fetch invite codes:', error);\r\n    } finally {\r\n      this.loading = false;\r\n    }\r\n  }\r\n\r\n  private async fetchGroups(): Promise<void> {\r\n    if (!this.currentUser) return;\r\n    \r\n    try {\r\n      this.loading = true;\r\n      this.groups = await this.apiService.getGroups(this.currentUser).toPromise() || [];\r\n    } catch (error) {\r\n      console.error('Failed to fetch groups:', error);\r\n    } finally {\r\n      this.loading = false;\r\n    }\r\n  }\r\n\r\n  private async fetchGroupUsers(groupId: string): Promise<void> {\r\n    if (!this.currentUser) return;\r\n    \r\n    try {\r\n      this.groupUsers = await this.apiService.getGroupUsers(this.currentUser, groupId).toPromise() || [];\r\n    } catch (error) {\r\n      console.error('Failed to fetch group users:', error);\r\n    }\r\n  }\r\n\r\n  async addUserToWhitelist(): Promise<void> {\r\n    if (!this.newUsername.trim() || !this.currentUser) return;\r\n    \r\n    try {\r\n      await this.apiService.addAllowedUser(this.currentUser, this.newUsername.trim()).toPromise();\r\n      this.newUsername = '';\r\n      this.fetchAllowedUsers();\r\n    } catch (error: any) {\r\n      alert(error.error?.error || error.message || 'Failed to add user');\r\n    }\r\n  }\r\n\r\n  async removeUserFromWhitelist(username: string): Promise<void> {\r\n    if (!this.currentUser) return;\r\n    \r\n    try {\r\n      await this.apiService.removeAllowedUser(this.currentUser, username).toPromise();\r\n      this.fetchAllowedUsers();\r\n    } catch (error: any) {\r\n      alert(error.error?.error || error.message || 'Failed to remove user');\r\n    }\r\n  }\r\n\r\n  async createInviteCode(): Promise<void> {\r\n    if (!this.currentUser) return;\r\n    \r\n    try {\r\n      const result = await this.apiService.createInviteCode(this.currentUser, this.newInviteConfig).toPromise();\r\n      this.newInviteConfig = {\r\n        maxUses: 1,\r\n        expiresInHours: 24,\r\n        customCode: ''\r\n      };\r\n      this.fetchInviteCodes();\r\n      \r\n      // Auto-copy the new code\r\n      if (result?.code) {\r\n        navigator.clipboard.writeText(result.code);\r\n        this.copiedCode = result.code;\r\n        setTimeout(() => this.copiedCode = '', 3000);\r\n      }\r\n    } catch (error: any) {\r\n      alert(error.error?.error || error.message || 'Failed to create invite code');\r\n    }\r\n  }\r\n\r\n  async deactivateInviteCode(code: string): Promise<void> {\r\n    if (!this.currentUser) return;\r\n    \r\n    try {\r\n      await this.apiService.deactivateInviteCode(this.currentUser, code).toPromise();\r\n      this.fetchInviteCodes();\r\n    } catch (error: any) {\r\n      alert(error.error?.error || error.message || 'Failed to deactivate invite code');\r\n    }\r\n  }\r\n\r\n  async createGroup(): Promise<void> {\r\n    if (!this.newGroupName.trim() || !this.currentUser) return;\r\n    \r\n    try {\r\n      await this.apiService.createGroup(\r\n        this.currentUser, \r\n        this.newGroupName.trim(), \r\n        this.newGroupDescription.trim() || undefined\r\n      ).toPromise();\r\n      this.newGroupName = '';\r\n      this.newGroupDescription = '';\r\n      this.fetchGroups();\r\n    } catch (error: any) {\r\n      alert(error.error?.error || error.message || 'Failed to create group');\r\n    }\r\n  }\r\n\r\n  async deleteGroup(groupId: string): Promise<void> {\r\n    if (!confirm('Are you sure you want to delete this group?') || !this.currentUser) return;\r\n    \r\n    try {\r\n      await this.apiService.deleteGroup(this.currentUser, groupId).toPromise();\r\n      this.fetchGroups();\r\n      if (this.selectedGroup && this.selectedGroup.id === groupId) {\r\n        this.selectedGroup = null;\r\n      }\r\n    } catch (error: any) {\r\n      alert(error.error?.error || error.message || 'Failed to delete group');\r\n    }\r\n  }\r\n\r\n  async grantUserAccess(username: string): Promise<void> {\r\n    if (!this.selectedGroup || !this.currentUser) return;\r\n    \r\n    try {\r\n      await this.apiService.grantUserAccess(this.currentUser, this.selectedGroup.id, username).toPromise();\r\n      this.fetchGroupUsers(this.selectedGroup.id);\r\n    } catch (error: any) {\r\n      alert(error.error?.error || error.message || 'Failed to grant user access');\r\n    }\r\n  }\r\n\r\n  async revokeUserAccess(username: string): Promise<void> {\r\n    if (!this.selectedGroup || !this.currentUser) return;\r\n    \r\n    try {\r\n      await this.apiService.revokeUserAccess(this.currentUser, this.selectedGroup.id, username).toPromise();\r\n      this.fetchGroupUsers(this.selectedGroup.id);\r\n    } catch (error: any) {\r\n      alert(error.error?.error || error.message || 'Failed to revoke user access');\r\n    }\r\n  }\r\n\r\n  copyToClipboard(text: string): void {\r\n    navigator.clipboard.writeText(text);\r\n    this.copiedCode = text;\r\n    setTimeout(() => this.copiedCode = '', 3000);\r\n  }\r\n\r\n  formatDate(dateString: string): string {\r\n    return new Date(dateString).toLocaleString();\r\n  }\r\n\r\n  onCloseClick(): void {\r\n    this.onClose.emit();\r\n  }\r\n\r\n  onCustomCodeInput(event: Event): void {\r\n    const target = event.target as HTMLInputElement;\r\n    this.newInviteConfig.customCode = target.value.toUpperCase();\r\n  }\r\n\r\n  // User management methods\r\n  private async fetchUsers(): Promise<void> {\r\n    if (!this.currentUser) return;\r\n    \r\n    try {\r\n      this.loading = true;\r\n      this.users = await this.apiService.getUsersWithPasswordStatus(this.currentUser).toPromise() || [];\r\n    } catch (error) {\r\n      console.error('Failed to fetch users:', error);\r\n    } finally {\r\n      this.loading = false;\r\n    }\r\n  }\r\n\r\n  async createUser(): Promise<void> {\r\n    if (!this.newUsername.trim() || !this.newUserPassword.trim() || !this.currentUser) return;\r\n    \r\n    if (this.newUserPassword.length < 6) {\r\n      alert('Password must be at least 6 characters long');\r\n      return;\r\n    }\r\n    \r\n    try {\r\n      await this.apiService.createUser(this.currentUser, this.newUsername.trim(), this.newUserPassword.trim()).toPromise();\r\n      this.newUsername = '';\r\n      this.newUserPassword = '';\r\n      this.fetchUsers();\r\n    } catch (error: any) {\r\n      alert(error.error?.error || error.message || 'Failed to create user');\r\n    }\r\n  }\r\n\r\n  openUserModal(user: UserWithPassword): void {\r\n    this.selectedUser = user;\r\n    this.newPassword = '';\r\n    this.resetPassword = '';\r\n    this.showUserModal = true;\r\n  }\r\n\r\n  closeUserModal(): void {\r\n    this.showUserModal = false;\r\n    this.selectedUser = null;\r\n    this.newPassword = '';\r\n    this.resetPassword = '';\r\n  }\r\n\r\n  async setUserPassword(): Promise<void> {\r\n    if (!this.selectedUser || !this.newPassword.trim() || !this.currentUser) return;\r\n    \r\n    if (this.newPassword.length < 6) {\r\n      alert('Password must be at least 6 characters long');\r\n      return;\r\n    }\r\n    \r\n    try {\r\n      await this.apiService.setUserPassword(this.currentUser, this.selectedUser.username, this.newPassword.trim()).toPromise();\r\n      alert('Password set successfully');\r\n      this.closeUserModal();\r\n      this.fetchUsers();\r\n    } catch (error: any) {\r\n      alert(error.error?.error || error.message || 'Failed to set password');\r\n    }\r\n  }\r\n\r\n  async resetUserPassword(): Promise<void> {\r\n    if (!this.selectedUser || !this.resetPassword.trim() || !this.currentUser) return;\r\n    \r\n    if (this.resetPassword.length < 6) {\r\n      alert('Password must be at least 6 characters long');\r\n      return;\r\n    }\r\n    \r\n    try {\r\n      await this.apiService.resetUserPassword(this.currentUser, this.selectedUser.username, this.resetPassword.trim()).toPromise();\r\n      alert('Password reset successfully');\r\n      this.closeUserModal();\r\n      this.fetchUsers();\r\n    } catch (error: any) {\r\n      alert(error.error?.error || error.message || 'Failed to reset password');\r\n    }\r\n  }\r\n\r\n  // Security Configuration Methods\r\n  async fetchSecurityConfig(): Promise<void> {\r\n    if (!this.currentUser) return;\r\n    \r\n    try {\r\n      const config = await this.apiService.getSecurityConfig(this.currentUser).toPromise();\r\n      if (config) {\r\n        this.currentSecurityConfig = {\r\n          securityMode: config.securityMode,\r\n          adminUsers: config.adminUsers,\r\n          lastUpdated: new Date(config.lastUpdated || Date.now())\r\n        };\r\n        \r\n        // Initialize form with current values\r\n        this.securityConfig = {\r\n          securityMode: config.securityMode,\r\n          adminUsers: [...config.adminUsers]\r\n        };\r\n        this.adminUsersText = config.adminUsers.join(', ');\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to fetch security configuration:', error);\r\n    }\r\n  }\r\n\r\n  async updateSecurityConfig(): Promise<void> {\r\n    if (!this.currentUser || !this.isSecurityConfigValid()) return;\r\n    \r\n    this.loading = true;\r\n    try {\r\n      await this.apiService.updateSecurityConfig(this.currentUser, this.securityConfig).toPromise();\r\n      alert('Security configuration updated successfully');\r\n      await this.fetchSecurityConfig();\r\n    } catch (error: any) {\r\n      alert(error.error?.error || error.message || 'Failed to update security configuration');\r\n    } finally {\r\n      this.loading = false;\r\n    }\r\n  }\r\n\r\n  resetSecurityConfig(): void {\r\n    this.securityConfig = {\r\n      securityMode: this.currentSecurityConfig.securityMode,\r\n      adminUsers: [...this.currentSecurityConfig.adminUsers]\r\n    };\r\n    this.adminUsersText = this.currentSecurityConfig.adminUsers.join(', ');\r\n  }\r\n\r\n  onAdminUsersChange(event: Event): void {\r\n    const target = event.target as HTMLTextAreaElement;\r\n    this.adminUsersText = target.value;\r\n    this.securityConfig.adminUsers = target.value\r\n      .split(',')\r\n      .map(user => user.trim())\r\n      .filter(user => user.length > 0);\r\n  }\r\n\r\n  isSecurityConfigValid(): boolean {\r\n    return this.securityConfig.securityMode.length > 0 && \r\n           this.securityConfig.adminUsers.length > 0 &&\r\n           this.securityConfig.adminUsers.every(user => user.trim().length > 0);\r\n  }\r\n\r\n  getSecurityModeDisplay(mode: string): string {\r\n    switch (mode) {\r\n      case 'open': return 'Open - Anyone can join';\r\n      case 'whitelist': return 'Whitelist - Only approved users';\r\n      case 'invite': return 'Invite - Users need invite codes';\r\n      default: return mode;\r\n    }\r\n  }\r\n}\r\n", "<!-- Material Design Admin Panel Modal -->\r\n<div class=\"admin-panel-overlay\" (click)=\"onCloseClick()\">\r\n  <mat-card class=\"admin-panel-container\" (click)=\"$event.stopPropagation()\">\r\n    <mat-card-header class=\"admin-panel-header\">\r\n      <div class=\"header-content\">\r\n        <mat-icon class=\"admin-icon\">admin_panel_settings</mat-icon>\r\n        <mat-card-title>Admin Panel</mat-card-title>\r\n      </div>\r\n      <button\r\n        (click)=\"onCloseClick()\"\r\n        mat-icon-button\r\n        matTooltip=\"Close\"\r\n        class=\"close-button\"\r\n      >\r\n        <mat-icon>close</mat-icon>\r\n      </button>\r\n    </mat-card-header>\r\n\r\n    <!-- Content -->\r\n    <mat-card-content class=\"admin-panel-content\">\r\n      <!-- Material Design Tabs -->\r\n      <mat-tab-group [(selectedIndex)]=\"activeTabIndex\" (selectedIndexChange)=\"onTabChange($event)\">\r\n        <mat-tab label=\"Security Config\">\r\n          <mat-icon matPrefix>security</mat-icon>\r\n        </mat-tab>\r\n        <mat-tab label=\"Allowed Users\">\r\n          <mat-icon matPrefix>people</mat-icon>\r\n        </mat-tab>\r\n        <mat-tab label=\"Invite Codes\">\r\n          <mat-icon matPrefix>vpn_key</mat-icon>\r\n        </mat-tab>\r\n        <mat-tab label=\"Groups\">\r\n          <mat-icon matPrefix>group</mat-icon>\r\n        </mat-tab>\r\n        <mat-tab label=\"Users\">\r\n          <mat-icon matPrefix>person</mat-icon>\r\n        </mat-tab>\r\n      </mat-tab-group>\r\n\r\n      <!-- Security Configuration Tab -->\r\n      <div *ngIf=\"activeTab === 'security'\" class=\"tab-content\">\r\n        <mat-card class=\"form-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Security Configuration</mat-card-title>\r\n            <mat-card-subtitle>Configure how users can access the chat</mat-card-subtitle>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <form class=\"security-form\">\r\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n                <mat-label>Security Mode</mat-label>\r\n                <mat-select [(ngModel)]=\"securityConfig.securityMode\" name=\"securityMode\">\r\n                  <mat-option value=\"open\">Open - Anyone can join</mat-option>\r\n                  <mat-option value=\"whitelist\">Whitelist - Only approved users</mat-option>\r\n                  <mat-option value=\"invite\">Invite - Users need invite codes</mat-option>\r\n                </mat-select>\r\n                <mat-hint>Choose how users can access the chat</mat-hint>\r\n              </mat-form-field>\r\n\r\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n                <mat-label>Admin Users</mat-label>\r\n                <textarea\r\n                  matInput\r\n                  [(ngModel)]=\"adminUsersText\"\r\n                  name=\"adminUsers\"\r\n                  placeholder=\"Enter admin usernames separated by commas\"\r\n                  rows=\"3\"\r\n                  (input)=\"onAdminUsersChange($event)\"\r\n                ></textarea>\r\n                <mat-hint>Comma-separated list of admin usernames</mat-hint>\r\n              </mat-form-field>\r\n\r\n              <div class=\"security-info\">\r\n                <mat-expansion-panel>\r\n                  <mat-expansion-panel-header>\r\n                    <mat-panel-title>\r\n                      <mat-icon>info</mat-icon>\r\n                      Security Mode Information\r\n                    </mat-panel-title>\r\n                  </mat-expansion-panel-header>\r\n                  <div class=\"security-mode-info\">\r\n                    <div class=\"mode-description\">\r\n                      <h4>Open Mode</h4>\r\n                      <p>Anyone can join with just a username. No restrictions. Good for testing and development.</p>\r\n                    </div>\r\n                    <div class=\"mode-description\">\r\n                      <h4>Whitelist Mode</h4>\r\n                      <p>Only pre-approved users can join. Admin manages the allowed users list. Most secure for private groups.</p>\r\n                    </div>\r\n                    <div class=\"mode-description\">\r\n                      <h4>Invite Mode</h4>\r\n                      <p>Users need a valid invite code to join. Codes can have usage limits and expiration dates. Good for controlled growth.</p>\r\n                    </div>\r\n                  </div>\r\n                </mat-expansion-panel>\r\n              </div>\r\n\r\n              <div class=\"form-actions\">\r\n                <button\r\n                  type=\"button\"\r\n                  (click)=\"updateSecurityConfig()\"\r\n                  [disabled]=\"loading || !isSecurityConfigValid()\"\r\n                  mat-raised-button\r\n                  color=\"primary\"\r\n                  class=\"save-button\"\r\n                >\r\n                  <mat-icon>save</mat-icon>\r\n                  Update Security Configuration\r\n                </button>\r\n                <button\r\n                  type=\"button\"\r\n                  (click)=\"resetSecurityConfig()\"\r\n                  [disabled]=\"loading\"\r\n                  mat-stroked-button\r\n                  class=\"reset-button\"\r\n                >\r\n                  <mat-icon>refresh</mat-icon>\r\n                  Reset to Current\r\n                </button>\r\n              </div>\r\n            </form>\r\n          </mat-card-content>\r\n        </mat-card>\r\n\r\n        <!-- Current Configuration Display -->\r\n        <mat-card class=\"info-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Current Configuration</mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div class=\"config-display\">\r\n              <div class=\"config-item\">\r\n                <span class=\"config-label\">Security Mode:</span>\r\n                <span class=\"config-value\" [ngClass]=\"'mode-' + currentSecurityConfig.securityMode\">\r\n                  {{ getSecurityModeDisplay(currentSecurityConfig.securityMode) }}\r\n                </span>\r\n              </div>\r\n              <div class=\"config-item\">\r\n                <span class=\"config-label\">Admin Users:</span>\r\n                <span class=\"config-value\">{{ currentSecurityConfig.adminUsers.join(', ') }}</span>\r\n              </div>\r\n              <div class=\"config-item\">\r\n                <span class=\"config-label\">Last Updated:</span>\r\n                <span class=\"config-value\">{{ currentSecurityConfig.lastUpdated | date:'medium' }}</span>\r\n              </div>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n      </div>\r\n\r\n      <!-- Whitelist Tab -->\r\n      <div *ngIf=\"activeTab === 'whitelist'\" class=\"tab-content\">\r\n        <!-- Add User Form -->\r\n        <mat-card class=\"form-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Add User to Whitelist</mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div class=\"form-row\">\r\n              <mat-form-field appearance=\"outline\" class=\"username-field\">\r\n                <mat-label>Username</mat-label>\r\n                <input\r\n                  matInput\r\n                  type=\"text\"\r\n                  name=\"newUsername\"\r\n                  [(ngModel)]=\"newUsername\"\r\n                  placeholder=\"Enter username\"\r\n                />\r\n              </mat-form-field>\r\n              <button\r\n                (click)=\"addUserToWhitelist()\"\r\n                [disabled]=\"!newUsername.trim()\"\r\n                mat-raised-button\r\n                color=\"primary\"\r\n                class=\"add-button\"\r\n              >\r\n                <mat-icon>add</mat-icon>\r\n                Add User\r\n              </button>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n\r\n        <!-- Users List -->\r\n        <mat-card class=\"list-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Allowed Users ({{ allowedUsers.length }})</mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div *ngIf=\"loading\" class=\"loading-state\">\r\n              <mat-spinner diameter=\"40\"></mat-spinner>\r\n              <p>Loading...</p>\r\n            </div>\r\n            <div *ngIf=\"!loading && allowedUsers.length === 0\" class=\"empty-state\">\r\n              <mat-icon>people_outline</mat-icon>\r\n              <p>No users in whitelist</p>\r\n            </div>\r\n            <div *ngIf=\"!loading && allowedUsers.length > 0\" class=\"user-list\">\r\n              <div *ngFor=\"let user of allowedUsers\" class=\"user-item\">\r\n                <div class=\"user-info\">\r\n                  <span class=\"user-name\">{{ user.username }}</span>\r\n                  <span class=\"user-details\">\r\n                    Added by {{ user.added_by }} on {{ formatDate(user.added_at) }}\r\n                  </span>\r\n                </div>\r\n                <div class=\"user-actions\">\r\n                  <mat-chip [class]=\"user.is_active ? 'active-chip' : 'inactive-chip'\">\r\n                    {{ user.is_active ? 'Active' : 'Inactive' }}\r\n                  </mat-chip>\r\n                  <button\r\n                    *ngIf=\"user.is_active\"\r\n                    (click)=\"removeUserFromWhitelist(user.username)\"\r\n                    mat-icon-button\r\n                    matTooltip=\"Remove User\"\r\n                    class=\"remove-button\"\r\n                  >\r\n                    <mat-icon>delete</mat-icon>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n      </div>\r\n\r\n      <!-- Invite Codes Tab -->\r\n      <div *ngIf=\"activeTab === 'invites'\" class=\"tab-content\">\r\n        <!-- Create Invite Code Form -->\r\n        <mat-card class=\"form-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Create Invite Code</mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div class=\"form-row\">\r\n              <mat-form-field appearance=\"outline\" class=\"field\">\r\n                <mat-label>Max Uses</mat-label>\r\n                <input\r\n                  matInput\r\n                  type=\"number\"\r\n                  [(ngModel)]=\"newInviteConfig.maxUses\"\r\n                  min=\"1\"\r\n                  placeholder=\"1\"\r\n                />\r\n              </mat-form-field>\r\n              <mat-form-field appearance=\"outline\" class=\"field\">\r\n                <mat-label>Expires In (Hours)</mat-label>\r\n                <input\r\n                  matInput\r\n                  type=\"number\"\r\n                  [(ngModel)]=\"newInviteConfig.expiresInHours\"\r\n                  min=\"1\"\r\n                  placeholder=\"24\"\r\n                />\r\n              </mat-form-field>\r\n              <mat-form-field appearance=\"outline\" class=\"field\">\r\n                <mat-label>Custom Code (Optional)</mat-label>\r\n                <input\r\n                  matInput\r\n                  type=\"text\"\r\n                  [(ngModel)]=\"newInviteConfig.customCode\"\r\n                  (input)=\"onCustomCodeInput($event)\"\r\n                  placeholder=\"Leave empty for auto-generated\"\r\n                />\r\n              </mat-form-field>\r\n              <button\r\n                (click)=\"createInviteCode()\"\r\n                mat-raised-button\r\n                color=\"primary\"\r\n                class=\"add-button\"\r\n              >\r\n                <mat-icon>add</mat-icon>\r\n                Create Code\r\n              </button>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n\r\n        <!-- Invite Codes List -->\r\n        <mat-card class=\"list-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Invite Codes ({{ inviteCodes.length }})</mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div *ngIf=\"loading\" class=\"loading-state\">\r\n              <mat-spinner diameter=\"40\"></mat-spinner>\r\n              <p>Loading...</p>\r\n            </div>\r\n            <div *ngIf=\"!loading && inviteCodes.length === 0\" class=\"empty-state\">\r\n              <mat-icon>vpn_key</mat-icon>\r\n              <p>No invite codes</p>\r\n            </div>\r\n            <div *ngIf=\"!loading && inviteCodes.length > 0\" class=\"invite-list\">\r\n              <div *ngFor=\"let code of inviteCodes\" class=\"invite-item\">\r\n                <div class=\"invite-info\">\r\n                  <span class=\"invite-code\">{{ code.code }}</span>\r\n                  <span class=\"invite-details\">\r\n                    Uses: {{ code.current_uses }}/{{ code.max_uses }} | \r\n                    Created by {{ code.created_by }} on {{ formatDate(code.created_at) }}\r\n                  </span>\r\n                  <span *ngIf=\"code.expires_at\" class=\"invite-expiry\">\r\n                    Expires: {{ formatDate(code.expires_at) }}\r\n                  </span>\r\n                </div>\r\n                <div class=\"invite-actions\">\r\n                  <mat-chip [class]=\"code.status === 'active' ? 'active-chip' : 'inactive-chip'\">\r\n                    {{ code.status }}\r\n                  </mat-chip>\r\n                  <button\r\n                    (click)=\"copyToClipboard(code.code)\"\r\n                    mat-icon-button\r\n                    matTooltip=\"Copy Code\"\r\n                    class=\"copy-button\"\r\n                  >\r\n                    <mat-icon>{{ copiedCode === code.code ? 'check' : 'content_copy' }}</mat-icon>\r\n                  </button>\r\n                  <button\r\n                    *ngIf=\"code.status === 'active'\"\r\n                    (click)=\"deactivateInviteCode(code.code)\"\r\n                    mat-icon-button\r\n                    matTooltip=\"Deactivate\"\r\n                    class=\"remove-button\"\r\n                  >\r\n                    <mat-icon>block</mat-icon>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n      </div>\r\n\r\n      <!-- Groups Tab -->\r\n      <div *ngIf=\"activeTab === 'groups'\" class=\"tab-content\">\r\n        <!-- Create Group Form -->\r\n        <mat-card class=\"form-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Create Group</mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div class=\"form-row\">\r\n              <mat-form-field appearance=\"outline\" class=\"field\">\r\n                <mat-label>Group Name</mat-label>\r\n                <input\r\n                  matInput\r\n                  type=\"text\"\r\n                  [(ngModel)]=\"newGroupName\"\r\n                  placeholder=\"Enter group name\"\r\n                />\r\n              </mat-form-field>\r\n              <mat-form-field appearance=\"outline\" class=\"field\">\r\n                <mat-label>Description (Optional)</mat-label>\r\n                <input\r\n                  matInput\r\n                  type=\"text\"\r\n                  [(ngModel)]=\"newGroupDescription\"\r\n                  placeholder=\"Enter group description\"\r\n                />\r\n              </mat-form-field>\r\n              <button\r\n                (click)=\"createGroup()\"\r\n                [disabled]=\"!newGroupName.trim()\"\r\n                mat-raised-button\r\n                color=\"primary\"\r\n                class=\"add-button\"\r\n              >\r\n                <mat-icon>add</mat-icon>\r\n                Create Group\r\n              </button>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n\r\n        <!-- Groups List -->\r\n        <mat-card class=\"list-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Groups ({{ groups.length }})</mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div *ngIf=\"loading\" class=\"loading-state\">\r\n              <mat-spinner diameter=\"40\"></mat-spinner>\r\n              <p>Loading...</p>\r\n            </div>\r\n            <div *ngIf=\"!loading && groups.length === 0\" class=\"empty-state\">\r\n              <mat-icon>group</mat-icon>\r\n              <p>No groups</p>\r\n            </div>\r\n            <div *ngIf=\"!loading && groups.length > 0\" class=\"group-list\">\r\n              <div *ngFor=\"let group of groups\" class=\"group-item\" [class.selected]=\"selectedGroup?.id === group.id\">\r\n                <div class=\"group-info\" (click)=\"onGroupSelect(group)\">\r\n                  <span class=\"group-name\">{{ group.name }}</span>\r\n                  <span *ngIf=\"group.description\" class=\"group-description\">{{ group.description }}</span>\r\n                  <span class=\"group-details\">\r\n                    Created by {{ group.created_by_username || 'Unknown' }}\r\n                  </span>\r\n                </div>\r\n                <div class=\"group-actions\">\r\n                  <button\r\n                    (click)=\"deleteGroup(group.id)\"\r\n                    mat-icon-button\r\n                    matTooltip=\"Delete Group\"\r\n                    class=\"remove-button\"\r\n                  >\r\n                    <mat-icon>delete</mat-icon>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n\r\n        <!-- Group Users Management -->\r\n        <mat-card *ngIf=\"selectedGroup\" class=\"list-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Group Users - {{ selectedGroup.name }}</mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div *ngIf=\"loading\" class=\"loading-state\">\r\n              <mat-spinner diameter=\"40\"></mat-spinner>\r\n              <p>Loading...</p>\r\n            </div>\r\n            <div *ngIf=\"!loading && groupUsers.length === 0\" class=\"empty-state\">\r\n              <mat-icon>person_outline</mat-icon>\r\n              <p>No users in this group</p>\r\n            </div>\r\n            <div *ngIf=\"!loading && groupUsers.length > 0\" class=\"user-list\">\r\n              <div *ngFor=\"let user of groupUsers\" class=\"user-item\">\r\n                <div class=\"user-info\">\r\n                  <span class=\"user-name\">{{ user.username }}</span>\r\n                  <span class=\"user-details\">\r\n                    Granted access on {{ formatDate(user.granted_at) }}\r\n                  </span>\r\n                </div>\r\n                <div class=\"user-actions\">\r\n                  <button\r\n                    (click)=\"revokeUserAccess(user.username)\"\r\n                    mat-icon-button\r\n                    matTooltip=\"Revoke Access\"\r\n                    class=\"remove-button\"\r\n                  >\r\n                    <mat-icon>remove_circle</mat-icon>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n      </div>\r\n\r\n      <!-- Users Tab -->\r\n      <div *ngIf=\"activeTab === 'users'\" class=\"tab-content\">\r\n        <!-- Create User Form -->\r\n        <mat-card class=\"form-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Create User</mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div class=\"form-row\">\r\n              <mat-form-field appearance=\"outline\" class=\"field\">\r\n                <mat-label>Username</mat-label>\r\n                <input\r\n                  matInput\r\n                  type=\"text\"\r\n                  [(ngModel)]=\"newUsername\"\r\n                  placeholder=\"Enter username\"\r\n                />\r\n              </mat-form-field>\r\n              <mat-form-field appearance=\"outline\" class=\"field\">\r\n                <mat-label>Password</mat-label>\r\n                <input\r\n                  matInput\r\n                  type=\"password\"\r\n                  [(ngModel)]=\"newUserPassword\"\r\n                  placeholder=\"Enter password\"\r\n                />\r\n              </mat-form-field>\r\n              <button\r\n                (click)=\"createUser()\"\r\n                [disabled]=\"!newUsername.trim() || !newUserPassword.trim()\"\r\n                mat-raised-button\r\n                color=\"primary\"\r\n                class=\"add-button\"\r\n              >\r\n                <mat-icon>add</mat-icon>\r\n                Create User\r\n              </button>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n\r\n        <!-- Users List -->\r\n        <mat-card class=\"list-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Users ({{ users.length }})</mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div *ngIf=\"loading\" class=\"loading-state\">\r\n              <mat-spinner diameter=\"40\"></mat-spinner>\r\n              <p>Loading...</p>\r\n            </div>\r\n            <div *ngIf=\"!loading && users.length === 0\" class=\"empty-state\">\r\n              <mat-icon>person</mat-icon>\r\n              <p>No users</p>\r\n            </div>\r\n            <div *ngIf=\"!loading && users.length > 0\" class=\"user-list\">\r\n              <div *ngFor=\"let user of users\" class=\"user-item\">\r\n                <div class=\"user-info\">\r\n                  <span class=\"user-name\">{{ user.username }}</span>\r\n                  <span class=\"user-details\">\r\n                    Last seen: {{ formatDate(user.last_seen) }} | \r\n                    Status: {{ user.online_status ? 'Online' : 'Offline' }}\r\n                  </span>\r\n                </div>\r\n                <div class=\"user-actions\">\r\n                  <mat-chip [class]=\"user.hasPassword ? 'active-chip' : 'inactive-chip'\">\r\n                    {{ user.hasPassword ? 'Has Password' : 'No Password' }}\r\n                  </mat-chip>\r\n                  <button\r\n                    (click)=\"openUserModal(user)\"\r\n                    mat-icon-button\r\n                    matTooltip=\"Manage User\"\r\n                    class=\"manage-button\"\r\n                  >\r\n                    <mat-icon>settings</mat-icon>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n      </div>\r\n    </mat-card-content>\r\n  </mat-card>\r\n</div>\r\n\r\n<!-- User Management Modal -->\r\n<div *ngIf=\"showUserModal\" class=\"modal-overlay\" (click)=\"closeUserModal()\">\r\n  <mat-card class=\"modal-container\" (click)=\"$event.stopPropagation()\">\r\n    <mat-card-header class=\"modal-header\">\r\n      <div class=\"header-content\">\r\n        <mat-icon class=\"modal-icon\">person</mat-icon>\r\n        <mat-card-title>Manage User: {{ selectedUser?.username }}</mat-card-title>\r\n      </div>\r\n      <button\r\n        (click)=\"closeUserModal()\"\r\n        mat-icon-button\r\n        matTooltip=\"Close\"\r\n        class=\"close-button\"\r\n      >\r\n        <mat-icon>close</mat-icon>\r\n      </button>\r\n    </mat-card-header>\r\n\r\n    <mat-card-content class=\"modal-content\">\r\n      <div class=\"user-info-section\">\r\n        <div class=\"info-item\">\r\n          <span class=\"info-label\">Username:</span>\r\n          <span class=\"info-value\">{{ selectedUser?.username }}</span>\r\n        </div>\r\n        <div class=\"info-item\">\r\n          <span class=\"info-label\">Status:</span>\r\n          <span class=\"info-value\">{{ selectedUser?.online_status ? 'Online' : 'Offline' }}</span>\r\n        </div>\r\n        <div class=\"info-item\">\r\n          <span class=\"info-label\">Last Seen:</span>\r\n          <span class=\"info-value\">{{ formatDate(selectedUser?.last_seen || '') }}</span>\r\n        </div>\r\n        <div class=\"info-item\">\r\n          <span class=\"info-label\">Password Status:</span>\r\n          <mat-chip [class]=\"selectedUser?.hasPassword ? 'active-chip' : 'inactive-chip'\">\r\n            {{ selectedUser?.hasPassword ? 'Has Password' : 'No Password' }}\r\n          </mat-chip>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"password-section\">\r\n        <mat-card class=\"password-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Set Password</mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n              <mat-label>New Password</mat-label>\r\n              <input\r\n                matInput\r\n                type=\"password\"\r\n                [(ngModel)]=\"newPassword\"\r\n                placeholder=\"Enter new password\"\r\n              />\r\n            </mat-form-field>\r\n            <button\r\n              (click)=\"setUserPassword()\"\r\n              [disabled]=\"!newPassword.trim()\"\r\n              mat-raised-button\r\n              color=\"primary\"\r\n              class=\"full-width\"\r\n            >\r\n              <mat-icon>lock</mat-icon>\r\n              Set Password\r\n            </button>\r\n          </mat-card-content>\r\n        </mat-card>\r\n\r\n        <mat-card class=\"password-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Reset Password</mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n              <mat-label>New Password</mat-label>\r\n              <input\r\n                matInput\r\n                type=\"password\"\r\n                [(ngModel)]=\"resetPassword\"\r\n                placeholder=\"Enter new password\"\r\n              />\r\n            </mat-form-field>\r\n            <button\r\n              (click)=\"resetUserPassword()\"\r\n              [disabled]=\"!resetPassword.trim()\"\r\n              mat-raised-button\r\n              color=\"warn\"\r\n              class=\"full-width\"\r\n            >\r\n              <mat-icon>refresh</mat-icon>\r\n              Reset Password\r\n            </button>\r\n          </mat-card-content>\r\n        </mat-card>\r\n      </div>\r\n    </mat-card-content>\r\n  </mat-card>\r\n</div>"]}, "metadata": {}, "sourceType": "module"}