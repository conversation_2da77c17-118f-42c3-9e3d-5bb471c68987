{"ast": null, "code": "// imported from https://github.com/socketio/base64-arraybuffer\nconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'; // Use a lookup table to find the index.\n\nconst lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\n\nfor (let i = 0; i < chars.length; i++) {\n  lookup[chars.charCodeAt(i)] = i;\n}\n\nexport const encode = arraybuffer => {\n  let bytes = new Uint8Array(arraybuffer),\n      i,\n      len = bytes.length,\n      base64 = '';\n\n  for (i = 0; i < len; i += 3) {\n    base64 += chars[bytes[i] >> 2];\n    base64 += chars[(bytes[i] & 3) << 4 | bytes[i + 1] >> 4];\n    base64 += chars[(bytes[i + 1] & 15) << 2 | bytes[i + 2] >> 6];\n    base64 += chars[bytes[i + 2] & 63];\n  }\n\n  if (len % 3 === 2) {\n    base64 = base64.substring(0, base64.length - 1) + '=';\n  } else if (len % 3 === 1) {\n    base64 = base64.substring(0, base64.length - 2) + '==';\n  }\n\n  return base64;\n};\nexport const decode = base64 => {\n  let bufferLength = base64.length * 0.75,\n      len = base64.length,\n      i,\n      p = 0,\n      encoded1,\n      encoded2,\n      encoded3,\n      encoded4;\n\n  if (base64[base64.length - 1] === '=') {\n    bufferLength--;\n\n    if (base64[base64.length - 2] === '=') {\n      bufferLength--;\n    }\n  }\n\n  const arraybuffer = new ArrayBuffer(bufferLength),\n        bytes = new Uint8Array(arraybuffer);\n\n  for (i = 0; i < len; i += 4) {\n    encoded1 = lookup[base64.charCodeAt(i)];\n    encoded2 = lookup[base64.charCodeAt(i + 1)];\n    encoded3 = lookup[base64.charCodeAt(i + 2)];\n    encoded4 = lookup[base64.charCodeAt(i + 3)];\n    bytes[p++] = encoded1 << 2 | encoded2 >> 4;\n    bytes[p++] = (encoded2 & 15) << 4 | encoded3 >> 2;\n    bytes[p++] = (encoded3 & 3) << 6 | encoded4 & 63;\n  }\n\n  return arraybuffer;\n};", "map": null, "metadata": {}, "sourceType": "module"}