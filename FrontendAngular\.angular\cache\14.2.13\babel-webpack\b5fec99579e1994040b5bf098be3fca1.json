{"ast": null, "code": "/**\n * @license Angular v14.3.0\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\nimport * as i1 from '@angular/common';\nimport { DOCUMENT, ɵparseCookieValue, XhrFactory as XhrFactory$1 } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, InjectionToken, Inject, PLATFORM_ID, NgModule } from '@angular/core';\nimport { of, Observable } from 'rxjs';\nimport { concatMap, filter, map } from 'rxjs/operators';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Transforms an `HttpRequest` into a stream of `HttpEvent`s, one of which will likely be a\n * `HttpResponse`.\n *\n * `HttpHandler` is injectable. When injected, the handler instance dispatches requests to the\n * first interceptor in the chain, which dispatches to the second, etc, eventually reaching the\n * `HttpBackend`.\n *\n * In an `HttpInterceptor`, the `HttpHandler` parameter is the next interceptor in the chain.\n *\n * @publicApi\n */\n\nclass HttpHandler {}\n/**\n * A final `HttpHandler` which will dispatch the request via browser HTTP APIs to a backend.\n *\n * Interceptors sit between the `HttpClient` interface and the `HttpBackend`.\n *\n * When injected, `HttpBackend` dispatches requests directly to the backend, without going\n * through the interceptor chain.\n *\n * @publicApi\n */\n\n\nclass HttpBackend {}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Represents the header configuration options for an HTTP request.\n * Instances are immutable. Modifying methods return a cloned\n * instance with the change. The original object is never changed.\n *\n * @publicApi\n */\n\n\nclass HttpHeaders {\n  /**  Constructs a new HTTP header object with the given values.*/\n  constructor(headers) {\n    /**\n     * Internal map of lowercased header names to the normalized\n     * form of the name (the form seen first).\n     */\n    this.normalizedNames = new Map();\n    /**\n     * Queued updates to be materialized the next initialization.\n     */\n\n    this.lazyUpdate = null;\n\n    if (!headers) {\n      this.headers = new Map();\n    } else if (typeof headers === 'string') {\n      this.lazyInit = () => {\n        this.headers = new Map();\n        headers.split('\\n').forEach(line => {\n          const index = line.indexOf(':');\n\n          if (index > 0) {\n            const name = line.slice(0, index);\n            const key = name.toLowerCase();\n            const value = line.slice(index + 1).trim();\n            this.maybeSetNormalizedName(name, key);\n\n            if (this.headers.has(key)) {\n              this.headers.get(key).push(value);\n            } else {\n              this.headers.set(key, [value]);\n            }\n          }\n        });\n      };\n    } else {\n      this.lazyInit = () => {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          assertValidHeaders(headers);\n        }\n\n        this.headers = new Map();\n        Object.keys(headers).forEach(name => {\n          let values = headers[name];\n          const key = name.toLowerCase();\n\n          if (typeof values === 'string') {\n            values = [values];\n          }\n\n          if (values.length > 0) {\n            this.headers.set(key, values);\n            this.maybeSetNormalizedName(name, key);\n          }\n        });\n      };\n    }\n  }\n  /**\n   * Checks for existence of a given header.\n   *\n   * @param name The header name to check for existence.\n   *\n   * @returns True if the header exists, false otherwise.\n   */\n\n\n  has(name) {\n    this.init();\n    return this.headers.has(name.toLowerCase());\n  }\n  /**\n   * Retrieves the first value of a given header.\n   *\n   * @param name The header name.\n   *\n   * @returns The value string if the header exists, null otherwise\n   */\n\n\n  get(name) {\n    this.init();\n    const values = this.headers.get(name.toLowerCase());\n    return values && values.length > 0 ? values[0] : null;\n  }\n  /**\n   * Retrieves the names of the headers.\n   *\n   * @returns A list of header names.\n   */\n\n\n  keys() {\n    this.init();\n    return Array.from(this.normalizedNames.values());\n  }\n  /**\n   * Retrieves a list of values for a given header.\n   *\n   * @param name The header name from which to retrieve values.\n   *\n   * @returns A string of values if the header exists, null otherwise.\n   */\n\n\n  getAll(name) {\n    this.init();\n    return this.headers.get(name.toLowerCase()) || null;\n  }\n  /**\n   * Appends a new value to the existing set of values for a header\n   * and returns them in a clone of the original instance.\n   *\n   * @param name The header name for which to append the values.\n   * @param value The value to append.\n   *\n   * @returns A clone of the HTTP headers object with the value appended to the given header.\n   */\n\n\n  append(name, value) {\n    return this.clone({\n      name,\n      value,\n      op: 'a'\n    });\n  }\n  /**\n   * Sets or modifies a value for a given header in a clone of the original instance.\n   * If the header already exists, its value is replaced with the given value\n   * in the returned object.\n   *\n   * @param name The header name.\n   * @param value The value or values to set or override for the given header.\n   *\n   * @returns A clone of the HTTP headers object with the newly set header value.\n   */\n\n\n  set(name, value) {\n    return this.clone({\n      name,\n      value,\n      op: 's'\n    });\n  }\n  /**\n   * Deletes values for a given header in a clone of the original instance.\n   *\n   * @param name The header name.\n   * @param value The value or values to delete for the given header.\n   *\n   * @returns A clone of the HTTP headers object with the given value deleted.\n   */\n\n\n  delete(name, value) {\n    return this.clone({\n      name,\n      value,\n      op: 'd'\n    });\n  }\n\n  maybeSetNormalizedName(name, lcName) {\n    if (!this.normalizedNames.has(lcName)) {\n      this.normalizedNames.set(lcName, name);\n    }\n  }\n\n  init() {\n    if (!!this.lazyInit) {\n      if (this.lazyInit instanceof HttpHeaders) {\n        this.copyFrom(this.lazyInit);\n      } else {\n        this.lazyInit();\n      }\n\n      this.lazyInit = null;\n\n      if (!!this.lazyUpdate) {\n        this.lazyUpdate.forEach(update => this.applyUpdate(update));\n        this.lazyUpdate = null;\n      }\n    }\n  }\n\n  copyFrom(other) {\n    other.init();\n    Array.from(other.headers.keys()).forEach(key => {\n      this.headers.set(key, other.headers.get(key));\n      this.normalizedNames.set(key, other.normalizedNames.get(key));\n    });\n  }\n\n  clone(update) {\n    const clone = new HttpHeaders();\n    clone.lazyInit = !!this.lazyInit && this.lazyInit instanceof HttpHeaders ? this.lazyInit : this;\n    clone.lazyUpdate = (this.lazyUpdate || []).concat([update]);\n    return clone;\n  }\n\n  applyUpdate(update) {\n    const key = update.name.toLowerCase();\n\n    switch (update.op) {\n      case 'a':\n      case 's':\n        let value = update.value;\n\n        if (typeof value === 'string') {\n          value = [value];\n        }\n\n        if (value.length === 0) {\n          return;\n        }\n\n        this.maybeSetNormalizedName(update.name, key);\n        const base = (update.op === 'a' ? this.headers.get(key) : undefined) || [];\n        base.push(...value);\n        this.headers.set(key, base);\n        break;\n\n      case 'd':\n        const toDelete = update.value;\n\n        if (!toDelete) {\n          this.headers.delete(key);\n          this.normalizedNames.delete(key);\n        } else {\n          let existing = this.headers.get(key);\n\n          if (!existing) {\n            return;\n          }\n\n          existing = existing.filter(value => toDelete.indexOf(value) === -1);\n\n          if (existing.length === 0) {\n            this.headers.delete(key);\n            this.normalizedNames.delete(key);\n          } else {\n            this.headers.set(key, existing);\n          }\n        }\n\n        break;\n    }\n  }\n  /**\n   * @internal\n   */\n\n\n  forEach(fn) {\n    this.init();\n    Array.from(this.normalizedNames.keys()).forEach(key => fn(this.normalizedNames.get(key), this.headers.get(key)));\n  }\n\n}\n/**\n * Verifies that the headers object has the right shape: the values\n * must be either strings or arrays. Throws an error if an invalid\n * header value is present.\n */\n\n\nfunction assertValidHeaders(headers) {\n  for (const [key, value] of Object.entries(headers)) {\n    if (typeof value !== 'string' && !Array.isArray(value)) {\n      throw new Error(`Unexpected value of the \\`${key}\\` header provided. ` + `Expecting either a string or an array, but got: \\`${value}\\`.`);\n    }\n  }\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Provides encoding and decoding of URL parameter and query-string values.\n *\n * Serializes and parses URL parameter keys and values to encode and decode them.\n * If you pass URL query parameters without encoding,\n * the query parameters can be misinterpreted at the receiving end.\n *\n *\n * @publicApi\n */\n\n\nclass HttpUrlEncodingCodec {\n  /**\n   * Encodes a key name for a URL parameter or query-string.\n   * @param key The key name.\n   * @returns The encoded key name.\n   */\n  encodeKey(key) {\n    return standardEncoding(key);\n  }\n  /**\n   * Encodes the value of a URL parameter or query-string.\n   * @param value The value.\n   * @returns The encoded value.\n   */\n\n\n  encodeValue(value) {\n    return standardEncoding(value);\n  }\n  /**\n   * Decodes an encoded URL parameter or query-string key.\n   * @param key The encoded key name.\n   * @returns The decoded key name.\n   */\n\n\n  decodeKey(key) {\n    return decodeURIComponent(key);\n  }\n  /**\n   * Decodes an encoded URL parameter or query-string value.\n   * @param value The encoded value.\n   * @returns The decoded value.\n   */\n\n\n  decodeValue(value) {\n    return decodeURIComponent(value);\n  }\n\n}\n\nfunction paramParser(rawParams, codec) {\n  const map = new Map();\n\n  if (rawParams.length > 0) {\n    // The `window.location.search` can be used while creating an instance of the `HttpParams` class\n    // (e.g. `new HttpParams({ fromString: window.location.search })`). The `window.location.search`\n    // may start with the `?` char, so we strip it if it's present.\n    const params = rawParams.replace(/^\\?/, '').split('&');\n    params.forEach(param => {\n      const eqIdx = param.indexOf('=');\n      const [key, val] = eqIdx == -1 ? [codec.decodeKey(param), ''] : [codec.decodeKey(param.slice(0, eqIdx)), codec.decodeValue(param.slice(eqIdx + 1))];\n      const list = map.get(key) || [];\n      list.push(val);\n      map.set(key, list);\n    });\n  }\n\n  return map;\n}\n/**\n * Encode input string with standard encodeURIComponent and then un-encode specific characters.\n */\n\n\nconst STANDARD_ENCODING_REGEX = /%(\\d[a-f0-9])/gi;\nconst STANDARD_ENCODING_REPLACEMENTS = {\n  '40': '@',\n  '3A': ':',\n  '24': '$',\n  '2C': ',',\n  '3B': ';',\n  '3D': '=',\n  '3F': '?',\n  '2F': '/'\n};\n\nfunction standardEncoding(v) {\n  return encodeURIComponent(v).replace(STANDARD_ENCODING_REGEX, (s, t) => STANDARD_ENCODING_REPLACEMENTS[t] ?? s);\n}\n\nfunction valueToString(value) {\n  return `${value}`;\n}\n/**\n * An HTTP request/response body that represents serialized parameters,\n * per the MIME type `application/x-www-form-urlencoded`.\n *\n * This class is immutable; all mutation operations return a new instance.\n *\n * @publicApi\n */\n\n\nclass HttpParams {\n  constructor(options = {}) {\n    this.updates = null;\n    this.cloneFrom = null;\n    this.encoder = options.encoder || new HttpUrlEncodingCodec();\n\n    if (!!options.fromString) {\n      if (!!options.fromObject) {\n        throw new Error(`Cannot specify both fromString and fromObject.`);\n      }\n\n      this.map = paramParser(options.fromString, this.encoder);\n    } else if (!!options.fromObject) {\n      this.map = new Map();\n      Object.keys(options.fromObject).forEach(key => {\n        const value = options.fromObject[key]; // convert the values to strings\n\n        const values = Array.isArray(value) ? value.map(valueToString) : [valueToString(value)];\n        this.map.set(key, values);\n      });\n    } else {\n      this.map = null;\n    }\n  }\n  /**\n   * Reports whether the body includes one or more values for a given parameter.\n   * @param param The parameter name.\n   * @returns True if the parameter has one or more values,\n   * false if it has no value or is not present.\n   */\n\n\n  has(param) {\n    this.init();\n    return this.map.has(param);\n  }\n  /**\n   * Retrieves the first value for a parameter.\n   * @param param The parameter name.\n   * @returns The first value of the given parameter,\n   * or `null` if the parameter is not present.\n   */\n\n\n  get(param) {\n    this.init();\n    const res = this.map.get(param);\n    return !!res ? res[0] : null;\n  }\n  /**\n   * Retrieves all values for a  parameter.\n   * @param param The parameter name.\n   * @returns All values in a string array,\n   * or `null` if the parameter not present.\n   */\n\n\n  getAll(param) {\n    this.init();\n    return this.map.get(param) || null;\n  }\n  /**\n   * Retrieves all the parameters for this body.\n   * @returns The parameter names in a string array.\n   */\n\n\n  keys() {\n    this.init();\n    return Array.from(this.map.keys());\n  }\n  /**\n   * Appends a new value to existing values for a parameter.\n   * @param param The parameter name.\n   * @param value The new value to add.\n   * @return A new body with the appended value.\n   */\n\n\n  append(param, value) {\n    return this.clone({\n      param,\n      value,\n      op: 'a'\n    });\n  }\n  /**\n   * Constructs a new body with appended values for the given parameter name.\n   * @param params parameters and values\n   * @return A new body with the new value.\n   */\n\n\n  appendAll(params) {\n    const updates = [];\n    Object.keys(params).forEach(param => {\n      const value = params[param];\n\n      if (Array.isArray(value)) {\n        value.forEach(_value => {\n          updates.push({\n            param,\n            value: _value,\n            op: 'a'\n          });\n        });\n      } else {\n        updates.push({\n          param,\n          value: value,\n          op: 'a'\n        });\n      }\n    });\n    return this.clone(updates);\n  }\n  /**\n   * Replaces the value for a parameter.\n   * @param param The parameter name.\n   * @param value The new value.\n   * @return A new body with the new value.\n   */\n\n\n  set(param, value) {\n    return this.clone({\n      param,\n      value,\n      op: 's'\n    });\n  }\n  /**\n   * Removes a given value or all values from a parameter.\n   * @param param The parameter name.\n   * @param value The value to remove, if provided.\n   * @return A new body with the given value removed, or with all values\n   * removed if no value is specified.\n   */\n\n\n  delete(param, value) {\n    return this.clone({\n      param,\n      value,\n      op: 'd'\n    });\n  }\n  /**\n   * Serializes the body to an encoded string, where key-value pairs (separated by `=`) are\n   * separated by `&`s.\n   */\n\n\n  toString() {\n    this.init();\n    return this.keys().map(key => {\n      const eKey = this.encoder.encodeKey(key); // `a: ['1']` produces `'a=1'`\n      // `b: []` produces `''`\n      // `c: ['1', '2']` produces `'c=1&c=2'`\n\n      return this.map.get(key).map(value => eKey + '=' + this.encoder.encodeValue(value)).join('&');\n    }) // filter out empty values because `b: []` produces `''`\n    // which results in `a=1&&c=1&c=2` instead of `a=1&c=1&c=2` if we don't\n    .filter(param => param !== '').join('&');\n  }\n\n  clone(update) {\n    const clone = new HttpParams({\n      encoder: this.encoder\n    });\n    clone.cloneFrom = this.cloneFrom || this;\n    clone.updates = (this.updates || []).concat(update);\n    return clone;\n  }\n\n  init() {\n    if (this.map === null) {\n      this.map = new Map();\n    }\n\n    if (this.cloneFrom !== null) {\n      this.cloneFrom.init();\n      this.cloneFrom.keys().forEach(key => this.map.set(key, this.cloneFrom.map.get(key)));\n      this.updates.forEach(update => {\n        switch (update.op) {\n          case 'a':\n          case 's':\n            const base = (update.op === 'a' ? this.map.get(update.param) : undefined) || [];\n            base.push(valueToString(update.value));\n            this.map.set(update.param, base);\n            break;\n\n          case 'd':\n            if (update.value !== undefined) {\n              let base = this.map.get(update.param) || [];\n              const idx = base.indexOf(valueToString(update.value));\n\n              if (idx !== -1) {\n                base.splice(idx, 1);\n              }\n\n              if (base.length > 0) {\n                this.map.set(update.param, base);\n              } else {\n                this.map.delete(update.param);\n              }\n            } else {\n              this.map.delete(update.param);\n              break;\n            }\n\n        }\n      });\n      this.cloneFrom = this.updates = null;\n    }\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * A token used to manipulate and access values stored in `HttpContext`.\n *\n * @publicApi\n */\n\n\nclass HttpContextToken {\n  constructor(defaultValue) {\n    this.defaultValue = defaultValue;\n  }\n\n}\n/**\n * Http context stores arbitrary user defined values and ensures type safety without\n * actually knowing the types. It is backed by a `Map` and guarantees that keys do not clash.\n *\n * This context is mutable and is shared between cloned requests unless explicitly specified.\n *\n * @usageNotes\n *\n * ### Usage Example\n *\n * ```typescript\n * // inside cache.interceptors.ts\n * export const IS_CACHE_ENABLED = new HttpContextToken<boolean>(() => false);\n *\n * export class CacheInterceptor implements HttpInterceptor {\n *\n *   intercept(req: HttpRequest<any>, delegate: HttpHandler): Observable<HttpEvent<any>> {\n *     if (req.context.get(IS_CACHE_ENABLED) === true) {\n *       return ...;\n *     }\n *     return delegate.handle(req);\n *   }\n * }\n *\n * // inside a service\n *\n * this.httpClient.get('/api/weather', {\n *   context: new HttpContext().set(IS_CACHE_ENABLED, true)\n * }).subscribe(...);\n * ```\n *\n * @publicApi\n */\n\n\nclass HttpContext {\n  constructor() {\n    this.map = new Map();\n  }\n  /**\n   * Store a value in the context. If a value is already present it will be overwritten.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   * @param value The value to store.\n   *\n   * @returns A reference to itself for easy chaining.\n   */\n\n\n  set(token, value) {\n    this.map.set(token, value);\n    return this;\n  }\n  /**\n   * Retrieve the value associated with the given token.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   *\n   * @returns The stored value or default if one is defined.\n   */\n\n\n  get(token) {\n    if (!this.map.has(token)) {\n      this.map.set(token, token.defaultValue());\n    }\n\n    return this.map.get(token);\n  }\n  /**\n   * Delete the value associated with the given token.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   *\n   * @returns A reference to itself for easy chaining.\n   */\n\n\n  delete(token) {\n    this.map.delete(token);\n    return this;\n  }\n  /**\n   * Checks for existence of a given token.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   *\n   * @returns True if the token exists, false otherwise.\n   */\n\n\n  has(token) {\n    return this.map.has(token);\n  }\n  /**\n   * @returns a list of tokens currently stored in the context.\n   */\n\n\n  keys() {\n    return this.map.keys();\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Determine whether the given HTTP method may include a body.\n */\n\n\nfunction mightHaveBody(method) {\n  switch (method) {\n    case 'DELETE':\n    case 'GET':\n    case 'HEAD':\n    case 'OPTIONS':\n    case 'JSONP':\n      return false;\n\n    default:\n      return true;\n  }\n}\n/**\n * Safely assert whether the given value is an ArrayBuffer.\n *\n * In some execution environments ArrayBuffer is not defined.\n */\n\n\nfunction isArrayBuffer(value) {\n  return typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer;\n}\n/**\n * Safely assert whether the given value is a Blob.\n *\n * In some execution environments Blob is not defined.\n */\n\n\nfunction isBlob(value) {\n  return typeof Blob !== 'undefined' && value instanceof Blob;\n}\n/**\n * Safely assert whether the given value is a FormData instance.\n *\n * In some execution environments FormData is not defined.\n */\n\n\nfunction isFormData(value) {\n  return typeof FormData !== 'undefined' && value instanceof FormData;\n}\n/**\n * Safely assert whether the given value is a URLSearchParams instance.\n *\n * In some execution environments URLSearchParams is not defined.\n */\n\n\nfunction isUrlSearchParams(value) {\n  return typeof URLSearchParams !== 'undefined' && value instanceof URLSearchParams;\n}\n/**\n * An outgoing HTTP request with an optional typed body.\n *\n * `HttpRequest` represents an outgoing request, including URL, method,\n * headers, body, and other request configuration options. Instances should be\n * assumed to be immutable. To modify a `HttpRequest`, the `clone`\n * method should be used.\n *\n * @publicApi\n */\n\n\nclass HttpRequest {\n  constructor(method, url, third, fourth) {\n    this.url = url;\n    /**\n     * The request body, or `null` if one isn't set.\n     *\n     * Bodies are not enforced to be immutable, as they can include a reference to any\n     * user-defined data type. However, interceptors should take care to preserve\n     * idempotence by treating them as such.\n     */\n\n    this.body = null;\n    /**\n     * Whether this request should be made in a way that exposes progress events.\n     *\n     * Progress events are expensive (change detection runs on each event) and so\n     * they should only be requested if the consumer intends to monitor them.\n     */\n\n    this.reportProgress = false;\n    /**\n     * Whether this request should be sent with outgoing credentials (cookies).\n     */\n\n    this.withCredentials = false;\n    /**\n     * The expected response type of the server.\n     *\n     * This is used to parse the response appropriately before returning it to\n     * the requestee.\n     */\n\n    this.responseType = 'json';\n    this.method = method.toUpperCase(); // Next, need to figure out which argument holds the HttpRequestInit\n    // options, if any.\n\n    let options; // Check whether a body argument is expected. The only valid way to omit\n    // the body argument is to use a known no-body method like GET.\n\n    if (mightHaveBody(this.method) || !!fourth) {\n      // Body is the third argument, options are the fourth.\n      this.body = third !== undefined ? third : null;\n      options = fourth;\n    } else {\n      // No body required, options are the third argument. The body stays null.\n      options = third;\n    } // If options have been passed, interpret them.\n\n\n    if (options) {\n      // Normalize reportProgress and withCredentials.\n      this.reportProgress = !!options.reportProgress;\n      this.withCredentials = !!options.withCredentials; // Override default response type of 'json' if one is provided.\n\n      if (!!options.responseType) {\n        this.responseType = options.responseType;\n      } // Override headers if they're provided.\n\n\n      if (!!options.headers) {\n        this.headers = options.headers;\n      }\n\n      if (!!options.context) {\n        this.context = options.context;\n      }\n\n      if (!!options.params) {\n        this.params = options.params;\n      }\n    } // If no headers have been passed in, construct a new HttpHeaders instance.\n\n\n    if (!this.headers) {\n      this.headers = new HttpHeaders();\n    } // If no context have been passed in, construct a new HttpContext instance.\n\n\n    if (!this.context) {\n      this.context = new HttpContext();\n    } // If no parameters have been passed in, construct a new HttpUrlEncodedParams instance.\n\n\n    if (!this.params) {\n      this.params = new HttpParams();\n      this.urlWithParams = url;\n    } else {\n      // Encode the parameters to a string in preparation for inclusion in the URL.\n      const params = this.params.toString();\n\n      if (params.length === 0) {\n        // No parameters, the visible URL is just the URL given at creation time.\n        this.urlWithParams = url;\n      } else {\n        // Does the URL already have query parameters? Look for '?'.\n        const qIdx = url.indexOf('?'); // There are 3 cases to handle:\n        // 1) No existing parameters -> append '?' followed by params.\n        // 2) '?' exists and is followed by existing query string ->\n        //    append '&' followed by params.\n        // 3) '?' exists at the end of the url -> append params directly.\n        // This basically amounts to determining the character, if any, with\n        // which to join the URL and parameters.\n\n        const sep = qIdx === -1 ? '?' : qIdx < url.length - 1 ? '&' : '';\n        this.urlWithParams = url + sep + params;\n      }\n    }\n  }\n  /**\n   * Transform the free-form body into a serialized format suitable for\n   * transmission to the server.\n   */\n\n\n  serializeBody() {\n    // If no body is present, no need to serialize it.\n    if (this.body === null) {\n      return null;\n    } // Check whether the body is already in a serialized form. If so,\n    // it can just be returned directly.\n\n\n    if (isArrayBuffer(this.body) || isBlob(this.body) || isFormData(this.body) || isUrlSearchParams(this.body) || typeof this.body === 'string') {\n      return this.body;\n    } // Check whether the body is an instance of HttpUrlEncodedParams.\n\n\n    if (this.body instanceof HttpParams) {\n      return this.body.toString();\n    } // Check whether the body is an object or array, and serialize with JSON if so.\n\n\n    if (typeof this.body === 'object' || typeof this.body === 'boolean' || Array.isArray(this.body)) {\n      return JSON.stringify(this.body);\n    } // Fall back on toString() for everything else.\n\n\n    return this.body.toString();\n  }\n  /**\n   * Examine the body and attempt to infer an appropriate MIME type\n   * for it.\n   *\n   * If no such type can be inferred, this method will return `null`.\n   */\n\n\n  detectContentTypeHeader() {\n    // An empty body has no content type.\n    if (this.body === null) {\n      return null;\n    } // FormData bodies rely on the browser's content type assignment.\n\n\n    if (isFormData(this.body)) {\n      return null;\n    } // Blobs usually have their own content type. If it doesn't, then\n    // no type can be inferred.\n\n\n    if (isBlob(this.body)) {\n      return this.body.type || null;\n    } // Array buffers have unknown contents and thus no type can be inferred.\n\n\n    if (isArrayBuffer(this.body)) {\n      return null;\n    } // Technically, strings could be a form of JSON data, but it's safe enough\n    // to assume they're plain strings.\n\n\n    if (typeof this.body === 'string') {\n      return 'text/plain';\n    } // `HttpUrlEncodedParams` has its own content-type.\n\n\n    if (this.body instanceof HttpParams) {\n      return 'application/x-www-form-urlencoded;charset=UTF-8';\n    } // Arrays, objects, boolean and numbers will be encoded as JSON.\n\n\n    if (typeof this.body === 'object' || typeof this.body === 'number' || typeof this.body === 'boolean') {\n      return 'application/json';\n    } // No type could be inferred.\n\n\n    return null;\n  }\n\n  clone(update = {}) {\n    // For method, url, and responseType, take the current value unless\n    // it is overridden in the update hash.\n    const method = update.method || this.method;\n    const url = update.url || this.url;\n    const responseType = update.responseType || this.responseType; // The body is somewhat special - a `null` value in update.body means\n    // whatever current body is present is being overridden with an empty\n    // body, whereas an `undefined` value in update.body implies no\n    // override.\n\n    const body = update.body !== undefined ? update.body : this.body; // Carefully handle the boolean options to differentiate between\n    // `false` and `undefined` in the update args.\n\n    const withCredentials = update.withCredentials !== undefined ? update.withCredentials : this.withCredentials;\n    const reportProgress = update.reportProgress !== undefined ? update.reportProgress : this.reportProgress; // Headers and params may be appended to if `setHeaders` or\n    // `setParams` are used.\n\n    let headers = update.headers || this.headers;\n    let params = update.params || this.params; // Pass on context if needed\n\n    const context = update.context ?? this.context; // Check whether the caller has asked to add headers.\n\n    if (update.setHeaders !== undefined) {\n      // Set every requested header.\n      headers = Object.keys(update.setHeaders).reduce((headers, name) => headers.set(name, update.setHeaders[name]), headers);\n    } // Check whether the caller has asked to set params.\n\n\n    if (update.setParams) {\n      // Set every requested param.\n      params = Object.keys(update.setParams).reduce((params, param) => params.set(param, update.setParams[param]), params);\n    } // Finally, construct the new HttpRequest using the pieces from above.\n\n\n    return new HttpRequest(method, url, body, {\n      params,\n      headers,\n      context,\n      reportProgress,\n      responseType,\n      withCredentials\n    });\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Type enumeration for the different kinds of `HttpEvent`.\n *\n * @publicApi\n */\n\n\nvar HttpEventType = /*#__PURE__*/(() => {\n  HttpEventType = HttpEventType || {};\n\n  /**\n   * The request was sent out over the wire.\n   */\n  HttpEventType[HttpEventType[\"Sent\"] = 0] = \"Sent\";\n  /**\n   * An upload progress event was received.\n   */\n\n  HttpEventType[HttpEventType[\"UploadProgress\"] = 1] = \"UploadProgress\";\n  /**\n   * The response status code and headers were received.\n   */\n\n  HttpEventType[HttpEventType[\"ResponseHeader\"] = 2] = \"ResponseHeader\";\n  /**\n   * A download progress event was received.\n   */\n\n  HttpEventType[HttpEventType[\"DownloadProgress\"] = 3] = \"DownloadProgress\";\n  /**\n   * The full response including the body was received.\n   */\n\n  HttpEventType[HttpEventType[\"Response\"] = 4] = \"Response\";\n  /**\n   * A custom event from an interceptor or a backend.\n   */\n\n  HttpEventType[HttpEventType[\"User\"] = 5] = \"User\";\n  return HttpEventType;\n})();\n\n/**\n * Base class for both `HttpResponse` and `HttpHeaderResponse`.\n *\n * @publicApi\n */\nclass HttpResponseBase {\n  /**\n   * Super-constructor for all responses.\n   *\n   * The single parameter accepted is an initialization hash. Any properties\n   * of the response passed there will override the default values.\n   */\n  constructor(init, defaultStatus = 200\n  /* HttpStatusCode.Ok */\n  , defaultStatusText = 'OK') {\n    // If the hash has values passed, use them to initialize the response.\n    // Otherwise use the default values.\n    this.headers = init.headers || new HttpHeaders();\n    this.status = init.status !== undefined ? init.status : defaultStatus;\n    this.statusText = init.statusText || defaultStatusText;\n    this.url = init.url || null; // Cache the ok value to avoid defining a getter.\n\n    this.ok = this.status >= 200 && this.status < 300;\n  }\n\n}\n/**\n * A partial HTTP response which only includes the status and header data,\n * but no response body.\n *\n * `HttpHeaderResponse` is a `HttpEvent` available on the response\n * event stream, only when progress events are requested.\n *\n * @publicApi\n */\n\n\nclass HttpHeaderResponse extends HttpResponseBase {\n  /**\n   * Create a new `HttpHeaderResponse` with the given parameters.\n   */\n  constructor(init = {}) {\n    super(init);\n    this.type = HttpEventType.ResponseHeader;\n  }\n  /**\n   * Copy this `HttpHeaderResponse`, overriding its contents with the\n   * given parameter hash.\n   */\n\n\n  clone(update = {}) {\n    // Perform a straightforward initialization of the new HttpHeaderResponse,\n    // overriding the current parameters with new ones if given.\n    return new HttpHeaderResponse({\n      headers: update.headers || this.headers,\n      status: update.status !== undefined ? update.status : this.status,\n      statusText: update.statusText || this.statusText,\n      url: update.url || this.url || undefined\n    });\n  }\n\n}\n/**\n * A full HTTP response, including a typed response body (which may be `null`\n * if one was not returned).\n *\n * `HttpResponse` is a `HttpEvent` available on the response event\n * stream.\n *\n * @publicApi\n */\n\n\nclass HttpResponse extends HttpResponseBase {\n  /**\n   * Construct a new `HttpResponse`.\n   */\n  constructor(init = {}) {\n    super(init);\n    this.type = HttpEventType.Response;\n    this.body = init.body !== undefined ? init.body : null;\n  }\n\n  clone(update = {}) {\n    return new HttpResponse({\n      body: update.body !== undefined ? update.body : this.body,\n      headers: update.headers || this.headers,\n      status: update.status !== undefined ? update.status : this.status,\n      statusText: update.statusText || this.statusText,\n      url: update.url || this.url || undefined\n    });\n  }\n\n}\n/**\n * A response that represents an error or failure, either from a\n * non-successful HTTP status, an error while executing the request,\n * or some other failure which occurred during the parsing of the response.\n *\n * Any error returned on the `Observable` response stream will be\n * wrapped in an `HttpErrorResponse` to provide additional context about\n * the state of the HTTP layer when the error occurred. The error property\n * will contain either a wrapped Error object or the error response returned\n * from the server.\n *\n * @publicApi\n */\n\n\nclass HttpErrorResponse extends HttpResponseBase {\n  constructor(init) {\n    // Initialize with a default status of 0 / Unknown Error.\n    super(init, 0, 'Unknown Error');\n    this.name = 'HttpErrorResponse';\n    /**\n     * Errors are never okay, even when the status code is in the 2xx success range.\n     */\n\n    this.ok = false; // If the response was successful, then this was a parse error. Otherwise, it was\n    // a protocol-level failure of some sort. Either the request failed in transit\n    // or the server returned an unsuccessful status code.\n\n    if (this.status >= 200 && this.status < 300) {\n      this.message = `Http failure during parsing for ${init.url || '(unknown url)'}`;\n    } else {\n      this.message = `Http failure response for ${init.url || '(unknown url)'}: ${init.status} ${init.statusText}`;\n    }\n\n    this.error = init.error || null;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Constructs an instance of `HttpRequestOptions<T>` from a source `HttpMethodOptions` and\n * the given `body`. This function clones the object and adds the body.\n *\n * Note that the `responseType` *options* value is a String that identifies the\n * single data type of the response.\n * A single overload version of the method handles each response type.\n * The value of `responseType` cannot be a union, as the combined signature could imply.\n *\n */\n\n\nfunction addBody(options, body) {\n  return {\n    body,\n    headers: options.headers,\n    context: options.context,\n    observe: options.observe,\n    params: options.params,\n    reportProgress: options.reportProgress,\n    responseType: options.responseType,\n    withCredentials: options.withCredentials\n  };\n}\n/**\n * Performs HTTP requests.\n * This service is available as an injectable class, with methods to perform HTTP requests.\n * Each request method has multiple signatures, and the return type varies based on\n * the signature that is called (mainly the values of `observe` and `responseType`).\n *\n * Note that the `responseType` *options* value is a String that identifies the\n * single data type of the response.\n * A single overload version of the method handles each response type.\n * The value of `responseType` cannot be a union, as the combined signature could imply.\n\n *\n * @usageNotes\n * Sample HTTP requests for the [Tour of Heroes](/tutorial/toh-pt0) application.\n *\n * ### HTTP Request Example\n *\n * ```\n *  // GET heroes whose name contains search term\n * searchHeroes(term: string): observable<Hero[]>{\n *\n *  const params = new HttpParams({fromString: 'name=term'});\n *    return this.httpClient.request('GET', this.heroesUrl, {responseType:'json', params});\n * }\n * ```\n *\n * Alternatively, the parameter string can be used without invoking HttpParams\n * by directly joining to the URL.\n * ```\n * this.httpClient.request('GET', this.heroesUrl + '?' + 'name=term', {responseType:'json'});\n * ```\n *\n *\n * ### JSONP Example\n * ```\n * requestJsonp(url, callback = 'callback') {\n *  return this.httpClient.jsonp(this.heroesURL, callback);\n * }\n * ```\n *\n * ### PATCH Example\n * ```\n * // PATCH one of the heroes' name\n * patchHero (id: number, heroName: string): Observable<{}> {\n * const url = `${this.heroesUrl}/${id}`;   // PATCH api/heroes/42\n *  return this.httpClient.patch(url, {name: heroName}, httpOptions)\n *    .pipe(catchError(this.handleError('patchHero')));\n * }\n * ```\n *\n * @see [HTTP Guide](guide/http)\n * @see [HTTP Request](api/common/http/HttpRequest)\n *\n * @publicApi\n */\n\n\nlet HttpClient = /*#__PURE__*/(() => {\n  class HttpClient {\n    constructor(handler) {\n      this.handler = handler;\n    }\n    /**\n     * Constructs an observable for a generic HTTP request that, when subscribed,\n     * fires the request through the chain of registered interceptors and on to the\n     * server.\n     *\n     * You can pass an `HttpRequest` directly as the only parameter. In this case,\n     * the call returns an observable of the raw `HttpEvent` stream.\n     *\n     * Alternatively you can pass an HTTP method as the first parameter,\n     * a URL string as the second, and an options hash containing the request body as the third.\n     * See `addBody()`. In this case, the specified `responseType` and `observe` options determine the\n     * type of returned observable.\n     *   * The `responseType` value determines how a successful response body is parsed.\n     *   * If `responseType` is the default `json`, you can pass a type interface for the resulting\n     * object as a type parameter to the call.\n     *\n     * The `observe` value determines the return type, according to what you are interested in\n     * observing.\n     *   * An `observe` value of events returns an observable of the raw `HttpEvent` stream, including\n     * progress events by default.\n     *   * An `observe` value of response returns an observable of `HttpResponse<T>`,\n     * where the `T` parameter depends on the `responseType` and any optionally provided type\n     * parameter.\n     *   * An `observe` value of body returns an observable of `<T>` with the same `T` body type.\n     *\n     */\n\n\n    request(first, url, options = {}) {\n      let req; // First, check whether the primary argument is an instance of `HttpRequest`.\n\n      if (first instanceof HttpRequest) {\n        // It is. The other arguments must be undefined (per the signatures) and can be\n        // ignored.\n        req = first;\n      } else {\n        // It's a string, so it represents a URL. Construct a request based on it,\n        // and incorporate the remaining arguments (assuming `GET` unless a method is\n        // provided.\n        // Figure out the headers.\n        let headers = undefined;\n\n        if (options.headers instanceof HttpHeaders) {\n          headers = options.headers;\n        } else {\n          headers = new HttpHeaders(options.headers);\n        } // Sort out parameters.\n\n\n        let params = undefined;\n\n        if (!!options.params) {\n          if (options.params instanceof HttpParams) {\n            params = options.params;\n          } else {\n            params = new HttpParams({\n              fromObject: options.params\n            });\n          }\n        } // Construct the request.\n\n\n        req = new HttpRequest(first, url, options.body !== undefined ? options.body : null, {\n          headers,\n          context: options.context,\n          params,\n          reportProgress: options.reportProgress,\n          // By default, JSON is assumed to be returned for all calls.\n          responseType: options.responseType || 'json',\n          withCredentials: options.withCredentials\n        });\n      } // Start with an Observable.of() the initial request, and run the handler (which\n      // includes all interceptors) inside a concatMap(). This way, the handler runs\n      // inside an Observable chain, which causes interceptors to be re-run on every\n      // subscription (this also makes retries re-run the handler, including interceptors).\n\n\n      const events$ = of(req).pipe(concatMap(req => this.handler.handle(req))); // If coming via the API signature which accepts a previously constructed HttpRequest,\n      // the only option is to get the event stream. Otherwise, return the event stream if\n      // that is what was requested.\n\n      if (first instanceof HttpRequest || options.observe === 'events') {\n        return events$;\n      } // The requested stream contains either the full response or the body. In either\n      // case, the first step is to filter the event stream to extract a stream of\n      // responses(s).\n\n\n      const res$ = events$.pipe(filter(event => event instanceof HttpResponse)); // Decide which stream to return.\n\n      switch (options.observe || 'body') {\n        case 'body':\n          // The requested stream is the body. Map the response stream to the response\n          // body. This could be done more simply, but a misbehaving interceptor might\n          // transform the response body into a different format and ignore the requested\n          // responseType. Guard against this by validating that the response is of the\n          // requested type.\n          switch (req.responseType) {\n            case 'arraybuffer':\n              return res$.pipe(map(res => {\n                // Validate that the body is an ArrayBuffer.\n                if (res.body !== null && !(res.body instanceof ArrayBuffer)) {\n                  throw new Error('Response is not an ArrayBuffer.');\n                }\n\n                return res.body;\n              }));\n\n            case 'blob':\n              return res$.pipe(map(res => {\n                // Validate that the body is a Blob.\n                if (res.body !== null && !(res.body instanceof Blob)) {\n                  throw new Error('Response is not a Blob.');\n                }\n\n                return res.body;\n              }));\n\n            case 'text':\n              return res$.pipe(map(res => {\n                // Validate that the body is a string.\n                if (res.body !== null && typeof res.body !== 'string') {\n                  throw new Error('Response is not a string.');\n                }\n\n                return res.body;\n              }));\n\n            case 'json':\n            default:\n              // No validation needed for JSON responses, as they can be of any type.\n              return res$.pipe(map(res => res.body));\n          }\n\n        case 'response':\n          // The response stream was requested directly, so return it.\n          return res$;\n\n        default:\n          // Guard against new future observe types being added.\n          throw new Error(`Unreachable: unhandled observe type ${options.observe}}`);\n      }\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `DELETE` request to execute on the server. See the individual overloads for\n     * details on the return type.\n     *\n     * @param url     The endpoint URL.\n     * @param options The HTTP options to send with the request.\n     *\n     */\n\n\n    delete(url, options = {}) {\n      return this.request('DELETE', url, options);\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `GET` request to execute on the server. See the individual overloads for\n     * details on the return type.\n     */\n\n\n    get(url, options = {}) {\n      return this.request('GET', url, options);\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `HEAD` request to execute on the server. The `HEAD` method returns\n     * meta information about the resource without transferring the\n     * resource itself. See the individual overloads for\n     * details on the return type.\n     */\n\n\n    head(url, options = {}) {\n      return this.request('HEAD', url, options);\n    }\n    /**\n     * Constructs an `Observable` that, when subscribed, causes a request with the special method\n     * `JSONP` to be dispatched via the interceptor pipeline.\n     * The [JSONP pattern](https://en.wikipedia.org/wiki/JSONP) works around limitations of certain\n     * API endpoints that don't support newer,\n     * and preferable [CORS](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS) protocol.\n     * JSONP treats the endpoint API as a JavaScript file and tricks the browser to process the\n     * requests even if the API endpoint is not located on the same domain (origin) as the client-side\n     * application making the request.\n     * The endpoint API must support JSONP callback for JSONP requests to work.\n     * The resource API returns the JSON response wrapped in a callback function.\n     * You can pass the callback function name as one of the query parameters.\n     * Note that JSONP requests can only be used with `GET` requests.\n     *\n     * @param url The resource URL.\n     * @param callbackParam The callback function name.\n     *\n     */\n\n\n    jsonp(url, callbackParam) {\n      return this.request('JSONP', url, {\n        params: new HttpParams().append(callbackParam, 'JSONP_CALLBACK'),\n        observe: 'body',\n        responseType: 'json'\n      });\n    }\n    /**\n     * Constructs an `Observable` that, when subscribed, causes the configured\n     * `OPTIONS` request to execute on the server. This method allows the client\n     * to determine the supported HTTP methods and other capabilities of an endpoint,\n     * without implying a resource action. See the individual overloads for\n     * details on the return type.\n     */\n\n\n    options(url, options = {}) {\n      return this.request('OPTIONS', url, options);\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `PATCH` request to execute on the server. See the individual overloads for\n     * details on the return type.\n     */\n\n\n    patch(url, body, options = {}) {\n      return this.request('PATCH', url, addBody(options, body));\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `POST` request to execute on the server. The server responds with the location of\n     * the replaced resource. See the individual overloads for\n     * details on the return type.\n     */\n\n\n    post(url, body, options = {}) {\n      return this.request('POST', url, addBody(options, body));\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `PUT` request to execute on the server. The `PUT` method replaces an existing resource\n     * with a new set of values.\n     * See the individual overloads for details on the return type.\n     */\n\n\n    put(url, body, options = {}) {\n      return this.request('PUT', url, addBody(options, body));\n    }\n\n  }\n\n  HttpClient.ɵfac = function HttpClient_Factory(t) {\n    return new (t || HttpClient)(i0.ɵɵinject(HttpHandler));\n  };\n\n  HttpClient.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HttpClient,\n    factory: HttpClient.ɵfac\n  });\n  return HttpClient;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * `HttpHandler` which applies an `HttpInterceptor` to an `HttpRequest`.\n *\n *\n */\n\n\nclass HttpInterceptorHandler {\n  constructor(next, interceptor) {\n    this.next = next;\n    this.interceptor = interceptor;\n  }\n\n  handle(req) {\n    return this.interceptor.intercept(req, this.next);\n  }\n\n}\n/**\n * A multi-provider token that represents the array of registered\n * `HttpInterceptor` objects.\n *\n * @publicApi\n */\n\n\nconst HTTP_INTERCEPTORS = /*#__PURE__*/new InjectionToken('HTTP_INTERCEPTORS');\nlet NoopInterceptor = /*#__PURE__*/(() => {\n  class NoopInterceptor {\n    intercept(req, next) {\n      return next.handle(req);\n    }\n\n  }\n\n  NoopInterceptor.ɵfac = function NoopInterceptor_Factory(t) {\n    return new (t || NoopInterceptor)();\n  };\n\n  NoopInterceptor.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NoopInterceptor,\n    factory: NoopInterceptor.ɵfac\n  });\n  return NoopInterceptor;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Every request made through JSONP needs a callback name that's unique across the\n// whole page. Each request is assigned an id and the callback name is constructed\n// from that. The next id to be assigned is tracked in a global variable here that\n// is shared among all applications on the page.\n\n\nlet nextRequestId = 0;\n/**\n * When a pending <script> is unsubscribed we'll move it to this document, so it won't be\n * executed.\n */\n\nlet foreignDocument; // Error text given when a JSONP script is injected, but doesn't invoke the callback\n// passed in its URL.\n\nconst JSONP_ERR_NO_CALLBACK = 'JSONP injected script did not invoke callback.'; // Error text given when a request is passed to the JsonpClientBackend that doesn't\n// have a request method JSONP.\n\nconst JSONP_ERR_WRONG_METHOD = 'JSONP requests must use JSONP request method.';\nconst JSONP_ERR_WRONG_RESPONSE_TYPE = 'JSONP requests must use Json response type.'; // Error text given when a request is passed to the JsonpClientBackend that has\n// headers set\n\nconst JSONP_ERR_HEADERS_NOT_SUPPORTED = 'JSONP requests do not support headers.';\n/**\n * DI token/abstract type representing a map of JSONP callbacks.\n *\n * In the browser, this should always be the `window` object.\n *\n *\n */\n\nclass JsonpCallbackContext {}\n/**\n * Processes an `HttpRequest` with the JSONP method,\n * by performing JSONP style requests.\n * @see `HttpHandler`\n * @see `HttpXhrBackend`\n *\n * @publicApi\n */\n\n\nlet JsonpClientBackend = /*#__PURE__*/(() => {\n  class JsonpClientBackend {\n    constructor(callbackMap, document) {\n      this.callbackMap = callbackMap;\n      this.document = document;\n      /**\n       * A resolved promise that can be used to schedule microtasks in the event handlers.\n       */\n\n      this.resolvedPromise = Promise.resolve();\n    }\n    /**\n     * Get the name of the next callback method, by incrementing the global `nextRequestId`.\n     */\n\n\n    nextCallback() {\n      return `ng_jsonp_callback_${nextRequestId++}`;\n    }\n    /**\n     * Processes a JSONP request and returns an event stream of the results.\n     * @param req The request object.\n     * @returns An observable of the response events.\n     *\n     */\n\n\n    handle(req) {\n      // Firstly, check both the method and response type. If either doesn't match\n      // then the request was improperly routed here and cannot be handled.\n      if (req.method !== 'JSONP') {\n        throw new Error(JSONP_ERR_WRONG_METHOD);\n      } else if (req.responseType !== 'json') {\n        throw new Error(JSONP_ERR_WRONG_RESPONSE_TYPE);\n      } // Check the request headers. JSONP doesn't support headers and\n      // cannot set any that were supplied.\n\n\n      if (req.headers.keys().length > 0) {\n        throw new Error(JSONP_ERR_HEADERS_NOT_SUPPORTED);\n      } // Everything else happens inside the Observable boundary.\n\n\n      return new Observable(observer => {\n        // The first step to make a request is to generate the callback name, and replace the\n        // callback placeholder in the URL with the name. Care has to be taken here to ensure\n        // a trailing &, if matched, gets inserted back into the URL in the correct place.\n        const callback = this.nextCallback();\n        const url = req.urlWithParams.replace(/=JSONP_CALLBACK(&|$)/, `=${callback}$1`); // Construct the <script> tag and point it at the URL.\n\n        const node = this.document.createElement('script');\n        node.src = url; // A JSONP request requires waiting for multiple callbacks. These variables\n        // are closed over and track state across those callbacks.\n        // The response object, if one has been received, or null otherwise.\n\n        let body = null; // Whether the response callback has been called.\n\n        let finished = false; // Set the response callback in this.callbackMap (which will be the window\n        // object in the browser. The script being loaded via the <script> tag will\n        // eventually call this callback.\n\n        this.callbackMap[callback] = data => {\n          // Data has been received from the JSONP script. Firstly, delete this callback.\n          delete this.callbackMap[callback]; // Set state to indicate data was received.\n\n          body = data;\n          finished = true;\n        }; // cleanup() is a utility closure that removes the <script> from the page and\n        // the response callback from the window. This logic is used in both the\n        // success, error, and cancellation paths, so it's extracted out for convenience.\n\n\n        const cleanup = () => {\n          // Remove the <script> tag if it's still on the page.\n          if (node.parentNode) {\n            node.parentNode.removeChild(node);\n          } // Remove the response callback from the callbackMap (window object in the\n          // browser).\n\n\n          delete this.callbackMap[callback];\n        }; // onLoad() is the success callback which runs after the response callback\n        // if the JSONP script loads successfully. The event itself is unimportant.\n        // If something went wrong, onLoad() may run without the response callback\n        // having been invoked.\n\n\n        const onLoad = event => {\n          // We wrap it in an extra Promise, to ensure the microtask\n          // is scheduled after the loaded endpoint has executed any potential microtask itself,\n          // which is not guaranteed in Internet Explorer and EdgeHTML. See issue #39496\n          this.resolvedPromise.then(() => {\n            // Cleanup the page.\n            cleanup(); // Check whether the response callback has run.\n\n            if (!finished) {\n              // It hasn't, something went wrong with the request. Return an error via\n              // the Observable error path. All JSONP errors have status 0.\n              observer.error(new HttpErrorResponse({\n                url,\n                status: 0,\n                statusText: 'JSONP Error',\n                error: new Error(JSONP_ERR_NO_CALLBACK)\n              }));\n              return;\n            } // Success. body either contains the response body or null if none was\n            // returned.\n\n\n            observer.next(new HttpResponse({\n              body,\n              status: 200\n              /* HttpStatusCode.Ok */\n              ,\n              statusText: 'OK',\n              url\n            })); // Complete the stream, the response is over.\n\n            observer.complete();\n          });\n        }; // onError() is the error callback, which runs if the script returned generates\n        // a Javascript error. It emits the error via the Observable error channel as\n        // a HttpErrorResponse.\n\n\n        const onError = error => {\n          cleanup(); // Wrap the error in a HttpErrorResponse.\n\n          observer.error(new HttpErrorResponse({\n            error,\n            status: 0,\n            statusText: 'JSONP Error',\n            url\n          }));\n        }; // Subscribe to both the success (load) and error events on the <script> tag,\n        // and add it to the page.\n\n\n        node.addEventListener('load', onLoad);\n        node.addEventListener('error', onError);\n        this.document.body.appendChild(node); // The request has now been successfully sent.\n\n        observer.next({\n          type: HttpEventType.Sent\n        }); // Cancellation handler.\n\n        return () => {\n          if (!finished) {\n            this.removeListeners(node);\n          } // And finally, clean up the page.\n\n\n          cleanup();\n        };\n      });\n    }\n\n    removeListeners(script) {\n      // Issue #34818\n      // Changing <script>'s ownerDocument will prevent it from execution.\n      // https://html.spec.whatwg.org/multipage/scripting.html#execute-the-script-block\n      if (!foreignDocument) {\n        foreignDocument = this.document.implementation.createHTMLDocument();\n      }\n\n      foreignDocument.adoptNode(script);\n    }\n\n  }\n\n  JsonpClientBackend.ɵfac = function JsonpClientBackend_Factory(t) {\n    return new (t || JsonpClientBackend)(i0.ɵɵinject(JsonpCallbackContext), i0.ɵɵinject(DOCUMENT));\n  };\n\n  JsonpClientBackend.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: JsonpClientBackend,\n    factory: JsonpClientBackend.ɵfac\n  });\n  return JsonpClientBackend;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Identifies requests with the method JSONP and\n * shifts them to the `JsonpClientBackend`.\n *\n * @see `HttpInterceptor`\n *\n * @publicApi\n */\n\n\nlet JsonpInterceptor = /*#__PURE__*/(() => {\n  class JsonpInterceptor {\n    constructor(jsonp) {\n      this.jsonp = jsonp;\n    }\n    /**\n     * Identifies and handles a given JSONP request.\n     * @param req The outgoing request object to handle.\n     * @param next The next interceptor in the chain, or the backend\n     * if no interceptors remain in the chain.\n     * @returns An observable of the event stream.\n     */\n\n\n    intercept(req, next) {\n      if (req.method === 'JSONP') {\n        return this.jsonp.handle(req);\n      } // Fall through for normal HTTP requests.\n\n\n      return next.handle(req);\n    }\n\n  }\n\n  JsonpInterceptor.ɵfac = function JsonpInterceptor_Factory(t) {\n    return new (t || JsonpInterceptor)(i0.ɵɵinject(JsonpClientBackend));\n  };\n\n  JsonpInterceptor.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: JsonpInterceptor,\n    factory: JsonpInterceptor.ɵfac\n  });\n  return JsonpInterceptor;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst XSSI_PREFIX = /^\\)\\]\\}',?\\n/;\n/**\n * Determine an appropriate URL for the response, by checking either\n * XMLHttpRequest.responseURL or the X-Request-URL header.\n */\n\nfunction getResponseUrl(xhr) {\n  if ('responseURL' in xhr && xhr.responseURL) {\n    return xhr.responseURL;\n  }\n\n  if (/^X-Request-URL:/m.test(xhr.getAllResponseHeaders())) {\n    return xhr.getResponseHeader('X-Request-URL');\n  }\n\n  return null;\n}\n/**\n * Uses `XMLHttpRequest` to send requests to a backend server.\n * @see `HttpHandler`\n * @see `JsonpClientBackend`\n *\n * @publicApi\n */\n\n\nlet HttpXhrBackend = /*#__PURE__*/(() => {\n  class HttpXhrBackend {\n    constructor(xhrFactory) {\n      this.xhrFactory = xhrFactory;\n    }\n    /**\n     * Processes a request and returns a stream of response events.\n     * @param req The request object.\n     * @returns An observable of the response events.\n     */\n\n\n    handle(req) {\n      // Quick check to give a better error message when a user attempts to use\n      // HttpClient.jsonp() without installing the HttpClientJsonpModule\n      if (req.method === 'JSONP') {\n        throw new Error(`Attempted to construct Jsonp request without HttpClientJsonpModule installed.`);\n      } // Everything happens on Observable subscription.\n\n\n      return new Observable(observer => {\n        // Start by setting up the XHR object with request method, URL, and withCredentials flag.\n        const xhr = this.xhrFactory.build();\n        xhr.open(req.method, req.urlWithParams);\n\n        if (!!req.withCredentials) {\n          xhr.withCredentials = true;\n        } // Add all the requested headers.\n\n\n        req.headers.forEach((name, values) => xhr.setRequestHeader(name, values.join(','))); // Add an Accept header if one isn't present already.\n\n        if (!req.headers.has('Accept')) {\n          xhr.setRequestHeader('Accept', 'application/json, text/plain, */*');\n        } // Auto-detect the Content-Type header if one isn't present already.\n\n\n        if (!req.headers.has('Content-Type')) {\n          const detectedType = req.detectContentTypeHeader(); // Sometimes Content-Type detection fails.\n\n          if (detectedType !== null) {\n            xhr.setRequestHeader('Content-Type', detectedType);\n          }\n        } // Set the responseType if one was requested.\n\n\n        if (req.responseType) {\n          const responseType = req.responseType.toLowerCase(); // JSON responses need to be processed as text. This is because if the server\n          // returns an XSSI-prefixed JSON response, the browser will fail to parse it,\n          // xhr.response will be null, and xhr.responseText cannot be accessed to\n          // retrieve the prefixed JSON data in order to strip the prefix. Thus, all JSON\n          // is parsed by first requesting text and then applying JSON.parse.\n\n          xhr.responseType = responseType !== 'json' ? responseType : 'text';\n        } // Serialize the request body if one is present. If not, this will be set to null.\n\n\n        const reqBody = req.serializeBody(); // If progress events are enabled, response headers will be delivered\n        // in two events - the HttpHeaderResponse event and the full HttpResponse\n        // event. However, since response headers don't change in between these\n        // two events, it doesn't make sense to parse them twice. So headerResponse\n        // caches the data extracted from the response whenever it's first parsed,\n        // to ensure parsing isn't duplicated.\n\n        let headerResponse = null; // partialFromXhr extracts the HttpHeaderResponse from the current XMLHttpRequest\n        // state, and memoizes it into headerResponse.\n\n        const partialFromXhr = () => {\n          if (headerResponse !== null) {\n            return headerResponse;\n          }\n\n          const statusText = xhr.statusText || 'OK'; // Parse headers from XMLHttpRequest - this step is lazy.\n\n          const headers = new HttpHeaders(xhr.getAllResponseHeaders()); // Read the response URL from the XMLHttpResponse instance and fall back on the\n          // request URL.\n\n          const url = getResponseUrl(xhr) || req.url; // Construct the HttpHeaderResponse and memoize it.\n\n          headerResponse = new HttpHeaderResponse({\n            headers,\n            status: xhr.status,\n            statusText,\n            url\n          });\n          return headerResponse;\n        }; // Next, a few closures are defined for the various events which XMLHttpRequest can\n        // emit. This allows them to be unregistered as event listeners later.\n        // First up is the load event, which represents a response being fully available.\n\n\n        const onLoad = () => {\n          // Read response state from the memoized partial data.\n          let {\n            headers,\n            status,\n            statusText,\n            url\n          } = partialFromXhr(); // The body will be read out if present.\n\n          let body = null;\n\n          if (status !== 204\n          /* HttpStatusCode.NoContent */\n          ) {\n            // Use XMLHttpRequest.response if set, responseText otherwise.\n            body = typeof xhr.response === 'undefined' ? xhr.responseText : xhr.response;\n          } // Normalize another potential bug (this one comes from CORS).\n\n\n          if (status === 0) {\n            status = !!body ? 200\n            /* HttpStatusCode.Ok */\n            : 0;\n          } // ok determines whether the response will be transmitted on the event or\n          // error channel. Unsuccessful status codes (not 2xx) will always be errors,\n          // but a successful status code can still result in an error if the user\n          // asked for JSON data and the body cannot be parsed as such.\n\n\n          let ok = status >= 200 && status < 300; // Check whether the body needs to be parsed as JSON (in many cases the browser\n          // will have done that already).\n\n          if (req.responseType === 'json' && typeof body === 'string') {\n            // Save the original body, before attempting XSSI prefix stripping.\n            const originalBody = body;\n            body = body.replace(XSSI_PREFIX, '');\n\n            try {\n              // Attempt the parse. If it fails, a parse error should be delivered to the user.\n              body = body !== '' ? JSON.parse(body) : null;\n            } catch (error) {\n              // Since the JSON.parse failed, it's reasonable to assume this might not have been a\n              // JSON response. Restore the original body (including any XSSI prefix) to deliver\n              // a better error response.\n              body = originalBody; // If this was an error request to begin with, leave it as a string, it probably\n              // just isn't JSON. Otherwise, deliver the parsing error to the user.\n\n              if (ok) {\n                // Even though the response status was 2xx, this is still an error.\n                ok = false; // The parse error contains the text of the body that failed to parse.\n\n                body = {\n                  error,\n                  text: body\n                };\n              }\n            }\n          }\n\n          if (ok) {\n            // A successful response is delivered on the event stream.\n            observer.next(new HttpResponse({\n              body,\n              headers,\n              status,\n              statusText,\n              url: url || undefined\n            })); // The full body has been received and delivered, no further events\n            // are possible. This request is complete.\n\n            observer.complete();\n          } else {\n            // An unsuccessful request is delivered on the error channel.\n            observer.error(new HttpErrorResponse({\n              // The error in this case is the response body (error from the server).\n              error: body,\n              headers,\n              status,\n              statusText,\n              url: url || undefined\n            }));\n          }\n        }; // The onError callback is called when something goes wrong at the network level.\n        // Connection timeout, DNS error, offline, etc. These are actual errors, and are\n        // transmitted on the error channel.\n\n\n        const onError = error => {\n          const {\n            url\n          } = partialFromXhr();\n          const res = new HttpErrorResponse({\n            error,\n            status: xhr.status || 0,\n            statusText: xhr.statusText || 'Unknown Error',\n            url: url || undefined\n          });\n          observer.error(res);\n        }; // The sentHeaders flag tracks whether the HttpResponseHeaders event\n        // has been sent on the stream. This is necessary to track if progress\n        // is enabled since the event will be sent on only the first download\n        // progress event.\n\n\n        let sentHeaders = false; // The download progress event handler, which is only registered if\n        // progress events are enabled.\n\n        const onDownProgress = event => {\n          // Send the HttpResponseHeaders event if it hasn't been sent already.\n          if (!sentHeaders) {\n            observer.next(partialFromXhr());\n            sentHeaders = true;\n          } // Start building the download progress event to deliver on the response\n          // event stream.\n\n\n          let progressEvent = {\n            type: HttpEventType.DownloadProgress,\n            loaded: event.loaded\n          }; // Set the total number of bytes in the event if it's available.\n\n          if (event.lengthComputable) {\n            progressEvent.total = event.total;\n          } // If the request was for text content and a partial response is\n          // available on XMLHttpRequest, include it in the progress event\n          // to allow for streaming reads.\n\n\n          if (req.responseType === 'text' && !!xhr.responseText) {\n            progressEvent.partialText = xhr.responseText;\n          } // Finally, fire the event.\n\n\n          observer.next(progressEvent);\n        }; // The upload progress event handler, which is only registered if\n        // progress events are enabled.\n\n\n        const onUpProgress = event => {\n          // Upload progress events are simpler. Begin building the progress\n          // event.\n          let progress = {\n            type: HttpEventType.UploadProgress,\n            loaded: event.loaded\n          }; // If the total number of bytes being uploaded is available, include\n          // it.\n\n          if (event.lengthComputable) {\n            progress.total = event.total;\n          } // Send the event.\n\n\n          observer.next(progress);\n        }; // By default, register for load and error events.\n\n\n        xhr.addEventListener('load', onLoad);\n        xhr.addEventListener('error', onError);\n        xhr.addEventListener('timeout', onError);\n        xhr.addEventListener('abort', onError); // Progress events are only enabled if requested.\n\n        if (req.reportProgress) {\n          // Download progress is always enabled if requested.\n          xhr.addEventListener('progress', onDownProgress); // Upload progress depends on whether there is a body to upload.\n\n          if (reqBody !== null && xhr.upload) {\n            xhr.upload.addEventListener('progress', onUpProgress);\n          }\n        } // Fire the request, and notify the event stream that it was fired.\n\n\n        xhr.send(reqBody);\n        observer.next({\n          type: HttpEventType.Sent\n        }); // This is the return from the Observable function, which is the\n        // request cancellation handler.\n\n        return () => {\n          // On a cancellation, remove all registered event listeners.\n          xhr.removeEventListener('error', onError);\n          xhr.removeEventListener('abort', onError);\n          xhr.removeEventListener('load', onLoad);\n          xhr.removeEventListener('timeout', onError);\n\n          if (req.reportProgress) {\n            xhr.removeEventListener('progress', onDownProgress);\n\n            if (reqBody !== null && xhr.upload) {\n              xhr.upload.removeEventListener('progress', onUpProgress);\n            }\n          } // Finally, abort the in-flight request.\n\n\n          if (xhr.readyState !== xhr.DONE) {\n            xhr.abort();\n          }\n        };\n      });\n    }\n\n  }\n\n  HttpXhrBackend.ɵfac = function HttpXhrBackend_Factory(t) {\n    return new (t || HttpXhrBackend)(i0.ɵɵinject(i1.XhrFactory));\n  };\n\n  HttpXhrBackend.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HttpXhrBackend,\n    factory: HttpXhrBackend.ɵfac\n  });\n  return HttpXhrBackend;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst XSRF_COOKIE_NAME = /*#__PURE__*/new InjectionToken('XSRF_COOKIE_NAME');\nconst XSRF_HEADER_NAME = /*#__PURE__*/new InjectionToken('XSRF_HEADER_NAME');\n/**\n * Retrieves the current XSRF token to use with the next outgoing request.\n *\n * @publicApi\n */\n\nclass HttpXsrfTokenExtractor {}\n/**\n * `HttpXsrfTokenExtractor` which retrieves the token from a cookie.\n */\n\n\nlet HttpXsrfCookieExtractor = /*#__PURE__*/(() => {\n  class HttpXsrfCookieExtractor {\n    constructor(doc, platform, cookieName) {\n      this.doc = doc;\n      this.platform = platform;\n      this.cookieName = cookieName;\n      this.lastCookieString = '';\n      this.lastToken = null;\n      /**\n       * @internal for testing\n       */\n\n      this.parseCount = 0;\n    }\n\n    getToken() {\n      if (this.platform === 'server') {\n        return null;\n      }\n\n      const cookieString = this.doc.cookie || '';\n\n      if (cookieString !== this.lastCookieString) {\n        this.parseCount++;\n        this.lastToken = ɵparseCookieValue(cookieString, this.cookieName);\n        this.lastCookieString = cookieString;\n      }\n\n      return this.lastToken;\n    }\n\n  }\n\n  HttpXsrfCookieExtractor.ɵfac = function HttpXsrfCookieExtractor_Factory(t) {\n    return new (t || HttpXsrfCookieExtractor)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(XSRF_COOKIE_NAME));\n  };\n\n  HttpXsrfCookieExtractor.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HttpXsrfCookieExtractor,\n    factory: HttpXsrfCookieExtractor.ɵfac\n  });\n  return HttpXsrfCookieExtractor;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * `HttpInterceptor` which adds an XSRF token to eligible outgoing requests.\n */\n\n\nlet HttpXsrfInterceptor = /*#__PURE__*/(() => {\n  class HttpXsrfInterceptor {\n    constructor(tokenService, headerName) {\n      this.tokenService = tokenService;\n      this.headerName = headerName;\n    }\n\n    intercept(req, next) {\n      const lcUrl = req.url.toLowerCase(); // Skip both non-mutating requests and absolute URLs.\n      // Non-mutating requests don't require a token, and absolute URLs require special handling\n      // anyway as the cookie set\n      // on our origin is not the same as the token expected by another origin.\n\n      if (req.method === 'GET' || req.method === 'HEAD' || lcUrl.startsWith('http://') || lcUrl.startsWith('https://')) {\n        return next.handle(req);\n      }\n\n      const token = this.tokenService.getToken(); // Be careful not to overwrite an existing header of the same name.\n\n      if (token !== null && !req.headers.has(this.headerName)) {\n        req = req.clone({\n          headers: req.headers.set(this.headerName, token)\n        });\n      }\n\n      return next.handle(req);\n    }\n\n  }\n\n  HttpXsrfInterceptor.ɵfac = function HttpXsrfInterceptor_Factory(t) {\n    return new (t || HttpXsrfInterceptor)(i0.ɵɵinject(HttpXsrfTokenExtractor), i0.ɵɵinject(XSRF_HEADER_NAME));\n  };\n\n  HttpXsrfInterceptor.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HttpXsrfInterceptor,\n    factory: HttpXsrfInterceptor.ɵfac\n  });\n  return HttpXsrfInterceptor;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * An injectable `HttpHandler` that applies multiple interceptors\n * to a request before passing it to the given `HttpBackend`.\n *\n * The interceptors are loaded lazily from the injector, to allow\n * interceptors to themselves inject classes depending indirectly\n * on `HttpInterceptingHandler` itself.\n * @see `HttpInterceptor`\n */\n\n\nlet HttpInterceptingHandler = /*#__PURE__*/(() => {\n  class HttpInterceptingHandler {\n    constructor(backend, injector) {\n      this.backend = backend;\n      this.injector = injector;\n      this.chain = null;\n    }\n\n    handle(req) {\n      if (this.chain === null) {\n        const interceptors = this.injector.get(HTTP_INTERCEPTORS, []);\n        this.chain = interceptors.reduceRight((next, interceptor) => new HttpInterceptorHandler(next, interceptor), this.backend);\n      }\n\n      return this.chain.handle(req);\n    }\n\n  }\n\n  HttpInterceptingHandler.ɵfac = function HttpInterceptingHandler_Factory(t) {\n    return new (t || HttpInterceptingHandler)(i0.ɵɵinject(HttpBackend), i0.ɵɵinject(i0.Injector));\n  };\n\n  HttpInterceptingHandler.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HttpInterceptingHandler,\n    factory: HttpInterceptingHandler.ɵfac\n  });\n  return HttpInterceptingHandler;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Constructs an `HttpHandler` that applies interceptors\n * to a request before passing it to the given `HttpBackend`.\n *\n * Use as a factory function within `HttpClientModule`.\n *\n *\n */\n\n\nfunction interceptingHandler(backend, interceptors = []) {\n  if (!interceptors) {\n    return backend;\n  }\n\n  return interceptors.reduceRight((next, interceptor) => new HttpInterceptorHandler(next, interceptor), backend);\n}\n/**\n * Factory function that determines where to store JSONP callbacks.\n *\n * Ordinarily JSONP callbacks are stored on the `window` object, but this may not exist\n * in test environments. In that case, callbacks are stored on an anonymous object instead.\n *\n *\n */\n\n\nfunction jsonpCallbackContext() {\n  if (typeof window === 'object') {\n    return window;\n  }\n\n  return {};\n}\n/**\n * Configures XSRF protection support for outgoing requests.\n *\n * For a server that supports a cookie-based XSRF protection system,\n * use directly to configure XSRF protection with the correct\n * cookie and header names.\n *\n * If no names are supplied, the default cookie name is `XSRF-TOKEN`\n * and the default header name is `X-XSRF-TOKEN`.\n *\n * @publicApi\n */\n\n\nlet HttpClientXsrfModule = /*#__PURE__*/(() => {\n  class HttpClientXsrfModule {\n    /**\n     * Disable the default XSRF protection.\n     */\n    static disable() {\n      return {\n        ngModule: HttpClientXsrfModule,\n        providers: [{\n          provide: HttpXsrfInterceptor,\n          useClass: NoopInterceptor\n        }]\n      };\n    }\n    /**\n     * Configure XSRF protection.\n     * @param options An object that can specify either or both\n     * cookie name or header name.\n     * - Cookie name default is `XSRF-TOKEN`.\n     * - Header name default is `X-XSRF-TOKEN`.\n     *\n     */\n\n\n    static withOptions(options = {}) {\n      return {\n        ngModule: HttpClientXsrfModule,\n        providers: [options.cookieName ? {\n          provide: XSRF_COOKIE_NAME,\n          useValue: options.cookieName\n        } : [], options.headerName ? {\n          provide: XSRF_HEADER_NAME,\n          useValue: options.headerName\n        } : []]\n      };\n    }\n\n  }\n\n  HttpClientXsrfModule.ɵfac = function HttpClientXsrfModule_Factory(t) {\n    return new (t || HttpClientXsrfModule)();\n  };\n\n  HttpClientXsrfModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: HttpClientXsrfModule\n  });\n  HttpClientXsrfModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [HttpXsrfInterceptor, {\n      provide: HTTP_INTERCEPTORS,\n      useExisting: HttpXsrfInterceptor,\n      multi: true\n    }, {\n      provide: HttpXsrfTokenExtractor,\n      useClass: HttpXsrfCookieExtractor\n    }, {\n      provide: XSRF_COOKIE_NAME,\n      useValue: 'XSRF-TOKEN'\n    }, {\n      provide: XSRF_HEADER_NAME,\n      useValue: 'X-XSRF-TOKEN'\n    }]\n  });\n  return HttpClientXsrfModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Configures the [dependency injector](guide/glossary#injector) for `HttpClient`\n * with supporting services for XSRF. Automatically imported by `HttpClientModule`.\n *\n * You can add interceptors to the chain behind `HttpClient` by binding them to the\n * multiprovider for built-in [DI token](guide/glossary#di-token) `HTTP_INTERCEPTORS`.\n *\n * @publicApi\n */\n\n\nlet HttpClientModule = /*#__PURE__*/(() => {\n  class HttpClientModule {}\n\n  HttpClientModule.ɵfac = function HttpClientModule_Factory(t) {\n    return new (t || HttpClientModule)();\n  };\n\n  HttpClientModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: HttpClientModule\n  });\n  HttpClientModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [HttpClient, {\n      provide: HttpHandler,\n      useClass: HttpInterceptingHandler\n    }, HttpXhrBackend, {\n      provide: HttpBackend,\n      useExisting: HttpXhrBackend\n    }],\n    imports: [HttpClientXsrfModule.withOptions({\n      cookieName: 'XSRF-TOKEN',\n      headerName: 'X-XSRF-TOKEN'\n    })]\n  });\n  return HttpClientModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Configures the [dependency injector](guide/glossary#injector) for `HttpClient`\n * with supporting services for JSONP.\n * Without this module, Jsonp requests reach the backend\n * with method JSONP, where they are rejected.\n *\n * You can add interceptors to the chain behind `HttpClient` by binding them to the\n * multiprovider for built-in [DI token](guide/glossary#di-token) `HTTP_INTERCEPTORS`.\n *\n * @publicApi\n */\n\n\nlet HttpClientJsonpModule = /*#__PURE__*/(() => {\n  class HttpClientJsonpModule {}\n\n  HttpClientJsonpModule.ɵfac = function HttpClientJsonpModule_Factory(t) {\n    return new (t || HttpClientJsonpModule)();\n  };\n\n  HttpClientJsonpModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: HttpClientJsonpModule\n  });\n  HttpClientJsonpModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [JsonpClientBackend, {\n      provide: JsonpCallbackContext,\n      useFactory: jsonpCallbackContext\n    }, {\n      provide: HTTP_INTERCEPTORS,\n      useClass: JsonpInterceptor,\n      multi: true\n    }]\n  });\n  return HttpClientJsonpModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * A wrapper around the `XMLHttpRequest` constructor.\n *\n * @publicApi\n * @see `XhrFactory`\n * @deprecated\n * `XhrFactory` has moved, please import `XhrFactory` from `@angular/common` instead.\n */\n\n\nconst XhrFactory = XhrFactory$1;\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { HTTP_INTERCEPTORS, HttpBackend, HttpClient, HttpClientJsonpModule, HttpClientModule, HttpClientXsrfModule, HttpContext, HttpContextToken, HttpErrorResponse, HttpEventType, HttpHandler, HttpHeaderResponse, HttpHeaders, HttpParams, HttpRequest, HttpResponse, HttpResponseBase, HttpUrlEncodingCodec, HttpXhrBackend, HttpXsrfTokenExtractor, JsonpClientBackend, JsonpInterceptor, XhrFactory, HttpInterceptingHandler as ɵHttpInterceptingHandler }; //# sourceMappingURL=http.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}