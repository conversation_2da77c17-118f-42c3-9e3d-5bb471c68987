{"ast": null, "code": "import { Subject } from './Subject';\nexport class AsyncSubject extends Subject {\n  constructor() {\n    super(...arguments);\n    this._value = null;\n    this._hasValue = false;\n    this._isComplete = false;\n  }\n\n  _checkFinalizedStatuses(subscriber) {\n    const {\n      hasError,\n      _hasValue,\n      _value,\n      thrownError,\n      isStopped,\n      _isComplete\n    } = this;\n\n    if (hasError) {\n      subscriber.error(thrownError);\n    } else if (isStopped || _isComplete) {\n      _hasValue && subscriber.next(_value);\n      subscriber.complete();\n    }\n  }\n\n  next(value) {\n    if (!this.isStopped) {\n      this._value = value;\n      this._hasValue = true;\n    }\n  }\n\n  complete() {\n    const {\n      _hasValue,\n      _value,\n      _isComplete\n    } = this;\n\n    if (!_isComplete) {\n      this._isComplete = true;\n      _hasValue && super.next(_value);\n      super.complete();\n    }\n  }\n\n} //# sourceMappingURL=AsyncSubject.js.map", "map": null, "metadata": {}, "sourceType": "module"}