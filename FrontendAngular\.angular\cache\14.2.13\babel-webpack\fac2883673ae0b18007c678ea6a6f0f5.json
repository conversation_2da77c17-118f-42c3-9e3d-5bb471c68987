{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nimport { identity } from '../util/identity';\nimport { noop } from '../util/noop';\nimport { popResultSelector } from '../util/args';\nexport function withLatestFrom(...inputs) {\n  const project = popResultSelector(inputs);\n  return operate((source, subscriber) => {\n    const len = inputs.length;\n    const otherValues = new Array(len);\n    let hasValue = inputs.map(() => false);\n    let ready = false;\n\n    for (let i = 0; i < len; i++) {\n      innerFrom(inputs[i]).subscribe(createOperatorSubscriber(subscriber, value => {\n        otherValues[i] = value;\n\n        if (!ready && !hasValue[i]) {\n          hasValue[i] = true;\n          (ready = hasValue.every(identity)) && (hasValue = null);\n        }\n      }, noop));\n    }\n\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      if (ready) {\n        const values = [value, ...otherValues];\n        subscriber.next(project ? project(...values) : values);\n      }\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "innerFrom", "identity", "noop", "popResultSelector", "withLatestFrom", "inputs", "project", "source", "subscriber", "len", "length", "otherValues", "Array", "hasValue", "map", "ready", "i", "subscribe", "value", "every", "values", "next"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/rxjs/dist/esm/internal/operators/withLatestFrom.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nimport { identity } from '../util/identity';\nimport { noop } from '../util/noop';\nimport { popResultSelector } from '../util/args';\nexport function withLatestFrom(...inputs) {\n    const project = popResultSelector(inputs);\n    return operate((source, subscriber) => {\n        const len = inputs.length;\n        const otherValues = new Array(len);\n        let hasValue = inputs.map(() => false);\n        let ready = false;\n        for (let i = 0; i < len; i++) {\n            innerFrom(inputs[i]).subscribe(createOperatorSubscriber(subscriber, (value) => {\n                otherValues[i] = value;\n                if (!ready && !hasValue[i]) {\n                    hasValue[i] = true;\n                    (ready = hasValue.every(identity)) && (hasValue = null);\n                }\n            }, noop));\n        }\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            if (ready) {\n                const values = [value, ...otherValues];\n                subscriber.next(project ? project(...values) : values);\n            }\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,SAASC,SAAT,QAA0B,yBAA1B;AACA,SAASC,QAAT,QAAyB,kBAAzB;AACA,SAASC,IAAT,QAAqB,cAArB;AACA,SAASC,iBAAT,QAAkC,cAAlC;AACA,OAAO,SAASC,cAAT,CAAwB,GAAGC,MAA3B,EAAmC;EACtC,MAAMC,OAAO,GAAGH,iBAAiB,CAACE,MAAD,CAAjC;EACA,OAAOP,OAAO,CAAC,CAACS,MAAD,EAASC,UAAT,KAAwB;IACnC,MAAMC,GAAG,GAAGJ,MAAM,CAACK,MAAnB;IACA,MAAMC,WAAW,GAAG,IAAIC,KAAJ,CAAUH,GAAV,CAApB;IACA,IAAII,QAAQ,GAAGR,MAAM,CAACS,GAAP,CAAW,MAAM,KAAjB,CAAf;IACA,IAAIC,KAAK,GAAG,KAAZ;;IACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGP,GAApB,EAAyBO,CAAC,EAA1B,EAA8B;MAC1BhB,SAAS,CAACK,MAAM,CAACW,CAAD,CAAP,CAAT,CAAqBC,SAArB,CAA+BlB,wBAAwB,CAACS,UAAD,EAAcU,KAAD,IAAW;QAC3EP,WAAW,CAACK,CAAD,CAAX,GAAiBE,KAAjB;;QACA,IAAI,CAACH,KAAD,IAAU,CAACF,QAAQ,CAACG,CAAD,CAAvB,EAA4B;UACxBH,QAAQ,CAACG,CAAD,CAAR,GAAc,IAAd;UACA,CAACD,KAAK,GAAGF,QAAQ,CAACM,KAAT,CAAelB,QAAf,CAAT,MAAuCY,QAAQ,GAAG,IAAlD;QACH;MACJ,CANsD,EAMpDX,IANoD,CAAvD;IAOH;;IACDK,MAAM,CAACU,SAAP,CAAiBlB,wBAAwB,CAACS,UAAD,EAAcU,KAAD,IAAW;MAC7D,IAAIH,KAAJ,EAAW;QACP,MAAMK,MAAM,GAAG,CAACF,KAAD,EAAQ,GAAGP,WAAX,CAAf;QACAH,UAAU,CAACa,IAAX,CAAgBf,OAAO,GAAGA,OAAO,CAAC,GAAGc,MAAJ,CAAV,GAAwBA,MAA/C;MACH;IACJ,CALwC,CAAzC;EAMH,CApBa,CAAd;AAqBH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}