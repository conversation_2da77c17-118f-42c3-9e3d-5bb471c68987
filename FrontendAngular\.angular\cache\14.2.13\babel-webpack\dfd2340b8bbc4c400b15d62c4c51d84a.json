{"ast": null, "code": "import _asyncToGenerator from \"R:/chateye/Frontend/chateye-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/chat.service\";\nimport * as i2 from \"../../services/api.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\n\nfunction LoginFormComponent__svg_svg_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 18);\n    i0.ɵɵelement(1, \"path\", 19);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LoginFormComponent__svg_svg_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 18);\n    i0.ɵɵelement(1, \"path\", 20);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LoginFormComponent__svg_svg_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 18);\n    i0.ɵɵelement(1, \"path\", 21);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LoginFormComponent_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Enter username and invite code to join\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LoginFormComponent_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Only approved users can join\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LoginFormComponent_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Enter your username to join the chat\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LoginFormComponent_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Loading...\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LoginFormComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 23);\n    i0.ɵɵelement(2, \"path\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Private chat - Access restricted\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction LoginFormComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\")(1, \"label\", 25);\n    i0.ɵɵtext(2, \" Invite Code \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"input\", 26);\n    i0.ɵɵlistener(\"ngModelChange\", function LoginFormComponent_div_22_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.inviteCode = $event);\n    })(\"input\", function LoginFormComponent_div_22_Template_input_input_3_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.inviteCode = $event.target.value.toUpperCase());\n    });\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"required\", ctx_r8.securityInfo == null ? null : ctx_r8.securityInfo.requiresInvite)(\"ngModel\", ctx_r8.inviteCode)(\"disabled\", ctx_r8.isLoading);\n  }\n}\n\nfunction LoginFormComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.error, \" \");\n  }\n}\n\nfunction LoginFormComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵelement(1, \"div\", 29);\n    i0.ɵɵtext(2, \" Joining... \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LoginFormComponent_span_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Join Chat\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nexport let LoginFormComponent = /*#__PURE__*/(() => {\n  class LoginFormComponent {\n    constructor(chatService, apiService) {\n      this.chatService = chatService;\n      this.apiService = apiService;\n      this.username = '';\n      this.inviteCode = '';\n      this.isLoading = false;\n      this.securityInfo = null;\n      this.error = '';\n    }\n\n    ngOnInit() {\n      this.fetchSecurityInfo();\n    }\n\n    fetchSecurityInfo() {\n      this.apiService.getSecurityInfo().subscribe({\n        next: data => {\n          this.securityInfo = data;\n        },\n        error: error => {\n          console.error('Failed to fetch security info:', error);\n          this.securityInfo = {\n            mode: 'open',\n            requiresInvite: false,\n            requiresWhitelist: false\n          };\n        }\n      });\n    }\n\n    onSubmit() {\n      var _this = this;\n\n      return _asyncToGenerator(function* () {\n        if (!_this.username.trim()) return; // Validate invite code if required\n\n        if (_this.securityInfo?.requiresInvite && !_this.inviteCode.trim()) {\n          _this.error = 'Invite code is required';\n          return;\n        }\n\n        _this.isLoading = true;\n        _this.error = '';\n\n        try {\n          yield _this.chatService.login(_this.username.trim(), _this.inviteCode.trim() || null);\n        } catch (error) {\n          console.error('Login failed:', error);\n          _this.error = error.error?.error || error.message || 'Login failed';\n        } finally {\n          _this.isLoading = false;\n        }\n      })();\n    }\n\n  }\n\n  LoginFormComponent.ɵfac = function LoginFormComponent_Factory(t) {\n    return new (t || LoginFormComponent)(i0.ɵɵdirectiveInject(i1.ChatService), i0.ɵɵdirectiveInject(i2.ApiService));\n  };\n\n  LoginFormComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LoginFormComponent,\n    selectors: [[\"app-login-form\"]],\n    decls: 31,\n    vars: 15,\n    consts: [[1, \"min-h-screen\", \"flex\", \"items-center\", \"justify-center\", \"bg-gradient-to-br\", \"from-primary-50\", \"to-primary-100\"], [1, \"max-w-md\", \"w-full\", \"space-y-8\", \"p-8\"], [1, \"text-center\"], [1, \"mx-auto\", \"h-12\", \"w-12\", \"flex\", \"items-center\", \"justify-center\", \"rounded-full\", \"bg-primary-500\", \"text-white\"], [\"class\", \"w-6 h-6\", \"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 4, \"ngIf\"], [1, \"mt-6\", \"text-3xl\", \"font-extrabold\", \"text-gray-900\"], [1, \"mt-2\", \"space-y-1\"], [1, \"text-sm\", \"text-gray-600\"], [4, \"ngIf\"], [\"class\", \"flex items-center justify-center space-x-2 text-xs text-gray-500\", 4, \"ngIf\"], [1, \"mt-8\", \"space-y-6\", 3, \"ngSubmit\"], [1, \"space-y-4\"], [\"for\", \"username\", 1, \"sr-only\"], [\"id\", \"username\", \"name\", \"username\", \"type\", \"text\", \"required\", \"\", \"placeholder\", \"Enter your username\", \"maxlength\", \"50\", 1, \"relative\", \"block\", \"w-full\", \"px-3\", \"py-3\", \"border\", \"border-gray-300\", \"placeholder-gray-500\", \"text-gray-900\", \"rounded-lg\", \"focus:outline-none\", \"focus:ring-primary-500\", \"focus:border-primary-500\", \"focus:z-10\", \"sm:text-sm\", 3, \"ngModel\", \"disabled\", \"ngModelChange\"], [\"class\", \"text-sm text-red-600 text-center bg-red-50 p-2 rounded-lg\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"group\", \"relative\", \"w-full\", \"flex\", \"justify-center\", \"py-3\", \"px-4\", \"border\", \"border-transparent\", \"text-sm\", \"font-medium\", \"rounded-lg\", \"text-white\", \"bg-primary-600\", \"hover:bg-primary-700\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-offset-2\", \"focus:ring-primary-500\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"transition-colors\", \"duration-200\", 3, \"disabled\"], [\"class\", \"flex items-center\", 4, \"ngIf\"], [1, \"text-xs\", \"text-gray-500\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-2\", \"text-xs\", \"text-gray-500\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-3\", \"h-3\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"], [\"for\", \"inviteCode\", 1, \"sr-only\"], [\"id\", \"inviteCode\", \"name\", \"inviteCode\", \"type\", \"text\", \"placeholder\", \"Enter invite code\", \"maxlength\", \"20\", 1, \"relative\", \"block\", \"w-full\", \"px-3\", \"py-3\", \"border\", \"border-gray-300\", \"placeholder-gray-500\", \"text-gray-900\", \"rounded-lg\", \"focus:outline-none\", \"focus:ring-primary-500\", \"focus:border-primary-500\", \"focus:z-10\", \"sm:text-sm\", 3, \"required\", \"ngModel\", \"disabled\", \"ngModelChange\", \"input\"], [1, \"text-sm\", \"text-red-600\", \"text-center\", \"bg-red-50\", \"p-2\", \"rounded-lg\"], [1, \"flex\", \"items-center\"], [1, \"animate-spin\", \"-ml-1\", \"mr-3\", \"h-5\", \"w-5\", \"border-2\", \"border-white\", \"border-t-transparent\", \"rounded-full\"]],\n    template: function LoginFormComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n        i0.ɵɵtemplate(4, LoginFormComponent__svg_svg_4_Template, 2, 0, \"svg\", 4);\n        i0.ɵɵtemplate(5, LoginFormComponent__svg_svg_5_Template, 2, 0, \"svg\", 4);\n        i0.ɵɵtemplate(6, LoginFormComponent__svg_svg_6_Template, 2, 0, \"svg\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"h2\", 5);\n        i0.ɵɵtext(8, \" Welcome to Chateye \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"div\", 6)(10, \"p\", 7);\n        i0.ɵɵtemplate(11, LoginFormComponent_span_11_Template, 2, 0, \"span\", 8);\n        i0.ɵɵtemplate(12, LoginFormComponent_span_12_Template, 2, 0, \"span\", 8);\n        i0.ɵɵtemplate(13, LoginFormComponent_span_13_Template, 2, 0, \"span\", 8);\n        i0.ɵɵtemplate(14, LoginFormComponent_span_14_Template, 2, 0, \"span\", 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(15, LoginFormComponent_div_15_Template, 5, 0, \"div\", 9);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(16, \"form\", 10);\n        i0.ɵɵlistener(\"ngSubmit\", function LoginFormComponent_Template_form_ngSubmit_16_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵelementStart(17, \"div\", 11)(18, \"div\")(19, \"label\", 12);\n        i0.ɵɵtext(20, \" Username \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(21, \"input\", 13);\n        i0.ɵɵlistener(\"ngModelChange\", function LoginFormComponent_Template_input_ngModelChange_21_listener($event) {\n          return ctx.username = $event;\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(22, LoginFormComponent_div_22_Template, 4, 3, \"div\", 8);\n        i0.ɵɵtemplate(23, LoginFormComponent_div_23_Template, 2, 1, \"div\", 14);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(24, \"div\")(25, \"button\", 15);\n        i0.ɵɵtemplate(26, LoginFormComponent_div_26_Template, 3, 0, \"div\", 16);\n        i0.ɵɵtemplate(27, LoginFormComponent_span_27_Template, 2, 0, \"span\", 8);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(28, \"div\", 2)(29, \"p\", 17);\n        i0.ɵɵtext(30, \" This is a private chat for friends only \");\n        i0.ɵɵelementEnd()()()();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", (ctx.securityInfo == null ? null : ctx.securityInfo.mode) === \"invite\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.securityInfo == null ? null : ctx.securityInfo.mode) === \"whitelist\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.securityInfo == null ? null : ctx.securityInfo.mode) === \"open\" || !ctx.securityInfo);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", (ctx.securityInfo == null ? null : ctx.securityInfo.mode) === \"invite\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.securityInfo == null ? null : ctx.securityInfo.mode) === \"whitelist\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.securityInfo == null ? null : ctx.securityInfo.mode) === \"open\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.securityInfo);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.securityInfo == null ? null : ctx.securityInfo.mode) !== \"open\");\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngModel\", ctx.username)(\"disabled\", ctx.isLoading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.securityInfo == null ? null : ctx.securityInfo.requiresInvite);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.error);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"disabled\", !ctx.username.trim() || ctx.isLoading || (ctx.securityInfo == null ? null : ctx.securityInfo.requiresInvite) && !ctx.inviteCode.trim());\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n      }\n    },\n    dependencies: [i3.NgIf, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.MaxLengthValidator, i4.NgModel, i4.NgForm]\n  });\n  return LoginFormComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}