{"ast": null, "code": "import _asyncToGenerator from \"R:/chateye/Frontend/chateye-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nexport let NotificationService = /*#__PURE__*/(() => {\n  class NotificationService {\n    constructor() {\n      this.permission = 'default';\n      this.requestPermission();\n    }\n\n    requestPermission() {\n      var _this = this;\n\n      return _asyncToGenerator(function* () {\n        if ('Notification' in window) {\n          _this.permission = yield Notification.requestPermission();\n        }\n      })();\n    }\n\n    showNotification(title, options = {}) {\n      if (this.permission === 'granted' && document.hidden) {\n        const notification = new Notification(title, {\n          icon: '/favicon.ico',\n          badge: '/favicon.ico',\n          tag: 'chateye-message',\n          ...options\n        }); // Auto close after 5 seconds\n\n        setTimeout(() => notification.close(), 5000); // Focus window when clicked\n\n        notification.onclick = () => {\n          window.focus();\n          notification.close();\n        };\n\n        return notification;\n      }\n\n      return null;\n    }\n\n    showMessageNotification(username, message) {\n      return this.showNotification(`New message from ${username}`, {\n        body: message.length > 50 ? message.substring(0, 50) + '...' : message,\n        tag: 'chateye-message'\n      });\n    }\n\n  }\n\n  NotificationService.ɵfac = function NotificationService_Factory(t) {\n    return new (t || NotificationService)();\n  };\n\n  NotificationService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: NotificationService,\n    factory: NotificationService.ɵfac,\n    providedIn: 'root'\n  });\n  return NotificationService;\n})();", "map": null, "metadata": {}, "sourceType": "module"}