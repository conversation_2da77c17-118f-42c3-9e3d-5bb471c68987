{"ast": null, "code": "import _asyncToGenerator from \"R:/chateye/Frontend/chateye-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { ApiService, Message, Group, User, SecurityInfo } from './api.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./api.service\";\nimport * as i2 from \"./socket.service\";\nimport * as i3 from \"./notification.service\";\nexport let ChatService = /*#__PURE__*/(() => {\n  class ChatService {\n    constructor(apiService, socketService, notificationService) {\n      this.apiService = apiService;\n      this.socketService = socketService;\n      this.notificationService = notificationService;\n      this.userSubject = new BehaviorSubject(null);\n      this.messagesSubject = new BehaviorSubject([]);\n      this.onlineUsersSubject = new BehaviorSubject([]);\n      this.groupsSubject = new BehaviorSubject([]);\n      this.currentGroupSubject = new BehaviorSubject(null);\n      this.replyToSubject = new BehaviorSubject(null);\n      this.loadingSubject = new BehaviorSubject(false);\n      this.isAdminSubject = new BehaviorSubject(false);\n      this.showAdminPanelSubject = new BehaviorSubject(false);\n      this.securityInfoSubject = new BehaviorSubject(null); // Public observables\n\n      this.user$ = this.userSubject.asObservable();\n      this.messages$ = this.messagesSubject.asObservable();\n      this.onlineUsers$ = this.onlineUsersSubject.asObservable();\n      this.groups$ = this.groupsSubject.asObservable();\n      this.currentGroup$ = this.currentGroupSubject.asObservable();\n      this.replyTo$ = this.replyToSubject.asObservable();\n      this.loading$ = this.loadingSubject.asObservable();\n      this.isAdmin$ = this.isAdminSubject.asObservable();\n      this.showAdminPanel$ = this.showAdminPanelSubject.asObservable();\n      this.securityInfo$ = this.securityInfoSubject.asObservable();\n      this.connected$ = this.socketService.isConnected$; // Computed observables\n\n      this.isLoggedIn$ = this.user$.pipe(map(user => !!user));\n      this.setupSocketListeners();\n    }\n\n    setupSocketListeners() {\n      this.socketService.on('connect', () => {\n        console.log('Connected to server');\n      });\n      this.socketService.on('disconnect', () => {\n        console.log('Disconnected from server');\n      });\n      this.socketService.on('userGroups', userGroups => {\n        console.log('Received user groups:', userGroups);\n        this.groupsSubject.next(userGroups || []);\n\n        if (userGroups && userGroups.length > 0) {\n          this.currentGroupSubject.next(userGroups[0]);\n        }\n      });\n      this.socketService.on('recentMessages', messages => {\n        console.log('Received recent messages:', messages.length);\n        this.messagesSubject.next(messages || []);\n      });\n      this.socketService.on('groupJoined', ({\n        groupId\n      }) => {\n        console.log('Joined group:', groupId);\n        const groups = this.groupsSubject.value;\n        const group = groups.find(g => g.id === groupId);\n\n        if (group) {\n          this.currentGroupSubject.next(group);\n        }\n      });\n      this.socketService.on('newMessage', message => {\n        console.log('New message received:', message);\n        const currentMessages = this.messagesSubject.value;\n        this.messagesSubject.next([...currentMessages, message]); // Show notification if not current user and window not focused\n\n        const currentUser = this.userSubject.value;\n\n        if (message.username !== currentUser && document.hidden) {\n          this.notificationService.showMessageNotification(message.username, message.text);\n        }\n      });\n      this.socketService.on('reactionUpdate', ({\n        messageId,\n        reactions\n      }) => {\n        console.log('Reaction update:', messageId, reactions);\n        const currentMessages = this.messagesSubject.value;\n        this.messagesSubject.next(currentMessages.map(msg => msg.id === messageId ? { ...msg,\n          reactions\n        } : msg));\n      });\n      this.socketService.on('onlineUsersUpdate', users => {\n        console.log('Online users updated:', users);\n        this.onlineUsersSubject.next(users || []);\n      });\n      this.socketService.on('userJoined', ({\n        username\n      }) => {\n        console.log('User joined:', username); // Online users will be updated via onlineUsersUpdate event\n      });\n      this.socketService.on('userLeft', ({\n        username\n      }) => {\n        console.log('User left:', username); // Online users will be updated via onlineUsersUpdate event\n      });\n      this.socketService.on('error', error => {\n        console.error('Socket error:', error);\n      });\n    }\n\n    login(_x) {\n      var _this = this;\n\n      return _asyncToGenerator(function* (username, inviteCode = null) {\n        try {\n          _this.loadingSubject.next(true);\n\n          const userData = yield _this.apiService.loginUser(username, inviteCode || undefined).toPromise(); // Connect to socket with auth data\n\n          _this.socketService.connect(username, inviteCode);\n\n          _this.userSubject.next(username);\n\n          _this.isAdminSubject.next(userData?.isAdmin || false);\n\n          _this.securityInfoSubject.next(userData?.securityInfo || null);\n        } catch (error) {\n          console.error('Login failed:', error);\n          throw error;\n        } finally {\n          _this.loadingSubject.next(false);\n        }\n      }).apply(this, arguments);\n    }\n\n    loadRecentMessages(groupId) {\n      var _this2 = this;\n\n      return _asyncToGenerator(function* () {\n        try {\n          const messages = yield _this2.apiService.getMessages(groupId, 50).toPromise();\n\n          _this2.messagesSubject.next(messages || []);\n        } catch (error) {\n          console.error('Failed to load messages:', error);\n        }\n      })();\n    }\n\n    joinGroup(groupId) {\n      const currentUser = this.userSubject.value;\n      if (!groupId || !currentUser) return;\n      this.socketService.joinGroup(groupId);\n      const groups = this.groupsSubject.value;\n      const group = groups.find(g => g.id === groupId);\n\n      if (group) {\n        this.currentGroupSubject.next(group);\n      }\n    }\n\n    sendMessage(text, replyToId = null) {\n      const currentUser = this.userSubject.value;\n      const currentGroup = this.currentGroupSubject.value;\n      if (!text.trim() || !currentUser || !currentGroup) return;\n      this.socketService.sendMessage(text, currentGroup.id, replyToId);\n    }\n\n    replyToMessage(message) {\n      this.replyToSubject.next(message);\n    }\n\n    cancelReply() {\n      this.replyToSubject.next(null);\n    }\n\n    addReaction(messageId, emoji) {\n      this.socketService.addReaction(messageId, emoji);\n    }\n\n    removeReaction(messageId) {\n      this.socketService.removeReaction(messageId);\n    }\n\n    showAdminPanel() {\n      this.showAdminPanelSubject.next(true);\n    }\n\n    hideAdminPanel() {\n      this.showAdminPanelSubject.next(false);\n    }\n\n    logout() {\n      this.socketService.disconnect();\n      this.userSubject.next(null);\n      this.messagesSubject.next([]);\n      this.onlineUsersSubject.next([]);\n      this.groupsSubject.next([]);\n      this.currentGroupSubject.next(null);\n      this.replyToSubject.next(null);\n      this.loadingSubject.next(false);\n      this.isAdminSubject.next(false);\n      this.showAdminPanelSubject.next(false);\n      this.securityInfoSubject.next(null);\n    }\n\n  }\n\n  ChatService.ɵfac = function ChatService_Factory(t) {\n    return new (t || ChatService)(i0.ɵɵinject(i1.ApiService), i0.ɵɵinject(i2.SocketService), i0.ɵɵinject(i3.NotificationService));\n  };\n\n  ChatService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ChatService,\n    factory: ChatService.ɵfac,\n    providedIn: 'root'\n  });\n  return ChatService;\n})();", "map": null, "metadata": {}, "sourceType": "module"}