{"name": "chateye-backend", "version": "1.0.0", "description": "Backend for Chateye chat application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "echo 'No build needed for Node.js'", "migrate": "node scripts/migrate.js", "migrate-groups": "node scripts/migrate-to-groups.js", "migrate-config": "node scripts/migrate_configuration.js", "update-db": "node scripts/update_database.js"}, "keywords": ["chat", "socket.io", "express"], "author": "", "license": "MIT", "dependencies": {"bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "pg": "^8.11.3", "socket.io": "^4.7.2", "uuid": "^9.0.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": "18.x"}}