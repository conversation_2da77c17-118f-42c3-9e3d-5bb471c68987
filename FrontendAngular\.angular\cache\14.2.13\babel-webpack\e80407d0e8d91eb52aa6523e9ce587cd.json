{"ast": null, "code": "import { ConnectableObservable } from '../observable/ConnectableObservable';\nimport { isFunction } from '../util/isFunction';\nimport { connect } from './connect';\nexport function multicast(subjectOrSubjectFactory, selector) {\n  const subjectFactory = isFunction(subjectOrSubjectFactory) ? subjectOrSubjectFactory : () => subjectOrSubjectFactory;\n\n  if (isFunction(selector)) {\n    return connect(selector, {\n      connector: subjectFactory\n    });\n  }\n\n  return source => new ConnectableObservable(source, subjectFactory);\n} //# sourceMappingURL=multicast.js.map", "map": null, "metadata": {}, "sourceType": "module"}