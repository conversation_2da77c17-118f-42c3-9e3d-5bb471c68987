<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Socket Connection Test</title>
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .connecting {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .connect-btn {
            background-color: #28a745;
            color: white;
        }
        .disconnect-btn {
            background-color: #dc3545;
            color: white;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Socket Connection Test</h1>
    
    <div id="status" class="status disconnected">
        Status: Disconnected
    </div>
    
    <div>
        <input type="text" id="username" placeholder="Username" value="testuser">
        <input type="text" id="inviteCode" placeholder="Invite Code (optional)">
        <button id="connectBtn" class="connect-btn">Connect</button>
        <button id="disconnectBtn" class="disconnect-btn">Disconnect</button>
        <button id="clearLogBtn">Clear Log</button>
    </div>
    
    <h3>Connection Log:</h3>
    <div id="log" class="log"></div>

    <script>
        let socket = null;
        let isConnecting = false;
        let lastConnectionAttempt = 0;
        const connectionCooldown = 5000;

        const statusDiv = document.getElementById('status');
        const logDiv = document.getElementById('log');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const clearLogBtn = document.getElementById('clearLogBtn');
        const usernameInput = document.getElementById('username');
        const inviteCodeInput = document.getElementById('inviteCode');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function updateStatus(status, className) {
            statusDiv.textContent = `Status: ${status}`;
            statusDiv.className = `status ${className}`;
        }

        function connect() {
            const username = usernameInput.value.trim();
            if (!username) {
                log('❌ Username is required');
                return;
            }

            // Check if already connected
            if (socket && socket.connected) {
                log('✅ Already connected');
                return;
            }

            // Prevent multiple simultaneous connections
            if (isConnecting) {
                log('⚠️ Connection already in progress');
                return;
            }

            // Prevent rapid reconnection attempts
            const now = Date.now();
            if (now - lastConnectionAttempt < connectionCooldown) {
                log('⚠️ Connection attempt too soon, waiting for cooldown...');
                return;
            }
            lastConnectionAttempt = now;

            isConnecting = true;
            updateStatus('Connecting...', 'connecting');
            log(`🔄 Connecting to https://chateye.onrender.com as ${username}...`);

            // Disconnect existing socket
            if (socket) {
                socket.removeAllListeners();
                socket.disconnect();
            }

            socket = io('https://chateye.onrender.com', {
                transports: ['websocket', 'polling'],
                timeout: 30000,
                reconnection: false,
                forceNew: true,
                auth: {
                    username: username,
                    inviteCode: inviteCodeInput.value.trim() || null
                }
            });

            socket.on('connect', () => {
                isConnecting = false;
                updateStatus('Connected', 'connected');
                log('✅ Connected successfully');
                socket.emit('join', { 
                    username: username, 
                    inviteCode: inviteCodeInput.value.trim() || null 
                });
            });

            socket.on('disconnect', (reason) => {
                isConnecting = false;
                updateStatus('Disconnected', 'disconnected');
                log(`🔌 Disconnected: ${reason}`);
            });

            socket.on('connect_error', (error) => {
                isConnecting = false;
                updateStatus('Connection Error', 'disconnected');
                log(`❌ Connection error: ${error.message || error}`);
            });

            socket.on('authError', (data) => {
                log(`❌ Authentication error: ${data.message}`);
            });

            socket.on('userGroups', (groups) => {
                log(`📁 Received ${groups.length} groups`);
            });

            socket.on('error', (error) => {
                log(`❌ Socket error: ${error.message || error}`);
            });
        }

        function disconnect() {
            if (socket) {
                log('🔌 Disconnecting...');
                socket.removeAllListeners();
                socket.disconnect();
                socket = null;
                isConnecting = false;
                updateStatus('Disconnected', 'disconnected');
                log('✅ Disconnected');
            }
        }

        function clearLog() {
            logDiv.innerHTML = '';
        }

        connectBtn.addEventListener('click', connect);
        disconnectBtn.addEventListener('click', disconnect);
        clearLogBtn.addEventListener('click', clearLog);

        // Initial log
        log('🚀 Socket test page loaded');
        log('Backend URL: https://chateye.onrender.com');
    </script>
</body>
</html>
