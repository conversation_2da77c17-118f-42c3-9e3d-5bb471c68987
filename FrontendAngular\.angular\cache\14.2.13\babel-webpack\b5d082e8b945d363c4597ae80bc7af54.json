{"ast": null, "code": "import { dateTimestampProvider } from './scheduler/dateTimestampProvider';\nexport class Scheduler {\n  constructor(schedulerActionCtor, now = Scheduler.now) {\n    this.schedulerActionCtor = schedulerActionCtor;\n    this.now = now;\n  }\n\n  schedule(work, delay = 0, state) {\n    return new this.schedulerActionCtor(this, work).schedule(state, delay);\n  }\n\n}\nScheduler.now = dateTimestampProvider.now;", "map": {"version": 3, "names": ["dateTimestampProvider", "Scheduler", "constructor", "schedulerActionCtor", "now", "schedule", "work", "delay", "state"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/rxjs/dist/esm/internal/Scheduler.js"], "sourcesContent": ["import { dateTimestampProvider } from './scheduler/dateTimestampProvider';\nexport class Scheduler {\n    constructor(schedulerActionCtor, now = Scheduler.now) {\n        this.schedulerActionCtor = schedulerActionCtor;\n        this.now = now;\n    }\n    schedule(work, delay = 0, state) {\n        return new this.schedulerActionCtor(this, work).schedule(state, delay);\n    }\n}\nScheduler.now = dateTimestampProvider.now;\n"], "mappings": "AAAA,SAASA,qBAAT,QAAsC,mCAAtC;AACA,OAAO,MAAMC,SAAN,CAAgB;EACnBC,WAAW,CAACC,mBAAD,EAAsBC,GAAG,GAAGH,SAAS,CAACG,GAAtC,EAA2C;IAClD,KAAKD,mBAAL,GAA2BA,mBAA3B;IACA,KAAKC,GAAL,GAAWA,GAAX;EACH;;EACDC,QAAQ,CAACC,IAAD,EAAOC,KAAK,GAAG,CAAf,EAAkBC,KAAlB,EAAyB;IAC7B,OAAO,IAAI,KAAKL,mBAAT,CAA6B,IAA7B,EAAmCG,IAAnC,EAAyCD,QAAzC,CAAkDG,KAAlD,EAAyDD,KAAzD,CAAP;EACH;;AAPkB;AASvBN,SAAS,CAACG,GAAV,GAAgBJ,qBAAqB,CAACI,GAAtC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}