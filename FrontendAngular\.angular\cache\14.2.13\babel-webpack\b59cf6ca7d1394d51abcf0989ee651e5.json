{"ast": null, "code": "import _asyncToGenerator from \"R:/chateye/FrontendAngular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { ApiService, Message, Group, User, SecurityInfo } from './api.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./api.service\";\nimport * as i2 from \"./socket.service\";\nimport * as i3 from \"./notification.service\";\nexport class ChatService {\n  constructor(apiService, socketService, notificationService) {\n    this.apiService = apiService;\n    this.socketService = socketService;\n    this.notificationService = notificationService;\n    this.userSubject = new BehaviorSubject(null);\n    this.messagesSubject = new BehaviorSubject([]);\n    this.onlineUsersSubject = new BehaviorSubject([]);\n    this.groupsSubject = new BehaviorSubject([]);\n    this.currentGroupSubject = new BehaviorSubject(null);\n    this.replyToSubject = new BehaviorSubject(null);\n    this.loadingSubject = new BehaviorSubject(false);\n    this.isAdminSubject = new BehaviorSubject(false);\n    this.showAdminPanelSubject = new BehaviorSubject(false);\n    this.securityInfoSubject = new BehaviorSubject(null);\n    this.editingMessageSubject = new BehaviorSubject(null); // Public observables\n\n    this.user$ = this.userSubject.asObservable();\n    this.messages$ = this.messagesSubject.asObservable();\n    this.onlineUsers$ = this.onlineUsersSubject.asObservable();\n    this.groups$ = this.groupsSubject.asObservable();\n    this.currentGroup$ = this.currentGroupSubject.asObservable();\n    this.replyTo$ = this.replyToSubject.asObservable();\n    this.loading$ = this.loadingSubject.asObservable();\n    this.isAdmin$ = this.isAdminSubject.asObservable();\n    this.showAdminPanel$ = this.showAdminPanelSubject.asObservable();\n    this.securityInfo$ = this.securityInfoSubject.asObservable();\n    this.editingMessage$ = this.editingMessageSubject.asObservable();\n    this.connected$ = this.socketService.isConnected$; // Computed observables\n\n    this.isLoggedIn$ = this.user$.pipe(map(user => !!user));\n\n    try {\n      this.setupSocketListeners();\n      this.setupMessageRefresh();\n    } catch (error) {\n      console.error('Error initializing ChatService:', error);\n    }\n  }\n\n  setupSocketListeners() {\n    this.socketService.on('connect', () => {\n      console.log('Connected to server'); // Clear any loading states when connected\n\n      this.loadingSubject.next(false);\n    });\n    this.socketService.on('disconnect', () => {\n      console.log('Disconnected from server'); // Don't set loading to true on disconnect to avoid UI freeze\n    });\n    this.socketService.on('connect_error', error => {\n      console.error('Socket connection error:', error);\n      this.loadingSubject.next(false);\n    });\n    this.socketService.on('userGroups', userGroups => {\n      console.log('Received user groups:', userGroups);\n      this.groupsSubject.next(userGroups || []);\n\n      if (userGroups && userGroups.length > 0) {\n        console.log('Auto-selecting first group:', userGroups[0]);\n        this.currentGroupSubject.next(userGroups[0]); // Load messages for the selected group\n\n        this.loadRecentMessages(userGroups[0].id);\n      } else {\n        console.log('No groups available for user'); // Clear current group and messages if no groups\n\n        this.currentGroupSubject.next(null);\n        this.messagesSubject.next([]);\n      }\n    });\n    this.socketService.on('recentMessages', messages => {\n      console.log('Received recent messages:', messages.length);\n      this.messagesSubject.next(messages || []);\n    });\n    this.socketService.on('groupJoined', ({\n      groupId\n    }) => {\n      console.log('Joined group:', groupId);\n      const groups = this.groupsSubject.value;\n      const group = groups.find(g => g.id === groupId);\n\n      if (group) {\n        this.currentGroupSubject.next(group);\n      }\n    });\n    this.socketService.on('newMessage', message => {\n      console.log('New message received:', message);\n      const currentMessages = this.messagesSubject.value;\n      this.messagesSubject.next([...currentMessages, message]); // Show notification if not current user and window not focused\n\n      const currentUser = this.userSubject.value;\n\n      if (message.username !== currentUser && document.hidden) {\n        this.notificationService.showMessageNotification(message.username, message.text);\n      }\n    });\n    this.socketService.on('reactionUpdate', ({\n      messageId,\n      reactions\n    }) => {\n      console.log('Reaction update:', messageId, reactions);\n      const currentMessages = this.messagesSubject.value;\n      this.messagesSubject.next(currentMessages.map(msg => msg.id === messageId ? { ...msg,\n        reactions\n      } : msg));\n    });\n    this.socketService.on('onlineUsersUpdate', users => {\n      console.log('Online users updated:', users);\n      this.onlineUsersSubject.next(users || []);\n    });\n    this.socketService.on('userJoined', ({\n      username\n    }) => {\n      console.log('User joined:', username); // Online users will be updated via onlineUsersUpdate event\n    });\n    this.socketService.on('userLeft', ({\n      username\n    }) => {\n      console.log('User left:', username); // Online users will be updated via onlineUsersUpdate event\n    });\n    this.socketService.on('messageUpdated', ({\n      messageId,\n      newText,\n      updatedAt\n    }) => {\n      console.log('Message updated:', messageId, newText);\n      const currentMessages = this.messagesSubject.value;\n      this.messagesSubject.next(currentMessages.map(msg => msg.id === messageId ? { ...msg,\n        text: newText,\n        updated_at: updatedAt\n      } : msg));\n    });\n    this.socketService.on('messageDeleted', ({\n      messageId\n    }) => {\n      console.log('Message deleted:', messageId);\n      const currentMessages = this.messagesSubject.value;\n      this.messagesSubject.next(currentMessages.filter(msg => msg.id !== messageId));\n    });\n    this.socketService.on('error', error => {\n      console.error('Socket error:', error);\n    });\n  }\n\n  setupMessageRefresh() {\n    // Clear any existing interval first\n    if (this.messageRefreshInterval) {\n      clearInterval(this.messageRefreshInterval);\n    } // Refresh messages every 5 seconds if socket is not connected\n\n\n    this.messageRefreshInterval = setInterval(() => {\n      try {\n        if (!this.socketService.isConnectedSubject.value) {\n          const currentGroup = this.currentGroupSubject.value;\n\n          if (currentGroup) {\n            console.log('Refreshing messages via HTTP API');\n            this.loadRecentMessages(currentGroup.id);\n          }\n        }\n      } catch (error) {\n        console.error('Error in message refresh interval:', error); // Clear the interval if there's an error to prevent crashes\n\n        if (this.messageRefreshInterval) {\n          clearInterval(this.messageRefreshInterval);\n          this.messageRefreshInterval = null;\n        }\n      }\n    }, 5000);\n  }\n\n  login(_x, _x2) {\n    var _this = this;\n\n    return _asyncToGenerator(function* (username, password, inviteCode = null) {\n      try {\n        _this.loadingSubject.next(true);\n\n        console.log('Starting login process for:', username);\n        const userData = yield _this.apiService.loginUser(username, password, inviteCode || undefined).toPromise();\n        console.log('Login API response:', userData); // Check if password is required\n\n        if (userData?.requiresPassword) {\n          throw new Error('Password required for this user');\n        } // Connect to socket with auth data\n\n\n        console.log('Connecting to socket...');\n\n        _this.socketService.connect(username, password, inviteCode);\n\n        _this.userSubject.next(username);\n\n        _this.isAdminSubject.next(userData?.isAdmin || false);\n\n        _this.securityInfoSubject.next(userData?.securityInfo || null);\n\n        console.log('Login completed successfully'); // Wait for socket connection before loading groups\n        // The socket will emit 'userGroups' event which will handle group loading\n        // This prevents race conditions between API and socket calls\n      } catch (error) {\n        console.error('Login failed:', error);\n        throw error;\n      } finally {\n        _this.loadingSubject.next(false);\n      }\n    }).apply(this, arguments);\n  }\n\n  loadRecentMessages(groupId) {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        console.log('Loading recent messages for group:', groupId);\n        const messages = yield _this2.apiService.getMessages(groupId, 50).toPromise();\n        console.log('Loaded messages:', messages?.length || 0);\n\n        _this2.messagesSubject.next(messages || []);\n      } catch (error) {\n        console.error('Failed to load messages:', error); // Set empty array to prevent UI from showing stale data\n\n        _this2.messagesSubject.next([]);\n      }\n    })();\n  }\n\n  loadUserGroups(username) {\n    var _this3 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        console.log('Loading user groups for:', username);\n        const groups = yield _this3.apiService.getUserGroups(username).toPromise();\n        console.log('Loaded user groups:', groups);\n\n        _this3.groupsSubject.next(groups || []);\n\n        if (groups && groups.length > 0) {\n          console.log('Auto-selecting first group:', groups[0]);\n\n          _this3.currentGroupSubject.next(groups[0]);\n\n          _this3.loadRecentMessages(groups[0].id);\n        }\n      } catch (error) {\n        console.error('Failed to load user groups:', error);\n      }\n    })();\n  }\n\n  joinGroup(groupId) {\n    const currentUser = this.userSubject.value;\n\n    if (!groupId || !currentUser) {\n      console.error('Cannot join group - missing groupId or user');\n      return;\n    }\n\n    console.log('Joining group:', groupId); // Check if socket is connected before joining\n\n    if (!this.socketService.isConnectedSubject.value) {\n      console.error('Cannot join group - socket not connected');\n      return;\n    }\n\n    this.socketService.joinGroup(groupId);\n    const groups = this.groupsSubject.value;\n    const group = groups.find(g => g.id === groupId);\n\n    if (group) {\n      this.currentGroupSubject.next(group); // Load recent messages for the group\n\n      this.loadRecentMessages(groupId);\n    } else {\n      console.error('Group not found in user groups:', groupId);\n    }\n  }\n\n  sendMessage(text, replyToId = null) {\n    const currentUser = this.userSubject.value;\n    const currentGroup = this.currentGroupSubject.value;\n\n    if (!text.trim()) {\n      console.error('Cannot send message - empty text');\n      return;\n    }\n\n    if (!currentUser) {\n      console.error('Cannot send message - user not logged in');\n      return;\n    }\n\n    if (!currentGroup) {\n      console.error('Cannot send message - no group selected. Available groups:', this.groupsSubject.value); // Try to auto-select the first available group\n\n      const groups = this.groupsSubject.value;\n\n      if (groups && groups.length > 0) {\n        console.log('Auto-selecting first available group:', groups[0]);\n        this.currentGroupSubject.next(groups[0]);\n        this.loadRecentMessages(groups[0].id); // Retry sending the message\n\n        setTimeout(() => this.sendMessage(text, replyToId), 100);\n        return;\n      } else {\n        console.error('No groups available for user');\n        return;\n      }\n    }\n\n    console.log('Sending message via socket:', {\n      text,\n      groupId: currentGroup.id,\n      replyToId\n    });\n    this.socketService.sendMessage(text, currentGroup.id, replyToId); // Fallback: If socket is not connected, try HTTP API\n\n    if (!this.socketService.isConnectedSubject.value) {\n      console.log('Socket not connected, trying HTTP API fallback');\n      this.apiService.sendMessage(text, currentUser, currentGroup.id, replyToId).subscribe({\n        next: response => {\n          console.log('Message sent via HTTP API:', response);\n        },\n        error: error => {\n          console.error('Failed to send message via HTTP API:', error);\n        }\n      });\n    }\n  }\n\n  replyToMessage(message) {\n    this.replyToSubject.next(message);\n  }\n\n  cancelReply() {\n    this.replyToSubject.next(null);\n  }\n\n  addReaction(messageId, emoji) {\n    this.socketService.addReaction(messageId, emoji);\n  }\n\n  removeReaction(data) {\n    this.socketService.removeReaction(data);\n  }\n\n  updateMessage(messageId, newText) {\n    var _this4 = this;\n\n    return _asyncToGenerator(function* () {\n      const currentUser = _this4.userSubject.value;\n\n      if (!currentUser) {\n        console.error('Cannot update message - user not logged in');\n        return;\n      }\n\n      try {\n        // Update the message in the local state immediately for better UX\n        const currentMessages = _this4.messagesSubject.value;\n\n        _this4.messagesSubject.next(currentMessages.map(msg => msg.id === messageId ? { ...msg,\n          text: newText,\n          updated_at: new Date().toISOString()\n        } : msg)); // Emit socket event for real-time updates\n\n\n        if (_this4.socketService.isConnectedSubject.value) {\n          _this4.socketService.emit('messageUpdated', {\n            messageId,\n            newText\n          });\n        } else {\n          // Fallback to HTTP API if socket not connected\n          const updatedMessage = yield _this4.apiService.updateMessage(messageId, newText, currentUser).toPromise();\n\n          _this4.messagesSubject.next(currentMessages.map(msg => msg.id === messageId ? { ...msg,\n            text: newText,\n            updated_at: updatedMessage.updated_at\n          } : msg));\n        }\n      } catch (error) {\n        console.error('Failed to update message:', error);\n        throw error;\n      }\n    })();\n  }\n\n  deleteMessage(messageId) {\n    var _this5 = this;\n\n    return _asyncToGenerator(function* () {\n      const currentUser = _this5.userSubject.value;\n\n      if (!currentUser) {\n        console.error('Cannot delete message - user not logged in');\n        return;\n      }\n\n      try {\n        // Remove the message from local state immediately for better UX\n        const currentMessages = _this5.messagesSubject.value;\n\n        _this5.messagesSubject.next(currentMessages.filter(msg => msg.id !== messageId)); // Emit socket event for real-time updates\n\n\n        if (_this5.socketService.isConnectedSubject.value) {\n          _this5.socketService.emit('messageDeleted', {\n            messageId\n          });\n        } else {\n          // Fallback to HTTP API if socket not connected\n          yield _this5.apiService.deleteMessage(messageId, currentUser).toPromise();\n        }\n      } catch (error) {\n        console.error('Failed to delete message:', error);\n        throw error;\n      }\n    })();\n  }\n\n  showAdminPanel() {\n    this.showAdminPanelSubject.next(true);\n  }\n\n  hideAdminPanel() {\n    this.showAdminPanelSubject.next(false);\n  }\n\n  startEditingMessage(message) {\n    this.editingMessageSubject.next(message);\n  }\n\n  cancelEditingMessage() {\n    this.editingMessageSubject.next(null);\n  }\n\n  logout() {\n    try {\n      // Disconnect socket first\n      this.socketService.disconnect(); // Clear interval\n\n      if (this.messageRefreshInterval) {\n        clearInterval(this.messageRefreshInterval);\n        this.messageRefreshInterval = null;\n      } // Reset all subjects\n\n\n      this.userSubject.next(null);\n      this.messagesSubject.next([]);\n      this.onlineUsersSubject.next([]);\n      this.groupsSubject.next([]);\n      this.currentGroupSubject.next(null);\n      this.replyToSubject.next(null);\n      this.loadingSubject.next(false);\n      this.isAdminSubject.next(false);\n      this.showAdminPanelSubject.next(false);\n      this.securityInfoSubject.next(null);\n      this.editingMessageSubject.next(null);\n      console.log('Logout completed successfully');\n    } catch (error) {\n      console.error('Error during logout:', error);\n    }\n  } // Method to refresh security info for all components\n\n\n  refreshSecurityInfo() {\n    this.apiService.getSecurityInfo().subscribe({\n      next: data => {\n        this.securityInfoSubject.next(data);\n      },\n      error: error => {\n        console.error('Failed to refresh security info:', error);\n      }\n    });\n  }\n\n}\n\nChatService.ɵfac = function ChatService_Factory(t) {\n  return new (t || ChatService)(i0.ɵɵinject(i1.ApiService), i0.ɵɵinject(i2.SocketService), i0.ɵɵinject(i3.NotificationService));\n};\n\nChatService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: ChatService,\n  factory: ChatService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "mappings": ";AACA,SAASA,eAAT,QAA2D,MAA3D;AACA,SAASC,GAAT,QAAoB,gBAApB;AACA,SAASC,UAAT,EAAqBC,OAArB,EAA8BC,KAA9B,EAAqCC,IAArC,EAA2CC,YAA3C,QAA8E,eAA9E;;;;;AAOA,OAAM,MAAOC,WAAP,CAAkB;EAgCtBC,YACUC,UADV,EAEUC,aAFV,EAGUC,mBAHV,EAGkD;IAFxC;IACA;IACA;IAlCF,mBAAc,IAAIX,eAAJ,CAAmC,IAAnC,CAAd;IACA,uBAAkB,IAAIA,eAAJ,CAA+B,EAA/B,CAAlB;IACA,0BAAqB,IAAIA,eAAJ,CAA4B,EAA5B,CAArB;IACA,qBAAgB,IAAIA,eAAJ,CAA6B,EAA7B,CAAhB;IACA,2BAAsB,IAAIA,eAAJ,CAAkC,IAAlC,CAAtB;IACA,sBAAiB,IAAIA,eAAJ,CAAoC,IAApC,CAAjB;IACA,sBAAiB,IAAIA,eAAJ,CAA6B,KAA7B,CAAjB;IACA,sBAAiB,IAAIA,eAAJ,CAA6B,KAA7B,CAAjB;IACA,6BAAwB,IAAIA,eAAJ,CAA6B,KAA7B,CAAxB;IACA,2BAAsB,IAAIA,eAAJ,CAAyC,IAAzC,CAAtB;IACA,6BAAwB,IAAIA,eAAJ,CAAoC,IAApC,CAAxB,CAwB0C,CAtBlD;;IACO,aAAQ,KAAKY,WAAL,CAAiBC,YAAjB,EAAR;IACA,iBAAY,KAAKC,eAAL,CAAqBD,YAArB,EAAZ;IACA,oBAAe,KAAKE,kBAAL,CAAwBF,YAAxB,EAAf;IACA,eAAU,KAAKG,aAAL,CAAmBH,YAAnB,EAAV;IACA,qBAAgB,KAAKI,mBAAL,CAAyBJ,YAAzB,EAAhB;IACA,gBAAW,KAAKK,cAAL,CAAoBL,YAApB,EAAX;IACA,gBAAW,KAAKM,cAAL,CAAoBN,YAApB,EAAX;IACA,gBAAW,KAAKO,cAAL,CAAoBP,YAApB,EAAX;IACA,uBAAkB,KAAKQ,qBAAL,CAA2BR,YAA3B,EAAlB;IACA,qBAAgB,KAAKS,mBAAL,CAAyBT,YAAzB,EAAhB;IACA,uBAAkB,KAAKU,qBAAL,CAA2BV,YAA3B,EAAlB;IACA,kBAAa,KAAKH,aAAL,CAAmBc,YAAhC,CAU2C,CARlD;;IACO,mBAAc,KAAKC,KAAL,CAAWC,IAAX,CAAgBzB,GAAG,CAAC0B,IAAI,IAAI,CAAC,CAACA,IAAX,CAAnB,CAAd;;IASL,IAAI;MACF,KAAKC,oBAAL;MACA,KAAKC,mBAAL;IACD,CAHD,CAGE,OAAOC,KAAP,EAAc;MACdC,OAAO,CAACD,KAAR,CAAc,iCAAd,EAAiDA,KAAjD;IACD;EACF;;EAEOF,oBAAoB;IAC1B,KAAKlB,aAAL,CAAmBsB,EAAnB,CAAsB,SAAtB,EAAiC,MAAK;MACpCD,OAAO,CAACE,GAAR,CAAY,qBAAZ,EADoC,CAEpC;;MACA,KAAKd,cAAL,CAAoBe,IAApB,CAAyB,KAAzB;IACD,CAJD;IAMA,KAAKxB,aAAL,CAAmBsB,EAAnB,CAAsB,YAAtB,EAAoC,MAAK;MACvCD,OAAO,CAACE,GAAR,CAAY,0BAAZ,EADuC,CAEvC;IACD,CAHD;IAKA,KAAKvB,aAAL,CAAmBsB,EAAnB,CAAsB,eAAtB,EAAwCF,KAAD,IAAU;MAC/CC,OAAO,CAACD,KAAR,CAAc,0BAAd,EAA0CA,KAA1C;MACA,KAAKX,cAAL,CAAoBe,IAApB,CAAyB,KAAzB;IACD,CAHD;IAKA,KAAKxB,aAAL,CAAmBsB,EAAnB,CAAsB,YAAtB,EAAqCG,UAAD,IAAwB;MAC1DJ,OAAO,CAACE,GAAR,CAAY,uBAAZ,EAAqCE,UAArC;MACA,KAAKnB,aAAL,CAAmBkB,IAAnB,CAAwBC,UAAU,IAAI,EAAtC;;MACA,IAAIA,UAAU,IAAIA,UAAU,CAACC,MAAX,GAAoB,CAAtC,EAAyC;QACvCL,OAAO,CAACE,GAAR,CAAY,6BAAZ,EAA2CE,UAAU,CAAC,CAAD,CAArD;QACA,KAAKlB,mBAAL,CAAyBiB,IAAzB,CAA8BC,UAAU,CAAC,CAAD,CAAxC,EAFuC,CAGvC;;QACA,KAAKE,kBAAL,CAAwBF,UAAU,CAAC,CAAD,CAAV,CAAcG,EAAtC;MACD,CALD,MAKO;QACLP,OAAO,CAACE,GAAR,CAAY,8BAAZ,EADK,CAEL;;QACA,KAAKhB,mBAAL,CAAyBiB,IAAzB,CAA8B,IAA9B;QACA,KAAKpB,eAAL,CAAqBoB,IAArB,CAA0B,EAA1B;MACD;IACF,CAdD;IAgBA,KAAKxB,aAAL,CAAmBsB,EAAnB,CAAsB,gBAAtB,EAAyCO,QAAD,IAAwB;MAC9DR,OAAO,CAACE,GAAR,CAAY,2BAAZ,EAAyCM,QAAQ,CAACH,MAAlD;MACA,KAAKtB,eAAL,CAAqBoB,IAArB,CAA0BK,QAAQ,IAAI,EAAtC;IACD,CAHD;IAKA,KAAK7B,aAAL,CAAmBsB,EAAnB,CAAsB,aAAtB,EAAqC,CAAC;MAAEQ;IAAF,CAAD,KAAqC;MACxET,OAAO,CAACE,GAAR,CAAY,eAAZ,EAA6BO,OAA7B;MACA,MAAMC,MAAM,GAAG,KAAKzB,aAAL,CAAmB0B,KAAlC;MACA,MAAMC,KAAK,GAAGF,MAAM,CAACG,IAAP,CAAYC,CAAC,IAAIA,CAAC,CAACP,EAAF,KAASE,OAA1B,CAAd;;MACA,IAAIG,KAAJ,EAAW;QACT,KAAK1B,mBAAL,CAAyBiB,IAAzB,CAA8BS,KAA9B;MACD;IACF,CAPD;IASA,KAAKjC,aAAL,CAAmBsB,EAAnB,CAAsB,YAAtB,EAAqCc,OAAD,IAAqB;MACvDf,OAAO,CAACE,GAAR,CAAY,uBAAZ,EAAqCa,OAArC;MACA,MAAMC,eAAe,GAAG,KAAKjC,eAAL,CAAqB4B,KAA7C;MACA,KAAK5B,eAAL,CAAqBoB,IAArB,CAA0B,CAAC,GAAGa,eAAJ,EAAqBD,OAArB,CAA1B,EAHuD,CAKvD;;MACA,MAAME,WAAW,GAAG,KAAKpC,WAAL,CAAiB8B,KAArC;;MACA,IAAII,OAAO,CAACG,QAAR,KAAqBD,WAArB,IAAoCE,QAAQ,CAACC,MAAjD,EAAyD;QACvD,KAAKxC,mBAAL,CAAyByC,uBAAzB,CAAiDN,OAAO,CAACG,QAAzD,EAAmEH,OAAO,CAACO,IAA3E;MACD;IACF,CAVD;IAYA,KAAK3C,aAAL,CAAmBsB,EAAnB,CAAsB,gBAAtB,EAAwC,CAAC;MAAEsB,SAAF;MAAaC;IAAb,CAAD,KAAsE;MAC5GxB,OAAO,CAACE,GAAR,CAAY,kBAAZ,EAAgCqB,SAAhC,EAA2CC,SAA3C;MACA,MAAMR,eAAe,GAAG,KAAKjC,eAAL,CAAqB4B,KAA7C;MACA,KAAK5B,eAAL,CAAqBoB,IAArB,CACEa,eAAe,CAAC9C,GAAhB,CAAoBuD,GAAG,IACrBA,GAAG,CAAClB,EAAJ,KAAWgB,SAAX,GACI,EAAE,GAAGE,GAAL;QAAUD;MAAV,CADJ,GAEIC,GAHN,CADF;IAOD,CAVD;IAYA,KAAK9C,aAAL,CAAmBsB,EAAnB,CAAsB,mBAAtB,EAA4CyB,KAAD,IAAkB;MAC3D1B,OAAO,CAACE,GAAR,CAAY,uBAAZ,EAAqCwB,KAArC;MACA,KAAK1C,kBAAL,CAAwBmB,IAAxB,CAA6BuB,KAAK,IAAI,EAAtC;IACD,CAHD;IAKA,KAAK/C,aAAL,CAAmBsB,EAAnB,CAAsB,YAAtB,EAAoC,CAAC;MAAEiB;IAAF,CAAD,KAAuC;MACzElB,OAAO,CAACE,GAAR,CAAY,cAAZ,EAA4BgB,QAA5B,EADyE,CAEzE;IACD,CAHD;IAKA,KAAKvC,aAAL,CAAmBsB,EAAnB,CAAsB,UAAtB,EAAkC,CAAC;MAAEiB;IAAF,CAAD,KAAuC;MACvElB,OAAO,CAACE,GAAR,CAAY,YAAZ,EAA0BgB,QAA1B,EADuE,CAEvE;IACD,CAHD;IAKA,KAAKvC,aAAL,CAAmBsB,EAAnB,CAAsB,gBAAtB,EAAwC,CAAC;MAAEsB,SAAF;MAAaI,OAAb;MAAsBC;IAAtB,CAAD,KAAiG;MACvI5B,OAAO,CAACE,GAAR,CAAY,kBAAZ,EAAgCqB,SAAhC,EAA2CI,OAA3C;MACA,MAAMX,eAAe,GAAG,KAAKjC,eAAL,CAAqB4B,KAA7C;MACA,KAAK5B,eAAL,CAAqBoB,IAArB,CACEa,eAAe,CAAC9C,GAAhB,CAAoBuD,GAAG,IACrBA,GAAG,CAAClB,EAAJ,KAAWgB,SAAX,GACI,EAAE,GAAGE,GAAL;QAAUH,IAAI,EAAEK,OAAhB;QAAyBE,UAAU,EAAED;MAArC,CADJ,GAEIH,GAHN,CADF;IAOD,CAVD;IAYA,KAAK9C,aAAL,CAAmBsB,EAAnB,CAAsB,gBAAtB,EAAwC,CAAC;MAAEsB;IAAF,CAAD,KAAyC;MAC/EvB,OAAO,CAACE,GAAR,CAAY,kBAAZ,EAAgCqB,SAAhC;MACA,MAAMP,eAAe,GAAG,KAAKjC,eAAL,CAAqB4B,KAA7C;MACA,KAAK5B,eAAL,CAAqBoB,IAArB,CACEa,eAAe,CAACc,MAAhB,CAAuBL,GAAG,IAAIA,GAAG,CAAClB,EAAJ,KAAWgB,SAAzC,CADF;IAGD,CAND;IAQA,KAAK5C,aAAL,CAAmBsB,EAAnB,CAAsB,OAAtB,EAAgCF,KAAD,IAAe;MAC5CC,OAAO,CAACD,KAAR,CAAc,eAAd,EAA+BA,KAA/B;IACD,CAFD;EAGD;;EAEOD,mBAAmB;IACzB;IACA,IAAI,KAAKiC,sBAAT,EAAiC;MAC/BC,aAAa,CAAC,KAAKD,sBAAN,CAAb;IACD,CAJwB,CAMzB;;;IACA,KAAKA,sBAAL,GAA8BE,WAAW,CAAC,MAAK;MAC7C,IAAI;QACF,IAAI,CAAC,KAAKtD,aAAL,CAAmBuD,kBAAnB,CAAsCvB,KAA3C,EAAkD;UAChD,MAAMwB,YAAY,GAAG,KAAKjD,mBAAL,CAAyByB,KAA9C;;UACA,IAAIwB,YAAJ,EAAkB;YAChBnC,OAAO,CAACE,GAAR,CAAY,kCAAZ;YACA,KAAKI,kBAAL,CAAwB6B,YAAY,CAAC5B,EAArC;UACD;QACF;MACF,CARD,CAQE,OAAOR,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,oCAAd,EAAoDA,KAApD,EADc,CAEd;;QACA,IAAI,KAAKgC,sBAAT,EAAiC;UAC/BC,aAAa,CAAC,KAAKD,sBAAN,CAAb;UACA,KAAKA,sBAAL,GAA8B,IAA9B;QACD;MACF;IACF,CAjBwC,EAiBtC,IAjBsC,CAAzC;EAkBD;;EAEKK,KAAK,UAAsE;IAAA;;IAAA,oCAArElB,QAAqE,EAAnDmB,QAAmD,EAAhCC,aAA4B,IAAI;MAC/E,IAAI;QACF,KAAI,CAAClD,cAAL,CAAoBe,IAApB,CAAyB,IAAzB;;QACAH,OAAO,CAACE,GAAR,CAAY,6BAAZ,EAA2CgB,QAA3C;QAEA,MAAMqB,QAAQ,SAAS,KAAI,CAAC7D,UAAL,CAAgB8D,SAAhB,CAA0BtB,QAA1B,EAAoCmB,QAApC,EAA8CC,UAAU,IAAIG,SAA5D,EAAuEC,SAAvE,EAAvB;QACA1C,OAAO,CAACE,GAAR,CAAY,qBAAZ,EAAmCqC,QAAnC,EALE,CAOF;;QACA,IAAIA,QAAQ,EAAEI,gBAAd,EAAgC;UAC9B,MAAM,IAAIC,KAAJ,CAAU,iCAAV,CAAN;QACD,CAVC,CAYF;;;QACA5C,OAAO,CAACE,GAAR,CAAY,yBAAZ;;QACA,KAAI,CAACvB,aAAL,CAAmBkE,OAAnB,CAA2B3B,QAA3B,EAAqCmB,QAArC,EAA+CC,UAA/C;;QAEA,KAAI,CAACzD,WAAL,CAAiBsB,IAAjB,CAAsBe,QAAtB;;QACA,KAAI,CAAC7B,cAAL,CAAoBc,IAApB,CAAyBoC,QAAQ,EAAEO,OAAV,IAAqB,KAA9C;;QACA,KAAI,CAACvD,mBAAL,CAAyBY,IAAzB,CAA8BoC,QAAQ,EAAEQ,YAAV,IAA0B,IAAxD;;QAEA/C,OAAO,CAACE,GAAR,CAAY,8BAAZ,EApBE,CAsBF;QACA;QACA;MAED,CA1BD,CA0BE,OAAOH,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,eAAd,EAA+BA,KAA/B;QACA,MAAMA,KAAN;MACD,CA7BD,SA6BU;QACR,KAAI,CAACX,cAAL,CAAoBe,IAApB,CAAyB,KAAzB;MACD;IAhC8E;EAiChF;;EAEKG,kBAAkB,CAACG,OAAD,EAAgB;IAAA;;IAAA;MACtC,IAAI;QACFT,OAAO,CAACE,GAAR,CAAY,oCAAZ,EAAkDO,OAAlD;QACA,MAAMD,QAAQ,SAAS,MAAI,CAAC9B,UAAL,CAAgBsE,WAAhB,CAA4BvC,OAA5B,EAAqC,EAArC,EAAyCiC,SAAzC,EAAvB;QACA1C,OAAO,CAACE,GAAR,CAAY,kBAAZ,EAAgCM,QAAQ,EAAEH,MAAV,IAAoB,CAApD;;QACA,MAAI,CAACtB,eAAL,CAAqBoB,IAArB,CAA0BK,QAAQ,IAAI,EAAtC;MACD,CALD,CAKE,OAAOT,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,0BAAd,EAA0CA,KAA1C,EADc,CAEd;;QACA,MAAI,CAAChB,eAAL,CAAqBoB,IAArB,CAA0B,EAA1B;MACD;IAVqC;EAWvC;;EAEK8C,cAAc,CAAC/B,QAAD,EAAiB;IAAA;;IAAA;MACnC,IAAI;QACFlB,OAAO,CAACE,GAAR,CAAY,0BAAZ,EAAwCgB,QAAxC;QACA,MAAMR,MAAM,SAAS,MAAI,CAAChC,UAAL,CAAgBwE,aAAhB,CAA8BhC,QAA9B,EAAwCwB,SAAxC,EAArB;QACA1C,OAAO,CAACE,GAAR,CAAY,qBAAZ,EAAmCQ,MAAnC;;QACA,MAAI,CAACzB,aAAL,CAAmBkB,IAAnB,CAAwBO,MAAM,IAAI,EAAlC;;QACA,IAAIA,MAAM,IAAIA,MAAM,CAACL,MAAP,GAAgB,CAA9B,EAAiC;UAC/BL,OAAO,CAACE,GAAR,CAAY,6BAAZ,EAA2CQ,MAAM,CAAC,CAAD,CAAjD;;UACA,MAAI,CAACxB,mBAAL,CAAyBiB,IAAzB,CAA8BO,MAAM,CAAC,CAAD,CAApC;;UACA,MAAI,CAACJ,kBAAL,CAAwBI,MAAM,CAAC,CAAD,CAAN,CAAUH,EAAlC;QACD;MACF,CAVD,CAUE,OAAOR,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,6BAAd,EAA6CA,KAA7C;MACD;IAbkC;EAcpC;;EAEDoD,SAAS,CAAC1C,OAAD,EAAgB;IACvB,MAAMQ,WAAW,GAAG,KAAKpC,WAAL,CAAiB8B,KAArC;;IACA,IAAI,CAACF,OAAD,IAAY,CAACQ,WAAjB,EAA8B;MAC5BjB,OAAO,CAACD,KAAR,CAAc,6CAAd;MACA;IACD;;IAEDC,OAAO,CAACE,GAAR,CAAY,gBAAZ,EAA8BO,OAA9B,EAPuB,CASvB;;IACA,IAAI,CAAC,KAAK9B,aAAL,CAAmBuD,kBAAnB,CAAsCvB,KAA3C,EAAkD;MAChDX,OAAO,CAACD,KAAR,CAAc,0CAAd;MACA;IACD;;IAED,KAAKpB,aAAL,CAAmBwE,SAAnB,CAA6B1C,OAA7B;IACA,MAAMC,MAAM,GAAG,KAAKzB,aAAL,CAAmB0B,KAAlC;IACA,MAAMC,KAAK,GAAGF,MAAM,CAACG,IAAP,CAAYC,CAAC,IAAIA,CAAC,CAACP,EAAF,KAASE,OAA1B,CAAd;;IACA,IAAIG,KAAJ,EAAW;MACT,KAAK1B,mBAAL,CAAyBiB,IAAzB,CAA8BS,KAA9B,EADS,CAET;;MACA,KAAKN,kBAAL,CAAwBG,OAAxB;IACD,CAJD,MAIO;MACLT,OAAO,CAACD,KAAR,CAAc,iCAAd,EAAiDU,OAAjD;IACD;EACF;;EAED2C,WAAW,CAAC9B,IAAD,EAAe+B,YAA2B,IAA1C,EAA8C;IACvD,MAAMpC,WAAW,GAAG,KAAKpC,WAAL,CAAiB8B,KAArC;IACA,MAAMwB,YAAY,GAAG,KAAKjD,mBAAL,CAAyByB,KAA9C;;IAEA,IAAI,CAACW,IAAI,CAACgC,IAAL,EAAL,EAAkB;MAChBtD,OAAO,CAACD,KAAR,CAAc,kCAAd;MACA;IACD;;IAED,IAAI,CAACkB,WAAL,EAAkB;MAChBjB,OAAO,CAACD,KAAR,CAAc,0CAAd;MACA;IACD;;IAED,IAAI,CAACoC,YAAL,EAAmB;MACjBnC,OAAO,CAACD,KAAR,CAAc,4DAAd,EAA4E,KAAKd,aAAL,CAAmB0B,KAA/F,EADiB,CAEjB;;MACA,MAAMD,MAAM,GAAG,KAAKzB,aAAL,CAAmB0B,KAAlC;;MACA,IAAID,MAAM,IAAIA,MAAM,CAACL,MAAP,GAAgB,CAA9B,EAAiC;QAC/BL,OAAO,CAACE,GAAR,CAAY,uCAAZ,EAAqDQ,MAAM,CAAC,CAAD,CAA3D;QACA,KAAKxB,mBAAL,CAAyBiB,IAAzB,CAA8BO,MAAM,CAAC,CAAD,CAApC;QACA,KAAKJ,kBAAL,CAAwBI,MAAM,CAAC,CAAD,CAAN,CAAUH,EAAlC,EAH+B,CAI/B;;QACAgD,UAAU,CAAC,MAAM,KAAKH,WAAL,CAAiB9B,IAAjB,EAAuB+B,SAAvB,CAAP,EAA0C,GAA1C,CAAV;QACA;MACD,CAPD,MAOO;QACLrD,OAAO,CAACD,KAAR,CAAc,8BAAd;QACA;MACD;IACF;;IAEDC,OAAO,CAACE,GAAR,CAAY,6BAAZ,EAA2C;MAAEoB,IAAF;MAAQb,OAAO,EAAE0B,YAAY,CAAC5B,EAA9B;MAAkC8C;IAAlC,CAA3C;IACA,KAAK1E,aAAL,CAAmByE,WAAnB,CAA+B9B,IAA/B,EAAqCa,YAAY,CAAC5B,EAAlD,EAAsD8C,SAAtD,EAhCuD,CAkCvD;;IACA,IAAI,CAAC,KAAK1E,aAAL,CAAmBuD,kBAAnB,CAAsCvB,KAA3C,EAAkD;MAChDX,OAAO,CAACE,GAAR,CAAY,gDAAZ;MACA,KAAKxB,UAAL,CAAgB0E,WAAhB,CAA4B9B,IAA5B,EAAkCL,WAAlC,EAA+CkB,YAAY,CAAC5B,EAA5D,EAAgE8C,SAAhE,EAA2EG,SAA3E,CAAqF;QACnFrD,IAAI,EAAGsD,QAAD,IAAa;UACjBzD,OAAO,CAACE,GAAR,CAAY,4BAAZ,EAA0CuD,QAA1C;QACD,CAHkF;QAInF1D,KAAK,EAAGA,KAAD,IAAU;UACfC,OAAO,CAACD,KAAR,CAAc,sCAAd,EAAsDA,KAAtD;QACD;MANkF,CAArF;IAQD;EACF;;EAED2D,cAAc,CAAC3C,OAAD,EAAiB;IAC7B,KAAK5B,cAAL,CAAoBgB,IAApB,CAAyBY,OAAzB;EACD;;EAED4C,WAAW;IACT,KAAKxE,cAAL,CAAoBgB,IAApB,CAAyB,IAAzB;EACD;;EAEDyD,WAAW,CAACrC,SAAD,EAAoBsC,KAApB,EAAiC;IAC1C,KAAKlF,aAAL,CAAmBiF,WAAnB,CAA+BrC,SAA/B,EAA0CsC,KAA1C;EACD;;EAEDC,cAAc,CAACC,IAAD,EAA4C;IACxD,KAAKpF,aAAL,CAAmBmF,cAAnB,CAAkCC,IAAlC;EACD;;EAEKC,aAAa,CAACzC,SAAD,EAAoBI,OAApB,EAAmC;IAAA;;IAAA;MACpD,MAAMV,WAAW,GAAG,MAAI,CAACpC,WAAL,CAAiB8B,KAArC;;MACA,IAAI,CAACM,WAAL,EAAkB;QAChBjB,OAAO,CAACD,KAAR,CAAc,4CAAd;QACA;MACD;;MAED,IAAI;QACF;QACA,MAAMiB,eAAe,GAAG,MAAI,CAACjC,eAAL,CAAqB4B,KAA7C;;QACA,MAAI,CAAC5B,eAAL,CAAqBoB,IAArB,CACEa,eAAe,CAAC9C,GAAhB,CAAoBuD,GAAG,IACrBA,GAAG,CAAClB,EAAJ,KAAWgB,SAAX,GACI,EAAE,GAAGE,GAAL;UAAUH,IAAI,EAAEK,OAAhB;UAAyBE,UAAU,EAAE,IAAIoC,IAAJ,GAAWC,WAAX;QAArC,CADJ,GAEIzC,GAHN,CADF,EAHE,CAWF;;;QACA,IAAI,MAAI,CAAC9C,aAAL,CAAmBuD,kBAAnB,CAAsCvB,KAA1C,EAAiD;UAC/C,MAAI,CAAChC,aAAL,CAAmBwF,IAAnB,CAAwB,gBAAxB,EAA0C;YAAE5C,SAAF;YAAaI;UAAb,CAA1C;QACD,CAFD,MAEO;UACL;UACA,MAAMyC,cAAc,SAAS,MAAI,CAAC1F,UAAL,CAAgBsF,aAAhB,CAA8BzC,SAA9B,EAAyCI,OAAzC,EAAkDV,WAAlD,EAA+DyB,SAA/D,EAA7B;;UACA,MAAI,CAAC3D,eAAL,CAAqBoB,IAArB,CACEa,eAAe,CAAC9C,GAAhB,CAAoBuD,GAAG,IACrBA,GAAG,CAAClB,EAAJ,KAAWgB,SAAX,GACI,EAAE,GAAGE,GAAL;YAAUH,IAAI,EAAEK,OAAhB;YAAyBE,UAAU,EAAEuC,cAAc,CAACvC;UAApD,CADJ,GAEIJ,GAHN,CADF;QAOD;MACF,CAzBD,CAyBE,OAAO1B,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,2BAAd,EAA2CA,KAA3C;QACA,MAAMA,KAAN;MACD;IAnCmD;EAoCrD;;EAEKsE,aAAa,CAAC9C,SAAD,EAAkB;IAAA;;IAAA;MACnC,MAAMN,WAAW,GAAG,MAAI,CAACpC,WAAL,CAAiB8B,KAArC;;MACA,IAAI,CAACM,WAAL,EAAkB;QAChBjB,OAAO,CAACD,KAAR,CAAc,4CAAd;QACA;MACD;;MAED,IAAI;QACF;QACA,MAAMiB,eAAe,GAAG,MAAI,CAACjC,eAAL,CAAqB4B,KAA7C;;QACA,MAAI,CAAC5B,eAAL,CAAqBoB,IAArB,CACEa,eAAe,CAACc,MAAhB,CAAuBL,GAAG,IAAIA,GAAG,CAAClB,EAAJ,KAAWgB,SAAzC,CADF,EAHE,CAOF;;;QACA,IAAI,MAAI,CAAC5C,aAAL,CAAmBuD,kBAAnB,CAAsCvB,KAA1C,EAAiD;UAC/C,MAAI,CAAChC,aAAL,CAAmBwF,IAAnB,CAAwB,gBAAxB,EAA0C;YAAE5C;UAAF,CAA1C;QACD,CAFD,MAEO;UACL;UACA,MAAM,MAAI,CAAC7C,UAAL,CAAgB2F,aAAhB,CAA8B9C,SAA9B,EAAyCN,WAAzC,EAAsDyB,SAAtD,EAAN;QACD;MACF,CAdD,CAcE,OAAO3C,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,2BAAd,EAA2CA,KAA3C;QACA,MAAMA,KAAN;MACD;IAxBkC;EAyBpC;;EAEDuE,cAAc;IACZ,KAAKhF,qBAAL,CAA2Ba,IAA3B,CAAgC,IAAhC;EACD;;EAEDoE,cAAc;IACZ,KAAKjF,qBAAL,CAA2Ba,IAA3B,CAAgC,KAAhC;EACD;;EAEDqE,mBAAmB,CAACzD,OAAD,EAAiB;IAClC,KAAKvB,qBAAL,CAA2BW,IAA3B,CAAgCY,OAAhC;EACD;;EAED0D,oBAAoB;IAClB,KAAKjF,qBAAL,CAA2BW,IAA3B,CAAgC,IAAhC;EACD;;EAEDuE,MAAM;IACJ,IAAI;MACF;MACA,KAAK/F,aAAL,CAAmBgG,UAAnB,GAFE,CAIF;;MACA,IAAI,KAAK5C,sBAAT,EAAiC;QAC/BC,aAAa,CAAC,KAAKD,sBAAN,CAAb;QACA,KAAKA,sBAAL,GAA8B,IAA9B;MACD,CARC,CAUF;;;MACA,KAAKlD,WAAL,CAAiBsB,IAAjB,CAAsB,IAAtB;MACA,KAAKpB,eAAL,CAAqBoB,IAArB,CAA0B,EAA1B;MACA,KAAKnB,kBAAL,CAAwBmB,IAAxB,CAA6B,EAA7B;MACA,KAAKlB,aAAL,CAAmBkB,IAAnB,CAAwB,EAAxB;MACA,KAAKjB,mBAAL,CAAyBiB,IAAzB,CAA8B,IAA9B;MACA,KAAKhB,cAAL,CAAoBgB,IAApB,CAAyB,IAAzB;MACA,KAAKf,cAAL,CAAoBe,IAApB,CAAyB,KAAzB;MACA,KAAKd,cAAL,CAAoBc,IAApB,CAAyB,KAAzB;MACA,KAAKb,qBAAL,CAA2Ba,IAA3B,CAAgC,KAAhC;MACA,KAAKZ,mBAAL,CAAyBY,IAAzB,CAA8B,IAA9B;MACA,KAAKX,qBAAL,CAA2BW,IAA3B,CAAgC,IAAhC;MAEAH,OAAO,CAACE,GAAR,CAAY,+BAAZ;IACD,CAxBD,CAwBE,OAAOH,KAAP,EAAc;MACdC,OAAO,CAACD,KAAR,CAAc,sBAAd,EAAsCA,KAAtC;IACD;EACF,CA/bqB,CAictB;;;EACA6E,mBAAmB;IACjB,KAAKlG,UAAL,CAAgBmG,eAAhB,GAAkCrB,SAAlC,CAA4C;MAC1CrD,IAAI,EAAG4D,IAAD,IAAS;QACb,KAAKxE,mBAAL,CAAyBY,IAAzB,CAA8B4D,IAA9B;MACD,CAHyC;MAI1ChE,KAAK,EAAGA,KAAD,IAAU;QACfC,OAAO,CAACD,KAAR,CAAc,kCAAd,EAAkDA,KAAlD;MACD;IANyC,CAA5C;EAQD;;AA3cqB;;;mBAAXvB,aAAWsG;AAAA;;;SAAXtG;EAAWuG,SAAXvG,WAAW;EAAAwG,YAFV", "names": ["BehaviorSubject", "map", "ApiService", "Message", "Group", "User", "SecurityInfo", "ChatService", "constructor", "apiService", "socketService", "notificationService", "userSubject", "asObservable", "messagesSubject", "onlineUsersSubject", "groupsSubject", "currentGroupSubject", "replyToSubject", "loadingSubject", "isAdminSubject", "showAdminPanelSubject", "securityInfoSubject", "editingMessageSubject", "isConnected$", "user$", "pipe", "user", "setupSocketListeners", "setupMessageRefresh", "error", "console", "on", "log", "next", "userGroups", "length", "loadRecentMessages", "id", "messages", "groupId", "groups", "value", "group", "find", "g", "message", "currentMessages", "currentUser", "username", "document", "hidden", "showMessageNotification", "text", "messageId", "reactions", "msg", "users", "newText", "updatedAt", "updated_at", "filter", "messageRefreshInterval", "clearInterval", "setInterval", "isConnectedSubject", "currentGroup", "login", "password", "inviteCode", "userData", "loginUser", "undefined", "to<PERSON>romise", "requiresPassword", "Error", "connect", "isAdmin", "securityInfo", "getMessages", "loadUserGroups", "getUserGroups", "joinGroup", "sendMessage", "replyToId", "trim", "setTimeout", "subscribe", "response", "replyToMessage", "cancelReply", "addReaction", "emoji", "removeReaction", "data", "updateMessage", "Date", "toISOString", "emit", "updatedMessage", "deleteMessage", "showAdminPanel", "hideAdminPanel", "startEditingMessage", "cancelEditingMessage", "logout", "disconnect", "refreshSecurityInfo", "getSecurityInfo", "i0", "factory", "providedIn"], "sourceRoot": "", "sources": ["R:\\chateye\\FrontendAngular\\src\\app\\services\\chat.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable, combineLatest } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\nimport { ApiService, Message, Group, User, SecurityInfo, LoginResponse } from './api.service';\r\nimport { SocketService } from './socket.service';\r\nimport { NotificationService } from './notification.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ChatService {\r\n  private userSubject = new BehaviorSubject<string | null>(null);\r\n  private messagesSubject = new BehaviorSubject<Message[]>([]);\r\n  private onlineUsersSubject = new BehaviorSubject<User[]>([]);\r\n  private groupsSubject = new BehaviorSubject<Group[]>([]);\r\n  private currentGroupSubject = new BehaviorSubject<Group | null>(null);\r\n  private replyToSubject = new BehaviorSubject<Message | null>(null);\r\n  private loadingSubject = new BehaviorSubject<boolean>(false);\r\n  private isAdminSubject = new BehaviorSubject<boolean>(false);\r\n  private showAdminPanelSubject = new BehaviorSubject<boolean>(false);\r\n  private securityInfoSubject = new BehaviorSubject<SecurityInfo | null>(null);\r\n  private editingMessageSubject = new BehaviorSubject<Message | null>(null);\r\n\r\n  // Public observables\r\n  public user$ = this.userSubject.asObservable();\r\n  public messages$ = this.messagesSubject.asObservable();\r\n  public onlineUsers$ = this.onlineUsersSubject.asObservable();\r\n  public groups$ = this.groupsSubject.asObservable();\r\n  public currentGroup$ = this.currentGroupSubject.asObservable();\r\n  public replyTo$ = this.replyToSubject.asObservable();\r\n  public loading$ = this.loadingSubject.asObservable();\r\n  public isAdmin$ = this.isAdminSubject.asObservable();\r\n  public showAdminPanel$ = this.showAdminPanelSubject.asObservable();\r\n  public securityInfo$ = this.securityInfoSubject.asObservable();\r\n  public editingMessage$ = this.editingMessageSubject.asObservable();\r\n  public connected$ = this.socketService.isConnected$;\r\n\r\n  // Computed observables\r\n  public isLoggedIn$ = this.user$.pipe(map(user => !!user));\r\n\r\n  private messageRefreshInterval: any;\r\n\r\n  constructor(\r\n    private apiService: ApiService,\r\n    private socketService: SocketService,\r\n    private notificationService: NotificationService\r\n  ) {\r\n    try {\r\n      this.setupSocketListeners();\r\n      this.setupMessageRefresh();\r\n    } catch (error) {\r\n      console.error('Error initializing ChatService:', error);\r\n    }\r\n  }\r\n\r\n  private setupSocketListeners(): void {\r\n    this.socketService.on('connect', () => {\r\n      console.log('Connected to server');\r\n      // Clear any loading states when connected\r\n      this.loadingSubject.next(false);\r\n    });\r\n\r\n    this.socketService.on('disconnect', () => {\r\n      console.log('Disconnected from server');\r\n      // Don't set loading to true on disconnect to avoid UI freeze\r\n    });\r\n\r\n    this.socketService.on('connect_error', (error) => {\r\n      console.error('Socket connection error:', error);\r\n      this.loadingSubject.next(false);\r\n    });\r\n\r\n    this.socketService.on('userGroups', (userGroups: Group[]) => {\r\n      console.log('Received user groups:', userGroups);\r\n      this.groupsSubject.next(userGroups || []);\r\n      if (userGroups && userGroups.length > 0) {\r\n        console.log('Auto-selecting first group:', userGroups[0]);\r\n        this.currentGroupSubject.next(userGroups[0]);\r\n        // Load messages for the selected group\r\n        this.loadRecentMessages(userGroups[0].id);\r\n      } else {\r\n        console.log('No groups available for user');\r\n        // Clear current group and messages if no groups\r\n        this.currentGroupSubject.next(null);\r\n        this.messagesSubject.next([]);\r\n      }\r\n    });\r\n\r\n    this.socketService.on('recentMessages', (messages: Message[]) => {\r\n      console.log('Received recent messages:', messages.length);\r\n      this.messagesSubject.next(messages || []);\r\n    });\r\n\r\n    this.socketService.on('groupJoined', ({ groupId }: { groupId: string }) => {\r\n      console.log('Joined group:', groupId);\r\n      const groups = this.groupsSubject.value;\r\n      const group = groups.find(g => g.id === groupId);\r\n      if (group) {\r\n        this.currentGroupSubject.next(group);\r\n      }\r\n    });\r\n\r\n    this.socketService.on('newMessage', (message: Message) => {\r\n      console.log('New message received:', message);\r\n      const currentMessages = this.messagesSubject.value;\r\n      this.messagesSubject.next([...currentMessages, message]);\r\n      \r\n      // Show notification if not current user and window not focused\r\n      const currentUser = this.userSubject.value;\r\n      if (message.username !== currentUser && document.hidden) {\r\n        this.notificationService.showMessageNotification(message.username, message.text);\r\n      }\r\n    });\r\n\r\n    this.socketService.on('reactionUpdate', ({ messageId, reactions }: { messageId: string; reactions: any[] }) => {\r\n      console.log('Reaction update:', messageId, reactions);\r\n      const currentMessages = this.messagesSubject.value;\r\n      this.messagesSubject.next(\r\n        currentMessages.map(msg => \r\n          msg.id === messageId \r\n            ? { ...msg, reactions }\r\n            : msg\r\n        )\r\n      );\r\n    });\r\n\r\n    this.socketService.on('onlineUsersUpdate', (users: User[]) => {\r\n      console.log('Online users updated:', users);\r\n      this.onlineUsersSubject.next(users || []);\r\n    });\r\n\r\n    this.socketService.on('userJoined', ({ username }: { username: string }) => {\r\n      console.log('User joined:', username);\r\n      // Online users will be updated via onlineUsersUpdate event\r\n    });\r\n\r\n    this.socketService.on('userLeft', ({ username }: { username: string }) => {\r\n      console.log('User left:', username);\r\n      // Online users will be updated via onlineUsersUpdate event\r\n    });\r\n\r\n    this.socketService.on('messageUpdated', ({ messageId, newText, updatedAt }: { messageId: string; newText: string; updatedAt: string }) => {\r\n      console.log('Message updated:', messageId, newText);\r\n      const currentMessages = this.messagesSubject.value;\r\n      this.messagesSubject.next(\r\n        currentMessages.map(msg => \r\n          msg.id === messageId \r\n            ? { ...msg, text: newText, updated_at: updatedAt }\r\n            : msg\r\n        )\r\n      );\r\n    });\r\n\r\n    this.socketService.on('messageDeleted', ({ messageId }: { messageId: string }) => {\r\n      console.log('Message deleted:', messageId);\r\n      const currentMessages = this.messagesSubject.value;\r\n      this.messagesSubject.next(\r\n        currentMessages.filter(msg => msg.id !== messageId)\r\n      );\r\n    });\r\n\r\n    this.socketService.on('error', (error: any) => {\r\n      console.error('Socket error:', error);\r\n    });\r\n  }\r\n\r\n  private setupMessageRefresh(): void {\r\n    // Clear any existing interval first\r\n    if (this.messageRefreshInterval) {\r\n      clearInterval(this.messageRefreshInterval);\r\n    }\r\n    \r\n    // Refresh messages every 5 seconds if socket is not connected\r\n    this.messageRefreshInterval = setInterval(() => {\r\n      try {\r\n        if (!this.socketService.isConnectedSubject.value) {\r\n          const currentGroup = this.currentGroupSubject.value;\r\n          if (currentGroup) {\r\n            console.log('Refreshing messages via HTTP API');\r\n            this.loadRecentMessages(currentGroup.id);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Error in message refresh interval:', error);\r\n        // Clear the interval if there's an error to prevent crashes\r\n        if (this.messageRefreshInterval) {\r\n          clearInterval(this.messageRefreshInterval);\r\n          this.messageRefreshInterval = null;\r\n        }\r\n      }\r\n    }, 5000);\r\n  }\r\n\r\n  async login(username: string, password?: string, inviteCode: string | null = null): Promise<void> {\r\n    try {\r\n      this.loadingSubject.next(true);\r\n      console.log('Starting login process for:', username);\r\n      \r\n      const userData = await this.apiService.loginUser(username, password, inviteCode || undefined).toPromise();\r\n      console.log('Login API response:', userData);\r\n      \r\n      // Check if password is required\r\n      if (userData?.requiresPassword) {\r\n        throw new Error('Password required for this user');\r\n      }\r\n      \r\n      // Connect to socket with auth data\r\n      console.log('Connecting to socket...');\r\n      this.socketService.connect(username, password, inviteCode);\r\n      \r\n      this.userSubject.next(username);\r\n      this.isAdminSubject.next(userData?.isAdmin || false);\r\n      this.securityInfoSubject.next(userData?.securityInfo || null);\r\n      \r\n      console.log('Login completed successfully');\r\n      \r\n      // Wait for socket connection before loading groups\r\n      // The socket will emit 'userGroups' event which will handle group loading\r\n      // This prevents race conditions between API and socket calls\r\n      \r\n    } catch (error) {\r\n      console.error('Login failed:', error);\r\n      throw error;\r\n    } finally {\r\n      this.loadingSubject.next(false);\r\n    }\r\n  }\r\n\r\n  async loadRecentMessages(groupId: string): Promise<void> {\r\n    try {\r\n      console.log('Loading recent messages for group:', groupId);\r\n      const messages = await this.apiService.getMessages(groupId, 50).toPromise();\r\n      console.log('Loaded messages:', messages?.length || 0);\r\n      this.messagesSubject.next(messages || []);\r\n    } catch (error) {\r\n      console.error('Failed to load messages:', error);\r\n      // Set empty array to prevent UI from showing stale data\r\n      this.messagesSubject.next([]);\r\n    }\r\n  }\r\n\r\n  async loadUserGroups(username: string): Promise<void> {\r\n    try {\r\n      console.log('Loading user groups for:', username);\r\n      const groups = await this.apiService.getUserGroups(username).toPromise();\r\n      console.log('Loaded user groups:', groups);\r\n      this.groupsSubject.next(groups || []);\r\n      if (groups && groups.length > 0) {\r\n        console.log('Auto-selecting first group:', groups[0]);\r\n        this.currentGroupSubject.next(groups[0]);\r\n        this.loadRecentMessages(groups[0].id);\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to load user groups:', error);\r\n    }\r\n  }\r\n\r\n  joinGroup(groupId: string): void {\r\n    const currentUser = this.userSubject.value;\r\n    if (!groupId || !currentUser) {\r\n      console.error('Cannot join group - missing groupId or user');\r\n      return;\r\n    }\r\n    \r\n    console.log('Joining group:', groupId);\r\n    \r\n    // Check if socket is connected before joining\r\n    if (!this.socketService.isConnectedSubject.value) {\r\n      console.error('Cannot join group - socket not connected');\r\n      return;\r\n    }\r\n    \r\n    this.socketService.joinGroup(groupId);\r\n    const groups = this.groupsSubject.value;\r\n    const group = groups.find(g => g.id === groupId);\r\n    if (group) {\r\n      this.currentGroupSubject.next(group);\r\n      // Load recent messages for the group\r\n      this.loadRecentMessages(groupId);\r\n    } else {\r\n      console.error('Group not found in user groups:', groupId);\r\n    }\r\n  }\r\n\r\n  sendMessage(text: string, replyToId: string | null = null): void {\r\n    const currentUser = this.userSubject.value;\r\n    const currentGroup = this.currentGroupSubject.value;\r\n    \r\n    if (!text.trim()) {\r\n      console.error('Cannot send message - empty text');\r\n      return;\r\n    }\r\n    \r\n    if (!currentUser) {\r\n      console.error('Cannot send message - user not logged in');\r\n      return;\r\n    }\r\n    \r\n    if (!currentGroup) {\r\n      console.error('Cannot send message - no group selected. Available groups:', this.groupsSubject.value);\r\n      // Try to auto-select the first available group\r\n      const groups = this.groupsSubject.value;\r\n      if (groups && groups.length > 0) {\r\n        console.log('Auto-selecting first available group:', groups[0]);\r\n        this.currentGroupSubject.next(groups[0]);\r\n        this.loadRecentMessages(groups[0].id);\r\n        // Retry sending the message\r\n        setTimeout(() => this.sendMessage(text, replyToId), 100);\r\n        return;\r\n      } else {\r\n        console.error('No groups available for user');\r\n        return;\r\n      }\r\n    }\r\n\r\n    console.log('Sending message via socket:', { text, groupId: currentGroup.id, replyToId });\r\n    this.socketService.sendMessage(text, currentGroup.id, replyToId);\r\n    \r\n    // Fallback: If socket is not connected, try HTTP API\r\n    if (!this.socketService.isConnectedSubject.value) {\r\n      console.log('Socket not connected, trying HTTP API fallback');\r\n      this.apiService.sendMessage(text, currentUser, currentGroup.id, replyToId).subscribe({\r\n        next: (response) => {\r\n          console.log('Message sent via HTTP API:', response);\r\n        },\r\n        error: (error) => {\r\n          console.error('Failed to send message via HTTP API:', error);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  replyToMessage(message: Message): void {\r\n    this.replyToSubject.next(message);\r\n  }\r\n\r\n  cancelReply(): void {\r\n    this.replyToSubject.next(null);\r\n  }\r\n\r\n  addReaction(messageId: string, emoji: string): void {\r\n    this.socketService.addReaction(messageId, emoji);\r\n  }\r\n\r\n  removeReaction(data: { messageId: string; emoji?: string }): void {\r\n    this.socketService.removeReaction(data);\r\n  }\r\n\r\n  async updateMessage(messageId: string, newText: string): Promise<void> {\r\n    const currentUser = this.userSubject.value;\r\n    if (!currentUser) {\r\n      console.error('Cannot update message - user not logged in');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Update the message in the local state immediately for better UX\r\n      const currentMessages = this.messagesSubject.value;\r\n      this.messagesSubject.next(\r\n        currentMessages.map(msg => \r\n          msg.id === messageId \r\n            ? { ...msg, text: newText, updated_at: new Date().toISOString() }\r\n            : msg\r\n        )\r\n      );\r\n\r\n      // Emit socket event for real-time updates\r\n      if (this.socketService.isConnectedSubject.value) {\r\n        this.socketService.emit('messageUpdated', { messageId, newText });\r\n      } else {\r\n        // Fallback to HTTP API if socket not connected\r\n        const updatedMessage = await this.apiService.updateMessage(messageId, newText, currentUser).toPromise();\r\n        this.messagesSubject.next(\r\n          currentMessages.map(msg => \r\n            msg.id === messageId \r\n              ? { ...msg, text: newText, updated_at: updatedMessage.updated_at }\r\n              : msg\r\n          )\r\n        );\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to update message:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async deleteMessage(messageId: string): Promise<void> {\r\n    const currentUser = this.userSubject.value;\r\n    if (!currentUser) {\r\n      console.error('Cannot delete message - user not logged in');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Remove the message from local state immediately for better UX\r\n      const currentMessages = this.messagesSubject.value;\r\n      this.messagesSubject.next(\r\n        currentMessages.filter(msg => msg.id !== messageId)\r\n      );\r\n\r\n      // Emit socket event for real-time updates\r\n      if (this.socketService.isConnectedSubject.value) {\r\n        this.socketService.emit('messageDeleted', { messageId });\r\n      } else {\r\n        // Fallback to HTTP API if socket not connected\r\n        await this.apiService.deleteMessage(messageId, currentUser).toPromise();\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to delete message:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  showAdminPanel(): void {\r\n    this.showAdminPanelSubject.next(true);\r\n  }\r\n\r\n  hideAdminPanel(): void {\r\n    this.showAdminPanelSubject.next(false);\r\n  }\r\n\r\n  startEditingMessage(message: Message): void {\r\n    this.editingMessageSubject.next(message);\r\n  }\r\n\r\n  cancelEditingMessage(): void {\r\n    this.editingMessageSubject.next(null);\r\n  }\r\n\r\n  logout(): void {\r\n    try {\r\n      // Disconnect socket first\r\n      this.socketService.disconnect();\r\n      \r\n      // Clear interval\r\n      if (this.messageRefreshInterval) {\r\n        clearInterval(this.messageRefreshInterval);\r\n        this.messageRefreshInterval = null;\r\n      }\r\n      \r\n      // Reset all subjects\r\n      this.userSubject.next(null);\r\n      this.messagesSubject.next([]);\r\n      this.onlineUsersSubject.next([]);\r\n      this.groupsSubject.next([]);\r\n      this.currentGroupSubject.next(null);\r\n      this.replyToSubject.next(null);\r\n      this.loadingSubject.next(false);\r\n      this.isAdminSubject.next(false);\r\n      this.showAdminPanelSubject.next(false);\r\n      this.securityInfoSubject.next(null);\r\n      this.editingMessageSubject.next(null);\r\n      \r\n      console.log('Logout completed successfully');\r\n    } catch (error) {\r\n      console.error('Error during logout:', error);\r\n    }\r\n  }\r\n\r\n  // Method to refresh security info for all components\r\n  refreshSecurityInfo(): void {\r\n    this.apiService.getSecurityInfo().subscribe({\r\n      next: (data) => {\r\n        this.securityInfoSubject.next(data);\r\n      },\r\n      error: (error) => {\r\n        console.error('Failed to refresh security info:', error);\r\n      }\r\n    });\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module"}