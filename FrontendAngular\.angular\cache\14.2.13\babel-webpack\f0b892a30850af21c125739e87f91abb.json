{"ast": null, "code": "import { merge } from './merge';\nexport function mergeWith(...otherSources) {\n  return merge(...otherSources);\n}", "map": {"version": 3, "names": ["merge", "mergeWith", "otherSources"], "sources": ["R:/chateye/FrontendAngular/node_modules/rxjs/dist/esm/internal/operators/mergeWith.js"], "sourcesContent": ["import { merge } from './merge';\nexport function mergeWith(...otherSources) {\n    return merge(...otherSources);\n}\n"], "mappings": "AAAA,SAASA,KAAT,QAAsB,SAAtB;AACA,OAAO,SAASC,SAAT,CAAmB,GAAGC,YAAtB,EAAoC;EACvC,OAAOF,KAAK,CAAC,GAAGE,YAAJ,CAAZ;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}