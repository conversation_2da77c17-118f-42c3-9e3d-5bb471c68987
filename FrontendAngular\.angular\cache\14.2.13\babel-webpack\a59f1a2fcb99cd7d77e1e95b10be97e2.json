{"ast": null, "code": "import * as i7 from '@angular/cdk/a11y';\nimport { FocusKeyManager, A11yModule } from '@angular/cdk/a11y';\nimport * as i5 from '@angular/cdk/observers';\nimport { ObserversModule } from '@angular/cdk/observers';\nimport * as i2 from '@angular/cdk/portal';\nimport { CdkPortal, TemplatePortal, CdkPortalOutlet, PortalModule } from '@angular/cdk/portal';\nimport * as i1$2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Inject, Optional, TemplateRef, Component, ChangeDetectionStrategy, ViewEncapsulation, ContentChild, ViewChild, Input, forwardRef, EventEmitter, Output, ContentChildren, QueryList, Attribute, NgModule } from '@angular/core';\nimport * as i4 from '@angular/material/core';\nimport { mixinDisabled, mixinColor, mixinDisableRipple, mixinTabIndex, MAT_RIPPLE_GLOBAL_OPTIONS, RippleRenderer, MatCommonModule, MatRippleModule } from '@angular/material/core';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { take, startWith, distinctUntilChanged, takeUntil, switchMap, skip, filter } from 'rxjs/operators';\nimport { Subject, Subscription, fromEvent, of, merge, EMPTY, Observable, timer } from 'rxjs';\nimport * as i1 from '@angular/cdk/bidi';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i1$1 from '@angular/cdk/scrolling';\nimport * as i3 from '@angular/cdk/platform';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport { hasModifierKey, SPACE, ENTER } from '@angular/cdk/keycodes';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Injection token for the MatInkBar's Positioner. */\n\nfunction MatTab_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\n\nconst _c0 = [\"*\"];\n\nfunction MatTabBody_ng_template_2_Template(rf, ctx) {}\n\nconst _c1 = function (a0) {\n  return {\n    animationDuration: a0\n  };\n};\n\nconst _c2 = function (a0, a1) {\n  return {\n    value: a0,\n    params: a1\n  };\n};\n\nconst _c3 = [\"tabListContainer\"];\nconst _c4 = [\"tabList\"];\nconst _c5 = [\"tabListInner\"];\nconst _c6 = [\"nextPaginator\"];\nconst _c7 = [\"previousPaginator\"];\nconst _c8 = [\"tabBodyWrapper\"];\nconst _c9 = [\"tabHeader\"];\n\nfunction MatTabGroup_div_2_ng_template_2_ng_template_0_Template(rf, ctx) {}\n\nfunction MatTabGroup_div_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MatTabGroup_div_2_ng_template_2_ng_template_0_Template, 0, 0, \"ng-template\", 10);\n  }\n\n  if (rf & 2) {\n    const tab_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"cdkPortalOutlet\", tab_r4.templateLabel);\n  }\n}\n\nfunction MatTabGroup_div_2_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const tab_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtextInterpolate(tab_r4.textLabel);\n  }\n}\n\nfunction MatTabGroup_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵlistener(\"click\", function MatTabGroup_div_2_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r13);\n      const tab_r4 = restoredCtx.$implicit;\n      const i_r5 = restoredCtx.index;\n      const ctx_r12 = i0.ɵɵnextContext();\n\n      const _r0 = i0.ɵɵreference(1);\n\n      return i0.ɵɵresetView(ctx_r12._handleClick(tab_r4, _r0, i_r5));\n    })(\"cdkFocusChange\", function MatTabGroup_div_2_Template_div_cdkFocusChange_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r13);\n      const i_r5 = restoredCtx.index;\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14._tabFocusChanged($event, i_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 7);\n    i0.ɵɵtemplate(2, MatTabGroup_div_2_ng_template_2_Template, 1, 1, \"ng-template\", 8);\n    i0.ɵɵtemplate(3, MatTabGroup_div_2_ng_template_3_Template, 1, 1, \"ng-template\", null, 9, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const tab_r4 = ctx.$implicit;\n    const i_r5 = ctx.index;\n\n    const _r7 = i0.ɵɵreference(4);\n\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"mat-tab-label-active\", ctx_r1.selectedIndex === i_r5);\n    i0.ɵɵproperty(\"id\", ctx_r1._getTabLabelId(i_r5))(\"ngClass\", tab_r4.labelClass)(\"disabled\", tab_r4.disabled)(\"matRippleDisabled\", tab_r4.disabled || ctx_r1.disableRipple);\n    i0.ɵɵattribute(\"tabIndex\", ctx_r1._getTabIndex(tab_r4, i_r5))(\"aria-posinset\", i_r5 + 1)(\"aria-setsize\", ctx_r1._tabs.length)(\"aria-controls\", ctx_r1._getTabContentId(i_r5))(\"aria-selected\", ctx_r1.selectedIndex === i_r5)(\"aria-label\", tab_r4.ariaLabel || null)(\"aria-labelledby\", !tab_r4.ariaLabel && tab_r4.ariaLabelledby ? tab_r4.ariaLabelledby : null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", tab_r4.templateLabel)(\"ngIfElse\", _r7);\n  }\n}\n\nfunction MatTabGroup_mat_tab_body_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"mat-tab-body\", 11);\n    i0.ɵɵlistener(\"_onCentered\", function MatTabGroup_mat_tab_body_5_Template_mat_tab_body__onCentered_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17._removeTabBodyWrapperHeight());\n    })(\"_onCentering\", function MatTabGroup_mat_tab_body_5_Template_mat_tab_body__onCentering_0_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19._setTabBodyWrapperHeight($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const tab_r15 = ctx.$implicit;\n    const i_r16 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"mat-tab-body-active\", ctx_r3.selectedIndex === i_r16);\n    i0.ɵɵproperty(\"id\", ctx_r3._getTabContentId(i_r16))(\"ngClass\", tab_r15.bodyClass)(\"content\", tab_r15.content)(\"position\", tab_r15.position)(\"origin\", tab_r15.origin)(\"animationDuration\", ctx_r3.animationDuration)(\"preserveContent\", ctx_r3.preserveContent);\n    i0.ɵɵattribute(\"tabindex\", ctx_r3.contentTabIndex != null && ctx_r3.selectedIndex === i_r16 ? ctx_r3.contentTabIndex : null)(\"aria-labelledby\", ctx_r3._getTabLabelId(i_r16));\n  }\n}\n\nconst _c10 = [\"mat-tab-nav-bar\", \"\"];\n\nconst _MAT_INK_BAR_POSITIONER = /*#__PURE__*/new InjectionToken('MatInkBarPositioner', {\n  providedIn: 'root',\n  factory: _MAT_INK_BAR_POSITIONER_FACTORY\n});\n/**\n * The default positioner function for the MatInkBar.\n * @docs-private\n */\n\n\nfunction _MAT_INK_BAR_POSITIONER_FACTORY() {\n  const method = element => ({\n    left: element ? (element.offsetLeft || 0) + 'px' : '0',\n    width: element ? (element.offsetWidth || 0) + 'px' : '0'\n  });\n\n  return method;\n}\n/**\n * The ink-bar is used to display and animate the line underneath the current active tab label.\n * @docs-private\n */\n\n\nlet MatInkBar = /*#__PURE__*/(() => {\n  class MatInkBar {\n    constructor(_elementRef, _ngZone, _inkBarPositioner, _animationMode) {\n      this._elementRef = _elementRef;\n      this._ngZone = _ngZone;\n      this._inkBarPositioner = _inkBarPositioner;\n      this._animationMode = _animationMode;\n    }\n    /**\n     * Calculates the styles from the provided element in order to align the ink-bar to that element.\n     * Shows the ink bar if previously set as hidden.\n     * @param element\n     */\n\n\n    alignToElement(element) {\n      this.show(); // `onStable` might not run for a while if the zone has already stabilized.\n      // Wrap the call in `NgZone.run` to ensure that it runs relatively soon.\n\n      this._ngZone.run(() => {\n        this._ngZone.onStable.pipe(take(1)).subscribe(() => {\n          const positions = this._inkBarPositioner(element);\n\n          const inkBar = this._elementRef.nativeElement;\n          inkBar.style.left = positions.left;\n          inkBar.style.width = positions.width;\n        });\n      });\n    }\n    /** Shows the ink bar. */\n\n\n    show() {\n      this._elementRef.nativeElement.style.visibility = 'visible';\n    }\n    /** Hides the ink bar. */\n\n\n    hide() {\n      this._elementRef.nativeElement.style.visibility = 'hidden';\n    }\n\n  }\n\n  MatInkBar.ɵfac = function MatInkBar_Factory(t) {\n    return new (t || MatInkBar)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(_MAT_INK_BAR_POSITIONER), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n  };\n\n  MatInkBar.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatInkBar,\n    selectors: [[\"mat-ink-bar\"]],\n    hostAttrs: [1, \"mat-ink-bar\"],\n    hostVars: 2,\n    hostBindings: function MatInkBar_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n      }\n    }\n  });\n  return MatInkBar;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Injection token that can be used to reference instances of `MatTabContent`. It serves as\n * alternative token to the actual `MatTabContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\n\n\nconst MAT_TAB_CONTENT = /*#__PURE__*/new InjectionToken('MatTabContent');\n/** Decorates the `ng-template` tags and reads out the template from it. */\n\nlet MatTabContent = /*#__PURE__*/(() => {\n  class MatTabContent {\n    constructor(\n    /** Content for the tab. */\n    template) {\n      this.template = template;\n    }\n\n  }\n\n  MatTabContent.ɵfac = function MatTabContent_Factory(t) {\n    return new (t || MatTabContent)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n  };\n\n  MatTabContent.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatTabContent,\n    selectors: [[\"\", \"matTabContent\", \"\"]],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_TAB_CONTENT,\n      useExisting: MatTabContent\n    }])]\n  });\n  return MatTabContent;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Injection token that can be used to reference instances of `MatTabLabel`. It serves as\n * alternative token to the actual `MatTabLabel` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\n\n\nconst MAT_TAB_LABEL = /*#__PURE__*/new InjectionToken('MatTabLabel');\n/**\n * Used to provide a tab label to a tab without causing a circular dependency.\n * @docs-private\n */\n\nconst MAT_TAB = /*#__PURE__*/new InjectionToken('MAT_TAB');\n/** Used to flag tab labels for use with the portal directive */\n\nlet MatTabLabel = /*#__PURE__*/(() => {\n  class MatTabLabel extends CdkPortal {\n    constructor(templateRef, viewContainerRef, _closestTab) {\n      super(templateRef, viewContainerRef);\n      this._closestTab = _closestTab;\n    }\n\n  }\n\n  MatTabLabel.ɵfac = function MatTabLabel_Factory(t) {\n    return new (t || MatTabLabel)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(MAT_TAB, 8));\n  };\n\n  MatTabLabel.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatTabLabel,\n    selectors: [[\"\", \"mat-tab-label\", \"\"], [\"\", \"matTabLabel\", \"\"]],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_TAB_LABEL,\n      useExisting: MatTabLabel\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n  return MatTabLabel;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to MatTab.\n\n/** @docs-private */\n\n\nconst _MatTabBase = /*#__PURE__*/mixinDisabled(class {});\n/**\n * Used to provide a tab group to a tab without causing a circular dependency.\n * @docs-private\n */\n\n\nconst MAT_TAB_GROUP = /*#__PURE__*/new InjectionToken('MAT_TAB_GROUP');\nlet MatTab = /*#__PURE__*/(() => {\n  class MatTab extends _MatTabBase {\n    constructor(_viewContainerRef, _closestTabGroup) {\n      super();\n      this._viewContainerRef = _viewContainerRef;\n      this._closestTabGroup = _closestTabGroup;\n      /** Plain text label for the tab, used when there is no template label. */\n\n      this.textLabel = '';\n      /** Portal that will be the hosted content of the tab */\n\n      this._contentPortal = null;\n      /** Emits whenever the internal state of the tab changes. */\n\n      this._stateChanges = new Subject();\n      /**\n       * The relatively indexed position where 0 represents the center, negative is left, and positive\n       * represents the right.\n       */\n\n      this.position = null;\n      /**\n       * The initial relatively index origin of the tab if it was created and selected after there\n       * was already a selected tab. Provides context of what position the tab should originate from.\n       */\n\n      this.origin = null;\n      /**\n       * Whether the tab is currently active.\n       */\n\n      this.isActive = false;\n    }\n    /** Content for the tab label given by `<ng-template mat-tab-label>`. */\n\n\n    get templateLabel() {\n      return this._templateLabel;\n    }\n\n    set templateLabel(value) {\n      this._setTemplateLabelInput(value);\n    }\n    /** @docs-private */\n\n\n    get content() {\n      return this._contentPortal;\n    }\n\n    ngOnChanges(changes) {\n      if (changes.hasOwnProperty('textLabel') || changes.hasOwnProperty('disabled')) {\n        this._stateChanges.next();\n      }\n    }\n\n    ngOnDestroy() {\n      this._stateChanges.complete();\n    }\n\n    ngOnInit() {\n      this._contentPortal = new TemplatePortal(this._explicitContent || this._implicitContent, this._viewContainerRef);\n    }\n    /**\n     * This has been extracted to a util because of TS 4 and VE.\n     * View Engine doesn't support property rename inheritance.\n     * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n     * @docs-private\n     */\n\n\n    _setTemplateLabelInput(value) {\n      // Only update the label if the query managed to find one. This works around an issue where a\n      // user may have manually set `templateLabel` during creation mode, which would then get\n      // clobbered by `undefined` when the query resolves. Also note that we check that the closest\n      // tab matches the current one so that we don't pick up labels from nested tabs.\n      if (value && value._closestTab === this) {\n        this._templateLabel = value;\n      }\n    }\n\n  }\n\n  MatTab.ɵfac = function MatTab_Factory(t) {\n    return new (t || MatTab)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(MAT_TAB_GROUP, 8));\n  };\n\n  MatTab.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatTab,\n    selectors: [[\"mat-tab\"]],\n    contentQueries: function MatTab_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MAT_TAB_LABEL, 5);\n        i0.ɵɵcontentQuery(dirIndex, MAT_TAB_CONTENT, 7, TemplateRef);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateLabel = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._explicitContent = _t.first);\n      }\n    },\n    viewQuery: function MatTab_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(TemplateRef, 7);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._implicitContent = _t.first);\n      }\n    },\n    inputs: {\n      disabled: \"disabled\",\n      textLabel: [\"label\", \"textLabel\"],\n      ariaLabel: [\"aria-label\", \"ariaLabel\"],\n      ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"],\n      labelClass: \"labelClass\",\n      bodyClass: \"bodyClass\"\n    },\n    exportAs: [\"matTab\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_TAB,\n      useExisting: MatTab\n    }]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function MatTab_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵtemplate(0, MatTab_ng_template_0_Template, 1, 0, \"ng-template\");\n      }\n    },\n    encapsulation: 2\n  });\n  return MatTab;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Animations used by the Material tabs.\n * @docs-private\n */\n\n\nconst matTabsAnimations = {\n  /** Animation translates a tab along the X axis. */\n  translateTab: /*#__PURE__*/trigger('translateTab', [\n  /*#__PURE__*/\n  // Transitions to `none` instead of 0, because some browsers might blur the content.\n  state('center, void, left-origin-center, right-origin-center', /*#__PURE__*/style({\n    transform: 'none'\n  })),\n  /*#__PURE__*/\n  // If the tab is either on the left or right, we additionally add a `min-height` of 1px\n  // in order to ensure that the element has a height before its state changes. This is\n  // necessary because Chrome does seem to skip the transition in RTL mode if the element does\n  // not have a static height and is not rendered. See related issue: #9465\n  state('left', /*#__PURE__*/style({\n    transform: 'translate3d(-100%, 0, 0)',\n    minHeight: '1px',\n    // Normally this is redundant since we detach the content from the DOM, but if the user\n    // opted into keeping the content in the DOM, we have to hide it so it isn't focusable.\n    visibility: 'hidden'\n  })), /*#__PURE__*/state('right', /*#__PURE__*/style({\n    transform: 'translate3d(100%, 0, 0)',\n    minHeight: '1px',\n    visibility: 'hidden'\n  })), /*#__PURE__*/transition('* => left, * => right, left => center, right => center', /*#__PURE__*/animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)')), /*#__PURE__*/transition('void => left-origin-center', [/*#__PURE__*/style({\n    transform: 'translate3d(-100%, 0, 0)',\n    visibility: 'hidden'\n  }), /*#__PURE__*/animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)')]), /*#__PURE__*/transition('void => right-origin-center', [/*#__PURE__*/style({\n    transform: 'translate3d(100%, 0, 0)',\n    visibility: 'hidden'\n  }), /*#__PURE__*/animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)')])])\n};\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * The portal host directive for the contents of the tab.\n * @docs-private\n */\n\nlet MatTabBodyPortal = /*#__PURE__*/(() => {\n  class MatTabBodyPortal extends CdkPortalOutlet {\n    constructor(componentFactoryResolver, viewContainerRef, _host, _document) {\n      super(componentFactoryResolver, viewContainerRef, _document);\n      this._host = _host;\n      /** Subscription to events for when the tab body begins centering. */\n\n      this._centeringSub = Subscription.EMPTY;\n      /** Subscription to events for when the tab body finishes leaving from center position. */\n\n      this._leavingSub = Subscription.EMPTY;\n    }\n    /** Set initial visibility or set up subscription for changing visibility. */\n\n\n    ngOnInit() {\n      super.ngOnInit();\n      this._centeringSub = this._host._beforeCentering.pipe(startWith(this._host._isCenterPosition(this._host._position))).subscribe(isCentering => {\n        if (isCentering && !this.hasAttached()) {\n          this.attach(this._host._content);\n        }\n      });\n      this._leavingSub = this._host._afterLeavingCenter.subscribe(() => {\n        if (!this._host.preserveContent) {\n          this.detach();\n        }\n      });\n    }\n    /** Clean up centering subscription. */\n\n\n    ngOnDestroy() {\n      super.ngOnDestroy();\n\n      this._centeringSub.unsubscribe();\n\n      this._leavingSub.unsubscribe();\n    }\n\n  }\n\n  MatTabBodyPortal.ɵfac = function MatTabBodyPortal_Factory(t) {\n    return new (t || MatTabBodyPortal)(i0.ɵɵdirectiveInject(i0.ComponentFactoryResolver), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(forwardRef(() => MatTabBody)), i0.ɵɵdirectiveInject(DOCUMENT));\n  };\n\n  MatTabBodyPortal.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatTabBodyPortal,\n    selectors: [[\"\", \"matTabBodyHost\", \"\"]],\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return MatTabBodyPortal;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Base class with all of the `MatTabBody` functionality.\n * @docs-private\n */\n\n\nlet _MatTabBodyBase = /*#__PURE__*/(() => {\n  class _MatTabBodyBase {\n    constructor(_elementRef, _dir, changeDetectorRef) {\n      this._elementRef = _elementRef;\n      this._dir = _dir;\n      /** Subscription to the directionality change observable. */\n\n      this._dirChangeSubscription = Subscription.EMPTY;\n      /** Emits when an animation on the tab is complete. */\n\n      this._translateTabComplete = new Subject();\n      /** Event emitted when the tab begins to animate towards the center as the active tab. */\n\n      this._onCentering = new EventEmitter();\n      /** Event emitted before the centering of the tab begins. */\n\n      this._beforeCentering = new EventEmitter();\n      /** Event emitted before the centering of the tab begins. */\n\n      this._afterLeavingCenter = new EventEmitter();\n      /** Event emitted when the tab completes its animation towards the center. */\n\n      this._onCentered = new EventEmitter(true); // Note that the default value will always be overwritten by `MatTabBody`, but we need one\n      // anyway to prevent the animations module from throwing an error if the body is used on its own.\n\n      /** Duration for the tab's animation. */\n\n      this.animationDuration = '500ms';\n      /** Whether the tab's content should be kept in the DOM while it's off-screen. */\n\n      this.preserveContent = false;\n\n      if (_dir) {\n        this._dirChangeSubscription = _dir.change.subscribe(dir => {\n          this._computePositionAnimationState(dir);\n\n          changeDetectorRef.markForCheck();\n        });\n      } // Ensure that we get unique animation events, because the `.done` callback can get\n      // invoked twice in some browsers. See https://github.com/angular/angular/issues/24084.\n\n\n      this._translateTabComplete.pipe(distinctUntilChanged((x, y) => {\n        return x.fromState === y.fromState && x.toState === y.toState;\n      })).subscribe(event => {\n        // If the transition to the center is complete, emit an event.\n        if (this._isCenterPosition(event.toState) && this._isCenterPosition(this._position)) {\n          this._onCentered.emit();\n        }\n\n        if (this._isCenterPosition(event.fromState) && !this._isCenterPosition(this._position)) {\n          this._afterLeavingCenter.emit();\n        }\n      });\n    }\n    /** The shifted index position of the tab body, where zero represents the active center tab. */\n\n\n    set position(position) {\n      this._positionIndex = position;\n\n      this._computePositionAnimationState();\n    }\n    /**\n     * After initialized, check if the content is centered and has an origin. If so, set the\n     * special position states that transition the tab from the left or right before centering.\n     */\n\n\n    ngOnInit() {\n      if (this._position == 'center' && this.origin != null) {\n        this._position = this._computePositionFromOrigin(this.origin);\n      }\n    }\n\n    ngOnDestroy() {\n      this._dirChangeSubscription.unsubscribe();\n\n      this._translateTabComplete.complete();\n    }\n\n    _onTranslateTabStarted(event) {\n      const isCentering = this._isCenterPosition(event.toState);\n\n      this._beforeCentering.emit(isCentering);\n\n      if (isCentering) {\n        this._onCentering.emit(this._elementRef.nativeElement.clientHeight);\n      }\n    }\n    /** The text direction of the containing app. */\n\n\n    _getLayoutDirection() {\n      return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Whether the provided position state is considered center, regardless of origin. */\n\n\n    _isCenterPosition(position) {\n      return position == 'center' || position == 'left-origin-center' || position == 'right-origin-center';\n    }\n    /** Computes the position state that will be used for the tab-body animation trigger. */\n\n\n    _computePositionAnimationState(dir = this._getLayoutDirection()) {\n      if (this._positionIndex < 0) {\n        this._position = dir == 'ltr' ? 'left' : 'right';\n      } else if (this._positionIndex > 0) {\n        this._position = dir == 'ltr' ? 'right' : 'left';\n      } else {\n        this._position = 'center';\n      }\n    }\n    /**\n     * Computes the position state based on the specified origin position. This is used if the\n     * tab is becoming visible immediately after creation.\n     */\n\n\n    _computePositionFromOrigin(origin) {\n      const dir = this._getLayoutDirection();\n\n      if (dir == 'ltr' && origin <= 0 || dir == 'rtl' && origin > 0) {\n        return 'left-origin-center';\n      }\n\n      return 'right-origin-center';\n    }\n\n  }\n\n  _MatTabBodyBase.ɵfac = function _MatTabBodyBase_Factory(t) {\n    return new (t || _MatTabBodyBase)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n\n  _MatTabBodyBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: _MatTabBodyBase,\n    inputs: {\n      _content: [\"content\", \"_content\"],\n      origin: \"origin\",\n      animationDuration: \"animationDuration\",\n      preserveContent: \"preserveContent\",\n      position: \"position\"\n    },\n    outputs: {\n      _onCentering: \"_onCentering\",\n      _beforeCentering: \"_beforeCentering\",\n      _afterLeavingCenter: \"_afterLeavingCenter\",\n      _onCentered: \"_onCentered\"\n    }\n  });\n  return _MatTabBodyBase;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Wrapper for the contents of a tab.\n * @docs-private\n */\n\n\nlet MatTabBody = /*#__PURE__*/(() => {\n  class MatTabBody extends _MatTabBodyBase {\n    constructor(elementRef, dir, changeDetectorRef) {\n      super(elementRef, dir, changeDetectorRef);\n    }\n\n  }\n\n  MatTabBody.ɵfac = function MatTabBody_Factory(t) {\n    return new (t || MatTabBody)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n\n  MatTabBody.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatTabBody,\n    selectors: [[\"mat-tab-body\"]],\n    viewQuery: function MatTabBody_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(CdkPortalOutlet, 5);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._portalHost = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-tab-body\"],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 3,\n    vars: 6,\n    consts: [[\"cdkScrollable\", \"\", 1, \"mat-tab-body-content\"], [\"content\", \"\"], [\"matTabBodyHost\", \"\"]],\n    template: function MatTabBody_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0, 1);\n        i0.ɵɵlistener(\"@translateTab.start\", function MatTabBody_Template_div_animation_translateTab_start_0_listener($event) {\n          return ctx._onTranslateTabStarted($event);\n        })(\"@translateTab.done\", function MatTabBody_Template_div_animation_translateTab_done_0_listener($event) {\n          return ctx._translateTabComplete.next($event);\n        });\n        i0.ɵɵtemplate(2, MatTabBody_ng_template_2_Template, 0, 0, \"ng-template\", 2);\n        i0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵproperty(\"@translateTab\", i0.ɵɵpureFunction2(3, _c2, ctx._position, i0.ɵɵpureFunction1(1, _c1, ctx.animationDuration)));\n      }\n    },\n    dependencies: [MatTabBodyPortal],\n    styles: [\".mat-tab-body-content{height:100%;overflow:auto}.mat-tab-group-dynamic-height .mat-tab-body-content{overflow:hidden}.mat-tab-body-content[style*=\\\"visibility: hidden\\\"]{display:none}\"],\n    encapsulation: 2,\n    data: {\n      animation: [matTabsAnimations.translateTab]\n    }\n  });\n  return MatTabBody;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Injection token that can be used to provide the default options the tabs module. */\n\n\nconst MAT_TABS_CONFIG = /*#__PURE__*/new InjectionToken('MAT_TABS_CONFIG');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to MatTabLabelWrapper.\n\n/** @docs-private */\n\nconst _MatTabLabelWrapperBase = /*#__PURE__*/mixinDisabled(class {});\n/**\n * Used in the `mat-tab-group` view to display tab labels.\n * @docs-private\n */\n\n\nlet MatTabLabelWrapper = /*#__PURE__*/(() => {\n  class MatTabLabelWrapper extends _MatTabLabelWrapperBase {\n    constructor(elementRef) {\n      super();\n      this.elementRef = elementRef;\n    }\n    /** Sets focus on the wrapper element */\n\n\n    focus() {\n      this.elementRef.nativeElement.focus();\n    }\n\n    getOffsetLeft() {\n      return this.elementRef.nativeElement.offsetLeft;\n    }\n\n    getOffsetWidth() {\n      return this.elementRef.nativeElement.offsetWidth;\n    }\n\n  }\n\n  MatTabLabelWrapper.ɵfac = function MatTabLabelWrapper_Factory(t) {\n    return new (t || MatTabLabelWrapper)(i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n\n  MatTabLabelWrapper.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatTabLabelWrapper,\n    selectors: [[\"\", \"matTabLabelWrapper\", \"\"]],\n    hostVars: 3,\n    hostBindings: function MatTabLabelWrapper_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-disabled\", !!ctx.disabled);\n        i0.ɵɵclassProp(\"mat-tab-disabled\", ctx.disabled);\n      }\n    },\n    inputs: {\n      disabled: \"disabled\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return MatTabLabelWrapper;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Config used to bind passive event listeners */\n\n\nconst passiveEventListenerOptions = /*#__PURE__*/normalizePassiveListenerOptions({\n  passive: true\n});\n/**\n * The distance in pixels that will be overshot when scrolling a tab label into view. This helps\n * provide a small affordance to the label next to it.\n */\n\nconst EXAGGERATED_OVERSCROLL = 60;\n/**\n * Amount of milliseconds to wait before starting to scroll the header automatically.\n * Set a little conservatively in order to handle fake events dispatched on touch devices.\n */\n\nconst HEADER_SCROLL_DELAY = 650;\n/**\n * Interval in milliseconds at which to scroll the header\n * while the user is holding their pointer.\n */\n\nconst HEADER_SCROLL_INTERVAL = 100;\n/**\n * Base class for a tab header that supported pagination.\n * @docs-private\n */\n\nlet MatPaginatedTabHeader = /*#__PURE__*/(() => {\n  class MatPaginatedTabHeader {\n    constructor(_elementRef, _changeDetectorRef, _viewportRuler, _dir, _ngZone, _platform, _animationMode) {\n      this._elementRef = _elementRef;\n      this._changeDetectorRef = _changeDetectorRef;\n      this._viewportRuler = _viewportRuler;\n      this._dir = _dir;\n      this._ngZone = _ngZone;\n      this._platform = _platform;\n      this._animationMode = _animationMode;\n      /** The distance in pixels that the tab labels should be translated to the left. */\n\n      this._scrollDistance = 0;\n      /** Whether the header should scroll to the selected index after the view has been checked. */\n\n      this._selectedIndexChanged = false;\n      /** Emits when the component is destroyed. */\n\n      this._destroyed = new Subject();\n      /** Whether the controls for pagination should be displayed */\n\n      this._showPaginationControls = false;\n      /** Whether the tab list can be scrolled more towards the end of the tab label list. */\n\n      this._disableScrollAfter = true;\n      /** Whether the tab list can be scrolled more towards the beginning of the tab label list. */\n\n      this._disableScrollBefore = true;\n      /** Stream that will stop the automated scrolling. */\n\n      this._stopScrolling = new Subject();\n      this._disablePagination = false;\n      this._selectedIndex = 0;\n      /** Event emitted when the option is selected. */\n\n      this.selectFocusedIndex = new EventEmitter();\n      /** Event emitted when a label is focused. */\n\n      this.indexFocused = new EventEmitter(); // Bind the `mouseleave` event on the outside since it doesn't change anything in the view.\n\n      _ngZone.runOutsideAngular(() => {\n        fromEvent(_elementRef.nativeElement, 'mouseleave').pipe(takeUntil(this._destroyed)).subscribe(() => {\n          this._stopInterval();\n        });\n      });\n    }\n    /**\n     * Whether pagination should be disabled. This can be used to avoid unnecessary\n     * layout recalculations if it's known that pagination won't be required.\n     */\n\n\n    get disablePagination() {\n      return this._disablePagination;\n    }\n\n    set disablePagination(value) {\n      this._disablePagination = coerceBooleanProperty(value);\n    }\n    /** The index of the active tab. */\n\n\n    get selectedIndex() {\n      return this._selectedIndex;\n    }\n\n    set selectedIndex(value) {\n      value = coerceNumberProperty(value);\n\n      if (this._selectedIndex != value) {\n        this._selectedIndexChanged = true;\n        this._selectedIndex = value;\n\n        if (this._keyManager) {\n          this._keyManager.updateActiveItem(value);\n        }\n      }\n    }\n\n    ngAfterViewInit() {\n      // We need to handle these events manually, because we want to bind passive event listeners.\n      fromEvent(this._previousPaginator.nativeElement, 'touchstart', passiveEventListenerOptions).pipe(takeUntil(this._destroyed)).subscribe(() => {\n        this._handlePaginatorPress('before');\n      });\n      fromEvent(this._nextPaginator.nativeElement, 'touchstart', passiveEventListenerOptions).pipe(takeUntil(this._destroyed)).subscribe(() => {\n        this._handlePaginatorPress('after');\n      });\n    }\n\n    ngAfterContentInit() {\n      const dirChange = this._dir ? this._dir.change : of('ltr');\n\n      const resize = this._viewportRuler.change(150);\n\n      const realign = () => {\n        this.updatePagination();\n\n        this._alignInkBarToSelectedTab();\n      };\n\n      this._keyManager = new FocusKeyManager(this._items).withHorizontalOrientation(this._getLayoutDirection()).withHomeAndEnd().withWrap();\n\n      this._keyManager.updateActiveItem(this._selectedIndex); // Defer the first call in order to allow for slower browsers to lay out the elements.\n      // This helps in cases where the user lands directly on a page with paginated tabs.\n      // Note that we use `onStable` instead of `requestAnimationFrame`, because the latter\n      // can hold up tests that are in a background tab.\n\n\n      this._ngZone.onStable.pipe(take(1)).subscribe(realign); // On dir change or window resize, realign the ink bar and update the orientation of\n      // the key manager if the direction has changed.\n\n\n      merge(dirChange, resize, this._items.changes, this._itemsResized()).pipe(takeUntil(this._destroyed)).subscribe(() => {\n        // We need to defer this to give the browser some time to recalculate\n        // the element dimensions. The call has to be wrapped in `NgZone.run`,\n        // because the viewport change handler runs outside of Angular.\n        this._ngZone.run(() => {\n          Promise.resolve().then(() => {\n            // Clamp the scroll distance, because it can change with the number of tabs.\n            this._scrollDistance = Math.max(0, Math.min(this._getMaxScrollDistance(), this._scrollDistance));\n            realign();\n          });\n        });\n\n        this._keyManager.withHorizontalOrientation(this._getLayoutDirection());\n      }); // If there is a change in the focus key manager we need to emit the `indexFocused`\n      // event in order to provide a public event that notifies about focus changes. Also we realign\n      // the tabs container by scrolling the new focused tab into the visible section.\n\n      this._keyManager.change.pipe(takeUntil(this._destroyed)).subscribe(newFocusIndex => {\n        this.indexFocused.emit(newFocusIndex);\n\n        this._setTabFocus(newFocusIndex);\n      });\n    }\n    /** Sends any changes that could affect the layout of the items. */\n\n\n    _itemsResized() {\n      if (typeof ResizeObserver !== 'function') {\n        return EMPTY;\n      }\n\n      return this._items.changes.pipe(startWith(this._items), switchMap(tabItems => new Observable(observer => this._ngZone.runOutsideAngular(() => {\n        const resizeObserver = new ResizeObserver(entries => observer.next(entries));\n        tabItems.forEach(item => resizeObserver.observe(item.elementRef.nativeElement));\n        return () => {\n          resizeObserver.disconnect();\n        };\n      }))), // Skip the first emit since the resize observer emits when an item\n      // is observed for new items when the tab is already inserted\n      skip(1), // Skip emissions where all the elements are invisible since we don't want\n      // the header to try and re-render with invalid measurements. See #25574.\n      filter(entries => entries.some(e => e.contentRect.width > 0 && e.contentRect.height > 0)));\n    }\n\n    ngAfterContentChecked() {\n      // If the number of tab labels have changed, check if scrolling should be enabled\n      if (this._tabLabelCount != this._items.length) {\n        this.updatePagination();\n        this._tabLabelCount = this._items.length;\n\n        this._changeDetectorRef.markForCheck();\n      } // If the selected index has changed, scroll to the label and check if the scrolling controls\n      // should be disabled.\n\n\n      if (this._selectedIndexChanged) {\n        this._scrollToLabel(this._selectedIndex);\n\n        this._checkScrollingControls();\n\n        this._alignInkBarToSelectedTab();\n\n        this._selectedIndexChanged = false;\n\n        this._changeDetectorRef.markForCheck();\n      } // If the scroll distance has been changed (tab selected, focused, scroll controls activated),\n      // then translate the header to reflect this.\n\n\n      if (this._scrollDistanceChanged) {\n        this._updateTabScrollPosition();\n\n        this._scrollDistanceChanged = false;\n\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n\n    ngOnDestroy() {\n      this._destroyed.next();\n\n      this._destroyed.complete();\n\n      this._stopScrolling.complete();\n    }\n    /** Handles keyboard events on the header. */\n\n\n    _handleKeydown(event) {\n      // We don't handle any key bindings with a modifier key.\n      if (hasModifierKey(event)) {\n        return;\n      }\n\n      switch (event.keyCode) {\n        case ENTER:\n        case SPACE:\n          if (this.focusIndex !== this.selectedIndex) {\n            this.selectFocusedIndex.emit(this.focusIndex);\n\n            this._itemSelected(event);\n          }\n\n          break;\n\n        default:\n          this._keyManager.onKeydown(event);\n\n      }\n    }\n    /**\n     * Callback for when the MutationObserver detects that the content has changed.\n     */\n\n\n    _onContentChanges() {\n      const textContent = this._elementRef.nativeElement.textContent; // We need to diff the text content of the header, because the MutationObserver callback\n      // will fire even if the text content didn't change which is inefficient and is prone\n      // to infinite loops if a poorly constructed expression is passed in (see #14249).\n\n      if (textContent !== this._currentTextContent) {\n        this._currentTextContent = textContent || ''; // The content observer runs outside the `NgZone` by default, which\n        // means that we need to bring the callback back in ourselves.\n\n        this._ngZone.run(() => {\n          this.updatePagination();\n\n          this._alignInkBarToSelectedTab();\n\n          this._changeDetectorRef.markForCheck();\n        });\n      }\n    }\n    /**\n     * Updates the view whether pagination should be enabled or not.\n     *\n     * WARNING: Calling this method can be very costly in terms of performance. It should be called\n     * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n     * page.\n     */\n\n\n    updatePagination() {\n      this._checkPaginationEnabled();\n\n      this._checkScrollingControls();\n\n      this._updateTabScrollPosition();\n    }\n    /** Tracks which element has focus; used for keyboard navigation */\n\n\n    get focusIndex() {\n      return this._keyManager ? this._keyManager.activeItemIndex : 0;\n    }\n    /** When the focus index is set, we must manually send focus to the correct label */\n\n\n    set focusIndex(value) {\n      if (!this._isValidIndex(value) || this.focusIndex === value || !this._keyManager) {\n        return;\n      }\n\n      this._keyManager.setActiveItem(value);\n    }\n    /**\n     * Determines if an index is valid.  If the tabs are not ready yet, we assume that the user is\n     * providing a valid index and return true.\n     */\n\n\n    _isValidIndex(index) {\n      if (!this._items) {\n        return true;\n      }\n\n      const tab = this._items ? this._items.toArray()[index] : null;\n      return !!tab && !tab.disabled;\n    }\n    /**\n     * Sets focus on the HTML element for the label wrapper and scrolls it into the view if\n     * scrolling is enabled.\n     */\n\n\n    _setTabFocus(tabIndex) {\n      if (this._showPaginationControls) {\n        this._scrollToLabel(tabIndex);\n      }\n\n      if (this._items && this._items.length) {\n        this._items.toArray()[tabIndex].focus(); // Do not let the browser manage scrolling to focus the element, this will be handled\n        // by using translation. In LTR, the scroll left should be 0. In RTL, the scroll width\n        // should be the full width minus the offset width.\n\n\n        const containerEl = this._tabListContainer.nativeElement;\n\n        const dir = this._getLayoutDirection();\n\n        if (dir == 'ltr') {\n          containerEl.scrollLeft = 0;\n        } else {\n          containerEl.scrollLeft = containerEl.scrollWidth - containerEl.offsetWidth;\n        }\n      }\n    }\n    /** The layout direction of the containing app. */\n\n\n    _getLayoutDirection() {\n      return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Performs the CSS transformation on the tab list that will cause the list to scroll. */\n\n\n    _updateTabScrollPosition() {\n      if (this.disablePagination) {\n        return;\n      }\n\n      const scrollDistance = this.scrollDistance;\n      const translateX = this._getLayoutDirection() === 'ltr' ? -scrollDistance : scrollDistance; // Don't use `translate3d` here because we don't want to create a new layer. A new layer\n      // seems to cause flickering and overflow in Internet Explorer. For example, the ink bar\n      // and ripples will exceed the boundaries of the visible tab bar.\n      // See: https://github.com/angular/components/issues/10276\n      // We round the `transform` here, because transforms with sub-pixel precision cause some\n      // browsers to blur the content of the element.\n\n      this._tabList.nativeElement.style.transform = `translateX(${Math.round(translateX)}px)`; // Setting the `transform` on IE will change the scroll offset of the parent, causing the\n      // position to be thrown off in some cases. We have to reset it ourselves to ensure that\n      // it doesn't get thrown off. Note that we scope it only to IE and Edge, because messing\n      // with the scroll position throws off Chrome 71+ in RTL mode (see #14689).\n\n      if (this._platform.TRIDENT || this._platform.EDGE) {\n        this._tabListContainer.nativeElement.scrollLeft = 0;\n      }\n    }\n    /** Sets the distance in pixels that the tab header should be transformed in the X-axis. */\n\n\n    get scrollDistance() {\n      return this._scrollDistance;\n    }\n\n    set scrollDistance(value) {\n      this._scrollTo(value);\n    }\n    /**\n     * Moves the tab list in the 'before' or 'after' direction (towards the beginning of the list or\n     * the end of the list, respectively). The distance to scroll is computed to be a third of the\n     * length of the tab list view window.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n\n\n    _scrollHeader(direction) {\n      const viewLength = this._tabListContainer.nativeElement.offsetWidth; // Move the scroll distance one-third the length of the tab list's viewport.\n\n      const scrollAmount = (direction == 'before' ? -1 : 1) * viewLength / 3;\n      return this._scrollTo(this._scrollDistance + scrollAmount);\n    }\n    /** Handles click events on the pagination arrows. */\n\n\n    _handlePaginatorClick(direction) {\n      this._stopInterval();\n\n      this._scrollHeader(direction);\n    }\n    /**\n     * Moves the tab list such that the desired tab label (marked by index) is moved into view.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n\n\n    _scrollToLabel(labelIndex) {\n      if (this.disablePagination) {\n        return;\n      }\n\n      const selectedLabel = this._items ? this._items.toArray()[labelIndex] : null;\n\n      if (!selectedLabel) {\n        return;\n      } // The view length is the visible width of the tab labels.\n\n\n      const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n      const {\n        offsetLeft,\n        offsetWidth\n      } = selectedLabel.elementRef.nativeElement;\n      let labelBeforePos, labelAfterPos;\n\n      if (this._getLayoutDirection() == 'ltr') {\n        labelBeforePos = offsetLeft;\n        labelAfterPos = labelBeforePos + offsetWidth;\n      } else {\n        labelAfterPos = this._tabListInner.nativeElement.offsetWidth - offsetLeft;\n        labelBeforePos = labelAfterPos - offsetWidth;\n      }\n\n      const beforeVisiblePos = this.scrollDistance;\n      const afterVisiblePos = this.scrollDistance + viewLength;\n\n      if (labelBeforePos < beforeVisiblePos) {\n        // Scroll header to move label to the before direction\n        this.scrollDistance -= beforeVisiblePos - labelBeforePos + EXAGGERATED_OVERSCROLL;\n      } else if (labelAfterPos > afterVisiblePos) {\n        // Scroll header to move label to the after direction\n        this.scrollDistance += labelAfterPos - afterVisiblePos + EXAGGERATED_OVERSCROLL;\n      }\n    }\n    /**\n     * Evaluate whether the pagination controls should be displayed. If the scroll width of the\n     * tab list is wider than the size of the header container, then the pagination controls should\n     * be shown.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n\n\n    _checkPaginationEnabled() {\n      if (this.disablePagination) {\n        this._showPaginationControls = false;\n      } else {\n        const isEnabled = this._tabListInner.nativeElement.scrollWidth > this._elementRef.nativeElement.offsetWidth;\n\n        if (!isEnabled) {\n          this.scrollDistance = 0;\n        }\n\n        if (isEnabled !== this._showPaginationControls) {\n          this._changeDetectorRef.markForCheck();\n        }\n\n        this._showPaginationControls = isEnabled;\n      }\n    }\n    /**\n     * Evaluate whether the before and after controls should be enabled or disabled.\n     * If the header is at the beginning of the list (scroll distance is equal to 0) then disable the\n     * before button. If the header is at the end of the list (scroll distance is equal to the\n     * maximum distance we can scroll), then disable the after button.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n\n\n    _checkScrollingControls() {\n      if (this.disablePagination) {\n        this._disableScrollAfter = this._disableScrollBefore = true;\n      } else {\n        // Check if the pagination arrows should be activated.\n        this._disableScrollBefore = this.scrollDistance == 0;\n        this._disableScrollAfter = this.scrollDistance == this._getMaxScrollDistance();\n\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n    /**\n     * Determines what is the maximum length in pixels that can be set for the scroll distance. This\n     * is equal to the difference in width between the tab list container and tab header container.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n\n\n    _getMaxScrollDistance() {\n      const lengthOfTabList = this._tabListInner.nativeElement.scrollWidth;\n      const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n      return lengthOfTabList - viewLength || 0;\n    }\n    /** Tells the ink-bar to align itself to the current label wrapper */\n\n\n    _alignInkBarToSelectedTab() {\n      const selectedItem = this._items && this._items.length ? this._items.toArray()[this.selectedIndex] : null;\n      const selectedLabelWrapper = selectedItem ? selectedItem.elementRef.nativeElement : null;\n\n      if (selectedLabelWrapper) {\n        this._inkBar.alignToElement(selectedLabelWrapper);\n      } else {\n        this._inkBar.hide();\n      }\n    }\n    /** Stops the currently-running paginator interval.  */\n\n\n    _stopInterval() {\n      this._stopScrolling.next();\n    }\n    /**\n     * Handles the user pressing down on one of the paginators.\n     * Starts scrolling the header after a certain amount of time.\n     * @param direction In which direction the paginator should be scrolled.\n     */\n\n\n    _handlePaginatorPress(direction, mouseEvent) {\n      // Don't start auto scrolling for right mouse button clicks. Note that we shouldn't have to\n      // null check the `button`, but we do it so we don't break tests that use fake events.\n      if (mouseEvent && mouseEvent.button != null && mouseEvent.button !== 0) {\n        return;\n      } // Avoid overlapping timers.\n\n\n      this._stopInterval(); // Start a timer after the delay and keep firing based on the interval.\n\n\n      timer(HEADER_SCROLL_DELAY, HEADER_SCROLL_INTERVAL) // Keep the timer going until something tells it to stop or the component is destroyed.\n      .pipe(takeUntil(merge(this._stopScrolling, this._destroyed))).subscribe(() => {\n        const {\n          maxScrollDistance,\n          distance\n        } = this._scrollHeader(direction); // Stop the timer if we've reached the start or the end.\n\n\n        if (distance === 0 || distance >= maxScrollDistance) {\n          this._stopInterval();\n        }\n      });\n    }\n    /**\n     * Scrolls the header to a given position.\n     * @param position Position to which to scroll.\n     * @returns Information on the current scroll distance and the maximum.\n     */\n\n\n    _scrollTo(position) {\n      if (this.disablePagination) {\n        return {\n          maxScrollDistance: 0,\n          distance: 0\n        };\n      }\n\n      const maxScrollDistance = this._getMaxScrollDistance();\n\n      this._scrollDistance = Math.max(0, Math.min(maxScrollDistance, position)); // Mark that the scroll distance has changed so that after the view is checked, the CSS\n      // transformation can move the header.\n\n      this._scrollDistanceChanged = true;\n\n      this._checkScrollingControls();\n\n      return {\n        maxScrollDistance,\n        distance: this._scrollDistance\n      };\n    }\n\n  }\n\n  MatPaginatedTabHeader.ɵfac = function MatPaginatedTabHeader_Factory(t) {\n    return new (t || MatPaginatedTabHeader)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.ViewportRuler), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n  };\n\n  MatPaginatedTabHeader.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatPaginatedTabHeader,\n    inputs: {\n      disablePagination: \"disablePagination\"\n    }\n  });\n  return MatPaginatedTabHeader;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Base class with all of the `MatTabHeader` functionality.\n * @docs-private\n */\n\n\nlet _MatTabHeaderBase = /*#__PURE__*/(() => {\n  class _MatTabHeaderBase extends MatPaginatedTabHeader {\n    constructor(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode) {\n      super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n      this._disableRipple = false;\n    }\n    /** Whether the ripple effect is disabled or not. */\n\n\n    get disableRipple() {\n      return this._disableRipple;\n    }\n\n    set disableRipple(value) {\n      this._disableRipple = coerceBooleanProperty(value);\n    }\n\n    _itemSelected(event) {\n      event.preventDefault();\n    }\n\n  }\n\n  _MatTabHeaderBase.ɵfac = function _MatTabHeaderBase_Factory(t) {\n    return new (t || _MatTabHeaderBase)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.ViewportRuler), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n  };\n\n  _MatTabHeaderBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: _MatTabHeaderBase,\n    inputs: {\n      disableRipple: \"disableRipple\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return _MatTabHeaderBase;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * The header of the tab group which displays a list of all the tabs in the tab group. Includes\n * an ink bar that follows the currently selected tab. When the tabs list's width exceeds the\n * width of the header container, then arrows will be displayed to allow the user to scroll\n * left and right across the header.\n * @docs-private\n */\n\n\nlet MatTabHeader = /*#__PURE__*/(() => {\n  class MatTabHeader extends _MatTabHeaderBase {\n    constructor(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode) {\n      super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n    }\n\n  }\n\n  MatTabHeader.ɵfac = function MatTabHeader_Factory(t) {\n    return new (t || MatTabHeader)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.ViewportRuler), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n  };\n\n  MatTabHeader.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatTabHeader,\n    selectors: [[\"mat-tab-header\"]],\n    contentQueries: function MatTabHeader_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatTabLabelWrapper, 4);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._items = _t);\n      }\n    },\n    viewQuery: function MatTabHeader_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(MatInkBar, 7);\n        i0.ɵɵviewQuery(_c3, 7);\n        i0.ɵɵviewQuery(_c4, 7);\n        i0.ɵɵviewQuery(_c5, 7);\n        i0.ɵɵviewQuery(_c6, 5);\n        i0.ɵɵviewQuery(_c7, 5);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._inkBar = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListContainer = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabList = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListInner = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nextPaginator = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._previousPaginator = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-tab-header\"],\n    hostVars: 4,\n    hostBindings: function MatTabHeader_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-tab-header-pagination-controls-enabled\", ctx._showPaginationControls)(\"mat-tab-header-rtl\", ctx._getLayoutDirection() == \"rtl\");\n      }\n    },\n    inputs: {\n      selectedIndex: \"selectedIndex\"\n    },\n    outputs: {\n      selectFocusedIndex: \"selectFocusedIndex\",\n      indexFocused: \"indexFocused\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c0,\n    decls: 14,\n    vars: 10,\n    consts: [[\"aria-hidden\", \"true\", \"type\", \"button\", \"mat-ripple\", \"\", \"tabindex\", \"-1\", 1, \"mat-tab-header-pagination\", \"mat-tab-header-pagination-before\", \"mat-elevation-z4\", 3, \"matRippleDisabled\", \"disabled\", \"click\", \"mousedown\", \"touchend\"], [\"previousPaginator\", \"\"], [1, \"mat-tab-header-pagination-chevron\"], [1, \"mat-tab-label-container\", 3, \"keydown\"], [\"tabListContainer\", \"\"], [\"role\", \"tablist\", 1, \"mat-tab-list\", 3, \"cdkObserveContent\"], [\"tabList\", \"\"], [1, \"mat-tab-labels\"], [\"tabListInner\", \"\"], [\"aria-hidden\", \"true\", \"type\", \"button\", \"mat-ripple\", \"\", \"tabindex\", \"-1\", 1, \"mat-tab-header-pagination\", \"mat-tab-header-pagination-after\", \"mat-elevation-z4\", 3, \"matRippleDisabled\", \"disabled\", \"mousedown\", \"click\", \"touchend\"], [\"nextPaginator\", \"\"]],\n    template: function MatTabHeader_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"button\", 0, 1);\n        i0.ɵɵlistener(\"click\", function MatTabHeader_Template_button_click_0_listener() {\n          return ctx._handlePaginatorClick(\"before\");\n        })(\"mousedown\", function MatTabHeader_Template_button_mousedown_0_listener($event) {\n          return ctx._handlePaginatorPress(\"before\", $event);\n        })(\"touchend\", function MatTabHeader_Template_button_touchend_0_listener() {\n          return ctx._stopInterval();\n        });\n        i0.ɵɵelement(2, \"div\", 2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"div\", 3, 4);\n        i0.ɵɵlistener(\"keydown\", function MatTabHeader_Template_div_keydown_3_listener($event) {\n          return ctx._handleKeydown($event);\n        });\n        i0.ɵɵelementStart(5, \"div\", 5, 6);\n        i0.ɵɵlistener(\"cdkObserveContent\", function MatTabHeader_Template_div_cdkObserveContent_5_listener() {\n          return ctx._onContentChanges();\n        });\n        i0.ɵɵelementStart(7, \"div\", 7, 8);\n        i0.ɵɵprojection(9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(10, \"mat-ink-bar\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"button\", 9, 10);\n        i0.ɵɵlistener(\"mousedown\", function MatTabHeader_Template_button_mousedown_11_listener($event) {\n          return ctx._handlePaginatorPress(\"after\", $event);\n        })(\"click\", function MatTabHeader_Template_button_click_11_listener() {\n          return ctx._handlePaginatorClick(\"after\");\n        })(\"touchend\", function MatTabHeader_Template_button_touchend_11_listener() {\n          return ctx._stopInterval();\n        });\n        i0.ɵɵelement(13, \"div\", 2);\n        i0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-tab-header-pagination-disabled\", ctx._disableScrollBefore);\n        i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollBefore || ctx.disableRipple)(\"disabled\", ctx._disableScrollBefore || null);\n        i0.ɵɵadvance(5);\n        i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n        i0.ɵɵadvance(6);\n        i0.ɵɵclassProp(\"mat-tab-header-pagination-disabled\", ctx._disableScrollAfter);\n        i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollAfter || ctx.disableRipple)(\"disabled\", ctx._disableScrollAfter || null);\n      }\n    },\n    dependencies: [i4.MatRipple, i5.CdkObserveContent, MatInkBar],\n    styles: [\".mat-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mat-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-tab-header-pagination::-moz-focus-inner{border:0}.mat-tab-header-pagination-controls-enabled .mat-tab-header-pagination{display:flex}.mat-tab-header-pagination-before,.mat-tab-header-rtl .mat-tab-header-pagination-after{padding-left:4px}.mat-tab-header-pagination-before .mat-tab-header-pagination-chevron,.mat-tab-header-rtl .mat-tab-header-pagination-after .mat-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-tab-header-rtl .mat-tab-header-pagination-before,.mat-tab-header-pagination-after{padding-right:4px}.mat-tab-header-rtl .mat-tab-header-pagination-before .mat-tab-header-pagination-chevron,.mat-tab-header-pagination-after .mat-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px}.mat-tab-header-pagination-disabled{box-shadow:none;cursor:default}.mat-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-ink-bar{position:absolute;bottom:0;height:2px;transition:500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-ink-bar._mat-animation-noopable{transition:none !important;animation:none !important}.mat-tab-group-inverted-header .mat-ink-bar{bottom:auto;top:0}.cdk-high-contrast-active .mat-ink-bar{outline:solid 2px;height:0}.mat-tab-labels{display:flex}[mat-align-tabs=center]>.mat-tab-header .mat-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-tab-header .mat-tab-labels{justify-content:flex-end}.mat-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1}.mat-tab-list._mat-animation-noopable{transition:none !important;animation:none !important}.mat-tab-label{height:48px;padding:0 24px;cursor:pointer;box-sizing:border-box;opacity:.6;min-width:160px;text-align:center;display:inline-flex;justify-content:center;align-items:center;white-space:nowrap;position:relative}.mat-tab-label:focus{outline:none}.mat-tab-label:focus:not(.mat-tab-disabled){opacity:1}.mat-tab-label.mat-tab-disabled{cursor:default}.cdk-high-contrast-active .mat-tab-label.mat-tab-disabled{opacity:.5}.mat-tab-label .mat-tab-label-content{display:inline-flex;justify-content:center;align-items:center;white-space:nowrap}.cdk-high-contrast-active .mat-tab-label{opacity:1}.mat-tab-label::before{margin:5px}@media(max-width: 599px){.mat-tab-label{min-width:72px}}\"],\n    encapsulation: 2\n  });\n  return MatTabHeader;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Used to generate unique ID's for each tab component */\n\n\nlet nextId = 0;\n/** A simple change event emitted on focus or selection changes. */\n\nclass MatTabChangeEvent {} // Boilerplate for applying mixins to MatTabGroup.\n\n/** @docs-private */\n\n\nconst _MatTabGroupMixinBase = /*#__PURE__*/mixinColor( /*#__PURE__*/mixinDisableRipple(class {\n  constructor(_elementRef) {\n    this._elementRef = _elementRef;\n  }\n\n}), 'primary');\n/**\n * Base class with all of the `MatTabGroupBase` functionality.\n * @docs-private\n */\n\n\nlet _MatTabGroupBase = /*#__PURE__*/(() => {\n  class _MatTabGroupBase extends _MatTabGroupMixinBase {\n    constructor(elementRef, _changeDetectorRef, defaultConfig, _animationMode) {\n      super(elementRef);\n      this._changeDetectorRef = _changeDetectorRef;\n      this._animationMode = _animationMode;\n      /** All of the tabs that belong to the group. */\n\n      this._tabs = new QueryList();\n      /** The tab index that should be selected after the content has been checked. */\n\n      this._indexToSelect = 0;\n      /** Index of the tab that was focused last. */\n\n      this._lastFocusedTabIndex = null;\n      /** Snapshot of the height of the tab body wrapper before another tab is activated. */\n\n      this._tabBodyWrapperHeight = 0;\n      /** Subscription to tabs being added/removed. */\n\n      this._tabsSubscription = Subscription.EMPTY;\n      /** Subscription to changes in the tab labels. */\n\n      this._tabLabelSubscription = Subscription.EMPTY;\n      this._dynamicHeight = false;\n      this._selectedIndex = null;\n      /** Position of the tab header. */\n\n      this.headerPosition = 'above';\n      this._disablePagination = false;\n      this._preserveContent = false;\n      /** Output to enable support for two-way binding on `[(selectedIndex)]` */\n\n      this.selectedIndexChange = new EventEmitter();\n      /** Event emitted when focus has changed within a tab group. */\n\n      this.focusChange = new EventEmitter();\n      /** Event emitted when the body animation has completed */\n\n      this.animationDone = new EventEmitter();\n      /** Event emitted when the tab selection has changed. */\n\n      this.selectedTabChange = new EventEmitter(true);\n      this._groupId = nextId++;\n      this.animationDuration = defaultConfig && defaultConfig.animationDuration ? defaultConfig.animationDuration : '500ms';\n      this.disablePagination = defaultConfig && defaultConfig.disablePagination != null ? defaultConfig.disablePagination : false;\n      this.dynamicHeight = defaultConfig && defaultConfig.dynamicHeight != null ? defaultConfig.dynamicHeight : false;\n      this.contentTabIndex = defaultConfig?.contentTabIndex ?? null;\n      this.preserveContent = !!defaultConfig?.preserveContent;\n    }\n    /** Whether the tab group should grow to the size of the active tab. */\n\n\n    get dynamicHeight() {\n      return this._dynamicHeight;\n    }\n\n    set dynamicHeight(value) {\n      this._dynamicHeight = coerceBooleanProperty(value);\n    }\n    /** The index of the active tab. */\n\n\n    get selectedIndex() {\n      return this._selectedIndex;\n    }\n\n    set selectedIndex(value) {\n      this._indexToSelect = coerceNumberProperty(value, null);\n    }\n    /** Duration for the tab animation. Will be normalized to milliseconds if no units are set. */\n\n\n    get animationDuration() {\n      return this._animationDuration;\n    }\n\n    set animationDuration(value) {\n      this._animationDuration = /^\\d+$/.test(value + '') ? value + 'ms' : value;\n    }\n    /**\n     * `tabindex` to be set on the inner element that wraps the tab content. Can be used for improved\n     * accessibility when the tab does not have focusable elements or if it has scrollable content.\n     * The `tabindex` will be removed automatically for inactive tabs.\n     * Read more at https://www.w3.org/TR/wai-aria-practices/examples/tabs/tabs-2/tabs.html\n     */\n\n\n    get contentTabIndex() {\n      return this._contentTabIndex;\n    }\n\n    set contentTabIndex(value) {\n      this._contentTabIndex = coerceNumberProperty(value, null);\n    }\n    /**\n     * Whether pagination should be disabled. This can be used to avoid unnecessary\n     * layout recalculations if it's known that pagination won't be required.\n     */\n\n\n    get disablePagination() {\n      return this._disablePagination;\n    }\n\n    set disablePagination(value) {\n      this._disablePagination = coerceBooleanProperty(value);\n    }\n    /**\n     * By default tabs remove their content from the DOM while it's off-screen.\n     * Setting this to `true` will keep it in the DOM which will prevent elements\n     * like iframes and videos from reloading next time it comes back into the view.\n     */\n\n\n    get preserveContent() {\n      return this._preserveContent;\n    }\n\n    set preserveContent(value) {\n      this._preserveContent = coerceBooleanProperty(value);\n    }\n    /** Background color of the tab group. */\n\n\n    get backgroundColor() {\n      return this._backgroundColor;\n    }\n\n    set backgroundColor(value) {\n      const nativeElement = this._elementRef.nativeElement;\n      nativeElement.classList.remove(`mat-background-${this.backgroundColor}`);\n\n      if (value) {\n        nativeElement.classList.add(`mat-background-${value}`);\n      }\n\n      this._backgroundColor = value;\n    }\n    /**\n     * After the content is checked, this component knows what tabs have been defined\n     * and what the selected index should be. This is where we can know exactly what position\n     * each tab should be in according to the new selected index, and additionally we know how\n     * a new selected tab should transition in (from the left or right).\n     */\n\n\n    ngAfterContentChecked() {\n      // Don't clamp the `indexToSelect` immediately in the setter because it can happen that\n      // the amount of tabs changes before the actual change detection runs.\n      const indexToSelect = this._indexToSelect = this._clampTabIndex(this._indexToSelect); // If there is a change in selected index, emit a change event. Should not trigger if\n      // the selected index has not yet been initialized.\n\n\n      if (this._selectedIndex != indexToSelect) {\n        const isFirstRun = this._selectedIndex == null;\n\n        if (!isFirstRun) {\n          this.selectedTabChange.emit(this._createChangeEvent(indexToSelect)); // Preserve the height so page doesn't scroll up during tab change.\n          // Fixes https://stackblitz.com/edit/mat-tabs-scroll-page-top-on-tab-change\n\n          const wrapper = this._tabBodyWrapper.nativeElement;\n          wrapper.style.minHeight = wrapper.clientHeight + 'px';\n        } // Changing these values after change detection has run\n        // since the checked content may contain references to them.\n\n\n        Promise.resolve().then(() => {\n          this._tabs.forEach((tab, index) => tab.isActive = index === indexToSelect);\n\n          if (!isFirstRun) {\n            this.selectedIndexChange.emit(indexToSelect); // Clear the min-height, this was needed during tab change to avoid\n            // unnecessary scrolling.\n\n            this._tabBodyWrapper.nativeElement.style.minHeight = '';\n          }\n        });\n      } // Setup the position for each tab and optionally setup an origin on the next selected tab.\n\n\n      this._tabs.forEach((tab, index) => {\n        tab.position = index - indexToSelect; // If there is already a selected tab, then set up an origin for the next selected tab\n        // if it doesn't have one already.\n\n        if (this._selectedIndex != null && tab.position == 0 && !tab.origin) {\n          tab.origin = indexToSelect - this._selectedIndex;\n        }\n      });\n\n      if (this._selectedIndex !== indexToSelect) {\n        this._selectedIndex = indexToSelect;\n        this._lastFocusedTabIndex = null;\n\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n\n    ngAfterContentInit() {\n      this._subscribeToAllTabChanges();\n\n      this._subscribeToTabLabels(); // Subscribe to changes in the amount of tabs, in order to be\n      // able to re-render the content as new tabs are added or removed.\n\n\n      this._tabsSubscription = this._tabs.changes.subscribe(() => {\n        const indexToSelect = this._clampTabIndex(this._indexToSelect); // Maintain the previously-selected tab if a new tab is added or removed and there is no\n        // explicit change that selects a different tab.\n\n\n        if (indexToSelect === this._selectedIndex) {\n          const tabs = this._tabs.toArray();\n\n          let selectedTab;\n\n          for (let i = 0; i < tabs.length; i++) {\n            if (tabs[i].isActive) {\n              // Assign both to the `_indexToSelect` and `_selectedIndex` so we don't fire a changed\n              // event, otherwise the consumer may end up in an infinite loop in some edge cases like\n              // adding a tab within the `selectedIndexChange` event.\n              this._indexToSelect = this._selectedIndex = i;\n              this._lastFocusedTabIndex = null;\n              selectedTab = tabs[i];\n              break;\n            }\n          } // If we haven't found an active tab and a tab exists at the selected index, it means\n          // that the active tab was swapped out. Since this won't be picked up by the rendering\n          // loop in `ngAfterContentChecked`, we need to sync it up manually.\n\n\n          if (!selectedTab && tabs[indexToSelect]) {\n            Promise.resolve().then(() => {\n              tabs[indexToSelect].isActive = true;\n              this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n            });\n          }\n        }\n\n        this._changeDetectorRef.markForCheck();\n      });\n    }\n    /** Listens to changes in all of the tabs. */\n\n\n    _subscribeToAllTabChanges() {\n      // Since we use a query with `descendants: true` to pick up the tabs, we may end up catching\n      // some that are inside of nested tab groups. We filter them out manually by checking that\n      // the closest group to the tab is the current one.\n      this._allTabs.changes.pipe(startWith(this._allTabs)).subscribe(tabs => {\n        this._tabs.reset(tabs.filter(tab => {\n          return tab._closestTabGroup === this || !tab._closestTabGroup;\n        }));\n\n        this._tabs.notifyOnChanges();\n      });\n    }\n\n    ngOnDestroy() {\n      this._tabs.destroy();\n\n      this._tabsSubscription.unsubscribe();\n\n      this._tabLabelSubscription.unsubscribe();\n    }\n    /** Re-aligns the ink bar to the selected tab element. */\n\n\n    realignInkBar() {\n      if (this._tabHeader) {\n        this._tabHeader._alignInkBarToSelectedTab();\n      }\n    }\n    /**\n     * Recalculates the tab group's pagination dimensions.\n     *\n     * WARNING: Calling this method can be very costly in terms of performance. It should be called\n     * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n     * page.\n     */\n\n\n    updatePagination() {\n      if (this._tabHeader) {\n        this._tabHeader.updatePagination();\n      }\n    }\n    /**\n     * Sets focus to a particular tab.\n     * @param index Index of the tab to be focused.\n     */\n\n\n    focusTab(index) {\n      const header = this._tabHeader;\n\n      if (header) {\n        header.focusIndex = index;\n      }\n    }\n\n    _focusChanged(index) {\n      this._lastFocusedTabIndex = index;\n      this.focusChange.emit(this._createChangeEvent(index));\n    }\n\n    _createChangeEvent(index) {\n      const event = new MatTabChangeEvent();\n      event.index = index;\n\n      if (this._tabs && this._tabs.length) {\n        event.tab = this._tabs.toArray()[index];\n      }\n\n      return event;\n    }\n    /**\n     * Subscribes to changes in the tab labels. This is needed, because the @Input for the label is\n     * on the MatTab component, whereas the data binding is inside the MatTabGroup. In order for the\n     * binding to be updated, we need to subscribe to changes in it and trigger change detection\n     * manually.\n     */\n\n\n    _subscribeToTabLabels() {\n      if (this._tabLabelSubscription) {\n        this._tabLabelSubscription.unsubscribe();\n      }\n\n      this._tabLabelSubscription = merge(...this._tabs.map(tab => tab._stateChanges)).subscribe(() => this._changeDetectorRef.markForCheck());\n    }\n    /** Clamps the given index to the bounds of 0 and the tabs length. */\n\n\n    _clampTabIndex(index) {\n      // Note the `|| 0`, which ensures that values like NaN can't get through\n      // and which would otherwise throw the component into an infinite loop\n      // (since Math.max(NaN, 0) === NaN).\n      return Math.min(this._tabs.length - 1, Math.max(index || 0, 0));\n    }\n    /** Returns a unique id for each tab label element */\n\n\n    _getTabLabelId(i) {\n      return `mat-tab-label-${this._groupId}-${i}`;\n    }\n    /** Returns a unique id for each tab content element */\n\n\n    _getTabContentId(i) {\n      return `mat-tab-content-${this._groupId}-${i}`;\n    }\n    /**\n     * Sets the height of the body wrapper to the height of the activating tab if dynamic\n     * height property is true.\n     */\n\n\n    _setTabBodyWrapperHeight(tabHeight) {\n      if (!this._dynamicHeight || !this._tabBodyWrapperHeight) {\n        return;\n      }\n\n      const wrapper = this._tabBodyWrapper.nativeElement;\n      wrapper.style.height = this._tabBodyWrapperHeight + 'px'; // This conditional forces the browser to paint the height so that\n      // the animation to the new height can have an origin.\n\n      if (this._tabBodyWrapper.nativeElement.offsetHeight) {\n        wrapper.style.height = tabHeight + 'px';\n      }\n    }\n    /** Removes the height of the tab body wrapper. */\n\n\n    _removeTabBodyWrapperHeight() {\n      const wrapper = this._tabBodyWrapper.nativeElement;\n      this._tabBodyWrapperHeight = wrapper.clientHeight;\n      wrapper.style.height = '';\n      this.animationDone.emit();\n    }\n    /** Handle click events, setting new selected index if appropriate. */\n\n\n    _handleClick(tab, tabHeader, index) {\n      if (!tab.disabled) {\n        this.selectedIndex = tabHeader.focusIndex = index;\n      }\n    }\n    /** Retrieves the tabindex for the tab. */\n\n\n    _getTabIndex(tab, index) {\n      if (tab.disabled) {\n        return null;\n      }\n\n      const targetIndex = this._lastFocusedTabIndex ?? this.selectedIndex;\n      return index === targetIndex ? 0 : -1;\n    }\n    /** Callback for when the focused state of a tab has changed. */\n\n\n    _tabFocusChanged(focusOrigin, index) {\n      // Mouse/touch focus happens during the `mousedown`/`touchstart` phase which\n      // can cause the tab to be moved out from under the pointer, interrupting the\n      // click sequence (see #21898). We don't need to scroll the tab into view for\n      // such cases anyway, because it will be done when the tab becomes selected.\n      if (focusOrigin && focusOrigin !== 'mouse' && focusOrigin !== 'touch') {\n        this._tabHeader.focusIndex = index;\n      }\n    }\n\n  }\n\n  _MatTabGroupBase.ɵfac = function _MatTabGroupBase_Factory(t) {\n    return new (t || _MatTabGroupBase)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_TABS_CONFIG, 8), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n  };\n\n  _MatTabGroupBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: _MatTabGroupBase,\n    inputs: {\n      dynamicHeight: \"dynamicHeight\",\n      selectedIndex: \"selectedIndex\",\n      headerPosition: \"headerPosition\",\n      animationDuration: \"animationDuration\",\n      contentTabIndex: \"contentTabIndex\",\n      disablePagination: \"disablePagination\",\n      preserveContent: \"preserveContent\",\n      backgroundColor: \"backgroundColor\"\n    },\n    outputs: {\n      selectedIndexChange: \"selectedIndexChange\",\n      focusChange: \"focusChange\",\n      animationDone: \"animationDone\",\n      selectedTabChange: \"selectedTabChange\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return _MatTabGroupBase;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Material design tab-group component. Supports basic tab pairs (label + content) and includes\n * animated ink-bar, keyboard navigation, and screen reader.\n * See: https://material.io/design/components/tabs.html\n */\n\n\nlet MatTabGroup = /*#__PURE__*/(() => {\n  class MatTabGroup extends _MatTabGroupBase {\n    constructor(elementRef, changeDetectorRef, defaultConfig, animationMode) {\n      super(elementRef, changeDetectorRef, defaultConfig, animationMode);\n    }\n\n  }\n\n  MatTabGroup.ɵfac = function MatTabGroup_Factory(t) {\n    return new (t || MatTabGroup)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_TABS_CONFIG, 8), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n  };\n\n  MatTabGroup.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatTabGroup,\n    selectors: [[\"mat-tab-group\"]],\n    contentQueries: function MatTabGroup_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatTab, 5);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allTabs = _t);\n      }\n    },\n    viewQuery: function MatTabGroup_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c8, 5);\n        i0.ɵɵviewQuery(_c9, 5);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabBodyWrapper = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabHeader = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-tab-group\"],\n    hostVars: 4,\n    hostBindings: function MatTabGroup_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-tab-group-dynamic-height\", ctx.dynamicHeight)(\"mat-tab-group-inverted-header\", ctx.headerPosition === \"below\");\n      }\n    },\n    inputs: {\n      color: \"color\",\n      disableRipple: \"disableRipple\"\n    },\n    exportAs: [\"matTabGroup\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_TAB_GROUP,\n      useExisting: MatTabGroup\n    }]), i0.ɵɵInheritDefinitionFeature],\n    decls: 6,\n    vars: 7,\n    consts: [[3, \"selectedIndex\", \"disableRipple\", \"disablePagination\", \"indexFocused\", \"selectFocusedIndex\"], [\"tabHeader\", \"\"], [\"class\", \"mat-tab-label mat-focus-indicator\", \"role\", \"tab\", \"matTabLabelWrapper\", \"\", \"mat-ripple\", \"\", \"cdkMonitorElementFocus\", \"\", 3, \"id\", \"mat-tab-label-active\", \"ngClass\", \"disabled\", \"matRippleDisabled\", \"click\", \"cdkFocusChange\", 4, \"ngFor\", \"ngForOf\"], [1, \"mat-tab-body-wrapper\"], [\"tabBodyWrapper\", \"\"], [\"role\", \"tabpanel\", 3, \"id\", \"mat-tab-body-active\", \"ngClass\", \"content\", \"position\", \"origin\", \"animationDuration\", \"preserveContent\", \"_onCentered\", \"_onCentering\", 4, \"ngFor\", \"ngForOf\"], [\"role\", \"tab\", \"matTabLabelWrapper\", \"\", \"mat-ripple\", \"\", \"cdkMonitorElementFocus\", \"\", 1, \"mat-tab-label\", \"mat-focus-indicator\", 3, \"id\", \"ngClass\", \"disabled\", \"matRippleDisabled\", \"click\", \"cdkFocusChange\"], [1, \"mat-tab-label-content\"], [3, \"ngIf\", \"ngIfElse\"], [\"tabTextLabel\", \"\"], [3, \"cdkPortalOutlet\"], [\"role\", \"tabpanel\", 3, \"id\", \"ngClass\", \"content\", \"position\", \"origin\", \"animationDuration\", \"preserveContent\", \"_onCentered\", \"_onCentering\"]],\n    template: function MatTabGroup_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"mat-tab-header\", 0, 1);\n        i0.ɵɵlistener(\"indexFocused\", function MatTabGroup_Template_mat_tab_header_indexFocused_0_listener($event) {\n          return ctx._focusChanged($event);\n        })(\"selectFocusedIndex\", function MatTabGroup_Template_mat_tab_header_selectFocusedIndex_0_listener($event) {\n          return ctx.selectedIndex = $event;\n        });\n        i0.ɵɵtemplate(2, MatTabGroup_div_2_Template, 5, 15, \"div\", 2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"div\", 3, 4);\n        i0.ɵɵtemplate(5, MatTabGroup_mat_tab_body_5_Template, 1, 11, \"mat-tab-body\", 5);\n        i0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵproperty(\"selectedIndex\", ctx.selectedIndex || 0)(\"disableRipple\", ctx.disableRipple)(\"disablePagination\", ctx.disablePagination);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx._tabs);\n        i0.ɵɵadvance(1);\n        i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx._tabs);\n      }\n    },\n    dependencies: [i1$2.NgClass, i1$2.NgForOf, i1$2.NgIf, i2.CdkPortalOutlet, i4.MatRipple, i7.CdkMonitorFocus, MatTabLabelWrapper, MatTabBody, MatTabHeader],\n    styles: [\".mat-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-tab-group.mat-tab-group-inverted-header{flex-direction:column-reverse}.mat-tab-label{height:48px;padding:0 24px;cursor:pointer;box-sizing:border-box;opacity:.6;min-width:160px;text-align:center;display:inline-flex;justify-content:center;align-items:center;white-space:nowrap;position:relative}.mat-tab-label:focus{outline:none}.mat-tab-label:focus:not(.mat-tab-disabled){opacity:1}.mat-tab-label.mat-tab-disabled{cursor:default}.cdk-high-contrast-active .mat-tab-label.mat-tab-disabled{opacity:.5}.mat-tab-label .mat-tab-label-content{display:inline-flex;justify-content:center;align-items:center;white-space:nowrap}.cdk-high-contrast-active .mat-tab-label{opacity:1}@media(max-width: 599px){.mat-tab-label{padding:0 12px}}@media(max-width: 959px){.mat-tab-label{padding:0 12px}}.mat-tab-group[mat-stretch-tabs]>.mat-tab-header .mat-tab-label{flex-basis:0;flex-grow:1}.mat-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}.mat-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-tab-body.mat-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-tab-group.mat-tab-group-dynamic-height .mat-tab-body.mat-tab-body-active{overflow-y:hidden}\"],\n    encapsulation: 2\n  });\n  return MatTabGroup;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Increasing integer for generating unique ids for tab nav components.\n\n\nlet nextUniqueId = 0;\n/**\n * Base class with all of the `MatTabNav` functionality.\n * @docs-private\n */\n\nlet _MatTabNavBase = /*#__PURE__*/(() => {\n  class _MatTabNavBase extends MatPaginatedTabHeader {\n    constructor(elementRef, dir, ngZone, changeDetectorRef, viewportRuler, platform, animationMode) {\n      super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n      this._disableRipple = false;\n      /** Theme color of the nav bar. */\n\n      this.color = 'primary';\n    }\n    /** Background color of the tab nav. */\n\n\n    get backgroundColor() {\n      return this._backgroundColor;\n    }\n\n    set backgroundColor(value) {\n      const classList = this._elementRef.nativeElement.classList;\n      classList.remove(`mat-background-${this.backgroundColor}`);\n\n      if (value) {\n        classList.add(`mat-background-${value}`);\n      }\n\n      this._backgroundColor = value;\n    }\n    /** Whether the ripple effect is disabled or not. */\n\n\n    get disableRipple() {\n      return this._disableRipple;\n    }\n\n    set disableRipple(value) {\n      this._disableRipple = coerceBooleanProperty(value);\n    }\n\n    _itemSelected() {// noop\n    }\n\n    ngAfterContentInit() {\n      // We need this to run before the `changes` subscription in parent to ensure that the\n      // selectedIndex is up-to-date by the time the super class starts looking for it.\n      this._items.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n        this.updateActiveLink();\n      });\n\n      super.ngAfterContentInit();\n    }\n    /** Notifies the component that the active link has been changed. */\n\n\n    updateActiveLink() {\n      if (!this._items) {\n        return;\n      }\n\n      const items = this._items.toArray();\n\n      for (let i = 0; i < items.length; i++) {\n        if (items[i].active) {\n          this.selectedIndex = i;\n\n          this._changeDetectorRef.markForCheck();\n\n          if (this.tabPanel) {\n            this.tabPanel._activeTabId = items[i].id;\n          }\n\n          return;\n        }\n      } // The ink bar should hide itself if no items are active.\n\n\n      this.selectedIndex = -1;\n\n      this._inkBar.hide();\n    }\n\n    _getRole() {\n      return this.tabPanel ? 'tablist' : this._elementRef.nativeElement.getAttribute('role');\n    }\n\n  }\n\n  _MatTabNavBase.ɵfac = function _MatTabNavBase_Factory(t) {\n    return new (t || _MatTabNavBase)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.ViewportRuler), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n  };\n\n  _MatTabNavBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: _MatTabNavBase,\n    inputs: {\n      backgroundColor: \"backgroundColor\",\n      disableRipple: \"disableRipple\",\n      color: \"color\",\n      tabPanel: \"tabPanel\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return _MatTabNavBase;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Navigation component matching the styles of the tab group header.\n * Provides anchored navigation with animated ink bar.\n */\n\n\nlet MatTabNav = /*#__PURE__*/(() => {\n  class MatTabNav extends _MatTabNavBase {\n    constructor(elementRef, dir, ngZone, changeDetectorRef, viewportRuler, platform, animationMode) {\n      super(elementRef, dir, ngZone, changeDetectorRef, viewportRuler, platform, animationMode);\n    }\n\n  }\n\n  MatTabNav.ɵfac = function MatTabNav_Factory(t) {\n    return new (t || MatTabNav)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.ViewportRuler), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n  };\n\n  MatTabNav.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatTabNav,\n    selectors: [[\"\", \"mat-tab-nav-bar\", \"\"]],\n    contentQueries: function MatTabNav_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatTabLink, 5);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._items = _t);\n      }\n    },\n    viewQuery: function MatTabNav_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(MatInkBar, 7);\n        i0.ɵɵviewQuery(_c3, 7);\n        i0.ɵɵviewQuery(_c4, 7);\n        i0.ɵɵviewQuery(_c5, 7);\n        i0.ɵɵviewQuery(_c6, 5);\n        i0.ɵɵviewQuery(_c7, 5);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._inkBar = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListContainer = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabList = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListInner = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nextPaginator = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._previousPaginator = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-tab-nav-bar\", \"mat-tab-header\"],\n    hostVars: 11,\n    hostBindings: function MatTabNav_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"role\", ctx._getRole());\n        i0.ɵɵclassProp(\"mat-tab-header-pagination-controls-enabled\", ctx._showPaginationControls)(\"mat-tab-header-rtl\", ctx._getLayoutDirection() == \"rtl\")(\"mat-primary\", ctx.color !== \"warn\" && ctx.color !== \"accent\")(\"mat-accent\", ctx.color === \"accent\")(\"mat-warn\", ctx.color === \"warn\");\n      }\n    },\n    inputs: {\n      color: \"color\"\n    },\n    exportAs: [\"matTabNavBar\", \"matTabNav\"],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    attrs: _c10,\n    ngContentSelectors: _c0,\n    decls: 14,\n    vars: 10,\n    consts: [[\"aria-hidden\", \"true\", \"type\", \"button\", \"mat-ripple\", \"\", \"tabindex\", \"-1\", 1, \"mat-tab-header-pagination\", \"mat-tab-header-pagination-before\", \"mat-elevation-z4\", 3, \"matRippleDisabled\", \"disabled\", \"click\", \"mousedown\", \"touchend\"], [\"previousPaginator\", \"\"], [1, \"mat-tab-header-pagination-chevron\"], [1, \"mat-tab-link-container\", 3, \"keydown\"], [\"tabListContainer\", \"\"], [1, \"mat-tab-list\", 3, \"cdkObserveContent\"], [\"tabList\", \"\"], [1, \"mat-tab-links\"], [\"tabListInner\", \"\"], [\"aria-hidden\", \"true\", \"type\", \"button\", \"mat-ripple\", \"\", \"tabindex\", \"-1\", 1, \"mat-tab-header-pagination\", \"mat-tab-header-pagination-after\", \"mat-elevation-z4\", 3, \"matRippleDisabled\", \"disabled\", \"mousedown\", \"click\", \"touchend\"], [\"nextPaginator\", \"\"]],\n    template: function MatTabNav_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"button\", 0, 1);\n        i0.ɵɵlistener(\"click\", function MatTabNav_Template_button_click_0_listener() {\n          return ctx._handlePaginatorClick(\"before\");\n        })(\"mousedown\", function MatTabNav_Template_button_mousedown_0_listener($event) {\n          return ctx._handlePaginatorPress(\"before\", $event);\n        })(\"touchend\", function MatTabNav_Template_button_touchend_0_listener() {\n          return ctx._stopInterval();\n        });\n        i0.ɵɵelement(2, \"div\", 2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"div\", 3, 4);\n        i0.ɵɵlistener(\"keydown\", function MatTabNav_Template_div_keydown_3_listener($event) {\n          return ctx._handleKeydown($event);\n        });\n        i0.ɵɵelementStart(5, \"div\", 5, 6);\n        i0.ɵɵlistener(\"cdkObserveContent\", function MatTabNav_Template_div_cdkObserveContent_5_listener() {\n          return ctx._onContentChanges();\n        });\n        i0.ɵɵelementStart(7, \"div\", 7, 8);\n        i0.ɵɵprojection(9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(10, \"mat-ink-bar\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"button\", 9, 10);\n        i0.ɵɵlistener(\"mousedown\", function MatTabNav_Template_button_mousedown_11_listener($event) {\n          return ctx._handlePaginatorPress(\"after\", $event);\n        })(\"click\", function MatTabNav_Template_button_click_11_listener() {\n          return ctx._handlePaginatorClick(\"after\");\n        })(\"touchend\", function MatTabNav_Template_button_touchend_11_listener() {\n          return ctx._stopInterval();\n        });\n        i0.ɵɵelement(13, \"div\", 2);\n        i0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-tab-header-pagination-disabled\", ctx._disableScrollBefore);\n        i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollBefore || ctx.disableRipple)(\"disabled\", ctx._disableScrollBefore || null);\n        i0.ɵɵadvance(5);\n        i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n        i0.ɵɵadvance(6);\n        i0.ɵɵclassProp(\"mat-tab-header-pagination-disabled\", ctx._disableScrollAfter);\n        i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollAfter || ctx.disableRipple)(\"disabled\", ctx._disableScrollAfter || null);\n      }\n    },\n    dependencies: [i4.MatRipple, i5.CdkObserveContent, MatInkBar],\n    styles: [\".mat-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mat-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-tab-header-pagination::-moz-focus-inner{border:0}.mat-tab-header-pagination-controls-enabled .mat-tab-header-pagination{display:flex}.mat-tab-header-pagination-before,.mat-tab-header-rtl .mat-tab-header-pagination-after{padding-left:4px}.mat-tab-header-pagination-before .mat-tab-header-pagination-chevron,.mat-tab-header-rtl .mat-tab-header-pagination-after .mat-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-tab-header-rtl .mat-tab-header-pagination-before,.mat-tab-header-pagination-after{padding-right:4px}.mat-tab-header-rtl .mat-tab-header-pagination-before .mat-tab-header-pagination-chevron,.mat-tab-header-pagination-after .mat-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px}.mat-tab-header-pagination-disabled{box-shadow:none;cursor:default}.mat-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-tab-links{display:flex}[mat-align-tabs=center]>.mat-tab-link-container .mat-tab-links{justify-content:center}[mat-align-tabs=end]>.mat-tab-link-container .mat-tab-links{justify-content:flex-end}.mat-ink-bar{position:absolute;bottom:0;height:2px;transition:500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-ink-bar._mat-animation-noopable{transition:none !important;animation:none !important}.mat-tab-group-inverted-header .mat-ink-bar{bottom:auto;top:0}.cdk-high-contrast-active .mat-ink-bar{outline:solid 2px;height:0}.mat-tab-link-container{display:flex;flex-grow:1;overflow:hidden;z-index:1}.mat-tab-link{height:48px;padding:0 24px;cursor:pointer;box-sizing:border-box;opacity:.6;min-width:160px;text-align:center;display:inline-flex;justify-content:center;align-items:center;white-space:nowrap;vertical-align:top;text-decoration:none;position:relative;overflow:hidden;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-tab-link:focus{outline:none}.mat-tab-link:focus:not(.mat-tab-disabled){opacity:1}.mat-tab-link.mat-tab-disabled{cursor:default}.cdk-high-contrast-active .mat-tab-link.mat-tab-disabled{opacity:.5}.mat-tab-link .mat-tab-label-content{display:inline-flex;justify-content:center;align-items:center;white-space:nowrap}.cdk-high-contrast-active .mat-tab-link{opacity:1}[mat-stretch-tabs] .mat-tab-link{flex-basis:0;flex-grow:1}.mat-tab-link.mat-tab-disabled{pointer-events:none}.mat-tab-link::before{margin:5px}@media(max-width: 599px){.mat-tab-link{min-width:72px}}\"],\n    encapsulation: 2\n  });\n  return MatTabNav;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})(); // Boilerplate for applying mixins to MatTabLink.\n\n\nconst _MatTabLinkMixinBase = /*#__PURE__*/mixinTabIndex( /*#__PURE__*/mixinDisableRipple( /*#__PURE__*/mixinDisabled(class {})));\n/** Base class with all of the `MatTabLink` functionality. */\n\n\nlet _MatTabLinkBase = /*#__PURE__*/(() => {\n  class _MatTabLinkBase extends _MatTabLinkMixinBase {\n    constructor(_tabNavBar,\n    /** @docs-private */\n    elementRef, globalRippleOptions, tabIndex, _focusMonitor, animationMode) {\n      super();\n      this._tabNavBar = _tabNavBar;\n      this.elementRef = elementRef;\n      this._focusMonitor = _focusMonitor;\n      /** Whether the tab link is active or not. */\n\n      this._isActive = false;\n      /** Unique id for the tab. */\n\n      this.id = `mat-tab-link-${nextUniqueId++}`;\n      this.rippleConfig = globalRippleOptions || {};\n      this.tabIndex = parseInt(tabIndex) || 0;\n\n      if (animationMode === 'NoopAnimations') {\n        this.rippleConfig.animation = {\n          enterDuration: 0,\n          exitDuration: 0\n        };\n      }\n    }\n    /** Whether the link is active. */\n\n\n    get active() {\n      return this._isActive;\n    }\n\n    set active(value) {\n      const newValue = coerceBooleanProperty(value);\n\n      if (newValue !== this._isActive) {\n        this._isActive = newValue;\n\n        this._tabNavBar.updateActiveLink();\n      }\n    }\n    /**\n     * Whether ripples are disabled on interaction.\n     * @docs-private\n     */\n\n\n    get rippleDisabled() {\n      return this.disabled || this.disableRipple || this._tabNavBar.disableRipple || !!this.rippleConfig.disabled;\n    }\n    /** Focuses the tab link. */\n\n\n    focus() {\n      this.elementRef.nativeElement.focus();\n    }\n\n    ngAfterViewInit() {\n      this._focusMonitor.monitor(this.elementRef);\n    }\n\n    ngOnDestroy() {\n      this._focusMonitor.stopMonitoring(this.elementRef);\n    }\n\n    _handleFocus() {\n      // Since we allow navigation through tabbing in the nav bar, we\n      // have to update the focused index whenever the link receives focus.\n      this._tabNavBar.focusIndex = this._tabNavBar._items.toArray().indexOf(this);\n    }\n\n    _handleKeydown(event) {\n      if (this._tabNavBar.tabPanel && event.keyCode === SPACE) {\n        this.elementRef.nativeElement.click();\n      }\n    }\n\n    _getAriaControls() {\n      return this._tabNavBar.tabPanel ? this._tabNavBar.tabPanel?.id : this.elementRef.nativeElement.getAttribute('aria-controls');\n    }\n\n    _getAriaSelected() {\n      if (this._tabNavBar.tabPanel) {\n        return this.active ? 'true' : 'false';\n      } else {\n        return this.elementRef.nativeElement.getAttribute('aria-selected');\n      }\n    }\n\n    _getAriaCurrent() {\n      return this.active && !this._tabNavBar.tabPanel ? 'page' : null;\n    }\n\n    _getRole() {\n      return this._tabNavBar.tabPanel ? 'tab' : this.elementRef.nativeElement.getAttribute('role');\n    }\n\n    _getTabIndex() {\n      if (this._tabNavBar.tabPanel) {\n        return this._isActive && !this.disabled ? 0 : -1;\n      } else {\n        return this.tabIndex;\n      }\n    }\n\n  }\n\n  _MatTabLinkBase.ɵfac = function _MatTabLinkBase_Factory(t) {\n    return new (t || _MatTabLinkBase)(i0.ɵɵdirectiveInject(_MatTabNavBase), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MAT_RIPPLE_GLOBAL_OPTIONS, 8), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(i7.FocusMonitor), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n  };\n\n  _MatTabLinkBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: _MatTabLinkBase,\n    inputs: {\n      active: \"active\",\n      id: \"id\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return _MatTabLinkBase;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Link inside of a `mat-tab-nav-bar`.\n */\n\n\nlet MatTabLink = /*#__PURE__*/(() => {\n  class MatTabLink extends _MatTabLinkBase {\n    constructor(tabNavBar, elementRef, ngZone, platform, globalRippleOptions, tabIndex, focusMonitor, animationMode) {\n      super(tabNavBar, elementRef, globalRippleOptions, tabIndex, focusMonitor, animationMode);\n      this._tabLinkRipple = new RippleRenderer(this, ngZone, elementRef, platform);\n\n      this._tabLinkRipple.setupTriggerEvents(elementRef.nativeElement);\n    }\n\n    ngOnDestroy() {\n      super.ngOnDestroy();\n\n      this._tabLinkRipple._removeTriggerEvents();\n    }\n\n  }\n\n  MatTabLink.ɵfac = function MatTabLink_Factory(t) {\n    return new (t || MatTabLink)(i0.ɵɵdirectiveInject(MatTabNav), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(MAT_RIPPLE_GLOBAL_OPTIONS, 8), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(i7.FocusMonitor), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n  };\n\n  MatTabLink.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatTabLink,\n    selectors: [[\"\", \"mat-tab-link\", \"\"], [\"\", \"matTabLink\", \"\"]],\n    hostAttrs: [1, \"mat-tab-link\", \"mat-focus-indicator\"],\n    hostVars: 11,\n    hostBindings: function MatTabLink_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"focus\", function MatTabLink_focus_HostBindingHandler() {\n          return ctx._handleFocus();\n        })(\"keydown\", function MatTabLink_keydown_HostBindingHandler($event) {\n          return ctx._handleKeydown($event);\n        });\n      }\n\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-controls\", ctx._getAriaControls())(\"aria-current\", ctx._getAriaCurrent())(\"aria-disabled\", ctx.disabled)(\"aria-selected\", ctx._getAriaSelected())(\"id\", ctx.id)(\"tabIndex\", ctx._getTabIndex())(\"role\", ctx._getRole());\n        i0.ɵɵclassProp(\"mat-tab-disabled\", ctx.disabled)(\"mat-tab-label-active\", ctx.active);\n      }\n    },\n    inputs: {\n      disabled: \"disabled\",\n      disableRipple: \"disableRipple\",\n      tabIndex: \"tabIndex\"\n    },\n    exportAs: [\"matTabLink\"],\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return MatTabLink;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Tab panel component associated with MatTabNav.\n */\n\n\nlet MatTabNavPanel = /*#__PURE__*/(() => {\n  class MatTabNavPanel {\n    constructor() {\n      /** Unique id for the tab panel. */\n      this.id = `mat-tab-nav-panel-${nextUniqueId++}`;\n    }\n\n  }\n\n  MatTabNavPanel.ɵfac = function MatTabNavPanel_Factory(t) {\n    return new (t || MatTabNavPanel)();\n  };\n\n  MatTabNavPanel.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatTabNavPanel,\n    selectors: [[\"mat-tab-nav-panel\"]],\n    hostAttrs: [\"role\", \"tabpanel\", 1, \"mat-tab-nav-panel\"],\n    hostVars: 2,\n    hostBindings: function MatTabNavPanel_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-labelledby\", ctx._activeTabId)(\"id\", ctx.id);\n      }\n    },\n    inputs: {\n      id: \"id\"\n    },\n    exportAs: [\"matTabNavPanel\"],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function MatTabNavPanel_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return MatTabNavPanel;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nlet MatTabsModule = /*#__PURE__*/(() => {\n  class MatTabsModule {}\n\n  MatTabsModule.ɵfac = function MatTabsModule_Factory(t) {\n    return new (t || MatTabsModule)();\n  };\n\n  MatTabsModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatTabsModule\n  });\n  MatTabsModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, MatCommonModule, PortalModule, MatRippleModule, ObserversModule, A11yModule, MatCommonModule]\n  });\n  return MatTabsModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { MAT_TAB, MAT_TABS_CONFIG, MAT_TAB_GROUP, MatInkBar, MatTab, MatTabBody, MatTabBodyPortal, MatTabChangeEvent, MatTabContent, MatTabGroup, MatTabHeader, MatTabLabel, MatTabLabelWrapper, MatTabLink, MatTabNav, MatTabNavPanel, MatTabsModule, _MAT_INK_BAR_POSITIONER, _MatTabBodyBase, _MatTabGroupBase, _MatTabHeaderBase, _MatTabLinkBase, _MatTabNavBase, matTabsAnimations }; //# sourceMappingURL=tabs.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}