const { query } = require('../database/db');

class Configuration {
  // Get a configuration value by key
  static async get(key, defaultValue = null) {
    try {
      const result = await query(
        'SELECT value FROM configuration WHERE key = $1',
        [key]
      );
      
      if (result.rows.length === 0) {
        return defaultValue;
      }
      
      return result.rows[0].value;
    } catch (error) {
      console.error('Error getting configuration:', error);
      return defaultValue;
    }
  }

  // Set a configuration value
  static async set(key, value, description = null, updatedBy = 'system') {
    try {
      const result = await query(
        `INSERT INTO configuration (key, value, description, updated_by) 
         VALUES ($1, $2, $3, $4)
         ON CONFLICT (key) 
         DO UPDATE SET 
           value = EXCLUDED.value,
           description = COALESCE(EXCLUDED.description, configuration.description),
           updated_by = EXCLUDED.updated_by,
           updated_at = CURRENT_TIMESTAMP
         RETURNING *`,
        [key, value, description, updatedBy]
      );
      
      return result.rows[0];
    } catch (error) {
      console.error('Error setting configuration:', error);
      throw error;
    }
  }

  // Get all configuration values
  static async getAll() {
    try {
      const result = await query(
        'SELECT * FROM configuration ORDER BY key'
      );
      
      return result.rows;
    } catch (error) {
      console.error('Error getting all configuration:', error);
      throw error;
    }
  }

  // Delete a configuration key
  static async delete(key) {
    try {
      const result = await query(
        'DELETE FROM configuration WHERE key = $1 RETURNING *',
        [key]
      );
      
      return result.rows[0];
    } catch (error) {
      console.error('Error deleting configuration:', error);
      throw error;
    }
  }

  // Initialize default security configuration
  static async initializeDefaults() {
    try {
      const defaults = [
        {
          key: 'SECURITY_MODE',
          value: 'whitelist',
          description: 'Security mode: open, whitelist, or invite'
        },
        {
          key: 'ADMIN_USERS',
          value: 'admin',
          description: 'Comma-separated list of admin usernames'
        },
        {
          key: 'MAX_MESSAGE_LENGTH',
          value: '1000',
          description: 'Maximum message length in characters'
        },
        {
          key: 'MESSAGE_RATE_LIMIT',
          value: '10',
          description: 'Maximum messages per minute per user'
        },
        {
          key: 'SESSION_TIMEOUT',
          value: '3600',
          description: 'Session timeout in seconds (1 hour)'
        }
      ];

      for (const config of defaults) {
        await this.set(config.key, config.value, config.description, 'system');
      }

      console.log('Default configuration initialized');
    } catch (error) {
      console.error('Error initializing default configuration:', error);
      throw error;
    }
  }

  // Get security configuration specifically
  static async getSecurityConfig() {
    try {
      const securityMode = await this.get('SECURITY_MODE', 'whitelist');
      const adminUsers = await this.get('ADMIN_USERS', 'admin');
      
      return {
        securityMode,
        adminUsers: adminUsers.split(',').map(user => user.trim().toLowerCase())
      };
    } catch (error) {
      console.error('Error getting security configuration:', error);
      return {
        securityMode: 'whitelist',
        adminUsers: ['admin']
      };
    }
  }

  // Update security configuration
  static async updateSecurityConfig(securityMode, adminUsers, updatedBy = 'system') {
    try {
      await this.set('SECURITY_MODE', securityMode, 'Security mode: open, whitelist, or invite', updatedBy);
      await this.set('ADMIN_USERS', adminUsers.join(','), 'Comma-separated list of admin usernames', updatedBy);
      
      return await this.getSecurityConfig();
    } catch (error) {
      console.error('Error updating security configuration:', error);
      throw error;
    }
  }

  // Check if a user is an admin based on database configuration
  static async isUserAdmin(username) {
    try {
      const { adminUsers } = await this.getSecurityConfig();
      return adminUsers.includes(username.toLowerCase());
    } catch (error) {
      console.error('Error checking admin status:', error);
      return false;
    }
  }
}

module.exports = Configuration;
