const fs = require('fs');
const path = require('path');
const { pool } = require('../database/db');
const AllowedUser = require('../models/AllowedUser');

async function runMigration() {
  try {
    console.log('Running database migration...');
    
    const schemaPath = path.join(__dirname, '../database/schema.sql');
    const schema = fs.readFileSync(schemaPath, 'utf8');
    
    await pool.query(schema);
    console.log('Migration completed successfully!');
    
    // Create a default system user
    await pool.query(`
      INSERT INTO users (username) 
      VALUES ('System') 
      ON CONFLICT (username) DO NOTHING
    `);
    
    console.log('Default system user created');
    
    // Initialize admin users in whitelist
    await AllowedUser.initializeDefaults();
    console.log('Admin users initialized');
    
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

runMigration();
