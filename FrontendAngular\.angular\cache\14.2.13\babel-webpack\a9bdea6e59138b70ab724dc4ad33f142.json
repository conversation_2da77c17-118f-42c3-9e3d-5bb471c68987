{"ast": null, "code": "import { innerFrom } from '../observable/innerFrom';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { operate } from '../util/lift';\nexport function catchError(selector) {\n  return operate((source, subscriber) => {\n    let innerSub = null;\n    let syncUnsub = false;\n    let handledResult;\n    innerSub = source.subscribe(createOperatorSubscriber(subscriber, undefined, undefined, err => {\n      handledResult = innerFrom(selector(err, catchError(selector)(source)));\n\n      if (innerSub) {\n        innerSub.unsubscribe();\n        innerSub = null;\n        handledResult.subscribe(subscriber);\n      } else {\n        syncUnsub = true;\n      }\n    }));\n\n    if (syncUnsub) {\n      innerSub.unsubscribe();\n      innerSub = null;\n      handledResult.subscribe(subscriber);\n    }\n  });\n}", "map": {"version": 3, "names": ["innerFrom", "createOperatorSubscriber", "operate", "catchError", "selector", "source", "subscriber", "innerSub", "syncUnsub", "handledResult", "subscribe", "undefined", "err", "unsubscribe"], "sources": ["R:/chateye/FrontendAngular/node_modules/rxjs/dist/esm/internal/operators/catchError.js"], "sourcesContent": ["import { innerFrom } from '../observable/innerFrom';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { operate } from '../util/lift';\nexport function catchError(selector) {\n    return operate((source, subscriber) => {\n        let innerSub = null;\n        let syncUnsub = false;\n        let handledResult;\n        innerSub = source.subscribe(createOperatorSubscriber(subscriber, undefined, undefined, (err) => {\n            handledResult = innerFrom(selector(err, catchError(selector)(source)));\n            if (innerSub) {\n                innerSub.unsubscribe();\n                innerSub = null;\n                handledResult.subscribe(subscriber);\n            }\n            else {\n                syncUnsub = true;\n            }\n        }));\n        if (syncUnsub) {\n            innerSub.unsubscribe();\n            innerSub = null;\n            handledResult.subscribe(subscriber);\n        }\n    });\n}\n"], "mappings": "AAAA,SAASA,SAAT,QAA0B,yBAA1B;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,OAAO,SAASC,UAAT,CAAoBC,QAApB,EAA8B;EACjC,OAAOF,OAAO,CAAC,CAACG,MAAD,EAASC,UAAT,KAAwB;IACnC,IAAIC,QAAQ,GAAG,IAAf;IACA,IAAIC,SAAS,GAAG,KAAhB;IACA,IAAIC,aAAJ;IACAF,QAAQ,GAAGF,MAAM,CAACK,SAAP,CAAiBT,wBAAwB,CAACK,UAAD,EAAaK,SAAb,EAAwBA,SAAxB,EAAoCC,GAAD,IAAS;MAC5FH,aAAa,GAAGT,SAAS,CAACI,QAAQ,CAACQ,GAAD,EAAMT,UAAU,CAACC,QAAD,CAAV,CAAqBC,MAArB,CAAN,CAAT,CAAzB;;MACA,IAAIE,QAAJ,EAAc;QACVA,QAAQ,CAACM,WAAT;QACAN,QAAQ,GAAG,IAAX;QACAE,aAAa,CAACC,SAAd,CAAwBJ,UAAxB;MACH,CAJD,MAKK;QACDE,SAAS,GAAG,IAAZ;MACH;IACJ,CAVmD,CAAzC,CAAX;;IAWA,IAAIA,SAAJ,EAAe;MACXD,QAAQ,CAACM,WAAT;MACAN,QAAQ,GAAG,IAAX;MACAE,aAAa,CAACC,SAAd,CAAwBJ,UAAxB;IACH;EACJ,CApBa,CAAd;AAqBH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}