{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Version, InjectionToken, NgModule, Optional, Inject, inject, LOCALE_ID, Injectable, Directive, Input, Component, ViewEncapsulation, ChangeDetectionStrategy, EventEmitter, Output } from '@angular/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader } from '@angular/cdk/a11y';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport { VERSION as VERSION$1 } from '@angular/cdk';\nimport * as i3 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i1$1 from '@angular/cdk/platform';\nimport { _isTestEnvironment, normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport { coerceBooleanProperty, coerceNumberProperty, coerceElement } from '@angular/cdk/coercion';\nimport { Observable, Subject } from 'rxjs';\nimport { startWith } from 'rxjs/operators';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { ENTER, SPACE, hasModifierKey } from '@angular/cdk/keycodes';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Current version of Angular Material. */\n\nconst _c0 = [\"*\", [[\"mat-option\"], [\"ng-container\"]]];\nconst _c1 = [\"*\", \"mat-option, ng-container\"];\n\nfunction MatOption_mat_pseudo_checkbox_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-pseudo-checkbox\", 4);\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"state\", ctx_r0.selected ? \"checked\" : \"unchecked\")(\"disabled\", ctx_r0.disabled);\n  }\n}\n\nfunction MatOption_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 5);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r1.group.label, \")\");\n  }\n}\n\nconst _c2 = [\"*\"];\nconst VERSION = /*#__PURE__*/new Version('14.2.7');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** @docs-private */\n\nlet AnimationCurves = /*#__PURE__*/(() => {\n  class AnimationCurves {}\n\n  AnimationCurves.STANDARD_CURVE = 'cubic-bezier(0.4,0.0,0.2,1)';\n  AnimationCurves.DECELERATION_CURVE = 'cubic-bezier(0.0,0.0,0.2,1)';\n  AnimationCurves.ACCELERATION_CURVE = 'cubic-bezier(0.4,0.0,1,1)';\n  AnimationCurves.SHARP_CURVE = 'cubic-bezier(0.4,0.0,0.6,1)';\n  /** @docs-private */\n\n  return AnimationCurves;\n})();\nlet AnimationDurations = /*#__PURE__*/(() => {\n  class AnimationDurations {}\n\n  AnimationDurations.COMPLEX = '375ms';\n  AnimationDurations.ENTERING = '225ms';\n  AnimationDurations.EXITING = '195ms';\n  /**\n   * @license\n   * Copyright Google LLC All Rights Reserved.\n   *\n   * Use of this source code is governed by an MIT-style license that can be\n   * found in the LICENSE file at https://angular.io/license\n   */\n\n  /** @docs-private */\n\n  return AnimationDurations;\n})();\n\nfunction MATERIAL_SANITY_CHECKS_FACTORY() {\n  return true;\n}\n/** Injection token that configures whether the Material sanity checks are enabled. */\n\n\nconst MATERIAL_SANITY_CHECKS = /*#__PURE__*/new InjectionToken('mat-sanity-checks', {\n  providedIn: 'root',\n  factory: MATERIAL_SANITY_CHECKS_FACTORY\n});\n/**\n * Module that captures anything that should be loaded and/or run for *all* Angular Material\n * components. This includes Bidi, etc.\n *\n * This module should be imported to each top-level component module (e.g., MatTabsModule).\n */\n\nlet MatCommonModule = /*#__PURE__*/(() => {\n  class MatCommonModule {\n    constructor(highContrastModeDetector, _sanityChecks, _document) {\n      this._sanityChecks = _sanityChecks;\n      this._document = _document;\n      /** Whether we've done the global sanity checks (e.g. a theme is loaded, there is a doctype). */\n\n      this._hasDoneGlobalChecks = false; // While A11yModule also does this, we repeat it here to avoid importing A11yModule\n      // in MatCommonModule.\n\n      highContrastModeDetector._applyBodyHighContrastModeCssClasses();\n\n      if (!this._hasDoneGlobalChecks) {\n        this._hasDoneGlobalChecks = true;\n\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          if (this._checkIsEnabled('doctype')) {\n            _checkDoctypeIsDefined(this._document);\n          }\n\n          if (this._checkIsEnabled('theme')) {\n            _checkThemeIsPresent(this._document);\n          }\n\n          if (this._checkIsEnabled('version')) {\n            _checkCdkVersionMatch();\n          }\n        }\n      }\n    }\n    /** Gets whether a specific sanity check is enabled. */\n\n\n    _checkIsEnabled(name) {\n      if (_isTestEnvironment()) {\n        return false;\n      }\n\n      if (typeof this._sanityChecks === 'boolean') {\n        return this._sanityChecks;\n      }\n\n      return !!this._sanityChecks[name];\n    }\n\n  }\n\n  MatCommonModule.ɵfac = function MatCommonModule_Factory(t) {\n    return new (t || MatCommonModule)(i0.ɵɵinject(i1.HighContrastModeDetector), i0.ɵɵinject(MATERIAL_SANITY_CHECKS, 8), i0.ɵɵinject(DOCUMENT));\n  };\n\n  MatCommonModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatCommonModule\n  });\n  MatCommonModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [BidiModule, BidiModule]\n  });\n  return MatCommonModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Checks that the page has a doctype. */\n\n\nfunction _checkDoctypeIsDefined(doc) {\n  if (!doc.doctype) {\n    console.warn('Current document does not have a doctype. This may cause ' + 'some Angular Material components not to behave as expected.');\n  }\n}\n/** Checks that a theme has been included. */\n\n\nfunction _checkThemeIsPresent(doc) {\n  // We need to assert that the `body` is defined, because these checks run very early\n  // and the `body` won't be defined if the consumer put their scripts in the `head`.\n  if (!doc.body || typeof getComputedStyle !== 'function') {\n    return;\n  }\n\n  const testElement = doc.createElement('div');\n  testElement.classList.add('mat-theme-loaded-marker');\n  doc.body.appendChild(testElement);\n  const computedStyle = getComputedStyle(testElement); // In some situations the computed style of the test element can be null. For example in\n  // Firefox, the computed style is null if an application is running inside of a hidden iframe.\n  // See: https://bugzilla.mozilla.org/show_bug.cgi?id=548397\n\n  if (computedStyle && computedStyle.display !== 'none') {\n    console.warn('Could not find Angular Material core theme. Most Material ' + 'components may not work as expected. For more info refer ' + 'to the theming guide: https://material.angular.io/guide/theming');\n  }\n\n  testElement.remove();\n}\n/** Checks whether the Material version matches the CDK version. */\n\n\nfunction _checkCdkVersionMatch() {\n  if (VERSION.full !== VERSION$1.full) {\n    console.warn('The Angular Material version (' + VERSION.full + ') does not match ' + 'the Angular CDK version (' + VERSION$1.full + ').\\n' + 'Please ensure the versions of these two packages exactly match.');\n  }\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction mixinDisabled(base) {\n  return class extends base {\n    constructor(...args) {\n      super(...args);\n      this._disabled = false;\n    }\n\n    get disabled() {\n      return this._disabled;\n    }\n\n    set disabled(value) {\n      this._disabled = coerceBooleanProperty(value);\n    }\n\n  };\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction mixinColor(base, defaultColor) {\n  return class extends base {\n    constructor(...args) {\n      super(...args);\n      this.defaultColor = defaultColor; // Set the default color that can be specified from the mixin.\n\n      this.color = defaultColor;\n    }\n\n    get color() {\n      return this._color;\n    }\n\n    set color(value) {\n      const colorPalette = value || this.defaultColor;\n\n      if (colorPalette !== this._color) {\n        if (this._color) {\n          this._elementRef.nativeElement.classList.remove(`mat-${this._color}`);\n        }\n\n        if (colorPalette) {\n          this._elementRef.nativeElement.classList.add(`mat-${colorPalette}`);\n        }\n\n        this._color = colorPalette;\n      }\n    }\n\n  };\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction mixinDisableRipple(base) {\n  return class extends base {\n    constructor(...args) {\n      super(...args);\n      this._disableRipple = false;\n    }\n    /** Whether the ripple effect is disabled or not. */\n\n\n    get disableRipple() {\n      return this._disableRipple;\n    }\n\n    set disableRipple(value) {\n      this._disableRipple = coerceBooleanProperty(value);\n    }\n\n  };\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction mixinTabIndex(base, defaultTabIndex = 0) {\n  return class extends base {\n    constructor(...args) {\n      super(...args);\n      this._tabIndex = defaultTabIndex;\n      this.defaultTabIndex = defaultTabIndex;\n    }\n\n    get tabIndex() {\n      return this.disabled ? -1 : this._tabIndex;\n    }\n\n    set tabIndex(value) {\n      // If the specified tabIndex value is null or undefined, fall back to the default value.\n      this._tabIndex = value != null ? coerceNumberProperty(value) : this.defaultTabIndex;\n    }\n\n  };\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction mixinErrorState(base) {\n  return class extends base {\n    constructor(...args) {\n      super(...args);\n      /** Whether the component is in an error state. */\n\n      this.errorState = false;\n    }\n    /** Updates the error state based on the provided error state matcher. */\n\n\n    updateErrorState() {\n      const oldState = this.errorState;\n      const parent = this._parentFormGroup || this._parentForm;\n      const matcher = this.errorStateMatcher || this._defaultErrorStateMatcher;\n      const control = this.ngControl ? this.ngControl.control : null;\n      const newState = matcher.isErrorState(control, parent);\n\n      if (newState !== oldState) {\n        this.errorState = newState;\n        this.stateChanges.next();\n      }\n    }\n\n  };\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Mixin to augment a directive with an initialized property that will emits when ngOnInit ends. */\n\n\nfunction mixinInitialized(base) {\n  return class extends base {\n    constructor(...args) {\n      super(...args);\n      /** Whether this directive has been marked as initialized. */\n\n      this._isInitialized = false;\n      /**\n       * List of subscribers that subscribed before the directive was initialized. Should be notified\n       * during _markInitialized. Set to null after pending subscribers are notified, and should\n       * not expect to be populated after.\n       */\n\n      this._pendingSubscribers = [];\n      /**\n       * Observable stream that emits when the directive initializes. If already initialized, the\n       * subscriber is stored to be notified once _markInitialized is called.\n       */\n\n      this.initialized = new Observable(subscriber => {\n        // If initialized, immediately notify the subscriber. Otherwise store the subscriber to notify\n        // when _markInitialized is called.\n        if (this._isInitialized) {\n          this._notifySubscriber(subscriber);\n        } else {\n          this._pendingSubscribers.push(subscriber);\n        }\n      });\n    }\n    /**\n     * Marks the state as initialized and notifies pending subscribers. Should be called at the end\n     * of ngOnInit.\n     * @docs-private\n     */\n\n\n    _markInitialized() {\n      if (this._isInitialized && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('This directive has already been marked as initialized and ' + 'should not be called twice.');\n      }\n\n      this._isInitialized = true;\n\n      this._pendingSubscribers.forEach(this._notifySubscriber);\n\n      this._pendingSubscribers = null;\n    }\n    /** Emits and completes the subscriber stream (should only emit once). */\n\n\n    _notifySubscriber(subscriber) {\n      subscriber.next();\n      subscriber.complete();\n    }\n\n  };\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** InjectionToken for datepicker that can be used to override default locale code. */\n\n\nconst MAT_DATE_LOCALE = /*#__PURE__*/new InjectionToken('MAT_DATE_LOCALE', {\n  providedIn: 'root',\n  factory: MAT_DATE_LOCALE_FACTORY\n});\n/** @docs-private */\n\nfunction MAT_DATE_LOCALE_FACTORY() {\n  return inject(LOCALE_ID);\n}\n/** Adapts type `D` to be usable as a date by cdk-based components that work with dates. */\n\n\nclass DateAdapter {\n  constructor() {\n    this._localeChanges = new Subject();\n    /** A stream that emits when the locale changes. */\n\n    this.localeChanges = this._localeChanges;\n  }\n  /**\n   * Given a potential date object, returns that same date object if it is\n   * a valid date, or `null` if it's not a valid date.\n   * @param obj The object to check.\n   * @returns A date or `null`.\n   */\n\n\n  getValidDateOrNull(obj) {\n    return this.isDateInstance(obj) && this.isValid(obj) ? obj : null;\n  }\n  /**\n   * Attempts to deserialize a value to a valid date object. This is different from parsing in that\n   * deserialize should only accept non-ambiguous, locale-independent formats (e.g. a ISO 8601\n   * string). The default implementation does not allow any deserialization, it simply checks that\n   * the given value is already a valid date object or null. The `<mat-datepicker>` will call this\n   * method on all of its `@Input()` properties that accept dates. It is therefore possible to\n   * support passing values from your backend directly to these properties by overriding this method\n   * to also deserialize the format used by your backend.\n   * @param value The value to be deserialized into a date object.\n   * @returns The deserialized date object, either a valid date, null if the value can be\n   *     deserialized into a null date (e.g. the empty string), or an invalid date.\n   */\n\n\n  deserialize(value) {\n    if (value == null || this.isDateInstance(value) && this.isValid(value)) {\n      return value;\n    }\n\n    return this.invalid();\n  }\n  /**\n   * Sets the locale used for all dates.\n   * @param locale The new locale.\n   */\n\n\n  setLocale(locale) {\n    this.locale = locale;\n\n    this._localeChanges.next();\n  }\n  /**\n   * Compares two dates.\n   * @param first The first date to compare.\n   * @param second The second date to compare.\n   * @returns 0 if the dates are equal, a number less than 0 if the first date is earlier,\n   *     a number greater than 0 if the first date is later.\n   */\n\n\n  compareDate(first, second) {\n    return this.getYear(first) - this.getYear(second) || this.getMonth(first) - this.getMonth(second) || this.getDate(first) - this.getDate(second);\n  }\n  /**\n   * Checks if two dates are equal.\n   * @param first The first date to check.\n   * @param second The second date to check.\n   * @returns Whether the two dates are equal.\n   *     Null dates are considered equal to other null dates.\n   */\n\n\n  sameDate(first, second) {\n    if (first && second) {\n      let firstValid = this.isValid(first);\n      let secondValid = this.isValid(second);\n\n      if (firstValid && secondValid) {\n        return !this.compareDate(first, second);\n      }\n\n      return firstValid == secondValid;\n    }\n\n    return first == second;\n  }\n  /**\n   * Clamp the given date between min and max dates.\n   * @param date The date to clamp.\n   * @param min The minimum value to allow. If null or omitted no min is enforced.\n   * @param max The maximum value to allow. If null or omitted no max is enforced.\n   * @returns `min` if `date` is less than `min`, `max` if date is greater than `max`,\n   *     otherwise `date`.\n   */\n\n\n  clampDate(date, min, max) {\n    if (min && this.compareDate(date, min) < 0) {\n      return min;\n    }\n\n    if (max && this.compareDate(date, max) > 0) {\n      return max;\n    }\n\n    return date;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst MAT_DATE_FORMATS = /*#__PURE__*/new InjectionToken('mat-date-formats');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Matches strings that have the form of a valid RFC 3339 string\n * (https://tools.ietf.org/html/rfc3339). Note that the string may not actually be a valid date\n * because the regex will match strings an with out of bounds month, date, etc.\n */\n\nconst ISO_8601_REGEX = /^\\d{4}-\\d{2}-\\d{2}(?:T\\d{2}:\\d{2}:\\d{2}(?:\\.\\d+)?(?:Z|(?:(?:\\+|-)\\d{2}:\\d{2}))?)?$/;\n/** Creates an array and fills it with values. */\n\nfunction range(length, valueFunction) {\n  const valuesArray = Array(length);\n\n  for (let i = 0; i < length; i++) {\n    valuesArray[i] = valueFunction(i);\n  }\n\n  return valuesArray;\n}\n/** Adapts the native JS Date for use with cdk-based components that work with dates. */\n\n\nlet NativeDateAdapter = /*#__PURE__*/(() => {\n  class NativeDateAdapter extends DateAdapter {\n    constructor(matDateLocale,\n    /**\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 14.0.0\n     */\n    _platform) {\n      super();\n      /**\n       * @deprecated No longer being used. To be removed.\n       * @breaking-change 14.0.0\n       */\n\n      this.useUtcForDisplay = false;\n      super.setLocale(matDateLocale);\n    }\n\n    getYear(date) {\n      return date.getFullYear();\n    }\n\n    getMonth(date) {\n      return date.getMonth();\n    }\n\n    getDate(date) {\n      return date.getDate();\n    }\n\n    getDayOfWeek(date) {\n      return date.getDay();\n    }\n\n    getMonthNames(style) {\n      const dtf = new Intl.DateTimeFormat(this.locale, {\n        month: style,\n        timeZone: 'utc'\n      });\n      return range(12, i => this._format(dtf, new Date(2017, i, 1)));\n    }\n\n    getDateNames() {\n      const dtf = new Intl.DateTimeFormat(this.locale, {\n        day: 'numeric',\n        timeZone: 'utc'\n      });\n      return range(31, i => this._format(dtf, new Date(2017, 0, i + 1)));\n    }\n\n    getDayOfWeekNames(style) {\n      const dtf = new Intl.DateTimeFormat(this.locale, {\n        weekday: style,\n        timeZone: 'utc'\n      });\n      return range(7, i => this._format(dtf, new Date(2017, 0, i + 1)));\n    }\n\n    getYearName(date) {\n      const dtf = new Intl.DateTimeFormat(this.locale, {\n        year: 'numeric',\n        timeZone: 'utc'\n      });\n      return this._format(dtf, date);\n    }\n\n    getFirstDayOfWeek() {\n      // We can't tell using native JS Date what the first day of the week is, we default to Sunday.\n      return 0;\n    }\n\n    getNumDaysInMonth(date) {\n      return this.getDate(this._createDateWithOverflow(this.getYear(date), this.getMonth(date) + 1, 0));\n    }\n\n    clone(date) {\n      return new Date(date.getTime());\n    }\n\n    createDate(year, month, date) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        // Check for invalid month and date (except upper bound on date which we have to check after\n        // creating the Date).\n        if (month < 0 || month > 11) {\n          throw Error(`Invalid month index \"${month}\". Month index has to be between 0 and 11.`);\n        }\n\n        if (date < 1) {\n          throw Error(`Invalid date \"${date}\". Date has to be greater than 0.`);\n        }\n      }\n\n      let result = this._createDateWithOverflow(year, month, date); // Check that the date wasn't above the upper bound for the month, causing the month to overflow\n\n\n      if (result.getMonth() != month && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error(`Invalid date \"${date}\" for month with index \"${month}\".`);\n      }\n\n      return result;\n    }\n\n    today() {\n      return new Date();\n    }\n\n    parse(value, parseFormat) {\n      // We have no way using the native JS Date to set the parse format or locale, so we ignore these\n      // parameters.\n      if (typeof value == 'number') {\n        return new Date(value);\n      }\n\n      return value ? new Date(Date.parse(value)) : null;\n    }\n\n    format(date, displayFormat) {\n      if (!this.isValid(date)) {\n        throw Error('NativeDateAdapter: Cannot format invalid date.');\n      }\n\n      const dtf = new Intl.DateTimeFormat(this.locale, { ...displayFormat,\n        timeZone: 'utc'\n      });\n      return this._format(dtf, date);\n    }\n\n    addCalendarYears(date, years) {\n      return this.addCalendarMonths(date, years * 12);\n    }\n\n    addCalendarMonths(date, months) {\n      let newDate = this._createDateWithOverflow(this.getYear(date), this.getMonth(date) + months, this.getDate(date)); // It's possible to wind up in the wrong month if the original month has more days than the new\n      // month. In this case we want to go to the last day of the desired month.\n      // Note: the additional + 12 % 12 ensures we end up with a positive number, since JS % doesn't\n      // guarantee this.\n\n\n      if (this.getMonth(newDate) != ((this.getMonth(date) + months) % 12 + 12) % 12) {\n        newDate = this._createDateWithOverflow(this.getYear(newDate), this.getMonth(newDate), 0);\n      }\n\n      return newDate;\n    }\n\n    addCalendarDays(date, days) {\n      return this._createDateWithOverflow(this.getYear(date), this.getMonth(date), this.getDate(date) + days);\n    }\n\n    toIso8601(date) {\n      return [date.getUTCFullYear(), this._2digit(date.getUTCMonth() + 1), this._2digit(date.getUTCDate())].join('-');\n    }\n    /**\n     * Returns the given value if given a valid Date or null. Deserializes valid ISO 8601 strings\n     * (https://www.ietf.org/rfc/rfc3339.txt) into valid Dates and empty string into null. Returns an\n     * invalid date for all other values.\n     */\n\n\n    deserialize(value) {\n      if (typeof value === 'string') {\n        if (!value) {\n          return null;\n        } // The `Date` constructor accepts formats other than ISO 8601, so we need to make sure the\n        // string is the right format first.\n\n\n        if (ISO_8601_REGEX.test(value)) {\n          let date = new Date(value);\n\n          if (this.isValid(date)) {\n            return date;\n          }\n        }\n      }\n\n      return super.deserialize(value);\n    }\n\n    isDateInstance(obj) {\n      return obj instanceof Date;\n    }\n\n    isValid(date) {\n      return !isNaN(date.getTime());\n    }\n\n    invalid() {\n      return new Date(NaN);\n    }\n    /** Creates a date but allows the month and date to overflow. */\n\n\n    _createDateWithOverflow(year, month, date) {\n      // Passing the year to the constructor causes year numbers <100 to be converted to 19xx.\n      // To work around this we use `setFullYear` and `setHours` instead.\n      const d = new Date();\n      d.setFullYear(year, month, date);\n      d.setHours(0, 0, 0, 0);\n      return d;\n    }\n    /**\n     * Pads a number to make it two digits.\n     * @param n The number to pad.\n     * @returns The padded number.\n     */\n\n\n    _2digit(n) {\n      return ('00' + n).slice(-2);\n    }\n    /**\n     * When converting Date object to string, javascript built-in functions may return wrong\n     * results because it applies its internal DST rules. The DST rules around the world change\n     * very frequently, and the current valid rule is not always valid in previous years though.\n     * We work around this problem building a new Date object which has its internal UTC\n     * representation with the local date and time.\n     * @param dtf Intl.DateTimeFormat object, containing the desired string format. It must have\n     *    timeZone set to 'utc' to work fine.\n     * @param date Date from which we want to get the string representation according to dtf\n     * @returns A Date object with its UTC representation based on the passed in date info\n     */\n\n\n    _format(dtf, date) {\n      // Passing the year to the constructor causes year numbers <100 to be converted to 19xx.\n      // To work around this we use `setUTCFullYear` and `setUTCHours` instead.\n      const d = new Date();\n      d.setUTCFullYear(date.getFullYear(), date.getMonth(), date.getDate());\n      d.setUTCHours(date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());\n      return dtf.format(d);\n    }\n\n  }\n\n  NativeDateAdapter.ɵfac = function NativeDateAdapter_Factory(t) {\n    return new (t || NativeDateAdapter)(i0.ɵɵinject(MAT_DATE_LOCALE, 8), i0.ɵɵinject(i1$1.Platform));\n  };\n\n  NativeDateAdapter.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NativeDateAdapter,\n    factory: NativeDateAdapter.ɵfac\n  });\n  return NativeDateAdapter;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst MAT_NATIVE_DATE_FORMATS = {\n  parse: {\n    dateInput: null\n  },\n  display: {\n    dateInput: {\n      year: 'numeric',\n      month: 'numeric',\n      day: 'numeric'\n    },\n    monthYearLabel: {\n      year: 'numeric',\n      month: 'short'\n    },\n    dateA11yLabel: {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    },\n    monthYearA11yLabel: {\n      year: 'numeric',\n      month: 'long'\n    }\n  }\n};\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nlet NativeDateModule = /*#__PURE__*/(() => {\n  class NativeDateModule {}\n\n  NativeDateModule.ɵfac = function NativeDateModule_Factory(t) {\n    return new (t || NativeDateModule)();\n  };\n\n  NativeDateModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NativeDateModule\n  });\n  NativeDateModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [{\n      provide: DateAdapter,\n      useClass: NativeDateAdapter\n    }]\n  });\n  return NativeDateModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet MatNativeDateModule = /*#__PURE__*/(() => {\n  class MatNativeDateModule {}\n\n  MatNativeDateModule.ɵfac = function MatNativeDateModule_Factory(t) {\n    return new (t || MatNativeDateModule)();\n  };\n\n  MatNativeDateModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatNativeDateModule\n  });\n  MatNativeDateModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [{\n      provide: MAT_DATE_FORMATS,\n      useValue: MAT_NATIVE_DATE_FORMATS\n    }],\n    imports: [NativeDateModule]\n  });\n  return MatNativeDateModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Error state matcher that matches when a control is invalid and dirty. */\n\n\nlet ShowOnDirtyErrorStateMatcher = /*#__PURE__*/(() => {\n  class ShowOnDirtyErrorStateMatcher {\n    isErrorState(control, form) {\n      return !!(control && control.invalid && (control.dirty || form && form.submitted));\n    }\n\n  }\n\n  ShowOnDirtyErrorStateMatcher.ɵfac = function ShowOnDirtyErrorStateMatcher_Factory(t) {\n    return new (t || ShowOnDirtyErrorStateMatcher)();\n  };\n\n  ShowOnDirtyErrorStateMatcher.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ShowOnDirtyErrorStateMatcher,\n    factory: ShowOnDirtyErrorStateMatcher.ɵfac\n  });\n  return ShowOnDirtyErrorStateMatcher;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Provider that defines how form controls behave with regards to displaying error messages. */\n\n\nlet ErrorStateMatcher = /*#__PURE__*/(() => {\n  class ErrorStateMatcher {\n    isErrorState(control, form) {\n      return !!(control && control.invalid && (control.touched || form && form.submitted));\n    }\n\n  }\n\n  ErrorStateMatcher.ɵfac = function ErrorStateMatcher_Factory(t) {\n    return new (t || ErrorStateMatcher)();\n  };\n\n  ErrorStateMatcher.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ErrorStateMatcher,\n    factory: ErrorStateMatcher.ɵfac,\n    providedIn: 'root'\n  });\n  return ErrorStateMatcher;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Shared directive to count lines inside a text area, such as a list item.\n * Line elements can be extracted with a @ContentChildren(MatLine) query, then\n * counted by checking the query list's length.\n */\n\n\nlet MatLine = /*#__PURE__*/(() => {\n  class MatLine {}\n\n  MatLine.ɵfac = function MatLine_Factory(t) {\n    return new (t || MatLine)();\n  };\n\n  MatLine.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatLine,\n    selectors: [[\"\", \"mat-line\", \"\"], [\"\", \"matLine\", \"\"]],\n    hostAttrs: [1, \"mat-line\"]\n  });\n  return MatLine;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Helper that takes a query list of lines and sets the correct class on the host.\n * @docs-private\n */\n\n\nfunction setLines(lines, element, prefix = 'mat') {\n  // Note: doesn't need to unsubscribe, because `changes`\n  // gets completed by Angular when the view is destroyed.\n  lines.changes.pipe(startWith(lines)).subscribe(({\n    length\n  }) => {\n    setClass(element, `${prefix}-2-line`, false);\n    setClass(element, `${prefix}-3-line`, false);\n    setClass(element, `${prefix}-multi-line`, false);\n\n    if (length === 2 || length === 3) {\n      setClass(element, `${prefix}-${length}-line`, true);\n    } else if (length > 3) {\n      setClass(element, `${prefix}-multi-line`, true);\n    }\n  });\n}\n/** Adds or removes a class from an element. */\n\n\nfunction setClass(element, className, isAdd) {\n  element.nativeElement.classList.toggle(className, isAdd);\n}\n\nlet MatLineModule = /*#__PURE__*/(() => {\n  class MatLineModule {}\n\n  MatLineModule.ɵfac = function MatLineModule_Factory(t) {\n    return new (t || MatLineModule)();\n  };\n\n  MatLineModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatLineModule\n  });\n  MatLineModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule, MatCommonModule]\n  });\n  return MatLineModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Reference to a previously launched ripple element.\n */\n\n\nclass RippleRef {\n  constructor(_renderer,\n  /** Reference to the ripple HTML element. */\n  element,\n  /** Ripple configuration used for the ripple. */\n  config,\n  /* Whether animations are forcibly disabled for ripples through CSS. */\n  _animationForciblyDisabledThroughCss = false) {\n    this._renderer = _renderer;\n    this.element = element;\n    this.config = config;\n    this._animationForciblyDisabledThroughCss = _animationForciblyDisabledThroughCss;\n    /** Current state of the ripple. */\n\n    this.state = 3\n    /* RippleState.HIDDEN */\n    ;\n  }\n  /** Fades out the ripple element. */\n\n\n  fadeOut() {\n    this._renderer.fadeOutRipple(this);\n  }\n\n} // TODO: import these values from `@material/ripple` eventually.\n\n/**\n * Default ripple animation configuration for ripples without an explicit\n * animation config specified.\n */\n\n\nconst defaultRippleAnimationConfig = {\n  enterDuration: 225,\n  exitDuration: 150\n};\n/**\n * Timeout for ignoring mouse events. Mouse events will be temporary ignored after touch\n * events to avoid synthetic mouse events.\n */\n\nconst ignoreMouseEventsTimeout = 800;\n/** Options that apply to all the event listeners that are bound by the ripple renderer. */\n\nconst passiveEventOptions = /*#__PURE__*/normalizePassiveListenerOptions({\n  passive: true\n});\n/** Events that signal that the pointer is down. */\n\nconst pointerDownEvents = ['mousedown', 'touchstart'];\n/** Events that signal that the pointer is up. */\n\nconst pointerUpEvents = ['mouseup', 'mouseleave', 'touchend', 'touchcancel'];\n/**\n * Helper service that performs DOM manipulations. Not intended to be used outside this module.\n * The constructor takes a reference to the ripple directive's host element and a map of DOM\n * event handlers to be installed on the element that triggers ripple animations.\n * This will eventually become a custom renderer once Angular support exists.\n * @docs-private\n */\n\nclass RippleRenderer {\n  constructor(_target, _ngZone, elementOrElementRef, platform) {\n    this._target = _target;\n    this._ngZone = _ngZone;\n    /** Whether the pointer is currently down or not. */\n\n    this._isPointerDown = false;\n    /**\n     * Map of currently active ripple references.\n     * The ripple reference is mapped to its element event listeners.\n     * The reason why `| null` is used is that event listeners are added only\n     * when the condition is truthy (see the `_startFadeOutTransition` method).\n     */\n\n    this._activeRipples = new Map();\n    /** Whether pointer-up event listeners have been registered. */\n\n    this._pointerUpEventsRegistered = false; // Only do anything if we're on the browser.\n\n    if (platform.isBrowser) {\n      this._containerElement = coerceElement(elementOrElementRef);\n    }\n  }\n  /**\n   * Fades in a ripple at the given coordinates.\n   * @param x Coordinate within the element, along the X axis at which to start the ripple.\n   * @param y Coordinate within the element, along the Y axis at which to start the ripple.\n   * @param config Extra ripple options.\n   */\n\n\n  fadeInRipple(x, y, config = {}) {\n    const containerRect = this._containerRect = this._containerRect || this._containerElement.getBoundingClientRect();\n\n    const animationConfig = { ...defaultRippleAnimationConfig,\n      ...config.animation\n    };\n\n    if (config.centered) {\n      x = containerRect.left + containerRect.width / 2;\n      y = containerRect.top + containerRect.height / 2;\n    }\n\n    const radius = config.radius || distanceToFurthestCorner(x, y, containerRect);\n    const offsetX = x - containerRect.left;\n    const offsetY = y - containerRect.top;\n    const enterDuration = animationConfig.enterDuration;\n    const ripple = document.createElement('div');\n    ripple.classList.add('mat-ripple-element');\n    ripple.style.left = `${offsetX - radius}px`;\n    ripple.style.top = `${offsetY - radius}px`;\n    ripple.style.height = `${radius * 2}px`;\n    ripple.style.width = `${radius * 2}px`; // If a custom color has been specified, set it as inline style. If no color is\n    // set, the default color will be applied through the ripple theme styles.\n\n    if (config.color != null) {\n      ripple.style.backgroundColor = config.color;\n    }\n\n    ripple.style.transitionDuration = `${enterDuration}ms`;\n\n    this._containerElement.appendChild(ripple); // By default the browser does not recalculate the styles of dynamically created\n    // ripple elements. This is critical to ensure that the `scale` animates properly.\n    // We enforce a style recalculation by calling `getComputedStyle` and *accessing* a property.\n    // See: https://gist.github.com/paulirish/5d52fb081b3570c81e3a\n\n\n    const computedStyles = window.getComputedStyle(ripple);\n    const userTransitionProperty = computedStyles.transitionProperty;\n    const userTransitionDuration = computedStyles.transitionDuration; // Note: We detect whether animation is forcibly disabled through CSS by the use of\n    // `transition: none`. This is technically unexpected since animations are controlled\n    // through the animation config, but this exists for backwards compatibility. This logic does\n    // not need to be super accurate since it covers some edge cases which can be easily avoided by users.\n\n    const animationForciblyDisabledThroughCss = userTransitionProperty === 'none' || // Note: The canonical unit for serialized CSS `<time>` properties is seconds. Additionally\n    // some browsers expand the duration for every property (in our case `opacity` and `transform`).\n    userTransitionDuration === '0s' || userTransitionDuration === '0s, 0s'; // Exposed reference to the ripple that will be returned.\n\n    const rippleRef = new RippleRef(this, ripple, config, animationForciblyDisabledThroughCss); // Start the enter animation by setting the transform/scale to 100%. The animation will\n    // execute as part of this statement because we forced a style recalculation before.\n    // Note: We use a 3d transform here in order to avoid an issue in Safari where\n    // the ripples aren't clipped when inside the shadow DOM (see #24028).\n\n    ripple.style.transform = 'scale3d(1, 1, 1)';\n    rippleRef.state = 0\n    /* RippleState.FADING_IN */\n    ;\n\n    if (!config.persistent) {\n      this._mostRecentTransientRipple = rippleRef;\n    }\n\n    let eventListeners = null; // Do not register the `transition` event listener if fade-in and fade-out duration\n    // are set to zero. The events won't fire anyway and we can save resources here.\n\n    if (!animationForciblyDisabledThroughCss && (enterDuration || animationConfig.exitDuration)) {\n      this._ngZone.runOutsideAngular(() => {\n        const onTransitionEnd = () => this._finishRippleTransition(rippleRef);\n\n        const onTransitionCancel = () => this._destroyRipple(rippleRef);\n\n        ripple.addEventListener('transitionend', onTransitionEnd); // If the transition is cancelled (e.g. due to DOM removal), we destroy the ripple\n        // directly as otherwise we would keep it part of the ripple container forever.\n        // https://www.w3.org/TR/css-transitions-1/#:~:text=no%20longer%20in%20the%20document.\n\n        ripple.addEventListener('transitioncancel', onTransitionCancel);\n        eventListeners = {\n          onTransitionEnd,\n          onTransitionCancel\n        };\n      });\n    } // Add the ripple reference to the list of all active ripples.\n\n\n    this._activeRipples.set(rippleRef, eventListeners); // In case there is no fade-in transition duration, we need to manually call the transition\n    // end listener because `transitionend` doesn't fire if there is no transition.\n\n\n    if (animationForciblyDisabledThroughCss || !enterDuration) {\n      this._finishRippleTransition(rippleRef);\n    }\n\n    return rippleRef;\n  }\n  /** Fades out a ripple reference. */\n\n\n  fadeOutRipple(rippleRef) {\n    // For ripples already fading out or hidden, this should be a noop.\n    if (rippleRef.state === 2\n    /* RippleState.FADING_OUT */\n    || rippleRef.state === 3\n    /* RippleState.HIDDEN */\n    ) {\n      return;\n    }\n\n    const rippleEl = rippleRef.element;\n    const animationConfig = { ...defaultRippleAnimationConfig,\n      ...rippleRef.config.animation\n    }; // This starts the fade-out transition and will fire the transition end listener that\n    // removes the ripple element from the DOM.\n\n    rippleEl.style.transitionDuration = `${animationConfig.exitDuration}ms`;\n    rippleEl.style.opacity = '0';\n    rippleRef.state = 2\n    /* RippleState.FADING_OUT */\n    ; // In case there is no fade-out transition duration, we need to manually call the\n    // transition end listener because `transitionend` doesn't fire if there is no transition.\n\n    if (rippleRef._animationForciblyDisabledThroughCss || !animationConfig.exitDuration) {\n      this._finishRippleTransition(rippleRef);\n    }\n  }\n  /** Fades out all currently active ripples. */\n\n\n  fadeOutAll() {\n    this._getActiveRipples().forEach(ripple => ripple.fadeOut());\n  }\n  /** Fades out all currently active non-persistent ripples. */\n\n\n  fadeOutAllNonPersistent() {\n    this._getActiveRipples().forEach(ripple => {\n      if (!ripple.config.persistent) {\n        ripple.fadeOut();\n      }\n    });\n  }\n  /** Sets up the trigger event listeners */\n\n\n  setupTriggerEvents(elementOrElementRef) {\n    const element = coerceElement(elementOrElementRef);\n\n    if (!element || element === this._triggerElement) {\n      return;\n    } // Remove all previously registered event listeners from the trigger element.\n\n\n    this._removeTriggerEvents();\n\n    this._triggerElement = element;\n\n    this._registerEvents(pointerDownEvents);\n  }\n  /**\n   * Handles all registered events.\n   * @docs-private\n   */\n\n\n  handleEvent(event) {\n    if (event.type === 'mousedown') {\n      this._onMousedown(event);\n    } else if (event.type === 'touchstart') {\n      this._onTouchStart(event);\n    } else {\n      this._onPointerUp();\n    } // If pointer-up events haven't been registered yet, do so now.\n    // We do this on-demand in order to reduce the total number of event listeners\n    // registered by the ripples, which speeds up the rendering time for large UIs.\n\n\n    if (!this._pointerUpEventsRegistered) {\n      this._registerEvents(pointerUpEvents);\n\n      this._pointerUpEventsRegistered = true;\n    }\n  }\n  /** Method that will be called if the fade-in or fade-in transition completed. */\n\n\n  _finishRippleTransition(rippleRef) {\n    if (rippleRef.state === 0\n    /* RippleState.FADING_IN */\n    ) {\n      this._startFadeOutTransition(rippleRef);\n    } else if (rippleRef.state === 2\n    /* RippleState.FADING_OUT */\n    ) {\n      this._destroyRipple(rippleRef);\n    }\n  }\n  /**\n   * Starts the fade-out transition of the given ripple if it's not persistent and the pointer\n   * is not held down anymore.\n   */\n\n\n  _startFadeOutTransition(rippleRef) {\n    const isMostRecentTransientRipple = rippleRef === this._mostRecentTransientRipple;\n    const {\n      persistent\n    } = rippleRef.config;\n    rippleRef.state = 1\n    /* RippleState.VISIBLE */\n    ; // When the timer runs out while the user has kept their pointer down, we want to\n    // keep only the persistent ripples and the latest transient ripple. We do this,\n    // because we don't want stacked transient ripples to appear after their enter\n    // animation has finished.\n\n    if (!persistent && (!isMostRecentTransientRipple || !this._isPointerDown)) {\n      rippleRef.fadeOut();\n    }\n  }\n  /** Destroys the given ripple by removing it from the DOM and updating its state. */\n\n\n  _destroyRipple(rippleRef) {\n    const eventListeners = this._activeRipples.get(rippleRef) ?? null;\n\n    this._activeRipples.delete(rippleRef); // Clear out the cached bounding rect if we have no more ripples.\n\n\n    if (!this._activeRipples.size) {\n      this._containerRect = null;\n    } // If the current ref is the most recent transient ripple, unset it\n    // avoid memory leaks.\n\n\n    if (rippleRef === this._mostRecentTransientRipple) {\n      this._mostRecentTransientRipple = null;\n    }\n\n    rippleRef.state = 3\n    /* RippleState.HIDDEN */\n    ;\n\n    if (eventListeners !== null) {\n      rippleRef.element.removeEventListener('transitionend', eventListeners.onTransitionEnd);\n      rippleRef.element.removeEventListener('transitioncancel', eventListeners.onTransitionCancel);\n    }\n\n    rippleRef.element.remove();\n  }\n  /** Function being called whenever the trigger is being pressed using mouse. */\n\n\n  _onMousedown(event) {\n    // Screen readers will fire fake mouse events for space/enter. Skip launching a\n    // ripple in this case for consistency with the non-screen-reader experience.\n    const isFakeMousedown = isFakeMousedownFromScreenReader(event);\n    const isSyntheticEvent = this._lastTouchStartEvent && Date.now() < this._lastTouchStartEvent + ignoreMouseEventsTimeout;\n\n    if (!this._target.rippleDisabled && !isFakeMousedown && !isSyntheticEvent) {\n      this._isPointerDown = true;\n      this.fadeInRipple(event.clientX, event.clientY, this._target.rippleConfig);\n    }\n  }\n  /** Function being called whenever the trigger is being pressed using touch. */\n\n\n  _onTouchStart(event) {\n    if (!this._target.rippleDisabled && !isFakeTouchstartFromScreenReader(event)) {\n      // Some browsers fire mouse events after a `touchstart` event. Those synthetic mouse\n      // events will launch a second ripple if we don't ignore mouse events for a specific\n      // time after a touchstart event.\n      this._lastTouchStartEvent = Date.now();\n      this._isPointerDown = true; // Use `changedTouches` so we skip any touches where the user put\n      // their finger down, but used another finger to tap the element again.\n\n      const touches = event.changedTouches;\n\n      for (let i = 0; i < touches.length; i++) {\n        this.fadeInRipple(touches[i].clientX, touches[i].clientY, this._target.rippleConfig);\n      }\n    }\n  }\n  /** Function being called whenever the trigger is being released. */\n\n\n  _onPointerUp() {\n    if (!this._isPointerDown) {\n      return;\n    }\n\n    this._isPointerDown = false; // Fade-out all ripples that are visible and not persistent.\n\n    this._getActiveRipples().forEach(ripple => {\n      // By default, only ripples that are completely visible will fade out on pointer release.\n      // If the `terminateOnPointerUp` option is set, ripples that still fade in will also fade out.\n      const isVisible = ripple.state === 1\n      /* RippleState.VISIBLE */\n      || ripple.config.terminateOnPointerUp && ripple.state === 0\n      /* RippleState.FADING_IN */\n      ;\n\n      if (!ripple.config.persistent && isVisible) {\n        ripple.fadeOut();\n      }\n    });\n  }\n  /** Registers event listeners for a given list of events. */\n\n\n  _registerEvents(eventTypes) {\n    this._ngZone.runOutsideAngular(() => {\n      eventTypes.forEach(type => {\n        this._triggerElement.addEventListener(type, this, passiveEventOptions);\n      });\n    });\n  }\n\n  _getActiveRipples() {\n    return Array.from(this._activeRipples.keys());\n  }\n  /** Removes previously registered event listeners from the trigger element. */\n\n\n  _removeTriggerEvents() {\n    if (this._triggerElement) {\n      pointerDownEvents.forEach(type => {\n        this._triggerElement.removeEventListener(type, this, passiveEventOptions);\n      });\n\n      if (this._pointerUpEventsRegistered) {\n        pointerUpEvents.forEach(type => {\n          this._triggerElement.removeEventListener(type, this, passiveEventOptions);\n        });\n      }\n    }\n  }\n\n}\n/**\n * Returns the distance from the point (x, y) to the furthest corner of a rectangle.\n */\n\n\nfunction distanceToFurthestCorner(x, y, rect) {\n  const distX = Math.max(Math.abs(x - rect.left), Math.abs(x - rect.right));\n  const distY = Math.max(Math.abs(y - rect.top), Math.abs(y - rect.bottom));\n  return Math.sqrt(distX * distX + distY * distY);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Injection token that can be used to specify the global ripple options. */\n\n\nconst MAT_RIPPLE_GLOBAL_OPTIONS = /*#__PURE__*/new InjectionToken('mat-ripple-global-options');\nlet MatRipple = /*#__PURE__*/(() => {\n  class MatRipple {\n    constructor(_elementRef, ngZone, platform, globalOptions, _animationMode) {\n      this._elementRef = _elementRef;\n      this._animationMode = _animationMode;\n      /**\n       * If set, the radius in pixels of foreground ripples when fully expanded. If unset, the radius\n       * will be the distance from the center of the ripple to the furthest corner of the host element's\n       * bounding rectangle.\n       */\n\n      this.radius = 0;\n      this._disabled = false;\n      /** Whether ripple directive is initialized and the input bindings are set. */\n\n      this._isInitialized = false;\n      this._globalOptions = globalOptions || {};\n      this._rippleRenderer = new RippleRenderer(this, ngZone, _elementRef, platform);\n    }\n    /**\n     * Whether click events will not trigger the ripple. Ripples can be still launched manually\n     * by using the `launch()` method.\n     */\n\n\n    get disabled() {\n      return this._disabled;\n    }\n\n    set disabled(value) {\n      if (value) {\n        this.fadeOutAllNonPersistent();\n      }\n\n      this._disabled = value;\n\n      this._setupTriggerEventsIfEnabled();\n    }\n    /**\n     * The element that triggers the ripple when click events are received.\n     * Defaults to the directive's host element.\n     */\n\n\n    get trigger() {\n      return this._trigger || this._elementRef.nativeElement;\n    }\n\n    set trigger(trigger) {\n      this._trigger = trigger;\n\n      this._setupTriggerEventsIfEnabled();\n    }\n\n    ngOnInit() {\n      this._isInitialized = true;\n\n      this._setupTriggerEventsIfEnabled();\n    }\n\n    ngOnDestroy() {\n      this._rippleRenderer._removeTriggerEvents();\n    }\n    /** Fades out all currently showing ripple elements. */\n\n\n    fadeOutAll() {\n      this._rippleRenderer.fadeOutAll();\n    }\n    /** Fades out all currently showing non-persistent ripple elements. */\n\n\n    fadeOutAllNonPersistent() {\n      this._rippleRenderer.fadeOutAllNonPersistent();\n    }\n    /**\n     * Ripple configuration from the directive's input values.\n     * @docs-private Implemented as part of RippleTarget\n     */\n\n\n    get rippleConfig() {\n      return {\n        centered: this.centered,\n        radius: this.radius,\n        color: this.color,\n        animation: { ...this._globalOptions.animation,\n          ...(this._animationMode === 'NoopAnimations' ? {\n            enterDuration: 0,\n            exitDuration: 0\n          } : {}),\n          ...this.animation\n        },\n        terminateOnPointerUp: this._globalOptions.terminateOnPointerUp\n      };\n    }\n    /**\n     * Whether ripples on pointer-down are disabled or not.\n     * @docs-private Implemented as part of RippleTarget\n     */\n\n\n    get rippleDisabled() {\n      return this.disabled || !!this._globalOptions.disabled;\n    }\n    /** Sets up the trigger event listeners if ripples are enabled. */\n\n\n    _setupTriggerEventsIfEnabled() {\n      if (!this.disabled && this._isInitialized) {\n        this._rippleRenderer.setupTriggerEvents(this.trigger);\n      }\n    }\n    /** Launches a manual ripple at the specified coordinated or just by the ripple config. */\n\n\n    launch(configOrX, y = 0, config) {\n      if (typeof configOrX === 'number') {\n        return this._rippleRenderer.fadeInRipple(configOrX, y, { ...this.rippleConfig,\n          ...config\n        });\n      } else {\n        return this._rippleRenderer.fadeInRipple(0, 0, { ...this.rippleConfig,\n          ...configOrX\n        });\n      }\n    }\n\n  }\n\n  MatRipple.ɵfac = function MatRipple_Factory(t) {\n    return new (t || MatRipple)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1$1.Platform), i0.ɵɵdirectiveInject(MAT_RIPPLE_GLOBAL_OPTIONS, 8), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n  };\n\n  MatRipple.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatRipple,\n    selectors: [[\"\", \"mat-ripple\", \"\"], [\"\", \"matRipple\", \"\"]],\n    hostAttrs: [1, \"mat-ripple\"],\n    hostVars: 2,\n    hostBindings: function MatRipple_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-ripple-unbounded\", ctx.unbounded);\n      }\n    },\n    inputs: {\n      color: [\"matRippleColor\", \"color\"],\n      unbounded: [\"matRippleUnbounded\", \"unbounded\"],\n      centered: [\"matRippleCentered\", \"centered\"],\n      radius: [\"matRippleRadius\", \"radius\"],\n      animation: [\"matRippleAnimation\", \"animation\"],\n      disabled: [\"matRippleDisabled\", \"disabled\"],\n      trigger: [\"matRippleTrigger\", \"trigger\"]\n    },\n    exportAs: [\"matRipple\"]\n  });\n  return MatRipple;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nlet MatRippleModule = /*#__PURE__*/(() => {\n  class MatRippleModule {}\n\n  MatRippleModule.ɵfac = function MatRippleModule_Factory(t) {\n    return new (t || MatRippleModule)();\n  };\n\n  MatRippleModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatRippleModule\n  });\n  MatRippleModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule, MatCommonModule]\n  });\n  return MatRippleModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Component that shows a simplified checkbox without including any kind of \"real\" checkbox.\n * Meant to be used when the checkbox is purely decorative and a large number of them will be\n * included, such as for the options in a multi-select. Uses no SVGs or complex animations.\n * Note that theming is meant to be handled by the parent element, e.g.\n * `mat-primary .mat-pseudo-checkbox`.\n *\n * Note that this component will be completely invisible to screen-reader users. This is *not*\n * interchangeable with `<mat-checkbox>` and should *not* be used if the user would directly\n * interact with the checkbox. The pseudo-checkbox should only be used as an implementation detail\n * of more complex components that appropriately handle selected / checked state.\n * @docs-private\n */\n\n\nlet MatPseudoCheckbox = /*#__PURE__*/(() => {\n  class MatPseudoCheckbox {\n    constructor(_animationMode) {\n      this._animationMode = _animationMode;\n      /** Display state of the checkbox. */\n\n      this.state = 'unchecked';\n      /** Whether the checkbox is disabled. */\n\n      this.disabled = false;\n    }\n\n  }\n\n  MatPseudoCheckbox.ɵfac = function MatPseudoCheckbox_Factory(t) {\n    return new (t || MatPseudoCheckbox)(i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n  };\n\n  MatPseudoCheckbox.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatPseudoCheckbox,\n    selectors: [[\"mat-pseudo-checkbox\"]],\n    hostAttrs: [1, \"mat-pseudo-checkbox\"],\n    hostVars: 8,\n    hostBindings: function MatPseudoCheckbox_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-pseudo-checkbox-indeterminate\", ctx.state === \"indeterminate\")(\"mat-pseudo-checkbox-checked\", ctx.state === \"checked\")(\"mat-pseudo-checkbox-disabled\", ctx.disabled)(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n      }\n    },\n    inputs: {\n      state: \"state\",\n      disabled: \"disabled\"\n    },\n    decls: 0,\n    vars: 0,\n    template: function MatPseudoCheckbox_Template(rf, ctx) {},\n    styles: [\".mat-pseudo-checkbox{width:16px;height:16px;border:2px solid;border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:\\\"\\\";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox.mat-pseudo-checkbox-indeterminate{border-color:rgba(0,0,0,0)}.mat-pseudo-checkbox._mat-animation-noopable{transition:none !important;animation:none !important}.mat-pseudo-checkbox._mat-animation-noopable::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{top:5px;left:1px;width:10px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{top:2.4px;left:1px;width:8px;height:3px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return MatPseudoCheckbox;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nlet MatPseudoCheckboxModule = /*#__PURE__*/(() => {\n  class MatPseudoCheckboxModule {}\n\n  MatPseudoCheckboxModule.ɵfac = function MatPseudoCheckboxModule_Factory(t) {\n    return new (t || MatPseudoCheckboxModule)();\n  };\n\n  MatPseudoCheckboxModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatPseudoCheckboxModule\n  });\n  MatPseudoCheckboxModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule]\n  });\n  return MatPseudoCheckboxModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Injection token used to provide the parent component to options.\n */\n\n\nconst MAT_OPTION_PARENT_COMPONENT = /*#__PURE__*/new InjectionToken('MAT_OPTION_PARENT_COMPONENT');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Notes on the accessibility pattern used for `mat-optgroup`.\n// The option group has two different \"modes\": regular and inert. The regular mode uses the\n// recommended a11y pattern which has `role=\"group\"` on the group element with `aria-labelledby`\n// pointing to the label. This works for `mat-select`, but it seems to hit a bug for autocomplete\n// under VoiceOver where the group doesn't get read out at all. The bug appears to be that if\n// there's __any__ a11y-related attribute on the group (e.g. `role` or `aria-labelledby`),\n// VoiceOver on Safari won't read it out.\n// We've introduced the `inert` mode as a workaround. Under this mode, all a11y attributes are\n// removed from the group, and we get the screen reader to read out the group label by mirroring it\n// inside an invisible element in the option. This is sub-optimal, because the screen reader will\n// repeat the group label on each navigation, whereas the default pattern only reads the group when\n// the user enters a new group. The following alternate approaches were considered:\n// 1. Reading out the group label using the `LiveAnnouncer` solves the problem, but we can't control\n//    when the text will be read out so sometimes it comes in too late or never if the user\n//    navigates quickly.\n// 2. `<mat-option aria-describedby=\"groupLabel\"` - This works on Safari, but VoiceOver in Chrome\n//    won't read out the description at all.\n// 3. `<mat-option aria-labelledby=\"optionLabel groupLabel\"` - This works on Chrome, but Safari\n//     doesn't read out the text at all. Furthermore, on\n// Boilerplate for applying mixins to MatOptgroup.\n\n/** @docs-private */\n\nconst _MatOptgroupMixinBase = /*#__PURE__*/mixinDisabled(class {}); // Counter for unique group ids.\n\n\nlet _uniqueOptgroupIdCounter = 0;\n\nlet _MatOptgroupBase = /*#__PURE__*/(() => {\n  class _MatOptgroupBase extends _MatOptgroupMixinBase {\n    constructor(parent) {\n      super();\n      /** Unique id for the underlying label. */\n\n      this._labelId = `mat-optgroup-label-${_uniqueOptgroupIdCounter++}`;\n      this._inert = parent?.inertGroups ?? false;\n    }\n\n  }\n\n  _MatOptgroupBase.ɵfac = function _MatOptgroupBase_Factory(t) {\n    return new (t || _MatOptgroupBase)(i0.ɵɵdirectiveInject(MAT_OPTION_PARENT_COMPONENT, 8));\n  };\n\n  _MatOptgroupBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: _MatOptgroupBase,\n    inputs: {\n      label: \"label\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n  return _MatOptgroupBase;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Injection token that can be used to reference instances of `MatOptgroup`. It serves as\n * alternative token to the actual `MatOptgroup` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\n\n\nconst MAT_OPTGROUP = /*#__PURE__*/new InjectionToken('MatOptgroup');\n/**\n * Component that is used to group instances of `mat-option`.\n */\n\nlet MatOptgroup = /*#__PURE__*/(() => {\n  class MatOptgroup extends _MatOptgroupBase {}\n\n  MatOptgroup.ɵfac = /* @__PURE__ */function () {\n    let ɵMatOptgroup_BaseFactory;\n    return function MatOptgroup_Factory(t) {\n      return (ɵMatOptgroup_BaseFactory || (ɵMatOptgroup_BaseFactory = i0.ɵɵgetInheritedFactory(MatOptgroup)))(t || MatOptgroup);\n    };\n  }();\n\n  MatOptgroup.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatOptgroup,\n    selectors: [[\"mat-optgroup\"]],\n    hostAttrs: [1, \"mat-optgroup\"],\n    hostVars: 5,\n    hostBindings: function MatOptgroup_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"role\", ctx._inert ? null : \"group\")(\"aria-disabled\", ctx._inert ? null : ctx.disabled.toString())(\"aria-labelledby\", ctx._inert ? null : ctx._labelId);\n        i0.ɵɵclassProp(\"mat-optgroup-disabled\", ctx.disabled);\n      }\n    },\n    inputs: {\n      disabled: \"disabled\"\n    },\n    exportAs: [\"matOptgroup\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_OPTGROUP,\n      useExisting: MatOptgroup\n    }]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c1,\n    decls: 4,\n    vars: 2,\n    consts: [[\"aria-hidden\", \"true\", 1, \"mat-optgroup-label\", 3, \"id\"]],\n    template: function MatOptgroup_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c0);\n        i0.ɵɵelementStart(0, \"span\", 0);\n        i0.ɵɵtext(1);\n        i0.ɵɵprojection(2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵprojection(3, 1);\n      }\n\n      if (rf & 2) {\n        i0.ɵɵproperty(\"id\", ctx._labelId);\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate1(\"\", ctx.label, \" \");\n      }\n    },\n    styles: [\".mat-optgroup-label{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;line-height:48px;height:48px;padding:0 16px;text-align:left;text-decoration:none;max-width:100%;-webkit-user-select:none;user-select:none;cursor:default}.mat-optgroup-label[disabled]{cursor:default}[dir=rtl] .mat-optgroup-label{text-align:right}.mat-optgroup-label .mat-icon{margin-right:16px;vertical-align:middle}.mat-optgroup-label .mat-icon svg{vertical-align:top}[dir=rtl] .mat-optgroup-label .mat-icon{margin-left:16px;margin-right:0}\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return MatOptgroup;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Option IDs need to be unique across components, so this counter exists outside of\n * the component definition.\n */\n\n\nlet _uniqueIdCounter = 0;\n/** Event object emitted by MatOption when selected or deselected. */\n\nclass MatOptionSelectionChange {\n  constructor(\n  /** Reference to the option that emitted the event. */\n  source,\n  /** Whether the change in the option's value was a result of a user action. */\n  isUserInput = false) {\n    this.source = source;\n    this.isUserInput = isUserInput;\n  }\n\n}\n\nlet _MatOptionBase = /*#__PURE__*/(() => {\n  class _MatOptionBase {\n    constructor(_element, _changeDetectorRef, _parent, group) {\n      this._element = _element;\n      this._changeDetectorRef = _changeDetectorRef;\n      this._parent = _parent;\n      this.group = group;\n      this._selected = false;\n      this._active = false;\n      this._disabled = false;\n      this._mostRecentViewValue = '';\n      /** The unique ID of the option. */\n\n      this.id = `mat-option-${_uniqueIdCounter++}`;\n      /** Event emitted when the option is selected or deselected. */\n      // tslint:disable-next-line:no-output-on-prefix\n\n      this.onSelectionChange = new EventEmitter();\n      /** Emits when the state of the option changes and any parents have to be notified. */\n\n      this._stateChanges = new Subject();\n    }\n    /** Whether the wrapping component is in multiple selection mode. */\n\n\n    get multiple() {\n      return this._parent && this._parent.multiple;\n    }\n    /** Whether or not the option is currently selected. */\n\n\n    get selected() {\n      return this._selected;\n    }\n    /** Whether the option is disabled. */\n\n\n    get disabled() {\n      return this.group && this.group.disabled || this._disabled;\n    }\n\n    set disabled(value) {\n      this._disabled = coerceBooleanProperty(value);\n    }\n    /** Whether ripples for the option are disabled. */\n\n\n    get disableRipple() {\n      return !!(this._parent && this._parent.disableRipple);\n    }\n    /**\n     * Whether or not the option is currently active and ready to be selected.\n     * An active option displays styles as if it is focused, but the\n     * focus is actually retained somewhere else. This comes in handy\n     * for components like autocomplete where focus must remain on the input.\n     */\n\n\n    get active() {\n      return this._active;\n    }\n    /**\n     * The displayed value of the option. It is necessary to show the selected option in the\n     * select's trigger.\n     */\n\n\n    get viewValue() {\n      // TODO(kara): Add input property alternative for node envs.\n      return (this._getHostElement().textContent || '').trim();\n    }\n    /** Selects the option. */\n\n\n    select() {\n      if (!this._selected) {\n        this._selected = true;\n\n        this._changeDetectorRef.markForCheck();\n\n        this._emitSelectionChangeEvent();\n      }\n    }\n    /** Deselects the option. */\n\n\n    deselect() {\n      if (this._selected) {\n        this._selected = false;\n\n        this._changeDetectorRef.markForCheck();\n\n        this._emitSelectionChangeEvent();\n      }\n    }\n    /** Sets focus onto this option. */\n\n\n    focus(_origin, options) {\n      // Note that we aren't using `_origin`, but we need to keep it because some internal consumers\n      // use `MatOption` in a `FocusKeyManager` and we need it to match `FocusableOption`.\n      const element = this._getHostElement();\n\n      if (typeof element.focus === 'function') {\n        element.focus(options);\n      }\n    }\n    /**\n     * This method sets display styles on the option to make it appear\n     * active. This is used by the ActiveDescendantKeyManager so key\n     * events will display the proper options as active on arrow key events.\n     */\n\n\n    setActiveStyles() {\n      if (!this._active) {\n        this._active = true;\n\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n    /**\n     * This method removes display styles on the option that made it appear\n     * active. This is used by the ActiveDescendantKeyManager so key\n     * events will display the proper options as active on arrow key events.\n     */\n\n\n    setInactiveStyles() {\n      if (this._active) {\n        this._active = false;\n\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n    /** Gets the label to be used when determining whether the option should be focused. */\n\n\n    getLabel() {\n      return this.viewValue;\n    }\n    /** Ensures the option is selected when activated from the keyboard. */\n\n\n    _handleKeydown(event) {\n      if ((event.keyCode === ENTER || event.keyCode === SPACE) && !hasModifierKey(event)) {\n        this._selectViaInteraction(); // Prevent the page from scrolling down and form submits.\n\n\n        event.preventDefault();\n      }\n    }\n    /**\n     * `Selects the option while indicating the selection came from the user. Used to\n     * determine if the select's view -> model callback should be invoked.`\n     */\n\n\n    _selectViaInteraction() {\n      if (!this.disabled) {\n        this._selected = this.multiple ? !this._selected : true;\n\n        this._changeDetectorRef.markForCheck();\n\n        this._emitSelectionChangeEvent(true);\n      }\n    }\n    /**\n     * Gets the `aria-selected` value for the option. We explicitly omit the `aria-selected`\n     * attribute from single-selection, unselected options. Including the `aria-selected=\"false\"`\n     * attributes adds a significant amount of noise to screen-reader users without providing useful\n     * information.\n     */\n\n\n    _getAriaSelected() {\n      return this.selected || (this.multiple ? false : null);\n    }\n    /** Returns the correct tabindex for the option depending on disabled state. */\n\n\n    _getTabIndex() {\n      return this.disabled ? '-1' : '0';\n    }\n    /** Gets the host DOM element. */\n\n\n    _getHostElement() {\n      return this._element.nativeElement;\n    }\n\n    ngAfterViewChecked() {\n      // Since parent components could be using the option's label to display the selected values\n      // (e.g. `mat-select`) and they don't have a way of knowing if the option's label has changed\n      // we have to check for changes in the DOM ourselves and dispatch an event. These checks are\n      // relatively cheap, however we still limit them only to selected options in order to avoid\n      // hitting the DOM too often.\n      if (this._selected) {\n        const viewValue = this.viewValue;\n\n        if (viewValue !== this._mostRecentViewValue) {\n          this._mostRecentViewValue = viewValue;\n\n          this._stateChanges.next();\n        }\n      }\n    }\n\n    ngOnDestroy() {\n      this._stateChanges.complete();\n    }\n    /** Emits the selection change event. */\n\n\n    _emitSelectionChangeEvent(isUserInput = false) {\n      this.onSelectionChange.emit(new MatOptionSelectionChange(this, isUserInput));\n    }\n\n  }\n\n  _MatOptionBase.ɵfac = function _MatOptionBase_Factory(t) {\n    i0.ɵɵinvalidFactory();\n  };\n\n  _MatOptionBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: _MatOptionBase,\n    inputs: {\n      value: \"value\",\n      id: \"id\",\n      disabled: \"disabled\"\n    },\n    outputs: {\n      onSelectionChange: \"onSelectionChange\"\n    }\n  });\n  return _MatOptionBase;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Single option inside of a `<mat-select>` element.\n */\n\n\nlet MatOption = /*#__PURE__*/(() => {\n  class MatOption extends _MatOptionBase {\n    constructor(element, changeDetectorRef, parent, group) {\n      super(element, changeDetectorRef, parent, group);\n    }\n\n  }\n\n  MatOption.ɵfac = function MatOption_Factory(t) {\n    return new (t || MatOption)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_OPTION_PARENT_COMPONENT, 8), i0.ɵɵdirectiveInject(MAT_OPTGROUP, 8));\n  };\n\n  MatOption.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatOption,\n    selectors: [[\"mat-option\"]],\n    hostAttrs: [\"role\", \"option\", 1, \"mat-option\", \"mat-focus-indicator\"],\n    hostVars: 12,\n    hostBindings: function MatOption_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function MatOption_click_HostBindingHandler() {\n          return ctx._selectViaInteraction();\n        })(\"keydown\", function MatOption_keydown_HostBindingHandler($event) {\n          return ctx._handleKeydown($event);\n        });\n      }\n\n      if (rf & 2) {\n        i0.ɵɵhostProperty(\"id\", ctx.id);\n        i0.ɵɵattribute(\"tabindex\", ctx._getTabIndex())(\"aria-selected\", ctx._getAriaSelected())(\"aria-disabled\", ctx.disabled.toString());\n        i0.ɵɵclassProp(\"mat-selected\", ctx.selected)(\"mat-option-multiple\", ctx.multiple)(\"mat-active\", ctx.active)(\"mat-option-disabled\", ctx.disabled);\n      }\n    },\n    exportAs: [\"matOption\"],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c2,\n    decls: 5,\n    vars: 4,\n    consts: [[\"class\", \"mat-option-pseudo-checkbox\", 3, \"state\", \"disabled\", 4, \"ngIf\"], [1, \"mat-option-text\"], [\"class\", \"cdk-visually-hidden\", 4, \"ngIf\"], [\"mat-ripple\", \"\", 1, \"mat-option-ripple\", 3, \"matRippleTrigger\", \"matRippleDisabled\"], [1, \"mat-option-pseudo-checkbox\", 3, \"state\", \"disabled\"], [1, \"cdk-visually-hidden\"]],\n    template: function MatOption_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵtemplate(0, MatOption_mat_pseudo_checkbox_0_Template, 1, 2, \"mat-pseudo-checkbox\", 0);\n        i0.ɵɵelementStart(1, \"span\", 1);\n        i0.ɵɵprojection(2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(3, MatOption_span_3_Template, 2, 1, \"span\", 2);\n        i0.ɵɵelement(4, \"div\", 3);\n      }\n\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.multiple);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.group && ctx.group._inert);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"matRippleTrigger\", ctx._getHostElement())(\"matRippleDisabled\", ctx.disabled || ctx.disableRipple);\n      }\n    },\n    dependencies: [MatRipple, i3.NgIf, MatPseudoCheckbox],\n    styles: [\".mat-option{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;line-height:48px;height:48px;padding:0 16px;text-align:left;text-decoration:none;max-width:100%;position:relative;cursor:pointer;outline:none;display:flex;flex-direction:row;max-width:100%;box-sizing:border-box;align-items:center;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-option[disabled]{cursor:default}[dir=rtl] .mat-option{text-align:right}.mat-option .mat-icon{margin-right:16px;vertical-align:middle}.mat-option .mat-icon svg{vertical-align:top}[dir=rtl] .mat-option .mat-icon{margin-left:16px;margin-right:0}.mat-option[aria-disabled=true]{-webkit-user-select:none;user-select:none;cursor:default}.mat-optgroup .mat-option:not(.mat-option-multiple){padding-left:32px}[dir=rtl] .mat-optgroup .mat-option:not(.mat-option-multiple){padding-left:16px;padding-right:32px}.mat-option.mat-active::before{content:\\\"\\\"}.cdk-high-contrast-active .mat-option[aria-disabled=true]{opacity:.5}.cdk-high-contrast-active .mat-option.mat-selected:not(.mat-option-multiple)::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .cdk-high-contrast-active .mat-option.mat-selected:not(.mat-option-multiple)::after{right:auto;left:16px}.mat-option-text{display:inline-block;flex-grow:1;overflow:hidden;text-overflow:ellipsis}.mat-option .mat-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-option-pseudo-checkbox{margin-right:8px}[dir=rtl] .mat-option-pseudo-checkbox{margin-left:8px;margin-right:0}\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n  return MatOption;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Counts the amount of option group labels that precede the specified option.\n * @param optionIndex Index of the option at which to start counting.\n * @param options Flat list of all of the options.\n * @param optionGroups Flat list of all of the option groups.\n * @docs-private\n */\n\n\nfunction _countGroupLabelsBeforeOption(optionIndex, options, optionGroups) {\n  if (optionGroups.length) {\n    let optionsArray = options.toArray();\n    let groups = optionGroups.toArray();\n    let groupCounter = 0;\n\n    for (let i = 0; i < optionIndex + 1; i++) {\n      if (optionsArray[i].group && optionsArray[i].group === groups[groupCounter]) {\n        groupCounter++;\n      }\n    }\n\n    return groupCounter;\n  }\n\n  return 0;\n}\n/**\n * Determines the position to which to scroll a panel in order for an option to be into view.\n * @param optionOffset Offset of the option from the top of the panel.\n * @param optionHeight Height of the options.\n * @param currentScrollPosition Current scroll position of the panel.\n * @param panelHeight Height of the panel.\n * @docs-private\n */\n\n\nfunction _getOptionScrollPosition(optionOffset, optionHeight, currentScrollPosition, panelHeight) {\n  if (optionOffset < currentScrollPosition) {\n    return optionOffset;\n  }\n\n  if (optionOffset + optionHeight > currentScrollPosition + panelHeight) {\n    return Math.max(0, optionOffset - panelHeight + optionHeight);\n  }\n\n  return currentScrollPosition;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nlet MatOptionModule = /*#__PURE__*/(() => {\n  class MatOptionModule {}\n\n  MatOptionModule.ɵfac = function MatOptionModule_Factory(t) {\n    return new (t || MatOptionModule)();\n  };\n\n  MatOptionModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatOptionModule\n  });\n  MatOptionModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatRippleModule, CommonModule, MatCommonModule, MatPseudoCheckboxModule]\n  });\n  return MatOptionModule;\n})();\n\n/*#__PURE__*/\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { AnimationCurves, AnimationDurations, DateAdapter, ErrorStateMatcher, MATERIAL_SANITY_CHECKS, MAT_DATE_FORMATS, MAT_DATE_LOCALE, MAT_DATE_LOCALE_FACTORY, MAT_NATIVE_DATE_FORMATS, MAT_OPTGROUP, MAT_OPTION_PARENT_COMPONENT, MAT_RIPPLE_GLOBAL_OPTIONS, MatCommonModule, MatLine, MatLineModule, MatNativeDateModule, MatOptgroup, MatOption, MatOptionModule, MatOptionSelectionChange, MatPseudoCheckbox, MatPseudoCheckboxModule, MatRipple, MatRippleModule, NativeDateAdapter, NativeDateModule, RippleRef, RippleRenderer, ShowOnDirtyErrorStateMatcher, VERSION, _MatOptgroupBase, _MatOptionBase, _countGroupLabelsBeforeOption, _getOptionScrollPosition, defaultRippleAnimationConfig, mixinColor, mixinDisableRipple, mixinDisabled, mixinErrorState, mixinInitialized, mixinTabIndex, setLines }; //# sourceMappingURL=core.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}