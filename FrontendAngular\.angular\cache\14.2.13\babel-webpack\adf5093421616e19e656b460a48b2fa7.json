{"ast": null, "code": "import * as i1 from '@angular/cdk/a11y';\nimport { A11yModule } from '@angular/cdk/a11y';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayConfig, OverlayRef, OverlayModule } from '@angular/cdk/overlay';\nimport { _getFocusedElementPierceShadowDom } from '@angular/cdk/platform';\nimport * as i3 from '@angular/cdk/portal';\nimport { BasePortalOutlet, CdkPortalOutlet, ComponentPortal, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, ViewChild, InjectionToken, Injector, TemplateRef, InjectFlags, Injectable, SkipSelf, NgModule } from '@angular/core';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { Subject, defer, of } from 'rxjs';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { startWith } from 'rxjs/operators';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Configuration for opening a modal dialog. */\n\nfunction CdkDialogContainer_ng_template_0_Template(rf, ctx) {}\n\nclass DialogConfig {\n  constructor() {\n    /** The ARIA role of the dialog element. */\n    this.role = 'dialog';\n    /** Optional CSS class or classes applied to the overlay panel. */\n\n    this.panelClass = '';\n    /** Whether the dialog has a backdrop. */\n\n    this.hasBackdrop = true;\n    /** Optional CSS class or classes applied to the overlay backdrop. */\n\n    this.backdropClass = '';\n    /** Whether the dialog closes with the escape key or pointer events outside the panel element. */\n\n    this.disableClose = false;\n    /** Width of the dialog. */\n\n    this.width = '';\n    /** Height of the dialog. */\n\n    this.height = '';\n    /** Data being injected into the child component. */\n\n    this.data = null;\n    /** ID of the element that describes the dialog. */\n\n    this.ariaDescribedBy = null;\n    /** ID of the element that labels the dialog. */\n\n    this.ariaLabelledBy = null;\n    /** Dialog label applied via `aria-label` */\n\n    this.ariaLabel = null;\n    /** Whether this is a modal dialog. Used to set the `aria-modal` attribute. */\n\n    this.ariaModal = true;\n    /**\n     * Where the dialog should focus on open.\n     * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n     * AutoFocusTarget instead.\n     */\n\n    this.autoFocus = 'first-tabbable';\n    /**\n     * Whether the dialog should restore focus to the previously-focused element upon closing.\n     * Has the following behavior based on the type that is passed in:\n     * - `boolean` - when true, will return focus to the element that was focused before the dialog\n     *    was opened, otherwise won't restore focus at all.\n     * - `string` - focus will be restored to the first element that matches the CSS selector.\n     * - `HTMLElement` - focus will be restored to the specific element.\n     */\n\n    this.restoreFocus = true;\n    /**\n     * Whether the dialog should close when the user navigates backwards or forwards through browser\n     * history. This does not apply to navigation via anchor element unless using URL-hash based\n     * routing (`HashLocationStrategy` in the Angular router).\n     */\n\n    this.closeOnNavigation = true;\n    /**\n     * Whether the dialog should close when the dialog service is destroyed. This is useful if\n     * another service is wrapping the dialog and is managing the destruction instead.\n     */\n\n    this.closeOnDestroy = true;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction throwDialogContentAlreadyAttachedError() {\n  throw Error('Attempting to attach dialog content after content is already attached');\n}\n/**\n * Internal component that wraps user-provided dialog content.\n * @docs-private\n */\n\n\nclass CdkDialogContainer extends BasePortalOutlet {\n  constructor(_elementRef, _focusTrapFactory, _document, _config, _interactivityChecker, _ngZone, _overlayRef, _focusMonitor) {\n    super();\n    this._elementRef = _elementRef;\n    this._focusTrapFactory = _focusTrapFactory;\n    this._config = _config;\n    this._interactivityChecker = _interactivityChecker;\n    this._ngZone = _ngZone;\n    this._overlayRef = _overlayRef;\n    this._focusMonitor = _focusMonitor;\n    /** Element that was focused before the dialog was opened. Save this to restore upon close. */\n\n    this._elementFocusedBeforeDialogWasOpened = null;\n    /**\n     * Type of interaction that led to the dialog being closed. This is used to determine\n     * whether the focus style will be applied when returning focus to its original location\n     * after the dialog is closed.\n     */\n\n    this._closeInteractionType = null;\n    /**\n     * Attaches a DOM portal to the dialog container.\n     * @param portal Portal to be attached.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n\n    this.attachDomPortal = portal => {\n      if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throwDialogContentAlreadyAttachedError();\n      }\n\n      const result = this._portalOutlet.attachDomPortal(portal);\n\n      this._contentAttached();\n\n      return result;\n    };\n\n    this._ariaLabelledBy = this._config.ariaLabelledBy || null;\n    this._document = _document;\n  }\n\n  _contentAttached() {\n    this._initializeFocusTrap();\n\n    this._handleBackdropClicks();\n\n    this._captureInitialFocus();\n  }\n  /**\n   * Can be used by child classes to customize the initial focus\n   * capturing behavior (e.g. if it's tied to an animation).\n   */\n\n\n  _captureInitialFocus() {\n    this._trapFocus();\n  }\n\n  ngOnDestroy() {\n    this._restoreFocus();\n  }\n  /**\n   * Attach a ComponentPortal as content to this dialog container.\n   * @param portal Portal to be attached as the dialog content.\n   */\n\n\n  attachComponentPortal(portal) {\n    if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwDialogContentAlreadyAttachedError();\n    }\n\n    const result = this._portalOutlet.attachComponentPortal(portal);\n\n    this._contentAttached();\n\n    return result;\n  }\n  /**\n   * Attach a TemplatePortal as content to this dialog container.\n   * @param portal Portal to be attached as the dialog content.\n   */\n\n\n  attachTemplatePortal(portal) {\n    if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwDialogContentAlreadyAttachedError();\n    }\n\n    const result = this._portalOutlet.attachTemplatePortal(portal);\n\n    this._contentAttached();\n\n    return result;\n  } // TODO(crisbeto): this shouldn't be exposed, but there are internal references to it.\n\n  /** Captures focus if it isn't already inside the dialog. */\n\n\n  _recaptureFocus() {\n    if (!this._containsFocus()) {\n      this._trapFocus();\n    }\n  }\n  /**\n   * Focuses the provided element. If the element is not focusable, it will add a tabIndex\n   * attribute to forcefully focus it. The attribute is removed after focus is moved.\n   * @param element The element to focus.\n   */\n\n\n  _forceFocus(element, options) {\n    if (!this._interactivityChecker.isFocusable(element)) {\n      element.tabIndex = -1; // The tabindex attribute should be removed to avoid navigating to that element again\n\n      this._ngZone.runOutsideAngular(() => {\n        const callback = () => {\n          element.removeEventListener('blur', callback);\n          element.removeEventListener('mousedown', callback);\n          element.removeAttribute('tabindex');\n        };\n\n        element.addEventListener('blur', callback);\n        element.addEventListener('mousedown', callback);\n      });\n    }\n\n    element.focus(options);\n  }\n  /**\n   * Focuses the first element that matches the given selector within the focus trap.\n   * @param selector The CSS selector for the element to set focus to.\n   */\n\n\n  _focusByCssSelector(selector, options) {\n    let elementToFocus = this._elementRef.nativeElement.querySelector(selector);\n\n    if (elementToFocus) {\n      this._forceFocus(elementToFocus, options);\n    }\n  }\n  /**\n   * Moves the focus inside the focus trap. When autoFocus is not set to 'dialog', if focus\n   * cannot be moved then focus will go to the dialog container.\n   */\n\n\n  _trapFocus() {\n    const element = this._elementRef.nativeElement; // If were to attempt to focus immediately, then the content of the dialog would not yet be\n    // ready in instances where change detection has to run first. To deal with this, we simply\n    // wait for the microtask queue to be empty when setting focus when autoFocus isn't set to\n    // dialog. If the element inside the dialog can't be focused, then the container is focused\n    // so the user can't tab into other elements behind it.\n\n    switch (this._config.autoFocus) {\n      case false:\n      case 'dialog':\n        // Ensure that focus is on the dialog container. It's possible that a different\n        // component tried to move focus while the open animation was running. See:\n        // https://github.com/angular/components/issues/16215. Note that we only want to do this\n        // if the focus isn't inside the dialog already, because it's possible that the consumer\n        // turned off `autoFocus` in order to move focus themselves.\n        if (!this._containsFocus()) {\n          element.focus();\n        }\n\n        break;\n\n      case true:\n      case 'first-tabbable':\n        this._focusTrap.focusInitialElementWhenReady().then(focusedSuccessfully => {\n          // If we weren't able to find a focusable element in the dialog, then focus the dialog\n          // container instead.\n          if (!focusedSuccessfully) {\n            this._focusDialogContainer();\n          }\n        });\n\n        break;\n\n      case 'first-heading':\n        this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role=\"heading\"]');\n\n        break;\n\n      default:\n        this._focusByCssSelector(this._config.autoFocus);\n\n        break;\n    }\n  }\n  /** Restores focus to the element that was focused before the dialog opened. */\n\n\n  _restoreFocus() {\n    const focusConfig = this._config.restoreFocus;\n    let focusTargetElement = null;\n\n    if (typeof focusConfig === 'string') {\n      focusTargetElement = this._document.querySelector(focusConfig);\n    } else if (typeof focusConfig === 'boolean') {\n      focusTargetElement = focusConfig ? this._elementFocusedBeforeDialogWasOpened : null;\n    } else if (focusConfig) {\n      focusTargetElement = focusConfig;\n    } // We need the extra check, because IE can set the `activeElement` to null in some cases.\n\n\n    if (this._config.restoreFocus && focusTargetElement && typeof focusTargetElement.focus === 'function') {\n      const activeElement = _getFocusedElementPierceShadowDom();\n\n      const element = this._elementRef.nativeElement; // Make sure that focus is still inside the dialog or is on the body (usually because a\n      // non-focusable element like the backdrop was clicked) before moving it. It's possible that\n      // the consumer moved it themselves before the animation was done, in which case we shouldn't\n      // do anything.\n\n      if (!activeElement || activeElement === this._document.body || activeElement === element || element.contains(activeElement)) {\n        if (this._focusMonitor) {\n          this._focusMonitor.focusVia(focusTargetElement, this._closeInteractionType);\n\n          this._closeInteractionType = null;\n        } else {\n          focusTargetElement.focus();\n        }\n      }\n    }\n\n    if (this._focusTrap) {\n      this._focusTrap.destroy();\n    }\n  }\n  /** Focuses the dialog container. */\n\n\n  _focusDialogContainer() {\n    // Note that there is no focus method when rendering on the server.\n    if (this._elementRef.nativeElement.focus) {\n      this._elementRef.nativeElement.focus();\n    }\n  }\n  /** Returns whether focus is inside the dialog. */\n\n\n  _containsFocus() {\n    const element = this._elementRef.nativeElement;\n\n    const activeElement = _getFocusedElementPierceShadowDom();\n\n    return element === activeElement || element.contains(activeElement);\n  }\n  /** Sets up the focus trap. */\n\n\n  _initializeFocusTrap() {\n    this._focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement); // Save the previously focused element. This element will be re-focused\n    // when the dialog closes.\n\n    if (this._document) {\n      this._elementFocusedBeforeDialogWasOpened = _getFocusedElementPierceShadowDom();\n    }\n  }\n  /** Sets up the listener that handles clicks on the dialog backdrop. */\n\n\n  _handleBackdropClicks() {\n    // Clicking on the backdrop will move focus out of dialog.\n    // Recapture it if closing via the backdrop is disabled.\n    this._overlayRef.backdropClick().subscribe(() => {\n      if (this._config.disableClose) {\n        this._recaptureFocus();\n      }\n    });\n  }\n\n}\n\nCdkDialogContainer.ɵfac = function CdkDialogContainer_Factory(t) {\n  return new (t || CdkDialogContainer)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.FocusTrapFactory), i0.ɵɵdirectiveInject(DOCUMENT, 8), i0.ɵɵdirectiveInject(DialogConfig), i0.ɵɵdirectiveInject(i1.InteractivityChecker), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1$1.OverlayRef), i0.ɵɵdirectiveInject(i1.FocusMonitor));\n};\n\nCdkDialogContainer.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: CdkDialogContainer,\n  selectors: [[\"cdk-dialog-container\"]],\n  viewQuery: function CdkDialogContainer_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(CdkPortalOutlet, 7);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._portalOutlet = _t.first);\n    }\n  },\n  hostAttrs: [\"tabindex\", \"-1\", 1, \"cdk-dialog-container\"],\n  hostVars: 6,\n  hostBindings: function CdkDialogContainer_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"id\", ctx._config.id || null)(\"role\", ctx._config.role)(\"aria-modal\", ctx._config.ariaModal)(\"aria-labelledby\", ctx._config.ariaLabel ? null : ctx._ariaLabelledBy)(\"aria-label\", ctx._config.ariaLabel)(\"aria-describedby\", ctx._config.ariaDescribedBy || null);\n    }\n  },\n  features: [i0.ɵɵInheritDefinitionFeature],\n  decls: 1,\n  vars: 0,\n  consts: [[\"cdkPortalOutlet\", \"\"]],\n  template: function CdkDialogContainer_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, CdkDialogContainer_ng_template_0_Template, 0, 0, \"ng-template\", 0);\n    }\n  },\n  dependencies: [i3.CdkPortalOutlet],\n  styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\"],\n  encapsulation: 2\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDialogContainer, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-dialog-container',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      host: {\n        'class': 'cdk-dialog-container',\n        'tabindex': '-1',\n        '[attr.id]': '_config.id || null',\n        '[attr.role]': '_config.role',\n        '[attr.aria-modal]': '_config.ariaModal',\n        '[attr.aria-labelledby]': '_config.ariaLabel ? null : _ariaLabelledBy',\n        '[attr.aria-label]': '_config.ariaLabel',\n        '[attr.aria-describedby]': '_config.ariaDescribedBy || null'\n      },\n      template: \"<ng-template cdkPortalOutlet></ng-template>\\n\",\n      styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.FocusTrapFactory\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DialogConfig]\n      }]\n    }, {\n      type: i1.InteractivityChecker\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i1$1.OverlayRef\n    }, {\n      type: i1.FocusMonitor\n    }];\n  }, {\n    _portalOutlet: [{\n      type: ViewChild,\n      args: [CdkPortalOutlet, {\n        static: true\n      }]\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Reference to a dialog opened via the Dialog service.\n */\n\n\nclass DialogRef {\n  constructor(overlayRef, config) {\n    this.overlayRef = overlayRef;\n    this.config = config;\n    /** Emits when the dialog has been closed. */\n\n    this.closed = new Subject();\n    this.disableClose = config.disableClose;\n    this.backdropClick = overlayRef.backdropClick();\n    this.keydownEvents = overlayRef.keydownEvents();\n    this.outsidePointerEvents = overlayRef.outsidePointerEvents();\n    this.id = config.id; // By the time the dialog is created we are guaranteed to have an ID.\n\n    this.keydownEvents.subscribe(event => {\n      if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n        event.preventDefault();\n        this.close(undefined, {\n          focusOrigin: 'keyboard'\n        });\n      }\n    });\n    this.backdropClick.subscribe(() => {\n      if (!this.disableClose) {\n        this.close(undefined, {\n          focusOrigin: 'mouse'\n        });\n      }\n    });\n  }\n  /**\n   * Close the dialog.\n   * @param result Optional result to return to the dialog opener.\n   * @param options Additional options to customize the closing behavior.\n   */\n\n\n  close(result, options) {\n    if (this.containerInstance) {\n      const closedSubject = this.closed;\n      this.containerInstance._closeInteractionType = options?.focusOrigin || 'program';\n      this.overlayRef.dispose();\n      closedSubject.next(result);\n      closedSubject.complete();\n      this.componentInstance = this.containerInstance = null;\n    }\n  }\n  /** Updates the position of the dialog based on the current position strategy. */\n\n\n  updatePosition() {\n    this.overlayRef.updatePosition();\n    return this;\n  }\n  /**\n   * Updates the dialog's width and height.\n   * @param width New width of the dialog.\n   * @param height New height of the dialog.\n   */\n\n\n  updateSize(width = '', height = '') {\n    this.overlayRef.updateSize({\n      width,\n      height\n    });\n    return this;\n  }\n  /** Add a CSS class or an array of classes to the overlay pane. */\n\n\n  addPanelClass(classes) {\n    this.overlayRef.addPanelClass(classes);\n    return this;\n  }\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n\n\n  removePanelClass(classes) {\n    this.overlayRef.removePanelClass(classes);\n    return this;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Injection token for the Dialog's ScrollStrategy. */\n\n\nconst DIALOG_SCROLL_STRATEGY = new InjectionToken('DialogScrollStrategy');\n/** Injection token for the Dialog's Data. */\n\nconst DIALOG_DATA = new InjectionToken('DialogData');\n/** Injection token that can be used to provide default options for the dialog module. */\n\nconst DEFAULT_DIALOG_CONFIG = new InjectionToken('DefaultDialogConfig');\n/** @docs-private */\n\nfunction DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.block();\n}\n/** @docs-private */\n\n\nconst DIALOG_SCROLL_STRATEGY_PROVIDER = {\n  provide: DIALOG_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Unique id for the created dialog. */\n\nlet uniqueId = 0;\n\nclass Dialog {\n  constructor(_overlay, _injector, _defaultOptions, _parentDialog, _overlayContainer, scrollStrategy) {\n    this._overlay = _overlay;\n    this._injector = _injector;\n    this._defaultOptions = _defaultOptions;\n    this._parentDialog = _parentDialog;\n    this._overlayContainer = _overlayContainer;\n    this._openDialogsAtThisLevel = [];\n    this._afterAllClosedAtThisLevel = new Subject();\n    this._afterOpenedAtThisLevel = new Subject();\n    this._ariaHiddenElements = new Map();\n    /**\n     * Stream that emits when all open dialog have finished closing.\n     * Will emit on subscribe if there are no open dialogs to begin with.\n     */\n\n    this.afterAllClosed = defer(() => this.openDialogs.length ? this._getAfterAllClosed() : this._getAfterAllClosed().pipe(startWith(undefined)));\n    this._scrollStrategy = scrollStrategy;\n  }\n  /** Keeps track of the currently-open dialogs. */\n\n\n  get openDialogs() {\n    return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n  }\n  /** Stream that emits when a dialog has been opened. */\n\n\n  get afterOpened() {\n    return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n  }\n\n  open(componentOrTemplateRef, config) {\n    const defaults = this._defaultOptions || new DialogConfig();\n    config = { ...defaults,\n      ...config\n    };\n    config.id = config.id || `cdk-dialog-${uniqueId++}`;\n\n    if (config.id && this.getDialogById(config.id) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`Dialog with id \"${config.id}\" exists already. The dialog id must be unique.`);\n    }\n\n    const overlayConfig = this._getOverlayConfig(config);\n\n    const overlayRef = this._overlay.create(overlayConfig);\n\n    const dialogRef = new DialogRef(overlayRef, config);\n\n    const dialogContainer = this._attachContainer(overlayRef, dialogRef, config);\n\n    dialogRef.containerInstance = dialogContainer;\n\n    this._attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config); // If this is the first dialog that we're opening, hide all the non-overlay content.\n\n\n    if (!this.openDialogs.length) {\n      this._hideNonDialogContentFromAssistiveTechnology();\n    }\n\n    this.openDialogs.push(dialogRef);\n    dialogRef.closed.subscribe(() => this._removeOpenDialog(dialogRef, true));\n    this.afterOpened.next(dialogRef);\n    return dialogRef;\n  }\n  /**\n   * Closes all of the currently-open dialogs.\n   */\n\n\n  closeAll() {\n    reverseForEach(this.openDialogs, dialog => dialog.close());\n  }\n  /**\n   * Finds an open dialog by its id.\n   * @param id ID to use when looking up the dialog.\n   */\n\n\n  getDialogById(id) {\n    return this.openDialogs.find(dialog => dialog.id === id);\n  }\n\n  ngOnDestroy() {\n    // Make one pass over all the dialogs that need to be untracked, but should not be closed. We\n    // want to stop tracking the open dialog even if it hasn't been closed, because the tracking\n    // determines when `aria-hidden` is removed from elements outside the dialog.\n    reverseForEach(this._openDialogsAtThisLevel, dialog => {\n      // Check for `false` specifically since we want `undefined` to be interpreted as `true`.\n      if (dialog.config.closeOnDestroy === false) {\n        this._removeOpenDialog(dialog, false);\n      }\n    }); // Make a second pass and close the remaining dialogs. We do this second pass in order to\n    // correctly dispatch the `afterAllClosed` event in case we have a mixed array of dialogs\n    // that should be closed and dialogs that should not.\n\n    reverseForEach(this._openDialogsAtThisLevel, dialog => dialog.close());\n\n    this._afterAllClosedAtThisLevel.complete();\n\n    this._afterOpenedAtThisLevel.complete();\n\n    this._openDialogsAtThisLevel = [];\n  }\n  /**\n   * Creates an overlay config from a dialog config.\n   * @param config The dialog configuration.\n   * @returns The overlay configuration.\n   */\n\n\n  _getOverlayConfig(config) {\n    const state = new OverlayConfig({\n      positionStrategy: config.positionStrategy || this._overlay.position().global().centerHorizontally().centerVertically(),\n      scrollStrategy: config.scrollStrategy || this._scrollStrategy(),\n      panelClass: config.panelClass,\n      hasBackdrop: config.hasBackdrop,\n      direction: config.direction,\n      minWidth: config.minWidth,\n      minHeight: config.minHeight,\n      maxWidth: config.maxWidth,\n      maxHeight: config.maxHeight,\n      width: config.width,\n      height: config.height,\n      disposeOnNavigation: config.closeOnNavigation\n    });\n\n    if (config.backdropClass) {\n      state.backdropClass = config.backdropClass;\n    }\n\n    return state;\n  }\n  /**\n   * Attaches a dialog container to a dialog's already-created overlay.\n   * @param overlay Reference to the dialog's underlying overlay.\n   * @param config The dialog configuration.\n   * @returns A promise resolving to a ComponentRef for the attached container.\n   */\n\n\n  _attachContainer(overlay, dialogRef, config) {\n    const userInjector = config.injector || config.viewContainerRef?.injector;\n    const providers = [{\n      provide: DialogConfig,\n      useValue: config\n    }, {\n      provide: DialogRef,\n      useValue: dialogRef\n    }, {\n      provide: OverlayRef,\n      useValue: overlay\n    }];\n    let containerType;\n\n    if (config.container) {\n      if (typeof config.container === 'function') {\n        containerType = config.container;\n      } else {\n        containerType = config.container.type;\n        providers.push(...config.container.providers(config));\n      }\n    } else {\n      containerType = CdkDialogContainer;\n    }\n\n    const containerPortal = new ComponentPortal(containerType, config.viewContainerRef, Injector.create({\n      parent: userInjector || this._injector,\n      providers\n    }), config.componentFactoryResolver);\n    const containerRef = overlay.attach(containerPortal);\n    return containerRef.instance;\n  }\n  /**\n   * Attaches the user-provided component to the already-created dialog container.\n   * @param componentOrTemplateRef The type of component being loaded into the dialog,\n   *     or a TemplateRef to instantiate as the content.\n   * @param dialogRef Reference to the dialog being opened.\n   * @param dialogContainer Component that is going to wrap the dialog content.\n   * @param config Configuration used to open the dialog.\n   */\n\n\n  _attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config) {\n    if (componentOrTemplateRef instanceof TemplateRef) {\n      const injector = this._createInjector(config, dialogRef, dialogContainer, undefined);\n\n      let context = {\n        $implicit: config.data,\n        dialogRef\n      };\n\n      if (config.templateContext) {\n        context = { ...context,\n          ...(typeof config.templateContext === 'function' ? config.templateContext() : config.templateContext)\n        };\n      }\n\n      dialogContainer.attachTemplatePortal(new TemplatePortal(componentOrTemplateRef, null, context, injector));\n    } else {\n      const injector = this._createInjector(config, dialogRef, dialogContainer, this._injector);\n\n      const contentRef = dialogContainer.attachComponentPortal(new ComponentPortal(componentOrTemplateRef, config.viewContainerRef, injector, config.componentFactoryResolver));\n      dialogRef.componentInstance = contentRef.instance;\n    }\n  }\n  /**\n   * Creates a custom injector to be used inside the dialog. This allows a component loaded inside\n   * of a dialog to close itself and, optionally, to return a value.\n   * @param config Config object that is used to construct the dialog.\n   * @param dialogRef Reference to the dialog being opened.\n   * @param dialogContainer Component that is going to wrap the dialog content.\n   * @param fallbackInjector Injector to use as a fallback when a lookup fails in the custom\n   * dialog injector, if the user didn't provide a custom one.\n   * @returns The custom injector that can be used inside the dialog.\n   */\n\n\n  _createInjector(config, dialogRef, dialogContainer, fallbackInjector) {\n    const userInjector = config.injector || config.viewContainerRef?.injector;\n    const providers = [{\n      provide: DIALOG_DATA,\n      useValue: config.data\n    }, {\n      provide: DialogRef,\n      useValue: dialogRef\n    }];\n\n    if (config.providers) {\n      if (typeof config.providers === 'function') {\n        providers.push(...config.providers(dialogRef, config, dialogContainer));\n      } else {\n        providers.push(...config.providers);\n      }\n    }\n\n    if (config.direction && (!userInjector || !userInjector.get(Directionality, null, InjectFlags.Optional))) {\n      providers.push({\n        provide: Directionality,\n        useValue: {\n          value: config.direction,\n          change: of()\n        }\n      });\n    }\n\n    return Injector.create({\n      parent: userInjector || fallbackInjector,\n      providers\n    });\n  }\n  /**\n   * Removes a dialog from the array of open dialogs.\n   * @param dialogRef Dialog to be removed.\n   * @param emitEvent Whether to emit an event if this is the last dialog.\n   */\n\n\n  _removeOpenDialog(dialogRef, emitEvent) {\n    const index = this.openDialogs.indexOf(dialogRef);\n\n    if (index > -1) {\n      this.openDialogs.splice(index, 1); // If all the dialogs were closed, remove/restore the `aria-hidden`\n      // to a the siblings and emit to the `afterAllClosed` stream.\n\n      if (!this.openDialogs.length) {\n        this._ariaHiddenElements.forEach((previousValue, element) => {\n          if (previousValue) {\n            element.setAttribute('aria-hidden', previousValue);\n          } else {\n            element.removeAttribute('aria-hidden');\n          }\n        });\n\n        this._ariaHiddenElements.clear();\n\n        if (emitEvent) {\n          this._getAfterAllClosed().next();\n        }\n      }\n    }\n  }\n  /** Hides all of the content that isn't an overlay from assistive technology. */\n\n\n  _hideNonDialogContentFromAssistiveTechnology() {\n    const overlayContainer = this._overlayContainer.getContainerElement(); // Ensure that the overlay container is attached to the DOM.\n\n\n    if (overlayContainer.parentElement) {\n      const siblings = overlayContainer.parentElement.children;\n\n      for (let i = siblings.length - 1; i > -1; i--) {\n        const sibling = siblings[i];\n\n        if (sibling !== overlayContainer && sibling.nodeName !== 'SCRIPT' && sibling.nodeName !== 'STYLE' && !sibling.hasAttribute('aria-live')) {\n          this._ariaHiddenElements.set(sibling, sibling.getAttribute('aria-hidden'));\n\n          sibling.setAttribute('aria-hidden', 'true');\n        }\n      }\n    }\n  }\n\n  _getAfterAllClosed() {\n    const parent = this._parentDialog;\n    return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n  }\n\n}\n\nDialog.ɵfac = function Dialog_Factory(t) {\n  return new (t || Dialog)(i0.ɵɵinject(i1$1.Overlay), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(DEFAULT_DIALOG_CONFIG, 8), i0.ɵɵinject(Dialog, 12), i0.ɵɵinject(i1$1.OverlayContainer), i0.ɵɵinject(DIALOG_SCROLL_STRATEGY));\n};\n\nDialog.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: Dialog,\n  factory: Dialog.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Dialog, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i1$1.Overlay\n    }, {\n      type: i0.Injector\n    }, {\n      type: DialogConfig,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DEFAULT_DIALOG_CONFIG]\n      }]\n    }, {\n      type: Dialog,\n      decorators: [{\n        type: Optional\n      }, {\n        type: SkipSelf\n      }]\n    }, {\n      type: i1$1.OverlayContainer\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DIALOG_SCROLL_STRATEGY]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Executes a callback against all elements in an array while iterating in reverse.\n * Useful if the array is being modified as it is being iterated.\n */\n\n\nfunction reverseForEach(items, callback) {\n  let i = items.length;\n\n  while (i--) {\n    callback(items[i]);\n  }\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass DialogModule {}\n\nDialogModule.ɵfac = function DialogModule_Factory(t) {\n  return new (t || DialogModule)();\n};\n\nDialogModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: DialogModule\n});\nDialogModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [Dialog, DIALOG_SCROLL_STRATEGY_PROVIDER],\n  imports: [OverlayModule, PortalModule, A11yModule, // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n  // don't have to remember to import it or be faced with an unhelpful error.\n  PortalModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DialogModule, [{\n    type: NgModule,\n    args: [{\n      imports: [OverlayModule, PortalModule, A11yModule],\n      exports: [// Re-export the PortalModule so that people extending the `CdkDialogContainer`\n      // don't have to remember to import it or be faced with an unhelpful error.\n      PortalModule, CdkDialogContainer],\n      declarations: [CdkDialogContainer],\n      providers: [Dialog, DIALOG_SCROLL_STRATEGY_PROVIDER]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { CdkDialogContainer, DEFAULT_DIALOG_CONFIG, DIALOG_DATA, DIALOG_SCROLL_STRATEGY, DIALOG_SCROLL_STRATEGY_PROVIDER, DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY, Dialog, DialogConfig, DialogModule, DialogRef, throwDialogContentAlreadyAttachedError };", "map": {"version": 3, "names": ["i1", "A11yModule", "i1$1", "Overlay", "OverlayConfig", "OverlayRef", "OverlayModule", "_getFocusedElementPierceShadowDom", "i3", "BasePortalOutlet", "CdkPortalOutlet", "ComponentPortal", "TemplatePortal", "PortalModule", "DOCUMENT", "i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Optional", "Inject", "ViewChild", "InjectionToken", "Injector", "TemplateRef", "InjectFlags", "Injectable", "SkipSelf", "NgModule", "ESCAPE", "hasModifierKey", "Subject", "defer", "of", "Directionality", "startWith", "DialogConfig", "constructor", "role", "panelClass", "hasBackdrop", "backdropClass", "disableClose", "width", "height", "data", "ariaDescribedBy", "ariaLabelledBy", "aria<PERSON><PERSON><PERSON>", "ariaModal", "autoFocus", "restoreFocus", "closeOnNavigation", "closeOnDestroy", "throwDialogContentAlreadyAttachedError", "Error", "CdkDialogContainer", "_elementRef", "_focusTrapFactory", "_document", "_config", "_interactivityC<PERSON>cker", "_ngZone", "_overlayRef", "_focusMonitor", "_elementFocusedBeforeDialogWasOpened", "_closeInteractionType", "attachDomPortal", "portal", "_portalOutlet", "has<PERSON>tta<PERSON>", "ngDevMode", "result", "_contentAttached", "_ariaLabelledBy", "_initializeFocusTrap", "_handleBackdropClicks", "_captureInitialFocus", "_trapFocus", "ngOnDestroy", "_restoreFocus", "attachComponentPortal", "attachTemplatePortal", "_recaptureFocus", "_containsFocus", "_forceFocus", "element", "options", "isFocusable", "tabIndex", "runOutsideAngular", "callback", "removeEventListener", "removeAttribute", "addEventListener", "focus", "_focusByCssSelector", "selector", "elementToFocus", "nativeElement", "querySelector", "_focusTrap", "focusInitialElementWhenReady", "then", "focusedSuccessfully", "_focusDialogContainer", "focusConfig", "focusTargetElement", "activeElement", "body", "contains", "focusVia", "destroy", "create", "backdropClick", "subscribe", "ɵfac", "ElementRef", "FocusTrapFactory", "InteractivityChecker", "NgZone", "FocusMonitor", "ɵcmp", "type", "args", "encapsulation", "None", "changeDetection", "<PERSON><PERSON><PERSON>", "host", "template", "styles", "undefined", "decorators", "static", "DialogRef", "overlayRef", "config", "closed", "keydownEvents", "outsidePointerEvents", "id", "event", "keyCode", "preventDefault", "close", "<PERSON><PERSON><PERSON><PERSON>", "containerInstance", "closedSubject", "dispose", "next", "complete", "componentInstance", "updatePosition", "updateSize", "addPanelClass", "classes", "removePanelClass", "DIALOG_SCROLL_STRATEGY", "DIALOG_DATA", "DEFAULT_DIALOG_CONFIG", "DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY", "overlay", "scrollStrategies", "block", "DIALOG_SCROLL_STRATEGY_PROVIDER", "provide", "deps", "useFactory", "uniqueId", "Dialog", "_overlay", "_injector", "_defaultOptions", "_parentDialog", "_overlayContainer", "scrollStrategy", "_openDialogsAtThisLevel", "_afterAllClosedAtThisLevel", "_afterOpenedAtThisLevel", "_ariaHiddenElements", "Map", "afterAllClosed", "openDialogs", "length", "_getAfterAllClosed", "pipe", "_scrollStrategy", "afterOpened", "open", "componentOrTemplateRef", "defaults", "getDialogById", "overlayConfig", "_getOverlayConfig", "dialogRef", "dialogContainer", "_attachC<PERSON>r", "_attach<PERSON><PERSON>og<PERSON><PERSON>nt", "_hideNonDialogContentFromAssistiveTechnology", "push", "_removeOpenDialog", "closeAll", "reverseForEach", "dialog", "find", "state", "positionStrategy", "position", "global", "centerHorizontally", "centerVertically", "direction", "min<PERSON><PERSON><PERSON>", "minHeight", "max<PERSON><PERSON><PERSON>", "maxHeight", "disposeOnNavigation", "userInjector", "injector", "viewContainerRef", "providers", "useValue", "containerType", "container", "containerPortal", "parent", "componentFactoryResolver", "containerRef", "attach", "instance", "_createInjector", "context", "$implicit", "templateContext", "contentRef", "fallbackInjector", "get", "value", "change", "emitEvent", "index", "indexOf", "splice", "for<PERSON>ach", "previousValue", "setAttribute", "clear", "overlayContainer", "getContainerElement", "parentElement", "siblings", "children", "i", "sibling", "nodeName", "hasAttribute", "set", "getAttribute", "OverlayContainer", "ɵprov", "items", "DialogModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["R:/chateye/FrontendAngular/node_modules/@angular/cdk/fesm2020/dialog.mjs"], "sourcesContent": ["import * as i1 from '@angular/cdk/a11y';\nimport { A11yModule } from '@angular/cdk/a11y';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayConfig, OverlayRef, OverlayModule } from '@angular/cdk/overlay';\nimport { _getFocusedElementPierceShadowDom } from '@angular/cdk/platform';\nimport * as i3 from '@angular/cdk/portal';\nimport { BasePortalOutlet, CdkPortalOutlet, ComponentPortal, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, ViewChild, InjectionToken, Injector, TemplateRef, InjectFlags, Injectable, SkipSelf, NgModule } from '@angular/core';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { Subject, defer, of } from 'rxjs';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { startWith } from 'rxjs/operators';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Configuration for opening a modal dialog. */\nclass DialogConfig {\n    constructor() {\n        /** The ARIA role of the dialog element. */\n        this.role = 'dialog';\n        /** Optional CSS class or classes applied to the overlay panel. */\n        this.panelClass = '';\n        /** Whether the dialog has a backdrop. */\n        this.hasBackdrop = true;\n        /** Optional CSS class or classes applied to the overlay backdrop. */\n        this.backdropClass = '';\n        /** Whether the dialog closes with the escape key or pointer events outside the panel element. */\n        this.disableClose = false;\n        /** Width of the dialog. */\n        this.width = '';\n        /** Height of the dialog. */\n        this.height = '';\n        /** Data being injected into the child component. */\n        this.data = null;\n        /** ID of the element that describes the dialog. */\n        this.ariaDescribedBy = null;\n        /** ID of the element that labels the dialog. */\n        this.ariaLabelledBy = null;\n        /** Dialog label applied via `aria-label` */\n        this.ariaLabel = null;\n        /** Whether this is a modal dialog. Used to set the `aria-modal` attribute. */\n        this.ariaModal = true;\n        /**\n         * Where the dialog should focus on open.\n         * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n         * AutoFocusTarget instead.\n         */\n        this.autoFocus = 'first-tabbable';\n        /**\n         * Whether the dialog should restore focus to the previously-focused element upon closing.\n         * Has the following behavior based on the type that is passed in:\n         * - `boolean` - when true, will return focus to the element that was focused before the dialog\n         *    was opened, otherwise won't restore focus at all.\n         * - `string` - focus will be restored to the first element that matches the CSS selector.\n         * - `HTMLElement` - focus will be restored to the specific element.\n         */\n        this.restoreFocus = true;\n        /**\n         * Whether the dialog should close when the user navigates backwards or forwards through browser\n         * history. This does not apply to navigation via anchor element unless using URL-hash based\n         * routing (`HashLocationStrategy` in the Angular router).\n         */\n        this.closeOnNavigation = true;\n        /**\n         * Whether the dialog should close when the dialog service is destroyed. This is useful if\n         * another service is wrapping the dialog and is managing the destruction instead.\n         */\n        this.closeOnDestroy = true;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction throwDialogContentAlreadyAttachedError() {\n    throw Error('Attempting to attach dialog content after content is already attached');\n}\n/**\n * Internal component that wraps user-provided dialog content.\n * @docs-private\n */\nclass CdkDialogContainer extends BasePortalOutlet {\n    constructor(_elementRef, _focusTrapFactory, _document, _config, _interactivityChecker, _ngZone, _overlayRef, _focusMonitor) {\n        super();\n        this._elementRef = _elementRef;\n        this._focusTrapFactory = _focusTrapFactory;\n        this._config = _config;\n        this._interactivityChecker = _interactivityChecker;\n        this._ngZone = _ngZone;\n        this._overlayRef = _overlayRef;\n        this._focusMonitor = _focusMonitor;\n        /** Element that was focused before the dialog was opened. Save this to restore upon close. */\n        this._elementFocusedBeforeDialogWasOpened = null;\n        /**\n         * Type of interaction that led to the dialog being closed. This is used to determine\n         * whether the focus style will be applied when returning focus to its original location\n         * after the dialog is closed.\n         */\n        this._closeInteractionType = null;\n        /**\n         * Attaches a DOM portal to the dialog container.\n         * @param portal Portal to be attached.\n         * @deprecated To be turned into a method.\n         * @breaking-change 10.0.0\n         */\n        this.attachDomPortal = (portal) => {\n            if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throwDialogContentAlreadyAttachedError();\n            }\n            const result = this._portalOutlet.attachDomPortal(portal);\n            this._contentAttached();\n            return result;\n        };\n        this._ariaLabelledBy = this._config.ariaLabelledBy || null;\n        this._document = _document;\n    }\n    _contentAttached() {\n        this._initializeFocusTrap();\n        this._handleBackdropClicks();\n        this._captureInitialFocus();\n    }\n    /**\n     * Can be used by child classes to customize the initial focus\n     * capturing behavior (e.g. if it's tied to an animation).\n     */\n    _captureInitialFocus() {\n        this._trapFocus();\n    }\n    ngOnDestroy() {\n        this._restoreFocus();\n    }\n    /**\n     * Attach a ComponentPortal as content to this dialog container.\n     * @param portal Portal to be attached as the dialog content.\n     */\n    attachComponentPortal(portal) {\n        if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwDialogContentAlreadyAttachedError();\n        }\n        const result = this._portalOutlet.attachComponentPortal(portal);\n        this._contentAttached();\n        return result;\n    }\n    /**\n     * Attach a TemplatePortal as content to this dialog container.\n     * @param portal Portal to be attached as the dialog content.\n     */\n    attachTemplatePortal(portal) {\n        if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwDialogContentAlreadyAttachedError();\n        }\n        const result = this._portalOutlet.attachTemplatePortal(portal);\n        this._contentAttached();\n        return result;\n    }\n    // TODO(crisbeto): this shouldn't be exposed, but there are internal references to it.\n    /** Captures focus if it isn't already inside the dialog. */\n    _recaptureFocus() {\n        if (!this._containsFocus()) {\n            this._trapFocus();\n        }\n    }\n    /**\n     * Focuses the provided element. If the element is not focusable, it will add a tabIndex\n     * attribute to forcefully focus it. The attribute is removed after focus is moved.\n     * @param element The element to focus.\n     */\n    _forceFocus(element, options) {\n        if (!this._interactivityChecker.isFocusable(element)) {\n            element.tabIndex = -1;\n            // The tabindex attribute should be removed to avoid navigating to that element again\n            this._ngZone.runOutsideAngular(() => {\n                const callback = () => {\n                    element.removeEventListener('blur', callback);\n                    element.removeEventListener('mousedown', callback);\n                    element.removeAttribute('tabindex');\n                };\n                element.addEventListener('blur', callback);\n                element.addEventListener('mousedown', callback);\n            });\n        }\n        element.focus(options);\n    }\n    /**\n     * Focuses the first element that matches the given selector within the focus trap.\n     * @param selector The CSS selector for the element to set focus to.\n     */\n    _focusByCssSelector(selector, options) {\n        let elementToFocus = this._elementRef.nativeElement.querySelector(selector);\n        if (elementToFocus) {\n            this._forceFocus(elementToFocus, options);\n        }\n    }\n    /**\n     * Moves the focus inside the focus trap. When autoFocus is not set to 'dialog', if focus\n     * cannot be moved then focus will go to the dialog container.\n     */\n    _trapFocus() {\n        const element = this._elementRef.nativeElement;\n        // If were to attempt to focus immediately, then the content of the dialog would not yet be\n        // ready in instances where change detection has to run first. To deal with this, we simply\n        // wait for the microtask queue to be empty when setting focus when autoFocus isn't set to\n        // dialog. If the element inside the dialog can't be focused, then the container is focused\n        // so the user can't tab into other elements behind it.\n        switch (this._config.autoFocus) {\n            case false:\n            case 'dialog':\n                // Ensure that focus is on the dialog container. It's possible that a different\n                // component tried to move focus while the open animation was running. See:\n                // https://github.com/angular/components/issues/16215. Note that we only want to do this\n                // if the focus isn't inside the dialog already, because it's possible that the consumer\n                // turned off `autoFocus` in order to move focus themselves.\n                if (!this._containsFocus()) {\n                    element.focus();\n                }\n                break;\n            case true:\n            case 'first-tabbable':\n                this._focusTrap.focusInitialElementWhenReady().then(focusedSuccessfully => {\n                    // If we weren't able to find a focusable element in the dialog, then focus the dialog\n                    // container instead.\n                    if (!focusedSuccessfully) {\n                        this._focusDialogContainer();\n                    }\n                });\n                break;\n            case 'first-heading':\n                this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role=\"heading\"]');\n                break;\n            default:\n                this._focusByCssSelector(this._config.autoFocus);\n                break;\n        }\n    }\n    /** Restores focus to the element that was focused before the dialog opened. */\n    _restoreFocus() {\n        const focusConfig = this._config.restoreFocus;\n        let focusTargetElement = null;\n        if (typeof focusConfig === 'string') {\n            focusTargetElement = this._document.querySelector(focusConfig);\n        }\n        else if (typeof focusConfig === 'boolean') {\n            focusTargetElement = focusConfig ? this._elementFocusedBeforeDialogWasOpened : null;\n        }\n        else if (focusConfig) {\n            focusTargetElement = focusConfig;\n        }\n        // We need the extra check, because IE can set the `activeElement` to null in some cases.\n        if (this._config.restoreFocus &&\n            focusTargetElement &&\n            typeof focusTargetElement.focus === 'function') {\n            const activeElement = _getFocusedElementPierceShadowDom();\n            const element = this._elementRef.nativeElement;\n            // Make sure that focus is still inside the dialog or is on the body (usually because a\n            // non-focusable element like the backdrop was clicked) before moving it. It's possible that\n            // the consumer moved it themselves before the animation was done, in which case we shouldn't\n            // do anything.\n            if (!activeElement ||\n                activeElement === this._document.body ||\n                activeElement === element ||\n                element.contains(activeElement)) {\n                if (this._focusMonitor) {\n                    this._focusMonitor.focusVia(focusTargetElement, this._closeInteractionType);\n                    this._closeInteractionType = null;\n                }\n                else {\n                    focusTargetElement.focus();\n                }\n            }\n        }\n        if (this._focusTrap) {\n            this._focusTrap.destroy();\n        }\n    }\n    /** Focuses the dialog container. */\n    _focusDialogContainer() {\n        // Note that there is no focus method when rendering on the server.\n        if (this._elementRef.nativeElement.focus) {\n            this._elementRef.nativeElement.focus();\n        }\n    }\n    /** Returns whether focus is inside the dialog. */\n    _containsFocus() {\n        const element = this._elementRef.nativeElement;\n        const activeElement = _getFocusedElementPierceShadowDom();\n        return element === activeElement || element.contains(activeElement);\n    }\n    /** Sets up the focus trap. */\n    _initializeFocusTrap() {\n        this._focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement);\n        // Save the previously focused element. This element will be re-focused\n        // when the dialog closes.\n        if (this._document) {\n            this._elementFocusedBeforeDialogWasOpened = _getFocusedElementPierceShadowDom();\n        }\n    }\n    /** Sets up the listener that handles clicks on the dialog backdrop. */\n    _handleBackdropClicks() {\n        // Clicking on the backdrop will move focus out of dialog.\n        // Recapture it if closing via the backdrop is disabled.\n        this._overlayRef.backdropClick().subscribe(() => {\n            if (this._config.disableClose) {\n                this._recaptureFocus();\n            }\n        });\n    }\n}\nCdkDialogContainer.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: CdkDialogContainer, deps: [{ token: i0.ElementRef }, { token: i1.FocusTrapFactory }, { token: DOCUMENT, optional: true }, { token: DialogConfig }, { token: i1.InteractivityChecker }, { token: i0.NgZone }, { token: i1$1.OverlayRef }, { token: i1.FocusMonitor }], target: i0.ɵɵFactoryTarget.Component });\nCdkDialogContainer.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: CdkDialogContainer, selector: \"cdk-dialog-container\", host: { attributes: { \"tabindex\": \"-1\" }, properties: { \"attr.id\": \"_config.id || null\", \"attr.role\": \"_config.role\", \"attr.aria-modal\": \"_config.ariaModal\", \"attr.aria-labelledby\": \"_config.ariaLabel ? null : _ariaLabelledBy\", \"attr.aria-label\": \"_config.ariaLabel\", \"attr.aria-describedby\": \"_config.ariaDescribedBy || null\" }, classAttribute: \"cdk-dialog-container\" }, viewQueries: [{ propertyName: \"_portalOutlet\", first: true, predicate: CdkPortalOutlet, descendants: true, static: true }], usesInheritance: true, ngImport: i0, template: \"<ng-template cdkPortalOutlet></ng-template>\\n\", styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\"], dependencies: [{ kind: \"directive\", type: i3.CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: CdkDialogContainer, decorators: [{\n            type: Component,\n            args: [{ selector: 'cdk-dialog-container', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, host: {\n                        'class': 'cdk-dialog-container',\n                        'tabindex': '-1',\n                        '[attr.id]': '_config.id || null',\n                        '[attr.role]': '_config.role',\n                        '[attr.aria-modal]': '_config.ariaModal',\n                        '[attr.aria-labelledby]': '_config.ariaLabel ? null : _ariaLabelledBy',\n                        '[attr.aria-label]': '_config.ariaLabel',\n                        '[attr.aria-describedby]': '_config.ariaDescribedBy || null',\n                    }, template: \"<ng-template cdkPortalOutlet></ng-template>\\n\", styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.FocusTrapFactory }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DialogConfig]\n                }] }, { type: i1.InteractivityChecker }, { type: i0.NgZone }, { type: i1$1.OverlayRef }, { type: i1.FocusMonitor }]; }, propDecorators: { _portalOutlet: [{\n                type: ViewChild,\n                args: [CdkPortalOutlet, { static: true }]\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Reference to a dialog opened via the Dialog service.\n */\nclass DialogRef {\n    constructor(overlayRef, config) {\n        this.overlayRef = overlayRef;\n        this.config = config;\n        /** Emits when the dialog has been closed. */\n        this.closed = new Subject();\n        this.disableClose = config.disableClose;\n        this.backdropClick = overlayRef.backdropClick();\n        this.keydownEvents = overlayRef.keydownEvents();\n        this.outsidePointerEvents = overlayRef.outsidePointerEvents();\n        this.id = config.id; // By the time the dialog is created we are guaranteed to have an ID.\n        this.keydownEvents.subscribe(event => {\n            if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n                event.preventDefault();\n                this.close(undefined, { focusOrigin: 'keyboard' });\n            }\n        });\n        this.backdropClick.subscribe(() => {\n            if (!this.disableClose) {\n                this.close(undefined, { focusOrigin: 'mouse' });\n            }\n        });\n    }\n    /**\n     * Close the dialog.\n     * @param result Optional result to return to the dialog opener.\n     * @param options Additional options to customize the closing behavior.\n     */\n    close(result, options) {\n        if (this.containerInstance) {\n            const closedSubject = this.closed;\n            this.containerInstance._closeInteractionType = options?.focusOrigin || 'program';\n            this.overlayRef.dispose();\n            closedSubject.next(result);\n            closedSubject.complete();\n            this.componentInstance = this.containerInstance = null;\n        }\n    }\n    /** Updates the position of the dialog based on the current position strategy. */\n    updatePosition() {\n        this.overlayRef.updatePosition();\n        return this;\n    }\n    /**\n     * Updates the dialog's width and height.\n     * @param width New width of the dialog.\n     * @param height New height of the dialog.\n     */\n    updateSize(width = '', height = '') {\n        this.overlayRef.updateSize({ width, height });\n        return this;\n    }\n    /** Add a CSS class or an array of classes to the overlay pane. */\n    addPanelClass(classes) {\n        this.overlayRef.addPanelClass(classes);\n        return this;\n    }\n    /** Remove a CSS class or an array of classes from the overlay pane. */\n    removePanelClass(classes) {\n        this.overlayRef.removePanelClass(classes);\n        return this;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Injection token for the Dialog's ScrollStrategy. */\nconst DIALOG_SCROLL_STRATEGY = new InjectionToken('DialogScrollStrategy');\n/** Injection token for the Dialog's Data. */\nconst DIALOG_DATA = new InjectionToken('DialogData');\n/** Injection token that can be used to provide default options for the dialog module. */\nconst DEFAULT_DIALOG_CONFIG = new InjectionToken('DefaultDialogConfig');\n/** @docs-private */\nfunction DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.block();\n}\n/** @docs-private */\nconst DIALOG_SCROLL_STRATEGY_PROVIDER = {\n    provide: DIALOG_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY,\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Unique id for the created dialog. */\nlet uniqueId = 0;\nclass Dialog {\n    constructor(_overlay, _injector, _defaultOptions, _parentDialog, _overlayContainer, scrollStrategy) {\n        this._overlay = _overlay;\n        this._injector = _injector;\n        this._defaultOptions = _defaultOptions;\n        this._parentDialog = _parentDialog;\n        this._overlayContainer = _overlayContainer;\n        this._openDialogsAtThisLevel = [];\n        this._afterAllClosedAtThisLevel = new Subject();\n        this._afterOpenedAtThisLevel = new Subject();\n        this._ariaHiddenElements = new Map();\n        /**\n         * Stream that emits when all open dialog have finished closing.\n         * Will emit on subscribe if there are no open dialogs to begin with.\n         */\n        this.afterAllClosed = defer(() => this.openDialogs.length\n            ? this._getAfterAllClosed()\n            : this._getAfterAllClosed().pipe(startWith(undefined)));\n        this._scrollStrategy = scrollStrategy;\n    }\n    /** Keeps track of the currently-open dialogs. */\n    get openDialogs() {\n        return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n    }\n    /** Stream that emits when a dialog has been opened. */\n    get afterOpened() {\n        return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n    }\n    open(componentOrTemplateRef, config) {\n        const defaults = (this._defaultOptions || new DialogConfig());\n        config = { ...defaults, ...config };\n        config.id = config.id || `cdk-dialog-${uniqueId++}`;\n        if (config.id &&\n            this.getDialogById(config.id) &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`Dialog with id \"${config.id}\" exists already. The dialog id must be unique.`);\n        }\n        const overlayConfig = this._getOverlayConfig(config);\n        const overlayRef = this._overlay.create(overlayConfig);\n        const dialogRef = new DialogRef(overlayRef, config);\n        const dialogContainer = this._attachContainer(overlayRef, dialogRef, config);\n        dialogRef.containerInstance = dialogContainer;\n        this._attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config);\n        // If this is the first dialog that we're opening, hide all the non-overlay content.\n        if (!this.openDialogs.length) {\n            this._hideNonDialogContentFromAssistiveTechnology();\n        }\n        this.openDialogs.push(dialogRef);\n        dialogRef.closed.subscribe(() => this._removeOpenDialog(dialogRef, true));\n        this.afterOpened.next(dialogRef);\n        return dialogRef;\n    }\n    /**\n     * Closes all of the currently-open dialogs.\n     */\n    closeAll() {\n        reverseForEach(this.openDialogs, dialog => dialog.close());\n    }\n    /**\n     * Finds an open dialog by its id.\n     * @param id ID to use when looking up the dialog.\n     */\n    getDialogById(id) {\n        return this.openDialogs.find(dialog => dialog.id === id);\n    }\n    ngOnDestroy() {\n        // Make one pass over all the dialogs that need to be untracked, but should not be closed. We\n        // want to stop tracking the open dialog even if it hasn't been closed, because the tracking\n        // determines when `aria-hidden` is removed from elements outside the dialog.\n        reverseForEach(this._openDialogsAtThisLevel, dialog => {\n            // Check for `false` specifically since we want `undefined` to be interpreted as `true`.\n            if (dialog.config.closeOnDestroy === false) {\n                this._removeOpenDialog(dialog, false);\n            }\n        });\n        // Make a second pass and close the remaining dialogs. We do this second pass in order to\n        // correctly dispatch the `afterAllClosed` event in case we have a mixed array of dialogs\n        // that should be closed and dialogs that should not.\n        reverseForEach(this._openDialogsAtThisLevel, dialog => dialog.close());\n        this._afterAllClosedAtThisLevel.complete();\n        this._afterOpenedAtThisLevel.complete();\n        this._openDialogsAtThisLevel = [];\n    }\n    /**\n     * Creates an overlay config from a dialog config.\n     * @param config The dialog configuration.\n     * @returns The overlay configuration.\n     */\n    _getOverlayConfig(config) {\n        const state = new OverlayConfig({\n            positionStrategy: config.positionStrategy ||\n                this._overlay.position().global().centerHorizontally().centerVertically(),\n            scrollStrategy: config.scrollStrategy || this._scrollStrategy(),\n            panelClass: config.panelClass,\n            hasBackdrop: config.hasBackdrop,\n            direction: config.direction,\n            minWidth: config.minWidth,\n            minHeight: config.minHeight,\n            maxWidth: config.maxWidth,\n            maxHeight: config.maxHeight,\n            width: config.width,\n            height: config.height,\n            disposeOnNavigation: config.closeOnNavigation,\n        });\n        if (config.backdropClass) {\n            state.backdropClass = config.backdropClass;\n        }\n        return state;\n    }\n    /**\n     * Attaches a dialog container to a dialog's already-created overlay.\n     * @param overlay Reference to the dialog's underlying overlay.\n     * @param config The dialog configuration.\n     * @returns A promise resolving to a ComponentRef for the attached container.\n     */\n    _attachContainer(overlay, dialogRef, config) {\n        const userInjector = config.injector || config.viewContainerRef?.injector;\n        const providers = [\n            { provide: DialogConfig, useValue: config },\n            { provide: DialogRef, useValue: dialogRef },\n            { provide: OverlayRef, useValue: overlay },\n        ];\n        let containerType;\n        if (config.container) {\n            if (typeof config.container === 'function') {\n                containerType = config.container;\n            }\n            else {\n                containerType = config.container.type;\n                providers.push(...config.container.providers(config));\n            }\n        }\n        else {\n            containerType = CdkDialogContainer;\n        }\n        const containerPortal = new ComponentPortal(containerType, config.viewContainerRef, Injector.create({ parent: userInjector || this._injector, providers }), config.componentFactoryResolver);\n        const containerRef = overlay.attach(containerPortal);\n        return containerRef.instance;\n    }\n    /**\n     * Attaches the user-provided component to the already-created dialog container.\n     * @param componentOrTemplateRef The type of component being loaded into the dialog,\n     *     or a TemplateRef to instantiate as the content.\n     * @param dialogRef Reference to the dialog being opened.\n     * @param dialogContainer Component that is going to wrap the dialog content.\n     * @param config Configuration used to open the dialog.\n     */\n    _attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config) {\n        if (componentOrTemplateRef instanceof TemplateRef) {\n            const injector = this._createInjector(config, dialogRef, dialogContainer, undefined);\n            let context = { $implicit: config.data, dialogRef };\n            if (config.templateContext) {\n                context = {\n                    ...context,\n                    ...(typeof config.templateContext === 'function'\n                        ? config.templateContext()\n                        : config.templateContext),\n                };\n            }\n            dialogContainer.attachTemplatePortal(new TemplatePortal(componentOrTemplateRef, null, context, injector));\n        }\n        else {\n            const injector = this._createInjector(config, dialogRef, dialogContainer, this._injector);\n            const contentRef = dialogContainer.attachComponentPortal(new ComponentPortal(componentOrTemplateRef, config.viewContainerRef, injector, config.componentFactoryResolver));\n            dialogRef.componentInstance = contentRef.instance;\n        }\n    }\n    /**\n     * Creates a custom injector to be used inside the dialog. This allows a component loaded inside\n     * of a dialog to close itself and, optionally, to return a value.\n     * @param config Config object that is used to construct the dialog.\n     * @param dialogRef Reference to the dialog being opened.\n     * @param dialogContainer Component that is going to wrap the dialog content.\n     * @param fallbackInjector Injector to use as a fallback when a lookup fails in the custom\n     * dialog injector, if the user didn't provide a custom one.\n     * @returns The custom injector that can be used inside the dialog.\n     */\n    _createInjector(config, dialogRef, dialogContainer, fallbackInjector) {\n        const userInjector = config.injector || config.viewContainerRef?.injector;\n        const providers = [\n            { provide: DIALOG_DATA, useValue: config.data },\n            { provide: DialogRef, useValue: dialogRef },\n        ];\n        if (config.providers) {\n            if (typeof config.providers === 'function') {\n                providers.push(...config.providers(dialogRef, config, dialogContainer));\n            }\n            else {\n                providers.push(...config.providers);\n            }\n        }\n        if (config.direction &&\n            (!userInjector ||\n                !userInjector.get(Directionality, null, InjectFlags.Optional))) {\n            providers.push({\n                provide: Directionality,\n                useValue: { value: config.direction, change: of() },\n            });\n        }\n        return Injector.create({ parent: userInjector || fallbackInjector, providers });\n    }\n    /**\n     * Removes a dialog from the array of open dialogs.\n     * @param dialogRef Dialog to be removed.\n     * @param emitEvent Whether to emit an event if this is the last dialog.\n     */\n    _removeOpenDialog(dialogRef, emitEvent) {\n        const index = this.openDialogs.indexOf(dialogRef);\n        if (index > -1) {\n            this.openDialogs.splice(index, 1);\n            // If all the dialogs were closed, remove/restore the `aria-hidden`\n            // to a the siblings and emit to the `afterAllClosed` stream.\n            if (!this.openDialogs.length) {\n                this._ariaHiddenElements.forEach((previousValue, element) => {\n                    if (previousValue) {\n                        element.setAttribute('aria-hidden', previousValue);\n                    }\n                    else {\n                        element.removeAttribute('aria-hidden');\n                    }\n                });\n                this._ariaHiddenElements.clear();\n                if (emitEvent) {\n                    this._getAfterAllClosed().next();\n                }\n            }\n        }\n    }\n    /** Hides all of the content that isn't an overlay from assistive technology. */\n    _hideNonDialogContentFromAssistiveTechnology() {\n        const overlayContainer = this._overlayContainer.getContainerElement();\n        // Ensure that the overlay container is attached to the DOM.\n        if (overlayContainer.parentElement) {\n            const siblings = overlayContainer.parentElement.children;\n            for (let i = siblings.length - 1; i > -1; i--) {\n                const sibling = siblings[i];\n                if (sibling !== overlayContainer &&\n                    sibling.nodeName !== 'SCRIPT' &&\n                    sibling.nodeName !== 'STYLE' &&\n                    !sibling.hasAttribute('aria-live')) {\n                    this._ariaHiddenElements.set(sibling, sibling.getAttribute('aria-hidden'));\n                    sibling.setAttribute('aria-hidden', 'true');\n                }\n            }\n        }\n    }\n    _getAfterAllClosed() {\n        const parent = this._parentDialog;\n        return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n    }\n}\nDialog.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: Dialog, deps: [{ token: i1$1.Overlay }, { token: i0.Injector }, { token: DEFAULT_DIALOG_CONFIG, optional: true }, { token: Dialog, optional: true, skipSelf: true }, { token: i1$1.OverlayContainer }, { token: DIALOG_SCROLL_STRATEGY }], target: i0.ɵɵFactoryTarget.Injectable });\nDialog.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: Dialog });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: Dialog, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: i1$1.Overlay }, { type: i0.Injector }, { type: DialogConfig, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DEFAULT_DIALOG_CONFIG]\n                }] }, { type: Dialog, decorators: [{\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }] }, { type: i1$1.OverlayContainer }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DIALOG_SCROLL_STRATEGY]\n                }] }]; } });\n/**\n * Executes a callback against all elements in an array while iterating in reverse.\n * Useful if the array is being modified as it is being iterated.\n */\nfunction reverseForEach(items, callback) {\n    let i = items.length;\n    while (i--) {\n        callback(items[i]);\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass DialogModule {\n}\nDialogModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: DialogModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nDialogModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.2.0\", ngImport: i0, type: DialogModule, declarations: [CdkDialogContainer], imports: [OverlayModule, PortalModule, A11yModule], exports: [\n        // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n        // don't have to remember to import it or be faced with an unhelpful error.\n        PortalModule,\n        CdkDialogContainer] });\nDialogModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: DialogModule, providers: [Dialog, DIALOG_SCROLL_STRATEGY_PROVIDER], imports: [OverlayModule, PortalModule, A11yModule, \n        // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n        // don't have to remember to import it or be faced with an unhelpful error.\n        PortalModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: DialogModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [OverlayModule, PortalModule, A11yModule],\n                    exports: [\n                        // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n                        // don't have to remember to import it or be faced with an unhelpful error.\n                        PortalModule,\n                        CdkDialogContainer,\n                    ],\n                    declarations: [CdkDialogContainer],\n                    providers: [Dialog, DIALOG_SCROLL_STRATEGY_PROVIDER],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkDialogContainer, DEFAULT_DIALOG_CONFIG, DIALOG_DATA, DIALOG_SCROLL_STRATEGY, DIALOG_SCROLL_STRATEGY_PROVIDER, DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY, Dialog, DialogConfig, DialogModule, DialogRef, throwDialogContentAlreadyAttachedError };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,mBAApB;AACA,SAASC,UAAT,QAA2B,mBAA3B;AACA,OAAO,KAAKC,IAAZ,MAAsB,sBAAtB;AACA,SAASC,OAAT,EAAkBC,aAAlB,EAAiCC,UAAjC,EAA6CC,aAA7C,QAAkE,sBAAlE;AACA,SAASC,iCAAT,QAAkD,uBAAlD;AACA,OAAO,KAAKC,EAAZ,MAAoB,qBAApB;AACA,SAASC,gBAAT,EAA2BC,eAA3B,EAA4CC,eAA5C,EAA6DC,cAA7D,EAA6EC,YAA7E,QAAiG,qBAAjG;AACA,SAASC,QAAT,QAAyB,iBAAzB;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,SAASC,SAAT,EAAoBC,iBAApB,EAAuCC,uBAAvC,EAAgEC,QAAhE,EAA0EC,MAA1E,EAAkFC,SAAlF,EAA6FC,cAA7F,EAA6GC,QAA7G,EAAuHC,WAAvH,EAAoIC,WAApI,EAAiJC,UAAjJ,EAA6JC,QAA7J,EAAuKC,QAAvK,QAAuL,eAAvL;AACA,SAASC,MAAT,EAAiBC,cAAjB,QAAuC,uBAAvC;AACA,SAASC,OAAT,EAAkBC,KAAlB,EAAyBC,EAAzB,QAAmC,MAAnC;AACA,SAASC,cAAT,QAA+B,mBAA/B;AACA,SAASC,SAAT,QAA0B,gBAA1B;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;;AACA,MAAMC,YAAN,CAAmB;EACfC,WAAW,GAAG;IACV;IACA,KAAKC,IAAL,GAAY,QAAZ;IACA;;IACA,KAAKC,UAAL,GAAkB,EAAlB;IACA;;IACA,KAAKC,WAAL,GAAmB,IAAnB;IACA;;IACA,KAAKC,aAAL,GAAqB,EAArB;IACA;;IACA,KAAKC,YAAL,GAAoB,KAApB;IACA;;IACA,KAAKC,KAAL,GAAa,EAAb;IACA;;IACA,KAAKC,MAAL,GAAc,EAAd;IACA;;IACA,KAAKC,IAAL,GAAY,IAAZ;IACA;;IACA,KAAKC,eAAL,GAAuB,IAAvB;IACA;;IACA,KAAKC,cAAL,GAAsB,IAAtB;IACA;;IACA,KAAKC,SAAL,GAAiB,IAAjB;IACA;;IACA,KAAKC,SAAL,GAAiB,IAAjB;IACA;AACR;AACA;AACA;AACA;;IACQ,KAAKC,SAAL,GAAiB,gBAAjB;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;;IACQ,KAAKC,YAAL,GAAoB,IAApB;IACA;AACR;AACA;AACA;AACA;;IACQ,KAAKC,iBAAL,GAAyB,IAAzB;IACA;AACR;AACA;AACA;;IACQ,KAAKC,cAAL,GAAsB,IAAtB;EACH;;AApDc;AAuDnB;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,sCAAT,GAAkD;EAC9C,MAAMC,KAAK,CAAC,uEAAD,CAAX;AACH;AACD;AACA;AACA;AACA;;;AACA,MAAMC,kBAAN,SAAiC/C,gBAAjC,CAAkD;EAC9C4B,WAAW,CAACoB,WAAD,EAAcC,iBAAd,EAAiCC,SAAjC,EAA4CC,OAA5C,EAAqDC,qBAArD,EAA4EC,OAA5E,EAAqFC,WAArF,EAAkGC,aAAlG,EAAiH;IACxH;IACA,KAAKP,WAAL,GAAmBA,WAAnB;IACA,KAAKC,iBAAL,GAAyBA,iBAAzB;IACA,KAAKE,OAAL,GAAeA,OAAf;IACA,KAAKC,qBAAL,GAA6BA,qBAA7B;IACA,KAAKC,OAAL,GAAeA,OAAf;IACA,KAAKC,WAAL,GAAmBA,WAAnB;IACA,KAAKC,aAAL,GAAqBA,aAArB;IACA;;IACA,KAAKC,oCAAL,GAA4C,IAA5C;IACA;AACR;AACA;AACA;AACA;;IACQ,KAAKC,qBAAL,GAA6B,IAA7B;IACA;AACR;AACA;AACA;AACA;AACA;;IACQ,KAAKC,eAAL,GAAwBC,MAAD,IAAY;MAC/B,IAAI,KAAKC,aAAL,CAAmBC,WAAnB,OAAqC,OAAOC,SAAP,KAAqB,WAArB,IAAoCA,SAAzE,CAAJ,EAAyF;QACrFjB,sCAAsC;MACzC;;MACD,MAAMkB,MAAM,GAAG,KAAKH,aAAL,CAAmBF,eAAnB,CAAmCC,MAAnC,CAAf;;MACA,KAAKK,gBAAL;;MACA,OAAOD,MAAP;IACH,CAPD;;IAQA,KAAKE,eAAL,GAAuB,KAAKd,OAAL,CAAab,cAAb,IAA+B,IAAtD;IACA,KAAKY,SAAL,GAAiBA,SAAjB;EACH;;EACDc,gBAAgB,GAAG;IACf,KAAKE,oBAAL;;IACA,KAAKC,qBAAL;;IACA,KAAKC,oBAAL;EACH;EACD;AACJ;AACA;AACA;;;EACIA,oBAAoB,GAAG;IACnB,KAAKC,UAAL;EACH;;EACDC,WAAW,GAAG;IACV,KAAKC,aAAL;EACH;EACD;AACJ;AACA;AACA;;;EACIC,qBAAqB,CAACb,MAAD,EAAS;IAC1B,IAAI,KAAKC,aAAL,CAAmBC,WAAnB,OAAqC,OAAOC,SAAP,KAAqB,WAArB,IAAoCA,SAAzE,CAAJ,EAAyF;MACrFjB,sCAAsC;IACzC;;IACD,MAAMkB,MAAM,GAAG,KAAKH,aAAL,CAAmBY,qBAAnB,CAAyCb,MAAzC,CAAf;;IACA,KAAKK,gBAAL;;IACA,OAAOD,MAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIU,oBAAoB,CAACd,MAAD,EAAS;IACzB,IAAI,KAAKC,aAAL,CAAmBC,WAAnB,OAAqC,OAAOC,SAAP,KAAqB,WAArB,IAAoCA,SAAzE,CAAJ,EAAyF;MACrFjB,sCAAsC;IACzC;;IACD,MAAMkB,MAAM,GAAG,KAAKH,aAAL,CAAmBa,oBAAnB,CAAwCd,MAAxC,CAAf;;IACA,KAAKK,gBAAL;;IACA,OAAOD,MAAP;EACH,CAzE6C,CA0E9C;;EACA;;;EACAW,eAAe,GAAG;IACd,IAAI,CAAC,KAAKC,cAAL,EAAL,EAA4B;MACxB,KAAKN,UAAL;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;;;EACIO,WAAW,CAACC,OAAD,EAAUC,OAAV,EAAmB;IAC1B,IAAI,CAAC,KAAK1B,qBAAL,CAA2B2B,WAA3B,CAAuCF,OAAvC,CAAL,EAAsD;MAClDA,OAAO,CAACG,QAAR,GAAmB,CAAC,CAApB,CADkD,CAElD;;MACA,KAAK3B,OAAL,CAAa4B,iBAAb,CAA+B,MAAM;QACjC,MAAMC,QAAQ,GAAG,MAAM;UACnBL,OAAO,CAACM,mBAAR,CAA4B,MAA5B,EAAoCD,QAApC;UACAL,OAAO,CAACM,mBAAR,CAA4B,WAA5B,EAAyCD,QAAzC;UACAL,OAAO,CAACO,eAAR,CAAwB,UAAxB;QACH,CAJD;;QAKAP,OAAO,CAACQ,gBAAR,CAAyB,MAAzB,EAAiCH,QAAjC;QACAL,OAAO,CAACQ,gBAAR,CAAyB,WAAzB,EAAsCH,QAAtC;MACH,CARD;IASH;;IACDL,OAAO,CAACS,KAAR,CAAcR,OAAd;EACH;EACD;AACJ;AACA;AACA;;;EACIS,mBAAmB,CAACC,QAAD,EAAWV,OAAX,EAAoB;IACnC,IAAIW,cAAc,GAAG,KAAKzC,WAAL,CAAiB0C,aAAjB,CAA+BC,aAA/B,CAA6CH,QAA7C,CAArB;;IACA,IAAIC,cAAJ,EAAoB;MAChB,KAAKb,WAAL,CAAiBa,cAAjB,EAAiCX,OAAjC;IACH;EACJ;EACD;AACJ;AACA;AACA;;;EACIT,UAAU,GAAG;IACT,MAAMQ,OAAO,GAAG,KAAK7B,WAAL,CAAiB0C,aAAjC,CADS,CAET;IACA;IACA;IACA;IACA;;IACA,QAAQ,KAAKvC,OAAL,CAAaV,SAArB;MACI,KAAK,KAAL;MACA,KAAK,QAAL;QACI;QACA;QACA;QACA;QACA;QACA,IAAI,CAAC,KAAKkC,cAAL,EAAL,EAA4B;UACxBE,OAAO,CAACS,KAAR;QACH;;QACD;;MACJ,KAAK,IAAL;MACA,KAAK,gBAAL;QACI,KAAKM,UAAL,CAAgBC,4BAAhB,GAA+CC,IAA/C,CAAoDC,mBAAmB,IAAI;UACvE;UACA;UACA,IAAI,CAACA,mBAAL,EAA0B;YACtB,KAAKC,qBAAL;UACH;QACJ,CAND;;QAOA;;MACJ,KAAK,eAAL;QACI,KAAKT,mBAAL,CAAyB,0CAAzB;;QACA;;MACJ;QACI,KAAKA,mBAAL,CAAyB,KAAKpC,OAAL,CAAaV,SAAtC;;QACA;IA3BR;EA6BH;EACD;;;EACA8B,aAAa,GAAG;IACZ,MAAM0B,WAAW,GAAG,KAAK9C,OAAL,CAAaT,YAAjC;IACA,IAAIwD,kBAAkB,GAAG,IAAzB;;IACA,IAAI,OAAOD,WAAP,KAAuB,QAA3B,EAAqC;MACjCC,kBAAkB,GAAG,KAAKhD,SAAL,CAAeyC,aAAf,CAA6BM,WAA7B,CAArB;IACH,CAFD,MAGK,IAAI,OAAOA,WAAP,KAAuB,SAA3B,EAAsC;MACvCC,kBAAkB,GAAGD,WAAW,GAAG,KAAKzC,oCAAR,GAA+C,IAA/E;IACH,CAFI,MAGA,IAAIyC,WAAJ,EAAiB;MAClBC,kBAAkB,GAAGD,WAArB;IACH,CAXW,CAYZ;;;IACA,IAAI,KAAK9C,OAAL,CAAaT,YAAb,IACAwD,kBADA,IAEA,OAAOA,kBAAkB,CAACZ,KAA1B,KAAoC,UAFxC,EAEoD;MAChD,MAAMa,aAAa,GAAGrG,iCAAiC,EAAvD;;MACA,MAAM+E,OAAO,GAAG,KAAK7B,WAAL,CAAiB0C,aAAjC,CAFgD,CAGhD;MACA;MACA;MACA;;MACA,IAAI,CAACS,aAAD,IACAA,aAAa,KAAK,KAAKjD,SAAL,CAAekD,IADjC,IAEAD,aAAa,KAAKtB,OAFlB,IAGAA,OAAO,CAACwB,QAAR,CAAiBF,aAAjB,CAHJ,EAGqC;QACjC,IAAI,KAAK5C,aAAT,EAAwB;UACpB,KAAKA,aAAL,CAAmB+C,QAAnB,CAA4BJ,kBAA5B,EAAgD,KAAKzC,qBAArD;;UACA,KAAKA,qBAAL,GAA6B,IAA7B;QACH,CAHD,MAIK;UACDyC,kBAAkB,CAACZ,KAAnB;QACH;MACJ;IACJ;;IACD,IAAI,KAAKM,UAAT,EAAqB;MACjB,KAAKA,UAAL,CAAgBW,OAAhB;IACH;EACJ;EACD;;;EACAP,qBAAqB,GAAG;IACpB;IACA,IAAI,KAAKhD,WAAL,CAAiB0C,aAAjB,CAA+BJ,KAAnC,EAA0C;MACtC,KAAKtC,WAAL,CAAiB0C,aAAjB,CAA+BJ,KAA/B;IACH;EACJ;EACD;;;EACAX,cAAc,GAAG;IACb,MAAME,OAAO,GAAG,KAAK7B,WAAL,CAAiB0C,aAAjC;;IACA,MAAMS,aAAa,GAAGrG,iCAAiC,EAAvD;;IACA,OAAO+E,OAAO,KAAKsB,aAAZ,IAA6BtB,OAAO,CAACwB,QAAR,CAAiBF,aAAjB,CAApC;EACH;EACD;;;EACAjC,oBAAoB,GAAG;IACnB,KAAK0B,UAAL,GAAkB,KAAK3C,iBAAL,CAAuBuD,MAAvB,CAA8B,KAAKxD,WAAL,CAAiB0C,aAA/C,CAAlB,CADmB,CAEnB;IACA;;IACA,IAAI,KAAKxC,SAAT,EAAoB;MAChB,KAAKM,oCAAL,GAA4C1D,iCAAiC,EAA7E;IACH;EACJ;EACD;;;EACAqE,qBAAqB,GAAG;IACpB;IACA;IACA,KAAKb,WAAL,CAAiBmD,aAAjB,GAAiCC,SAAjC,CAA2C,MAAM;MAC7C,IAAI,KAAKvD,OAAL,CAAalB,YAAjB,EAA+B;QAC3B,KAAKyC,eAAL;MACH;IACJ,CAJD;EAKH;;AAhO6C;;AAkOlD3B,kBAAkB,CAAC4D,IAAnB;EAAA,iBAA+G5D,kBAA/G,EAAqGzC,EAArG,mBAAmJA,EAAE,CAACsG,UAAtJ,GAAqGtG,EAArG,mBAA6Kf,EAAE,CAACsH,gBAAhL,GAAqGvG,EAArG,mBAA6MD,QAA7M,MAAqGC,EAArG,mBAAkPqB,YAAlP,GAAqGrB,EAArG,mBAA2Qf,EAAE,CAACuH,oBAA9Q,GAAqGxG,EAArG,mBAA+SA,EAAE,CAACyG,MAAlT,GAAqGzG,EAArG,mBAAqUb,IAAI,CAACG,UAA1U,GAAqGU,EAArG,mBAAiWf,EAAE,CAACyH,YAApW;AAAA;;AACAjE,kBAAkB,CAACkE,IAAnB,kBADqG3G,EACrG;EAAA,MAAmGyC,kBAAnG;EAAA;EAAA;IAAA;MADqGzC,EACrG,aAAolBL,eAAplB;IAAA;;IAAA;MAAA;;MADqGK,EACrG,qBADqGA,EACrG;IAAA;EAAA;EAAA,wBAA2L,IAA3L;EAAA;EAAA;IAAA;MADqGA,EACrG;IAAA;EAAA;EAAA,WADqGA,EACrG;EAAA;EAAA;EAAA;EAAA;IAAA;MADqGA,EAColB,iFAAzrB;IAAA;EAAA;EAAA,eAAk4BP,EAAE,CAACE,eAAr4B;EAAA;EAAA;AAAA;;AACA;EAAA,mDAFqGK,EAErG,mBAA2FyC,kBAA3F,EAA2H,CAAC;IAChHmE,IAAI,EAAE3G,SAD0G;IAEhH4G,IAAI,EAAE,CAAC;MAAE3B,QAAQ,EAAE,sBAAZ;MAAoC4B,aAAa,EAAE5G,iBAAiB,CAAC6G,IAArE;MAA2EC,eAAe,EAAE7G,uBAAuB,CAAC8G,OAApH;MAA6HC,IAAI,EAAE;QAC9H,SAAS,sBADqH;QAE9H,YAAY,IAFkH;QAG9H,aAAa,oBAHiH;QAI9H,eAAe,cAJ+G;QAK9H,qBAAqB,mBALyG;QAM9H,0BAA0B,4CANoG;QAO9H,qBAAqB,mBAPyG;QAQ9H,2BAA2B;MARmG,CAAnI;MASIC,QAAQ,EAAE,+CATd;MAS+DC,MAAM,EAAE,CAAC,mGAAD;IATvE,CAAD;EAF0G,CAAD,CAA3H,EAY4B,YAAY;IAAE,OAAO,CAAC;MAAER,IAAI,EAAE5G,EAAE,CAACsG;IAAX,CAAD,EAA0B;MAAEM,IAAI,EAAE3H,EAAE,CAACsH;IAAX,CAA1B,EAAyD;MAAEK,IAAI,EAAES,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACtHV,IAAI,EAAExG;MADgH,CAAD,EAEtH;QACCwG,IAAI,EAAEvG,MADP;QAECwG,IAAI,EAAE,CAAC9G,QAAD;MAFP,CAFsH;IAA/B,CAAzD,EAK3B;MAAE6G,IAAI,EAAES,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAClCV,IAAI,EAAEvG,MAD4B;QAElCwG,IAAI,EAAE,CAACxF,YAAD;MAF4B,CAAD;IAA/B,CAL2B,EAQ3B;MAAEuF,IAAI,EAAE3H,EAAE,CAACuH;IAAX,CAR2B,EAQQ;MAAEI,IAAI,EAAE5G,EAAE,CAACyG;IAAX,CARR,EAQ6B;MAAEG,IAAI,EAAEzH,IAAI,CAACG;IAAb,CAR7B,EAQwD;MAAEsH,IAAI,EAAE3H,EAAE,CAACyH;IAAX,CARxD,CAAP;EAQ4F,CApBtI,EAoBwJ;IAAEpD,aAAa,EAAE,CAAC;MAC1JsD,IAAI,EAAEtG,SADoJ;MAE1JuG,IAAI,EAAE,CAAClH,eAAD,EAAkB;QAAE4H,MAAM,EAAE;MAAV,CAAlB;IAFoJ,CAAD;EAAjB,CApBxJ;AAAA;AAyBA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;;AACA,MAAMC,SAAN,CAAgB;EACZlG,WAAW,CAACmG,UAAD,EAAaC,MAAb,EAAqB;IAC5B,KAAKD,UAAL,GAAkBA,UAAlB;IACA,KAAKC,MAAL,GAAcA,MAAd;IACA;;IACA,KAAKC,MAAL,GAAc,IAAI3G,OAAJ,EAAd;IACA,KAAKW,YAAL,GAAoB+F,MAAM,CAAC/F,YAA3B;IACA,KAAKwE,aAAL,GAAqBsB,UAAU,CAACtB,aAAX,EAArB;IACA,KAAKyB,aAAL,GAAqBH,UAAU,CAACG,aAAX,EAArB;IACA,KAAKC,oBAAL,GAA4BJ,UAAU,CAACI,oBAAX,EAA5B;IACA,KAAKC,EAAL,GAAUJ,MAAM,CAACI,EAAjB,CAT4B,CASP;;IACrB,KAAKF,aAAL,CAAmBxB,SAAnB,CAA6B2B,KAAK,IAAI;MAClC,IAAIA,KAAK,CAACC,OAAN,KAAkBlH,MAAlB,IAA4B,CAAC,KAAKa,YAAlC,IAAkD,CAACZ,cAAc,CAACgH,KAAD,CAArE,EAA8E;QAC1EA,KAAK,CAACE,cAAN;QACA,KAAKC,KAAL,CAAWb,SAAX,EAAsB;UAAEc,WAAW,EAAE;QAAf,CAAtB;MACH;IACJ,CALD;IAMA,KAAKhC,aAAL,CAAmBC,SAAnB,CAA6B,MAAM;MAC/B,IAAI,CAAC,KAAKzE,YAAV,EAAwB;QACpB,KAAKuG,KAAL,CAAWb,SAAX,EAAsB;UAAEc,WAAW,EAAE;QAAf,CAAtB;MACH;IACJ,CAJD;EAKH;EACD;AACJ;AACA;AACA;AACA;;;EACID,KAAK,CAACzE,MAAD,EAASe,OAAT,EAAkB;IACnB,IAAI,KAAK4D,iBAAT,EAA4B;MACxB,MAAMC,aAAa,GAAG,KAAKV,MAA3B;MACA,KAAKS,iBAAL,CAAuBjF,qBAAvB,GAA+CqB,OAAO,EAAE2D,WAAT,IAAwB,SAAvE;MACA,KAAKV,UAAL,CAAgBa,OAAhB;MACAD,aAAa,CAACE,IAAd,CAAmB9E,MAAnB;MACA4E,aAAa,CAACG,QAAd;MACA,KAAKC,iBAAL,GAAyB,KAAKL,iBAAL,GAAyB,IAAlD;IACH;EACJ;EACD;;;EACAM,cAAc,GAAG;IACb,KAAKjB,UAAL,CAAgBiB,cAAhB;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIC,UAAU,CAAC/G,KAAK,GAAG,EAAT,EAAaC,MAAM,GAAG,EAAtB,EAA0B;IAChC,KAAK4F,UAAL,CAAgBkB,UAAhB,CAA2B;MAAE/G,KAAF;MAASC;IAAT,CAA3B;IACA,OAAO,IAAP;EACH;EACD;;;EACA+G,aAAa,CAACC,OAAD,EAAU;IACnB,KAAKpB,UAAL,CAAgBmB,aAAhB,CAA8BC,OAA9B;IACA,OAAO,IAAP;EACH;EACD;;;EACAC,gBAAgB,CAACD,OAAD,EAAU;IACtB,KAAKpB,UAAL,CAAgBqB,gBAAhB,CAAiCD,OAAjC;IACA,OAAO,IAAP;EACH;;AA7DW;AAgEhB;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAME,sBAAsB,GAAG,IAAIxI,cAAJ,CAAmB,sBAAnB,CAA/B;AACA;;AACA,MAAMyI,WAAW,GAAG,IAAIzI,cAAJ,CAAmB,YAAnB,CAApB;AACA;;AACA,MAAM0I,qBAAqB,GAAG,IAAI1I,cAAJ,CAAmB,qBAAnB,CAA9B;AACA;;AACA,SAAS2I,uCAAT,CAAiDC,OAAjD,EAA0D;EACtD,OAAO,MAAMA,OAAO,CAACC,gBAAR,CAAyBC,KAAzB,EAAb;AACH;AACD;;;AACA,MAAMC,+BAA+B,GAAG;EACpCC,OAAO,EAAER,sBAD2B;EAEpCS,IAAI,EAAE,CAACpK,OAAD,CAF8B;EAGpCqK,UAAU,EAAEP;AAHwB,CAAxC;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;AACA,IAAIQ,QAAQ,GAAG,CAAf;;AACA,MAAMC,MAAN,CAAa;EACTrI,WAAW,CAACsI,QAAD,EAAWC,SAAX,EAAsBC,eAAtB,EAAuCC,aAAvC,EAAsDC,iBAAtD,EAAyEC,cAAzE,EAAyF;IAChG,KAAKL,QAAL,GAAgBA,QAAhB;IACA,KAAKC,SAAL,GAAiBA,SAAjB;IACA,KAAKC,eAAL,GAAuBA,eAAvB;IACA,KAAKC,aAAL,GAAqBA,aAArB;IACA,KAAKC,iBAAL,GAAyBA,iBAAzB;IACA,KAAKE,uBAAL,GAA+B,EAA/B;IACA,KAAKC,0BAAL,GAAkC,IAAInJ,OAAJ,EAAlC;IACA,KAAKoJ,uBAAL,GAA+B,IAAIpJ,OAAJ,EAA/B;IACA,KAAKqJ,mBAAL,GAA2B,IAAIC,GAAJ,EAA3B;IACA;AACR;AACA;AACA;;IACQ,KAAKC,cAAL,GAAsBtJ,KAAK,CAAC,MAAM,KAAKuJ,WAAL,CAAiBC,MAAjB,GAC5B,KAAKC,kBAAL,EAD4B,GAE5B,KAAKA,kBAAL,GAA0BC,IAA1B,CAA+BvJ,SAAS,CAACiG,SAAD,CAAxC,CAFqB,CAA3B;IAGA,KAAKuD,eAAL,GAAuBX,cAAvB;EACH;EACD;;;EACe,IAAXO,WAAW,GAAG;IACd,OAAO,KAAKT,aAAL,GAAqB,KAAKA,aAAL,CAAmBS,WAAxC,GAAsD,KAAKN,uBAAlE;EACH;EACD;;;EACe,IAAXW,WAAW,GAAG;IACd,OAAO,KAAKd,aAAL,GAAqB,KAAKA,aAAL,CAAmBc,WAAxC,GAAsD,KAAKT,uBAAlE;EACH;;EACDU,IAAI,CAACC,sBAAD,EAAyBrD,MAAzB,EAAiC;IACjC,MAAMsD,QAAQ,GAAI,KAAKlB,eAAL,IAAwB,IAAIzI,YAAJ,EAA1C;IACAqG,MAAM,GAAG,EAAE,GAAGsD,QAAL;MAAe,GAAGtD;IAAlB,CAAT;IACAA,MAAM,CAACI,EAAP,GAAYJ,MAAM,CAACI,EAAP,IAAc,cAAa4B,QAAQ,EAAG,EAAlD;;IACA,IAAIhC,MAAM,CAACI,EAAP,IACA,KAAKmD,aAAL,CAAmBvD,MAAM,CAACI,EAA1B,CADA,KAEC,OAAOtE,SAAP,KAAqB,WAArB,IAAoCA,SAFrC,CAAJ,EAEqD;MACjD,MAAMhB,KAAK,CAAE,mBAAkBkF,MAAM,CAACI,EAAG,iDAA9B,CAAX;IACH;;IACD,MAAMoD,aAAa,GAAG,KAAKC,iBAAL,CAAuBzD,MAAvB,CAAtB;;IACA,MAAMD,UAAU,GAAG,KAAKmC,QAAL,CAAc1D,MAAd,CAAqBgF,aAArB,CAAnB;;IACA,MAAME,SAAS,GAAG,IAAI5D,SAAJ,CAAcC,UAAd,EAA0BC,MAA1B,CAAlB;;IACA,MAAM2D,eAAe,GAAG,KAAKC,gBAAL,CAAsB7D,UAAtB,EAAkC2D,SAAlC,EAA6C1D,MAA7C,CAAxB;;IACA0D,SAAS,CAAChD,iBAAV,GAA8BiD,eAA9B;;IACA,KAAKE,oBAAL,CAA0BR,sBAA1B,EAAkDK,SAAlD,EAA6DC,eAA7D,EAA8E3D,MAA9E,EAdiC,CAejC;;;IACA,IAAI,CAAC,KAAK8C,WAAL,CAAiBC,MAAtB,EAA8B;MAC1B,KAAKe,4CAAL;IACH;;IACD,KAAKhB,WAAL,CAAiBiB,IAAjB,CAAsBL,SAAtB;IACAA,SAAS,CAACzD,MAAV,CAAiBvB,SAAjB,CAA2B,MAAM,KAAKsF,iBAAL,CAAuBN,SAAvB,EAAkC,IAAlC,CAAjC;IACA,KAAKP,WAAL,CAAiBtC,IAAjB,CAAsB6C,SAAtB;IACA,OAAOA,SAAP;EACH;EACD;AACJ;AACA;;;EACIO,QAAQ,GAAG;IACPC,cAAc,CAAC,KAAKpB,WAAN,EAAmBqB,MAAM,IAAIA,MAAM,CAAC3D,KAAP,EAA7B,CAAd;EACH;EACD;AACJ;AACA;AACA;;;EACI+C,aAAa,CAACnD,EAAD,EAAK;IACd,OAAO,KAAK0C,WAAL,CAAiBsB,IAAjB,CAAsBD,MAAM,IAAIA,MAAM,CAAC/D,EAAP,KAAcA,EAA9C,CAAP;EACH;;EACD9D,WAAW,GAAG;IACV;IACA;IACA;IACA4H,cAAc,CAAC,KAAK1B,uBAAN,EAA+B2B,MAAM,IAAI;MACnD;MACA,IAAIA,MAAM,CAACnE,MAAP,CAAcpF,cAAd,KAAiC,KAArC,EAA4C;QACxC,KAAKoJ,iBAAL,CAAuBG,MAAvB,EAA+B,KAA/B;MACH;IACJ,CALa,CAAd,CAJU,CAUV;IACA;IACA;;IACAD,cAAc,CAAC,KAAK1B,uBAAN,EAA+B2B,MAAM,IAAIA,MAAM,CAAC3D,KAAP,EAAzC,CAAd;;IACA,KAAKiC,0BAAL,CAAgC3B,QAAhC;;IACA,KAAK4B,uBAAL,CAA6B5B,QAA7B;;IACA,KAAK0B,uBAAL,GAA+B,EAA/B;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIiB,iBAAiB,CAACzD,MAAD,EAAS;IACtB,MAAMqE,KAAK,GAAG,IAAI1M,aAAJ,CAAkB;MAC5B2M,gBAAgB,EAAEtE,MAAM,CAACsE,gBAAP,IACd,KAAKpC,QAAL,CAAcqC,QAAd,GAAyBC,MAAzB,GAAkCC,kBAAlC,GAAuDC,gBAAvD,EAFwB;MAG5BnC,cAAc,EAAEvC,MAAM,CAACuC,cAAP,IAAyB,KAAKW,eAAL,EAHb;MAI5BpJ,UAAU,EAAEkG,MAAM,CAAClG,UAJS;MAK5BC,WAAW,EAAEiG,MAAM,CAACjG,WALQ;MAM5B4K,SAAS,EAAE3E,MAAM,CAAC2E,SANU;MAO5BC,QAAQ,EAAE5E,MAAM,CAAC4E,QAPW;MAQ5BC,SAAS,EAAE7E,MAAM,CAAC6E,SARU;MAS5BC,QAAQ,EAAE9E,MAAM,CAAC8E,QATW;MAU5BC,SAAS,EAAE/E,MAAM,CAAC+E,SAVU;MAW5B7K,KAAK,EAAE8F,MAAM,CAAC9F,KAXc;MAY5BC,MAAM,EAAE6F,MAAM,CAAC7F,MAZa;MAa5B6K,mBAAmB,EAAEhF,MAAM,CAACrF;IAbA,CAAlB,CAAd;;IAeA,IAAIqF,MAAM,CAAChG,aAAX,EAA0B;MACtBqK,KAAK,CAACrK,aAAN,GAAsBgG,MAAM,CAAChG,aAA7B;IACH;;IACD,OAAOqK,KAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIT,gBAAgB,CAACnC,OAAD,EAAUiC,SAAV,EAAqB1D,MAArB,EAA6B;IACzC,MAAMiF,YAAY,GAAGjF,MAAM,CAACkF,QAAP,IAAmBlF,MAAM,CAACmF,gBAAP,EAAyBD,QAAjE;IACA,MAAME,SAAS,GAAG,CACd;MAAEvD,OAAO,EAAElI,YAAX;MAAyB0L,QAAQ,EAAErF;IAAnC,CADc,EAEd;MAAE6B,OAAO,EAAE/B,SAAX;MAAsBuF,QAAQ,EAAE3B;IAAhC,CAFc,EAGd;MAAE7B,OAAO,EAAEjK,UAAX;MAAuByN,QAAQ,EAAE5D;IAAjC,CAHc,CAAlB;IAKA,IAAI6D,aAAJ;;IACA,IAAItF,MAAM,CAACuF,SAAX,EAAsB;MAClB,IAAI,OAAOvF,MAAM,CAACuF,SAAd,KAA4B,UAAhC,EAA4C;QACxCD,aAAa,GAAGtF,MAAM,CAACuF,SAAvB;MACH,CAFD,MAGK;QACDD,aAAa,GAAGtF,MAAM,CAACuF,SAAP,CAAiBrG,IAAjC;QACAkG,SAAS,CAACrB,IAAV,CAAe,GAAG/D,MAAM,CAACuF,SAAP,CAAiBH,SAAjB,CAA2BpF,MAA3B,CAAlB;MACH;IACJ,CARD,MASK;MACDsF,aAAa,GAAGvK,kBAAhB;IACH;;IACD,MAAMyK,eAAe,GAAG,IAAItN,eAAJ,CAAoBoN,aAApB,EAAmCtF,MAAM,CAACmF,gBAA1C,EAA4DrM,QAAQ,CAAC0F,MAAT,CAAgB;MAAEiH,MAAM,EAAER,YAAY,IAAI,KAAK9C,SAA/B;MAA0CiD;IAA1C,CAAhB,CAA5D,EAAoIpF,MAAM,CAAC0F,wBAA3I,CAAxB;IACA,MAAMC,YAAY,GAAGlE,OAAO,CAACmE,MAAR,CAAeJ,eAAf,CAArB;IACA,OAAOG,YAAY,CAACE,QAApB;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIhC,oBAAoB,CAACR,sBAAD,EAAyBK,SAAzB,EAAoCC,eAApC,EAAqD3D,MAArD,EAA6D;IAC7E,IAAIqD,sBAAsB,YAAYtK,WAAtC,EAAmD;MAC/C,MAAMmM,QAAQ,GAAG,KAAKY,eAAL,CAAqB9F,MAArB,EAA6B0D,SAA7B,EAAwCC,eAAxC,EAAyDhE,SAAzD,CAAjB;;MACA,IAAIoG,OAAO,GAAG;QAAEC,SAAS,EAAEhG,MAAM,CAAC5F,IAApB;QAA0BsJ;MAA1B,CAAd;;MACA,IAAI1D,MAAM,CAACiG,eAAX,EAA4B;QACxBF,OAAO,GAAG,EACN,GAAGA,OADG;UAEN,IAAI,OAAO/F,MAAM,CAACiG,eAAd,KAAkC,UAAlC,GACEjG,MAAM,CAACiG,eAAP,EADF,GAEEjG,MAAM,CAACiG,eAFb;QAFM,CAAV;MAMH;;MACDtC,eAAe,CAAClH,oBAAhB,CAAqC,IAAItE,cAAJ,CAAmBkL,sBAAnB,EAA2C,IAA3C,EAAiD0C,OAAjD,EAA0Db,QAA1D,CAArC;IACH,CAZD,MAaK;MACD,MAAMA,QAAQ,GAAG,KAAKY,eAAL,CAAqB9F,MAArB,EAA6B0D,SAA7B,EAAwCC,eAAxC,EAAyD,KAAKxB,SAA9D,CAAjB;;MACA,MAAM+D,UAAU,GAAGvC,eAAe,CAACnH,qBAAhB,CAAsC,IAAItE,eAAJ,CAAoBmL,sBAApB,EAA4CrD,MAAM,CAACmF,gBAAnD,EAAqED,QAArE,EAA+ElF,MAAM,CAAC0F,wBAAtF,CAAtC,CAAnB;MACAhC,SAAS,CAAC3C,iBAAV,GAA8BmF,UAAU,CAACL,QAAzC;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIC,eAAe,CAAC9F,MAAD,EAAS0D,SAAT,EAAoBC,eAApB,EAAqCwC,gBAArC,EAAuD;IAClE,MAAMlB,YAAY,GAAGjF,MAAM,CAACkF,QAAP,IAAmBlF,MAAM,CAACmF,gBAAP,EAAyBD,QAAjE;IACA,MAAME,SAAS,GAAG,CACd;MAAEvD,OAAO,EAAEP,WAAX;MAAwB+D,QAAQ,EAAErF,MAAM,CAAC5F;IAAzC,CADc,EAEd;MAAEyH,OAAO,EAAE/B,SAAX;MAAsBuF,QAAQ,EAAE3B;IAAhC,CAFc,CAAlB;;IAIA,IAAI1D,MAAM,CAACoF,SAAX,EAAsB;MAClB,IAAI,OAAOpF,MAAM,CAACoF,SAAd,KAA4B,UAAhC,EAA4C;QACxCA,SAAS,CAACrB,IAAV,CAAe,GAAG/D,MAAM,CAACoF,SAAP,CAAiB1B,SAAjB,EAA4B1D,MAA5B,EAAoC2D,eAApC,CAAlB;MACH,CAFD,MAGK;QACDyB,SAAS,CAACrB,IAAV,CAAe,GAAG/D,MAAM,CAACoF,SAAzB;MACH;IACJ;;IACD,IAAIpF,MAAM,CAAC2E,SAAP,KACC,CAACM,YAAD,IACG,CAACA,YAAY,CAACmB,GAAb,CAAiB3M,cAAjB,EAAiC,IAAjC,EAAuCT,WAAW,CAACN,QAAnD,CAFL,CAAJ,EAEwE;MACpE0M,SAAS,CAACrB,IAAV,CAAe;QACXlC,OAAO,EAAEpI,cADE;QAEX4L,QAAQ,EAAE;UAAEgB,KAAK,EAAErG,MAAM,CAAC2E,SAAhB;UAA2B2B,MAAM,EAAE9M,EAAE;QAArC;MAFC,CAAf;IAIH;;IACD,OAAOV,QAAQ,CAAC0F,MAAT,CAAgB;MAAEiH,MAAM,EAAER,YAAY,IAAIkB,gBAA1B;MAA4Cf;IAA5C,CAAhB,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIpB,iBAAiB,CAACN,SAAD,EAAY6C,SAAZ,EAAuB;IACpC,MAAMC,KAAK,GAAG,KAAK1D,WAAL,CAAiB2D,OAAjB,CAAyB/C,SAAzB,CAAd;;IACA,IAAI8C,KAAK,GAAG,CAAC,CAAb,EAAgB;MACZ,KAAK1D,WAAL,CAAiB4D,MAAjB,CAAwBF,KAAxB,EAA+B,CAA/B,EADY,CAEZ;MACA;;MACA,IAAI,CAAC,KAAK1D,WAAL,CAAiBC,MAAtB,EAA8B;QAC1B,KAAKJ,mBAAL,CAAyBgE,OAAzB,CAAiC,CAACC,aAAD,EAAgB/J,OAAhB,KAA4B;UACzD,IAAI+J,aAAJ,EAAmB;YACf/J,OAAO,CAACgK,YAAR,CAAqB,aAArB,EAAoCD,aAApC;UACH,CAFD,MAGK;YACD/J,OAAO,CAACO,eAAR,CAAwB,aAAxB;UACH;QACJ,CAPD;;QAQA,KAAKuF,mBAAL,CAAyBmE,KAAzB;;QACA,IAAIP,SAAJ,EAAe;UACX,KAAKvD,kBAAL,GAA0BnC,IAA1B;QACH;MACJ;IACJ;EACJ;EACD;;;EACAiD,4CAA4C,GAAG;IAC3C,MAAMiD,gBAAgB,GAAG,KAAKzE,iBAAL,CAAuB0E,mBAAvB,EAAzB,CAD2C,CAE3C;;;IACA,IAAID,gBAAgB,CAACE,aAArB,EAAoC;MAChC,MAAMC,QAAQ,GAAGH,gBAAgB,CAACE,aAAjB,CAA+BE,QAAhD;;MACA,KAAK,IAAIC,CAAC,GAAGF,QAAQ,CAACnE,MAAT,GAAkB,CAA/B,EAAkCqE,CAAC,GAAG,CAAC,CAAvC,EAA0CA,CAAC,EAA3C,EAA+C;QAC3C,MAAMC,OAAO,GAAGH,QAAQ,CAACE,CAAD,CAAxB;;QACA,IAAIC,OAAO,KAAKN,gBAAZ,IACAM,OAAO,CAACC,QAAR,KAAqB,QADrB,IAEAD,OAAO,CAACC,QAAR,KAAqB,OAFrB,IAGA,CAACD,OAAO,CAACE,YAAR,CAAqB,WAArB,CAHL,EAGwC;UACpC,KAAK5E,mBAAL,CAAyB6E,GAAzB,CAA6BH,OAA7B,EAAsCA,OAAO,CAACI,YAAR,CAAqB,aAArB,CAAtC;;UACAJ,OAAO,CAACR,YAAR,CAAqB,aAArB,EAAoC,MAApC;QACH;MACJ;IACJ;EACJ;;EACD7D,kBAAkB,GAAG;IACjB,MAAMyC,MAAM,GAAG,KAAKpD,aAApB;IACA,OAAOoD,MAAM,GAAGA,MAAM,CAACzC,kBAAP,EAAH,GAAiC,KAAKP,0BAAnD;EACH;;AAzPQ;;AA2PbR,MAAM,CAACtD,IAAP;EAAA,iBAAmGsD,MAAnG,EAjYqG3J,EAiYrG,UAA2Hb,IAAI,CAACC,OAAhI,GAjYqGY,EAiYrG,UAAoJA,EAAE,CAACQ,QAAvJ,GAjYqGR,EAiYrG,UAA4KiJ,qBAA5K,MAjYqGjJ,EAiYrG,UAA8N2J,MAA9N,OAjYqG3J,EAiYrG,UAAiRb,IAAI,CAACiQ,gBAAtR,GAjYqGpP,EAiYrG,UAAmT+I,sBAAnT;AAAA;;AACAY,MAAM,CAAC0F,KAAP,kBAlYqGrP,EAkYrG;EAAA,OAAuG2J,MAAvG;EAAA,SAAuGA,MAAvG;AAAA;;AACA;EAAA,mDAnYqG3J,EAmYrG,mBAA2F2J,MAA3F,EAA+G,CAAC;IACpG/C,IAAI,EAAEjG;EAD8F,CAAD,CAA/G,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAEiG,IAAI,EAAEzH,IAAI,CAACC;IAAb,CAAD,EAAyB;MAAEwH,IAAI,EAAE5G,EAAE,CAACQ;IAAX,CAAzB,EAAgD;MAAEoG,IAAI,EAAEvF,YAAR;MAAsBiG,UAAU,EAAE,CAAC;QAChHV,IAAI,EAAExG;MAD0G,CAAD,EAEhH;QACCwG,IAAI,EAAEvG,MADP;QAECwG,IAAI,EAAE,CAACoC,qBAAD;MAFP,CAFgH;IAAlC,CAAhD,EAK3B;MAAErC,IAAI,EAAE+C,MAAR;MAAgBrC,UAAU,EAAE,CAAC;QAC/BV,IAAI,EAAExG;MADyB,CAAD,EAE/B;QACCwG,IAAI,EAAEhG;MADP,CAF+B;IAA5B,CAL2B,EAS3B;MAAEgG,IAAI,EAAEzH,IAAI,CAACiQ;IAAb,CAT2B,EASM;MAAExI,IAAI,EAAES,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACnEV,IAAI,EAAEvG,MAD6D;QAEnEwG,IAAI,EAAE,CAACkC,sBAAD;MAF6D,CAAD;IAA/B,CATN,CAAP;EAYlB,CAdxB;AAAA;AAeA;AACA;AACA;AACA;;;AACA,SAAS6C,cAAT,CAAwB0D,KAAxB,EAA+B1K,QAA/B,EAAyC;EACrC,IAAIkK,CAAC,GAAGQ,KAAK,CAAC7E,MAAd;;EACA,OAAOqE,CAAC,EAAR,EAAY;IACRlK,QAAQ,CAAC0K,KAAK,CAACR,CAAD,CAAN,CAAR;EACH;AACJ;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMS,YAAN,CAAmB;;AAEnBA,YAAY,CAAClJ,IAAb;EAAA,iBAAyGkJ,YAAzG;AAAA;;AACAA,YAAY,CAACC,IAAb,kBAvaqGxP,EAuarG;EAAA,MAA0GuP;AAA1G;AAKAA,YAAY,CAACE,IAAb,kBA5aqGzP,EA4arG;EAAA,WAAmI,CAAC2J,MAAD,EAASL,+BAAT,CAAnI;EAAA,UAAwL/J,aAAxL,EAAuMO,YAAvM,EAAqNZ,UAArN,EACQ;EACA;EACAY,YAHR;AAAA;;AAIA;EAAA,mDAhbqGE,EAgbrG,mBAA2FuP,YAA3F,EAAqH,CAAC;IAC1G3I,IAAI,EAAE/F,QADoG;IAE1GgG,IAAI,EAAE,CAAC;MACC6I,OAAO,EAAE,CAACnQ,aAAD,EAAgBO,YAAhB,EAA8BZ,UAA9B,CADV;MAECyQ,OAAO,EAAE,CACL;MACA;MACA7P,YAHK,EAIL2C,kBAJK,CAFV;MAQCmN,YAAY,EAAE,CAACnN,kBAAD,CARf;MASCqK,SAAS,EAAE,CAACnD,MAAD,EAASL,+BAAT;IATZ,CAAD;EAFoG,CAAD,CAArH;AAAA;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAAS7G,kBAAT,EAA6BwG,qBAA7B,EAAoDD,WAApD,EAAiED,sBAAjE,EAAyFO,+BAAzF,EAA0HJ,uCAA1H,EAAmKS,MAAnK,EAA2KtI,YAA3K,EAAyLkO,YAAzL,EAAuM/H,SAAvM,EAAkNjF,sCAAlN", "ignoreList": []}, "metadata": {}, "sourceType": "module"}