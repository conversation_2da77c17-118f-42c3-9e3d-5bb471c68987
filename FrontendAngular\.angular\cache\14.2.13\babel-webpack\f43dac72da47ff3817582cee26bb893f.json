{"ast": null, "code": "import { identity } from '../util/identity';\nimport { isScheduler } from '../util/isScheduler';\nimport { defer } from './defer';\nimport { scheduleIterable } from '../scheduled/scheduleIterable';\nexport function generate(initialStateOrOptions, condition, iterate, resultSelectorOrScheduler, scheduler) {\n  let resultSelector;\n  let initialState;\n\n  if (arguments.length === 1) {\n    ({\n      initialState,\n      condition,\n      iterate,\n      resultSelector = identity,\n      scheduler\n    } = initialStateOrOptions);\n  } else {\n    initialState = initialStateOrOptions;\n\n    if (!resultSelectorOrScheduler || isScheduler(resultSelectorOrScheduler)) {\n      resultSelector = identity;\n      scheduler = resultSelectorOrScheduler;\n    } else {\n      resultSelector = resultSelectorOrScheduler;\n    }\n  }\n\n  function* gen() {\n    for (let state = initialState; !condition || condition(state); state = iterate(state)) {\n      yield resultSelector(state);\n    }\n  }\n\n  return defer(scheduler ? () => scheduleIterable(gen(), scheduler) : gen);\n}", "map": {"version": 3, "names": ["identity", "isScheduler", "defer", "scheduleIterable", "generate", "initialStateOrOptions", "condition", "iterate", "resultSelectorOrScheduler", "scheduler", "resultSelector", "initialState", "arguments", "length", "gen", "state"], "sources": ["R:/chateye/FrontendAngular/node_modules/rxjs/dist/esm/internal/observable/generate.js"], "sourcesContent": ["import { identity } from '../util/identity';\nimport { isScheduler } from '../util/isScheduler';\nimport { defer } from './defer';\nimport { scheduleIterable } from '../scheduled/scheduleIterable';\nexport function generate(initialStateOrOptions, condition, iterate, resultSelectorOrScheduler, scheduler) {\n    let resultSelector;\n    let initialState;\n    if (arguments.length === 1) {\n        ({\n            initialState,\n            condition,\n            iterate,\n            resultSelector = identity,\n            scheduler,\n        } = initialStateOrOptions);\n    }\n    else {\n        initialState = initialStateOrOptions;\n        if (!resultSelectorOrScheduler || isScheduler(resultSelectorOrScheduler)) {\n            resultSelector = identity;\n            scheduler = resultSelectorOrScheduler;\n        }\n        else {\n            resultSelector = resultSelectorOrScheduler;\n        }\n    }\n    function* gen() {\n        for (let state = initialState; !condition || condition(state); state = iterate(state)) {\n            yield resultSelector(state);\n        }\n    }\n    return defer((scheduler\n        ?\n            () => scheduleIterable(gen(), scheduler)\n        :\n            gen));\n}\n"], "mappings": "AAAA,SAASA,QAAT,QAAyB,kBAAzB;AACA,SAASC,WAAT,QAA4B,qBAA5B;AACA,SAASC,KAAT,QAAsB,SAAtB;AACA,SAASC,gBAAT,QAAiC,+BAAjC;AACA,OAAO,SAASC,QAAT,CAAkBC,qBAAlB,EAAyCC,SAAzC,EAAoDC,OAApD,EAA6DC,yBAA7D,EAAwFC,SAAxF,EAAmG;EACtG,IAAIC,cAAJ;EACA,IAAIC,YAAJ;;EACA,IAAIC,SAAS,CAACC,MAAV,KAAqB,CAAzB,EAA4B;IACxB,CAAC;MACGF,YADH;MAEGL,SAFH;MAGGC,OAHH;MAIGG,cAAc,GAAGV,QAJpB;MAKGS;IALH,IAMGJ,qBANJ;EAOH,CARD,MASK;IACDM,YAAY,GAAGN,qBAAf;;IACA,IAAI,CAACG,yBAAD,IAA8BP,WAAW,CAACO,yBAAD,CAA7C,EAA0E;MACtEE,cAAc,GAAGV,QAAjB;MACAS,SAAS,GAAGD,yBAAZ;IACH,CAHD,MAIK;MACDE,cAAc,GAAGF,yBAAjB;IACH;EACJ;;EACD,UAAUM,GAAV,GAAgB;IACZ,KAAK,IAAIC,KAAK,GAAGJ,YAAjB,EAA+B,CAACL,SAAD,IAAcA,SAAS,CAACS,KAAD,CAAtD,EAA+DA,KAAK,GAAGR,OAAO,CAACQ,KAAD,CAA9E,EAAuF;MACnF,MAAML,cAAc,CAACK,KAAD,CAApB;IACH;EACJ;;EACD,OAAOb,KAAK,CAAEO,SAAS,GAEf,MAAMN,gBAAgB,CAACW,GAAG,EAAJ,EAAQL,SAAR,CAFP,GAIfK,GAJI,CAAZ;AAKH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}