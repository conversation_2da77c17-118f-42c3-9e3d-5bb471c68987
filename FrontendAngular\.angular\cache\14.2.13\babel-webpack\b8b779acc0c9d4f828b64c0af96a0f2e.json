{"ast": null, "code": "import { isFunction } from '../util/isFunction';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { identity } from '../util/identity';\nexport function tap(observerOrNext, error, complete) {\n  const tapObserver = isFunction(observerOrNext) || error || complete ? {\n    next: observerOrNext,\n    error,\n    complete\n  } : observerOrNext;\n  return tapObserver ? operate((source, subscriber) => {\n    var _a;\n\n    (_a = tapObserver.subscribe) === null || _a === void 0 ? void 0 : _a.call(tapObserver);\n    let isUnsub = true;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      var _a;\n\n      (_a = tapObserver.next) === null || _a === void 0 ? void 0 : _a.call(tapObserver, value);\n      subscriber.next(value);\n    }, () => {\n      var _a;\n\n      isUnsub = false;\n      (_a = tapObserver.complete) === null || _a === void 0 ? void 0 : _a.call(tapObserver);\n      subscriber.complete();\n    }, err => {\n      var _a;\n\n      isUnsub = false;\n      (_a = tapObserver.error) === null || _a === void 0 ? void 0 : _a.call(tapObserver, err);\n      subscriber.error(err);\n    }, () => {\n      var _a, _b;\n\n      if (isUnsub) {\n        (_a = tapObserver.unsubscribe) === null || _a === void 0 ? void 0 : _a.call(tapObserver);\n      }\n\n      (_b = tapObserver.finalize) === null || _b === void 0 ? void 0 : _b.call(tapObserver);\n    }));\n  }) : identity;\n}", "map": {"version": 3, "names": ["isFunction", "operate", "createOperatorSubscriber", "identity", "tap", "observerOrNext", "error", "complete", "tapObserver", "next", "source", "subscriber", "_a", "subscribe", "call", "isUnsub", "value", "err", "_b", "unsubscribe", "finalize"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/rxjs/dist/esm/internal/operators/tap.js"], "sourcesContent": ["import { isFunction } from '../util/isFunction';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { identity } from '../util/identity';\nexport function tap(observerOrNext, error, complete) {\n    const tapObserver = isFunction(observerOrNext) || error || complete\n        ?\n            { next: observerOrNext, error, complete }\n        : observerOrNext;\n    return tapObserver\n        ? operate((source, subscriber) => {\n            var _a;\n            (_a = tapObserver.subscribe) === null || _a === void 0 ? void 0 : _a.call(tapObserver);\n            let isUnsub = true;\n            source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n                var _a;\n                (_a = tapObserver.next) === null || _a === void 0 ? void 0 : _a.call(tapObserver, value);\n                subscriber.next(value);\n            }, () => {\n                var _a;\n                isUnsub = false;\n                (_a = tapObserver.complete) === null || _a === void 0 ? void 0 : _a.call(tapObserver);\n                subscriber.complete();\n            }, (err) => {\n                var _a;\n                isUnsub = false;\n                (_a = tapObserver.error) === null || _a === void 0 ? void 0 : _a.call(tapObserver, err);\n                subscriber.error(err);\n            }, () => {\n                var _a, _b;\n                if (isUnsub) {\n                    (_a = tapObserver.unsubscribe) === null || _a === void 0 ? void 0 : _a.call(tapObserver);\n                }\n                (_b = tapObserver.finalize) === null || _b === void 0 ? void 0 : _b.call(tapObserver);\n            }));\n        })\n        :\n            identity;\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,oBAA3B;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,SAASC,QAAT,QAAyB,kBAAzB;AACA,OAAO,SAASC,GAAT,CAAaC,cAAb,EAA6BC,KAA7B,EAAoCC,QAApC,EAA8C;EACjD,MAAMC,WAAW,GAAGR,UAAU,CAACK,cAAD,CAAV,IAA8BC,KAA9B,IAAuCC,QAAvC,GAEZ;IAAEE,IAAI,EAAEJ,cAAR;IAAwBC,KAAxB;IAA+BC;EAA/B,CAFY,GAGdF,cAHN;EAIA,OAAOG,WAAW,GACZP,OAAO,CAAC,CAACS,MAAD,EAASC,UAAT,KAAwB;IAC9B,IAAIC,EAAJ;;IACA,CAACA,EAAE,GAAGJ,WAAW,CAACK,SAAlB,MAAiC,IAAjC,IAAyCD,EAAE,KAAK,KAAK,CAArD,GAAyD,KAAK,CAA9D,GAAkEA,EAAE,CAACE,IAAH,CAAQN,WAAR,CAAlE;IACA,IAAIO,OAAO,GAAG,IAAd;IACAL,MAAM,CAACG,SAAP,CAAiBX,wBAAwB,CAACS,UAAD,EAAcK,KAAD,IAAW;MAC7D,IAAIJ,EAAJ;;MACA,CAACA,EAAE,GAAGJ,WAAW,CAACC,IAAlB,MAA4B,IAA5B,IAAoCG,EAAE,KAAK,KAAK,CAAhD,GAAoD,KAAK,CAAzD,GAA6DA,EAAE,CAACE,IAAH,CAAQN,WAAR,EAAqBQ,KAArB,CAA7D;MACAL,UAAU,CAACF,IAAX,CAAgBO,KAAhB;IACH,CAJwC,EAItC,MAAM;MACL,IAAIJ,EAAJ;;MACAG,OAAO,GAAG,KAAV;MACA,CAACH,EAAE,GAAGJ,WAAW,CAACD,QAAlB,MAAgC,IAAhC,IAAwCK,EAAE,KAAK,KAAK,CAApD,GAAwD,KAAK,CAA7D,GAAiEA,EAAE,CAACE,IAAH,CAAQN,WAAR,CAAjE;MACAG,UAAU,CAACJ,QAAX;IACH,CATwC,EASrCU,GAAD,IAAS;MACR,IAAIL,EAAJ;;MACAG,OAAO,GAAG,KAAV;MACA,CAACH,EAAE,GAAGJ,WAAW,CAACF,KAAlB,MAA6B,IAA7B,IAAqCM,EAAE,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,EAAE,CAACE,IAAH,CAAQN,WAAR,EAAqBS,GAArB,CAA9D;MACAN,UAAU,CAACL,KAAX,CAAiBW,GAAjB;IACH,CAdwC,EActC,MAAM;MACL,IAAIL,EAAJ,EAAQM,EAAR;;MACA,IAAIH,OAAJ,EAAa;QACT,CAACH,EAAE,GAAGJ,WAAW,CAACW,WAAlB,MAAmC,IAAnC,IAA2CP,EAAE,KAAK,KAAK,CAAvD,GAA2D,KAAK,CAAhE,GAAoEA,EAAE,CAACE,IAAH,CAAQN,WAAR,CAApE;MACH;;MACD,CAACU,EAAE,GAAGV,WAAW,CAACY,QAAlB,MAAgC,IAAhC,IAAwCF,EAAE,KAAK,KAAK,CAApD,GAAwD,KAAK,CAA7D,GAAiEA,EAAE,CAACJ,IAAH,CAAQN,WAAR,CAAjE;IACH,CApBwC,CAAzC;EAqBH,CAzBQ,CADK,GA4BVL,QA5BR;AA6BH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}