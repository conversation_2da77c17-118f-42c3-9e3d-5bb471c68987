{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"../emoji-picker/emoji-picker.component\";\n\nfunction MessageComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"p\", 8);\n    i0.ɵɵtext(2, \" Replying to \");\n    i0.ɵɵelementStart(3, \"span\", 9);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"p\", 10);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.message.replyUsername);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.message.replyText, \" \");\n  }\n}\n\nfunction MessageComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"span\", 12);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 13);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.message.username, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.formatTime(ctx_r1.message.timestamp), \" \");\n  }\n}\n\nfunction MessageComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"span\", 15);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatTime(ctx_r2.message.timestamp), \" \");\n  }\n}\n\nfunction MessageComponent_div_7_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function MessageComponent_div_7_button_1_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const reaction_r7 = restoredCtx.$implicit;\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.onReactionClick(reaction_r7));\n    });\n    i0.ɵɵelementStart(1, \"span\", 19);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const reaction_r7 = ctx.$implicit;\n    i0.ɵɵclassMap(\"inline-flex items-center px-2 py-1 rounded-full text-xs border transition-colors duration-200 \" + (reaction_r7.userReacted ? \"bg-primary-100 border-primary-300 text-primary-700\" : \"bg-gray-100 border-gray-300 text-gray-700 hover:bg-gray-200\"));\n    i0.ɵɵproperty(\"title\", reaction_r7.users.join(\", \") + \" reacted with \" + reaction_r7.emoji);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(reaction_r7.emoji);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(reaction_r7.count);\n  }\n}\n\nfunction MessageComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵtemplate(1, MessageComponent_div_7_button_1_Template, 5, 5, \"button\", 17);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.getReactionValues());\n  }\n}\n\nfunction MessageComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function MessageComponent_div_8_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onReplyClick());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 21);\n    i0.ɵɵelement(3, \"path\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function MessageComponent_div_8_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.onEmojiClick());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(5, \"svg\", 21);\n    i0.ɵɵelement(6, \"path\", 24);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(\"absolute top-0 \" + (ctx_r4.isOwnMessage ? \"left-0 -translate-x-full\" : \"right-0 translate-x-full\") + \" flex items-center space-x-1 bg-white shadow-lg rounded-lg border border-gray-200 px-2 py-1\");\n  }\n}\n\nfunction MessageComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"app-emoji-picker\", 26);\n    i0.ɵɵlistener(\"onEmojiSelect\", function MessageComponent_div_9_Template_app_emoji_picker_onEmojiSelect_1_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.onEmojiSelect($event));\n    })(\"onClose\", function MessageComponent_div_9_Template_app_emoji_picker_onClose_1_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.onEmojiClose());\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\n\nexport let MessageComponent = /*#__PURE__*/(() => {\n  class MessageComponent {\n    constructor() {\n      this.currentUser = null;\n      this.onReply = new EventEmitter();\n      this.onAddReaction = new EventEmitter();\n      this.onRemoveReaction = new EventEmitter();\n      this.showEmojiPicker = false;\n      this.showActions = false;\n    }\n\n    get isOwnMessage() {\n      return this.message.username === this.currentUser;\n    }\n\n    formatTime(timestamp) {\n      return new Date(timestamp).toLocaleTimeString('en-US', {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: false\n      });\n    }\n\n    onEmojiSelect(emoji) {\n      this.onAddReaction.emit({\n        messageId: this.message.id,\n        emoji: emoji.native\n      });\n      this.showEmojiPicker = false;\n    }\n\n    onReactionClick(reaction) {\n      const userReacted = reaction.user_id === this.currentUser || reaction.username === this.currentUser;\n\n      if (userReacted) {\n        this.onRemoveReaction.emit(this.message.id);\n      } else {\n        this.onAddReaction.emit({\n          messageId: this.message.id,\n          emoji: reaction.emoji\n        });\n      }\n    }\n\n    get groupedReactions() {\n      if (!this.message.reactions) return {};\n      return this.message.reactions.reduce((acc, reaction) => {\n        if (!acc[reaction.emoji]) {\n          acc[reaction.emoji] = {\n            emoji: reaction.emoji,\n            count: 0,\n            users: [],\n            userReacted: false\n          };\n        }\n\n        acc[reaction.emoji].count++;\n        acc[reaction.emoji].users.push(reaction.username);\n\n        if (reaction.username === this.currentUser) {\n          acc[reaction.emoji].userReacted = true;\n        }\n\n        return acc;\n      }, {});\n    }\n\n    onMouseEnter() {\n      this.showActions = true;\n    }\n\n    onMouseLeave() {\n      this.showActions = false;\n    }\n\n    onReplyClick() {\n      this.onReply.emit(this.message);\n    }\n\n    onEmojiClick() {\n      this.showEmojiPicker = !this.showEmojiPicker;\n    }\n\n    onEmojiClose() {\n      this.showEmojiPicker = false;\n    }\n\n    getReactionKeys() {\n      return Object.keys(this.groupedReactions);\n    }\n\n    getReactionValues() {\n      return Object.values(this.groupedReactions);\n    }\n\n  }\n\n  MessageComponent.ɵfac = function MessageComponent_Factory(t) {\n    return new (t || MessageComponent)();\n  };\n\n  MessageComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: MessageComponent,\n    selectors: [[\"app-message\"]],\n    inputs: {\n      message: \"message\",\n      currentUser: \"currentUser\"\n    },\n    outputs: {\n      onReply: \"onReply\",\n      onAddReaction: \"onAddReaction\",\n      onRemoveReaction: \"onRemoveReaction\"\n    },\n    decls: 10,\n    vars: 13,\n    consts: [[3, \"mouseenter\", \"mouseleave\"], [\"class\", \"mb-1 pl-3 border-l-2 border-gray-300\", 4, \"ngIf\"], [\"class\", \"flex items-center justify-between mb-1\", 4, \"ngIf\"], [\"class\", \"text-right mt-1\", 4, \"ngIf\"], [\"class\", \"flex flex-wrap gap-1 mt-1 ml-3\", 4, \"ngIf\"], [3, \"class\", 4, \"ngIf\"], [\"class\", \"absolute top-8 right-0 z-50\", 4, \"ngIf\"], [1, \"mb-1\", \"pl-3\", \"border-l-2\", \"border-gray-300\"], [1, \"text-xs\", \"text-gray-500\"], [1, \"font-medium\"], [1, \"text-xs\", \"text-gray-600\", \"truncate\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-1\"], [1, \"text-xs\", \"font-medium\", \"text-gray-600\"], [1, \"text-xs\", \"text-gray-400\"], [1, \"text-right\", \"mt-1\"], [1, \"text-xs\", \"text-primary-200\"], [1, \"flex\", \"flex-wrap\", \"gap-1\", \"mt-1\", \"ml-3\"], [3, \"class\", \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [3, \"title\", \"click\"], [1, \"mr-1\"], [\"title\", \"Reply\", 1, \"p-1\", \"text-gray-500\", \"hover:text-gray-700\", \"hover:bg-gray-100\", \"rounded\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-3.5\", \"h-3.5\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6\"], [\"title\", \"React\", 1, \"p-1\", \"text-gray-500\", \"hover:text-gray-700\", \"hover:bg-gray-100\", \"rounded\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"absolute\", \"top-8\", \"right-0\", \"z-50\"], [3, \"onEmojiSelect\", \"onClose\"]],\n    template: function MessageComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵlistener(\"mouseenter\", function MessageComponent_Template_div_mouseenter_0_listener() {\n          return ctx.onMouseEnter();\n        })(\"mouseleave\", function MessageComponent_Template_div_mouseleave_0_listener() {\n          return ctx.onMouseLeave();\n        });\n        i0.ɵɵtemplate(1, MessageComponent_div_1_Template, 7, 2, \"div\", 1);\n        i0.ɵɵelementStart(2, \"div\");\n        i0.ɵɵtemplate(3, MessageComponent_div_3_Template, 5, 2, \"div\", 2);\n        i0.ɵɵelementStart(4, \"p\");\n        i0.ɵɵtext(5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(6, MessageComponent_div_6_Template, 3, 1, \"div\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(7, MessageComponent_div_7_Template, 2, 1, \"div\", 4);\n        i0.ɵɵtemplate(8, MessageComponent_div_8_Template, 7, 2, \"div\", 5);\n        i0.ɵɵtemplate(9, MessageComponent_div_9_Template, 2, 0, \"div\", 6);\n        i0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵclassMap(\"group relative \" + (ctx.isOwnMessage ? \"ml-auto\" : \"mr-auto\") + \" max-w-xs sm:max-w-md lg:max-w-lg\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.message.replyTo);\n        i0.ɵɵadvance(1);\n        i0.ɵɵclassMap(\"message-bubble \" + (ctx.isOwnMessage ? \"own-message\" : \"\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isOwnMessage);\n        i0.ɵɵadvance(1);\n        i0.ɵɵclassMap(\"text-sm \" + (ctx.isOwnMessage ? \"text-white\" : \"text-gray-900\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate1(\" \", ctx.message.text, \" \");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isOwnMessage);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.getReactionKeys().length > 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.showActions);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.showEmojiPicker);\n      }\n    },\n    dependencies: [i1.NgForOf, i1.NgIf, i2.EmojiPickerComponent]\n  });\n  return MessageComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}