{"ast": null, "code": "import { zip } from '../observable/zip';\nimport { joinAllInternals } from './joinAllInternals';\nexport function zipAll(project) {\n  return joinAllInternals(zip, project);\n}", "map": {"version": 3, "names": ["zip", "joinAllInternals", "zipAll", "project"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/rxjs/dist/esm/internal/operators/zipAll.js"], "sourcesContent": ["import { zip } from '../observable/zip';\nimport { joinAllInternals } from './joinAllInternals';\nexport function zipAll(project) {\n    return joinAllInternals(zip, project);\n}\n"], "mappings": "AAAA,SAASA,GAAT,QAAoB,mBAApB;AACA,SAASC,gBAAT,QAAiC,oBAAjC;AACA,OAAO,SAASC,MAAT,CAAgBC,OAAhB,EAAyB;EAC5B,OAAOF,gBAAgB,CAACD,GAAD,EAAMG,OAAN,CAAvB;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}