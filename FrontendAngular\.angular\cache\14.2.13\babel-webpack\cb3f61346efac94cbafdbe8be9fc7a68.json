{"ast": null, "code": "import { EventEmitter, ElementRef } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"../emoji-picker/emoji-picker.component\";\nconst _c0 = [\"emojiPicker\"];\n\nfunction MessageComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵelement(1, \"div\", 19);\n    i0.ɵɵelementStart(2, \"div\", 20)(3, \"span\", 21);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 22);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.message.replyUsername);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.message.replyText);\n  }\n}\n\nfunction MessageComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"span\", 24);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25)(4, \"span\", 26);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function MessageComponent_div_3_Template_button_click_6_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onEmojiClick($event));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(7, \"svg\", 28);\n    i0.ɵɵelement(8, \"path\", 14);\n    i0.ɵɵelementEnd()()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.message.username);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.formatTime(ctx_r1.message.timestamp));\n  }\n}\n\nfunction MessageComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"span\", 26);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function MessageComponent_div_7_Template_button_click_3_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onEmojiClick($event));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 28);\n    i0.ɵɵelement(5, \"path\", 14);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.formatTime(ctx_r2.message.timestamp));\n  }\n}\n\nfunction MessageComponent_div_8_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function MessageComponent_div_8_button_1_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r14);\n      const reaction_r12 = restoredCtx.$implicit;\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r13.onReactionClick(reaction_r12));\n    });\n    i0.ɵɵelementStart(1, \"span\", 33);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 34);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const reaction_r12 = ctx.$implicit;\n    i0.ɵɵclassProp(\"user-reacted\", reaction_r12.userReacted);\n    i0.ɵɵproperty(\"title\", reaction_r12.users.join(\", \") + \" reacted with \" + reaction_r12.emoji);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(reaction_r12.emoji);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(reaction_r12.count);\n  }\n}\n\nfunction MessageComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtemplate(1, MessageComponent_div_8_button_1_Template, 5, 5, \"button\", 31);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.getReactionValues());\n  }\n}\n\nfunction MessageComponent_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function MessageComponent_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.onEditClick());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 10);\n    i0.ɵɵelement(2, \"path\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\", 12);\n    i0.ɵɵtext(4, \"Edit\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction MessageComponent_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function MessageComponent_button_21_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.onDeleteClick());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 10);\n    i0.ɵɵelement(2, \"path\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\", 12);\n    i0.ɵɵtext(4, \"Delete\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction MessageComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 39, 40);\n    i0.ɵɵlistener(\"click\", function MessageComponent_div_22_Template_div_click_0_listener($event) {\n      return $event.stopPropagation();\n    });\n    i0.ɵɵelementStart(2, \"app-emoji-picker\", 41);\n    i0.ɵɵlistener(\"onEmojiSelect\", function MessageComponent_div_22_Template_app_emoji_picker_onEmojiSelect_2_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.onEmojiSelect($event));\n    })(\"onClose\", function MessageComponent_div_22_Template_app_emoji_picker_onClose_2_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.onEmojiClose());\n    });\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"own-picker\", ctx_r6.isOwnMessage);\n  }\n}\n\nexport let MessageComponent = /*#__PURE__*/(() => {\n  class MessageComponent {\n    constructor() {\n      this.currentUser = null;\n      this.onReply = new EventEmitter();\n      this.onAddReaction = new EventEmitter();\n      this.onRemoveReaction = new EventEmitter();\n      this.onEdit = new EventEmitter();\n      this.onDelete = new EventEmitter();\n      this.isOwnMessage = false;\n      this.showActions = false;\n      this.showEmojiPicker = false;\n    }\n\n    ngOnInit() {\n      this.isOwnMessage = this.message.username === this.currentUser;\n    }\n\n    ngOnDestroy() {\n      if (this.clickListener) {\n        document.removeEventListener('mousedown', this.clickListener);\n      }\n    }\n\n    formatTime(timestamp) {\n      const date = new Date(timestamp);\n      const now = new Date();\n      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n\n      if (diffInMinutes < 1) {\n        return 'now';\n      } else if (diffInMinutes < 60) {\n        return `${diffInMinutes}m`;\n      } else if (diffInMinutes < 1440) {\n        const hours = Math.floor(diffInMinutes / 60);\n        return `${hours}h`;\n      } else {\n        return date.toLocaleDateString('en-US', {\n          month: 'short',\n          day: 'numeric',\n          hour: 'numeric',\n          minute: '2-digit',\n          hour12: true\n        });\n      }\n    }\n\n    getReactionKeys() {\n      const reactions = this.message.reactions || [];\n      const emojiGroups = this.groupReactionsByEmoji(reactions);\n      return Object.keys(emojiGroups);\n    }\n\n    getReactionValues() {\n      const reactions = this.message.reactions || [];\n      const emojiGroups = this.groupReactionsByEmoji(reactions);\n      return Object.entries(emojiGroups).map(([emoji, data]) => ({\n        emoji,\n        count: data.count,\n        users: data.users || [],\n        userReacted: data.users?.includes(this.currentUser) || false\n      }));\n    }\n\n    groupReactionsByEmoji(reactions) {\n      const groups = {};\n      reactions.forEach(reaction => {\n        if (!groups[reaction.emoji]) {\n          groups[reaction.emoji] = {\n            count: 0,\n            users: []\n          };\n        }\n\n        groups[reaction.emoji].count++;\n        groups[reaction.emoji].users.push(reaction.username);\n      });\n      return groups;\n    }\n\n    onMouseEnter() {\n      this.showActions = true;\n    }\n\n    onMouseLeave() {\n      this.showActions = false; // Don't hide emoji picker on mouse leave to allow interaction\n    }\n\n    onReplyClick() {\n      this.onReply.emit(this.message);\n    }\n\n    onEmojiClick(event) {\n      event.stopPropagation();\n      this.showEmojiPicker = !this.showEmojiPicker;\n\n      if (this.showEmojiPicker) {\n        // Position the emoji picker near the click\n        setTimeout(() => {\n          if (this.emojiPicker) {\n            const rect = event.target.getBoundingClientRect();\n            const picker = this.emojiPicker.nativeElement; // Position above the button with proper spacing\n\n            picker.style.position = 'fixed';\n            picker.style.top = `${rect.top - 320}px`; // Fixed height above button\n\n            picker.style.left = `${rect.left - 160}px`; // Center the picker\n\n            picker.style.right = 'auto';\n            picker.style.transform = 'none';\n            picker.style.zIndex = '1000'; // Ensure it's within viewport bounds\n\n            const viewportWidth = window.innerWidth;\n            const viewportHeight = window.innerHeight;\n            const pickerRect = picker.getBoundingClientRect();\n\n            if (pickerRect.left < 10) {\n              picker.style.left = '10px';\n            }\n\n            if (pickerRect.right > viewportWidth - 10) {\n              picker.style.left = `${viewportWidth - pickerRect.width - 10}px`;\n            }\n\n            if (pickerRect.top < 10) {\n              picker.style.top = `${rect.bottom + 10}px`; // Show below if no space above\n            }\n          } // Set up click outside listener with delay\n\n\n          setTimeout(() => {\n            this.clickListener = event => {\n              if (this.emojiPicker && !this.emojiPicker.nativeElement.contains(event.target)) {\n                this.showEmojiPicker = false;\n\n                if (this.clickListener) {\n                  document.removeEventListener('mousedown', this.clickListener);\n                }\n              }\n            };\n\n            document.addEventListener('mousedown', this.clickListener);\n          }, 200); // Delay to prevent immediate closing\n        }, 50);\n      } else {\n        if (this.clickListener) {\n          document.removeEventListener('mousedown', this.clickListener);\n        }\n      }\n    }\n\n    onEmojiClose() {\n      this.showEmojiPicker = false;\n\n      if (this.clickListener) {\n        document.removeEventListener('mousedown', this.clickListener);\n      }\n    }\n\n    onEmojiSelect(emoji) {\n      this.onAddReaction.emit({\n        messageId: this.message.id,\n        emoji: emoji.native\n      });\n      this.showEmojiPicker = false;\n    }\n\n    onReactionClick(reaction) {\n      if (reaction.userReacted) {\n        // Remove the specific emoji reaction\n        this.onRemoveReaction.emit({\n          messageId: this.message.id,\n          emoji: reaction.emoji\n        });\n      } else {\n        // Add the emoji reaction\n        this.onAddReaction.emit({\n          messageId: this.message.id,\n          emoji: reaction.emoji\n        });\n      }\n    }\n\n    onRightClick(event) {\n      event.preventDefault(); // Show action buttons on right click\n\n      this.showActions = true; // Hide after 3 seconds\n\n      setTimeout(() => {\n        this.showActions = false;\n      }, 3000);\n    }\n\n    onEditClick() {\n      this.onEdit.emit(this.message);\n    }\n\n    onDeleteClick() {\n      if (confirm('Are you sure you want to delete this message?')) {\n        this.onDelete.emit(this.message);\n      }\n    }\n\n  }\n\n  MessageComponent.ɵfac = function MessageComponent_Factory(t) {\n    return new (t || MessageComponent)();\n  };\n\n  MessageComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: MessageComponent,\n    selectors: [[\"app-message\"]],\n    viewQuery: function MessageComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.emojiPicker = _t.first);\n      }\n    },\n    inputs: {\n      message: \"message\",\n      currentUser: \"currentUser\"\n    },\n    outputs: {\n      onReply: \"onReply\",\n      onAddReaction: \"onAddReaction\",\n      onRemoveReaction: \"onRemoveReaction\",\n      onEdit: \"onEdit\",\n      onDelete: \"onDelete\"\n    },\n    decls: 23,\n    vars: 16,\n    consts: [[1, \"fluid-message-container\", 3, \"contextmenu\"], [\"class\", \"fluid-reply-indicator\", 4, \"ngIf\"], [1, \"fluid-message-bubble\"], [\"class\", \"fluid-message-header\", 4, \"ngIf\"], [1, \"fluid-message-content\"], [1, \"message-text\"], [\"class\", \"fluid-message-footer\", 4, \"ngIf\"], [\"class\", \"fluid-reactions\", 4, \"ngIf\"], [1, \"fluid-action-buttons\"], [\"title\", \"Reply to this message\", 1, \"action-button\", 3, \"click\"], [\"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"stroke\", \"currentColor\", 1, \"action-icon\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6\"], [1, \"action-text\"], [\"title\", \"React to this message\", 1, \"action-button\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [\"class\", \"action-button edit-button\", \"title\", \"Edit this message\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"action-button delete-button\", \"title\", \"Delete this message\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"fluid-emoji-picker\", 3, \"own-picker\", \"click\", 4, \"ngIf\"], [1, \"fluid-reply-indicator\"], [1, \"reply-line\"], [1, \"reply-content\"], [1, \"reply-username\"], [1, \"reply-text\"], [1, \"fluid-message-header\"], [1, \"username\"], [1, \"header-actions\"], [1, \"timestamp\"], [\"title\", \"Quick react\", 1, \"quick-react-button\", 3, \"click\"], [\"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"stroke\", \"currentColor\", 1, \"quick-react-icon\"], [1, \"fluid-message-footer\"], [1, \"fluid-reactions\"], [\"class\", \"fluid-reaction-button\", 3, \"user-reacted\", \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"fluid-reaction-button\", 3, \"title\", \"click\"], [1, \"reaction-emoji\"], [1, \"reaction-count\"], [\"title\", \"Edit this message\", 1, \"action-button\", \"edit-button\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"], [\"title\", \"Delete this message\", 1, \"action-button\", \"delete-button\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"], [1, \"fluid-emoji-picker\", 3, \"click\"], [\"emojiPicker\", \"\"], [3, \"onEmojiSelect\", \"onClose\"]],\n    template: function MessageComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵlistener(\"contextmenu\", function MessageComponent_Template_div_contextmenu_0_listener($event) {\n          return ctx.onRightClick($event);\n        });\n        i0.ɵɵtemplate(1, MessageComponent_div_1_Template, 7, 2, \"div\", 1);\n        i0.ɵɵelementStart(2, \"div\", 2);\n        i0.ɵɵtemplate(3, MessageComponent_div_3_Template, 9, 2, \"div\", 3);\n        i0.ɵɵelementStart(4, \"div\", 4)(5, \"p\", 5);\n        i0.ɵɵtext(6);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(7, MessageComponent_div_7_Template, 6, 1, \"div\", 6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(8, MessageComponent_div_8_Template, 2, 1, \"div\", 7);\n        i0.ɵɵelementStart(9, \"div\", 8)(10, \"button\", 9);\n        i0.ɵɵlistener(\"click\", function MessageComponent_Template_button_click_10_listener() {\n          return ctx.onReplyClick();\n        });\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(11, \"svg\", 10);\n        i0.ɵɵelement(12, \"path\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵnamespaceHTML();\n        i0.ɵɵelementStart(13, \"span\", 12);\n        i0.ɵɵtext(14, \"Reply\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(15, \"button\", 13);\n        i0.ɵɵlistener(\"click\", function MessageComponent_Template_button_click_15_listener($event) {\n          return ctx.onEmojiClick($event);\n        });\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(16, \"svg\", 10);\n        i0.ɵɵelement(17, \"path\", 14);\n        i0.ɵɵelementEnd();\n        i0.ɵɵnamespaceHTML();\n        i0.ɵɵelementStart(18, \"span\", 12);\n        i0.ɵɵtext(19, \"React\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(20, MessageComponent_button_20_Template, 5, 0, \"button\", 15);\n        i0.ɵɵtemplate(21, MessageComponent_button_21_Template, 5, 0, \"button\", 16);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(22, MessageComponent_div_22_Template, 3, 2, \"div\", 17);\n        i0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"own-message\", ctx.isOwnMessage);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.message.replyTo);\n        i0.ɵɵadvance(1);\n        i0.ɵɵclassProp(\"own-bubble\", ctx.isOwnMessage);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isOwnMessage);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.message.text);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isOwnMessage);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.getReactionKeys().length > 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵclassProp(\"own-actions\", ctx.isOwnMessage)(\"visible\", ctx.showActions);\n        i0.ɵɵadvance(11);\n        i0.ɵɵproperty(\"ngIf\", ctx.isOwnMessage);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isOwnMessage);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.showEmojiPicker);\n      }\n    },\n    dependencies: [i1.NgForOf, i1.NgIf, i2.EmojiPickerComponent],\n    styles: [\".fluid-message-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;max-width:100%;margin-bottom:.75rem;position:relative;transition:all .3s ease}.fluid-message-container.own-message[_ngcontent-%COMP%]{align-items:flex-end}.fluid-message-container[_ngcontent-%COMP%]:hover{transform:translateY(-2px)}.fluid-reply-indicator[_ngcontent-%COMP%]{display:flex;align-items:flex-start;margin-bottom:.75rem;padding:.75rem;background:linear-gradient(135deg,rgba(102,126,234,.1),rgba(118,75,162,.1));border-radius:15px;border-left:4px solid #667eea;backdrop-filter:blur(10px);transition:all .3s ease}.fluid-reply-indicator[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,rgba(102,126,234,.15),rgba(118,75,162,.15));transform:translate(4px)}.reply-line[_ngcontent-%COMP%]{width:3px;height:2.5rem;background:linear-gradient(180deg,#667eea,#764ba2);margin-right:.75rem;border-radius:2px;box-shadow:0 2px 8px #667eea4d}.reply-content[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;gap:.375rem}.reply-username[_ngcontent-%COMP%]{font-size:.8rem;font-weight:700;color:#667eea;text-transform:capitalize}.reply-text[_ngcontent-%COMP%]{font-size:.8rem;color:#64748b;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;max-width:250px;line-height:1.4}.fluid-message-bubble[_ngcontent-%COMP%]{background:white;border:1px solid rgba(102,126,234,.1);border-radius:20px;padding:1rem 1.25rem;box-shadow:0 4px 20px #00000014;transition:all .3s ease;position:relative;backdrop-filter:blur(10px);max-width:100%;word-wrap:break-word}.fluid-message-bubble.own-bubble[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);border-color:#fff3;color:#fff;box-shadow:0 8px 30px #667eea4d}.fluid-message-bubble[_ngcontent-%COMP%]:hover{box-shadow:0 8px 30px #0000001f;transform:translateY(-1px)}.fluid-message-bubble.own-bubble[_ngcontent-%COMP%]:hover{box-shadow:0 12px 40px #667eea66}.fluid-message-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:.5rem}.header-actions[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.username[_ngcontent-%COMP%]{font-size:.8rem;font-weight:700;color:#667eea;text-transform:capitalize}.timestamp[_ngcontent-%COMP%]{font-size:.7rem;color:#94a3b8;font-weight:500}.fluid-message-bubble.own-bubble[_ngcontent-%COMP%]   .timestamp[_ngcontent-%COMP%]{color:#fffc}.quick-react-button[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:1.5rem;height:1.5rem;background:rgba(102,126,234,.1);border:none;border-radius:50%;cursor:pointer;color:#94a3b8;transition:all .3s ease;opacity:0}.fluid-message-bubble[_ngcontent-%COMP%]:hover   .quick-react-button[_ngcontent-%COMP%]{opacity:1}.quick-react-button[_ngcontent-%COMP%]:hover{background:rgba(102,126,234,.2);color:#667eea;transform:scale(1.1)}.quick-react-icon[_ngcontent-%COMP%]{width:.75rem;height:.75rem}.fluid-message-content[_ngcontent-%COMP%]{margin:0}.message-text[_ngcontent-%COMP%]{margin:0;font-size:.95rem;line-height:1.6;word-wrap:break-word;white-space:pre-wrap;color:#1e293b}.fluid-message-bubble.own-bubble[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]{color:#fff}.fluid-message-footer[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-top:.5rem}.fluid-reactions[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:.5rem;margin-top:.75rem;margin-left:.75rem}.fluid-reaction-button[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:.375rem;padding:.375rem .75rem;background:rgba(102,126,234,.1);border:1px solid rgba(102,126,234,.2);border-radius:20px;font-size:.8rem;cursor:pointer;transition:all .3s ease;backdrop-filter:blur(10px)}.fluid-reaction-button[_ngcontent-%COMP%]:hover{background:rgba(102,126,234,.15);transform:scale(1.05) translateY(-1px);box-shadow:0 4px 15px #667eea33}.fluid-reaction-button.user-reacted[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);border-color:#667eea;color:#fff;box-shadow:0 4px 15px #667eea4d}.reaction-emoji[_ngcontent-%COMP%]{font-size:.8rem}.reaction-count[_ngcontent-%COMP%]{font-weight:600;font-size:.75rem}.fluid-action-buttons[_ngcontent-%COMP%]{position:absolute;top:-.75rem;right:0;display:flex;gap:.375rem;background:rgba(255,255,255,.95);border:1px solid rgba(102,126,234,.1);border-radius:15px;padding:.375rem;box-shadow:0 8px 30px #00000026;opacity:0;transform:translateY(-.75rem);transition:all .3s ease;z-index:30;backdrop-filter:blur(20px)}.fluid-message-container[_ngcontent-%COMP%]:hover   .fluid-action-buttons[_ngcontent-%COMP%], .fluid-action-buttons.visible[_ngcontent-%COMP%]{opacity:1;transform:translateY(0)}@media (max-width: 768px){.fluid-action-buttons[_ngcontent-%COMP%]{position:static;opacity:1;transform:none;margin-top:.5rem;box-shadow:none;border:none;background:transparent;padding:0;justify-content:flex-start}}.fluid-action-buttons.own-actions[_ngcontent-%COMP%]{right:auto;left:0}.action-button[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:.375rem;min-width:2.25rem;height:2.25rem;padding:.375rem .75rem;background:none;border:none;border-radius:10px;cursor:pointer;color:#64748b;transition:all .3s ease;font-size:.75rem;font-weight:500}.action-button[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,rgba(102,126,234,.1),rgba(118,75,162,.1));color:#667eea;transform:scale(1.05)}.edit-button[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,rgba(34,197,94,.1),rgba(16,185,129,.1));color:#10b981}.delete-button[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,rgba(239,68,68,.1),rgba(220,38,38,.1));color:#ef4444}.action-icon[_ngcontent-%COMP%]{width:1rem;height:1rem;flex-shrink:0}.action-text[_ngcontent-%COMP%]{font-size:.7rem;font-weight:600;white-space:nowrap}@media (max-width: 480px){.action-text[_ngcontent-%COMP%]{display:none}.action-button[_ngcontent-%COMP%]{min-width:2rem;padding:.375rem}}.fluid-emoji-picker[_ngcontent-%COMP%]{position:fixed;z-index:1000;transform:none;margin:0;pointer-events:auto;animation:emojiPickerSlideIn .2s ease}.fluid-emoji-picker.own-picker[_ngcontent-%COMP%]{right:auto;left:0}@keyframes emojiPickerSlideIn{0%{opacity:0;transform:translateY(10px) scale(.95)}to{opacity:1;transform:translateY(0) scale(1)}}@media (max-width: 1024px){.fluid-message-bubble[_ngcontent-%COMP%]{padding:.875rem 1.125rem;border-radius:18px}.reply-text[_ngcontent-%COMP%]{max-width:200px}.fluid-reactions[_ngcontent-%COMP%]{margin-left:.625rem}}@media (max-width: 768px){.fluid-message-bubble[_ngcontent-%COMP%]{padding:.75rem 1rem;border-radius:16px}.message-text[_ngcontent-%COMP%]{font-size:.9rem}.reply-text[_ngcontent-%COMP%]{max-width:180px}.fluid-reactions[_ngcontent-%COMP%]{margin-left:.5rem;gap:.375rem}.fluid-reaction-button[_ngcontent-%COMP%]{padding:.25rem .625rem;font-size:.75rem}.fluid-action-buttons[_ngcontent-%COMP%]{position:static;opacity:1;transform:none;margin-top:.5rem;box-shadow:none;border:none;background:transparent;padding:0}}@media (max-width: 480px){.fluid-message-bubble[_ngcontent-%COMP%]{padding:.625rem .875rem;border-radius:14px}.message-text[_ngcontent-%COMP%]{font-size:.85rem}.reply-text[_ngcontent-%COMP%]{max-width:150px}.username[_ngcontent-%COMP%]{font-size:.75rem}.timestamp[_ngcontent-%COMP%]{font-size:.65rem}}@keyframes fluidMessageAppear{0%{opacity:0;transform:translateY(20px) scale(.95)}to{opacity:1;transform:translateY(0) scale(1)}}.fluid-message-container[_ngcontent-%COMP%]{animation:fluidMessageAppear .4s ease}*[_ngcontent-%COMP%]{transition:all .3s ease}\"]\n  });\n  return MessageComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}