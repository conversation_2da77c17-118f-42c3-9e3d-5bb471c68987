const User = require('../models/User');
const AllowedUser = require('../models/AllowedUser');
const InviteCode = require('../models/InviteCode');
const Configuration = require('../models/Configuration');

class AuthMiddleware {
  // Check if user is allowed to join
  static async checkUserAccess(username, password = null, inviteCode = null) {
    try {
      // Get security configuration from database
      const { securityMode, adminUsers } = await Configuration.getSecurityConfig();
      
      // Always allow admin users
      if (adminUsers.includes(username.toLowerCase())) {
        return { allowed: true, isAdmin: true };
      }

      // Check if user has a password set
      const hasPassword = await User.hasPassword(username);
      
      // If user has a password, verify it
      if (hasPassword) {
        if (!password) {
          return { allowed: false, error: 'Password required', requiresPassword: true };
        }
        
        const isValidPassword = await User.verifyPassword(username, password);
        if (!isValidPassword) {
          return { allowed: false, error: 'Invalid password', requiresPassword: true };
        }
      }

      switch (securityMode) {
        case 'open':
          return { allowed: true, isAdmin: false };

        case 'whitelist':
          const isWhitelisted = await AllowedUser.isUserAllowed(username);
          return { allowed: isWhitelisted, isAdmin: false };

        case 'invite':
          if (!inviteCode) {
            return { allowed: false, error: 'Invite code required' };
          }
          
          const codeValid = await InviteCode.validateAndUseCode(inviteCode);
          if (codeValid) {
            // Add user to whitelist after successful invite
            await AllowedUser.addUser(username, 'invite-system');
            return { allowed: true, isAdmin: false };
          }
          return { allowed: false, error: 'Invalid or expired invite code' };

        default:
          return { allowed: false, error: 'Unknown security mode' };
      }
    } catch (error) {
      console.error('Auth check error:', error);
      return { allowed: false, error: 'Authentication error' };
    }
  }

  // Express middleware for API routes
  static async apiAuthMiddleware(req, res, next) {
    try {
      const { username, password, inviteCode } = req.body;
      
      if (!username) {
        return res.status(400).json({ error: 'Username required' });
      }

      const authResult = await AuthMiddleware.checkUserAccess(username, password, inviteCode);
      
      if (!authResult.allowed) {
        const { securityMode } = await Configuration.getSecurityConfig();
        return res.status(403).json({ 
          error: authResult.error || 'Access denied',
          securityMode: securityMode,
          requiresPassword: authResult.requiresPassword || false
        });
      }

      req.userAuth = authResult;
      next();
    } catch (error) {
      console.error('API auth middleware error:', error);
      res.status(500).json({ error: 'Authentication error' });
    }
  }

  // Socket.IO authentication
  static async socketAuthMiddleware(socket, next) {
    try {
      const { username, password, inviteCode } = socket.handshake.auth;
      
      if (!username) {
        return next(new Error('Username required'));
      }

      const authResult = await AuthMiddleware.checkUserAccess(username, password, inviteCode);
      
      if (!authResult.allowed) {
        return next(new Error(authResult.error || 'Access denied'));
      }

      socket.userAuth = authResult;
      next();
    } catch (error) {
      console.error('Socket auth middleware error:', error);
      next(new Error('Authentication error'));
    }
  }

  // Get security info for frontend
  static async getSecurityInfo() {
    try {
      const { securityMode } = await Configuration.getSecurityConfig();
      return {
        mode: securityMode,
        requiresInvite: securityMode === 'invite',
        requiresWhitelist: securityMode === 'whitelist',
        isOpen: securityMode === 'open'
      };
    } catch (error) {
      console.error('Error getting security info:', error);
      return {
        mode: 'whitelist',
        requiresInvite: false,
        requiresWhitelist: true,
        isOpen: false
      };
    }
  }
}

module.exports = AuthMiddleware;
