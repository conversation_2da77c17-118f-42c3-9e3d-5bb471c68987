{"ast": null, "code": "import { url } from \"./url.js\";\nimport { Manager } from \"./manager.js\";\nimport { Socket } from \"./socket.js\";\n/**\n * Managers cache.\n */\n\nconst cache = {};\n\nfunction lookup(uri, opts) {\n  if (typeof uri === \"object\") {\n    opts = uri;\n    uri = undefined;\n  }\n\n  opts = opts || {};\n  const parsed = url(uri, opts.path || \"/socket.io\");\n  const source = parsed.source;\n  const id = parsed.id;\n  const path = parsed.path;\n  const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n  const newConnection = opts.forceNew || opts[\"force new connection\"] || false === opts.multiplex || sameNamespace;\n  let io;\n\n  if (newConnection) {\n    io = new Manager(source, opts);\n  } else {\n    if (!cache[id]) {\n      cache[id] = new Manager(source, opts);\n    }\n\n    io = cache[id];\n  }\n\n  if (parsed.query && !opts.query) {\n    opts.query = parsed.queryKey;\n  }\n\n  return io.socket(parsed.path, opts);\n} // so that \"lookup\" can be used both as a function (e.g. `io(...)`) and as a\n// namespace (e.g. `io.connect(...)`), for backward compatibility\n\n\nObject.assign(lookup, {\n  Manager,\n  Socket,\n  io: lookup,\n  connect: lookup\n});\n/**\n * Protocol version.\n *\n * @public\n */\n\nexport { protocol } from \"socket.io-parser\";\n/**\n * Expose constructors for standalone build.\n *\n * @public\n */\n\nexport { Manager, Socket, lookup as io, lookup as connect, lookup as default };\nexport { Fetch, NodeXHR, XHR, NodeWebSocket, WebSocket, WebTransport } from \"engine.io-client\";", "map": {"version": 3, "names": ["url", "Manager", "Socket", "cache", "lookup", "uri", "opts", "undefined", "parsed", "path", "source", "id", "sameNamespace", "newConnection", "forceNew", "multiplex", "io", "query", "query<PERSON><PERSON>", "socket", "Object", "assign", "connect", "protocol", "default", "<PERSON>tch", "NodeXHR", "XHR", "NodeWebSocket", "WebSocket", "WebTransport"], "sources": ["R:/chateye/FrontendAngular/node_modules/socket.io-client/build/esm/index.js"], "sourcesContent": ["import { url } from \"./url.js\";\nimport { Manager } from \"./manager.js\";\nimport { Socket } from \"./socket.js\";\n/**\n * Managers cache.\n */\nconst cache = {};\nfunction lookup(uri, opts) {\n    if (typeof uri === \"object\") {\n        opts = uri;\n        uri = undefined;\n    }\n    opts = opts || {};\n    const parsed = url(uri, opts.path || \"/socket.io\");\n    const source = parsed.source;\n    const id = parsed.id;\n    const path = parsed.path;\n    const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n    const newConnection = opts.forceNew ||\n        opts[\"force new connection\"] ||\n        false === opts.multiplex ||\n        sameNamespace;\n    let io;\n    if (newConnection) {\n        io = new Manager(source, opts);\n    }\n    else {\n        if (!cache[id]) {\n            cache[id] = new Manager(source, opts);\n        }\n        io = cache[id];\n    }\n    if (parsed.query && !opts.query) {\n        opts.query = parsed.queryKey;\n    }\n    return io.socket(parsed.path, opts);\n}\n// so that \"lookup\" can be used both as a function (e.g. `io(...)`) and as a\n// namespace (e.g. `io.connect(...)`), for backward compatibility\nObject.assign(lookup, {\n    Manager,\n    Socket,\n    io: lookup,\n    connect: lookup,\n});\n/**\n * Protocol version.\n *\n * @public\n */\nexport { protocol } from \"socket.io-parser\";\n/**\n * Expose constructors for standalone build.\n *\n * @public\n */\nexport { Manager, Socket, lookup as io, lookup as connect, lookup as default, };\nexport { Fetch, NodeXHR, XHR, NodeWebSocket, WebSocket, WebTransport, } from \"engine.io-client\";\n"], "mappings": "AAAA,SAASA,GAAT,QAAoB,UAApB;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,MAAT,QAAuB,aAAvB;AACA;AACA;AACA;;AACA,MAAMC,KAAK,GAAG,EAAd;;AACA,SAASC,MAAT,CAAgBC,GAAhB,EAAqBC,IAArB,EAA2B;EACvB,IAAI,OAAOD,GAAP,KAAe,QAAnB,EAA6B;IACzBC,IAAI,GAAGD,GAAP;IACAA,GAAG,GAAGE,SAAN;EACH;;EACDD,IAAI,GAAGA,IAAI,IAAI,EAAf;EACA,MAAME,MAAM,GAAGR,GAAG,CAACK,GAAD,EAAMC,IAAI,CAACG,IAAL,IAAa,YAAnB,CAAlB;EACA,MAAMC,MAAM,GAAGF,MAAM,CAACE,MAAtB;EACA,MAAMC,EAAE,GAAGH,MAAM,CAACG,EAAlB;EACA,MAAMF,IAAI,GAAGD,MAAM,CAACC,IAApB;EACA,MAAMG,aAAa,GAAGT,KAAK,CAACQ,EAAD,CAAL,IAAaF,IAAI,IAAIN,KAAK,CAACQ,EAAD,CAAL,CAAU,MAAV,CAA3C;EACA,MAAME,aAAa,GAAGP,IAAI,CAACQ,QAAL,IAClBR,IAAI,CAAC,sBAAD,CADc,IAElB,UAAUA,IAAI,CAACS,SAFG,IAGlBH,aAHJ;EAIA,IAAII,EAAJ;;EACA,IAAIH,aAAJ,EAAmB;IACfG,EAAE,GAAG,IAAIf,OAAJ,CAAYS,MAAZ,EAAoBJ,IAApB,CAAL;EACH,CAFD,MAGK;IACD,IAAI,CAACH,KAAK,CAACQ,EAAD,CAAV,EAAgB;MACZR,KAAK,CAACQ,EAAD,CAAL,GAAY,IAAIV,OAAJ,CAAYS,MAAZ,EAAoBJ,IAApB,CAAZ;IACH;;IACDU,EAAE,GAAGb,KAAK,CAACQ,EAAD,CAAV;EACH;;EACD,IAAIH,MAAM,CAACS,KAAP,IAAgB,CAACX,IAAI,CAACW,KAA1B,EAAiC;IAC7BX,IAAI,CAACW,KAAL,GAAaT,MAAM,CAACU,QAApB;EACH;;EACD,OAAOF,EAAE,CAACG,MAAH,CAAUX,MAAM,CAACC,IAAjB,EAAuBH,IAAvB,CAAP;AACH,C,CACD;AACA;;;AACAc,MAAM,CAACC,MAAP,CAAcjB,MAAd,EAAsB;EAClBH,OADkB;EAElBC,MAFkB;EAGlBc,EAAE,EAAEZ,MAHc;EAIlBkB,OAAO,EAAElB;AAJS,CAAtB;AAMA;AACA;AACA;AACA;AACA;;AACA,SAASmB,QAAT,QAAyB,kBAAzB;AACA;AACA;AACA;AACA;AACA;;AACA,SAAStB,OAAT,EAAkBC,MAAlB,EAA0BE,MAAM,IAAIY,EAApC,EAAwCZ,MAAM,IAAIkB,OAAlD,EAA2DlB,MAAM,IAAIoB,OAArE;AACA,SAASC,KAAT,EAAgBC,OAAhB,EAAyBC,GAAzB,EAA8BC,aAA9B,EAA6CC,SAA7C,EAAwDC,YAAxD,QAA6E,kBAA7E", "ignoreList": []}, "metadata": {}, "sourceType": "module"}