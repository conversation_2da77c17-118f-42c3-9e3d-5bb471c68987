{"ast": null, "code": "export const isArrayLike = x => x && typeof x.length === 'number' && typeof x !== 'function';", "map": {"version": 3, "names": ["isArrayLike", "x", "length"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/rxjs/dist/esm/internal/util/isArrayLike.js"], "sourcesContent": ["export const isArrayLike = ((x) => x && typeof x.length === 'number' && typeof x !== 'function');\n"], "mappings": "AAAA,OAAO,MAAMA,WAAW,GAAKC,CAAD,IAAOA,CAAC,IAAI,OAAOA,CAAC,CAACC,MAAT,KAAoB,QAAzB,IAAqC,OAAOD,CAAP,KAAa,UAA9E", "ignoreList": []}, "metadata": {}, "sourceType": "module"}