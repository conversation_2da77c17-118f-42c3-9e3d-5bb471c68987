{"ast": null, "code": "import { reduce } from './reduce';\nimport { operate } from '../util/lift';\n\nconst arrReducer = (arr, value) => (arr.push(value), arr);\n\nexport function toArray() {\n  return operate((source, subscriber) => {\n    reduce(arrReducer, [])(source).subscribe(subscriber);\n  });\n}", "map": {"version": 3, "names": ["reduce", "operate", "arrReducer", "arr", "value", "push", "toArray", "source", "subscriber", "subscribe"], "sources": ["R:/chateye/FrontendAngular/node_modules/rxjs/dist/esm/internal/operators/toArray.js"], "sourcesContent": ["import { reduce } from './reduce';\nimport { operate } from '../util/lift';\nconst arrReducer = (arr, value) => (arr.push(value), arr);\nexport function toArray() {\n    return operate((source, subscriber) => {\n        reduce(arrReducer, [])(source).subscribe(subscriber);\n    });\n}\n"], "mappings": "AAAA,SAASA,MAAT,QAAuB,UAAvB;AACA,SAASC,OAAT,QAAwB,cAAxB;;AACA,MAAMC,UAAU,GAAG,CAACC,GAAD,EAAMC,KAAN,MAAiBD,GAAG,CAACE,IAAJ,CAASD,KAAT,GAAiBD,GAAlC,CAAnB;;AACA,OAAO,SAASG,OAAT,GAAmB;EACtB,OAAOL,OAAO,CAAC,CAACM,MAAD,EAASC,UAAT,KAAwB;IACnCR,MAAM,CAACE,UAAD,EAAa,EAAb,CAAN,CAAuBK,MAAvB,EAA+BE,SAA/B,CAAyCD,UAAzC;EACH,CAFa,CAAd;AAGH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}