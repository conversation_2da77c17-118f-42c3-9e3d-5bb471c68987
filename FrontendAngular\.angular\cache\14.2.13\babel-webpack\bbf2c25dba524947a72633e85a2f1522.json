{"ast": null, "code": "import { isFunction } from './isFunction';\nexport function isScheduler(value) {\n  return value && isFunction(value.schedule);\n}", "map": {"version": 3, "names": ["isFunction", "isScheduler", "value", "schedule"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/rxjs/dist/esm/internal/util/isScheduler.js"], "sourcesContent": ["import { isFunction } from './isFunction';\nexport function isScheduler(value) {\n    return value && isFunction(value.schedule);\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,cAA3B;AACA,OAAO,SAASC,WAAT,CAAqBC,KAArB,EAA4B;EAC/B,OAAOA,KAAK,IAAIF,UAAU,CAACE,KAAK,CAACC,QAAP,CAA1B;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}