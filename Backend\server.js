const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
require('dotenv').config();

const messagesRouter = require('./routes/messages');
const usersRouter = require('./routes/users');
const adminRouter = require('./routes/admin');
const passwordsRouter = require('./routes/passwords');
const handleSocketConnection = require('./socket/socketHandlers');
const AuthMiddleware = require('./middleware/auth');
const Group = require('./models/Group');
const AllowedUser = require('./models/AllowedUser');

const app = express();
const server = http.createServer(app);

// Configure Socket.IO with CORS
const io = socketIo(server, {
  cors: {
    origin: [
      process.env.FRONTEND_URL || "http://localhost:5173",
      "http://localhost:4200",  // Angular dev server
      "http://localhost:62324", // Angular dev server (alternative port)
      /^http:\/\/localhost:\d+$/, // Allow any localhost port
      "https://chateye.vercel.app",  // Main Vercel deployment
      "https://chateye-angular.vercel.app",  // Angular Vercel deployment
      "https://chateye-3htc.vercel.app",  // React Vercel deployment
      "https://chateye-angular.netlify.app",  // Netlify deployment
      "https://chateye-angular.onrender.com"  // Render deployment
    ],
    methods: ["GET", "POST"],
    credentials: true
  }
});

// Middleware
app.use(cors({
  origin: [
    process.env.FRONTEND_URL || "http://localhost:5173",
    "http://localhost:4200",  // Angular dev server
    "http://localhost:62324", // Angular dev server (alternative port)
    /^http:\/\/localhost:\d+$/, // Allow any localhost port
    "https://chateye.vercel.app",  // Main Vercel deployment
    "https://chateye-angular.vercel.app",  // Angular Vercel deployment
    "https://chateye-3htc.vercel.app",  // React Vercel deployment
    "https://chateye-angular.netlify.app",  // Netlify deployment
    "https://chateye-angular.onrender.com"  // Render deployment
  ],
  credentials: true
}));
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// API Routes
app.use('/api/messages', messagesRouter);
app.use('/api/users', usersRouter);
app.use('/api/admin', adminRouter);
app.use('/api/passwords', passwordsRouter);

// Socket.IO connection handling
handleSocketConnection(io);

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

const PORT = process.env.PORT || 3001;

// Initialize default data
const initializeApp = async () => {
  try {
    // Initialize default allowed users
    await AllowedUser.initializeDefaults();
    
    // Initialize default groups
    await Group.initializeDefaultGroups();
    
    console.log('✅ Default data initialized');
  } catch (error) {
    console.error('❌ Error initializing default data:', error);
  }
};

server.listen(PORT, async () => {
  console.log(`🚀 Chateye backend server running on port ${PORT}`);
  console.log(`📡 Socket.IO server ready for connections`);
  console.log(`🌐 CORS enabled for: ${process.env.FRONTEND_URL || "http://localhost:5173"}, http://localhost:4200, and production URLs (including chateye.vercel.app)`);
  
  // Initialize default data
  await initializeApp();
});
