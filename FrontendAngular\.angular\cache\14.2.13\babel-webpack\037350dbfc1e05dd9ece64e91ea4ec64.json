{"ast": null, "code": "import { EventEmitter, ElementRef, SimpleChanges } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/chat.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/material/card\";\nimport * as i5 from \"@angular/material/form-field\";\nimport * as i6 from \"@angular/material/input\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/tooltip\";\nimport * as i10 from \"../emoji-picker/emoji-picker.component\";\nconst _c0 = [\"textareaRef\"];\n\nfunction MessageInputComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"mat-card\", 13)(2, \"mat-card-content\")(3, \"div\", 14)(4, \"span\", 15);\n    i0.ɵɵtext(5, \"Replying to\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 16);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function MessageInputComponent_div_1_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onCancelReplyClick());\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"close\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"p\", 18);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r0.replyTo.username);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.replyTo.text);\n  }\n}\n\nfunction MessageInputComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"app-emoji-picker\", 20);\n    i0.ɵɵlistener(\"onEmojiSelect\", function MessageInputComponent_div_16_Template_app_emoji_picker_onEmojiSelect_1_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onEmojiSelect($event));\n    })(\"onClose\", function MessageInputComponent_div_16_Template_app_emoji_picker_onClose_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onEmojiClose());\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\n\nexport let MessageInputComponent = /*#__PURE__*/(() => {\n  class MessageInputComponent {\n    constructor(chatService) {\n      this.chatService = chatService;\n      this.replyTo = null;\n      this.onSendMessage = new EventEmitter();\n      this.onCancelReply = new EventEmitter();\n      this.message = '';\n      this.showEmojiPicker = false;\n      this.typingTimer = null;\n    }\n\n    ngAfterViewInit() {\n      if (this.textareaRef) {\n        // Use setTimeout to avoid ExpressionChangedAfterItHasBeenCheckedError\n        setTimeout(() => {\n          this.textareaRef.nativeElement.focus();\n        }, 0);\n      }\n    }\n\n    ngOnChanges(changes) {\n      if (changes['replyTo'] && this.textareaRef) {\n        // Use setTimeout to avoid ExpressionChangedAfterItHasBeenCheckedError\n        setTimeout(() => {\n          this.textareaRef.nativeElement.focus();\n        }, 0);\n      }\n    }\n\n    onSubmit() {\n      if (!this.message.trim()) return; // Stop typing indicator when sending message\n\n      this.chatService.stopTyping();\n\n      if (this.typingTimer) {\n        clearTimeout(this.typingTimer);\n        this.typingTimer = null;\n      }\n\n      this.onSendMessage.emit({\n        text: this.message.trim(),\n        replyToId: this.replyTo?.id || null\n      });\n      this.message = '';\n      this.showEmojiPicker = false;\n\n      if (this.replyTo) {\n        this.onCancelReply.emit();\n      }\n    }\n\n    onKeyPress(event) {\n      if (event.key === 'Enter' && !event.shiftKey) {\n        event.preventDefault();\n        this.onSubmit();\n      }\n    }\n\n    onEmojiSelect(emoji) {\n      const textarea = this.textareaRef.nativeElement;\n      const start = textarea.selectionStart;\n      const end = textarea.selectionEnd;\n      const newMessage = this.message.slice(0, start) + emoji.native + this.message.slice(end);\n      this.message = newMessage;\n      this.showEmojiPicker = false; // Restore cursor position\n\n      setTimeout(() => {\n        textarea.selectionStart = textarea.selectionEnd = start + emoji.native.length;\n        textarea.focus();\n      }, 0);\n    }\n\n    adjustTextareaHeight() {\n      const textarea = this.textareaRef.nativeElement;\n\n      if (textarea) {\n        textarea.style.height = 'auto';\n        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';\n      }\n    }\n\n    onMessageChange() {\n      this.adjustTextareaHeight();\n      this.handleTyping();\n    }\n\n    handleTyping() {\n      // Start typing indicator\n      this.chatService.startTyping(); // Clear existing timer\n\n      if (this.typingTimer) {\n        clearTimeout(this.typingTimer);\n      } // Set timer to stop typing after 1 second of inactivity\n\n\n      this.typingTimer = setTimeout(() => {\n        this.chatService.stopTyping();\n      }, 1000);\n    }\n\n    onEmojiClick() {\n      this.showEmojiPicker = !this.showEmojiPicker;\n    }\n\n    onEmojiClose() {\n      this.showEmojiPicker = false;\n    }\n\n    onCancelReplyClick() {\n      this.onCancelReply.emit();\n    }\n\n    ngOnDestroy() {\n      // Clean up typing timer and stop typing indicator\n      if (this.typingTimer) {\n        clearTimeout(this.typingTimer);\n      }\n\n      this.chatService.stopTyping();\n    }\n\n  }\n\n  MessageInputComponent.ɵfac = function MessageInputComponent_Factory(t) {\n    return new (t || MessageInputComponent)(i0.ɵɵdirectiveInject(i1.ChatService));\n  };\n\n  MessageInputComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: MessageInputComponent,\n    selectors: [[\"app-message-input\"]],\n    viewQuery: function MessageInputComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.textareaRef = _t.first);\n      }\n    },\n    inputs: {\n      replyTo: \"replyTo\"\n    },\n    outputs: {\n      onSendMessage: \"onSendMessage\",\n      onCancelReply: \"onCancelReply\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 17,\n    vars: 4,\n    consts: [[1, \"input-container\"], [\"class\", \"reply-indicator\", 4, \"ngIf\"], [1, \"input-form\", 3, \"ngSubmit\"], [1, \"input-card\"], [1, \"input-wrapper\"], [1, \"text-input-container\"], [\"appearance\", \"outline\", 1, \"message-field\"], [\"matInput\", \"\", \"name\", \"message\", \"placeholder\", \"Type your message...\", \"rows\", \"1\", 1, \"message-textarea\", 3, \"ngModel\", \"ngModelChange\", \"keypress\", \"input\"], [\"textareaRef\", \"\"], [\"type\", \"button\", \"mat-icon-button\", \"\", \"matTooltip\", \"Add emoji\", 1, \"emoji-button\", 3, \"click\"], [\"type\", \"submit\", \"mat-fab\", \"\", \"color\", \"primary\", \"matTooltip\", \"Send message\", 1, \"send-button\", 3, \"disabled\"], [\"class\", \"emoji-picker-container\", 4, \"ngIf\"], [1, \"reply-indicator\"], [1, \"reply-card\"], [1, \"reply-header\"], [1, \"reply-label\"], [1, \"reply-username\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Cancel reply\", 1, \"reply-close-button\", 3, \"click\"], [1, \"reply-text\"], [1, \"emoji-picker-container\"], [3, \"onEmojiSelect\", \"onClose\"]],\n    template: function MessageInputComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵtemplate(1, MessageInputComponent_div_1_Template, 13, 2, \"div\", 1);\n        i0.ɵɵelementStart(2, \"form\", 2);\n        i0.ɵɵlistener(\"ngSubmit\", function MessageInputComponent_Template_form_ngSubmit_2_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵelementStart(3, \"mat-card\", 3)(4, \"mat-card-content\")(5, \"div\", 4)(6, \"div\", 5)(7, \"mat-form-field\", 6)(8, \"textarea\", 7, 8);\n        i0.ɵɵlistener(\"ngModelChange\", function MessageInputComponent_Template_textarea_ngModelChange_8_listener($event) {\n          return ctx.message = $event;\n        })(\"keypress\", function MessageInputComponent_Template_textarea_keypress_8_listener($event) {\n          return ctx.onKeyPress($event);\n        })(\"input\", function MessageInputComponent_Template_textarea_input_8_listener() {\n          return ctx.onMessageChange();\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"button\", 9);\n        i0.ɵɵlistener(\"click\", function MessageInputComponent_Template_button_click_10_listener() {\n          return ctx.onEmojiClick();\n        });\n        i0.ɵɵelementStart(11, \"mat-icon\");\n        i0.ɵɵtext(12, \"emoji_emotions\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(13, \"button\", 10)(14, \"mat-icon\");\n        i0.ɵɵtext(15, \"send\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵtemplate(16, MessageInputComponent_div_16_Template, 2, 0, \"div\", 11);\n        i0.ɵɵelementEnd()();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.replyTo);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngModel\", ctx.message);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"disabled\", !ctx.message.trim());\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.showEmojiPicker);\n      }\n    },\n    dependencies: [i2.NgIf, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.NgModel, i3.NgForm, i4.MatCard, i4.MatCardContent, i5.MatFormField, i6.MatInput, i7.MatButton, i8.MatIcon, i9.MatTooltip, i10.EmojiPickerComponent],\n    styles: [\".input-container[_ngcontent-%COMP%]{background:white;position:relative}.reply-indicator[_ngcontent-%COMP%]{margin-bottom:1rem;animation:replySlideIn .3s ease}.reply-card[_ngcontent-%COMP%]{background:rgba(63,81,181,.05);border-left:4px solid #3f51b5}.reply-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;margin-bottom:.5rem}.reply-label[_ngcontent-%COMP%]{font-size:.8rem;color:#666;font-weight:500}.reply-username[_ngcontent-%COMP%]{font-size:.8rem;color:#3f51b5;font-weight:600;text-transform:capitalize}.reply-close-button[_ngcontent-%COMP%]{margin-left:auto}.reply-text[_ngcontent-%COMP%]{font-size:.85rem;color:#333;margin:0;line-height:1.4;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.input-form[_ngcontent-%COMP%]{position:relative}.input-card[_ngcontent-%COMP%]{box-shadow:0 2px 8px #0000001a;border-radius:16px}.input-wrapper[_ngcontent-%COMP%]{display:flex;align-items:flex-end;gap:1rem}.text-input-container[_ngcontent-%COMP%]{flex:1;position:relative;display:flex;align-items:flex-end}.message-field[_ngcontent-%COMP%]{width:100%;margin:0}.message-textarea[_ngcontent-%COMP%]{min-height:2.5rem;max-height:8rem;resize:none;font-size:.95rem;line-height:1.5;font-family:inherit}.emoji-button[_ngcontent-%COMP%]{margin-left:.5rem;color:#666}.emoji-button[_ngcontent-%COMP%]:hover{background:rgba(63,81,181,.1);color:#3f51b5}.send-button[_ngcontent-%COMP%]{width:48px;height:48px;flex-shrink:0}.send-button[_ngcontent-%COMP%]:hover{transform:scale(1.05)}.emoji-picker-container[_ngcontent-%COMP%]{position:absolute;bottom:100%;right:0;margin-bottom:.75rem;z-index:50;animation:emojiSlideIn .3s ease}@keyframes replySlideIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}@keyframes emojiSlideIn{0%{opacity:0;transform:translateY(10px) scale(.95)}to{opacity:1;transform:translateY(0) scale(1)}}@media (max-width: 768px){.input-wrapper[_ngcontent-%COMP%]{gap:.75rem}.message-textarea[_ngcontent-%COMP%]{font-size:.9rem}.send-button[_ngcontent-%COMP%]{width:44px;height:44px}}@media (max-width: 480px){.input-wrapper[_ngcontent-%COMP%]{gap:.5rem}.message-textarea[_ngcontent-%COMP%]{font-size:.85rem}.reply-text[_ngcontent-%COMP%]{font-size:.8rem}.send-button[_ngcontent-%COMP%]{width:40px;height:40px}}*[_ngcontent-%COMP%]{transition:all .3s ease}\"]\n  });\n  return MessageInputComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}