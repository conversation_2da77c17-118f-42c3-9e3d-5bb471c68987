{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\n\nfunction SidebarComponent_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 26);\n    i0.ɵɵtext(1, \" Admin \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction SidebarComponent_div_27_p_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const group_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", group_r5.description, \" \");\n  }\n}\n\nfunction SidebarComponent_div_27_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 35);\n  }\n}\n\nfunction SidebarComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_div_27_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const group_r5 = restoredCtx.$implicit;\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onGroupClick(group_r5.id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 28);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 29);\n    i0.ɵɵelement(3, \"path\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"div\", 30)(5, \"p\", 31);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, SidebarComponent_div_27_p_7_Template, 2, 1, \"p\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, SidebarComponent_div_27_div_8_Template, 1, 0, \"div\", 33);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const group_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(\"group-item cursor-pointer \" + ((ctx_r1.currentGroup == null ? null : ctx_r1.currentGroup.id) === group_r5.id ? \"bg-primary-50 border-primary-200\" : \"hover:bg-gray-50\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", group_r5.name, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", group_r5.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.currentGroup == null ? null : ctx_r1.currentGroup.id) === group_r5.id);\n  }\n}\n\nfunction SidebarComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"p\", 37);\n    i0.ɵɵtext(2, \"No groups available\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction SidebarComponent_div_36_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 42);\n    i0.ɵɵtext(1, \"(you)\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction SidebarComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 39);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 30)(4, \"p\", 31);\n    i0.ɵɵtext(5);\n    i0.ɵɵtemplate(6, SidebarComponent_div_36_span_6_Template, 2, 0, \"span\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(7, \"div\", 41);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const user_r11 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", user_r11.username.charAt(0).toUpperCase(), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", user_r11.username, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", user_r11.username === ctx_r3.currentUser);\n  }\n}\n\nfunction SidebarComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"p\", 37);\n    i0.ɵɵtext(2, \"No users online\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nexport let SidebarComponent = /*#__PURE__*/(() => {\n  class SidebarComponent {\n    constructor() {\n      this.onlineUsers = [];\n      this.currentUser = null;\n      this.isAdmin = false;\n      this.groups = [];\n      this.currentGroup = null;\n\n      this.onJoinGroup = () => {};\n    }\n\n    onGroupClick(groupId) {\n      this.onJoinGroup(groupId);\n    }\n\n  }\n\n  SidebarComponent.ɵfac = function SidebarComponent_Factory(t) {\n    return new (t || SidebarComponent)();\n  };\n\n  SidebarComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: SidebarComponent,\n    selectors: [[\"app-sidebar\"]],\n    inputs: {\n      onlineUsers: \"onlineUsers\",\n      currentUser: \"currentUser\",\n      isAdmin: \"isAdmin\",\n      groups: \"groups\",\n      currentGroup: \"currentGroup\",\n      onJoinGroup: \"onJoinGroup\"\n    },\n    decls: 41,\n    vars: 9,\n    consts: [[1, \"w-64\", \"bg-white\", \"border-r\", \"border-gray-200\", \"flex\", \"flex-col\"], [1, \"p-4\", \"border-b\", \"border-gray-200\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [1, \"h-8\", \"w-8\", \"flex\", \"items-center\", \"justify-center\", \"rounded-full\", \"bg-primary-500\", \"text-white\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"], [1, \"text-lg\", \"font-semibold\", \"text-gray-900\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [1, \"w-8\", \"h-8\", \"bg-primary-500\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"text-sm\", \"font-medium\"], [1, \"flex-1\"], [1, \"text-sm\", \"font-medium\", \"text-gray-900\"], [\"class\", \"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800\", 4, \"ngIf\"], [1, \"text-xs\", \"text-green-600\"], [1, \"flex-1\", \"overflow-y-auto\", \"custom-scrollbar\"], [1, \"p-4\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"mb-3\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-gray-500\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 20l4-16m2 16l4-16M6 9h14M4 15h14\"], [1, \"text-sm\", \"font-medium\", \"text-gray-700\"], [1, \"space-y-1\"], [3, \"class\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [1, \"p-4\", \"border-t\", \"border-gray-200\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\"], [\"class\", \"user-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"text-xs\", \"text-gray-500\", \"text-center\"], [1, \"inline-flex\", \"items-center\", \"px-2\", \"py-0.5\", \"rounded\", \"text-xs\", \"font-medium\", \"bg-yellow-100\", \"text-yellow-800\"], [3, \"click\"], [1, \"w-6\", \"h-6\", \"bg-primary-100\", \"rounded\", \"flex\", \"items-center\", \"justify-center\", \"text-xs\", \"font-medium\", \"text-primary-700\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-3\", \"h-3\"], [1, \"flex-1\", \"min-w-0\"], [1, \"text-sm\", \"text-gray-900\", \"truncate\"], [\"class\", \"text-xs text-gray-500 truncate\", 4, \"ngIf\"], [\"class\", \"w-2 h-2 bg-primary-500 rounded-full\", 4, \"ngIf\"], [1, \"text-xs\", \"text-gray-500\", \"truncate\"], [1, \"w-2\", \"h-2\", \"bg-primary-500\", \"rounded-full\"], [1, \"text-center\", \"py-4\"], [1, \"text-sm\", \"text-gray-500\"], [1, \"user-item\"], [1, \"w-6\", \"h-6\", \"bg-gray-300\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"text-xs\", \"font-medium\", \"text-gray-700\"], [\"class\", \"text-xs text-gray-500 ml-1\", 4, \"ngIf\"], [1, \"online-indicator\"], [1, \"text-xs\", \"text-gray-500\", \"ml-1\"]],\n    template: function SidebarComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(4, \"svg\", 4);\n        i0.ɵɵelement(5, \"path\", 5);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵnamespaceHTML();\n        i0.ɵɵelementStart(6, \"h1\", 6);\n        i0.ɵɵtext(7, \"Chateye\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(8, \"div\", 1)(9, \"div\", 7)(10, \"div\", 8);\n        i0.ɵɵtext(11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"div\", 9)(13, \"div\", 2)(14, \"p\", 10);\n        i0.ɵɵtext(15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(16, SidebarComponent_span_16_Template, 2, 0, \"span\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"p\", 12);\n        i0.ɵɵtext(18, \"Online\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(19, \"div\", 13)(20, \"div\", 14)(21, \"div\", 15);\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(22, \"svg\", 16);\n        i0.ɵɵelement(23, \"path\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵnamespaceHTML();\n        i0.ɵɵelementStart(24, \"h2\", 18);\n        i0.ɵɵtext(25);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(26, \"div\", 19);\n        i0.ɵɵtemplate(27, SidebarComponent_div_27_Template, 9, 5, \"div\", 20);\n        i0.ɵɵtemplate(28, SidebarComponent_div_28_Template, 3, 0, \"div\", 21);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(29, \"div\", 22)(30, \"div\", 15);\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(31, \"svg\", 16);\n        i0.ɵɵelement(32, \"path\", 23);\n        i0.ɵɵelementEnd();\n        i0.ɵɵnamespaceHTML();\n        i0.ɵɵelementStart(33, \"h2\", 18);\n        i0.ɵɵtext(34);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(35, \"div\", 19);\n        i0.ɵɵtemplate(36, SidebarComponent_div_36_Template, 8, 3, \"div\", 24);\n        i0.ɵɵtemplate(37, SidebarComponent_div_37_Template, 3, 0, \"div\", 21);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(38, \"div\", 22)(39, \"p\", 25);\n        i0.ɵɵtext(40, \" Private chat for friends \");\n        i0.ɵɵelementEnd()()();\n      }\n\n      if (rf & 2) {\n        let tmp_0_0;\n        i0.ɵɵadvance(11);\n        i0.ɵɵtextInterpolate1(\" \", ctx.currentUser == null ? null : (tmp_0_0 = ctx.currentUser.charAt(0)) == null ? null : tmp_0_0.toUpperCase(), \" \");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(ctx.currentUser);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isAdmin);\n        i0.ɵɵadvance(9);\n        i0.ɵɵtextInterpolate1(\" Groups (\", ctx.groups.length, \") \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.groups);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.groups.length === 0);\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate1(\" Online Users (\", ctx.onlineUsers.length, \") \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.onlineUsers);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.onlineUsers.length === 0);\n      }\n    },\n    dependencies: [i1.NgForOf, i1.NgIf]\n  });\n  return SidebarComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}