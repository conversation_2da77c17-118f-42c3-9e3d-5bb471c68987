{"ast": null, "code": "import { createErrorClass } from './createErrorClass';\nexport const ArgumentOutOfRangeError = createErrorClass(_super => function ArgumentOutOfRangeErrorImpl() {\n  _super(this);\n\n  this.name = 'ArgumentOutOfRangeError';\n  this.message = 'argument out of range';\n});", "map": {"version": 3, "names": ["createErrorClass", "ArgumentOutOfRangeError", "_super", "ArgumentOutOfRangeErrorImpl", "name", "message"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/rxjs/dist/esm/internal/util/ArgumentOutOfRangeError.js"], "sourcesContent": ["import { createErrorClass } from './createErrorClass';\nexport const ArgumentOutOfRangeError = createErrorClass((_super) => function ArgumentOutOfRangeErrorImpl() {\n    _super(this);\n    this.name = 'ArgumentOutOfRangeError';\n    this.message = 'argument out of range';\n});\n"], "mappings": "AAAA,SAASA,gBAAT,QAAiC,oBAAjC;AACA,OAAO,MAAMC,uBAAuB,GAAGD,gBAAgB,CAAEE,MAAD,IAAY,SAASC,2BAAT,GAAuC;EACvGD,MAAM,CAAC,IAAD,CAAN;;EACA,KAAKE,IAAL,GAAY,yBAAZ;EACA,KAAKC,OAAL,GAAe,uBAAf;AACH,CAJsD,CAAhD", "ignoreList": []}, "metadata": {}, "sourceType": "module"}