{"ast": null, "code": "import _asyncToGenerator from \"R:/chateye/FrontendAngular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { ApiService, Message, Group, User, SecurityInfo } from './api.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./api.service\";\nimport * as i2 from \"./socket.service\";\nimport * as i3 from \"./notification.service\";\nexport let ChatService = /*#__PURE__*/(() => {\n  class ChatService {\n    constructor(apiService, socketService, notificationService) {\n      this.apiService = apiService;\n      this.socketService = socketService;\n      this.notificationService = notificationService;\n      this.userSubject = new BehaviorSubject(null);\n      this.messagesSubject = new BehaviorSubject([]);\n      this.onlineUsersSubject = new BehaviorSubject([]);\n      this.groupsSubject = new BehaviorSubject([]);\n      this.currentGroupSubject = new BehaviorSubject(null);\n      this.replyToSubject = new BehaviorSubject(null);\n      this.loadingSubject = new BehaviorSubject(false);\n      this.isAdminSubject = new BehaviorSubject(false);\n      this.showAdminPanelSubject = new BehaviorSubject(false);\n      this.securityInfoSubject = new BehaviorSubject(null);\n      this.editingMessageSubject = new BehaviorSubject(null); // Public observables\n\n      this.user$ = this.userSubject.asObservable();\n      this.messages$ = this.messagesSubject.asObservable();\n      this.onlineUsers$ = this.onlineUsersSubject.asObservable();\n      this.groups$ = this.groupsSubject.asObservable();\n      this.currentGroup$ = this.currentGroupSubject.asObservable();\n      this.replyTo$ = this.replyToSubject.asObservable();\n      this.loading$ = this.loadingSubject.asObservable();\n      this.isAdmin$ = this.isAdminSubject.asObservable();\n      this.showAdminPanel$ = this.showAdminPanelSubject.asObservable();\n      this.securityInfo$ = this.securityInfoSubject.asObservable();\n      this.editingMessage$ = this.editingMessageSubject.asObservable();\n      this.connected$ = this.socketService.isConnected$; // Typing indicators\n\n      this.typingUsersSubject = new BehaviorSubject([]);\n      this.typingUsers$ = this.typingUsersSubject.asObservable();\n      this.typingTimeout = null; // Computed observables\n\n      this.isLoggedIn$ = this.user$.pipe(map(user => !!user));\n      this.isLoadingGroups = false;\n      this.isLoadingMessages = false;\n      this.lastMessageLoadTime = 0;\n      this.MESSAGE_LOAD_THROTTLE = 1000; // 1 second throttle\n\n      try {\n        this.setupSocketListeners();\n        this.setupMessageRefresh();\n      } catch (error) {\n        console.error('Error initializing ChatService:', error);\n      }\n    }\n\n    setupSocketListeners() {\n      this.socketService.on('connect', () => {\n        // Clear any loading states when connected\n        this.loadingSubject.next(false);\n      });\n      this.socketService.on('disconnect', () => {// Don't set loading to true on disconnect to avoid UI freeze\n      });\n      this.socketService.on('connect_error', error => {\n        console.error('Socket connection error:', error);\n        this.loadingSubject.next(false);\n      });\n      this.socketService.on('userGroups', userGroups => {\n        this.groupsSubject.next(userGroups || []);\n\n        if (userGroups && userGroups.length > 0) {\n          this.currentGroupSubject.next(userGroups[0]); // Load messages for the selected group\n\n          this.loadRecentMessages(userGroups[0].id);\n        } else {\n          // Clear current group and messages if no groups\n          this.currentGroupSubject.next(null);\n          this.messagesSubject.next([]);\n        }\n      });\n      this.socketService.on('recentMessages', messages => {\n        this.messagesSubject.next(messages || []);\n      });\n      this.socketService.on('groupJoined', ({\n        groupId\n      }) => {\n        const groups = this.groupsSubject.value;\n        const group = groups.find(g => g.id === groupId);\n\n        if (group) {\n          this.currentGroupSubject.next(group);\n        }\n      });\n      this.socketService.on('newMessage', message => {\n        const currentMessages = this.messagesSubject.value;\n        const currentUser = this.userSubject.value;\n        const currentGroup = this.currentGroupSubject.value; // Only process messages for the current group to avoid confusion\n\n        if (currentGroup && message.groupId !== currentGroup.id) {\n          return;\n        } // Check for duplicate messages to prevent double-display\n\n\n        const existingMessage = currentMessages.find(msg => msg.id === message.id || msg.text === message.text && msg.username === message.username && Math.abs(new Date(msg.timestamp).getTime() - new Date(message.timestamp).getTime()) < 5000);\n\n        if (existingMessage && !existingMessage.id.startsWith('temp_')) {\n          return;\n        } // Check if this is our own message (to replace optimistic message)\n\n\n        if (message.username === currentUser) {\n          // Find and replace optimistic message with real message\n          const optimisticIndex = currentMessages.findIndex(msg => msg.id.startsWith('temp_') && msg.text === message.text && msg.username === currentUser && msg.groupId === message.groupId);\n\n          if (optimisticIndex !== -1) {\n            // Replace optimistic message\n            const updatedMessages = [...currentMessages];\n            updatedMessages[optimisticIndex] = message;\n            this.messagesSubject.next(this.sortMessagesByTimestamp(updatedMessages));\n          } else {\n            // Add new message if no optimistic message found (e.g., from another tab)\n            const updatedMessages = [...currentMessages, message];\n            this.messagesSubject.next(this.sortMessagesByTimestamp(updatedMessages));\n          }\n        } else {\n          // Add message from other users\n          const updatedMessages = [...currentMessages, message];\n          this.messagesSubject.next(this.sortMessagesByTimestamp(updatedMessages));\n        } // Show notification if not current user and window not focused\n\n\n        if (message.username !== currentUser && document.hidden) {\n          this.notificationService.showMessageNotification(message.username, message.text);\n        }\n      });\n      this.socketService.on('reactionUpdate', ({\n        messageId,\n        reactions\n      }) => {\n        const currentMessages = this.messagesSubject.value;\n        this.messagesSubject.next(currentMessages.map(msg => msg.id === messageId ? { ...msg,\n          reactions\n        } : msg));\n      });\n      this.socketService.on('onlineUsersUpdate', users => {\n        this.onlineUsersSubject.next(users || []);\n      });\n      this.socketService.on('userJoined', ({\n        username\n      }) => {// Online users will be updated via onlineUsersUpdate event\n      });\n      this.socketService.on('userLeft', ({\n        username\n      }) => {// Online users will be updated via onlineUsersUpdate event\n      }); // Handle typing indicators\n\n      this.socketService.on('userTyping', ({\n        username,\n        groupId,\n        isTyping\n      }) => {\n        const currentGroup = this.currentGroupSubject.value;\n\n        if (currentGroup && groupId === currentGroup.id) {\n          const currentTypingUsers = this.typingUsersSubject.value;\n\n          if (isTyping) {\n            // Add user to typing list if not already there\n            if (!currentTypingUsers.includes(username)) {\n              this.typingUsersSubject.next([...currentTypingUsers, username]);\n            }\n          } else {\n            // Remove user from typing list\n            this.typingUsersSubject.next(currentTypingUsers.filter(user => user !== username));\n          }\n        }\n      });\n      this.socketService.on('messageUpdated', ({\n        messageId,\n        newText,\n        updatedAt\n      }) => {\n        const currentMessages = this.messagesSubject.value;\n        this.messagesSubject.next(currentMessages.map(msg => msg.id === messageId ? { ...msg,\n          text: newText,\n          updated_at: updatedAt\n        } : msg));\n      });\n      this.socketService.on('messageDeleted', ({\n        messageId\n      }) => {\n        const currentMessages = this.messagesSubject.value;\n        this.messagesSubject.next(currentMessages.filter(msg => msg.id !== messageId));\n      });\n      this.socketService.on('error', error => {\n        console.error('Socket error:', error);\n      });\n    }\n\n    setupMessageRefresh() {\n      // Clear any existing interval first\n      if (this.messageRefreshInterval) {\n        clearInterval(this.messageRefreshInterval);\n      } // Temporarily disable message refresh to prevent browser freezing\n      // TODO: Re-enable once socket connection issues are resolved\n\n      /*\r\n      this.messageRefreshInterval = setInterval(() => {\r\n        try {\r\n          if (!this.socketService.isConnectedSubject.value) {\r\n            const currentGroup = this.currentGroupSubject.value;\r\n            if (currentGroup) {\r\n              console.log('Refreshing messages via HTTP API');\r\n              this.loadRecentMessages(currentGroup.id);\r\n            }\r\n          }\r\n        } catch (error) {\r\n          console.error('Error in message refresh interval:', error);\r\n          // Clear the interval if there's an error to prevent crashes\r\n          if (this.messageRefreshInterval) {\r\n            clearInterval(this.messageRefreshInterval);\r\n            this.messageRefreshInterval = null;\r\n          }\r\n        }\r\n      }, 5000);\r\n      */\n\n    }\n\n    login(_x, _x2) {\n      var _this = this;\n\n      return _asyncToGenerator(function* (username, password, inviteCode = null) {\n        try {\n          _this.loadingSubject.next(true); // Reset socket connection state to clear any previous errors\n\n\n          _this.socketService.resetConnectionState();\n\n          const userData = yield _this.apiService.loginUser(username, password, inviteCode || undefined).toPromise(); // Check if password is required\n\n          if (userData?.requiresPassword) {\n            throw new Error('Password required for this user');\n          } // Connect to socket with auth data\n\n\n          _this.socketService.connect(username, password, inviteCode);\n\n          _this.userSubject.next(username);\n\n          _this.isAdminSubject.next(userData?.isAdmin || false);\n\n          _this.securityInfoSubject.next(userData?.securityInfo || null); // Wait for socket connection before loading groups\n          // The socket will emit 'userGroups' event which will handle group loading\n          // This prevents race conditions between API and socket calls\n          // Add fallback: if socket doesn't load groups within 5 seconds, try API\n\n\n          setTimeout(() => {\n            if (_this.groupsSubject.value.length === 0 && !_this.socketService.isConnected()) {\n              _this.loadUserGroups(username);\n            }\n          }, 5000);\n        } catch (error) {\n          console.error('Login failed:', error);\n          throw error;\n        } finally {\n          _this.loadingSubject.next(false);\n        }\n      }).apply(this, arguments);\n    }\n\n    loadRecentMessages(groupId) {\n      var _this2 = this;\n\n      return _asyncToGenerator(function* () {\n        // Safety guard to prevent infinite loops\n        if (_this2.isLoadingMessages) {\n          return;\n        } // Throttle message loading to prevent excessive API calls\n\n\n        const now = Date.now();\n\n        if (now - _this2.lastMessageLoadTime < _this2.MESSAGE_LOAD_THROTTLE) {\n          return;\n        }\n\n        _this2.lastMessageLoadTime = now;\n        _this2.isLoadingMessages = true;\n\n        try {\n          const messages = yield _this2.apiService.getMessages(groupId, 50).toPromise(); // For group switching, replace messages with only the current group's messages\n          // This ensures we only show messages for the current group\n\n          _this2.messagesSubject.next(messages || []);\n        } catch (error) {\n          console.error('Failed to load messages:', error); // Set empty array to prevent UI from showing stale data\n\n          _this2.messagesSubject.next([]);\n        } finally {\n          _this2.isLoadingMessages = false;\n        }\n      })();\n    }\n\n    loadUserGroups(username) {\n      var _this3 = this;\n\n      return _asyncToGenerator(function* () {\n        // Safety guard to prevent infinite loops\n        if (_this3.isLoadingGroups) {\n          return;\n        }\n\n        _this3.isLoadingGroups = true;\n\n        try {\n          const groups = yield _this3.apiService.getUserGroups(username).toPromise();\n\n          _this3.groupsSubject.next(groups || []);\n\n          if (groups && groups.length > 0) {\n            _this3.currentGroupSubject.next(groups[0]);\n\n            _this3.loadRecentMessages(groups[0].id);\n          }\n        } catch (error) {\n          console.error('Failed to load user groups:', error);\n        } finally {\n          _this3.isLoadingGroups = false;\n        }\n      })();\n    } // Helper method to sort messages by timestamp\n\n\n    sortMessagesByTimestamp(messages) {\n      return messages.sort((a, b) => {\n        const timeA = new Date(a.timestamp).getTime();\n        const timeB = new Date(b.timestamp).getTime();\n        return timeA - timeB;\n      });\n    } // Helper method to replace optimistic message with real message\n\n\n    replaceOptimisticMessage(tempId, realMessage) {\n      const currentMessages = this.messagesSubject.value;\n      const updatedMessages = currentMessages.map(msg => msg.id === tempId ? realMessage : msg);\n      this.messagesSubject.next(this.sortMessagesByTimestamp(updatedMessages));\n    } // Helper method to remove optimistic message (on error)\n\n\n    removeOptimisticMessage(tempId) {\n      const currentMessages = this.messagesSubject.value;\n      const updatedMessages = currentMessages.filter(msg => msg.id !== tempId);\n      this.messagesSubject.next(updatedMessages);\n    } // Helper method to check if message already exists\n\n\n    messageExists(message, messages) {\n      return messages.some(msg => msg.id === message.id || msg.text === message.text && msg.username === message.username && Math.abs(new Date(msg.timestamp).getTime() - new Date(message.timestamp).getTime()) < 5000);\n    }\n\n    joinGroup(groupId) {\n      const currentUser = this.userSubject.value;\n\n      if (!groupId || !currentUser) {\n        console.error('Cannot join group - missing groupId or user');\n        return;\n      } // Check if socket is connected before joining\n\n\n      if (!this.socketService.isConnectedSubject.value) {\n        console.error('Cannot join group - socket not connected');\n        return;\n      }\n\n      this.socketService.joinGroup(groupId);\n      const groups = this.groupsSubject.value;\n      const group = groups.find(g => g.id === groupId);\n\n      if (group) {\n        this.currentGroupSubject.next(group); // Load recent messages for the group\n\n        this.loadRecentMessages(groupId);\n      } else {\n        console.error('Group not found in user groups:', groupId);\n      }\n    }\n\n    sendMessage(text, replyToId = null) {\n      const currentUser = this.userSubject.value;\n      const currentGroup = this.currentGroupSubject.value;\n\n      if (!text.trim()) {\n        console.error('Cannot send message - empty text');\n        return;\n      }\n\n      if (!currentUser) {\n        console.error('Cannot send message - user not logged in');\n        return;\n      }\n\n      if (!currentGroup) {\n        console.error('Cannot send message - no group selected. Available groups:', this.groupsSubject.value); // Try to auto-select the first available group\n\n        const groups = this.groupsSubject.value;\n\n        if (groups && groups.length > 0) {\n          this.currentGroupSubject.next(groups[0]);\n          this.loadRecentMessages(groups[0].id); // Retry sending the message - DISABLED to prevent infinite loops\n          // setTimeout(() => this.sendMessage(text, replyToId), 100);\n\n          return;\n        } else {\n          console.error('No groups available for user');\n          return;\n        }\n      } // Create optimistic message for immediate display\n\n\n      const optimisticMessage = {\n        id: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        text: text.trim(),\n        username: currentUser,\n        timestamp: new Date().toISOString(),\n        groupId: currentGroup.id,\n        replyTo: replyToId || undefined,\n        reactions: []\n      }; // Add optimistic message to local state immediately\n\n      const currentMessages = this.messagesSubject.value;\n      const updatedMessages = [...currentMessages, optimisticMessage];\n      this.messagesSubject.next(updatedMessages); // Send message with acknowledgment and retry logic\n\n      this.sendMessageWithRetry(text, currentGroup.id, replyToId, optimisticMessage);\n    }\n\n    sendMessageWithRetry(_x3, _x4, _x5, _x6) {\n      var _this4 = this;\n\n      return _asyncToGenerator(function* (text, groupId, replyToId, optimisticMessage, retryCount = 0) {\n        const maxRetries = 3;\n        const retryDelay = 1000 * Math.pow(2, retryCount); // Exponential backoff\n\n        try {\n          if (_this4.socketService.isConnected()) {\n            // Try sending via socket with acknowledgment\n            const response = yield _this4.socketService.emitWithAck('sendMessage', {\n              text,\n              groupId,\n              replyTo: replyToId,\n              messageId: optimisticMessage.id\n            });\n\n            if (response.success) {\n              // Replace optimistic message with real message\n              _this4.replaceOptimisticMessage(optimisticMessage.id, response.message);\n\n              return;\n            } else {\n              throw new Error(response.error || 'Unknown error');\n            }\n          } else {\n            throw new Error('Socket not connected');\n          }\n        } catch (error) {\n          console.error(`❌ Failed to send message (attempt ${retryCount + 1}):`, error);\n\n          if (retryCount < maxRetries) {\n            // Retry with exponential backoff\n            setTimeout(() => {\n              _this4.sendMessageWithRetry(text, groupId, replyToId, optimisticMessage, retryCount + 1);\n            }, retryDelay);\n          } else {\n            // All retries failed, try HTTP API as final fallback\n            _this4.sendMessageViaHttpFallback(text, groupId, replyToId, optimisticMessage);\n          }\n        }\n      }).apply(this, arguments);\n    }\n\n    sendMessageViaHttpFallback(text, groupId, replyToId, optimisticMessage) {\n      const currentUser = this.userSubject.value;\n\n      if (!currentUser) {\n        this.removeOptimisticMessage(optimisticMessage.id);\n        return;\n      }\n\n      this.apiService.sendMessage(text, currentUser, groupId, replyToId).subscribe({\n        next: response => {\n          this.replaceOptimisticMessage(optimisticMessage.id, response);\n        },\n        error: error => {\n          console.error('❌ Failed to send message via HTTP API:', error);\n          this.removeOptimisticMessage(optimisticMessage.id); // Show error notification to user\n\n          this.showMessageError('Failed to send message. Please try again.');\n        }\n      });\n    }\n\n    showMessageError(message) {\n      // You can implement a toast notification service here\n      console.error('💬 Message Error:', message); // For now, just log the error. In a real app, you'd show a toast/snackbar\n    } // Typing indicator methods\n\n\n    startTyping() {\n      const currentGroup = this.currentGroupSubject.value;\n\n      if (currentGroup && this.socketService.isConnected()) {\n        this.socketService.sendTypingIndicator(currentGroup.id, true); // Clear existing timeout\n\n        if (this.typingTimeout) {\n          clearTimeout(this.typingTimeout);\n        } // Set timeout to stop typing after 3 seconds of inactivity\n\n\n        this.typingTimeout = setTimeout(() => {\n          this.stopTyping();\n        }, 3000);\n      }\n    }\n\n    stopTyping() {\n      const currentGroup = this.currentGroupSubject.value;\n\n      if (currentGroup && this.socketService.isConnected()) {\n        this.socketService.sendTypingIndicator(currentGroup.id, false); // Clear timeout\n\n        if (this.typingTimeout) {\n          clearTimeout(this.typingTimeout);\n          this.typingTimeout = null;\n        }\n      }\n    }\n\n    replyToMessage(message) {\n      this.replyToSubject.next(message);\n    }\n\n    cancelReply() {\n      this.replyToSubject.next(null);\n    }\n\n    addReaction(messageId, emoji) {\n      var _this5 = this;\n\n      return _asyncToGenerator(function* () {\n        try {\n          if (_this5.socketService.isConnected()) {\n            // Try with acknowledgment first\n            const response = yield _this5.socketService.emitWithAck('addReaction', {\n              messageId,\n              emoji\n            });\n\n            if (response.success) {} else {\n              throw new Error(response.error || 'Failed to add reaction');\n            }\n          } else {\n            // Fallback to HTTP API\n            const currentUser = _this5.userSubject.value;\n\n            if (currentUser) {\n              _this5.apiService.addReaction(messageId, emoji, currentUser).subscribe({\n                next: response => {},\n                error: error => {\n                  console.error('❌ Failed to add reaction via HTTP API:', error);\n                }\n              });\n            }\n          }\n        } catch (error) {\n          console.error('❌ Failed to add reaction:', error); // Fallback to old method\n\n          _this5.socketService.addReaction(messageId, emoji);\n        }\n      })();\n    }\n\n    removeReaction(data) {\n      this.socketService.removeReaction(data);\n    }\n\n    updateMessage(messageId, newText) {\n      var _this6 = this;\n\n      return _asyncToGenerator(function* () {\n        const currentUser = _this6.userSubject.value;\n\n        if (!currentUser) {\n          console.error('Cannot update message - user not logged in');\n          return;\n        }\n\n        try {\n          // Update the message in the local state immediately for better UX\n          const currentMessages = _this6.messagesSubject.value;\n\n          _this6.messagesSubject.next(currentMessages.map(msg => msg.id === messageId ? { ...msg,\n            text: newText,\n            updated_at: new Date().toISOString()\n          } : msg)); // Emit socket event for real-time updates\n\n\n          if (_this6.socketService.isConnectedSubject.value) {\n            _this6.socketService.updateMessage(messageId, newText);\n          } else {\n            // Fallback to HTTP API if socket not connected\n            const updatedMessage = yield _this6.apiService.updateMessage(messageId, newText, currentUser).toPromise();\n\n            _this6.messagesSubject.next(currentMessages.map(msg => msg.id === messageId ? { ...msg,\n              text: newText,\n              updated_at: updatedMessage.updated_at\n            } : msg));\n          }\n        } catch (error) {\n          console.error('Failed to update message:', error);\n          throw error;\n        }\n      })();\n    }\n\n    deleteMessage(messageId) {\n      var _this7 = this;\n\n      return _asyncToGenerator(function* () {\n        const currentUser = _this7.userSubject.value;\n\n        if (!currentUser) {\n          console.error('Cannot delete message - user not logged in');\n          return;\n        }\n\n        try {\n          // Remove the message from local state immediately for better UX\n          const currentMessages = _this7.messagesSubject.value;\n\n          _this7.messagesSubject.next(currentMessages.filter(msg => msg.id !== messageId)); // Emit socket event for real-time updates\n\n\n          if (_this7.socketService.isConnectedSubject.value) {\n            _this7.socketService.deleteMessage(messageId);\n          } else {\n            // Fallback to HTTP API if socket not connected\n            yield _this7.apiService.deleteMessage(messageId, currentUser).toPromise();\n          }\n        } catch (error) {\n          console.error('Failed to delete message:', error);\n          throw error;\n        }\n      })();\n    }\n\n    showAdminPanel() {\n      this.showAdminPanelSubject.next(true);\n    }\n\n    hideAdminPanel() {\n      this.showAdminPanelSubject.next(false);\n    }\n\n    startEditingMessage(message) {\n      this.editingMessageSubject.next(message);\n    }\n\n    cancelEditingMessage() {\n      this.editingMessageSubject.next(null);\n    }\n\n    logout() {\n      try {\n        // Disconnect socket first\n        this.socketService.disconnect(); // Clear interval\n\n        if (this.messageRefreshInterval) {\n          clearInterval(this.messageRefreshInterval);\n          this.messageRefreshInterval = null;\n        } // Reset all subjects\n\n\n        this.userSubject.next(null);\n        this.messagesSubject.next([]);\n        this.onlineUsersSubject.next([]);\n        this.groupsSubject.next([]);\n        this.currentGroupSubject.next(null);\n        this.replyToSubject.next(null);\n        this.loadingSubject.next(false);\n        this.isAdminSubject.next(false);\n        this.showAdminPanelSubject.next(false);\n        this.securityInfoSubject.next(null);\n        this.editingMessageSubject.next(null);\n      } catch (error) {\n        console.error('Error during logout:', error);\n      }\n    } // Method to refresh security info for all components\n\n\n    refreshSecurityInfo() {\n      this.apiService.getSecurityInfo().subscribe({\n        next: data => {\n          this.securityInfoSubject.next(data);\n        },\n        error: error => {\n          console.error('Failed to refresh security info:', error);\n        }\n      });\n    }\n\n  }\n\n  ChatService.ɵfac = function ChatService_Factory(t) {\n    return new (t || ChatService)(i0.ɵɵinject(i1.ApiService), i0.ɵɵinject(i2.SocketService), i0.ɵɵinject(i3.NotificationService));\n  };\n\n  ChatService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ChatService,\n    factory: ChatService.ɵfac,\n    providedIn: 'root'\n  });\n  return ChatService;\n})();", "map": null, "metadata": {}, "sourceType": "module"}