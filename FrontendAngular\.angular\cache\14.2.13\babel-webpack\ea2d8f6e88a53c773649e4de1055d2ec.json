{"ast": null, "code": "import * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollable, CdkScrollableModule } from '@angular/cdk/scrolling';\nimport * as i5 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, EventEmitter, Optional, Input, Output, ViewChild, QueryList, ContentChildren, ContentChild, NgModule } from '@angular/core';\nimport { MatCommonModule } from '@angular/material/core';\nimport * as i2 from '@angular/cdk/a11y';\nimport * as i4 from '@angular/cdk/bidi';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport * as i3 from '@angular/cdk/platform';\nimport { Subject, fromEvent, merge } from 'rxjs';\nimport { filter, map, mapTo, takeUntil, distinctUntilChanged, take, startWith, debounceTime } from 'rxjs/operators';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Animations used by the Material drawers.\n * @docs-private\n */\n\nconst _c0 = [\"*\"];\nconst _c1 = [\"content\"];\n\nfunction MatDrawerContainer_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵlistener(\"click\", function MatDrawerContainer_div_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2._onBackdropClicked());\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"mat-drawer-shown\", ctx_r0._isShowingBackdrop());\n  }\n}\n\nfunction MatDrawerContainer_mat_drawer_content_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-drawer-content\");\n    i0.ɵɵprojection(1, 2);\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c2 = [[[\"mat-drawer\"]], [[\"mat-drawer-content\"]], \"*\"];\nconst _c3 = [\"mat-drawer\", \"mat-drawer-content\", \"*\"];\n\nfunction MatSidenavContainer_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵlistener(\"click\", function MatSidenavContainer_div_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2._onBackdropClicked());\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"mat-drawer-shown\", ctx_r0._isShowingBackdrop());\n  }\n}\n\nfunction MatSidenavContainer_mat_sidenav_content_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-sidenav-content\");\n    i0.ɵɵprojection(1, 2);\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c4 = [[[\"mat-sidenav\"]], [[\"mat-sidenav-content\"]], \"*\"];\nconst _c5 = [\"mat-sidenav\", \"mat-sidenav-content\", \"*\"];\nconst _c6 = \".mat-drawer-container{position:relative;z-index:1;box-sizing:border-box;-webkit-overflow-scrolling:touch;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}.cdk-high-contrast-active .mat-drawer-backdrop{opacity:.5}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{position:relative;z-index:4;display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}.cdk-high-contrast-active .mat-drawer,.cdk-high-contrast-active [dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}.cdk-high-contrast-active [dir=rtl] .mat-drawer,.cdk-high-contrast-active .mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer{transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer[style*=\\\"visibility: hidden\\\"]{display:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto;-webkit-overflow-scrolling:touch}.mat-sidenav-fixed{position:fixed}\";\nconst matDrawerAnimations = {\n  /** Animation that slides a drawer in and out. */\n  transformDrawer: trigger('transform', [// We remove the `transform` here completely, rather than setting it to zero, because:\n  // 1. Having a transform can cause elements with ripples or an animated\n  //    transform to shift around in Chrome with an RTL layout (see #10023).\n  // 2. 3d transforms causes text to appear blurry on IE and Edge.\n  state('open, open-instant', style({\n    'transform': 'none',\n    'visibility': 'visible'\n  })), state('void', style({\n    // Avoids the shadow showing up when closed in SSR.\n    'box-shadow': 'none',\n    'visibility': 'hidden'\n  })), transition('void => open-instant', animate('0ms')), transition('void <=> open, open-instant => void', animate('400ms cubic-bezier(0.25, 0.8, 0.25, 1)'))])\n};\n/**\n * Throws an exception when two MatDrawer are matching the same position.\n * @docs-private\n */\n\nfunction throwMatDuplicatedDrawerError(position) {\n  throw Error(`A drawer was already declared for 'position=\"${position}\"'`);\n}\n/** Configures whether drawers should use auto sizing by default. */\n\n\nconst MAT_DRAWER_DEFAULT_AUTOSIZE = new InjectionToken('MAT_DRAWER_DEFAULT_AUTOSIZE', {\n  providedIn: 'root',\n  factory: MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY\n});\n/**\n * Used to provide a drawer container to a drawer while avoiding circular references.\n * @docs-private\n */\n\nconst MAT_DRAWER_CONTAINER = new InjectionToken('MAT_DRAWER_CONTAINER');\n/** @docs-private */\n\nfunction MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY() {\n  return false;\n}\n\nclass MatDrawerContent extends CdkScrollable {\n  constructor(_changeDetectorRef, _container, elementRef, scrollDispatcher, ngZone) {\n    super(elementRef, scrollDispatcher, ngZone);\n    this._changeDetectorRef = _changeDetectorRef;\n    this._container = _container;\n  }\n\n  ngAfterContentInit() {\n    this._container._contentMarginChanges.subscribe(() => {\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n\n}\n\nMatDrawerContent.ɵfac = function MatDrawerContent_Factory(t) {\n  return new (t || MatDrawerContent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(forwardRef(() => MatDrawerContainer)), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.ScrollDispatcher), i0.ɵɵdirectiveInject(i0.NgZone));\n};\n\nMatDrawerContent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatDrawerContent,\n  selectors: [[\"mat-drawer-content\"]],\n  hostAttrs: [1, \"mat-drawer-content\"],\n  hostVars: 4,\n  hostBindings: function MatDrawerContent_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵstyleProp(\"margin-left\", ctx._container._contentMargins.left, \"px\")(\"margin-right\", ctx._container._contentMargins.right, \"px\");\n    }\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkScrollable,\n    useExisting: MatDrawerContent\n  }]), i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c0,\n  decls: 1,\n  vars: 0,\n  template: function MatDrawerContent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵprojection(0);\n    }\n  },\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDrawerContent, [{\n    type: Component,\n    args: [{\n      selector: 'mat-drawer-content',\n      template: '<ng-content></ng-content>',\n      host: {\n        'class': 'mat-drawer-content',\n        '[style.margin-left.px]': '_container._contentMargins.left',\n        '[style.margin-right.px]': '_container._contentMargins.right'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [{\n        provide: CdkScrollable,\n        useExisting: MatDrawerContent\n      }]\n    }]\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }, {\n      type: MatDrawerContainer,\n      decorators: [{\n        type: Inject,\n        args: [forwardRef(() => MatDrawerContainer)]\n      }]\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i1.ScrollDispatcher\n    }, {\n      type: i0.NgZone\n    }];\n  }, null);\n})();\n/**\n * This component corresponds to a drawer that can be opened on the drawer container.\n */\n\n\nclass MatDrawer {\n  constructor(_elementRef, _focusTrapFactory, _focusMonitor, _platform, _ngZone, _interactivityChecker, _doc, _container) {\n    this._elementRef = _elementRef;\n    this._focusTrapFactory = _focusTrapFactory;\n    this._focusMonitor = _focusMonitor;\n    this._platform = _platform;\n    this._ngZone = _ngZone;\n    this._interactivityChecker = _interactivityChecker;\n    this._doc = _doc;\n    this._container = _container;\n    this._elementFocusedBeforeDrawerWasOpened = null;\n    /** Whether the drawer is initialized. Used for disabling the initial animation. */\n\n    this._enableAnimations = false;\n    this._position = 'start';\n    this._mode = 'over';\n    this._disableClose = false;\n    this._opened = false;\n    /** Emits whenever the drawer has started animating. */\n\n    this._animationStarted = new Subject();\n    /** Emits whenever the drawer is done animating. */\n\n    this._animationEnd = new Subject();\n    /** Current state of the sidenav animation. */\n\n    this._animationState = 'void';\n    /** Event emitted when the drawer open state is changed. */\n\n    this.openedChange = // Note this has to be async in order to avoid some issues with two-bindings (see #8872).\n    new EventEmitter(\n    /* isAsync */\n    true);\n    /** Event emitted when the drawer has been opened. */\n\n    this._openedStream = this.openedChange.pipe(filter(o => o), map(() => {}));\n    /** Event emitted when the drawer has started opening. */\n\n    this.openedStart = this._animationStarted.pipe(filter(e => e.fromState !== e.toState && e.toState.indexOf('open') === 0), mapTo(undefined));\n    /** Event emitted when the drawer has been closed. */\n\n    this._closedStream = this.openedChange.pipe(filter(o => !o), map(() => {}));\n    /** Event emitted when the drawer has started closing. */\n\n    this.closedStart = this._animationStarted.pipe(filter(e => e.fromState !== e.toState && e.toState === 'void'), mapTo(undefined));\n    /** Emits when the component is destroyed. */\n\n    this._destroyed = new Subject();\n    /** Event emitted when the drawer's position changes. */\n    // tslint:disable-next-line:no-output-on-prefix\n\n    this.onPositionChanged = new EventEmitter();\n    /**\n     * An observable that emits when the drawer mode changes. This is used by the drawer container to\n     * to know when to when the mode changes so it can adapt the margins on the content.\n     */\n\n    this._modeChanged = new Subject();\n    this.openedChange.subscribe(opened => {\n      if (opened) {\n        if (this._doc) {\n          this._elementFocusedBeforeDrawerWasOpened = this._doc.activeElement;\n        }\n\n        this._takeFocus();\n      } else if (this._isFocusWithinDrawer()) {\n        this._restoreFocus(this._openedVia || 'program');\n      }\n    });\n    /**\n     * Listen to `keydown` events outside the zone so that change detection is not run every\n     * time a key is pressed. Instead we re-enter the zone only if the `ESC` key is pressed\n     * and we don't have close disabled.\n     */\n\n    this._ngZone.runOutsideAngular(() => {\n      fromEvent(this._elementRef.nativeElement, 'keydown').pipe(filter(event => {\n        return event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event);\n      }), takeUntil(this._destroyed)).subscribe(event => this._ngZone.run(() => {\n        this.close();\n        event.stopPropagation();\n        event.preventDefault();\n      }));\n    }); // We need a Subject with distinctUntilChanged, because the `done` event\n    // fires twice on some browsers. See https://github.com/angular/angular/issues/24084\n\n\n    this._animationEnd.pipe(distinctUntilChanged((x, y) => {\n      return x.fromState === y.fromState && x.toState === y.toState;\n    })).subscribe(event => {\n      const {\n        fromState,\n        toState\n      } = event;\n\n      if (toState.indexOf('open') === 0 && fromState === 'void' || toState === 'void' && fromState.indexOf('open') === 0) {\n        this.openedChange.emit(this._opened);\n      }\n    });\n  }\n  /** The side that the drawer is attached to. */\n\n\n  get position() {\n    return this._position;\n  }\n\n  set position(value) {\n    // Make sure we have a valid value.\n    value = value === 'end' ? 'end' : 'start';\n\n    if (value !== this._position) {\n      // Static inputs in Ivy are set before the element is in the DOM.\n      if (this._isAttached) {\n        this._updatePositionInParent(value);\n      }\n\n      this._position = value;\n      this.onPositionChanged.emit();\n    }\n  }\n  /** Mode of the drawer; one of 'over', 'push' or 'side'. */\n\n\n  get mode() {\n    return this._mode;\n  }\n\n  set mode(value) {\n    this._mode = value;\n\n    this._updateFocusTrapState();\n\n    this._modeChanged.next();\n  }\n  /** Whether the drawer can be closed with the escape key or by clicking on the backdrop. */\n\n\n  get disableClose() {\n    return this._disableClose;\n  }\n\n  set disableClose(value) {\n    this._disableClose = coerceBooleanProperty(value);\n  }\n  /**\n   * Whether the drawer should focus the first focusable element automatically when opened.\n   * Defaults to false in when `mode` is set to `side`, otherwise defaults to `true`. If explicitly\n   * enabled, focus will be moved into the sidenav in `side` mode as well.\n   * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or AutoFocusTarget\n   * instead.\n   */\n\n\n  get autoFocus() {\n    const value = this._autoFocus; // Note that usually we don't allow autoFocus to be set to `first-tabbable` in `side` mode,\n    // because we don't know how the sidenav is being used, but in some cases it still makes\n    // sense to do it. The consumer can explicitly set `autoFocus`.\n\n    if (value == null) {\n      if (this.mode === 'side') {\n        return 'dialog';\n      } else {\n        return 'first-tabbable';\n      }\n    }\n\n    return value;\n  }\n\n  set autoFocus(value) {\n    if (value === 'true' || value === 'false' || value == null) {\n      value = coerceBooleanProperty(value);\n    }\n\n    this._autoFocus = value;\n  }\n  /**\n   * Whether the drawer is opened. We overload this because we trigger an event when it\n   * starts or end.\n   */\n\n\n  get opened() {\n    return this._opened;\n  }\n\n  set opened(value) {\n    this.toggle(coerceBooleanProperty(value));\n  }\n  /**\n   * Focuses the provided element. If the element is not focusable, it will add a tabIndex\n   * attribute to forcefully focus it. The attribute is removed after focus is moved.\n   * @param element The element to focus.\n   */\n\n\n  _forceFocus(element, options) {\n    if (!this._interactivityChecker.isFocusable(element)) {\n      element.tabIndex = -1; // The tabindex attribute should be removed to avoid navigating to that element again\n\n      this._ngZone.runOutsideAngular(() => {\n        const callback = () => {\n          element.removeEventListener('blur', callback);\n          element.removeEventListener('mousedown', callback);\n          element.removeAttribute('tabindex');\n        };\n\n        element.addEventListener('blur', callback);\n        element.addEventListener('mousedown', callback);\n      });\n    }\n\n    element.focus(options);\n  }\n  /**\n   * Focuses the first element that matches the given selector within the focus trap.\n   * @param selector The CSS selector for the element to set focus to.\n   */\n\n\n  _focusByCssSelector(selector, options) {\n    let elementToFocus = this._elementRef.nativeElement.querySelector(selector);\n\n    if (elementToFocus) {\n      this._forceFocus(elementToFocus, options);\n    }\n  }\n  /**\n   * Moves focus into the drawer. Note that this works even if\n   * the focus trap is disabled in `side` mode.\n   */\n\n\n  _takeFocus() {\n    if (!this._focusTrap) {\n      return;\n    }\n\n    const element = this._elementRef.nativeElement; // When autoFocus is not on the sidenav, if the element cannot be focused or does\n    // not exist, focus the sidenav itself so the keyboard navigation still works.\n    // We need to check that `focus` is a function due to Universal.\n\n    switch (this.autoFocus) {\n      case false:\n      case 'dialog':\n        return;\n\n      case true:\n      case 'first-tabbable':\n        this._focusTrap.focusInitialElementWhenReady().then(hasMovedFocus => {\n          if (!hasMovedFocus && typeof this._elementRef.nativeElement.focus === 'function') {\n            element.focus();\n          }\n        });\n\n        break;\n\n      case 'first-heading':\n        this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role=\"heading\"]');\n\n        break;\n\n      default:\n        this._focusByCssSelector(this.autoFocus);\n\n        break;\n    }\n  }\n  /**\n   * Restores focus to the element that was originally focused when the drawer opened.\n   * If no element was focused at that time, the focus will be restored to the drawer.\n   */\n\n\n  _restoreFocus(focusOrigin) {\n    if (this.autoFocus === 'dialog') {\n      return;\n    }\n\n    if (this._elementFocusedBeforeDrawerWasOpened) {\n      this._focusMonitor.focusVia(this._elementFocusedBeforeDrawerWasOpened, focusOrigin);\n    } else {\n      this._elementRef.nativeElement.blur();\n    }\n\n    this._elementFocusedBeforeDrawerWasOpened = null;\n  }\n  /** Whether focus is currently within the drawer. */\n\n\n  _isFocusWithinDrawer() {\n    const activeEl = this._doc.activeElement;\n    return !!activeEl && this._elementRef.nativeElement.contains(activeEl);\n  }\n\n  ngAfterViewInit() {\n    this._isAttached = true;\n    this._focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement);\n\n    this._updateFocusTrapState(); // Only update the DOM position when the sidenav is positioned at\n    // the end since we project the sidenav before the content by default.\n\n\n    if (this._position === 'end') {\n      this._updatePositionInParent('end');\n    }\n  }\n\n  ngAfterContentChecked() {\n    // Enable the animations after the lifecycle hooks have run, in order to avoid animating\n    // drawers that are open by default. When we're on the server, we shouldn't enable the\n    // animations, because we don't want the drawer to animate the first time the user sees\n    // the page.\n    if (this._platform.isBrowser) {\n      this._enableAnimations = true;\n    }\n  }\n\n  ngOnDestroy() {\n    if (this._focusTrap) {\n      this._focusTrap.destroy();\n    }\n\n    this._anchor?.remove();\n    this._anchor = null;\n\n    this._animationStarted.complete();\n\n    this._animationEnd.complete();\n\n    this._modeChanged.complete();\n\n    this._destroyed.next();\n\n    this._destroyed.complete();\n  }\n  /**\n   * Open the drawer.\n   * @param openedVia Whether the drawer was opened by a key press, mouse click or programmatically.\n   * Used for focus management after the sidenav is closed.\n   */\n\n\n  open(openedVia) {\n    return this.toggle(true, openedVia);\n  }\n  /** Close the drawer. */\n\n\n  close() {\n    return this.toggle(false);\n  }\n  /** Closes the drawer with context that the backdrop was clicked. */\n\n\n  _closeViaBackdropClick() {\n    // If the drawer is closed upon a backdrop click, we always want to restore focus. We\n    // don't need to check whether focus is currently in the drawer, as clicking on the\n    // backdrop causes blurs the active element.\n    return this._setOpen(\n    /* isOpen */\n    false,\n    /* restoreFocus */\n    true, 'mouse');\n  }\n  /**\n   * Toggle this drawer.\n   * @param isOpen Whether the drawer should be open.\n   * @param openedVia Whether the drawer was opened by a key press, mouse click or programmatically.\n   * Used for focus management after the sidenav is closed.\n   */\n\n\n  toggle(isOpen = !this.opened, openedVia) {\n    // If the focus is currently inside the drawer content and we are closing the drawer,\n    // restore the focus to the initially focused element (when the drawer opened).\n    if (isOpen && openedVia) {\n      this._openedVia = openedVia;\n    }\n\n    const result = this._setOpen(isOpen,\n    /* restoreFocus */\n    !isOpen && this._isFocusWithinDrawer(), this._openedVia || 'program');\n\n    if (!isOpen) {\n      this._openedVia = null;\n    }\n\n    return result;\n  }\n  /**\n   * Toggles the opened state of the drawer.\n   * @param isOpen Whether the drawer should open or close.\n   * @param restoreFocus Whether focus should be restored on close.\n   * @param focusOrigin Origin to use when restoring focus.\n   */\n\n\n  _setOpen(isOpen, restoreFocus, focusOrigin) {\n    this._opened = isOpen;\n\n    if (isOpen) {\n      this._animationState = this._enableAnimations ? 'open' : 'open-instant';\n    } else {\n      this._animationState = 'void';\n\n      if (restoreFocus) {\n        this._restoreFocus(focusOrigin);\n      }\n    }\n\n    this._updateFocusTrapState();\n\n    return new Promise(resolve => {\n      this.openedChange.pipe(take(1)).subscribe(open => resolve(open ? 'open' : 'close'));\n    });\n  }\n\n  _getWidth() {\n    return this._elementRef.nativeElement ? this._elementRef.nativeElement.offsetWidth || 0 : 0;\n  }\n  /** Updates the enabled state of the focus trap. */\n\n\n  _updateFocusTrapState() {\n    if (this._focusTrap) {\n      // The focus trap is only enabled when the drawer is open in any mode other than side.\n      this._focusTrap.enabled = this.opened && this.mode !== 'side';\n    }\n  }\n  /**\n   * Updates the position of the drawer in the DOM. We need to move the element around ourselves\n   * when it's in the `end` position so that it comes after the content and the visual order\n   * matches the tab order. We also need to be able to move it back to `start` if the sidenav\n   * started off as `end` and was changed to `start`.\n   */\n\n\n  _updatePositionInParent(newPosition) {\n    const element = this._elementRef.nativeElement;\n    const parent = element.parentNode;\n\n    if (newPosition === 'end') {\n      if (!this._anchor) {\n        this._anchor = this._doc.createComment('mat-drawer-anchor');\n        parent.insertBefore(this._anchor, element);\n      }\n\n      parent.appendChild(element);\n    } else if (this._anchor) {\n      this._anchor.parentNode.insertBefore(element, this._anchor);\n    }\n  }\n\n}\n\nMatDrawer.ɵfac = function MatDrawer_Factory(t) {\n  return new (t || MatDrawer)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.FocusTrapFactory), i0.ɵɵdirectiveInject(i2.FocusMonitor), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.InteractivityChecker), i0.ɵɵdirectiveInject(DOCUMENT, 8), i0.ɵɵdirectiveInject(MAT_DRAWER_CONTAINER, 8));\n};\n\nMatDrawer.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatDrawer,\n  selectors: [[\"mat-drawer\"]],\n  viewQuery: function MatDrawer_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c1, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._content = _t.first);\n    }\n  },\n  hostAttrs: [\"tabIndex\", \"-1\", 1, \"mat-drawer\"],\n  hostVars: 12,\n  hostBindings: function MatDrawer_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵsyntheticHostListener(\"@transform.start\", function MatDrawer_animation_transform_start_HostBindingHandler($event) {\n        return ctx._animationStarted.next($event);\n      })(\"@transform.done\", function MatDrawer_animation_transform_done_HostBindingHandler($event) {\n        return ctx._animationEnd.next($event);\n      });\n    }\n\n    if (rf & 2) {\n      i0.ɵɵattribute(\"align\", null);\n      i0.ɵɵsyntheticHostProperty(\"@transform\", ctx._animationState);\n      i0.ɵɵclassProp(\"mat-drawer-end\", ctx.position === \"end\")(\"mat-drawer-over\", ctx.mode === \"over\")(\"mat-drawer-push\", ctx.mode === \"push\")(\"mat-drawer-side\", ctx.mode === \"side\")(\"mat-drawer-opened\", ctx.opened);\n    }\n  },\n  inputs: {\n    position: \"position\",\n    mode: \"mode\",\n    disableClose: \"disableClose\",\n    autoFocus: \"autoFocus\",\n    opened: \"opened\"\n  },\n  outputs: {\n    openedChange: \"openedChange\",\n    _openedStream: \"opened\",\n    openedStart: \"openedStart\",\n    _closedStream: \"closed\",\n    closedStart: \"closedStart\",\n    onPositionChanged: \"positionChanged\"\n  },\n  exportAs: [\"matDrawer\"],\n  ngContentSelectors: _c0,\n  decls: 3,\n  vars: 0,\n  consts: [[\"cdkScrollable\", \"\", 1, \"mat-drawer-inner-container\"], [\"content\", \"\"]],\n  template: function MatDrawer_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"div\", 0, 1);\n      i0.ɵɵprojection(2);\n      i0.ɵɵelementEnd();\n    }\n  },\n  dependencies: [i1.CdkScrollable],\n  encapsulation: 2,\n  data: {\n    animation: [matDrawerAnimations.transformDrawer]\n  },\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDrawer, [{\n    type: Component,\n    args: [{\n      selector: 'mat-drawer',\n      exportAs: 'matDrawer',\n      animations: [matDrawerAnimations.transformDrawer],\n      host: {\n        'class': 'mat-drawer',\n        // must prevent the browser from aligning text based on value\n        '[attr.align]': 'null',\n        '[class.mat-drawer-end]': 'position === \"end\"',\n        '[class.mat-drawer-over]': 'mode === \"over\"',\n        '[class.mat-drawer-push]': 'mode === \"push\"',\n        '[class.mat-drawer-side]': 'mode === \"side\"',\n        '[class.mat-drawer-opened]': 'opened',\n        'tabIndex': '-1',\n        '[@transform]': '_animationState',\n        '(@transform.start)': '_animationStarted.next($event)',\n        '(@transform.done)': '_animationEnd.next($event)'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: \"<div class=\\\"mat-drawer-inner-container\\\" cdkScrollable #content>\\r\\n  <ng-content></ng-content>\\r\\n</div>\\r\\n\"\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i2.FocusTrapFactory\n    }, {\n      type: i2.FocusMonitor\n    }, {\n      type: i3.Platform\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i2.InteractivityChecker\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: MatDrawerContainer,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_DRAWER_CONTAINER]\n      }]\n    }];\n  }, {\n    position: [{\n      type: Input\n    }],\n    mode: [{\n      type: Input\n    }],\n    disableClose: [{\n      type: Input\n    }],\n    autoFocus: [{\n      type: Input\n    }],\n    opened: [{\n      type: Input\n    }],\n    openedChange: [{\n      type: Output\n    }],\n    _openedStream: [{\n      type: Output,\n      args: ['opened']\n    }],\n    openedStart: [{\n      type: Output\n    }],\n    _closedStream: [{\n      type: Output,\n      args: ['closed']\n    }],\n    closedStart: [{\n      type: Output\n    }],\n    onPositionChanged: [{\n      type: Output,\n      args: ['positionChanged']\n    }],\n    _content: [{\n      type: ViewChild,\n      args: ['content']\n    }]\n  });\n})();\n/**\n * `<mat-drawer-container>` component.\n *\n * This is the parent component to one or two `<mat-drawer>`s that validates the state internally\n * and coordinates the backdrop and content styling.\n */\n\n\nclass MatDrawerContainer {\n  constructor(_dir, _element, _ngZone, _changeDetectorRef, viewportRuler, defaultAutosize = false, _animationMode) {\n    this._dir = _dir;\n    this._element = _element;\n    this._ngZone = _ngZone;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._animationMode = _animationMode;\n    /** Drawers that belong to this container. */\n\n    this._drawers = new QueryList();\n    /** Event emitted when the drawer backdrop is clicked. */\n\n    this.backdropClick = new EventEmitter();\n    /** Emits when the component is destroyed. */\n\n    this._destroyed = new Subject();\n    /** Emits on every ngDoCheck. Used for debouncing reflows. */\n\n    this._doCheckSubject = new Subject();\n    /**\n     * Margins to be applied to the content. These are used to push / shrink the drawer content when a\n     * drawer is open. We use margin rather than transform even for push mode because transform breaks\n     * fixed position elements inside of the transformed element.\n     */\n\n    this._contentMargins = {\n      left: null,\n      right: null\n    };\n    this._contentMarginChanges = new Subject(); // If a `Dir` directive exists up the tree, listen direction changes\n    // and update the left/right properties to point to the proper start/end.\n\n    if (_dir) {\n      _dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => {\n        this._validateDrawers();\n\n        this.updateContentMargins();\n      });\n    } // Since the minimum width of the sidenav depends on the viewport width,\n    // we need to recompute the margins if the viewport changes.\n\n\n    viewportRuler.change().pipe(takeUntil(this._destroyed)).subscribe(() => this.updateContentMargins());\n    this._autosize = defaultAutosize;\n  }\n  /** The drawer child with the `start` position. */\n\n\n  get start() {\n    return this._start;\n  }\n  /** The drawer child with the `end` position. */\n\n\n  get end() {\n    return this._end;\n  }\n  /**\n   * Whether to automatically resize the container whenever\n   * the size of any of its drawers changes.\n   *\n   * **Use at your own risk!** Enabling this option can cause layout thrashing by measuring\n   * the drawers on every change detection cycle. Can be configured globally via the\n   * `MAT_DRAWER_DEFAULT_AUTOSIZE` token.\n   */\n\n\n  get autosize() {\n    return this._autosize;\n  }\n\n  set autosize(value) {\n    this._autosize = coerceBooleanProperty(value);\n  }\n  /**\n   * Whether the drawer container should have a backdrop while one of the sidenavs is open.\n   * If explicitly set to `true`, the backdrop will be enabled for drawers in the `side`\n   * mode as well.\n   */\n\n\n  get hasBackdrop() {\n    if (this._backdropOverride == null) {\n      return !this._start || this._start.mode !== 'side' || !this._end || this._end.mode !== 'side';\n    }\n\n    return this._backdropOverride;\n  }\n\n  set hasBackdrop(value) {\n    this._backdropOverride = value == null ? null : coerceBooleanProperty(value);\n  }\n  /** Reference to the CdkScrollable instance that wraps the scrollable content. */\n\n\n  get scrollable() {\n    return this._userContent || this._content;\n  }\n\n  ngAfterContentInit() {\n    this._allDrawers.changes.pipe(startWith(this._allDrawers), takeUntil(this._destroyed)).subscribe(drawer => {\n      this._drawers.reset(drawer.filter(item => !item._container || item._container === this));\n\n      this._drawers.notifyOnChanges();\n    });\n\n    this._drawers.changes.pipe(startWith(null)).subscribe(() => {\n      this._validateDrawers();\n\n      this._drawers.forEach(drawer => {\n        this._watchDrawerToggle(drawer);\n\n        this._watchDrawerPosition(drawer);\n\n        this._watchDrawerMode(drawer);\n      });\n\n      if (!this._drawers.length || this._isDrawerOpen(this._start) || this._isDrawerOpen(this._end)) {\n        this.updateContentMargins();\n      }\n\n      this._changeDetectorRef.markForCheck();\n    }); // Avoid hitting the NgZone through the debounce timeout.\n\n\n    this._ngZone.runOutsideAngular(() => {\n      this._doCheckSubject.pipe(debounceTime(10), // Arbitrary debounce time, less than a frame at 60fps\n      takeUntil(this._destroyed)).subscribe(() => this.updateContentMargins());\n    });\n  }\n\n  ngOnDestroy() {\n    this._contentMarginChanges.complete();\n\n    this._doCheckSubject.complete();\n\n    this._drawers.destroy();\n\n    this._destroyed.next();\n\n    this._destroyed.complete();\n  }\n  /** Calls `open` of both start and end drawers */\n\n\n  open() {\n    this._drawers.forEach(drawer => drawer.open());\n  }\n  /** Calls `close` of both start and end drawers */\n\n\n  close() {\n    this._drawers.forEach(drawer => drawer.close());\n  }\n  /**\n   * Recalculates and updates the inline styles for the content. Note that this should be used\n   * sparingly, because it causes a reflow.\n   */\n\n\n  updateContentMargins() {\n    // 1. For drawers in `over` mode, they don't affect the content.\n    // 2. For drawers in `side` mode they should shrink the content. We do this by adding to the\n    //    left margin (for left drawer) or right margin (for right the drawer).\n    // 3. For drawers in `push` mode the should shift the content without resizing it. We do this by\n    //    adding to the left or right margin and simultaneously subtracting the same amount of\n    //    margin from the other side.\n    let left = 0;\n    let right = 0;\n\n    if (this._left && this._left.opened) {\n      if (this._left.mode == 'side') {\n        left += this._left._getWidth();\n      } else if (this._left.mode == 'push') {\n        const width = this._left._getWidth();\n\n        left += width;\n        right -= width;\n      }\n    }\n\n    if (this._right && this._right.opened) {\n      if (this._right.mode == 'side') {\n        right += this._right._getWidth();\n      } else if (this._right.mode == 'push') {\n        const width = this._right._getWidth();\n\n        right += width;\n        left -= width;\n      }\n    } // If either `right` or `left` is zero, don't set a style to the element. This\n    // allows users to specify a custom size via CSS class in SSR scenarios where the\n    // measured widths will always be zero. Note that we reset to `null` here, rather\n    // than below, in order to ensure that the types in the `if` below are consistent.\n\n\n    left = left || null;\n    right = right || null;\n\n    if (left !== this._contentMargins.left || right !== this._contentMargins.right) {\n      this._contentMargins = {\n        left,\n        right\n      }; // Pull back into the NgZone since in some cases we could be outside. We need to be careful\n      // to do it only when something changed, otherwise we can end up hitting the zone too often.\n\n      this._ngZone.run(() => this._contentMarginChanges.next(this._contentMargins));\n    }\n  }\n\n  ngDoCheck() {\n    // If users opted into autosizing, do a check every change detection cycle.\n    if (this._autosize && this._isPushed()) {\n      // Run outside the NgZone, otherwise the debouncer will throw us into an infinite loop.\n      this._ngZone.runOutsideAngular(() => this._doCheckSubject.next());\n    }\n  }\n  /**\n   * Subscribes to drawer events in order to set a class on the main container element when the\n   * drawer is open and the backdrop is visible. This ensures any overflow on the container element\n   * is properly hidden.\n   */\n\n\n  _watchDrawerToggle(drawer) {\n    drawer._animationStarted.pipe(filter(event => event.fromState !== event.toState), takeUntil(this._drawers.changes)).subscribe(event => {\n      // Set the transition class on the container so that the animations occur. This should not\n      // be set initially because animations should only be triggered via a change in state.\n      if (event.toState !== 'open-instant' && this._animationMode !== 'NoopAnimations') {\n        this._element.nativeElement.classList.add('mat-drawer-transition');\n      }\n\n      this.updateContentMargins();\n\n      this._changeDetectorRef.markForCheck();\n    });\n\n    if (drawer.mode !== 'side') {\n      drawer.openedChange.pipe(takeUntil(this._drawers.changes)).subscribe(() => this._setContainerClass(drawer.opened));\n    }\n  }\n  /**\n   * Subscribes to drawer onPositionChanged event in order to\n   * re-validate drawers when the position changes.\n   */\n\n\n  _watchDrawerPosition(drawer) {\n    if (!drawer) {\n      return;\n    } // NOTE: We need to wait for the microtask queue to be empty before validating,\n    // since both drawers may be swapping positions at the same time.\n\n\n    drawer.onPositionChanged.pipe(takeUntil(this._drawers.changes)).subscribe(() => {\n      this._ngZone.onMicrotaskEmpty.pipe(take(1)).subscribe(() => {\n        this._validateDrawers();\n      });\n    });\n  }\n  /** Subscribes to changes in drawer mode so we can run change detection. */\n\n\n  _watchDrawerMode(drawer) {\n    if (drawer) {\n      drawer._modeChanged.pipe(takeUntil(merge(this._drawers.changes, this._destroyed))).subscribe(() => {\n        this.updateContentMargins();\n\n        this._changeDetectorRef.markForCheck();\n      });\n    }\n  }\n  /** Toggles the 'mat-drawer-opened' class on the main 'mat-drawer-container' element. */\n\n\n  _setContainerClass(isAdd) {\n    const classList = this._element.nativeElement.classList;\n    const className = 'mat-drawer-container-has-open';\n\n    if (isAdd) {\n      classList.add(className);\n    } else {\n      classList.remove(className);\n    }\n  }\n  /** Validate the state of the drawer children components. */\n\n\n  _validateDrawers() {\n    this._start = this._end = null; // Ensure that we have at most one start and one end drawer.\n\n    this._drawers.forEach(drawer => {\n      if (drawer.position == 'end') {\n        if (this._end != null && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n          throwMatDuplicatedDrawerError('end');\n        }\n\n        this._end = drawer;\n      } else {\n        if (this._start != null && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n          throwMatDuplicatedDrawerError('start');\n        }\n\n        this._start = drawer;\n      }\n    });\n\n    this._right = this._left = null; // Detect if we're LTR or RTL.\n\n    if (this._dir && this._dir.value === 'rtl') {\n      this._left = this._end;\n      this._right = this._start;\n    } else {\n      this._left = this._start;\n      this._right = this._end;\n    }\n  }\n  /** Whether the container is being pushed to the side by one of the drawers. */\n\n\n  _isPushed() {\n    return this._isDrawerOpen(this._start) && this._start.mode != 'over' || this._isDrawerOpen(this._end) && this._end.mode != 'over';\n  }\n\n  _onBackdropClicked() {\n    this.backdropClick.emit();\n\n    this._closeModalDrawersViaBackdrop();\n  }\n\n  _closeModalDrawersViaBackdrop() {\n    // Close all open drawers where closing is not disabled and the mode is not `side`.\n    [this._start, this._end].filter(drawer => drawer && !drawer.disableClose && this._canHaveBackdrop(drawer)).forEach(drawer => drawer._closeViaBackdropClick());\n  }\n\n  _isShowingBackdrop() {\n    return this._isDrawerOpen(this._start) && this._canHaveBackdrop(this._start) || this._isDrawerOpen(this._end) && this._canHaveBackdrop(this._end);\n  }\n\n  _canHaveBackdrop(drawer) {\n    return drawer.mode !== 'side' || !!this._backdropOverride;\n  }\n\n  _isDrawerOpen(drawer) {\n    return drawer != null && drawer.opened;\n  }\n\n}\n\nMatDrawerContainer.ɵfac = function MatDrawerContainer_Factory(t) {\n  return new (t || MatDrawerContainer)(i0.ɵɵdirectiveInject(i4.Directionality, 8), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.ViewportRuler), i0.ɵɵdirectiveInject(MAT_DRAWER_DEFAULT_AUTOSIZE), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n};\n\nMatDrawerContainer.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatDrawerContainer,\n  selectors: [[\"mat-drawer-container\"]],\n  contentQueries: function MatDrawerContainer_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MatDrawerContent, 5);\n      i0.ɵɵcontentQuery(dirIndex, MatDrawer, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._content = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allDrawers = _t);\n    }\n  },\n  viewQuery: function MatDrawerContainer_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(MatDrawerContent, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._userContent = _t.first);\n    }\n  },\n  hostAttrs: [1, \"mat-drawer-container\"],\n  hostVars: 2,\n  hostBindings: function MatDrawerContainer_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-drawer-container-explicit-backdrop\", ctx._backdropOverride);\n    }\n  },\n  inputs: {\n    autosize: \"autosize\",\n    hasBackdrop: \"hasBackdrop\"\n  },\n  outputs: {\n    backdropClick: \"backdropClick\"\n  },\n  exportAs: [\"matDrawerContainer\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_DRAWER_CONTAINER,\n    useExisting: MatDrawerContainer\n  }])],\n  ngContentSelectors: _c3,\n  decls: 4,\n  vars: 2,\n  consts: [[\"class\", \"mat-drawer-backdrop\", 3, \"mat-drawer-shown\", \"click\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"mat-drawer-backdrop\", 3, \"click\"]],\n  template: function MatDrawerContainer_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c2);\n      i0.ɵɵtemplate(0, MatDrawerContainer_div_0_Template, 1, 2, \"div\", 0);\n      i0.ɵɵprojection(1);\n      i0.ɵɵprojection(2, 1);\n      i0.ɵɵtemplate(3, MatDrawerContainer_mat_drawer_content_3_Template, 2, 0, \"mat-drawer-content\", 1);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.hasBackdrop);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", !ctx._content);\n    }\n  },\n  dependencies: [i5.NgIf, MatDrawerContent],\n  styles: [\".mat-drawer-container{position:relative;z-index:1;box-sizing:border-box;-webkit-overflow-scrolling:touch;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}.cdk-high-contrast-active .mat-drawer-backdrop{opacity:.5}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{position:relative;z-index:4;display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}.cdk-high-contrast-active .mat-drawer,.cdk-high-contrast-active [dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}.cdk-high-contrast-active [dir=rtl] .mat-drawer,.cdk-high-contrast-active .mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer{transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer[style*=\\\"visibility: hidden\\\"]{display:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto;-webkit-overflow-scrolling:touch}.mat-sidenav-fixed{position:fixed}\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDrawerContainer, [{\n    type: Component,\n    args: [{\n      selector: 'mat-drawer-container',\n      exportAs: 'matDrawerContainer',\n      host: {\n        'class': 'mat-drawer-container',\n        '[class.mat-drawer-container-explicit-backdrop]': '_backdropOverride'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [{\n        provide: MAT_DRAWER_CONTAINER,\n        useExisting: MatDrawerContainer\n      }],\n      template: \"<div class=\\\"mat-drawer-backdrop\\\" (click)=\\\"_onBackdropClicked()\\\" *ngIf=\\\"hasBackdrop\\\"\\n     [class.mat-drawer-shown]=\\\"_isShowingBackdrop()\\\"></div>\\n\\n<ng-content select=\\\"mat-drawer\\\"></ng-content>\\n\\n<ng-content select=\\\"mat-drawer-content\\\">\\n</ng-content>\\n<mat-drawer-content *ngIf=\\\"!_content\\\">\\n  <ng-content></ng-content>\\n</mat-drawer-content>\\n\",\n      styles: [\".mat-drawer-container{position:relative;z-index:1;box-sizing:border-box;-webkit-overflow-scrolling:touch;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}.cdk-high-contrast-active .mat-drawer-backdrop{opacity:.5}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{position:relative;z-index:4;display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}.cdk-high-contrast-active .mat-drawer,.cdk-high-contrast-active [dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}.cdk-high-contrast-active [dir=rtl] .mat-drawer,.cdk-high-contrast-active .mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer{transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer[style*=\\\"visibility: hidden\\\"]{display:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto;-webkit-overflow-scrolling:touch}.mat-sidenav-fixed{position:fixed}\"]\n    }]\n  }], function () {\n    return [{\n      type: i4.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1.ViewportRuler\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_DRAWER_DEFAULT_AUTOSIZE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    _allDrawers: [{\n      type: ContentChildren,\n      args: [MatDrawer, {\n        // We need to use `descendants: true`, because Ivy will no longer match\n        // indirect descendants if it's left as false.\n        descendants: true\n      }]\n    }],\n    _content: [{\n      type: ContentChild,\n      args: [MatDrawerContent]\n    }],\n    _userContent: [{\n      type: ViewChild,\n      args: [MatDrawerContent]\n    }],\n    autosize: [{\n      type: Input\n    }],\n    hasBackdrop: [{\n      type: Input\n    }],\n    backdropClick: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass MatSidenavContent extends MatDrawerContent {\n  constructor(changeDetectorRef, container, elementRef, scrollDispatcher, ngZone) {\n    super(changeDetectorRef, container, elementRef, scrollDispatcher, ngZone);\n  }\n\n}\n\nMatSidenavContent.ɵfac = function MatSidenavContent_Factory(t) {\n  return new (t || MatSidenavContent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(forwardRef(() => MatSidenavContainer)), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.ScrollDispatcher), i0.ɵɵdirectiveInject(i0.NgZone));\n};\n\nMatSidenavContent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatSidenavContent,\n  selectors: [[\"mat-sidenav-content\"]],\n  hostAttrs: [1, \"mat-drawer-content\", \"mat-sidenav-content\"],\n  hostVars: 4,\n  hostBindings: function MatSidenavContent_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵstyleProp(\"margin-left\", ctx._container._contentMargins.left, \"px\")(\"margin-right\", ctx._container._contentMargins.right, \"px\");\n    }\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkScrollable,\n    useExisting: MatSidenavContent\n  }]), i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c0,\n  decls: 1,\n  vars: 0,\n  template: function MatSidenavContent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵprojection(0);\n    }\n  },\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSidenavContent, [{\n    type: Component,\n    args: [{\n      selector: 'mat-sidenav-content',\n      template: '<ng-content></ng-content>',\n      host: {\n        'class': 'mat-drawer-content mat-sidenav-content',\n        '[style.margin-left.px]': '_container._contentMargins.left',\n        '[style.margin-right.px]': '_container._contentMargins.right'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [{\n        provide: CdkScrollable,\n        useExisting: MatSidenavContent\n      }]\n    }]\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }, {\n      type: MatSidenavContainer,\n      decorators: [{\n        type: Inject,\n        args: [forwardRef(() => MatSidenavContainer)]\n      }]\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i1.ScrollDispatcher\n    }, {\n      type: i0.NgZone\n    }];\n  }, null);\n})();\n\nclass MatSidenav extends MatDrawer {\n  constructor() {\n    super(...arguments);\n    this._fixedInViewport = false;\n    this._fixedTopGap = 0;\n    this._fixedBottomGap = 0;\n  }\n  /** Whether the sidenav is fixed in the viewport. */\n\n\n  get fixedInViewport() {\n    return this._fixedInViewport;\n  }\n\n  set fixedInViewport(value) {\n    this._fixedInViewport = coerceBooleanProperty(value);\n  }\n  /**\n   * The gap between the top of the sidenav and the top of the viewport when the sidenav is in fixed\n   * mode.\n   */\n\n\n  get fixedTopGap() {\n    return this._fixedTopGap;\n  }\n\n  set fixedTopGap(value) {\n    this._fixedTopGap = coerceNumberProperty(value);\n  }\n  /**\n   * The gap between the bottom of the sidenav and the bottom of the viewport when the sidenav is in\n   * fixed mode.\n   */\n\n\n  get fixedBottomGap() {\n    return this._fixedBottomGap;\n  }\n\n  set fixedBottomGap(value) {\n    this._fixedBottomGap = coerceNumberProperty(value);\n  }\n\n}\n\nMatSidenav.ɵfac = /* @__PURE__ */function () {\n  let ɵMatSidenav_BaseFactory;\n  return function MatSidenav_Factory(t) {\n    return (ɵMatSidenav_BaseFactory || (ɵMatSidenav_BaseFactory = i0.ɵɵgetInheritedFactory(MatSidenav)))(t || MatSidenav);\n  };\n}();\n\nMatSidenav.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatSidenav,\n  selectors: [[\"mat-sidenav\"]],\n  hostAttrs: [\"tabIndex\", \"-1\", 1, \"mat-drawer\", \"mat-sidenav\"],\n  hostVars: 17,\n  hostBindings: function MatSidenav_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"align\", null);\n      i0.ɵɵstyleProp(\"top\", ctx.fixedInViewport ? ctx.fixedTopGap : null, \"px\")(\"bottom\", ctx.fixedInViewport ? ctx.fixedBottomGap : null, \"px\");\n      i0.ɵɵclassProp(\"mat-drawer-end\", ctx.position === \"end\")(\"mat-drawer-over\", ctx.mode === \"over\")(\"mat-drawer-push\", ctx.mode === \"push\")(\"mat-drawer-side\", ctx.mode === \"side\")(\"mat-drawer-opened\", ctx.opened)(\"mat-sidenav-fixed\", ctx.fixedInViewport);\n    }\n  },\n  inputs: {\n    fixedInViewport: \"fixedInViewport\",\n    fixedTopGap: \"fixedTopGap\",\n    fixedBottomGap: \"fixedBottomGap\"\n  },\n  exportAs: [\"matSidenav\"],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c0,\n  decls: 3,\n  vars: 0,\n  consts: [[\"cdkScrollable\", \"\", 1, \"mat-drawer-inner-container\"], [\"content\", \"\"]],\n  template: function MatSidenav_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"div\", 0, 1);\n      i0.ɵɵprojection(2);\n      i0.ɵɵelementEnd();\n    }\n  },\n  dependencies: [i1.CdkScrollable],\n  encapsulation: 2,\n  data: {\n    animation: [matDrawerAnimations.transformDrawer]\n  },\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSidenav, [{\n    type: Component,\n    args: [{\n      selector: 'mat-sidenav',\n      exportAs: 'matSidenav',\n      animations: [matDrawerAnimations.transformDrawer],\n      host: {\n        'class': 'mat-drawer mat-sidenav',\n        'tabIndex': '-1',\n        // must prevent the browser from aligning text based on value\n        '[attr.align]': 'null',\n        '[class.mat-drawer-end]': 'position === \"end\"',\n        '[class.mat-drawer-over]': 'mode === \"over\"',\n        '[class.mat-drawer-push]': 'mode === \"push\"',\n        '[class.mat-drawer-side]': 'mode === \"side\"',\n        '[class.mat-drawer-opened]': 'opened',\n        '[class.mat-sidenav-fixed]': 'fixedInViewport',\n        '[style.top.px]': 'fixedInViewport ? fixedTopGap : null',\n        '[style.bottom.px]': 'fixedInViewport ? fixedBottomGap : null'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: \"<div class=\\\"mat-drawer-inner-container\\\" cdkScrollable #content>\\r\\n  <ng-content></ng-content>\\r\\n</div>\\r\\n\"\n    }]\n  }], null, {\n    fixedInViewport: [{\n      type: Input\n    }],\n    fixedTopGap: [{\n      type: Input\n    }],\n    fixedBottomGap: [{\n      type: Input\n    }]\n  });\n})();\n\nclass MatSidenavContainer extends MatDrawerContainer {}\n\nMatSidenavContainer.ɵfac = /* @__PURE__ */function () {\n  let ɵMatSidenavContainer_BaseFactory;\n  return function MatSidenavContainer_Factory(t) {\n    return (ɵMatSidenavContainer_BaseFactory || (ɵMatSidenavContainer_BaseFactory = i0.ɵɵgetInheritedFactory(MatSidenavContainer)))(t || MatSidenavContainer);\n  };\n}();\n\nMatSidenavContainer.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatSidenavContainer,\n  selectors: [[\"mat-sidenav-container\"]],\n  contentQueries: function MatSidenavContainer_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MatSidenavContent, 5);\n      i0.ɵɵcontentQuery(dirIndex, MatSidenav, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._content = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allDrawers = _t);\n    }\n  },\n  hostAttrs: [1, \"mat-drawer-container\", \"mat-sidenav-container\"],\n  hostVars: 2,\n  hostBindings: function MatSidenavContainer_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-drawer-container-explicit-backdrop\", ctx._backdropOverride);\n    }\n  },\n  exportAs: [\"matSidenavContainer\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_DRAWER_CONTAINER,\n    useExisting: MatSidenavContainer\n  }]), i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c5,\n  decls: 4,\n  vars: 2,\n  consts: [[\"class\", \"mat-drawer-backdrop\", 3, \"mat-drawer-shown\", \"click\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"mat-drawer-backdrop\", 3, \"click\"]],\n  template: function MatSidenavContainer_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c4);\n      i0.ɵɵtemplate(0, MatSidenavContainer_div_0_Template, 1, 2, \"div\", 0);\n      i0.ɵɵprojection(1);\n      i0.ɵɵprojection(2, 1);\n      i0.ɵɵtemplate(3, MatSidenavContainer_mat_sidenav_content_3_Template, 2, 0, \"mat-sidenav-content\", 1);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.hasBackdrop);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", !ctx._content);\n    }\n  },\n  dependencies: [i5.NgIf, MatSidenavContent],\n  styles: [_c6],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSidenavContainer, [{\n    type: Component,\n    args: [{\n      selector: 'mat-sidenav-container',\n      exportAs: 'matSidenavContainer',\n      host: {\n        'class': 'mat-drawer-container mat-sidenav-container',\n        '[class.mat-drawer-container-explicit-backdrop]': '_backdropOverride'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [{\n        provide: MAT_DRAWER_CONTAINER,\n        useExisting: MatSidenavContainer\n      }],\n      template: \"<div class=\\\"mat-drawer-backdrop\\\" (click)=\\\"_onBackdropClicked()\\\" *ngIf=\\\"hasBackdrop\\\"\\n     [class.mat-drawer-shown]=\\\"_isShowingBackdrop()\\\"></div>\\n\\n<ng-content select=\\\"mat-sidenav\\\"></ng-content>\\n\\n<ng-content select=\\\"mat-sidenav-content\\\">\\n</ng-content>\\n<mat-sidenav-content *ngIf=\\\"!_content\\\">\\n  <ng-content></ng-content>\\n</mat-sidenav-content>\\n\",\n      styles: [\".mat-drawer-container{position:relative;z-index:1;box-sizing:border-box;-webkit-overflow-scrolling:touch;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}.cdk-high-contrast-active .mat-drawer-backdrop{opacity:.5}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{position:relative;z-index:4;display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}.cdk-high-contrast-active .mat-drawer,.cdk-high-contrast-active [dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}.cdk-high-contrast-active [dir=rtl] .mat-drawer,.cdk-high-contrast-active .mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer{transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer[style*=\\\"visibility: hidden\\\"]{display:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto;-webkit-overflow-scrolling:touch}.mat-sidenav-fixed{position:fixed}\"]\n    }]\n  }], null, {\n    _allDrawers: [{\n      type: ContentChildren,\n      args: [MatSidenav, {\n        // We need to use `descendants: true`, because Ivy will no longer match\n        // indirect descendants if it's left as false.\n        descendants: true\n      }]\n    }],\n    _content: [{\n      type: ContentChild,\n      args: [MatSidenavContent]\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass MatSidenavModule {}\n\nMatSidenavModule.ɵfac = function MatSidenavModule_Factory(t) {\n  return new (t || MatSidenavModule)();\n};\n\nMatSidenavModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatSidenavModule\n});\nMatSidenavModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, MatCommonModule, CdkScrollableModule, CdkScrollableModule, MatCommonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSidenavModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, MatCommonModule, CdkScrollableModule],\n      exports: [CdkScrollableModule, MatCommonModule, MatDrawer, MatDrawerContainer, MatDrawerContent, MatSidenav, MatSidenavContainer, MatSidenavContent],\n      declarations: [MatDrawer, MatDrawerContainer, MatDrawerContent, MatSidenav, MatSidenavContainer, MatSidenavContent]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { MAT_DRAWER_DEFAULT_AUTOSIZE, MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY, MatDrawer, MatDrawerContainer, MatDrawerContent, MatSidenav, MatSidenavContainer, MatSidenavContent, MatSidenavModule, matDrawerAnimations, throwMatDuplicatedDrawerError };", "map": {"version": 3, "names": ["i1", "CdkScrollable", "CdkScrollableModule", "i5", "DOCUMENT", "CommonModule", "i0", "InjectionToken", "forwardRef", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Inject", "EventEmitter", "Optional", "Input", "Output", "ViewChild", "QueryList", "ContentChildren", "ContentChild", "NgModule", "MatCommonModule", "i2", "i4", "coerceBooleanProperty", "coerceNumberProperty", "ESCAPE", "hasModifierKey", "i3", "Subject", "fromEvent", "merge", "filter", "map", "mapTo", "takeUntil", "distinctUntilChanged", "take", "startWith", "debounceTime", "trigger", "state", "style", "transition", "animate", "ANIMATION_MODULE_TYPE", "matDrawerAnimations", "transformDrawer", "throwMatDuplicatedDrawerError", "position", "Error", "MAT_DRAWER_DEFAULT_AUTOSIZE", "providedIn", "factory", "MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY", "MAT_DRAWER_CONTAINER", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "_changeDetectorRef", "_container", "elementRef", "scroll<PERSON><PERSON><PERSON>tcher", "ngZone", "ngAfterContentInit", "_contentMarginChanges", "subscribe", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵfac", "ChangeDetectorRef", "Mat<PERSON>rawerContainer", "ElementRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NgZone", "ɵcmp", "provide", "useExisting", "type", "args", "selector", "template", "host", "changeDetection", "OnPush", "encapsulation", "None", "providers", "decorators", "<PERSON><PERSON><PERSON><PERSON>", "_elementRef", "_focusTrapFactory", "_focusMonitor", "_platform", "_ngZone", "_interactivityC<PERSON>cker", "_doc", "_elementFocusedBeforeDrawerWasOpened", "_enableAnimations", "_position", "_mode", "_disableClose", "_opened", "_animationStarted", "_animationEnd", "_animationState", "openedChange", "_openedStream", "pipe", "o", "openedStart", "e", "fromState", "toState", "indexOf", "undefined", "_closedStream", "closedStart", "_destroyed", "onPositionChanged", "_modeChanged", "opened", "activeElement", "_takeFocus", "_isFocusWithinDrawer", "_restoreFocus", "_openedVia", "runOutsideAngular", "nativeElement", "event", "keyCode", "disableClose", "run", "close", "stopPropagation", "preventDefault", "x", "y", "emit", "value", "_isAttached", "_updatePositionInParent", "mode", "_updateFocusTrapState", "next", "autoFocus", "_autoFocus", "toggle", "_forceFocus", "element", "options", "isFocusable", "tabIndex", "callback", "removeEventListener", "removeAttribute", "addEventListener", "focus", "_focusByCssSelector", "elementToFocus", "querySelector", "_focusTrap", "focusInitialElementWhenReady", "then", "hasMovedFocus", "<PERSON><PERSON><PERSON><PERSON>", "focusVia", "blur", "activeEl", "contains", "ngAfterViewInit", "create", "ngAfterContentChecked", "<PERSON><PERSON><PERSON><PERSON>", "ngOnDestroy", "destroy", "_anchor", "remove", "complete", "open", "openedVia", "_closeViaBackdropClick", "_setOpen", "isOpen", "result", "restoreFocus", "Promise", "resolve", "_getWidth", "offsetWidth", "enabled", "newPosition", "parent", "parentNode", "createComment", "insertBefore", "append<PERSON><PERSON><PERSON>", "FocusTrapFactory", "FocusMonitor", "Platform", "InteractivityChecker", "exportAs", "animations", "_content", "_dir", "_element", "viewportRuler", "defaultAutosize", "_animationMode", "_drawers", "backdropClick", "_doCheckSubject", "_contentMargins", "left", "right", "change", "_validateDrawers", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_autosize", "start", "_start", "end", "_end", "autosize", "hasBackdrop", "_backdropOverride", "scrollable", "_userContent", "_allDrawers", "changes", "drawer", "reset", "item", "notifyOn<PERSON><PERSON>es", "for<PERSON>ach", "_watchDrawerToggle", "_watchDrawerPosition", "_watchDrawerMode", "length", "_isDrawerOpen", "_left", "width", "_right", "ngDoCheck", "_isPushed", "classList", "add", "_setContainerClass", "onMicrotaskEmpty", "isAdd", "className", "ngDevMode", "_onBackdropClicked", "_closeModalDrawersViaBackdrop", "_canHaveBackdrop", "_isShowingBackdrop", "Directionality", "ViewportRuler", "NgIf", "styles", "descendants", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "changeDetectorRef", "container", "Mat<PERSON>idenav<PERSON><PERSON>r", "<PERSON><PERSON><PERSON><PERSON>", "arguments", "_fixedInViewport", "_fixedTopGap", "_fixedBottomGap", "fixedInViewport", "fixedTopGap", "fixedBottomGap", "MatSidenavModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["R:/chateye/FrontendAngular/node_modules/@angular/material/fesm2020/sidenav.mjs"], "sourcesContent": ["import * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollable, CdkScrollableModule } from '@angular/cdk/scrolling';\nimport * as i5 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, EventEmitter, Optional, Input, Output, ViewChild, QueryList, ContentChildren, ContentChild, NgModule } from '@angular/core';\nimport { MatCommonModule } from '@angular/material/core';\nimport * as i2 from '@angular/cdk/a11y';\nimport * as i4 from '@angular/cdk/bidi';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport * as i3 from '@angular/cdk/platform';\nimport { Subject, fromEvent, merge } from 'rxjs';\nimport { filter, map, mapTo, takeUntil, distinctUntilChanged, take, startWith, debounceTime } from 'rxjs/operators';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Animations used by the Material drawers.\n * @docs-private\n */\nconst matDrawerAnimations = {\n    /** Animation that slides a drawer in and out. */\n    transformDrawer: trigger('transform', [\n        // We remove the `transform` here completely, rather than setting it to zero, because:\n        // 1. Having a transform can cause elements with ripples or an animated\n        //    transform to shift around in Chrome with an RTL layout (see #10023).\n        // 2. 3d transforms causes text to appear blurry on IE and Edge.\n        state('open, open-instant', style({\n            'transform': 'none',\n            'visibility': 'visible',\n        })),\n        state('void', style({\n            // Avoids the shadow showing up when closed in SSR.\n            'box-shadow': 'none',\n            'visibility': 'hidden',\n        })),\n        transition('void => open-instant', animate('0ms')),\n        transition('void <=> open, open-instant => void', animate('400ms cubic-bezier(0.25, 0.8, 0.25, 1)')),\n    ]),\n};\n\n/**\n * Throws an exception when two MatDrawer are matching the same position.\n * @docs-private\n */\nfunction throwMatDuplicatedDrawerError(position) {\n    throw Error(`A drawer was already declared for 'position=\"${position}\"'`);\n}\n/** Configures whether drawers should use auto sizing by default. */\nconst MAT_DRAWER_DEFAULT_AUTOSIZE = new InjectionToken('MAT_DRAWER_DEFAULT_AUTOSIZE', {\n    providedIn: 'root',\n    factory: MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY,\n});\n/**\n * Used to provide a drawer container to a drawer while avoiding circular references.\n * @docs-private\n */\nconst MAT_DRAWER_CONTAINER = new InjectionToken('MAT_DRAWER_CONTAINER');\n/** @docs-private */\nfunction MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY() {\n    return false;\n}\nclass MatDrawerContent extends CdkScrollable {\n    constructor(_changeDetectorRef, _container, elementRef, scrollDispatcher, ngZone) {\n        super(elementRef, scrollDispatcher, ngZone);\n        this._changeDetectorRef = _changeDetectorRef;\n        this._container = _container;\n    }\n    ngAfterContentInit() {\n        this._container._contentMarginChanges.subscribe(() => {\n            this._changeDetectorRef.markForCheck();\n        });\n    }\n}\nMatDrawerContent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatDrawerContent, deps: [{ token: i0.ChangeDetectorRef }, { token: forwardRef(() => MatDrawerContainer) }, { token: i0.ElementRef }, { token: i1.ScrollDispatcher }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Component });\nMatDrawerContent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatDrawerContent, selector: \"mat-drawer-content\", host: { properties: { \"style.margin-left.px\": \"_container._contentMargins.left\", \"style.margin-right.px\": \"_container._contentMargins.right\" }, classAttribute: \"mat-drawer-content\" }, providers: [\n        {\n            provide: CdkScrollable,\n            useExisting: MatDrawerContent,\n        },\n    ], usesInheritance: true, ngImport: i0, template: '<ng-content></ng-content>', isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatDrawerContent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'mat-drawer-content',\n                    template: '<ng-content></ng-content>',\n                    host: {\n                        'class': 'mat-drawer-content',\n                        '[style.margin-left.px]': '_container._contentMargins.left',\n                        '[style.margin-right.px]': '_container._contentMargins.right',\n                    },\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    providers: [\n                        {\n                            provide: CdkScrollable,\n                            useExisting: MatDrawerContent,\n                        },\n                    ],\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ChangeDetectorRef }, { type: MatDrawerContainer, decorators: [{\n                    type: Inject,\n                    args: [forwardRef(() => MatDrawerContainer)]\n                }] }, { type: i0.ElementRef }, { type: i1.ScrollDispatcher }, { type: i0.NgZone }]; } });\n/**\n * This component corresponds to a drawer that can be opened on the drawer container.\n */\nclass MatDrawer {\n    constructor(_elementRef, _focusTrapFactory, _focusMonitor, _platform, _ngZone, _interactivityChecker, _doc, _container) {\n        this._elementRef = _elementRef;\n        this._focusTrapFactory = _focusTrapFactory;\n        this._focusMonitor = _focusMonitor;\n        this._platform = _platform;\n        this._ngZone = _ngZone;\n        this._interactivityChecker = _interactivityChecker;\n        this._doc = _doc;\n        this._container = _container;\n        this._elementFocusedBeforeDrawerWasOpened = null;\n        /** Whether the drawer is initialized. Used for disabling the initial animation. */\n        this._enableAnimations = false;\n        this._position = 'start';\n        this._mode = 'over';\n        this._disableClose = false;\n        this._opened = false;\n        /** Emits whenever the drawer has started animating. */\n        this._animationStarted = new Subject();\n        /** Emits whenever the drawer is done animating. */\n        this._animationEnd = new Subject();\n        /** Current state of the sidenav animation. */\n        this._animationState = 'void';\n        /** Event emitted when the drawer open state is changed. */\n        this.openedChange = \n        // Note this has to be async in order to avoid some issues with two-bindings (see #8872).\n        new EventEmitter(/* isAsync */ true);\n        /** Event emitted when the drawer has been opened. */\n        this._openedStream = this.openedChange.pipe(filter(o => o), map(() => { }));\n        /** Event emitted when the drawer has started opening. */\n        this.openedStart = this._animationStarted.pipe(filter(e => e.fromState !== e.toState && e.toState.indexOf('open') === 0), mapTo(undefined));\n        /** Event emitted when the drawer has been closed. */\n        this._closedStream = this.openedChange.pipe(filter(o => !o), map(() => { }));\n        /** Event emitted when the drawer has started closing. */\n        this.closedStart = this._animationStarted.pipe(filter(e => e.fromState !== e.toState && e.toState === 'void'), mapTo(undefined));\n        /** Emits when the component is destroyed. */\n        this._destroyed = new Subject();\n        /** Event emitted when the drawer's position changes. */\n        // tslint:disable-next-line:no-output-on-prefix\n        this.onPositionChanged = new EventEmitter();\n        /**\n         * An observable that emits when the drawer mode changes. This is used by the drawer container to\n         * to know when to when the mode changes so it can adapt the margins on the content.\n         */\n        this._modeChanged = new Subject();\n        this.openedChange.subscribe((opened) => {\n            if (opened) {\n                if (this._doc) {\n                    this._elementFocusedBeforeDrawerWasOpened = this._doc.activeElement;\n                }\n                this._takeFocus();\n            }\n            else if (this._isFocusWithinDrawer()) {\n                this._restoreFocus(this._openedVia || 'program');\n            }\n        });\n        /**\n         * Listen to `keydown` events outside the zone so that change detection is not run every\n         * time a key is pressed. Instead we re-enter the zone only if the `ESC` key is pressed\n         * and we don't have close disabled.\n         */\n        this._ngZone.runOutsideAngular(() => {\n            fromEvent(this._elementRef.nativeElement, 'keydown')\n                .pipe(filter(event => {\n                return event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event);\n            }), takeUntil(this._destroyed))\n                .subscribe(event => this._ngZone.run(() => {\n                this.close();\n                event.stopPropagation();\n                event.preventDefault();\n            }));\n        });\n        // We need a Subject with distinctUntilChanged, because the `done` event\n        // fires twice on some browsers. See https://github.com/angular/angular/issues/24084\n        this._animationEnd\n            .pipe(distinctUntilChanged((x, y) => {\n            return x.fromState === y.fromState && x.toState === y.toState;\n        }))\n            .subscribe((event) => {\n            const { fromState, toState } = event;\n            if ((toState.indexOf('open') === 0 && fromState === 'void') ||\n                (toState === 'void' && fromState.indexOf('open') === 0)) {\n                this.openedChange.emit(this._opened);\n            }\n        });\n    }\n    /** The side that the drawer is attached to. */\n    get position() {\n        return this._position;\n    }\n    set position(value) {\n        // Make sure we have a valid value.\n        value = value === 'end' ? 'end' : 'start';\n        if (value !== this._position) {\n            // Static inputs in Ivy are set before the element is in the DOM.\n            if (this._isAttached) {\n                this._updatePositionInParent(value);\n            }\n            this._position = value;\n            this.onPositionChanged.emit();\n        }\n    }\n    /** Mode of the drawer; one of 'over', 'push' or 'side'. */\n    get mode() {\n        return this._mode;\n    }\n    set mode(value) {\n        this._mode = value;\n        this._updateFocusTrapState();\n        this._modeChanged.next();\n    }\n    /** Whether the drawer can be closed with the escape key or by clicking on the backdrop. */\n    get disableClose() {\n        return this._disableClose;\n    }\n    set disableClose(value) {\n        this._disableClose = coerceBooleanProperty(value);\n    }\n    /**\n     * Whether the drawer should focus the first focusable element automatically when opened.\n     * Defaults to false in when `mode` is set to `side`, otherwise defaults to `true`. If explicitly\n     * enabled, focus will be moved into the sidenav in `side` mode as well.\n     * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or AutoFocusTarget\n     * instead.\n     */\n    get autoFocus() {\n        const value = this._autoFocus;\n        // Note that usually we don't allow autoFocus to be set to `first-tabbable` in `side` mode,\n        // because we don't know how the sidenav is being used, but in some cases it still makes\n        // sense to do it. The consumer can explicitly set `autoFocus`.\n        if (value == null) {\n            if (this.mode === 'side') {\n                return 'dialog';\n            }\n            else {\n                return 'first-tabbable';\n            }\n        }\n        return value;\n    }\n    set autoFocus(value) {\n        if (value === 'true' || value === 'false' || value == null) {\n            value = coerceBooleanProperty(value);\n        }\n        this._autoFocus = value;\n    }\n    /**\n     * Whether the drawer is opened. We overload this because we trigger an event when it\n     * starts or end.\n     */\n    get opened() {\n        return this._opened;\n    }\n    set opened(value) {\n        this.toggle(coerceBooleanProperty(value));\n    }\n    /**\n     * Focuses the provided element. If the element is not focusable, it will add a tabIndex\n     * attribute to forcefully focus it. The attribute is removed after focus is moved.\n     * @param element The element to focus.\n     */\n    _forceFocus(element, options) {\n        if (!this._interactivityChecker.isFocusable(element)) {\n            element.tabIndex = -1;\n            // The tabindex attribute should be removed to avoid navigating to that element again\n            this._ngZone.runOutsideAngular(() => {\n                const callback = () => {\n                    element.removeEventListener('blur', callback);\n                    element.removeEventListener('mousedown', callback);\n                    element.removeAttribute('tabindex');\n                };\n                element.addEventListener('blur', callback);\n                element.addEventListener('mousedown', callback);\n            });\n        }\n        element.focus(options);\n    }\n    /**\n     * Focuses the first element that matches the given selector within the focus trap.\n     * @param selector The CSS selector for the element to set focus to.\n     */\n    _focusByCssSelector(selector, options) {\n        let elementToFocus = this._elementRef.nativeElement.querySelector(selector);\n        if (elementToFocus) {\n            this._forceFocus(elementToFocus, options);\n        }\n    }\n    /**\n     * Moves focus into the drawer. Note that this works even if\n     * the focus trap is disabled in `side` mode.\n     */\n    _takeFocus() {\n        if (!this._focusTrap) {\n            return;\n        }\n        const element = this._elementRef.nativeElement;\n        // When autoFocus is not on the sidenav, if the element cannot be focused or does\n        // not exist, focus the sidenav itself so the keyboard navigation still works.\n        // We need to check that `focus` is a function due to Universal.\n        switch (this.autoFocus) {\n            case false:\n            case 'dialog':\n                return;\n            case true:\n            case 'first-tabbable':\n                this._focusTrap.focusInitialElementWhenReady().then(hasMovedFocus => {\n                    if (!hasMovedFocus && typeof this._elementRef.nativeElement.focus === 'function') {\n                        element.focus();\n                    }\n                });\n                break;\n            case 'first-heading':\n                this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role=\"heading\"]');\n                break;\n            default:\n                this._focusByCssSelector(this.autoFocus);\n                break;\n        }\n    }\n    /**\n     * Restores focus to the element that was originally focused when the drawer opened.\n     * If no element was focused at that time, the focus will be restored to the drawer.\n     */\n    _restoreFocus(focusOrigin) {\n        if (this.autoFocus === 'dialog') {\n            return;\n        }\n        if (this._elementFocusedBeforeDrawerWasOpened) {\n            this._focusMonitor.focusVia(this._elementFocusedBeforeDrawerWasOpened, focusOrigin);\n        }\n        else {\n            this._elementRef.nativeElement.blur();\n        }\n        this._elementFocusedBeforeDrawerWasOpened = null;\n    }\n    /** Whether focus is currently within the drawer. */\n    _isFocusWithinDrawer() {\n        const activeEl = this._doc.activeElement;\n        return !!activeEl && this._elementRef.nativeElement.contains(activeEl);\n    }\n    ngAfterViewInit() {\n        this._isAttached = true;\n        this._focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement);\n        this._updateFocusTrapState();\n        // Only update the DOM position when the sidenav is positioned at\n        // the end since we project the sidenav before the content by default.\n        if (this._position === 'end') {\n            this._updatePositionInParent('end');\n        }\n    }\n    ngAfterContentChecked() {\n        // Enable the animations after the lifecycle hooks have run, in order to avoid animating\n        // drawers that are open by default. When we're on the server, we shouldn't enable the\n        // animations, because we don't want the drawer to animate the first time the user sees\n        // the page.\n        if (this._platform.isBrowser) {\n            this._enableAnimations = true;\n        }\n    }\n    ngOnDestroy() {\n        if (this._focusTrap) {\n            this._focusTrap.destroy();\n        }\n        this._anchor?.remove();\n        this._anchor = null;\n        this._animationStarted.complete();\n        this._animationEnd.complete();\n        this._modeChanged.complete();\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /**\n     * Open the drawer.\n     * @param openedVia Whether the drawer was opened by a key press, mouse click or programmatically.\n     * Used for focus management after the sidenav is closed.\n     */\n    open(openedVia) {\n        return this.toggle(true, openedVia);\n    }\n    /** Close the drawer. */\n    close() {\n        return this.toggle(false);\n    }\n    /** Closes the drawer with context that the backdrop was clicked. */\n    _closeViaBackdropClick() {\n        // If the drawer is closed upon a backdrop click, we always want to restore focus. We\n        // don't need to check whether focus is currently in the drawer, as clicking on the\n        // backdrop causes blurs the active element.\n        return this._setOpen(/* isOpen */ false, /* restoreFocus */ true, 'mouse');\n    }\n    /**\n     * Toggle this drawer.\n     * @param isOpen Whether the drawer should be open.\n     * @param openedVia Whether the drawer was opened by a key press, mouse click or programmatically.\n     * Used for focus management after the sidenav is closed.\n     */\n    toggle(isOpen = !this.opened, openedVia) {\n        // If the focus is currently inside the drawer content and we are closing the drawer,\n        // restore the focus to the initially focused element (when the drawer opened).\n        if (isOpen && openedVia) {\n            this._openedVia = openedVia;\n        }\n        const result = this._setOpen(isOpen, \n        /* restoreFocus */ !isOpen && this._isFocusWithinDrawer(), this._openedVia || 'program');\n        if (!isOpen) {\n            this._openedVia = null;\n        }\n        return result;\n    }\n    /**\n     * Toggles the opened state of the drawer.\n     * @param isOpen Whether the drawer should open or close.\n     * @param restoreFocus Whether focus should be restored on close.\n     * @param focusOrigin Origin to use when restoring focus.\n     */\n    _setOpen(isOpen, restoreFocus, focusOrigin) {\n        this._opened = isOpen;\n        if (isOpen) {\n            this._animationState = this._enableAnimations ? 'open' : 'open-instant';\n        }\n        else {\n            this._animationState = 'void';\n            if (restoreFocus) {\n                this._restoreFocus(focusOrigin);\n            }\n        }\n        this._updateFocusTrapState();\n        return new Promise(resolve => {\n            this.openedChange.pipe(take(1)).subscribe(open => resolve(open ? 'open' : 'close'));\n        });\n    }\n    _getWidth() {\n        return this._elementRef.nativeElement ? this._elementRef.nativeElement.offsetWidth || 0 : 0;\n    }\n    /** Updates the enabled state of the focus trap. */\n    _updateFocusTrapState() {\n        if (this._focusTrap) {\n            // The focus trap is only enabled when the drawer is open in any mode other than side.\n            this._focusTrap.enabled = this.opened && this.mode !== 'side';\n        }\n    }\n    /**\n     * Updates the position of the drawer in the DOM. We need to move the element around ourselves\n     * when it's in the `end` position so that it comes after the content and the visual order\n     * matches the tab order. We also need to be able to move it back to `start` if the sidenav\n     * started off as `end` and was changed to `start`.\n     */\n    _updatePositionInParent(newPosition) {\n        const element = this._elementRef.nativeElement;\n        const parent = element.parentNode;\n        if (newPosition === 'end') {\n            if (!this._anchor) {\n                this._anchor = this._doc.createComment('mat-drawer-anchor');\n                parent.insertBefore(this._anchor, element);\n            }\n            parent.appendChild(element);\n        }\n        else if (this._anchor) {\n            this._anchor.parentNode.insertBefore(element, this._anchor);\n        }\n    }\n}\nMatDrawer.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatDrawer, deps: [{ token: i0.ElementRef }, { token: i2.FocusTrapFactory }, { token: i2.FocusMonitor }, { token: i3.Platform }, { token: i0.NgZone }, { token: i2.InteractivityChecker }, { token: DOCUMENT, optional: true }, { token: MAT_DRAWER_CONTAINER, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nMatDrawer.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatDrawer, selector: \"mat-drawer\", inputs: { position: \"position\", mode: \"mode\", disableClose: \"disableClose\", autoFocus: \"autoFocus\", opened: \"opened\" }, outputs: { openedChange: \"openedChange\", _openedStream: \"opened\", openedStart: \"openedStart\", _closedStream: \"closed\", closedStart: \"closedStart\", onPositionChanged: \"positionChanged\" }, host: { attributes: { \"tabIndex\": \"-1\" }, listeners: { \"@transform.start\": \"_animationStarted.next($event)\", \"@transform.done\": \"_animationEnd.next($event)\" }, properties: { \"attr.align\": \"null\", \"class.mat-drawer-end\": \"position === \\\"end\\\"\", \"class.mat-drawer-over\": \"mode === \\\"over\\\"\", \"class.mat-drawer-push\": \"mode === \\\"push\\\"\", \"class.mat-drawer-side\": \"mode === \\\"side\\\"\", \"class.mat-drawer-opened\": \"opened\", \"@transform\": \"_animationState\" }, classAttribute: \"mat-drawer\" }, viewQueries: [{ propertyName: \"_content\", first: true, predicate: [\"content\"], descendants: true }], exportAs: [\"matDrawer\"], ngImport: i0, template: \"<div class=\\\"mat-drawer-inner-container\\\" cdkScrollable #content>\\r\\n  <ng-content></ng-content>\\r\\n</div>\\r\\n\", dependencies: [{ kind: \"directive\", type: i1.CdkScrollable, selector: \"[cdk-scrollable], [cdkScrollable]\" }], animations: [matDrawerAnimations.transformDrawer], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatDrawer, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-drawer', exportAs: 'matDrawer', animations: [matDrawerAnimations.transformDrawer], host: {\n                        'class': 'mat-drawer',\n                        // must prevent the browser from aligning text based on value\n                        '[attr.align]': 'null',\n                        '[class.mat-drawer-end]': 'position === \"end\"',\n                        '[class.mat-drawer-over]': 'mode === \"over\"',\n                        '[class.mat-drawer-push]': 'mode === \"push\"',\n                        '[class.mat-drawer-side]': 'mode === \"side\"',\n                        '[class.mat-drawer-opened]': 'opened',\n                        'tabIndex': '-1',\n                        '[@transform]': '_animationState',\n                        '(@transform.start)': '_animationStarted.next($event)',\n                        '(@transform.done)': '_animationEnd.next($event)',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, template: \"<div class=\\\"mat-drawer-inner-container\\\" cdkScrollable #content>\\r\\n  <ng-content></ng-content>\\r\\n</div>\\r\\n\" }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i2.FocusTrapFactory }, { type: i2.FocusMonitor }, { type: i3.Platform }, { type: i0.NgZone }, { type: i2.InteractivityChecker }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: MatDrawerContainer, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_DRAWER_CONTAINER]\n                }] }]; }, propDecorators: { position: [{\n                type: Input\n            }], mode: [{\n                type: Input\n            }], disableClose: [{\n                type: Input\n            }], autoFocus: [{\n                type: Input\n            }], opened: [{\n                type: Input\n            }], openedChange: [{\n                type: Output\n            }], _openedStream: [{\n                type: Output,\n                args: ['opened']\n            }], openedStart: [{\n                type: Output\n            }], _closedStream: [{\n                type: Output,\n                args: ['closed']\n            }], closedStart: [{\n                type: Output\n            }], onPositionChanged: [{\n                type: Output,\n                args: ['positionChanged']\n            }], _content: [{\n                type: ViewChild,\n                args: ['content']\n            }] } });\n/**\n * `<mat-drawer-container>` component.\n *\n * This is the parent component to one or two `<mat-drawer>`s that validates the state internally\n * and coordinates the backdrop and content styling.\n */\nclass MatDrawerContainer {\n    constructor(_dir, _element, _ngZone, _changeDetectorRef, viewportRuler, defaultAutosize = false, _animationMode) {\n        this._dir = _dir;\n        this._element = _element;\n        this._ngZone = _ngZone;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._animationMode = _animationMode;\n        /** Drawers that belong to this container. */\n        this._drawers = new QueryList();\n        /** Event emitted when the drawer backdrop is clicked. */\n        this.backdropClick = new EventEmitter();\n        /** Emits when the component is destroyed. */\n        this._destroyed = new Subject();\n        /** Emits on every ngDoCheck. Used for debouncing reflows. */\n        this._doCheckSubject = new Subject();\n        /**\n         * Margins to be applied to the content. These are used to push / shrink the drawer content when a\n         * drawer is open. We use margin rather than transform even for push mode because transform breaks\n         * fixed position elements inside of the transformed element.\n         */\n        this._contentMargins = { left: null, right: null };\n        this._contentMarginChanges = new Subject();\n        // If a `Dir` directive exists up the tree, listen direction changes\n        // and update the left/right properties to point to the proper start/end.\n        if (_dir) {\n            _dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => {\n                this._validateDrawers();\n                this.updateContentMargins();\n            });\n        }\n        // Since the minimum width of the sidenav depends on the viewport width,\n        // we need to recompute the margins if the viewport changes.\n        viewportRuler\n            .change()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => this.updateContentMargins());\n        this._autosize = defaultAutosize;\n    }\n    /** The drawer child with the `start` position. */\n    get start() {\n        return this._start;\n    }\n    /** The drawer child with the `end` position. */\n    get end() {\n        return this._end;\n    }\n    /**\n     * Whether to automatically resize the container whenever\n     * the size of any of its drawers changes.\n     *\n     * **Use at your own risk!** Enabling this option can cause layout thrashing by measuring\n     * the drawers on every change detection cycle. Can be configured globally via the\n     * `MAT_DRAWER_DEFAULT_AUTOSIZE` token.\n     */\n    get autosize() {\n        return this._autosize;\n    }\n    set autosize(value) {\n        this._autosize = coerceBooleanProperty(value);\n    }\n    /**\n     * Whether the drawer container should have a backdrop while one of the sidenavs is open.\n     * If explicitly set to `true`, the backdrop will be enabled for drawers in the `side`\n     * mode as well.\n     */\n    get hasBackdrop() {\n        if (this._backdropOverride == null) {\n            return !this._start || this._start.mode !== 'side' || !this._end || this._end.mode !== 'side';\n        }\n        return this._backdropOverride;\n    }\n    set hasBackdrop(value) {\n        this._backdropOverride = value == null ? null : coerceBooleanProperty(value);\n    }\n    /** Reference to the CdkScrollable instance that wraps the scrollable content. */\n    get scrollable() {\n        return this._userContent || this._content;\n    }\n    ngAfterContentInit() {\n        this._allDrawers.changes\n            .pipe(startWith(this._allDrawers), takeUntil(this._destroyed))\n            .subscribe((drawer) => {\n            this._drawers.reset(drawer.filter(item => !item._container || item._container === this));\n            this._drawers.notifyOnChanges();\n        });\n        this._drawers.changes.pipe(startWith(null)).subscribe(() => {\n            this._validateDrawers();\n            this._drawers.forEach((drawer) => {\n                this._watchDrawerToggle(drawer);\n                this._watchDrawerPosition(drawer);\n                this._watchDrawerMode(drawer);\n            });\n            if (!this._drawers.length ||\n                this._isDrawerOpen(this._start) ||\n                this._isDrawerOpen(this._end)) {\n                this.updateContentMargins();\n            }\n            this._changeDetectorRef.markForCheck();\n        });\n        // Avoid hitting the NgZone through the debounce timeout.\n        this._ngZone.runOutsideAngular(() => {\n            this._doCheckSubject\n                .pipe(debounceTime(10), // Arbitrary debounce time, less than a frame at 60fps\n            takeUntil(this._destroyed))\n                .subscribe(() => this.updateContentMargins());\n        });\n    }\n    ngOnDestroy() {\n        this._contentMarginChanges.complete();\n        this._doCheckSubject.complete();\n        this._drawers.destroy();\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /** Calls `open` of both start and end drawers */\n    open() {\n        this._drawers.forEach(drawer => drawer.open());\n    }\n    /** Calls `close` of both start and end drawers */\n    close() {\n        this._drawers.forEach(drawer => drawer.close());\n    }\n    /**\n     * Recalculates and updates the inline styles for the content. Note that this should be used\n     * sparingly, because it causes a reflow.\n     */\n    updateContentMargins() {\n        // 1. For drawers in `over` mode, they don't affect the content.\n        // 2. For drawers in `side` mode they should shrink the content. We do this by adding to the\n        //    left margin (for left drawer) or right margin (for right the drawer).\n        // 3. For drawers in `push` mode the should shift the content without resizing it. We do this by\n        //    adding to the left or right margin and simultaneously subtracting the same amount of\n        //    margin from the other side.\n        let left = 0;\n        let right = 0;\n        if (this._left && this._left.opened) {\n            if (this._left.mode == 'side') {\n                left += this._left._getWidth();\n            }\n            else if (this._left.mode == 'push') {\n                const width = this._left._getWidth();\n                left += width;\n                right -= width;\n            }\n        }\n        if (this._right && this._right.opened) {\n            if (this._right.mode == 'side') {\n                right += this._right._getWidth();\n            }\n            else if (this._right.mode == 'push') {\n                const width = this._right._getWidth();\n                right += width;\n                left -= width;\n            }\n        }\n        // If either `right` or `left` is zero, don't set a style to the element. This\n        // allows users to specify a custom size via CSS class in SSR scenarios where the\n        // measured widths will always be zero. Note that we reset to `null` here, rather\n        // than below, in order to ensure that the types in the `if` below are consistent.\n        left = left || null;\n        right = right || null;\n        if (left !== this._contentMargins.left || right !== this._contentMargins.right) {\n            this._contentMargins = { left, right };\n            // Pull back into the NgZone since in some cases we could be outside. We need to be careful\n            // to do it only when something changed, otherwise we can end up hitting the zone too often.\n            this._ngZone.run(() => this._contentMarginChanges.next(this._contentMargins));\n        }\n    }\n    ngDoCheck() {\n        // If users opted into autosizing, do a check every change detection cycle.\n        if (this._autosize && this._isPushed()) {\n            // Run outside the NgZone, otherwise the debouncer will throw us into an infinite loop.\n            this._ngZone.runOutsideAngular(() => this._doCheckSubject.next());\n        }\n    }\n    /**\n     * Subscribes to drawer events in order to set a class on the main container element when the\n     * drawer is open and the backdrop is visible. This ensures any overflow on the container element\n     * is properly hidden.\n     */\n    _watchDrawerToggle(drawer) {\n        drawer._animationStarted\n            .pipe(filter((event) => event.fromState !== event.toState), takeUntil(this._drawers.changes))\n            .subscribe((event) => {\n            // Set the transition class on the container so that the animations occur. This should not\n            // be set initially because animations should only be triggered via a change in state.\n            if (event.toState !== 'open-instant' && this._animationMode !== 'NoopAnimations') {\n                this._element.nativeElement.classList.add('mat-drawer-transition');\n            }\n            this.updateContentMargins();\n            this._changeDetectorRef.markForCheck();\n        });\n        if (drawer.mode !== 'side') {\n            drawer.openedChange\n                .pipe(takeUntil(this._drawers.changes))\n                .subscribe(() => this._setContainerClass(drawer.opened));\n        }\n    }\n    /**\n     * Subscribes to drawer onPositionChanged event in order to\n     * re-validate drawers when the position changes.\n     */\n    _watchDrawerPosition(drawer) {\n        if (!drawer) {\n            return;\n        }\n        // NOTE: We need to wait for the microtask queue to be empty before validating,\n        // since both drawers may be swapping positions at the same time.\n        drawer.onPositionChanged.pipe(takeUntil(this._drawers.changes)).subscribe(() => {\n            this._ngZone.onMicrotaskEmpty.pipe(take(1)).subscribe(() => {\n                this._validateDrawers();\n            });\n        });\n    }\n    /** Subscribes to changes in drawer mode so we can run change detection. */\n    _watchDrawerMode(drawer) {\n        if (drawer) {\n            drawer._modeChanged\n                .pipe(takeUntil(merge(this._drawers.changes, this._destroyed)))\n                .subscribe(() => {\n                this.updateContentMargins();\n                this._changeDetectorRef.markForCheck();\n            });\n        }\n    }\n    /** Toggles the 'mat-drawer-opened' class on the main 'mat-drawer-container' element. */\n    _setContainerClass(isAdd) {\n        const classList = this._element.nativeElement.classList;\n        const className = 'mat-drawer-container-has-open';\n        if (isAdd) {\n            classList.add(className);\n        }\n        else {\n            classList.remove(className);\n        }\n    }\n    /** Validate the state of the drawer children components. */\n    _validateDrawers() {\n        this._start = this._end = null;\n        // Ensure that we have at most one start and one end drawer.\n        this._drawers.forEach(drawer => {\n            if (drawer.position == 'end') {\n                if (this._end != null && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                    throwMatDuplicatedDrawerError('end');\n                }\n                this._end = drawer;\n            }\n            else {\n                if (this._start != null && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                    throwMatDuplicatedDrawerError('start');\n                }\n                this._start = drawer;\n            }\n        });\n        this._right = this._left = null;\n        // Detect if we're LTR or RTL.\n        if (this._dir && this._dir.value === 'rtl') {\n            this._left = this._end;\n            this._right = this._start;\n        }\n        else {\n            this._left = this._start;\n            this._right = this._end;\n        }\n    }\n    /** Whether the container is being pushed to the side by one of the drawers. */\n    _isPushed() {\n        return ((this._isDrawerOpen(this._start) && this._start.mode != 'over') ||\n            (this._isDrawerOpen(this._end) && this._end.mode != 'over'));\n    }\n    _onBackdropClicked() {\n        this.backdropClick.emit();\n        this._closeModalDrawersViaBackdrop();\n    }\n    _closeModalDrawersViaBackdrop() {\n        // Close all open drawers where closing is not disabled and the mode is not `side`.\n        [this._start, this._end]\n            .filter(drawer => drawer && !drawer.disableClose && this._canHaveBackdrop(drawer))\n            .forEach(drawer => drawer._closeViaBackdropClick());\n    }\n    _isShowingBackdrop() {\n        return ((this._isDrawerOpen(this._start) && this._canHaveBackdrop(this._start)) ||\n            (this._isDrawerOpen(this._end) && this._canHaveBackdrop(this._end)));\n    }\n    _canHaveBackdrop(drawer) {\n        return drawer.mode !== 'side' || !!this._backdropOverride;\n    }\n    _isDrawerOpen(drawer) {\n        return drawer != null && drawer.opened;\n    }\n}\nMatDrawerContainer.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatDrawerContainer, deps: [{ token: i4.Directionality, optional: true }, { token: i0.ElementRef }, { token: i0.NgZone }, { token: i0.ChangeDetectorRef }, { token: i1.ViewportRuler }, { token: MAT_DRAWER_DEFAULT_AUTOSIZE }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nMatDrawerContainer.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatDrawerContainer, selector: \"mat-drawer-container\", inputs: { autosize: \"autosize\", hasBackdrop: \"hasBackdrop\" }, outputs: { backdropClick: \"backdropClick\" }, host: { properties: { \"class.mat-drawer-container-explicit-backdrop\": \"_backdropOverride\" }, classAttribute: \"mat-drawer-container\" }, providers: [\n        {\n            provide: MAT_DRAWER_CONTAINER,\n            useExisting: MatDrawerContainer,\n        },\n    ], queries: [{ propertyName: \"_content\", first: true, predicate: MatDrawerContent, descendants: true }, { propertyName: \"_allDrawers\", predicate: MatDrawer, descendants: true }], viewQueries: [{ propertyName: \"_userContent\", first: true, predicate: MatDrawerContent, descendants: true }], exportAs: [\"matDrawerContainer\"], ngImport: i0, template: \"<div class=\\\"mat-drawer-backdrop\\\" (click)=\\\"_onBackdropClicked()\\\" *ngIf=\\\"hasBackdrop\\\"\\n     [class.mat-drawer-shown]=\\\"_isShowingBackdrop()\\\"></div>\\n\\n<ng-content select=\\\"mat-drawer\\\"></ng-content>\\n\\n<ng-content select=\\\"mat-drawer-content\\\">\\n</ng-content>\\n<mat-drawer-content *ngIf=\\\"!_content\\\">\\n  <ng-content></ng-content>\\n</mat-drawer-content>\\n\", styles: [\".mat-drawer-container{position:relative;z-index:1;box-sizing:border-box;-webkit-overflow-scrolling:touch;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}.cdk-high-contrast-active .mat-drawer-backdrop{opacity:.5}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{position:relative;z-index:4;display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}.cdk-high-contrast-active .mat-drawer,.cdk-high-contrast-active [dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}.cdk-high-contrast-active [dir=rtl] .mat-drawer,.cdk-high-contrast-active .mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer{transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer[style*=\\\"visibility: hidden\\\"]{display:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto;-webkit-overflow-scrolling:touch}.mat-sidenav-fixed{position:fixed}\"], dependencies: [{ kind: \"directive\", type: i5.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"component\", type: MatDrawerContent, selector: \"mat-drawer-content\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatDrawerContainer, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-drawer-container', exportAs: 'matDrawerContainer', host: {\n                        'class': 'mat-drawer-container',\n                        '[class.mat-drawer-container-explicit-backdrop]': '_backdropOverride',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, providers: [\n                        {\n                            provide: MAT_DRAWER_CONTAINER,\n                            useExisting: MatDrawerContainer,\n                        },\n                    ], template: \"<div class=\\\"mat-drawer-backdrop\\\" (click)=\\\"_onBackdropClicked()\\\" *ngIf=\\\"hasBackdrop\\\"\\n     [class.mat-drawer-shown]=\\\"_isShowingBackdrop()\\\"></div>\\n\\n<ng-content select=\\\"mat-drawer\\\"></ng-content>\\n\\n<ng-content select=\\\"mat-drawer-content\\\">\\n</ng-content>\\n<mat-drawer-content *ngIf=\\\"!_content\\\">\\n  <ng-content></ng-content>\\n</mat-drawer-content>\\n\", styles: [\".mat-drawer-container{position:relative;z-index:1;box-sizing:border-box;-webkit-overflow-scrolling:touch;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}.cdk-high-contrast-active .mat-drawer-backdrop{opacity:.5}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{position:relative;z-index:4;display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}.cdk-high-contrast-active .mat-drawer,.cdk-high-contrast-active [dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}.cdk-high-contrast-active [dir=rtl] .mat-drawer,.cdk-high-contrast-active .mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer{transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer[style*=\\\"visibility: hidden\\\"]{display:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto;-webkit-overflow-scrolling:touch}.mat-sidenav-fixed{position:fixed}\"] }]\n        }], ctorParameters: function () { return [{ type: i4.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.ElementRef }, { type: i0.NgZone }, { type: i0.ChangeDetectorRef }, { type: i1.ViewportRuler }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_DRAWER_DEFAULT_AUTOSIZE]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { _allDrawers: [{\n                type: ContentChildren,\n                args: [MatDrawer, {\n                        // We need to use `descendants: true`, because Ivy will no longer match\n                        // indirect descendants if it's left as false.\n                        descendants: true,\n                    }]\n            }], _content: [{\n                type: ContentChild,\n                args: [MatDrawerContent]\n            }], _userContent: [{\n                type: ViewChild,\n                args: [MatDrawerContent]\n            }], autosize: [{\n                type: Input\n            }], hasBackdrop: [{\n                type: Input\n            }], backdropClick: [{\n                type: Output\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatSidenavContent extends MatDrawerContent {\n    constructor(changeDetectorRef, container, elementRef, scrollDispatcher, ngZone) {\n        super(changeDetectorRef, container, elementRef, scrollDispatcher, ngZone);\n    }\n}\nMatSidenavContent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatSidenavContent, deps: [{ token: i0.ChangeDetectorRef }, { token: forwardRef(() => MatSidenavContainer) }, { token: i0.ElementRef }, { token: i1.ScrollDispatcher }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Component });\nMatSidenavContent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatSidenavContent, selector: \"mat-sidenav-content\", host: { properties: { \"style.margin-left.px\": \"_container._contentMargins.left\", \"style.margin-right.px\": \"_container._contentMargins.right\" }, classAttribute: \"mat-drawer-content mat-sidenav-content\" }, providers: [\n        {\n            provide: CdkScrollable,\n            useExisting: MatSidenavContent,\n        },\n    ], usesInheritance: true, ngImport: i0, template: '<ng-content></ng-content>', isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatSidenavContent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'mat-sidenav-content',\n                    template: '<ng-content></ng-content>',\n                    host: {\n                        'class': 'mat-drawer-content mat-sidenav-content',\n                        '[style.margin-left.px]': '_container._contentMargins.left',\n                        '[style.margin-right.px]': '_container._contentMargins.right',\n                    },\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    providers: [\n                        {\n                            provide: CdkScrollable,\n                            useExisting: MatSidenavContent,\n                        },\n                    ],\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ChangeDetectorRef }, { type: MatSidenavContainer, decorators: [{\n                    type: Inject,\n                    args: [forwardRef(() => MatSidenavContainer)]\n                }] }, { type: i0.ElementRef }, { type: i1.ScrollDispatcher }, { type: i0.NgZone }]; } });\nclass MatSidenav extends MatDrawer {\n    constructor() {\n        super(...arguments);\n        this._fixedInViewport = false;\n        this._fixedTopGap = 0;\n        this._fixedBottomGap = 0;\n    }\n    /** Whether the sidenav is fixed in the viewport. */\n    get fixedInViewport() {\n        return this._fixedInViewport;\n    }\n    set fixedInViewport(value) {\n        this._fixedInViewport = coerceBooleanProperty(value);\n    }\n    /**\n     * The gap between the top of the sidenav and the top of the viewport when the sidenav is in fixed\n     * mode.\n     */\n    get fixedTopGap() {\n        return this._fixedTopGap;\n    }\n    set fixedTopGap(value) {\n        this._fixedTopGap = coerceNumberProperty(value);\n    }\n    /**\n     * The gap between the bottom of the sidenav and the bottom of the viewport when the sidenav is in\n     * fixed mode.\n     */\n    get fixedBottomGap() {\n        return this._fixedBottomGap;\n    }\n    set fixedBottomGap(value) {\n        this._fixedBottomGap = coerceNumberProperty(value);\n    }\n}\nMatSidenav.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatSidenav, deps: null, target: i0.ɵɵFactoryTarget.Component });\nMatSidenav.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatSidenav, selector: \"mat-sidenav\", inputs: { fixedInViewport: \"fixedInViewport\", fixedTopGap: \"fixedTopGap\", fixedBottomGap: \"fixedBottomGap\" }, host: { attributes: { \"tabIndex\": \"-1\" }, properties: { \"attr.align\": \"null\", \"class.mat-drawer-end\": \"position === \\\"end\\\"\", \"class.mat-drawer-over\": \"mode === \\\"over\\\"\", \"class.mat-drawer-push\": \"mode === \\\"push\\\"\", \"class.mat-drawer-side\": \"mode === \\\"side\\\"\", \"class.mat-drawer-opened\": \"opened\", \"class.mat-sidenav-fixed\": \"fixedInViewport\", \"style.top.px\": \"fixedInViewport ? fixedTopGap : null\", \"style.bottom.px\": \"fixedInViewport ? fixedBottomGap : null\" }, classAttribute: \"mat-drawer mat-sidenav\" }, exportAs: [\"matSidenav\"], usesInheritance: true, ngImport: i0, template: \"<div class=\\\"mat-drawer-inner-container\\\" cdkScrollable #content>\\r\\n  <ng-content></ng-content>\\r\\n</div>\\r\\n\", dependencies: [{ kind: \"directive\", type: i1.CdkScrollable, selector: \"[cdk-scrollable], [cdkScrollable]\" }], animations: [matDrawerAnimations.transformDrawer], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatSidenav, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-sidenav', exportAs: 'matSidenav', animations: [matDrawerAnimations.transformDrawer], host: {\n                        'class': 'mat-drawer mat-sidenav',\n                        'tabIndex': '-1',\n                        // must prevent the browser from aligning text based on value\n                        '[attr.align]': 'null',\n                        '[class.mat-drawer-end]': 'position === \"end\"',\n                        '[class.mat-drawer-over]': 'mode === \"over\"',\n                        '[class.mat-drawer-push]': 'mode === \"push\"',\n                        '[class.mat-drawer-side]': 'mode === \"side\"',\n                        '[class.mat-drawer-opened]': 'opened',\n                        '[class.mat-sidenav-fixed]': 'fixedInViewport',\n                        '[style.top.px]': 'fixedInViewport ? fixedTopGap : null',\n                        '[style.bottom.px]': 'fixedInViewport ? fixedBottomGap : null',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, template: \"<div class=\\\"mat-drawer-inner-container\\\" cdkScrollable #content>\\r\\n  <ng-content></ng-content>\\r\\n</div>\\r\\n\" }]\n        }], propDecorators: { fixedInViewport: [{\n                type: Input\n            }], fixedTopGap: [{\n                type: Input\n            }], fixedBottomGap: [{\n                type: Input\n            }] } });\nclass MatSidenavContainer extends MatDrawerContainer {\n}\nMatSidenavContainer.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatSidenavContainer, deps: null, target: i0.ɵɵFactoryTarget.Component });\nMatSidenavContainer.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatSidenavContainer, selector: \"mat-sidenav-container\", host: { properties: { \"class.mat-drawer-container-explicit-backdrop\": \"_backdropOverride\" }, classAttribute: \"mat-drawer-container mat-sidenav-container\" }, providers: [\n        {\n            provide: MAT_DRAWER_CONTAINER,\n            useExisting: MatSidenavContainer,\n        },\n    ], queries: [{ propertyName: \"_content\", first: true, predicate: MatSidenavContent, descendants: true }, { propertyName: \"_allDrawers\", predicate: MatSidenav, descendants: true }], exportAs: [\"matSidenavContainer\"], usesInheritance: true, ngImport: i0, template: \"<div class=\\\"mat-drawer-backdrop\\\" (click)=\\\"_onBackdropClicked()\\\" *ngIf=\\\"hasBackdrop\\\"\\n     [class.mat-drawer-shown]=\\\"_isShowingBackdrop()\\\"></div>\\n\\n<ng-content select=\\\"mat-sidenav\\\"></ng-content>\\n\\n<ng-content select=\\\"mat-sidenav-content\\\">\\n</ng-content>\\n<mat-sidenav-content *ngIf=\\\"!_content\\\">\\n  <ng-content></ng-content>\\n</mat-sidenav-content>\\n\", styles: [\".mat-drawer-container{position:relative;z-index:1;box-sizing:border-box;-webkit-overflow-scrolling:touch;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}.cdk-high-contrast-active .mat-drawer-backdrop{opacity:.5}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{position:relative;z-index:4;display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}.cdk-high-contrast-active .mat-drawer,.cdk-high-contrast-active [dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}.cdk-high-contrast-active [dir=rtl] .mat-drawer,.cdk-high-contrast-active .mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer{transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer[style*=\\\"visibility: hidden\\\"]{display:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto;-webkit-overflow-scrolling:touch}.mat-sidenav-fixed{position:fixed}\"], dependencies: [{ kind: \"directive\", type: i5.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"component\", type: MatSidenavContent, selector: \"mat-sidenav-content\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatSidenavContainer, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-sidenav-container', exportAs: 'matSidenavContainer', host: {\n                        'class': 'mat-drawer-container mat-sidenav-container',\n                        '[class.mat-drawer-container-explicit-backdrop]': '_backdropOverride',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, providers: [\n                        {\n                            provide: MAT_DRAWER_CONTAINER,\n                            useExisting: MatSidenavContainer,\n                        },\n                    ], template: \"<div class=\\\"mat-drawer-backdrop\\\" (click)=\\\"_onBackdropClicked()\\\" *ngIf=\\\"hasBackdrop\\\"\\n     [class.mat-drawer-shown]=\\\"_isShowingBackdrop()\\\"></div>\\n\\n<ng-content select=\\\"mat-sidenav\\\"></ng-content>\\n\\n<ng-content select=\\\"mat-sidenav-content\\\">\\n</ng-content>\\n<mat-sidenav-content *ngIf=\\\"!_content\\\">\\n  <ng-content></ng-content>\\n</mat-sidenav-content>\\n\", styles: [\".mat-drawer-container{position:relative;z-index:1;box-sizing:border-box;-webkit-overflow-scrolling:touch;display:block;overflow:hidden}.mat-drawer-container[fullscreen]{top:0;left:0;right:0;bottom:0;position:absolute}.mat-drawer-container[fullscreen].mat-drawer-container-has-open{overflow:hidden}.mat-drawer-container.mat-drawer-container-explicit-backdrop .mat-drawer-side{z-index:3}.mat-drawer-container.ng-animate-disabled .mat-drawer-backdrop,.mat-drawer-container.ng-animate-disabled .mat-drawer-content,.ng-animate-disabled .mat-drawer-container .mat-drawer-backdrop,.ng-animate-disabled .mat-drawer-container .mat-drawer-content{transition:none}.mat-drawer-backdrop{top:0;left:0;right:0;bottom:0;position:absolute;display:block;z-index:3;visibility:hidden}.mat-drawer-backdrop.mat-drawer-shown{visibility:visible}.mat-drawer-transition .mat-drawer-backdrop{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:background-color,visibility}.cdk-high-contrast-active .mat-drawer-backdrop{opacity:.5}.mat-drawer-content{position:relative;z-index:1;display:block;height:100%;overflow:auto}.mat-drawer-transition .mat-drawer-content{transition-duration:400ms;transition-timing-function:cubic-bezier(0.25, 0.8, 0.25, 1);transition-property:transform,margin-left,margin-right}.mat-drawer{position:relative;z-index:4;display:block;position:absolute;top:0;bottom:0;z-index:3;outline:0;box-sizing:border-box;overflow-y:auto;transform:translate3d(-100%, 0, 0)}.cdk-high-contrast-active .mat-drawer,.cdk-high-contrast-active [dir=rtl] .mat-drawer.mat-drawer-end{border-right:solid 1px currentColor}.cdk-high-contrast-active [dir=rtl] .mat-drawer,.cdk-high-contrast-active .mat-drawer.mat-drawer-end{border-left:solid 1px currentColor;border-right:none}.mat-drawer.mat-drawer-side{z-index:2}.mat-drawer.mat-drawer-end{right:0;transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer{transform:translate3d(100%, 0, 0)}[dir=rtl] .mat-drawer.mat-drawer-end{left:0;right:auto;transform:translate3d(-100%, 0, 0)}.mat-drawer[style*=\\\"visibility: hidden\\\"]{display:none}.mat-drawer-inner-container{width:100%;height:100%;overflow:auto;-webkit-overflow-scrolling:touch}.mat-sidenav-fixed{position:fixed}\"] }]\n        }], propDecorators: { _allDrawers: [{\n                type: ContentChildren,\n                args: [MatSidenav, {\n                        // We need to use `descendants: true`, because Ivy will no longer match\n                        // indirect descendants if it's left as false.\n                        descendants: true,\n                    }]\n            }], _content: [{\n                type: ContentChild,\n                args: [MatSidenavContent]\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatSidenavModule {\n}\nMatSidenavModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatSidenavModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatSidenavModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.2.0\", ngImport: i0, type: MatSidenavModule, declarations: [MatDrawer,\n        MatDrawerContainer,\n        MatDrawerContent,\n        MatSidenav,\n        MatSidenavContainer,\n        MatSidenavContent], imports: [CommonModule, MatCommonModule, CdkScrollableModule], exports: [CdkScrollableModule,\n        MatCommonModule,\n        MatDrawer,\n        MatDrawerContainer,\n        MatDrawerContent,\n        MatSidenav,\n        MatSidenavContainer,\n        MatSidenavContent] });\nMatSidenavModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatSidenavModule, imports: [CommonModule, MatCommonModule, CdkScrollableModule, CdkScrollableModule,\n        MatCommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatSidenavModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, MatCommonModule, CdkScrollableModule],\n                    exports: [\n                        CdkScrollableModule,\n                        MatCommonModule,\n                        MatDrawer,\n                        MatDrawerContainer,\n                        MatDrawerContent,\n                        MatSidenav,\n                        MatSidenavContainer,\n                        MatSidenavContent,\n                    ],\n                    declarations: [\n                        MatDrawer,\n                        MatDrawerContainer,\n                        MatDrawerContent,\n                        MatSidenav,\n                        MatSidenavContainer,\n                        MatSidenavContent,\n                    ],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_DRAWER_DEFAULT_AUTOSIZE, MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY, MatDrawer, MatDrawerContainer, MatDrawerContent, MatSidenav, MatSidenavContainer, MatSidenavContent, MatSidenavModule, matDrawerAnimations, throwMatDuplicatedDrawerError };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,wBAApB;AACA,SAASC,aAAT,EAAwBC,mBAAxB,QAAmD,wBAAnD;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,QAAT,EAAmBC,YAAnB,QAAuC,iBAAvC;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,SAASC,cAAT,EAAyBC,UAAzB,EAAqCC,SAArC,EAAgDC,uBAAhD,EAAyEC,iBAAzE,EAA4FC,MAA5F,EAAoGC,YAApG,EAAkHC,QAAlH,EAA4HC,KAA5H,EAAmIC,MAAnI,EAA2IC,SAA3I,EAAsJC,SAAtJ,EAAiKC,eAAjK,EAAkLC,YAAlL,EAAgMC,QAAhM,QAAgN,eAAhN;AACA,SAASC,eAAT,QAAgC,wBAAhC;AACA,OAAO,KAAKC,EAAZ,MAAoB,mBAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,mBAApB;AACA,SAASC,qBAAT,EAAgCC,oBAAhC,QAA4D,uBAA5D;AACA,SAASC,MAAT,EAAiBC,cAAjB,QAAuC,uBAAvC;AACA,OAAO,KAAKC,EAAZ,MAAoB,uBAApB;AACA,SAASC,OAAT,EAAkBC,SAAlB,EAA6BC,KAA7B,QAA0C,MAA1C;AACA,SAASC,MAAT,EAAiBC,GAAjB,EAAsBC,KAAtB,EAA6BC,SAA7B,EAAwCC,oBAAxC,EAA8DC,IAA9D,EAAoEC,SAApE,EAA+EC,YAA/E,QAAmG,gBAAnG;AACA,SAASC,OAAT,EAAkBC,KAAlB,EAAyBC,KAAzB,EAAgCC,UAAhC,EAA4CC,OAA5C,QAA2D,qBAA3D;AACA,SAASC,qBAAT,QAAsC,sCAAtC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;;;;;;gBAuDmGxC,E;;IAAAA,EAmvB6P,4B;IAnvB7PA,EAmvBgS;MAnvBhSA,EAmvBgS;MAAA,eAnvBhSA,EAmvBgS;MAAA,OAnvBhSA,EAmvB0S,yCAAV;IAAA,E;IAnvBhSA,EAmvB+Y,e;;;;mBAnvB/YA,E;IAAAA,EAmvB6V,6D;;;;;;IAnvB7VA,EAmvBugB,wC;IAnvBvgBA,EAmvBmjB,mB;IAnvBnjBA,EAmvB8kB,e;;;;;;;;;gBAnvB9kBA,E;;IAAAA,EA44ByK,4B;IA54BzKA,EA44B4M;MA54B5MA,EA44B4M;MAAA,eA54B5MA,EA44B4M;MAAA,OA54B5MA,EA44BsN,yCAAV;IAAA,E;IA54B5MA,EA44B2T,e;;;;mBA54B3TA,E;IAAAA,EA44ByQ,6D;;;;;;IA54BzQA,EA44Bqb,yC;IA54BrbA,EA44Bke,mB;IA54BleA,EA44B6f,e;;;;;;;AAl8BhmB,MAAMyC,mBAAmB,GAAG;EACxB;EACAC,eAAe,EAAEP,OAAO,CAAC,WAAD,EAAc,CAClC;EACA;EACA;EACA;EACAC,KAAK,CAAC,oBAAD,EAAuBC,KAAK,CAAC;IAC9B,aAAa,MADiB;IAE9B,cAAc;EAFgB,CAAD,CAA5B,CAL6B,EASlCD,KAAK,CAAC,MAAD,EAASC,KAAK,CAAC;IAChB;IACA,cAAc,MAFE;IAGhB,cAAc;EAHE,CAAD,CAAd,CAT6B,EAclCC,UAAU,CAAC,sBAAD,EAAyBC,OAAO,CAAC,KAAD,CAAhC,CAdwB,EAelCD,UAAU,CAAC,qCAAD,EAAwCC,OAAO,CAAC,wCAAD,CAA/C,CAfwB,CAAd;AAFA,CAA5B;AAqBA;AACA;AACA;AACA;;AACA,SAASI,6BAAT,CAAuCC,QAAvC,EAAiD;EAC7C,MAAMC,KAAK,CAAE,gDAA+CD,QAAS,IAA1D,CAAX;AACH;AACD;;;AACA,MAAME,2BAA2B,GAAG,IAAI7C,cAAJ,CAAmB,6BAAnB,EAAkD;EAClF8C,UAAU,EAAE,MADsE;EAElFC,OAAO,EAAEC;AAFyE,CAAlD,CAApC;AAIA;AACA;AACA;AACA;;AACA,MAAMC,oBAAoB,GAAG,IAAIjD,cAAJ,CAAmB,sBAAnB,CAA7B;AACA;;AACA,SAASgD,mCAAT,GAA+C;EAC3C,OAAO,KAAP;AACH;;AACD,MAAME,gBAAN,SAA+BxD,aAA/B,CAA6C;EACzCyD,WAAW,CAACC,kBAAD,EAAqBC,UAArB,EAAiCC,UAAjC,EAA6CC,gBAA7C,EAA+DC,MAA/D,EAAuE;IAC9E,MAAMF,UAAN,EAAkBC,gBAAlB,EAAoCC,MAApC;IACA,KAAKJ,kBAAL,GAA0BA,kBAA1B;IACA,KAAKC,UAAL,GAAkBA,UAAlB;EACH;;EACDI,kBAAkB,GAAG;IACjB,KAAKJ,UAAL,CAAgBK,qBAAhB,CAAsCC,SAAtC,CAAgD,MAAM;MAClD,KAAKP,kBAAL,CAAwBQ,YAAxB;IACH,CAFD;EAGH;;AAVwC;;AAY7CV,gBAAgB,CAACW,IAAjB;EAAA,iBAA6GX,gBAA7G,EAAmGnD,EAAnG,mBAA+IA,EAAE,CAAC+D,iBAAlJ,GAAmG/D,EAAnG,mBAAgLE,UAAU,CAAC,MAAM8D,kBAAP,CAA1L,GAAmGhE,EAAnG,mBAAiOA,EAAE,CAACiE,UAApO,GAAmGjE,EAAnG,mBAA2PN,EAAE,CAACwE,gBAA9P,GAAmGlE,EAAnG,mBAA2RA,EAAE,CAACmE,MAA9R;AAAA;;AACAhB,gBAAgB,CAACiB,IAAjB,kBADmGpE,EACnG;EAAA,MAAiGmD,gBAAjG;EAAA;EAAA;EAAA;EAAA;IAAA;MADmGnD,EACnG;IAAA;EAAA;EAAA,WADmGA,EACnG,oBAAsV,CAC9U;IACIqE,OAAO,EAAE1E,aADb;IAEI2E,WAAW,EAAEnB;EAFjB,CAD8U,CAAtV,GADmGnD,EACnG;EAAA;EAAA;EAAA;EAAA;IAAA;MADmGA,EACnG;MADmGA,EAM5C,gBALvD;IAAA;EAAA;EAAA;EAAA;AAAA;;AAMA;EAAA,mDAPmGA,EAOnG,mBAA2FmD,gBAA3F,EAAyH,CAAC;IAC9GoB,IAAI,EAAEpE,SADwG;IAE9GqE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBADX;MAECC,QAAQ,EAAE,2BAFX;MAGCC,IAAI,EAAE;QACF,SAAS,oBADP;QAEF,0BAA0B,iCAFxB;QAGF,2BAA2B;MAHzB,CAHP;MAQCC,eAAe,EAAExE,uBAAuB,CAACyE,MAR1C;MASCC,aAAa,EAAEzE,iBAAiB,CAAC0E,IATlC;MAUCC,SAAS,EAAE,CACP;QACIX,OAAO,EAAE1E,aADb;QAEI2E,WAAW,EAAEnB;MAFjB,CADO;IAVZ,CAAD;EAFwG,CAAD,CAAzH,EAmB4B,YAAY;IAAE,OAAO,CAAC;MAAEoB,IAAI,EAAEvE,EAAE,CAAC+D;IAAX,CAAD,EAAiC;MAAEQ,IAAI,EAAEP,kBAAR;MAA4BiB,UAAU,EAAE,CAAC;QACvGV,IAAI,EAAEjE,MADiG;QAEvGkE,IAAI,EAAE,CAACtE,UAAU,CAAC,MAAM8D,kBAAP,CAAX;MAFiG,CAAD;IAAxC,CAAjC,EAG3B;MAAEO,IAAI,EAAEvE,EAAE,CAACiE;IAAX,CAH2B,EAGF;MAAEM,IAAI,EAAE7E,EAAE,CAACwE;IAAX,CAHE,EAG6B;MAAEK,IAAI,EAAEvE,EAAE,CAACmE;IAAX,CAH7B,CAAP;EAG2D,CAtBrG;AAAA;AAuBA;AACA;AACA;;;AACA,MAAMe,SAAN,CAAgB;EACZ9B,WAAW,CAAC+B,WAAD,EAAcC,iBAAd,EAAiCC,aAAjC,EAAgDC,SAAhD,EAA2DC,OAA3D,EAAoEC,qBAApE,EAA2FC,IAA3F,EAAiGnC,UAAjG,EAA6G;IACpH,KAAK6B,WAAL,GAAmBA,WAAnB;IACA,KAAKC,iBAAL,GAAyBA,iBAAzB;IACA,KAAKC,aAAL,GAAqBA,aAArB;IACA,KAAKC,SAAL,GAAiBA,SAAjB;IACA,KAAKC,OAAL,GAAeA,OAAf;IACA,KAAKC,qBAAL,GAA6BA,qBAA7B;IACA,KAAKC,IAAL,GAAYA,IAAZ;IACA,KAAKnC,UAAL,GAAkBA,UAAlB;IACA,KAAKoC,oCAAL,GAA4C,IAA5C;IACA;;IACA,KAAKC,iBAAL,GAAyB,KAAzB;IACA,KAAKC,SAAL,GAAiB,OAAjB;IACA,KAAKC,KAAL,GAAa,MAAb;IACA,KAAKC,aAAL,GAAqB,KAArB;IACA,KAAKC,OAAL,GAAe,KAAf;IACA;;IACA,KAAKC,iBAAL,GAAyB,IAAIxE,OAAJ,EAAzB;IACA;;IACA,KAAKyE,aAAL,GAAqB,IAAIzE,OAAJ,EAArB;IACA;;IACA,KAAK0E,eAAL,GAAuB,MAAvB;IACA;;IACA,KAAKC,YAAL,GACA;IACA,IAAI5F,YAAJ;IAAiB;IAAc,IAA/B,CAFA;IAGA;;IACA,KAAK6F,aAAL,GAAqB,KAAKD,YAAL,CAAkBE,IAAlB,CAAuB1E,MAAM,CAAC2E,CAAC,IAAIA,CAAN,CAA7B,EAAuC1E,GAAG,CAAC,MAAM,CAAG,CAAV,CAA1C,CAArB;IACA;;IACA,KAAK2E,WAAL,GAAmB,KAAKP,iBAAL,CAAuBK,IAAvB,CAA4B1E,MAAM,CAAC6E,CAAC,IAAIA,CAAC,CAACC,SAAF,KAAgBD,CAAC,CAACE,OAAlB,IAA6BF,CAAC,CAACE,OAAF,CAAUC,OAAV,CAAkB,MAAlB,MAA8B,CAAjE,CAAlC,EAAuG9E,KAAK,CAAC+E,SAAD,CAA5G,CAAnB;IACA;;IACA,KAAKC,aAAL,GAAqB,KAAKV,YAAL,CAAkBE,IAAlB,CAAuB1E,MAAM,CAAC2E,CAAC,IAAI,CAACA,CAAP,CAA7B,EAAwC1E,GAAG,CAAC,MAAM,CAAG,CAAV,CAA3C,CAArB;IACA;;IACA,KAAKkF,WAAL,GAAmB,KAAKd,iBAAL,CAAuBK,IAAvB,CAA4B1E,MAAM,CAAC6E,CAAC,IAAIA,CAAC,CAACC,SAAF,KAAgBD,CAAC,CAACE,OAAlB,IAA6BF,CAAC,CAACE,OAAF,KAAc,MAAjD,CAAlC,EAA4F7E,KAAK,CAAC+E,SAAD,CAAjG,CAAnB;IACA;;IACA,KAAKG,UAAL,GAAkB,IAAIvF,OAAJ,EAAlB;IACA;IACA;;IACA,KAAKwF,iBAAL,GAAyB,IAAIzG,YAAJ,EAAzB;IACA;AACR;AACA;AACA;;IACQ,KAAK0G,YAAL,GAAoB,IAAIzF,OAAJ,EAApB;IACA,KAAK2E,YAAL,CAAkBvC,SAAlB,CAA6BsD,MAAD,IAAY;MACpC,IAAIA,MAAJ,EAAY;QACR,IAAI,KAAKzB,IAAT,EAAe;UACX,KAAKC,oCAAL,GAA4C,KAAKD,IAAL,CAAU0B,aAAtD;QACH;;QACD,KAAKC,UAAL;MACH,CALD,MAMK,IAAI,KAAKC,oBAAL,EAAJ,EAAiC;QAClC,KAAKC,aAAL,CAAmB,KAAKC,UAAL,IAAmB,SAAtC;MACH;IACJ,CAVD;IAWA;AACR;AACA;AACA;AACA;;IACQ,KAAKhC,OAAL,CAAaiC,iBAAb,CAA+B,MAAM;MACjC/F,SAAS,CAAC,KAAK0D,WAAL,CAAiBsC,aAAlB,EAAiC,SAAjC,CAAT,CACKpB,IADL,CACU1E,MAAM,CAAC+F,KAAK,IAAI;QACtB,OAAOA,KAAK,CAACC,OAAN,KAAkBtG,MAAlB,IAA4B,CAAC,KAAKuG,YAAlC,IAAkD,CAACtG,cAAc,CAACoG,KAAD,CAAxE;MACH,CAFe,CADhB,EAGI5F,SAAS,CAAC,KAAKiF,UAAN,CAHb,EAIKnD,SAJL,CAIe8D,KAAK,IAAI,KAAKnC,OAAL,CAAasC,GAAb,CAAiB,MAAM;QAC3C,KAAKC,KAAL;QACAJ,KAAK,CAACK,eAAN;QACAL,KAAK,CAACM,cAAN;MACH,CAJuB,CAJxB;IASH,CAVD,EA5DoH,CAuEpH;IACA;;;IACA,KAAK/B,aAAL,CACKI,IADL,CACUtE,oBAAoB,CAAC,CAACkG,CAAD,EAAIC,CAAJ,KAAU;MACrC,OAAOD,CAAC,CAACxB,SAAF,KAAgByB,CAAC,CAACzB,SAAlB,IAA+BwB,CAAC,CAACvB,OAAF,KAAcwB,CAAC,CAACxB,OAAtD;IACH,CAF6B,CAD9B,EAIK9C,SAJL,CAIgB8D,KAAD,IAAW;MACtB,MAAM;QAAEjB,SAAF;QAAaC;MAAb,IAAyBgB,KAA/B;;MACA,IAAKhB,OAAO,CAACC,OAAR,CAAgB,MAAhB,MAA4B,CAA5B,IAAiCF,SAAS,KAAK,MAAhD,IACCC,OAAO,KAAK,MAAZ,IAAsBD,SAAS,CAACE,OAAV,CAAkB,MAAlB,MAA8B,CADzD,EAC6D;QACzD,KAAKR,YAAL,CAAkBgC,IAAlB,CAAuB,KAAKpC,OAA5B;MACH;IACJ,CAVD;EAWH;EACD;;;EACY,IAARnD,QAAQ,GAAG;IACX,OAAO,KAAKgD,SAAZ;EACH;;EACW,IAARhD,QAAQ,CAACwF,KAAD,EAAQ;IAChB;IACAA,KAAK,GAAGA,KAAK,KAAK,KAAV,GAAkB,KAAlB,GAA0B,OAAlC;;IACA,IAAIA,KAAK,KAAK,KAAKxC,SAAnB,EAA8B;MAC1B;MACA,IAAI,KAAKyC,WAAT,EAAsB;QAClB,KAAKC,uBAAL,CAA6BF,KAA7B;MACH;;MACD,KAAKxC,SAAL,GAAiBwC,KAAjB;MACA,KAAKpB,iBAAL,CAAuBmB,IAAvB;IACH;EACJ;EACD;;;EACQ,IAAJI,IAAI,GAAG;IACP,OAAO,KAAK1C,KAAZ;EACH;;EACO,IAAJ0C,IAAI,CAACH,KAAD,EAAQ;IACZ,KAAKvC,KAAL,GAAauC,KAAb;;IACA,KAAKI,qBAAL;;IACA,KAAKvB,YAAL,CAAkBwB,IAAlB;EACH;EACD;;;EACgB,IAAZb,YAAY,GAAG;IACf,OAAO,KAAK9B,aAAZ;EACH;;EACe,IAAZ8B,YAAY,CAACQ,KAAD,EAAQ;IACpB,KAAKtC,aAAL,GAAqB3E,qBAAqB,CAACiH,KAAD,CAA1C;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACiB,IAATM,SAAS,GAAG;IACZ,MAAMN,KAAK,GAAG,KAAKO,UAAnB,CADY,CAEZ;IACA;IACA;;IACA,IAAIP,KAAK,IAAI,IAAb,EAAmB;MACf,IAAI,KAAKG,IAAL,KAAc,MAAlB,EAA0B;QACtB,OAAO,QAAP;MACH,CAFD,MAGK;QACD,OAAO,gBAAP;MACH;IACJ;;IACD,OAAOH,KAAP;EACH;;EACY,IAATM,SAAS,CAACN,KAAD,EAAQ;IACjB,IAAIA,KAAK,KAAK,MAAV,IAAoBA,KAAK,KAAK,OAA9B,IAAyCA,KAAK,IAAI,IAAtD,EAA4D;MACxDA,KAAK,GAAGjH,qBAAqB,CAACiH,KAAD,CAA7B;IACH;;IACD,KAAKO,UAAL,GAAkBP,KAAlB;EACH;EACD;AACJ;AACA;AACA;;;EACc,IAANlB,MAAM,GAAG;IACT,OAAO,KAAKnB,OAAZ;EACH;;EACS,IAANmB,MAAM,CAACkB,KAAD,EAAQ;IACd,KAAKQ,MAAL,CAAYzH,qBAAqB,CAACiH,KAAD,CAAjC;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIS,WAAW,CAACC,OAAD,EAAUC,OAAV,EAAmB;IAC1B,IAAI,CAAC,KAAKvD,qBAAL,CAA2BwD,WAA3B,CAAuCF,OAAvC,CAAL,EAAsD;MAClDA,OAAO,CAACG,QAAR,GAAmB,CAAC,CAApB,CADkD,CAElD;;MACA,KAAK1D,OAAL,CAAaiC,iBAAb,CAA+B,MAAM;QACjC,MAAM0B,QAAQ,GAAG,MAAM;UACnBJ,OAAO,CAACK,mBAAR,CAA4B,MAA5B,EAAoCD,QAApC;UACAJ,OAAO,CAACK,mBAAR,CAA4B,WAA5B,EAAyCD,QAAzC;UACAJ,OAAO,CAACM,eAAR,CAAwB,UAAxB;QACH,CAJD;;QAKAN,OAAO,CAACO,gBAAR,CAAyB,MAAzB,EAAiCH,QAAjC;QACAJ,OAAO,CAACO,gBAAR,CAAyB,WAAzB,EAAsCH,QAAtC;MACH,CARD;IASH;;IACDJ,OAAO,CAACQ,KAAR,CAAcP,OAAd;EACH;EACD;AACJ;AACA;AACA;;;EACIQ,mBAAmB,CAAC9E,QAAD,EAAWsE,OAAX,EAAoB;IACnC,IAAIS,cAAc,GAAG,KAAKrE,WAAL,CAAiBsC,aAAjB,CAA+BgC,aAA/B,CAA6ChF,QAA7C,CAArB;;IACA,IAAI+E,cAAJ,EAAoB;MAChB,KAAKX,WAAL,CAAiBW,cAAjB,EAAiCT,OAAjC;IACH;EACJ;EACD;AACJ;AACA;AACA;;;EACI3B,UAAU,GAAG;IACT,IAAI,CAAC,KAAKsC,UAAV,EAAsB;MAClB;IACH;;IACD,MAAMZ,OAAO,GAAG,KAAK3D,WAAL,CAAiBsC,aAAjC,CAJS,CAKT;IACA;IACA;;IACA,QAAQ,KAAKiB,SAAb;MACI,KAAK,KAAL;MACA,KAAK,QAAL;QACI;;MACJ,KAAK,IAAL;MACA,KAAK,gBAAL;QACI,KAAKgB,UAAL,CAAgBC,4BAAhB,GAA+CC,IAA/C,CAAoDC,aAAa,IAAI;UACjE,IAAI,CAACA,aAAD,IAAkB,OAAO,KAAK1E,WAAL,CAAiBsC,aAAjB,CAA+B6B,KAAtC,KAAgD,UAAtE,EAAkF;YAC9ER,OAAO,CAACQ,KAAR;UACH;QACJ,CAJD;;QAKA;;MACJ,KAAK,eAAL;QACI,KAAKC,mBAAL,CAAyB,0CAAzB;;QACA;;MACJ;QACI,KAAKA,mBAAL,CAAyB,KAAKb,SAA9B;;QACA;IAjBR;EAmBH;EACD;AACJ;AACA;AACA;;;EACIpB,aAAa,CAACwC,WAAD,EAAc;IACvB,IAAI,KAAKpB,SAAL,KAAmB,QAAvB,EAAiC;MAC7B;IACH;;IACD,IAAI,KAAKhD,oCAAT,EAA+C;MAC3C,KAAKL,aAAL,CAAmB0E,QAAnB,CAA4B,KAAKrE,oCAAjC,EAAuEoE,WAAvE;IACH,CAFD,MAGK;MACD,KAAK3E,WAAL,CAAiBsC,aAAjB,CAA+BuC,IAA/B;IACH;;IACD,KAAKtE,oCAAL,GAA4C,IAA5C;EACH;EACD;;;EACA2B,oBAAoB,GAAG;IACnB,MAAM4C,QAAQ,GAAG,KAAKxE,IAAL,CAAU0B,aAA3B;IACA,OAAO,CAAC,CAAC8C,QAAF,IAAc,KAAK9E,WAAL,CAAiBsC,aAAjB,CAA+ByC,QAA/B,CAAwCD,QAAxC,CAArB;EACH;;EACDE,eAAe,GAAG;IACd,KAAK9B,WAAL,GAAmB,IAAnB;IACA,KAAKqB,UAAL,GAAkB,KAAKtE,iBAAL,CAAuBgF,MAAvB,CAA8B,KAAKjF,WAAL,CAAiBsC,aAA/C,CAAlB;;IACA,KAAKe,qBAAL,GAHc,CAId;IACA;;;IACA,IAAI,KAAK5C,SAAL,KAAmB,KAAvB,EAA8B;MAC1B,KAAK0C,uBAAL,CAA6B,KAA7B;IACH;EACJ;;EACD+B,qBAAqB,GAAG;IACpB;IACA;IACA;IACA;IACA,IAAI,KAAK/E,SAAL,CAAegF,SAAnB,EAA8B;MAC1B,KAAK3E,iBAAL,GAAyB,IAAzB;IACH;EACJ;;EACD4E,WAAW,GAAG;IACV,IAAI,KAAKb,UAAT,EAAqB;MACjB,KAAKA,UAAL,CAAgBc,OAAhB;IACH;;IACD,KAAKC,OAAL,EAAcC,MAAd;IACA,KAAKD,OAAL,GAAe,IAAf;;IACA,KAAKzE,iBAAL,CAAuB2E,QAAvB;;IACA,KAAK1E,aAAL,CAAmB0E,QAAnB;;IACA,KAAK1D,YAAL,CAAkB0D,QAAlB;;IACA,KAAK5D,UAAL,CAAgB0B,IAAhB;;IACA,KAAK1B,UAAL,CAAgB4D,QAAhB;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIC,IAAI,CAACC,SAAD,EAAY;IACZ,OAAO,KAAKjC,MAAL,CAAY,IAAZ,EAAkBiC,SAAlB,CAAP;EACH;EACD;;;EACA/C,KAAK,GAAG;IACJ,OAAO,KAAKc,MAAL,CAAY,KAAZ,CAAP;EACH;EACD;;;EACAkC,sBAAsB,GAAG;IACrB;IACA;IACA;IACA,OAAO,KAAKC,QAAL;IAAc;IAAa,KAA3B;IAAkC;IAAmB,IAArD,EAA2D,OAA3D,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACInC,MAAM,CAACoC,MAAM,GAAG,CAAC,KAAK9D,MAAhB,EAAwB2D,SAAxB,EAAmC;IACrC;IACA;IACA,IAAIG,MAAM,IAAIH,SAAd,EAAyB;MACrB,KAAKtD,UAAL,GAAkBsD,SAAlB;IACH;;IACD,MAAMI,MAAM,GAAG,KAAKF,QAAL,CAAcC,MAAd;IACf;IAAmB,CAACA,MAAD,IAAW,KAAK3D,oBAAL,EADf,EAC4C,KAAKE,UAAL,IAAmB,SAD/D,CAAf;;IAEA,IAAI,CAACyD,MAAL,EAAa;MACT,KAAKzD,UAAL,GAAkB,IAAlB;IACH;;IACD,OAAO0D,MAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIF,QAAQ,CAACC,MAAD,EAASE,YAAT,EAAuBpB,WAAvB,EAAoC;IACxC,KAAK/D,OAAL,GAAeiF,MAAf;;IACA,IAAIA,MAAJ,EAAY;MACR,KAAK9E,eAAL,GAAuB,KAAKP,iBAAL,GAAyB,MAAzB,GAAkC,cAAzD;IACH,CAFD,MAGK;MACD,KAAKO,eAAL,GAAuB,MAAvB;;MACA,IAAIgF,YAAJ,EAAkB;QACd,KAAK5D,aAAL,CAAmBwC,WAAnB;MACH;IACJ;;IACD,KAAKtB,qBAAL;;IACA,OAAO,IAAI2C,OAAJ,CAAYC,OAAO,IAAI;MAC1B,KAAKjF,YAAL,CAAkBE,IAAlB,CAAuBrE,IAAI,CAAC,CAAD,CAA3B,EAAgC4B,SAAhC,CAA0CgH,IAAI,IAAIQ,OAAO,CAACR,IAAI,GAAG,MAAH,GAAY,OAAjB,CAAzD;IACH,CAFM,CAAP;EAGH;;EACDS,SAAS,GAAG;IACR,OAAO,KAAKlG,WAAL,CAAiBsC,aAAjB,GAAiC,KAAKtC,WAAL,CAAiBsC,aAAjB,CAA+B6D,WAA/B,IAA8C,CAA/E,GAAmF,CAA1F;EACH;EACD;;;EACA9C,qBAAqB,GAAG;IACpB,IAAI,KAAKkB,UAAT,EAAqB;MACjB;MACA,KAAKA,UAAL,CAAgB6B,OAAhB,GAA0B,KAAKrE,MAAL,IAAe,KAAKqB,IAAL,KAAc,MAAvD;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACID,uBAAuB,CAACkD,WAAD,EAAc;IACjC,MAAM1C,OAAO,GAAG,KAAK3D,WAAL,CAAiBsC,aAAjC;IACA,MAAMgE,MAAM,GAAG3C,OAAO,CAAC4C,UAAvB;;IACA,IAAIF,WAAW,KAAK,KAApB,EAA2B;MACvB,IAAI,CAAC,KAAKf,OAAV,EAAmB;QACf,KAAKA,OAAL,GAAe,KAAKhF,IAAL,CAAUkG,aAAV,CAAwB,mBAAxB,CAAf;QACAF,MAAM,CAACG,YAAP,CAAoB,KAAKnB,OAAzB,EAAkC3B,OAAlC;MACH;;MACD2C,MAAM,CAACI,WAAP,CAAmB/C,OAAnB;IACH,CAND,MAOK,IAAI,KAAK2B,OAAT,EAAkB;MACnB,KAAKA,OAAL,CAAaiB,UAAb,CAAwBE,YAAxB,CAAqC9C,OAArC,EAA8C,KAAK2B,OAAnD;IACH;EACJ;;AAxWW;;AA0WhBvF,SAAS,CAACpB,IAAV;EAAA,iBAAsGoB,SAAtG,EA3YmGlF,EA2YnG,mBAAiIA,EAAE,CAACiE,UAApI,GA3YmGjE,EA2YnG,mBAA2JiB,EAAE,CAAC6K,gBAA9J,GA3YmG9L,EA2YnG,mBAA2LiB,EAAE,CAAC8K,YAA9L,GA3YmG/L,EA2YnG,mBAAuNuB,EAAE,CAACyK,QAA1N,GA3YmGhM,EA2YnG,mBAA+OA,EAAE,CAACmE,MAAlP,GA3YmGnE,EA2YnG,mBAAqQiB,EAAE,CAACgL,oBAAxQ,GA3YmGjM,EA2YnG,mBAAySF,QAAzS,MA3YmGE,EA2YnG,mBAA8UkD,oBAA9U;AAAA;;AACAgC,SAAS,CAACd,IAAV,kBA5YmGpE,EA4YnG;EAAA,MAA0FkF,SAA1F;EAAA;EAAA;IAAA;MA5YmGlF,EA4YnG;IAAA;;IAAA;MAAA;;MA5YmGA,EA4YnG,qBA5YmGA,EA4YnG;IAAA;EAAA;EAAA,wBAAkd,IAAld;EAAA;EAAA;IAAA;MA5YmGA,EA4YnG;QAAA,OAA0F,kCAA1F;MAAA;QAAA,OAA0F,8BAA1F;MAAA;IAAA;;IAAA;MA5YmGA,EA4YnG;MA5YmGA,EA4YnG;MA5YmGA,EA4YnG;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MA5YmGA,EA4YnG;MA5YmGA,EA4Y08B,+BAA7iC;MA5YmGA,EA4YihC,gBAApnC;MA5YmGA,EA4Y8iC,eAAjpC;IAAA;EAAA;EAAA,eAAwsCN,EAAE,CAACC,aAA3sC;EAAA;EAAA;IAAA,WAAwxC,CAAC8C,mBAAmB,CAACC,eAArB;EAAxxC;EAAA;AAAA;;AACA;EAAA,mDA7YmG1C,EA6YnG,mBAA2FkF,SAA3F,EAAkH,CAAC;IACvGX,IAAI,EAAEpE,SADiG;IAEvGqE,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,YAAZ;MAA0ByH,QAAQ,EAAE,WAApC;MAAiDC,UAAU,EAAE,CAAC1J,mBAAmB,CAACC,eAArB,CAA7D;MAAoGiC,IAAI,EAAE;QACrG,SAAS,YAD4F;QAErG;QACA,gBAAgB,MAHqF;QAIrG,0BAA0B,oBAJ2E;QAKrG,2BAA2B,iBAL0E;QAMrG,2BAA2B,iBAN0E;QAOrG,2BAA2B,iBAP0E;QAQrG,6BAA6B,QARwE;QASrG,YAAY,IATyF;QAUrG,gBAAgB,iBAVqF;QAWrG,sBAAsB,gCAX+E;QAYrG,qBAAqB;MAZgF,CAA1G;MAaIC,eAAe,EAAExE,uBAAuB,CAACyE,MAb7C;MAaqDC,aAAa,EAAEzE,iBAAiB,CAAC0E,IAbtF;MAa4FL,QAAQ,EAAE;IAbtG,CAAD;EAFiG,CAAD,CAAlH,EAgB4B,YAAY;IAAE,OAAO,CAAC;MAAEH,IAAI,EAAEvE,EAAE,CAACiE;IAAX,CAAD,EAA0B;MAAEM,IAAI,EAAEtD,EAAE,CAAC6K;IAAX,CAA1B,EAAyD;MAAEvH,IAAI,EAAEtD,EAAE,CAAC8K;IAAX,CAAzD,EAAoF;MAAExH,IAAI,EAAEhD,EAAE,CAACyK;IAAX,CAApF,EAA2G;MAAEzH,IAAI,EAAEvE,EAAE,CAACmE;IAAX,CAA3G,EAAgI;MAAEI,IAAI,EAAEtD,EAAE,CAACgL;IAAX,CAAhI,EAAmK;MAAE1H,IAAI,EAAEqC,SAAR;MAAmB3B,UAAU,EAAE,CAAC;QAChOV,IAAI,EAAE/D;MAD0N,CAAD,EAEhO;QACC+D,IAAI,EAAEjE,MADP;QAECkE,IAAI,EAAE,CAAC1E,QAAD;MAFP,CAFgO;IAA/B,CAAnK,EAK3B;MAAEyE,IAAI,EAAEP,kBAAR;MAA4BiB,UAAU,EAAE,CAAC;QAC3CV,IAAI,EAAE/D;MADqC,CAAD,EAE3C;QACC+D,IAAI,EAAEjE,MADP;QAECkE,IAAI,EAAE,CAACtB,oBAAD;MAFP,CAF2C;IAAxC,CAL2B,CAAP;EAUlB,CA1BxB,EA0B0C;IAAEN,QAAQ,EAAE,CAAC;MACvC2B,IAAI,EAAE9D;IADiC,CAAD,CAAZ;IAE1B8H,IAAI,EAAE,CAAC;MACPhE,IAAI,EAAE9D;IADC,CAAD,CAFoB;IAI1BmH,YAAY,EAAE,CAAC;MACfrD,IAAI,EAAE9D;IADS,CAAD,CAJY;IAM1BiI,SAAS,EAAE,CAAC;MACZnE,IAAI,EAAE9D;IADM,CAAD,CANe;IAQ1ByG,MAAM,EAAE,CAAC;MACT3C,IAAI,EAAE9D;IADG,CAAD,CARkB;IAU1B0F,YAAY,EAAE,CAAC;MACf5B,IAAI,EAAE7D;IADS,CAAD,CAVY;IAY1B0F,aAAa,EAAE,CAAC;MAChB7B,IAAI,EAAE7D,MADU;MAEhB8D,IAAI,EAAE,CAAC,QAAD;IAFU,CAAD,CAZW;IAe1B+B,WAAW,EAAE,CAAC;MACdhC,IAAI,EAAE7D;IADQ,CAAD,CAfa;IAiB1BmG,aAAa,EAAE,CAAC;MAChBtC,IAAI,EAAE7D,MADU;MAEhB8D,IAAI,EAAE,CAAC,QAAD;IAFU,CAAD,CAjBW;IAoB1BsC,WAAW,EAAE,CAAC;MACdvC,IAAI,EAAE7D;IADQ,CAAD,CApBa;IAsB1BsG,iBAAiB,EAAE,CAAC;MACpBzC,IAAI,EAAE7D,MADc;MAEpB8D,IAAI,EAAE,CAAC,iBAAD;IAFc,CAAD,CAtBO;IAyB1B4H,QAAQ,EAAE,CAAC;MACX7H,IAAI,EAAE5D,SADK;MAEX6D,IAAI,EAAE,CAAC,SAAD;IAFK,CAAD;EAzBgB,CA1B1C;AAAA;AAuDA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMR,kBAAN,CAAyB;EACrBZ,WAAW,CAACiJ,IAAD,EAAOC,QAAP,EAAiB/G,OAAjB,EAA0BlC,kBAA1B,EAA8CkJ,aAA9C,EAA6DC,eAAe,GAAG,KAA/E,EAAsFC,cAAtF,EAAsG;IAC7G,KAAKJ,IAAL,GAAYA,IAAZ;IACA,KAAKC,QAAL,GAAgBA,QAAhB;IACA,KAAK/G,OAAL,GAAeA,OAAf;IACA,KAAKlC,kBAAL,GAA0BA,kBAA1B;IACA,KAAKoJ,cAAL,GAAsBA,cAAtB;IACA;;IACA,KAAKC,QAAL,GAAgB,IAAI9L,SAAJ,EAAhB;IACA;;IACA,KAAK+L,aAAL,GAAqB,IAAIpM,YAAJ,EAArB;IACA;;IACA,KAAKwG,UAAL,GAAkB,IAAIvF,OAAJ,EAAlB;IACA;;IACA,KAAKoL,eAAL,GAAuB,IAAIpL,OAAJ,EAAvB;IACA;AACR;AACA;AACA;AACA;;IACQ,KAAKqL,eAAL,GAAuB;MAAEC,IAAI,EAAE,IAAR;MAAcC,KAAK,EAAE;IAArB,CAAvB;IACA,KAAKpJ,qBAAL,GAA6B,IAAInC,OAAJ,EAA7B,CApB6G,CAqB7G;IACA;;IACA,IAAI6K,IAAJ,EAAU;MACNA,IAAI,CAACW,MAAL,CAAY3G,IAAZ,CAAiBvE,SAAS,CAAC,KAAKiF,UAAN,CAA1B,EAA6CnD,SAA7C,CAAuD,MAAM;QACzD,KAAKqJ,gBAAL;;QACA,KAAKC,oBAAL;MACH,CAHD;IAIH,CA5B4G,CA6B7G;IACA;;;IACAX,aAAa,CACRS,MADL,GAEK3G,IAFL,CAEUvE,SAAS,CAAC,KAAKiF,UAAN,CAFnB,EAGKnD,SAHL,CAGe,MAAM,KAAKsJ,oBAAL,EAHrB;IAIA,KAAKC,SAAL,GAAiBX,eAAjB;EACH;EACD;;;EACS,IAALY,KAAK,GAAG;IACR,OAAO,KAAKC,MAAZ;EACH;EACD;;;EACO,IAAHC,GAAG,GAAG;IACN,OAAO,KAAKC,IAAZ;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;EACgB,IAARC,QAAQ,GAAG;IACX,OAAO,KAAKL,SAAZ;EACH;;EACW,IAARK,QAAQ,CAACpF,KAAD,EAAQ;IAChB,KAAK+E,SAAL,GAAiBhM,qBAAqB,CAACiH,KAAD,CAAtC;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACmB,IAAXqF,WAAW,GAAG;IACd,IAAI,KAAKC,iBAAL,IAA0B,IAA9B,EAAoC;MAChC,OAAO,CAAC,KAAKL,MAAN,IAAgB,KAAKA,MAAL,CAAY9E,IAAZ,KAAqB,MAArC,IAA+C,CAAC,KAAKgF,IAArD,IAA6D,KAAKA,IAAL,CAAUhF,IAAV,KAAmB,MAAvF;IACH;;IACD,OAAO,KAAKmF,iBAAZ;EACH;;EACc,IAAXD,WAAW,CAACrF,KAAD,EAAQ;IACnB,KAAKsF,iBAAL,GAAyBtF,KAAK,IAAI,IAAT,GAAgB,IAAhB,GAAuBjH,qBAAqB,CAACiH,KAAD,CAArE;EACH;EACD;;;EACc,IAAVuF,UAAU,GAAG;IACb,OAAO,KAAKC,YAAL,IAAqB,KAAKxB,QAAjC;EACH;;EACD1I,kBAAkB,GAAG;IACjB,KAAKmK,WAAL,CAAiBC,OAAjB,CACKzH,IADL,CACUpE,SAAS,CAAC,KAAK4L,WAAN,CADnB,EACuC/L,SAAS,CAAC,KAAKiF,UAAN,CADhD,EAEKnD,SAFL,CAEgBmK,MAAD,IAAY;MACvB,KAAKrB,QAAL,CAAcsB,KAAd,CAAoBD,MAAM,CAACpM,MAAP,CAAcsM,IAAI,IAAI,CAACA,IAAI,CAAC3K,UAAN,IAAoB2K,IAAI,CAAC3K,UAAL,KAAoB,IAA9D,CAApB;;MACA,KAAKoJ,QAAL,CAAcwB,eAAd;IACH,CALD;;IAMA,KAAKxB,QAAL,CAAcoB,OAAd,CAAsBzH,IAAtB,CAA2BpE,SAAS,CAAC,IAAD,CAApC,EAA4C2B,SAA5C,CAAsD,MAAM;MACxD,KAAKqJ,gBAAL;;MACA,KAAKP,QAAL,CAAcyB,OAAd,CAAuBJ,MAAD,IAAY;QAC9B,KAAKK,kBAAL,CAAwBL,MAAxB;;QACA,KAAKM,oBAAL,CAA0BN,MAA1B;;QACA,KAAKO,gBAAL,CAAsBP,MAAtB;MACH,CAJD;;MAKA,IAAI,CAAC,KAAKrB,QAAL,CAAc6B,MAAf,IACA,KAAKC,aAAL,CAAmB,KAAKnB,MAAxB,CADA,IAEA,KAAKmB,aAAL,CAAmB,KAAKjB,IAAxB,CAFJ,EAEmC;QAC/B,KAAKL,oBAAL;MACH;;MACD,KAAK7J,kBAAL,CAAwBQ,YAAxB;IACH,CAbD,EAPiB,CAqBjB;;;IACA,KAAK0B,OAAL,CAAaiC,iBAAb,CAA+B,MAAM;MACjC,KAAKoF,eAAL,CACKvG,IADL,CACUnE,YAAY,CAAC,EAAD,CADtB,EAC4B;MAC5BJ,SAAS,CAAC,KAAKiF,UAAN,CAFT,EAGKnD,SAHL,CAGe,MAAM,KAAKsJ,oBAAL,EAHrB;IAIH,CALD;EAMH;;EACD3C,WAAW,GAAG;IACV,KAAK5G,qBAAL,CAA2BgH,QAA3B;;IACA,KAAKiC,eAAL,CAAqBjC,QAArB;;IACA,KAAK+B,QAAL,CAAclC,OAAd;;IACA,KAAKzD,UAAL,CAAgB0B,IAAhB;;IACA,KAAK1B,UAAL,CAAgB4D,QAAhB;EACH;EACD;;;EACAC,IAAI,GAAG;IACH,KAAK8B,QAAL,CAAcyB,OAAd,CAAsBJ,MAAM,IAAIA,MAAM,CAACnD,IAAP,EAAhC;EACH;EACD;;;EACA9C,KAAK,GAAG;IACJ,KAAK4E,QAAL,CAAcyB,OAAd,CAAsBJ,MAAM,IAAIA,MAAM,CAACjG,KAAP,EAAhC;EACH;EACD;AACJ;AACA;AACA;;;EACIoF,oBAAoB,GAAG;IACnB;IACA;IACA;IACA;IACA;IACA;IACA,IAAIJ,IAAI,GAAG,CAAX;IACA,IAAIC,KAAK,GAAG,CAAZ;;IACA,IAAI,KAAK0B,KAAL,IAAc,KAAKA,KAAL,CAAWvH,MAA7B,EAAqC;MACjC,IAAI,KAAKuH,KAAL,CAAWlG,IAAX,IAAmB,MAAvB,EAA+B;QAC3BuE,IAAI,IAAI,KAAK2B,KAAL,CAAWpD,SAAX,EAAR;MACH,CAFD,MAGK,IAAI,KAAKoD,KAAL,CAAWlG,IAAX,IAAmB,MAAvB,EAA+B;QAChC,MAAMmG,KAAK,GAAG,KAAKD,KAAL,CAAWpD,SAAX,EAAd;;QACAyB,IAAI,IAAI4B,KAAR;QACA3B,KAAK,IAAI2B,KAAT;MACH;IACJ;;IACD,IAAI,KAAKC,MAAL,IAAe,KAAKA,MAAL,CAAYzH,MAA/B,EAAuC;MACnC,IAAI,KAAKyH,MAAL,CAAYpG,IAAZ,IAAoB,MAAxB,EAAgC;QAC5BwE,KAAK,IAAI,KAAK4B,MAAL,CAAYtD,SAAZ,EAAT;MACH,CAFD,MAGK,IAAI,KAAKsD,MAAL,CAAYpG,IAAZ,IAAoB,MAAxB,EAAgC;QACjC,MAAMmG,KAAK,GAAG,KAAKC,MAAL,CAAYtD,SAAZ,EAAd;;QACA0B,KAAK,IAAI2B,KAAT;QACA5B,IAAI,IAAI4B,KAAR;MACH;IACJ,CA5BkB,CA6BnB;IACA;IACA;IACA;;;IACA5B,IAAI,GAAGA,IAAI,IAAI,IAAf;IACAC,KAAK,GAAGA,KAAK,IAAI,IAAjB;;IACA,IAAID,IAAI,KAAK,KAAKD,eAAL,CAAqBC,IAA9B,IAAsCC,KAAK,KAAK,KAAKF,eAAL,CAAqBE,KAAzE,EAAgF;MAC5E,KAAKF,eAAL,GAAuB;QAAEC,IAAF;QAAQC;MAAR,CAAvB,CAD4E,CAE5E;MACA;;MACA,KAAKxH,OAAL,CAAasC,GAAb,CAAiB,MAAM,KAAKlE,qBAAL,CAA2B8E,IAA3B,CAAgC,KAAKoE,eAArC,CAAvB;IACH;EACJ;;EACD+B,SAAS,GAAG;IACR;IACA,IAAI,KAAKzB,SAAL,IAAkB,KAAK0B,SAAL,EAAtB,EAAwC;MACpC;MACA,KAAKtJ,OAAL,CAAaiC,iBAAb,CAA+B,MAAM,KAAKoF,eAAL,CAAqBnE,IAArB,EAArC;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;;;EACI2F,kBAAkB,CAACL,MAAD,EAAS;IACvBA,MAAM,CAAC/H,iBAAP,CACKK,IADL,CACU1E,MAAM,CAAE+F,KAAD,IAAWA,KAAK,CAACjB,SAAN,KAAoBiB,KAAK,CAAChB,OAAtC,CADhB,EACgE5E,SAAS,CAAC,KAAK4K,QAAL,CAAcoB,OAAf,CADzE,EAEKlK,SAFL,CAEgB8D,KAAD,IAAW;MACtB;MACA;MACA,IAAIA,KAAK,CAAChB,OAAN,KAAkB,cAAlB,IAAoC,KAAK+F,cAAL,KAAwB,gBAAhE,EAAkF;QAC9E,KAAKH,QAAL,CAAc7E,aAAd,CAA4BqH,SAA5B,CAAsCC,GAAtC,CAA0C,uBAA1C;MACH;;MACD,KAAK7B,oBAAL;;MACA,KAAK7J,kBAAL,CAAwBQ,YAAxB;IACH,CAVD;;IAWA,IAAIkK,MAAM,CAACxF,IAAP,KAAgB,MAApB,EAA4B;MACxBwF,MAAM,CAAC5H,YAAP,CACKE,IADL,CACUvE,SAAS,CAAC,KAAK4K,QAAL,CAAcoB,OAAf,CADnB,EAEKlK,SAFL,CAEe,MAAM,KAAKoL,kBAAL,CAAwBjB,MAAM,CAAC7G,MAA/B,CAFrB;IAGH;EACJ;EACD;AACJ;AACA;AACA;;;EACImH,oBAAoB,CAACN,MAAD,EAAS;IACzB,IAAI,CAACA,MAAL,EAAa;MACT;IACH,CAHwB,CAIzB;IACA;;;IACAA,MAAM,CAAC/G,iBAAP,CAAyBX,IAAzB,CAA8BvE,SAAS,CAAC,KAAK4K,QAAL,CAAcoB,OAAf,CAAvC,EAAgElK,SAAhE,CAA0E,MAAM;MAC5E,KAAK2B,OAAL,CAAa0J,gBAAb,CAA8B5I,IAA9B,CAAmCrE,IAAI,CAAC,CAAD,CAAvC,EAA4C4B,SAA5C,CAAsD,MAAM;QACxD,KAAKqJ,gBAAL;MACH,CAFD;IAGH,CAJD;EAKH;EACD;;;EACAqB,gBAAgB,CAACP,MAAD,EAAS;IACrB,IAAIA,MAAJ,EAAY;MACRA,MAAM,CAAC9G,YAAP,CACKZ,IADL,CACUvE,SAAS,CAACJ,KAAK,CAAC,KAAKgL,QAAL,CAAcoB,OAAf,EAAwB,KAAK/G,UAA7B,CAAN,CADnB,EAEKnD,SAFL,CAEe,MAAM;QACjB,KAAKsJ,oBAAL;;QACA,KAAK7J,kBAAL,CAAwBQ,YAAxB;MACH,CALD;IAMH;EACJ;EACD;;;EACAmL,kBAAkB,CAACE,KAAD,EAAQ;IACtB,MAAMJ,SAAS,GAAG,KAAKxC,QAAL,CAAc7E,aAAd,CAA4BqH,SAA9C;IACA,MAAMK,SAAS,GAAG,+BAAlB;;IACA,IAAID,KAAJ,EAAW;MACPJ,SAAS,CAACC,GAAV,CAAcI,SAAd;IACH,CAFD,MAGK;MACDL,SAAS,CAACpE,MAAV,CAAiByE,SAAjB;IACH;EACJ;EACD;;;EACAlC,gBAAgB,GAAG;IACf,KAAKI,MAAL,GAAc,KAAKE,IAAL,GAAY,IAA1B,CADe,CAEf;;IACA,KAAKb,QAAL,CAAcyB,OAAd,CAAsBJ,MAAM,IAAI;MAC5B,IAAIA,MAAM,CAACnL,QAAP,IAAmB,KAAvB,EAA8B;QAC1B,IAAI,KAAK2K,IAAL,IAAa,IAAb,KAAsB,OAAO6B,SAAP,KAAqB,WAArB,IAAoCA,SAA1D,CAAJ,EAA0E;UACtEzM,6BAA6B,CAAC,KAAD,CAA7B;QACH;;QACD,KAAK4K,IAAL,GAAYQ,MAAZ;MACH,CALD,MAMK;QACD,IAAI,KAAKV,MAAL,IAAe,IAAf,KAAwB,OAAO+B,SAAP,KAAqB,WAArB,IAAoCA,SAA5D,CAAJ,EAA4E;UACxEzM,6BAA6B,CAAC,OAAD,CAA7B;QACH;;QACD,KAAK0K,MAAL,GAAcU,MAAd;MACH;IACJ,CAbD;;IAcA,KAAKY,MAAL,GAAc,KAAKF,KAAL,GAAa,IAA3B,CAjBe,CAkBf;;IACA,IAAI,KAAKpC,IAAL,IAAa,KAAKA,IAAL,CAAUjE,KAAV,KAAoB,KAArC,EAA4C;MACxC,KAAKqG,KAAL,GAAa,KAAKlB,IAAlB;MACA,KAAKoB,MAAL,GAAc,KAAKtB,MAAnB;IACH,CAHD,MAIK;MACD,KAAKoB,KAAL,GAAa,KAAKpB,MAAlB;MACA,KAAKsB,MAAL,GAAc,KAAKpB,IAAnB;IACH;EACJ;EACD;;;EACAsB,SAAS,GAAG;IACR,OAAS,KAAKL,aAAL,CAAmB,KAAKnB,MAAxB,KAAmC,KAAKA,MAAL,CAAY9E,IAAZ,IAAoB,MAAxD,IACH,KAAKiG,aAAL,CAAmB,KAAKjB,IAAxB,KAAiC,KAAKA,IAAL,CAAUhF,IAAV,IAAkB,MADxD;EAEH;;EACD8G,kBAAkB,GAAG;IACjB,KAAK1C,aAAL,CAAmBxE,IAAnB;;IACA,KAAKmH,6BAAL;EACH;;EACDA,6BAA6B,GAAG;IAC5B;IACA,CAAC,KAAKjC,MAAN,EAAc,KAAKE,IAAnB,EACK5L,MADL,CACYoM,MAAM,IAAIA,MAAM,IAAI,CAACA,MAAM,CAACnG,YAAlB,IAAkC,KAAK2H,gBAAL,CAAsBxB,MAAtB,CADxD,EAEKI,OAFL,CAEaJ,MAAM,IAAIA,MAAM,CAACjD,sBAAP,EAFvB;EAGH;;EACD0E,kBAAkB,GAAG;IACjB,OAAS,KAAKhB,aAAL,CAAmB,KAAKnB,MAAxB,KAAmC,KAAKkC,gBAAL,CAAsB,KAAKlC,MAA3B,CAApC,IACH,KAAKmB,aAAL,CAAmB,KAAKjB,IAAxB,KAAiC,KAAKgC,gBAAL,CAAsB,KAAKhC,IAA3B,CADtC;EAEH;;EACDgC,gBAAgB,CAACxB,MAAD,EAAS;IACrB,OAAOA,MAAM,CAACxF,IAAP,KAAgB,MAAhB,IAA0B,CAAC,CAAC,KAAKmF,iBAAxC;EACH;;EACDc,aAAa,CAACT,MAAD,EAAS;IAClB,OAAOA,MAAM,IAAI,IAAV,IAAkBA,MAAM,CAAC7G,MAAhC;EACH;;AAjSoB;;AAmSzBlD,kBAAkB,CAACF,IAAnB;EAAA,iBAA+GE,kBAA/G,EA7uBmGhE,EA6uBnG,mBAAmJkB,EAAE,CAACuO,cAAtJ,MA7uBmGzP,EA6uBnG,mBAAiMA,EAAE,CAACiE,UAApM,GA7uBmGjE,EA6uBnG,mBAA2NA,EAAE,CAACmE,MAA9N,GA7uBmGnE,EA6uBnG,mBAAiPA,EAAE,CAAC+D,iBAApP,GA7uBmG/D,EA6uBnG,mBAAkRN,EAAE,CAACgQ,aAArR,GA7uBmG1P,EA6uBnG,mBAA+S8C,2BAA/S,GA7uBmG9C,EA6uBnG,mBAAuVwC,qBAAvV;AAAA;;AACAwB,kBAAkB,CAACI,IAAnB,kBA9uBmGpE,EA8uBnG;EAAA,MAAmGgE,kBAAnG;EAAA;EAAA;IAAA;MA9uBmGhE,EA8uBnG,0BAKqEmD,gBALrE;MA9uBmGnD,EA8uBnG,0BAKsJkF,SALtJ;IAAA;;IAAA;MAAA;;MA9uBmGlF,EA8uBnG,qBA9uBmGA,EA8uBnG;MA9uBmGA,EA8uBnG,qBA9uBmGA,EA8uBnG;IAAA;EAAA;EAAA;IAAA;MA9uBmGA,EA8uBnG,aAK6PmD,gBAL7P;IAAA;;IAAA;MAAA;;MA9uBmGnD,EA8uBnG,qBA9uBmGA,EA8uBnG;IAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MA9uBmGA,EA8uBnG;IAAA;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA;EAAA,WA9uBmGA,EA8uBnG,oBAAsZ,CAC9Y;IACIqE,OAAO,EAAEnB,oBADb;IAEIoB,WAAW,EAAEN;EAFjB,CAD8Y,CAAtZ;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MA9uBmGhE,EA8uBnG;MA9uBmGA,EAmvB6P,iEALhW;MA9uBmGA,EAmvByZ,gBAL5f;MA9uBmGA,EAmvB4c,mBAL/iB;MA9uBmGA,EAmvBugB,+FAL1mB;IAAA;;IAAA;MA9uBmGA,EAmvBkU,oCALra;MA9uBmGA,EAmvB4hB,aAL/nB;MA9uBmGA,EAmvB4hB,kCAL/nB;IAAA;EAAA;EAAA,eAKo8FH,EAAE,CAAC8P,IALv8F,EAKwiGxM,gBALxiG;EAAA;EAAA;EAAA;AAAA;;AAMA;EAAA,mDApvBmGnD,EAovBnG,mBAA2FgE,kBAA3F,EAA2H,CAAC;IAChHO,IAAI,EAAEpE,SAD0G;IAEhHqE,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,sBAAZ;MAAoCyH,QAAQ,EAAE,oBAA9C;MAAoEvH,IAAI,EAAE;QACrE,SAAS,sBAD4D;QAErE,kDAAkD;MAFmB,CAA1E;MAGIC,eAAe,EAAExE,uBAAuB,CAACyE,MAH7C;MAGqDC,aAAa,EAAEzE,iBAAiB,CAAC0E,IAHtF;MAG4FC,SAAS,EAAE,CAClG;QACIX,OAAO,EAAEnB,oBADb;QAEIoB,WAAW,EAAEN;MAFjB,CADkG,CAHvG;MAQIU,QAAQ,EAAE,0WARd;MAQ0XkL,MAAM,EAAE,CAAC,msEAAD;IARlY,CAAD;EAF0G,CAAD,CAA3H,EAW4B,YAAY;IAAE,OAAO,CAAC;MAAErL,IAAI,EAAErD,EAAE,CAACuO,cAAX;MAA2BxK,UAAU,EAAE,CAAC;QACtEV,IAAI,EAAE/D;MADgE,CAAD;IAAvC,CAAD,EAE3B;MAAE+D,IAAI,EAAEvE,EAAE,CAACiE;IAAX,CAF2B,EAEF;MAAEM,IAAI,EAAEvE,EAAE,CAACmE;IAAX,CAFE,EAEmB;MAAEI,IAAI,EAAEvE,EAAE,CAAC+D;IAAX,CAFnB,EAEmD;MAAEQ,IAAI,EAAE7E,EAAE,CAACgQ;IAAX,CAFnD,EAE+E;MAAEnL,IAAI,EAAEqC,SAAR;MAAmB3B,UAAU,EAAE,CAAC;QAC5IV,IAAI,EAAEjE,MADsI;QAE5IkE,IAAI,EAAE,CAAC1B,2BAAD;MAFsI,CAAD;IAA/B,CAF/E,EAK3B;MAAEyB,IAAI,EAAEqC,SAAR;MAAmB3B,UAAU,EAAE,CAAC;QAClCV,IAAI,EAAE/D;MAD4B,CAAD,EAElC;QACC+D,IAAI,EAAEjE,MADP;QAECkE,IAAI,EAAE,CAAChC,qBAAD;MAFP,CAFkC;IAA/B,CAL2B,CAAP;EAUlB,CArBxB,EAqB0C;IAAEqL,WAAW,EAAE,CAAC;MAC1CtJ,IAAI,EAAE1D,eADoC;MAE1C2D,IAAI,EAAE,CAACU,SAAD,EAAY;QACV;QACA;QACA2K,WAAW,EAAE;MAHH,CAAZ;IAFoC,CAAD,CAAf;IAO1BzD,QAAQ,EAAE,CAAC;MACX7H,IAAI,EAAEzD,YADK;MAEX0D,IAAI,EAAE,CAACrB,gBAAD;IAFK,CAAD,CAPgB;IAU1ByK,YAAY,EAAE,CAAC;MACfrJ,IAAI,EAAE5D,SADS;MAEf6D,IAAI,EAAE,CAACrB,gBAAD;IAFS,CAAD,CAVY;IAa1BqK,QAAQ,EAAE,CAAC;MACXjJ,IAAI,EAAE9D;IADK,CAAD,CAbgB;IAe1BgN,WAAW,EAAE,CAAC;MACdlJ,IAAI,EAAE9D;IADQ,CAAD,CAfa;IAiB1BkM,aAAa,EAAE,CAAC;MAChBpI,IAAI,EAAE7D;IADU,CAAD;EAjBW,CArB1C;AAAA;AA0CA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMoP,iBAAN,SAAgC3M,gBAAhC,CAAiD;EAC7CC,WAAW,CAAC2M,iBAAD,EAAoBC,SAApB,EAA+BzM,UAA/B,EAA2CC,gBAA3C,EAA6DC,MAA7D,EAAqE;IAC5E,MAAMsM,iBAAN,EAAyBC,SAAzB,EAAoCzM,UAApC,EAAgDC,gBAAhD,EAAkEC,MAAlE;EACH;;AAH4C;;AAKjDqM,iBAAiB,CAAChM,IAAlB;EAAA,iBAA8GgM,iBAA9G,EA1yBmG9P,EA0yBnG,mBAAiJA,EAAE,CAAC+D,iBAApJ,GA1yBmG/D,EA0yBnG,mBAAkLE,UAAU,CAAC,MAAM+P,mBAAP,CAA5L,GA1yBmGjQ,EA0yBnG,mBAAoOA,EAAE,CAACiE,UAAvO,GA1yBmGjE,EA0yBnG,mBAA8PN,EAAE,CAACwE,gBAAjQ,GA1yBmGlE,EA0yBnG,mBAA8RA,EAAE,CAACmE,MAAjS;AAAA;;AACA2L,iBAAiB,CAAC1L,IAAlB,kBA3yBmGpE,EA2yBnG;EAAA,MAAkG8P,iBAAlG;EAAA;EAAA;EAAA;EAAA;IAAA;MA3yBmG9P,EA2yBnG;IAAA;EAAA;EAAA,WA3yBmGA,EA2yBnG,oBAA6W,CACrW;IACIqE,OAAO,EAAE1E,aADb;IAEI2E,WAAW,EAAEwL;EAFjB,CADqW,CAA7W,GA3yBmG9P,EA2yBnG;EAAA;EAAA;EAAA;EAAA;IAAA;MA3yBmGA,EA2yBnG;MA3yBmGA,EAgzB5C,gBALvD;IAAA;EAAA;EAAA;EAAA;AAAA;;AAMA;EAAA,mDAjzBmGA,EAizBnG,mBAA2F8P,iBAA3F,EAA0H,CAAC;IAC/GvL,IAAI,EAAEpE,SADyG;IAE/GqE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,qBADX;MAECC,QAAQ,EAAE,2BAFX;MAGCC,IAAI,EAAE;QACF,SAAS,wCADP;QAEF,0BAA0B,iCAFxB;QAGF,2BAA2B;MAHzB,CAHP;MAQCC,eAAe,EAAExE,uBAAuB,CAACyE,MAR1C;MASCC,aAAa,EAAEzE,iBAAiB,CAAC0E,IATlC;MAUCC,SAAS,EAAE,CACP;QACIX,OAAO,EAAE1E,aADb;QAEI2E,WAAW,EAAEwL;MAFjB,CADO;IAVZ,CAAD;EAFyG,CAAD,CAA1H,EAmB4B,YAAY;IAAE,OAAO,CAAC;MAAEvL,IAAI,EAAEvE,EAAE,CAAC+D;IAAX,CAAD,EAAiC;MAAEQ,IAAI,EAAE0L,mBAAR;MAA6BhL,UAAU,EAAE,CAAC;QACxGV,IAAI,EAAEjE,MADkG;QAExGkE,IAAI,EAAE,CAACtE,UAAU,CAAC,MAAM+P,mBAAP,CAAX;MAFkG,CAAD;IAAzC,CAAjC,EAG3B;MAAE1L,IAAI,EAAEvE,EAAE,CAACiE;IAAX,CAH2B,EAGF;MAAEM,IAAI,EAAE7E,EAAE,CAACwE;IAAX,CAHE,EAG6B;MAAEK,IAAI,EAAEvE,EAAE,CAACmE;IAAX,CAH7B,CAAP;EAG2D,CAtBrG;AAAA;;AAuBA,MAAM+L,UAAN,SAAyBhL,SAAzB,CAAmC;EAC/B9B,WAAW,GAAG;IACV,MAAM,GAAG+M,SAAT;IACA,KAAKC,gBAAL,GAAwB,KAAxB;IACA,KAAKC,YAAL,GAAoB,CAApB;IACA,KAAKC,eAAL,GAAuB,CAAvB;EACH;EACD;;;EACmB,IAAfC,eAAe,GAAG;IAClB,OAAO,KAAKH,gBAAZ;EACH;;EACkB,IAAfG,eAAe,CAACnI,KAAD,EAAQ;IACvB,KAAKgI,gBAAL,GAAwBjP,qBAAqB,CAACiH,KAAD,CAA7C;EACH;EACD;AACJ;AACA;AACA;;;EACmB,IAAXoI,WAAW,GAAG;IACd,OAAO,KAAKH,YAAZ;EACH;;EACc,IAAXG,WAAW,CAACpI,KAAD,EAAQ;IACnB,KAAKiI,YAAL,GAAoBjP,oBAAoB,CAACgH,KAAD,CAAxC;EACH;EACD;AACJ;AACA;AACA;;;EACsB,IAAdqI,cAAc,GAAG;IACjB,OAAO,KAAKH,eAAZ;EACH;;EACiB,IAAdG,cAAc,CAACrI,KAAD,EAAQ;IACtB,KAAKkI,eAAL,GAAuBlP,oBAAoB,CAACgH,KAAD,CAA3C;EACH;;AAjC8B;;AAmCnC8H,UAAU,CAACpM,IAAX;EAAA;EAAA;IAAA,8DA32BmG9D,EA22BnG,uBAAuGkQ,UAAvG,SAAuGA,UAAvG;EAAA;AAAA;;AACAA,UAAU,CAAC9L,IAAX,kBA52BmGpE,EA42BnG;EAAA,MAA2FkQ,UAA3F;EAAA;EAAA,wBAAgR,IAAhR;EAAA;EAAA;IAAA;MA52BmGlQ,EA42BnG;MA52BmGA,EA42BnG;MA52BmGA,EA42BnG;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WA52BmGA,EA42BnG;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MA52BmGA,EA42BnG;MA52BmGA,EA42BotB,+BAAvzB;MA52BmGA,EA42B2xB,gBAA93B;MA52BmGA,EA42BwzB,eAA35B;IAAA;EAAA;EAAA,eAAk9BN,EAAE,CAACC,aAAr9B;EAAA;EAAA;IAAA,WAAkiC,CAAC8C,mBAAmB,CAACC,eAArB;EAAliC;EAAA;AAAA;;AACA;EAAA,mDA72BmG1C,EA62BnG,mBAA2FkQ,UAA3F,EAAmH,CAAC;IACxG3L,IAAI,EAAEpE,SADkG;IAExGqE,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,aAAZ;MAA2ByH,QAAQ,EAAE,YAArC;MAAmDC,UAAU,EAAE,CAAC1J,mBAAmB,CAACC,eAArB,CAA/D;MAAsGiC,IAAI,EAAE;QACvG,SAAS,wBAD8F;QAEvG,YAAY,IAF2F;QAGvG;QACA,gBAAgB,MAJuF;QAKvG,0BAA0B,oBAL6E;QAMvG,2BAA2B,iBAN4E;QAOvG,2BAA2B,iBAP4E;QAQvG,2BAA2B,iBAR4E;QASvG,6BAA6B,QAT0E;QAUvG,6BAA6B,iBAV0E;QAWvG,kBAAkB,sCAXqF;QAYvG,qBAAqB;MAZkF,CAA5G;MAaIC,eAAe,EAAExE,uBAAuB,CAACyE,MAb7C;MAaqDC,aAAa,EAAEzE,iBAAiB,CAAC0E,IAbtF;MAa4FL,QAAQ,EAAE;IAbtG,CAAD;EAFkG,CAAD,CAAnH,QAgB4B;IAAE6L,eAAe,EAAE,CAAC;MAChChM,IAAI,EAAE9D;IAD0B,CAAD,CAAnB;IAEZ+P,WAAW,EAAE,CAAC;MACdjM,IAAI,EAAE9D;IADQ,CAAD,CAFD;IAIZgQ,cAAc,EAAE,CAAC;MACjBlM,IAAI,EAAE9D;IADW,CAAD;EAJJ,CAhB5B;AAAA;;AAuBA,MAAMwP,mBAAN,SAAkCjM,kBAAlC,CAAqD;;AAErDiM,mBAAmB,CAACnM,IAApB;EAAA;EAAA;IAAA,gFAt4BmG9D,EAs4BnG,uBAAgHiQ,mBAAhH,SAAgHA,mBAAhH;EAAA;AAAA;;AACAA,mBAAmB,CAAC7L,IAApB,kBAv4BmGpE,EAu4BnG;EAAA,MAAoGiQ,mBAApG;EAAA;EAAA;IAAA;MAv4BmGjQ,EAu4BnG,0BAKqE8P,iBALrE;MAv4BmG9P,EAu4BnG,0BAKuJkQ,UALvJ;IAAA;;IAAA;MAAA;;MAv4BmGlQ,EAu4BnG,qBAv4BmGA,EAu4BnG;MAv4BmGA,EAu4BnG,qBAv4BmGA,EAu4BnG;IAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAv4BmGA,EAu4BnG;IAAA;EAAA;EAAA;EAAA,WAv4BmGA,EAu4BnG,oBAAoU,CAC5T;IACIqE,OAAO,EAAEnB,oBADb;IAEIoB,WAAW,EAAE2L;EAFjB,CAD4T,CAApU,GAv4BmGjQ,EAu4BnG;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAv4BmGA,EAu4BnG;MAv4BmGA,EA44ByK,kEAL5Q;MAv4BmGA,EA44BqU,gBALxa;MAv4BmGA,EA44ByX,mBAL5d;MAv4BmGA,EA44Bqb,kGALxhB;IAAA;;IAAA;MAv4BmGA,EA44B8O,oCALjV;MAv4BmGA,EA44B2c,aAL9iB;MAv4BmGA,EA44B2c,kCAL9iB;IAAA;EAAA;EAAA,eAKo3FH,EAAE,CAAC8P,IALv3F,EAKw9FG,iBALx9F;EAAA;EAAA;EAAA;AAAA;;AAMA;EAAA,mDA74BmG9P,EA64BnG,mBAA2FiQ,mBAA3F,EAA4H,CAAC;IACjH1L,IAAI,EAAEpE,SAD2G;IAEjHqE,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,uBAAZ;MAAqCyH,QAAQ,EAAE,qBAA/C;MAAsEvH,IAAI,EAAE;QACvE,SAAS,4CAD8D;QAEvE,kDAAkD;MAFqB,CAA5E;MAGIC,eAAe,EAAExE,uBAAuB,CAACyE,MAH7C;MAGqDC,aAAa,EAAEzE,iBAAiB,CAAC0E,IAHtF;MAG4FC,SAAS,EAAE,CAClG;QACIX,OAAO,EAAEnB,oBADb;QAEIoB,WAAW,EAAE2L;MAFjB,CADkG,CAHvG;MAQIvL,QAAQ,EAAE,8WARd;MAQ8XkL,MAAM,EAAE,CAAC,msEAAD;IARtY,CAAD;EAF2G,CAAD,CAA5H,QAW4B;IAAE/B,WAAW,EAAE,CAAC;MAC5BtJ,IAAI,EAAE1D,eADsB;MAE5B2D,IAAI,EAAE,CAAC0L,UAAD,EAAa;QACX;QACA;QACAL,WAAW,EAAE;MAHF,CAAb;IAFsB,CAAD,CAAf;IAOZzD,QAAQ,EAAE,CAAC;MACX7H,IAAI,EAAEzD,YADK;MAEX0D,IAAI,EAAE,CAACsL,iBAAD;IAFK,CAAD;EAPE,CAX5B;AAAA;AAuBA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMY,gBAAN,CAAuB;;AAEvBA,gBAAgB,CAAC5M,IAAjB;EAAA,iBAA6G4M,gBAA7G;AAAA;;AACAA,gBAAgB,CAACC,IAAjB,kBA96BmG3Q,EA86BnG;EAAA,MAA8G0Q;AAA9G;AAaAA,gBAAgB,CAACE,IAAjB,kBA37BmG5Q,EA27BnG;EAAA,UAA0ID,YAA1I,EAAwJiB,eAAxJ,EAAyKpB,mBAAzK,EAA8LA,mBAA9L,EACQoB,eADR;AAAA;;AAEA;EAAA,mDA77BmGhB,EA67BnG,mBAA2F0Q,gBAA3F,EAAyH,CAAC;IAC9GnM,IAAI,EAAExD,QADwG;IAE9GyD,IAAI,EAAE,CAAC;MACCqM,OAAO,EAAE,CAAC9Q,YAAD,EAAeiB,eAAf,EAAgCpB,mBAAhC,CADV;MAECkR,OAAO,EAAE,CACLlR,mBADK,EAELoB,eAFK,EAGLkE,SAHK,EAILlB,kBAJK,EAKLb,gBALK,EAML+M,UANK,EAOLD,mBAPK,EAQLH,iBARK,CAFV;MAYCiB,YAAY,EAAE,CACV7L,SADU,EAEVlB,kBAFU,EAGVb,gBAHU,EAIV+M,UAJU,EAKVD,mBALU,EAMVH,iBANU;IAZf,CAAD;EAFwG,CAAD,CAAzH;AAAA;AAyBA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAAShN,2BAAT,EAAsCG,mCAAtC,EAA2EiC,SAA3E,EAAsFlB,kBAAtF,EAA0Gb,gBAA1G,EAA4H+M,UAA5H,EAAwID,mBAAxI,EAA6JH,iBAA7J,EAAgLY,gBAAhL,EAAkMjO,mBAAlM,EAAuNE,6BAAvN", "ignoreList": []}, "metadata": {}, "sourceType": "module"}