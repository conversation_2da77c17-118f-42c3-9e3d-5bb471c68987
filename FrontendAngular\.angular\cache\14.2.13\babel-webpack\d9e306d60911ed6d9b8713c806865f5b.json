{"ast": null, "code": "import { Observable } from '../Observable';\nimport { Subscription } from '../Subscription';\nimport { refCount as higherOrderRefCount } from '../operators/refCount';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { hasLift } from '../util/lift';\nexport class ConnectableObservable extends Observable {\n  constructor(source, subjectFactory) {\n    super();\n    this.source = source;\n    this.subjectFactory = subjectFactory;\n    this._subject = null;\n    this._refCount = 0;\n    this._connection = null;\n\n    if (hasLift(source)) {\n      this.lift = source.lift;\n    }\n  }\n\n  _subscribe(subscriber) {\n    return this.getSubject().subscribe(subscriber);\n  }\n\n  getSubject() {\n    const subject = this._subject;\n\n    if (!subject || subject.isStopped) {\n      this._subject = this.subjectFactory();\n    }\n\n    return this._subject;\n  }\n\n  _teardown() {\n    this._refCount = 0;\n    const {\n      _connection\n    } = this;\n    this._subject = this._connection = null;\n    _connection === null || _connection === void 0 ? void 0 : _connection.unsubscribe();\n  }\n\n  connect() {\n    let connection = this._connection;\n\n    if (!connection) {\n      connection = this._connection = new Subscription();\n      const subject = this.getSubject();\n      connection.add(this.source.subscribe(createOperatorSubscriber(subject, undefined, () => {\n        this._teardown();\n\n        subject.complete();\n      }, err => {\n        this._teardown();\n\n        subject.error(err);\n      }, () => this._teardown())));\n\n      if (connection.closed) {\n        this._connection = null;\n        connection = Subscription.EMPTY;\n      }\n    }\n\n    return connection;\n  }\n\n  refCount() {\n    return higherOrderRefCount()(this);\n  }\n\n}", "map": {"version": 3, "names": ["Observable", "Subscription", "refCount", "higherOrderRefCount", "createOperatorSubscriber", "hasLift", "ConnectableObservable", "constructor", "source", "subjectFactory", "_subject", "_refCount", "_connection", "lift", "_subscribe", "subscriber", "getSubject", "subscribe", "subject", "isStopped", "_teardown", "unsubscribe", "connect", "connection", "add", "undefined", "complete", "err", "error", "closed", "EMPTY"], "sources": ["R:/chateye/Frontend/chateye-angular/node_modules/rxjs/dist/esm/internal/observable/ConnectableObservable.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { Subscription } from '../Subscription';\nimport { refCount as higherOrderRefCount } from '../operators/refCount';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { hasLift } from '../util/lift';\nexport class ConnectableObservable extends Observable {\n    constructor(source, subjectFactory) {\n        super();\n        this.source = source;\n        this.subjectFactory = subjectFactory;\n        this._subject = null;\n        this._refCount = 0;\n        this._connection = null;\n        if (hasLift(source)) {\n            this.lift = source.lift;\n        }\n    }\n    _subscribe(subscriber) {\n        return this.getSubject().subscribe(subscriber);\n    }\n    getSubject() {\n        const subject = this._subject;\n        if (!subject || subject.isStopped) {\n            this._subject = this.subjectFactory();\n        }\n        return this._subject;\n    }\n    _teardown() {\n        this._refCount = 0;\n        const { _connection } = this;\n        this._subject = this._connection = null;\n        _connection === null || _connection === void 0 ? void 0 : _connection.unsubscribe();\n    }\n    connect() {\n        let connection = this._connection;\n        if (!connection) {\n            connection = this._connection = new Subscription();\n            const subject = this.getSubject();\n            connection.add(this.source.subscribe(createOperatorSubscriber(subject, undefined, () => {\n                this._teardown();\n                subject.complete();\n            }, (err) => {\n                this._teardown();\n                subject.error(err);\n            }, () => this._teardown())));\n            if (connection.closed) {\n                this._connection = null;\n                connection = Subscription.EMPTY;\n            }\n        }\n        return connection;\n    }\n    refCount() {\n        return higherOrderRefCount()(this);\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,QAAQ,IAAIC,mBAArB,QAAgD,uBAAhD;AACA,SAASC,wBAAT,QAAyC,iCAAzC;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,OAAO,MAAMC,qBAAN,SAAoCN,UAApC,CAA+C;EAClDO,WAAW,CAACC,MAAD,EAASC,cAAT,EAAyB;IAChC;IACA,KAAKD,MAAL,GAAcA,MAAd;IACA,KAAKC,cAAL,GAAsBA,cAAtB;IACA,KAAKC,QAAL,GAAgB,IAAhB;IACA,KAAKC,SAAL,GAAiB,CAAjB;IACA,KAAKC,WAAL,GAAmB,IAAnB;;IACA,IAAIP,OAAO,CAACG,MAAD,CAAX,EAAqB;MACjB,KAAKK,IAAL,GAAYL,MAAM,CAACK,IAAnB;IACH;EACJ;;EACDC,UAAU,CAACC,UAAD,EAAa;IACnB,OAAO,KAAKC,UAAL,GAAkBC,SAAlB,CAA4BF,UAA5B,CAAP;EACH;;EACDC,UAAU,GAAG;IACT,MAAME,OAAO,GAAG,KAAKR,QAArB;;IACA,IAAI,CAACQ,OAAD,IAAYA,OAAO,CAACC,SAAxB,EAAmC;MAC/B,KAAKT,QAAL,GAAgB,KAAKD,cAAL,EAAhB;IACH;;IACD,OAAO,KAAKC,QAAZ;EACH;;EACDU,SAAS,GAAG;IACR,KAAKT,SAAL,GAAiB,CAAjB;IACA,MAAM;MAAEC;IAAF,IAAkB,IAAxB;IACA,KAAKF,QAAL,GAAgB,KAAKE,WAAL,GAAmB,IAAnC;IACAA,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,WAAW,CAACS,WAAZ,EAA1D;EACH;;EACDC,OAAO,GAAG;IACN,IAAIC,UAAU,GAAG,KAAKX,WAAtB;;IACA,IAAI,CAACW,UAAL,EAAiB;MACbA,UAAU,GAAG,KAAKX,WAAL,GAAmB,IAAIX,YAAJ,EAAhC;MACA,MAAMiB,OAAO,GAAG,KAAKF,UAAL,EAAhB;MACAO,UAAU,CAACC,GAAX,CAAe,KAAKhB,MAAL,CAAYS,SAAZ,CAAsBb,wBAAwB,CAACc,OAAD,EAAUO,SAAV,EAAqB,MAAM;QACpF,KAAKL,SAAL;;QACAF,OAAO,CAACQ,QAAR;MACH,CAH4D,EAGzDC,GAAD,IAAS;QACR,KAAKP,SAAL;;QACAF,OAAO,CAACU,KAAR,CAAcD,GAAd;MACH,CAN4D,EAM1D,MAAM,KAAKP,SAAL,EANoD,CAA9C,CAAf;;MAOA,IAAIG,UAAU,CAACM,MAAf,EAAuB;QACnB,KAAKjB,WAAL,GAAmB,IAAnB;QACAW,UAAU,GAAGtB,YAAY,CAAC6B,KAA1B;MACH;IACJ;;IACD,OAAOP,UAAP;EACH;;EACDrB,QAAQ,GAAG;IACP,OAAOC,mBAAmB,GAAG,IAAH,CAA1B;EACH;;AAjDiD", "ignoreList": []}, "metadata": {}, "sourceType": "module"}