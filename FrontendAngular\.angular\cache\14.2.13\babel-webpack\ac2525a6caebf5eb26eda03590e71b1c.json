{"ast": null, "code": "import { Event<PERSON><PERSON><PERSON>, ElementRef, SimpleChang<PERSON> } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/divider\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/tooltip\";\nimport * as i6 from \"@angular/material/progress-spinner\";\nimport * as i7 from \"../message/message.component\";\nconst _c0 = [\"scrollContainer\"];\nconst _c1 = [\"scrollAnchor\"];\n\nfunction MessageListComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"mat-spinner\", 5);\n    i0.ɵɵelementStart(2, \"p\", 6);\n    i0.ɵɵtext(3, \"Loading messages...\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction MessageListComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"mat-icon\", 8);\n    i0.ɵɵtext(2, \"chat_bubble_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 9);\n    i0.ɵɵtext(4, \"No messages yet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 10);\n    i0.ɵɵtext(6, \"Start the conversation!\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction MessageListComponent_div_3_div_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵelement(1, \"mat-divider\");\n    i0.ɵɵelementStart(2, \"span\", 24);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"mat-divider\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const message_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r9.formatDate(message_r7.timestamp));\n  }\n}\n\nfunction MessageListComponent_div_3_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵtemplate(1, MessageListComponent_div_3_div_4_div_1_Template, 5, 1, \"div\", 20);\n    i0.ɵɵelementStart(2, \"div\", 21)(3, \"app-message\", 22);\n    i0.ɵɵlistener(\"onReply\", function MessageListComponent_div_3_div_4_Template_app_message_onReply_3_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.onMessageReply($event));\n    })(\"onAddReaction\", function MessageListComponent_div_3_div_4_Template_app_message_onAddReaction_3_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r13.onMessageAddReaction($event));\n    })(\"onRemoveReaction\", function MessageListComponent_div_3_div_4_Template_app_message_onRemoveReaction_3_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.onMessageRemoveReaction($event));\n    })(\"onEdit\", function MessageListComponent_div_3_div_4_Template_app_message_onEdit_3_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.onMessageEdit($event));\n    })(\"onDelete\", function MessageListComponent_div_3_div_4_Template_app_message_onDelete_3_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.onMessageDelete($event));\n    })(\"onMessageClick\", function MessageListComponent_div_3_div_4_Template_app_message_onMessageClick_3_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.onMessageClickHandler($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const message_r7 = ctx.$implicit;\n    const i_r8 = ctx.index;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"own-message\", ctx_r4.isOwnMessage(message_r7));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.shouldShowDateSeparator(message_r7, i_r8));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"message\", message_r7)(\"currentUser\", ctx_r4.currentUser)(\"highlightedMessageId\", ctx_r4.highlightedMessageId);\n  }\n}\n\nfunction MessageListComponent_div_3_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function MessageListComponent_div_3_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r18.scrollToBottom());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"keyboard_arrow_down\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction MessageListComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12, 13);\n    i0.ɵɵlistener(\"scroll\", function MessageListComponent_div_3_Template_div_scroll_1_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.onScroll($event));\n    });\n    i0.ɵɵelementStart(3, \"div\", 14);\n    i0.ɵɵtemplate(4, MessageListComponent_div_3_div_4_Template, 4, 6, \"div\", 15);\n    i0.ɵɵelement(5, \"div\", 16, 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, MessageListComponent_div_3_button_7_Template, 3, 0, \"button\", 18);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.messages)(\"ngForTrackBy\", ctx_r2.trackByMessageId);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showScrollButton);\n  }\n}\n\nexport let MessageListComponent = /*#__PURE__*/(() => {\n  class MessageListComponent {\n    constructor() {\n      this.messages = [];\n      this.currentUser = null;\n      this.loading = false;\n      this.highlightedMessageId = null;\n      this.onReply = new EventEmitter();\n      this.onAddReaction = new EventEmitter();\n      this.onRemoveReaction = new EventEmitter();\n      this.onEdit = new EventEmitter();\n      this.onDelete = new EventEmitter();\n      this.onMessageClick = new EventEmitter();\n      this.showScrollButton = false;\n      this.isNearBottom = true;\n    }\n\n    ngAfterViewInit() {\n      this.scrollToBottom(false);\n      this.setupScrollListener();\n    }\n\n    ngOnChanges(changes) {\n      if (changes['messages'] && !changes['messages'].firstChange) {\n        // Auto-scroll only if user is near bottom\n        if (this.isNearBottom) {\n          setTimeout(() => this.scrollToBottom(), 100);\n        }\n      }\n    }\n\n    ngOnDestroy() {\n      if (this.scrollTimeout) {\n        clearTimeout(this.scrollTimeout);\n      }\n    }\n\n    setupScrollListener() {\n      if (this.scrollContainer) {\n        this.scrollContainer.nativeElement.addEventListener('scroll', () => {\n          this.checkScrollPosition();\n        });\n      }\n    }\n\n    onScroll(event) {\n      this.checkScrollPosition();\n    }\n\n    checkScrollPosition() {\n      if (!this.scrollContainer) return;\n      const element = this.scrollContainer.nativeElement;\n      const threshold = 100;\n      const isNearBottom = element.scrollTop + element.clientHeight >= element.scrollHeight - threshold;\n      this.isNearBottom = isNearBottom;\n      this.showScrollButton = !isNearBottom && this.messages.length > 0;\n    }\n\n    scrollToBottom(smooth = true) {\n      if (this.scrollAnchor) {\n        this.scrollAnchor.nativeElement.scrollIntoView({\n          behavior: smooth ? 'smooth' : 'auto',\n          block: 'end'\n        });\n      }\n    }\n\n    shouldShowDateSeparator(message, index) {\n      if (index === 0) return true;\n      const prevMessage = this.messages[index - 1];\n      return new Date(message.timestamp).toDateString() !== new Date(prevMessage.timestamp).toDateString();\n    }\n\n    formatDate(timestamp) {\n      const date = new Date(timestamp);\n      const today = new Date();\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n\n      if (date.toDateString() === today.toDateString()) {\n        return 'Today';\n      } else if (date.toDateString() === yesterday.toDateString()) {\n        return 'Yesterday';\n      } else {\n        return date.toLocaleDateString('en-US', {\n          weekday: 'long',\n          year: 'numeric',\n          month: 'long',\n          day: 'numeric'\n        });\n      }\n    }\n\n    isOwnMessage(message) {\n      return message.username === this.currentUser;\n    }\n\n    trackByMessageId(index, message) {\n      return message.id || index.toString();\n    }\n\n    onMessageReply(message) {\n      this.onReply.emit(message);\n    }\n\n    onMessageAddReaction(event) {\n      this.onAddReaction.emit(event);\n    }\n\n    onMessageRemoveReaction(data) {\n      this.onRemoveReaction.emit(data);\n    }\n\n    onMessageEdit(message) {\n      this.onEdit.emit(message);\n    }\n\n    onMessageDelete(message) {\n      this.onDelete.emit(message);\n    }\n\n    onMessageClickHandler(message) {\n      this.onMessageClick.emit(message);\n    }\n\n  }\n\n  MessageListComponent.ɵfac = function MessageListComponent_Factory(t) {\n    return new (t || MessageListComponent)();\n  };\n\n  MessageListComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: MessageListComponent,\n    selectors: [[\"app-message-list\"]],\n    viewQuery: function MessageListComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n      }\n\n      if (rf & 2) {\n        let _t;\n\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollContainer = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollAnchor = _t.first);\n      }\n    },\n    inputs: {\n      messages: \"messages\",\n      currentUser: \"currentUser\",\n      loading: \"loading\",\n      highlightedMessageId: \"highlightedMessageId\"\n    },\n    outputs: {\n      onReply: \"onReply\",\n      onAddReaction: \"onAddReaction\",\n      onRemoveReaction: \"onRemoveReaction\",\n      onEdit: \"onEdit\",\n      onDelete: \"onDelete\",\n      onMessageClick: \"onMessageClick\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 4,\n    vars: 3,\n    consts: [[1, \"messages-container\"], [\"class\", \"loading-state\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"messages-wrapper\", 4, \"ngIf\"], [1, \"loading-state\"], [\"diameter\", \"40\"], [1, \"loading-text\"], [1, \"empty-state\"], [1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-description\"], [1, \"messages-wrapper\"], [1, \"scroll-container\", 3, \"scroll\"], [\"scrollContainer\", \"\"], [1, \"messages-list\"], [\"class\", \"message-wrapper\", 3, \"own-message\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"scroll-anchor\"], [\"scrollAnchor\", \"\"], [\"mat-fab\", \"\", \"class\", \"scroll-button\", \"matTooltip\", \"Scroll to bottom\", 3, \"click\", 4, \"ngIf\"], [1, \"message-wrapper\"], [\"class\", \"date-separator\", 4, \"ngIf\"], [1, \"message-item\"], [3, \"message\", \"currentUser\", \"highlightedMessageId\", \"onReply\", \"onAddReaction\", \"onRemoveReaction\", \"onEdit\", \"onDelete\", \"onMessageClick\"], [1, \"date-separator\"], [1, \"date-text\"], [\"mat-fab\", \"\", \"matTooltip\", \"Scroll to bottom\", 1, \"scroll-button\", 3, \"click\"]],\n    template: function MessageListComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵtemplate(1, MessageListComponent_div_1_Template, 4, 0, \"div\", 1);\n        i0.ɵɵtemplate(2, MessageListComponent_div_2_Template, 7, 0, \"div\", 2);\n        i0.ɵɵtemplate(3, MessageListComponent_div_3_Template, 8, 3, \"div\", 3);\n        i0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.messages.length === 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.messages.length > 0);\n      }\n    },\n    dependencies: [i1.NgForOf, i1.NgIf, i2.MatDivider, i3.MatButton, i4.MatIcon, i5.MatTooltip, i6.MatProgressSpinner, i7.MessageComponent],\n    styles: [\".messages-container[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;background:#f8f9fa;position:relative;overflow:hidden}.loading-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;padding:2rem}.loading-text[_ngcontent-%COMP%]{margin-top:1rem;color:#666;font-size:1rem;font-weight:500}.empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;padding:3rem 2rem;text-align:center}.empty-icon[_ngcontent-%COMP%]{font-size:4rem;width:4rem;height:4rem;margin-bottom:1.5rem;opacity:.6;color:#3f51b5;animation:float 3s ease-in-out infinite}.empty-title[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:500;color:#333;margin-bottom:.75rem}.empty-description[_ngcontent-%COMP%]{color:#666;font-size:1rem;font-weight:400}.messages-wrapper[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;position:relative;min-height:0}.scroll-container[_ngcontent-%COMP%]{flex:1;overflow-y:auto;overflow-x:hidden;padding:1rem;scroll-behavior:smooth;scrollbar-width:thin;scrollbar-color:rgba(63,81,181,.3) transparent}.scroll-container[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px}.scroll-container[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:transparent}.scroll-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#3f51b5;border-radius:3px;-webkit-transition:all .3s ease;transition:all .3s ease}.scroll-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#303f9f}.messages-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.75rem;max-width:100%}.message-wrapper[_ngcontent-%COMP%]{display:flex;flex-direction:column;animation:messageSlide .4s ease}.message-wrapper.own-message[_ngcontent-%COMP%]{align-items:flex-end}.date-separator[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;margin:1.5rem 0;position:relative}.date-text[_ngcontent-%COMP%]{background:#3f51b5;color:#fff;padding:.5rem 1rem;border-radius:16px;font-size:.75rem;font-weight:500;text-transform:uppercase;letter-spacing:.05em;box-shadow:0 2px 8px #3f51b54d;margin:0 1rem}.message-item[_ngcontent-%COMP%]{display:flex;max-width:70%;transition:all .3s ease}.message-wrapper.own-message[_ngcontent-%COMP%]   .message-item[_ngcontent-%COMP%]{max-width:70%}.scroll-anchor[_ngcontent-%COMP%]{height:1px;width:100%}.scroll-button[_ngcontent-%COMP%]{position:absolute;bottom:1rem;right:1rem;z-index:20}.scroll-button[_ngcontent-%COMP%]:hover{transform:translateY(-2px)}@keyframes float{0%,to{transform:translateY(0)}50%{transform:translateY(-10px)}}@keyframes messageSlide{0%{opacity:0;transform:translateY(20px) scale(.95)}to{opacity:1;transform:translateY(0) scale(1)}}@media (max-width: 1024px){.scroll-container[_ngcontent-%COMP%]{padding:.875rem}.messages-list[_ngcontent-%COMP%]{gap:.625rem}.message-item[_ngcontent-%COMP%]{max-width:75%}}@media (max-width: 768px){.scroll-container[_ngcontent-%COMP%]{padding:.75rem}.messages-list[_ngcontent-%COMP%]{gap:.5rem}.message-item[_ngcontent-%COMP%]{max-width:85%}.date-text[_ngcontent-%COMP%]{font-size:.7rem;padding:.5rem .875rem}}@media (max-width: 480px){.scroll-container[_ngcontent-%COMP%]{padding:.5rem}.messages-list[_ngcontent-%COMP%]{gap:.375rem}.message-item[_ngcontent-%COMP%]{max-width:90%}.empty-icon[_ngcontent-%COMP%]{font-size:3rem;width:3rem;height:3rem}.empty-title[_ngcontent-%COMP%]{font-size:1.25rem}.empty-description[_ngcontent-%COMP%]{font-size:.9rem}}*[_ngcontent-%COMP%]{transition:all .3s ease}\"]\n  });\n  return MessageListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}