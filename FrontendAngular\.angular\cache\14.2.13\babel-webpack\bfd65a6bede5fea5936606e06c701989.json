{"ast": null, "code": "export function createObject(keys, values) {\n  return keys.reduce((result, key, i) => (result[key] = values[i], result), {});\n}", "map": {"version": 3, "names": ["createObject", "keys", "values", "reduce", "result", "key", "i"], "sources": ["R:/chateye/FrontendAngular/node_modules/rxjs/dist/esm/internal/util/createObject.js"], "sourcesContent": ["export function createObject(keys, values) {\n    return keys.reduce((result, key, i) => ((result[key] = values[i]), result), {});\n}\n"], "mappings": "AAAA,OAAO,SAASA,YAAT,CAAsBC,IAAtB,EAA4BC,MAA5B,EAAoC;EACvC,OAAOD,IAAI,CAACE,MAAL,CAAY,CAACC,MAAD,EAASC,GAAT,EAAcC,CAAd,MAAsBF,MAAM,CAACC,GAAD,CAAN,GAAcH,MAAM,CAACI,CAAD,CAArB,EAA2BF,MAAhD,CAAZ,EAAqE,EAArE,CAAP;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}