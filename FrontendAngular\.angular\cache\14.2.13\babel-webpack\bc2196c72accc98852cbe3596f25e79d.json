{"ast": null, "code": "import * as i7 from '@angular/cdk/a11y';\nimport { FocusKeyManager, A11yModule } from '@angular/cdk/a11y';\nimport * as i5 from '@angular/cdk/observers';\nimport { ObserversModule } from '@angular/cdk/observers';\nimport * as i2 from '@angular/cdk/portal';\nimport { CdkPortal, TemplatePortal, CdkPortalOutlet, PortalModule } from '@angular/cdk/portal';\nimport * as i1$2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Inject, Optional, TemplateRef, Component, ChangeDetectionStrategy, ViewEncapsulation, ContentChild, ViewChild, Input, forwardRef, EventEmitter, Output, ContentChildren, QueryList, Attribute, NgModule } from '@angular/core';\nimport * as i4 from '@angular/material/core';\nimport { mixinDisabled, mixinColor, mixinDisableRipple, mixinTabIndex, MAT_RIPPLE_GLOBAL_OPTIONS, RippleRenderer, MatCommonModule, MatRippleModule } from '@angular/material/core';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { take, startWith, distinctUntilChanged, takeUntil, switchMap, skip, filter } from 'rxjs/operators';\nimport { Subject, Subscription, fromEvent, of, merge, EMPTY, Observable, timer } from 'rxjs';\nimport * as i1 from '@angular/cdk/bidi';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i1$1 from '@angular/cdk/scrolling';\nimport * as i3 from '@angular/cdk/platform';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport { hasModifierKey, SPACE, ENTER } from '@angular/cdk/keycodes';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Injection token for the MatInkBar's Positioner. */\n\nfunction MatTab_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\n\nconst _c0 = [\"*\"];\n\nfunction MatTabBody_ng_template_2_Template(rf, ctx) {}\n\nconst _c1 = function (a0) {\n  return {\n    animationDuration: a0\n  };\n};\n\nconst _c2 = function (a0, a1) {\n  return {\n    value: a0,\n    params: a1\n  };\n};\n\nconst _c3 = [\"tabListContainer\"];\nconst _c4 = [\"tabList\"];\nconst _c5 = [\"tabListInner\"];\nconst _c6 = [\"nextPaginator\"];\nconst _c7 = [\"previousPaginator\"];\nconst _c8 = [\"tabBodyWrapper\"];\nconst _c9 = [\"tabHeader\"];\n\nfunction MatTabGroup_div_2_ng_template_2_ng_template_0_Template(rf, ctx) {}\n\nfunction MatTabGroup_div_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MatTabGroup_div_2_ng_template_2_ng_template_0_Template, 0, 0, \"ng-template\", 10);\n  }\n\n  if (rf & 2) {\n    const tab_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"cdkPortalOutlet\", tab_r4.templateLabel);\n  }\n}\n\nfunction MatTabGroup_div_2_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const tab_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtextInterpolate(tab_r4.textLabel);\n  }\n}\n\nfunction MatTabGroup_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵlistener(\"click\", function MatTabGroup_div_2_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r13);\n      const tab_r4 = restoredCtx.$implicit;\n      const i_r5 = restoredCtx.index;\n      const ctx_r12 = i0.ɵɵnextContext();\n\n      const _r0 = i0.ɵɵreference(1);\n\n      return i0.ɵɵresetView(ctx_r12._handleClick(tab_r4, _r0, i_r5));\n    })(\"cdkFocusChange\", function MatTabGroup_div_2_Template_div_cdkFocusChange_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r13);\n      const i_r5 = restoredCtx.index;\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14._tabFocusChanged($event, i_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 7);\n    i0.ɵɵtemplate(2, MatTabGroup_div_2_ng_template_2_Template, 1, 1, \"ng-template\", 8);\n    i0.ɵɵtemplate(3, MatTabGroup_div_2_ng_template_3_Template, 1, 1, \"ng-template\", null, 9, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const tab_r4 = ctx.$implicit;\n    const i_r5 = ctx.index;\n\n    const _r7 = i0.ɵɵreference(4);\n\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"mat-tab-label-active\", ctx_r1.selectedIndex === i_r5);\n    i0.ɵɵproperty(\"id\", ctx_r1._getTabLabelId(i_r5))(\"ngClass\", tab_r4.labelClass)(\"disabled\", tab_r4.disabled)(\"matRippleDisabled\", tab_r4.disabled || ctx_r1.disableRipple);\n    i0.ɵɵattribute(\"tabIndex\", ctx_r1._getTabIndex(tab_r4, i_r5))(\"aria-posinset\", i_r5 + 1)(\"aria-setsize\", ctx_r1._tabs.length)(\"aria-controls\", ctx_r1._getTabContentId(i_r5))(\"aria-selected\", ctx_r1.selectedIndex === i_r5)(\"aria-label\", tab_r4.ariaLabel || null)(\"aria-labelledby\", !tab_r4.ariaLabel && tab_r4.ariaLabelledby ? tab_r4.ariaLabelledby : null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", tab_r4.templateLabel)(\"ngIfElse\", _r7);\n  }\n}\n\nfunction MatTabGroup_mat_tab_body_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"mat-tab-body\", 11);\n    i0.ɵɵlistener(\"_onCentered\", function MatTabGroup_mat_tab_body_5_Template_mat_tab_body__onCentered_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17._removeTabBodyWrapperHeight());\n    })(\"_onCentering\", function MatTabGroup_mat_tab_body_5_Template_mat_tab_body__onCentering_0_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19._setTabBodyWrapperHeight($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const tab_r15 = ctx.$implicit;\n    const i_r16 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"mat-tab-body-active\", ctx_r3.selectedIndex === i_r16);\n    i0.ɵɵproperty(\"id\", ctx_r3._getTabContentId(i_r16))(\"ngClass\", tab_r15.bodyClass)(\"content\", tab_r15.content)(\"position\", tab_r15.position)(\"origin\", tab_r15.origin)(\"animationDuration\", ctx_r3.animationDuration)(\"preserveContent\", ctx_r3.preserveContent);\n    i0.ɵɵattribute(\"tabindex\", ctx_r3.contentTabIndex != null && ctx_r3.selectedIndex === i_r16 ? ctx_r3.contentTabIndex : null)(\"aria-labelledby\", ctx_r3._getTabLabelId(i_r16));\n  }\n}\n\nconst _c10 = [\"mat-tab-nav-bar\", \"\"];\n\nconst _MAT_INK_BAR_POSITIONER = new InjectionToken('MatInkBarPositioner', {\n  providedIn: 'root',\n  factory: _MAT_INK_BAR_POSITIONER_FACTORY\n});\n/**\n * The default positioner function for the MatInkBar.\n * @docs-private\n */\n\n\nfunction _MAT_INK_BAR_POSITIONER_FACTORY() {\n  const method = element => ({\n    left: element ? (element.offsetLeft || 0) + 'px' : '0',\n    width: element ? (element.offsetWidth || 0) + 'px' : '0'\n  });\n\n  return method;\n}\n/**\n * The ink-bar is used to display and animate the line underneath the current active tab label.\n * @docs-private\n */\n\n\nclass MatInkBar {\n  constructor(_elementRef, _ngZone, _inkBarPositioner, _animationMode) {\n    this._elementRef = _elementRef;\n    this._ngZone = _ngZone;\n    this._inkBarPositioner = _inkBarPositioner;\n    this._animationMode = _animationMode;\n  }\n  /**\n   * Calculates the styles from the provided element in order to align the ink-bar to that element.\n   * Shows the ink bar if previously set as hidden.\n   * @param element\n   */\n\n\n  alignToElement(element) {\n    this.show(); // `onStable` might not run for a while if the zone has already stabilized.\n    // Wrap the call in `NgZone.run` to ensure that it runs relatively soon.\n\n    this._ngZone.run(() => {\n      this._ngZone.onStable.pipe(take(1)).subscribe(() => {\n        const positions = this._inkBarPositioner(element);\n\n        const inkBar = this._elementRef.nativeElement;\n        inkBar.style.left = positions.left;\n        inkBar.style.width = positions.width;\n      });\n    });\n  }\n  /** Shows the ink bar. */\n\n\n  show() {\n    this._elementRef.nativeElement.style.visibility = 'visible';\n  }\n  /** Hides the ink bar. */\n\n\n  hide() {\n    this._elementRef.nativeElement.style.visibility = 'hidden';\n  }\n\n}\n\nMatInkBar.ɵfac = function MatInkBar_Factory(t) {\n  return new (t || MatInkBar)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(_MAT_INK_BAR_POSITIONER), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n};\n\nMatInkBar.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatInkBar,\n  selectors: [[\"mat-ink-bar\"]],\n  hostAttrs: [1, \"mat-ink-bar\"],\n  hostVars: 2,\n  hostBindings: function MatInkBar_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n    }\n  }\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatInkBar, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-ink-bar',\n      host: {\n        'class': 'mat-ink-bar',\n        '[class._mat-animation-noopable]': `_animationMode === 'NoopAnimations'`\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [_MAT_INK_BAR_POSITIONER]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Injection token that can be used to reference instances of `MatTabContent`. It serves as\n * alternative token to the actual `MatTabContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\n\n\nconst MAT_TAB_CONTENT = new InjectionToken('MatTabContent');\n/** Decorates the `ng-template` tags and reads out the template from it. */\n\nclass MatTabContent {\n  constructor(\n  /** Content for the tab. */\n  template) {\n    this.template = template;\n  }\n\n}\n\nMatTabContent.ɵfac = function MatTabContent_Factory(t) {\n  return new (t || MatTabContent)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n};\n\nMatTabContent.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatTabContent,\n  selectors: [[\"\", \"matTabContent\", \"\"]],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_TAB_CONTENT,\n    useExisting: MatTabContent\n  }])]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabContent, [{\n    type: Directive,\n    args: [{\n      selector: '[matTabContent]',\n      providers: [{\n        provide: MAT_TAB_CONTENT,\n        useExisting: MatTabContent\n      }]\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Injection token that can be used to reference instances of `MatTabLabel`. It serves as\n * alternative token to the actual `MatTabLabel` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\n\n\nconst MAT_TAB_LABEL = new InjectionToken('MatTabLabel');\n/**\n * Used to provide a tab label to a tab without causing a circular dependency.\n * @docs-private\n */\n\nconst MAT_TAB = new InjectionToken('MAT_TAB');\n/** Used to flag tab labels for use with the portal directive */\n\nclass MatTabLabel extends CdkPortal {\n  constructor(templateRef, viewContainerRef, _closestTab) {\n    super(templateRef, viewContainerRef);\n    this._closestTab = _closestTab;\n  }\n\n}\n\nMatTabLabel.ɵfac = function MatTabLabel_Factory(t) {\n  return new (t || MatTabLabel)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(MAT_TAB, 8));\n};\n\nMatTabLabel.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatTabLabel,\n  selectors: [[\"\", \"mat-tab-label\", \"\"], [\"\", \"matTabLabel\", \"\"]],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_TAB_LABEL,\n    useExisting: MatTabLabel\n  }]), i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabLabel, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-tab-label], [matTabLabel]',\n      providers: [{\n        provide: MAT_TAB_LABEL,\n        useExisting: MatTabLabel\n      }]\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_TAB]\n      }, {\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to MatTab.\n\n/** @docs-private */\n\n\nconst _MatTabBase = mixinDisabled(class {});\n/**\n * Used to provide a tab group to a tab without causing a circular dependency.\n * @docs-private\n */\n\n\nconst MAT_TAB_GROUP = new InjectionToken('MAT_TAB_GROUP');\n\nclass MatTab extends _MatTabBase {\n  constructor(_viewContainerRef, _closestTabGroup) {\n    super();\n    this._viewContainerRef = _viewContainerRef;\n    this._closestTabGroup = _closestTabGroup;\n    /** Plain text label for the tab, used when there is no template label. */\n\n    this.textLabel = '';\n    /** Portal that will be the hosted content of the tab */\n\n    this._contentPortal = null;\n    /** Emits whenever the internal state of the tab changes. */\n\n    this._stateChanges = new Subject();\n    /**\n     * The relatively indexed position where 0 represents the center, negative is left, and positive\n     * represents the right.\n     */\n\n    this.position = null;\n    /**\n     * The initial relatively index origin of the tab if it was created and selected after there\n     * was already a selected tab. Provides context of what position the tab should originate from.\n     */\n\n    this.origin = null;\n    /**\n     * Whether the tab is currently active.\n     */\n\n    this.isActive = false;\n  }\n  /** Content for the tab label given by `<ng-template mat-tab-label>`. */\n\n\n  get templateLabel() {\n    return this._templateLabel;\n  }\n\n  set templateLabel(value) {\n    this._setTemplateLabelInput(value);\n  }\n  /** @docs-private */\n\n\n  get content() {\n    return this._contentPortal;\n  }\n\n  ngOnChanges(changes) {\n    if (changes.hasOwnProperty('textLabel') || changes.hasOwnProperty('disabled')) {\n      this._stateChanges.next();\n    }\n  }\n\n  ngOnDestroy() {\n    this._stateChanges.complete();\n  }\n\n  ngOnInit() {\n    this._contentPortal = new TemplatePortal(this._explicitContent || this._implicitContent, this._viewContainerRef);\n  }\n  /**\n   * This has been extracted to a util because of TS 4 and VE.\n   * View Engine doesn't support property rename inheritance.\n   * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n   * @docs-private\n   */\n\n\n  _setTemplateLabelInput(value) {\n    // Only update the label if the query managed to find one. This works around an issue where a\n    // user may have manually set `templateLabel` during creation mode, which would then get\n    // clobbered by `undefined` when the query resolves. Also note that we check that the closest\n    // tab matches the current one so that we don't pick up labels from nested tabs.\n    if (value && value._closestTab === this) {\n      this._templateLabel = value;\n    }\n  }\n\n}\n\nMatTab.ɵfac = function MatTab_Factory(t) {\n  return new (t || MatTab)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(MAT_TAB_GROUP, 8));\n};\n\nMatTab.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatTab,\n  selectors: [[\"mat-tab\"]],\n  contentQueries: function MatTab_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MAT_TAB_LABEL, 5);\n      i0.ɵɵcontentQuery(dirIndex, MAT_TAB_CONTENT, 7, TemplateRef);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateLabel = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._explicitContent = _t.first);\n    }\n  },\n  viewQuery: function MatTab_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(TemplateRef, 7);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._implicitContent = _t.first);\n    }\n  },\n  inputs: {\n    disabled: \"disabled\",\n    textLabel: [\"label\", \"textLabel\"],\n    ariaLabel: [\"aria-label\", \"ariaLabel\"],\n    ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"],\n    labelClass: \"labelClass\",\n    bodyClass: \"bodyClass\"\n  },\n  exportAs: [\"matTab\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_TAB,\n    useExisting: MatTab\n  }]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n  ngContentSelectors: _c0,\n  decls: 1,\n  vars: 0,\n  template: function MatTab_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵtemplate(0, MatTab_ng_template_0_Template, 1, 0, \"ng-template\");\n    }\n  },\n  encapsulation: 2\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTab, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab',\n      inputs: ['disabled'],\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matTab',\n      providers: [{\n        provide: MAT_TAB,\n        useExisting: MatTab\n      }],\n      template: \"<!-- Create a template for the content of the <mat-tab> so that we can grab a reference to this\\n    TemplateRef and use it in a Portal to render the tab content in the appropriate place in the\\n    tab-group. -->\\n<ng-template><ng-content></ng-content></ng-template>\\n\"\n    }]\n  }], function () {\n    return [{\n      type: i0.ViewContainerRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_TAB_GROUP]\n      }, {\n        type: Optional\n      }]\n    }];\n  }, {\n    templateLabel: [{\n      type: ContentChild,\n      args: [MAT_TAB_LABEL]\n    }],\n    _explicitContent: [{\n      type: ContentChild,\n      args: [MAT_TAB_CONTENT, {\n        read: TemplateRef,\n        static: true\n      }]\n    }],\n    _implicitContent: [{\n      type: ViewChild,\n      args: [TemplateRef, {\n        static: true\n      }]\n    }],\n    textLabel: [{\n      type: Input,\n      args: ['label']\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    labelClass: [{\n      type: Input\n    }],\n    bodyClass: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Animations used by the Material tabs.\n * @docs-private\n */\n\n\nconst matTabsAnimations = {\n  /** Animation translates a tab along the X axis. */\n  translateTab: trigger('translateTab', [// Transitions to `none` instead of 0, because some browsers might blur the content.\n  state('center, void, left-origin-center, right-origin-center', style({\n    transform: 'none'\n  })), // If the tab is either on the left or right, we additionally add a `min-height` of 1px\n  // in order to ensure that the element has a height before its state changes. This is\n  // necessary because Chrome does seem to skip the transition in RTL mode if the element does\n  // not have a static height and is not rendered. See related issue: #9465\n  state('left', style({\n    transform: 'translate3d(-100%, 0, 0)',\n    minHeight: '1px',\n    // Normally this is redundant since we detach the content from the DOM, but if the user\n    // opted into keeping the content in the DOM, we have to hide it so it isn't focusable.\n    visibility: 'hidden'\n  })), state('right', style({\n    transform: 'translate3d(100%, 0, 0)',\n    minHeight: '1px',\n    visibility: 'hidden'\n  })), transition('* => left, * => right, left => center, right => center', animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)')), transition('void => left-origin-center', [style({\n    transform: 'translate3d(-100%, 0, 0)',\n    visibility: 'hidden'\n  }), animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)')]), transition('void => right-origin-center', [style({\n    transform: 'translate3d(100%, 0, 0)',\n    visibility: 'hidden'\n  }), animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)')])])\n};\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * The portal host directive for the contents of the tab.\n * @docs-private\n */\n\nclass MatTabBodyPortal extends CdkPortalOutlet {\n  constructor(componentFactoryResolver, viewContainerRef, _host, _document) {\n    super(componentFactoryResolver, viewContainerRef, _document);\n    this._host = _host;\n    /** Subscription to events for when the tab body begins centering. */\n\n    this._centeringSub = Subscription.EMPTY;\n    /** Subscription to events for when the tab body finishes leaving from center position. */\n\n    this._leavingSub = Subscription.EMPTY;\n  }\n  /** Set initial visibility or set up subscription for changing visibility. */\n\n\n  ngOnInit() {\n    super.ngOnInit();\n    this._centeringSub = this._host._beforeCentering.pipe(startWith(this._host._isCenterPosition(this._host._position))).subscribe(isCentering => {\n      if (isCentering && !this.hasAttached()) {\n        this.attach(this._host._content);\n      }\n    });\n    this._leavingSub = this._host._afterLeavingCenter.subscribe(() => {\n      if (!this._host.preserveContent) {\n        this.detach();\n      }\n    });\n  }\n  /** Clean up centering subscription. */\n\n\n  ngOnDestroy() {\n    super.ngOnDestroy();\n\n    this._centeringSub.unsubscribe();\n\n    this._leavingSub.unsubscribe();\n  }\n\n}\n\nMatTabBodyPortal.ɵfac = function MatTabBodyPortal_Factory(t) {\n  return new (t || MatTabBodyPortal)(i0.ɵɵdirectiveInject(i0.ComponentFactoryResolver), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(forwardRef(() => MatTabBody)), i0.ɵɵdirectiveInject(DOCUMENT));\n};\n\nMatTabBodyPortal.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatTabBodyPortal,\n  selectors: [[\"\", \"matTabBodyHost\", \"\"]],\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabBodyPortal, [{\n    type: Directive,\n    args: [{\n      selector: '[matTabBodyHost]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ComponentFactoryResolver\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: MatTabBody,\n      decorators: [{\n        type: Inject,\n        args: [forwardRef(() => MatTabBody)]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Base class with all of the `MatTabBody` functionality.\n * @docs-private\n */\n\n\nclass _MatTabBodyBase {\n  constructor(_elementRef, _dir, changeDetectorRef) {\n    this._elementRef = _elementRef;\n    this._dir = _dir;\n    /** Subscription to the directionality change observable. */\n\n    this._dirChangeSubscription = Subscription.EMPTY;\n    /** Emits when an animation on the tab is complete. */\n\n    this._translateTabComplete = new Subject();\n    /** Event emitted when the tab begins to animate towards the center as the active tab. */\n\n    this._onCentering = new EventEmitter();\n    /** Event emitted before the centering of the tab begins. */\n\n    this._beforeCentering = new EventEmitter();\n    /** Event emitted before the centering of the tab begins. */\n\n    this._afterLeavingCenter = new EventEmitter();\n    /** Event emitted when the tab completes its animation towards the center. */\n\n    this._onCentered = new EventEmitter(true); // Note that the default value will always be overwritten by `MatTabBody`, but we need one\n    // anyway to prevent the animations module from throwing an error if the body is used on its own.\n\n    /** Duration for the tab's animation. */\n\n    this.animationDuration = '500ms';\n    /** Whether the tab's content should be kept in the DOM while it's off-screen. */\n\n    this.preserveContent = false;\n\n    if (_dir) {\n      this._dirChangeSubscription = _dir.change.subscribe(dir => {\n        this._computePositionAnimationState(dir);\n\n        changeDetectorRef.markForCheck();\n      });\n    } // Ensure that we get unique animation events, because the `.done` callback can get\n    // invoked twice in some browsers. See https://github.com/angular/angular/issues/24084.\n\n\n    this._translateTabComplete.pipe(distinctUntilChanged((x, y) => {\n      return x.fromState === y.fromState && x.toState === y.toState;\n    })).subscribe(event => {\n      // If the transition to the center is complete, emit an event.\n      if (this._isCenterPosition(event.toState) && this._isCenterPosition(this._position)) {\n        this._onCentered.emit();\n      }\n\n      if (this._isCenterPosition(event.fromState) && !this._isCenterPosition(this._position)) {\n        this._afterLeavingCenter.emit();\n      }\n    });\n  }\n  /** The shifted index position of the tab body, where zero represents the active center tab. */\n\n\n  set position(position) {\n    this._positionIndex = position;\n\n    this._computePositionAnimationState();\n  }\n  /**\n   * After initialized, check if the content is centered and has an origin. If so, set the\n   * special position states that transition the tab from the left or right before centering.\n   */\n\n\n  ngOnInit() {\n    if (this._position == 'center' && this.origin != null) {\n      this._position = this._computePositionFromOrigin(this.origin);\n    }\n  }\n\n  ngOnDestroy() {\n    this._dirChangeSubscription.unsubscribe();\n\n    this._translateTabComplete.complete();\n  }\n\n  _onTranslateTabStarted(event) {\n    const isCentering = this._isCenterPosition(event.toState);\n\n    this._beforeCentering.emit(isCentering);\n\n    if (isCentering) {\n      this._onCentering.emit(this._elementRef.nativeElement.clientHeight);\n    }\n  }\n  /** The text direction of the containing app. */\n\n\n  _getLayoutDirection() {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n  /** Whether the provided position state is considered center, regardless of origin. */\n\n\n  _isCenterPosition(position) {\n    return position == 'center' || position == 'left-origin-center' || position == 'right-origin-center';\n  }\n  /** Computes the position state that will be used for the tab-body animation trigger. */\n\n\n  _computePositionAnimationState(dir = this._getLayoutDirection()) {\n    if (this._positionIndex < 0) {\n      this._position = dir == 'ltr' ? 'left' : 'right';\n    } else if (this._positionIndex > 0) {\n      this._position = dir == 'ltr' ? 'right' : 'left';\n    } else {\n      this._position = 'center';\n    }\n  }\n  /**\n   * Computes the position state based on the specified origin position. This is used if the\n   * tab is becoming visible immediately after creation.\n   */\n\n\n  _computePositionFromOrigin(origin) {\n    const dir = this._getLayoutDirection();\n\n    if (dir == 'ltr' && origin <= 0 || dir == 'rtl' && origin > 0) {\n      return 'left-origin-center';\n    }\n\n    return 'right-origin-center';\n  }\n\n}\n\n_MatTabBodyBase.ɵfac = function _MatTabBodyBase_Factory(t) {\n  return new (t || _MatTabBodyBase)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n\n_MatTabBodyBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatTabBodyBase,\n  inputs: {\n    _content: [\"content\", \"_content\"],\n    origin: \"origin\",\n    animationDuration: \"animationDuration\",\n    preserveContent: \"preserveContent\",\n    position: \"position\"\n  },\n  outputs: {\n    _onCentering: \"_onCentering\",\n    _beforeCentering: \"_beforeCentering\",\n    _afterLeavingCenter: \"_afterLeavingCenter\",\n    _onCentered: \"_onCentered\"\n  }\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatTabBodyBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    _onCentering: [{\n      type: Output\n    }],\n    _beforeCentering: [{\n      type: Output\n    }],\n    _afterLeavingCenter: [{\n      type: Output\n    }],\n    _onCentered: [{\n      type: Output\n    }],\n    _content: [{\n      type: Input,\n      args: ['content']\n    }],\n    origin: [{\n      type: Input\n    }],\n    animationDuration: [{\n      type: Input\n    }],\n    preserveContent: [{\n      type: Input\n    }],\n    position: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Wrapper for the contents of a tab.\n * @docs-private\n */\n\n\nclass MatTabBody extends _MatTabBodyBase {\n  constructor(elementRef, dir, changeDetectorRef) {\n    super(elementRef, dir, changeDetectorRef);\n  }\n\n}\n\nMatTabBody.ɵfac = function MatTabBody_Factory(t) {\n  return new (t || MatTabBody)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n\nMatTabBody.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatTabBody,\n  selectors: [[\"mat-tab-body\"]],\n  viewQuery: function MatTabBody_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(CdkPortalOutlet, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._portalHost = _t.first);\n    }\n  },\n  hostAttrs: [1, \"mat-tab-body\"],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  decls: 3,\n  vars: 6,\n  consts: [[\"cdkScrollable\", \"\", 1, \"mat-tab-body-content\"], [\"content\", \"\"], [\"matTabBodyHost\", \"\"]],\n  template: function MatTabBody_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0, 1);\n      i0.ɵɵlistener(\"@translateTab.start\", function MatTabBody_Template_div_animation_translateTab_start_0_listener($event) {\n        return ctx._onTranslateTabStarted($event);\n      })(\"@translateTab.done\", function MatTabBody_Template_div_animation_translateTab_done_0_listener($event) {\n        return ctx._translateTabComplete.next($event);\n      });\n      i0.ɵɵtemplate(2, MatTabBody_ng_template_2_Template, 0, 0, \"ng-template\", 2);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"@translateTab\", i0.ɵɵpureFunction2(3, _c2, ctx._position, i0.ɵɵpureFunction1(1, _c1, ctx.animationDuration)));\n    }\n  },\n  dependencies: [MatTabBodyPortal],\n  styles: [\".mat-tab-body-content{height:100%;overflow:auto}.mat-tab-group-dynamic-height .mat-tab-body-content{overflow:hidden}.mat-tab-body-content[style*=\\\"visibility: hidden\\\"]{display:none}\"],\n  encapsulation: 2,\n  data: {\n    animation: [matTabsAnimations.translateTab]\n  }\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabBody, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab-body',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      animations: [matTabsAnimations.translateTab],\n      host: {\n        'class': 'mat-tab-body'\n      },\n      template: \"<div class=\\\"mat-tab-body-content\\\" #content\\n     [@translateTab]=\\\"{\\n        value: _position,\\n        params: {animationDuration: animationDuration}\\n     }\\\"\\n     (@translateTab.start)=\\\"_onTranslateTabStarted($event)\\\"\\n     (@translateTab.done)=\\\"_translateTabComplete.next($event)\\\"\\n     cdkScrollable>\\n  <ng-template matTabBodyHost></ng-template>\\n</div>\\n\",\n      styles: [\".mat-tab-body-content{height:100%;overflow:auto}.mat-tab-group-dynamic-height .mat-tab-body-content{overflow:hidden}.mat-tab-body-content[style*=\\\"visibility: hidden\\\"]{display:none}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    _portalHost: [{\n      type: ViewChild,\n      args: [CdkPortalOutlet]\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Injection token that can be used to provide the default options the tabs module. */\n\n\nconst MAT_TABS_CONFIG = new InjectionToken('MAT_TABS_CONFIG');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to MatTabLabelWrapper.\n\n/** @docs-private */\n\nconst _MatTabLabelWrapperBase = mixinDisabled(class {});\n/**\n * Used in the `mat-tab-group` view to display tab labels.\n * @docs-private\n */\n\n\nclass MatTabLabelWrapper extends _MatTabLabelWrapperBase {\n  constructor(elementRef) {\n    super();\n    this.elementRef = elementRef;\n  }\n  /** Sets focus on the wrapper element */\n\n\n  focus() {\n    this.elementRef.nativeElement.focus();\n  }\n\n  getOffsetLeft() {\n    return this.elementRef.nativeElement.offsetLeft;\n  }\n\n  getOffsetWidth() {\n    return this.elementRef.nativeElement.offsetWidth;\n  }\n\n}\n\nMatTabLabelWrapper.ɵfac = function MatTabLabelWrapper_Factory(t) {\n  return new (t || MatTabLabelWrapper)(i0.ɵɵdirectiveInject(i0.ElementRef));\n};\n\nMatTabLabelWrapper.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatTabLabelWrapper,\n  selectors: [[\"\", \"matTabLabelWrapper\", \"\"]],\n  hostVars: 3,\n  hostBindings: function MatTabLabelWrapper_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"aria-disabled\", !!ctx.disabled);\n      i0.ɵɵclassProp(\"mat-tab-disabled\", ctx.disabled);\n    }\n  },\n  inputs: {\n    disabled: \"disabled\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabLabelWrapper, [{\n    type: Directive,\n    args: [{\n      selector: '[matTabLabelWrapper]',\n      inputs: ['disabled'],\n      host: {\n        '[class.mat-tab-disabled]': 'disabled',\n        '[attr.aria-disabled]': '!!disabled'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Config used to bind passive event listeners */\n\n\nconst passiveEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true\n});\n/**\n * The distance in pixels that will be overshot when scrolling a tab label into view. This helps\n * provide a small affordance to the label next to it.\n */\n\nconst EXAGGERATED_OVERSCROLL = 60;\n/**\n * Amount of milliseconds to wait before starting to scroll the header automatically.\n * Set a little conservatively in order to handle fake events dispatched on touch devices.\n */\n\nconst HEADER_SCROLL_DELAY = 650;\n/**\n * Interval in milliseconds at which to scroll the header\n * while the user is holding their pointer.\n */\n\nconst HEADER_SCROLL_INTERVAL = 100;\n/**\n * Base class for a tab header that supported pagination.\n * @docs-private\n */\n\nclass MatPaginatedTabHeader {\n  constructor(_elementRef, _changeDetectorRef, _viewportRuler, _dir, _ngZone, _platform, _animationMode) {\n    this._elementRef = _elementRef;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._viewportRuler = _viewportRuler;\n    this._dir = _dir;\n    this._ngZone = _ngZone;\n    this._platform = _platform;\n    this._animationMode = _animationMode;\n    /** The distance in pixels that the tab labels should be translated to the left. */\n\n    this._scrollDistance = 0;\n    /** Whether the header should scroll to the selected index after the view has been checked. */\n\n    this._selectedIndexChanged = false;\n    /** Emits when the component is destroyed. */\n\n    this._destroyed = new Subject();\n    /** Whether the controls for pagination should be displayed */\n\n    this._showPaginationControls = false;\n    /** Whether the tab list can be scrolled more towards the end of the tab label list. */\n\n    this._disableScrollAfter = true;\n    /** Whether the tab list can be scrolled more towards the beginning of the tab label list. */\n\n    this._disableScrollBefore = true;\n    /** Stream that will stop the automated scrolling. */\n\n    this._stopScrolling = new Subject();\n    this._disablePagination = false;\n    this._selectedIndex = 0;\n    /** Event emitted when the option is selected. */\n\n    this.selectFocusedIndex = new EventEmitter();\n    /** Event emitted when a label is focused. */\n\n    this.indexFocused = new EventEmitter(); // Bind the `mouseleave` event on the outside since it doesn't change anything in the view.\n\n    _ngZone.runOutsideAngular(() => {\n      fromEvent(_elementRef.nativeElement, 'mouseleave').pipe(takeUntil(this._destroyed)).subscribe(() => {\n        this._stopInterval();\n      });\n    });\n  }\n  /**\n   * Whether pagination should be disabled. This can be used to avoid unnecessary\n   * layout recalculations if it's known that pagination won't be required.\n   */\n\n\n  get disablePagination() {\n    return this._disablePagination;\n  }\n\n  set disablePagination(value) {\n    this._disablePagination = coerceBooleanProperty(value);\n  }\n  /** The index of the active tab. */\n\n\n  get selectedIndex() {\n    return this._selectedIndex;\n  }\n\n  set selectedIndex(value) {\n    value = coerceNumberProperty(value);\n\n    if (this._selectedIndex != value) {\n      this._selectedIndexChanged = true;\n      this._selectedIndex = value;\n\n      if (this._keyManager) {\n        this._keyManager.updateActiveItem(value);\n      }\n    }\n  }\n\n  ngAfterViewInit() {\n    // We need to handle these events manually, because we want to bind passive event listeners.\n    fromEvent(this._previousPaginator.nativeElement, 'touchstart', passiveEventListenerOptions).pipe(takeUntil(this._destroyed)).subscribe(() => {\n      this._handlePaginatorPress('before');\n    });\n    fromEvent(this._nextPaginator.nativeElement, 'touchstart', passiveEventListenerOptions).pipe(takeUntil(this._destroyed)).subscribe(() => {\n      this._handlePaginatorPress('after');\n    });\n  }\n\n  ngAfterContentInit() {\n    const dirChange = this._dir ? this._dir.change : of('ltr');\n\n    const resize = this._viewportRuler.change(150);\n\n    const realign = () => {\n      this.updatePagination();\n\n      this._alignInkBarToSelectedTab();\n    };\n\n    this._keyManager = new FocusKeyManager(this._items).withHorizontalOrientation(this._getLayoutDirection()).withHomeAndEnd().withWrap();\n\n    this._keyManager.updateActiveItem(this._selectedIndex); // Defer the first call in order to allow for slower browsers to lay out the elements.\n    // This helps in cases where the user lands directly on a page with paginated tabs.\n    // Note that we use `onStable` instead of `requestAnimationFrame`, because the latter\n    // can hold up tests that are in a background tab.\n\n\n    this._ngZone.onStable.pipe(take(1)).subscribe(realign); // On dir change or window resize, realign the ink bar and update the orientation of\n    // the key manager if the direction has changed.\n\n\n    merge(dirChange, resize, this._items.changes, this._itemsResized()).pipe(takeUntil(this._destroyed)).subscribe(() => {\n      // We need to defer this to give the browser some time to recalculate\n      // the element dimensions. The call has to be wrapped in `NgZone.run`,\n      // because the viewport change handler runs outside of Angular.\n      this._ngZone.run(() => {\n        Promise.resolve().then(() => {\n          // Clamp the scroll distance, because it can change with the number of tabs.\n          this._scrollDistance = Math.max(0, Math.min(this._getMaxScrollDistance(), this._scrollDistance));\n          realign();\n        });\n      });\n\n      this._keyManager.withHorizontalOrientation(this._getLayoutDirection());\n    }); // If there is a change in the focus key manager we need to emit the `indexFocused`\n    // event in order to provide a public event that notifies about focus changes. Also we realign\n    // the tabs container by scrolling the new focused tab into the visible section.\n\n    this._keyManager.change.pipe(takeUntil(this._destroyed)).subscribe(newFocusIndex => {\n      this.indexFocused.emit(newFocusIndex);\n\n      this._setTabFocus(newFocusIndex);\n    });\n  }\n  /** Sends any changes that could affect the layout of the items. */\n\n\n  _itemsResized() {\n    if (typeof ResizeObserver !== 'function') {\n      return EMPTY;\n    }\n\n    return this._items.changes.pipe(startWith(this._items), switchMap(tabItems => new Observable(observer => this._ngZone.runOutsideAngular(() => {\n      const resizeObserver = new ResizeObserver(entries => observer.next(entries));\n      tabItems.forEach(item => resizeObserver.observe(item.elementRef.nativeElement));\n      return () => {\n        resizeObserver.disconnect();\n      };\n    }))), // Skip the first emit since the resize observer emits when an item\n    // is observed for new items when the tab is already inserted\n    skip(1), // Skip emissions where all the elements are invisible since we don't want\n    // the header to try and re-render with invalid measurements. See #25574.\n    filter(entries => entries.some(e => e.contentRect.width > 0 && e.contentRect.height > 0)));\n  }\n\n  ngAfterContentChecked() {\n    // If the number of tab labels have changed, check if scrolling should be enabled\n    if (this._tabLabelCount != this._items.length) {\n      this.updatePagination();\n      this._tabLabelCount = this._items.length;\n\n      this._changeDetectorRef.markForCheck();\n    } // If the selected index has changed, scroll to the label and check if the scrolling controls\n    // should be disabled.\n\n\n    if (this._selectedIndexChanged) {\n      this._scrollToLabel(this._selectedIndex);\n\n      this._checkScrollingControls();\n\n      this._alignInkBarToSelectedTab();\n\n      this._selectedIndexChanged = false;\n\n      this._changeDetectorRef.markForCheck();\n    } // If the scroll distance has been changed (tab selected, focused, scroll controls activated),\n    // then translate the header to reflect this.\n\n\n    if (this._scrollDistanceChanged) {\n      this._updateTabScrollPosition();\n\n      this._scrollDistanceChanged = false;\n\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n\n  ngOnDestroy() {\n    this._destroyed.next();\n\n    this._destroyed.complete();\n\n    this._stopScrolling.complete();\n  }\n  /** Handles keyboard events on the header. */\n\n\n  _handleKeydown(event) {\n    // We don't handle any key bindings with a modifier key.\n    if (hasModifierKey(event)) {\n      return;\n    }\n\n    switch (event.keyCode) {\n      case ENTER:\n      case SPACE:\n        if (this.focusIndex !== this.selectedIndex) {\n          this.selectFocusedIndex.emit(this.focusIndex);\n\n          this._itemSelected(event);\n        }\n\n        break;\n\n      default:\n        this._keyManager.onKeydown(event);\n\n    }\n  }\n  /**\n   * Callback for when the MutationObserver detects that the content has changed.\n   */\n\n\n  _onContentChanges() {\n    const textContent = this._elementRef.nativeElement.textContent; // We need to diff the text content of the header, because the MutationObserver callback\n    // will fire even if the text content didn't change which is inefficient and is prone\n    // to infinite loops if a poorly constructed expression is passed in (see #14249).\n\n    if (textContent !== this._currentTextContent) {\n      this._currentTextContent = textContent || ''; // The content observer runs outside the `NgZone` by default, which\n      // means that we need to bring the callback back in ourselves.\n\n      this._ngZone.run(() => {\n        this.updatePagination();\n\n        this._alignInkBarToSelectedTab();\n\n        this._changeDetectorRef.markForCheck();\n      });\n    }\n  }\n  /**\n   * Updates the view whether pagination should be enabled or not.\n   *\n   * WARNING: Calling this method can be very costly in terms of performance. It should be called\n   * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n   * page.\n   */\n\n\n  updatePagination() {\n    this._checkPaginationEnabled();\n\n    this._checkScrollingControls();\n\n    this._updateTabScrollPosition();\n  }\n  /** Tracks which element has focus; used for keyboard navigation */\n\n\n  get focusIndex() {\n    return this._keyManager ? this._keyManager.activeItemIndex : 0;\n  }\n  /** When the focus index is set, we must manually send focus to the correct label */\n\n\n  set focusIndex(value) {\n    if (!this._isValidIndex(value) || this.focusIndex === value || !this._keyManager) {\n      return;\n    }\n\n    this._keyManager.setActiveItem(value);\n  }\n  /**\n   * Determines if an index is valid.  If the tabs are not ready yet, we assume that the user is\n   * providing a valid index and return true.\n   */\n\n\n  _isValidIndex(index) {\n    if (!this._items) {\n      return true;\n    }\n\n    const tab = this._items ? this._items.toArray()[index] : null;\n    return !!tab && !tab.disabled;\n  }\n  /**\n   * Sets focus on the HTML element for the label wrapper and scrolls it into the view if\n   * scrolling is enabled.\n   */\n\n\n  _setTabFocus(tabIndex) {\n    if (this._showPaginationControls) {\n      this._scrollToLabel(tabIndex);\n    }\n\n    if (this._items && this._items.length) {\n      this._items.toArray()[tabIndex].focus(); // Do not let the browser manage scrolling to focus the element, this will be handled\n      // by using translation. In LTR, the scroll left should be 0. In RTL, the scroll width\n      // should be the full width minus the offset width.\n\n\n      const containerEl = this._tabListContainer.nativeElement;\n\n      const dir = this._getLayoutDirection();\n\n      if (dir == 'ltr') {\n        containerEl.scrollLeft = 0;\n      } else {\n        containerEl.scrollLeft = containerEl.scrollWidth - containerEl.offsetWidth;\n      }\n    }\n  }\n  /** The layout direction of the containing app. */\n\n\n  _getLayoutDirection() {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n  /** Performs the CSS transformation on the tab list that will cause the list to scroll. */\n\n\n  _updateTabScrollPosition() {\n    if (this.disablePagination) {\n      return;\n    }\n\n    const scrollDistance = this.scrollDistance;\n    const translateX = this._getLayoutDirection() === 'ltr' ? -scrollDistance : scrollDistance; // Don't use `translate3d` here because we don't want to create a new layer. A new layer\n    // seems to cause flickering and overflow in Internet Explorer. For example, the ink bar\n    // and ripples will exceed the boundaries of the visible tab bar.\n    // See: https://github.com/angular/components/issues/10276\n    // We round the `transform` here, because transforms with sub-pixel precision cause some\n    // browsers to blur the content of the element.\n\n    this._tabList.nativeElement.style.transform = `translateX(${Math.round(translateX)}px)`; // Setting the `transform` on IE will change the scroll offset of the parent, causing the\n    // position to be thrown off in some cases. We have to reset it ourselves to ensure that\n    // it doesn't get thrown off. Note that we scope it only to IE and Edge, because messing\n    // with the scroll position throws off Chrome 71+ in RTL mode (see #14689).\n\n    if (this._platform.TRIDENT || this._platform.EDGE) {\n      this._tabListContainer.nativeElement.scrollLeft = 0;\n    }\n  }\n  /** Sets the distance in pixels that the tab header should be transformed in the X-axis. */\n\n\n  get scrollDistance() {\n    return this._scrollDistance;\n  }\n\n  set scrollDistance(value) {\n    this._scrollTo(value);\n  }\n  /**\n   * Moves the tab list in the 'before' or 'after' direction (towards the beginning of the list or\n   * the end of the list, respectively). The distance to scroll is computed to be a third of the\n   * length of the tab list view window.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n\n\n  _scrollHeader(direction) {\n    const viewLength = this._tabListContainer.nativeElement.offsetWidth; // Move the scroll distance one-third the length of the tab list's viewport.\n\n    const scrollAmount = (direction == 'before' ? -1 : 1) * viewLength / 3;\n    return this._scrollTo(this._scrollDistance + scrollAmount);\n  }\n  /** Handles click events on the pagination arrows. */\n\n\n  _handlePaginatorClick(direction) {\n    this._stopInterval();\n\n    this._scrollHeader(direction);\n  }\n  /**\n   * Moves the tab list such that the desired tab label (marked by index) is moved into view.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n\n\n  _scrollToLabel(labelIndex) {\n    if (this.disablePagination) {\n      return;\n    }\n\n    const selectedLabel = this._items ? this._items.toArray()[labelIndex] : null;\n\n    if (!selectedLabel) {\n      return;\n    } // The view length is the visible width of the tab labels.\n\n\n    const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n    const {\n      offsetLeft,\n      offsetWidth\n    } = selectedLabel.elementRef.nativeElement;\n    let labelBeforePos, labelAfterPos;\n\n    if (this._getLayoutDirection() == 'ltr') {\n      labelBeforePos = offsetLeft;\n      labelAfterPos = labelBeforePos + offsetWidth;\n    } else {\n      labelAfterPos = this._tabListInner.nativeElement.offsetWidth - offsetLeft;\n      labelBeforePos = labelAfterPos - offsetWidth;\n    }\n\n    const beforeVisiblePos = this.scrollDistance;\n    const afterVisiblePos = this.scrollDistance + viewLength;\n\n    if (labelBeforePos < beforeVisiblePos) {\n      // Scroll header to move label to the before direction\n      this.scrollDistance -= beforeVisiblePos - labelBeforePos + EXAGGERATED_OVERSCROLL;\n    } else if (labelAfterPos > afterVisiblePos) {\n      // Scroll header to move label to the after direction\n      this.scrollDistance += labelAfterPos - afterVisiblePos + EXAGGERATED_OVERSCROLL;\n    }\n  }\n  /**\n   * Evaluate whether the pagination controls should be displayed. If the scroll width of the\n   * tab list is wider than the size of the header container, then the pagination controls should\n   * be shown.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n\n\n  _checkPaginationEnabled() {\n    if (this.disablePagination) {\n      this._showPaginationControls = false;\n    } else {\n      const isEnabled = this._tabListInner.nativeElement.scrollWidth > this._elementRef.nativeElement.offsetWidth;\n\n      if (!isEnabled) {\n        this.scrollDistance = 0;\n      }\n\n      if (isEnabled !== this._showPaginationControls) {\n        this._changeDetectorRef.markForCheck();\n      }\n\n      this._showPaginationControls = isEnabled;\n    }\n  }\n  /**\n   * Evaluate whether the before and after controls should be enabled or disabled.\n   * If the header is at the beginning of the list (scroll distance is equal to 0) then disable the\n   * before button. If the header is at the end of the list (scroll distance is equal to the\n   * maximum distance we can scroll), then disable the after button.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n\n\n  _checkScrollingControls() {\n    if (this.disablePagination) {\n      this._disableScrollAfter = this._disableScrollBefore = true;\n    } else {\n      // Check if the pagination arrows should be activated.\n      this._disableScrollBefore = this.scrollDistance == 0;\n      this._disableScrollAfter = this.scrollDistance == this._getMaxScrollDistance();\n\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /**\n   * Determines what is the maximum length in pixels that can be set for the scroll distance. This\n   * is equal to the difference in width between the tab list container and tab header container.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n\n\n  _getMaxScrollDistance() {\n    const lengthOfTabList = this._tabListInner.nativeElement.scrollWidth;\n    const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n    return lengthOfTabList - viewLength || 0;\n  }\n  /** Tells the ink-bar to align itself to the current label wrapper */\n\n\n  _alignInkBarToSelectedTab() {\n    const selectedItem = this._items && this._items.length ? this._items.toArray()[this.selectedIndex] : null;\n    const selectedLabelWrapper = selectedItem ? selectedItem.elementRef.nativeElement : null;\n\n    if (selectedLabelWrapper) {\n      this._inkBar.alignToElement(selectedLabelWrapper);\n    } else {\n      this._inkBar.hide();\n    }\n  }\n  /** Stops the currently-running paginator interval.  */\n\n\n  _stopInterval() {\n    this._stopScrolling.next();\n  }\n  /**\n   * Handles the user pressing down on one of the paginators.\n   * Starts scrolling the header after a certain amount of time.\n   * @param direction In which direction the paginator should be scrolled.\n   */\n\n\n  _handlePaginatorPress(direction, mouseEvent) {\n    // Don't start auto scrolling for right mouse button clicks. Note that we shouldn't have to\n    // null check the `button`, but we do it so we don't break tests that use fake events.\n    if (mouseEvent && mouseEvent.button != null && mouseEvent.button !== 0) {\n      return;\n    } // Avoid overlapping timers.\n\n\n    this._stopInterval(); // Start a timer after the delay and keep firing based on the interval.\n\n\n    timer(HEADER_SCROLL_DELAY, HEADER_SCROLL_INTERVAL) // Keep the timer going until something tells it to stop or the component is destroyed.\n    .pipe(takeUntil(merge(this._stopScrolling, this._destroyed))).subscribe(() => {\n      const {\n        maxScrollDistance,\n        distance\n      } = this._scrollHeader(direction); // Stop the timer if we've reached the start or the end.\n\n\n      if (distance === 0 || distance >= maxScrollDistance) {\n        this._stopInterval();\n      }\n    });\n  }\n  /**\n   * Scrolls the header to a given position.\n   * @param position Position to which to scroll.\n   * @returns Information on the current scroll distance and the maximum.\n   */\n\n\n  _scrollTo(position) {\n    if (this.disablePagination) {\n      return {\n        maxScrollDistance: 0,\n        distance: 0\n      };\n    }\n\n    const maxScrollDistance = this._getMaxScrollDistance();\n\n    this._scrollDistance = Math.max(0, Math.min(maxScrollDistance, position)); // Mark that the scroll distance has changed so that after the view is checked, the CSS\n    // transformation can move the header.\n\n    this._scrollDistanceChanged = true;\n\n    this._checkScrollingControls();\n\n    return {\n      maxScrollDistance,\n      distance: this._scrollDistance\n    };\n  }\n\n}\n\nMatPaginatedTabHeader.ɵfac = function MatPaginatedTabHeader_Factory(t) {\n  return new (t || MatPaginatedTabHeader)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.ViewportRuler), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n};\n\nMatPaginatedTabHeader.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatPaginatedTabHeader,\n  inputs: {\n    disablePagination: \"disablePagination\"\n  }\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPaginatedTabHeader, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1$1.ViewportRuler\n    }, {\n      type: i1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i3.Platform\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    disablePagination: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Base class with all of the `MatTabHeader` functionality.\n * @docs-private\n */\n\n\nclass _MatTabHeaderBase extends MatPaginatedTabHeader {\n  constructor(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode) {\n    super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n    this._disableRipple = false;\n  }\n  /** Whether the ripple effect is disabled or not. */\n\n\n  get disableRipple() {\n    return this._disableRipple;\n  }\n\n  set disableRipple(value) {\n    this._disableRipple = coerceBooleanProperty(value);\n  }\n\n  _itemSelected(event) {\n    event.preventDefault();\n  }\n\n}\n\n_MatTabHeaderBase.ɵfac = function _MatTabHeaderBase_Factory(t) {\n  return new (t || _MatTabHeaderBase)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.ViewportRuler), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n};\n\n_MatTabHeaderBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatTabHeaderBase,\n  inputs: {\n    disableRipple: \"disableRipple\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatTabHeaderBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1$1.ViewportRuler\n    }, {\n      type: i1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i3.Platform\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    disableRipple: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * The header of the tab group which displays a list of all the tabs in the tab group. Includes\n * an ink bar that follows the currently selected tab. When the tabs list's width exceeds the\n * width of the header container, then arrows will be displayed to allow the user to scroll\n * left and right across the header.\n * @docs-private\n */\n\n\nclass MatTabHeader extends _MatTabHeaderBase {\n  constructor(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode) {\n    super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n  }\n\n}\n\nMatTabHeader.ɵfac = function MatTabHeader_Factory(t) {\n  return new (t || MatTabHeader)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.ViewportRuler), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n};\n\nMatTabHeader.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatTabHeader,\n  selectors: [[\"mat-tab-header\"]],\n  contentQueries: function MatTabHeader_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MatTabLabelWrapper, 4);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._items = _t);\n    }\n  },\n  viewQuery: function MatTabHeader_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(MatInkBar, 7);\n      i0.ɵɵviewQuery(_c3, 7);\n      i0.ɵɵviewQuery(_c4, 7);\n      i0.ɵɵviewQuery(_c5, 7);\n      i0.ɵɵviewQuery(_c6, 5);\n      i0.ɵɵviewQuery(_c7, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._inkBar = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListContainer = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabList = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListInner = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nextPaginator = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._previousPaginator = _t.first);\n    }\n  },\n  hostAttrs: [1, \"mat-tab-header\"],\n  hostVars: 4,\n  hostBindings: function MatTabHeader_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-tab-header-pagination-controls-enabled\", ctx._showPaginationControls)(\"mat-tab-header-rtl\", ctx._getLayoutDirection() == \"rtl\");\n    }\n  },\n  inputs: {\n    selectedIndex: \"selectedIndex\"\n  },\n  outputs: {\n    selectFocusedIndex: \"selectFocusedIndex\",\n    indexFocused: \"indexFocused\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c0,\n  decls: 14,\n  vars: 10,\n  consts: [[\"aria-hidden\", \"true\", \"type\", \"button\", \"mat-ripple\", \"\", \"tabindex\", \"-1\", 1, \"mat-tab-header-pagination\", \"mat-tab-header-pagination-before\", \"mat-elevation-z4\", 3, \"matRippleDisabled\", \"disabled\", \"click\", \"mousedown\", \"touchend\"], [\"previousPaginator\", \"\"], [1, \"mat-tab-header-pagination-chevron\"], [1, \"mat-tab-label-container\", 3, \"keydown\"], [\"tabListContainer\", \"\"], [\"role\", \"tablist\", 1, \"mat-tab-list\", 3, \"cdkObserveContent\"], [\"tabList\", \"\"], [1, \"mat-tab-labels\"], [\"tabListInner\", \"\"], [\"aria-hidden\", \"true\", \"type\", \"button\", \"mat-ripple\", \"\", \"tabindex\", \"-1\", 1, \"mat-tab-header-pagination\", \"mat-tab-header-pagination-after\", \"mat-elevation-z4\", 3, \"matRippleDisabled\", \"disabled\", \"mousedown\", \"click\", \"touchend\"], [\"nextPaginator\", \"\"]],\n  template: function MatTabHeader_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"button\", 0, 1);\n      i0.ɵɵlistener(\"click\", function MatTabHeader_Template_button_click_0_listener() {\n        return ctx._handlePaginatorClick(\"before\");\n      })(\"mousedown\", function MatTabHeader_Template_button_mousedown_0_listener($event) {\n        return ctx._handlePaginatorPress(\"before\", $event);\n      })(\"touchend\", function MatTabHeader_Template_button_touchend_0_listener() {\n        return ctx._stopInterval();\n      });\n      i0.ɵɵelement(2, \"div\", 2);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(3, \"div\", 3, 4);\n      i0.ɵɵlistener(\"keydown\", function MatTabHeader_Template_div_keydown_3_listener($event) {\n        return ctx._handleKeydown($event);\n      });\n      i0.ɵɵelementStart(5, \"div\", 5, 6);\n      i0.ɵɵlistener(\"cdkObserveContent\", function MatTabHeader_Template_div_cdkObserveContent_5_listener() {\n        return ctx._onContentChanges();\n      });\n      i0.ɵɵelementStart(7, \"div\", 7, 8);\n      i0.ɵɵprojection(9);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(10, \"mat-ink-bar\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(11, \"button\", 9, 10);\n      i0.ɵɵlistener(\"mousedown\", function MatTabHeader_Template_button_mousedown_11_listener($event) {\n        return ctx._handlePaginatorPress(\"after\", $event);\n      })(\"click\", function MatTabHeader_Template_button_click_11_listener() {\n        return ctx._handlePaginatorClick(\"after\");\n      })(\"touchend\", function MatTabHeader_Template_button_touchend_11_listener() {\n        return ctx._stopInterval();\n      });\n      i0.ɵɵelement(13, \"div\", 2);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-tab-header-pagination-disabled\", ctx._disableScrollBefore);\n      i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollBefore || ctx.disableRipple)(\"disabled\", ctx._disableScrollBefore || null);\n      i0.ɵɵadvance(5);\n      i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n      i0.ɵɵadvance(6);\n      i0.ɵɵclassProp(\"mat-tab-header-pagination-disabled\", ctx._disableScrollAfter);\n      i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollAfter || ctx.disableRipple)(\"disabled\", ctx._disableScrollAfter || null);\n    }\n  },\n  dependencies: [i4.MatRipple, i5.CdkObserveContent, MatInkBar],\n  styles: [\".mat-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mat-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-tab-header-pagination::-moz-focus-inner{border:0}.mat-tab-header-pagination-controls-enabled .mat-tab-header-pagination{display:flex}.mat-tab-header-pagination-before,.mat-tab-header-rtl .mat-tab-header-pagination-after{padding-left:4px}.mat-tab-header-pagination-before .mat-tab-header-pagination-chevron,.mat-tab-header-rtl .mat-tab-header-pagination-after .mat-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-tab-header-rtl .mat-tab-header-pagination-before,.mat-tab-header-pagination-after{padding-right:4px}.mat-tab-header-rtl .mat-tab-header-pagination-before .mat-tab-header-pagination-chevron,.mat-tab-header-pagination-after .mat-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px}.mat-tab-header-pagination-disabled{box-shadow:none;cursor:default}.mat-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-ink-bar{position:absolute;bottom:0;height:2px;transition:500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-ink-bar._mat-animation-noopable{transition:none !important;animation:none !important}.mat-tab-group-inverted-header .mat-ink-bar{bottom:auto;top:0}.cdk-high-contrast-active .mat-ink-bar{outline:solid 2px;height:0}.mat-tab-labels{display:flex}[mat-align-tabs=center]>.mat-tab-header .mat-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-tab-header .mat-tab-labels{justify-content:flex-end}.mat-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1}.mat-tab-list._mat-animation-noopable{transition:none !important;animation:none !important}.mat-tab-label{height:48px;padding:0 24px;cursor:pointer;box-sizing:border-box;opacity:.6;min-width:160px;text-align:center;display:inline-flex;justify-content:center;align-items:center;white-space:nowrap;position:relative}.mat-tab-label:focus{outline:none}.mat-tab-label:focus:not(.mat-tab-disabled){opacity:1}.mat-tab-label.mat-tab-disabled{cursor:default}.cdk-high-contrast-active .mat-tab-label.mat-tab-disabled{opacity:.5}.mat-tab-label .mat-tab-label-content{display:inline-flex;justify-content:center;align-items:center;white-space:nowrap}.cdk-high-contrast-active .mat-tab-label{opacity:1}.mat-tab-label::before{margin:5px}@media(max-width: 599px){.mat-tab-label{min-width:72px}}\"],\n  encapsulation: 2\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabHeader, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab-header',\n      inputs: ['selectedIndex'],\n      outputs: ['selectFocusedIndex', 'indexFocused'],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      host: {\n        'class': 'mat-tab-header',\n        '[class.mat-tab-header-pagination-controls-enabled]': '_showPaginationControls',\n        '[class.mat-tab-header-rtl]': \"_getLayoutDirection() == 'rtl'\"\n      },\n      template: \"<button class=\\\"mat-tab-header-pagination mat-tab-header-pagination-before mat-elevation-z4\\\"\\n     #previousPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     tabindex=\\\"-1\\\"\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     [disabled]=\\\"_disableScrollBefore || null\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\\n<div class=\\\"mat-tab-label-container\\\" #tabListContainer (keydown)=\\\"_handleKeydown($event)\\\">\\n  <div\\n    #tabList\\n    class=\\\"mat-tab-list\\\"\\n    [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\"\\n    role=\\\"tablist\\\"\\n    (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-tab-labels\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n    <mat-ink-bar></mat-ink-bar>\\n  </div>\\n</div>\\n\\n<button class=\\\"mat-tab-header-pagination mat-tab-header-pagination-after mat-elevation-z4\\\"\\n     #nextPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     [disabled]=\\\"_disableScrollAfter || null\\\"\\n     tabindex=\\\"-1\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\",\n      styles: [\".mat-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mat-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-tab-header-pagination::-moz-focus-inner{border:0}.mat-tab-header-pagination-controls-enabled .mat-tab-header-pagination{display:flex}.mat-tab-header-pagination-before,.mat-tab-header-rtl .mat-tab-header-pagination-after{padding-left:4px}.mat-tab-header-pagination-before .mat-tab-header-pagination-chevron,.mat-tab-header-rtl .mat-tab-header-pagination-after .mat-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-tab-header-rtl .mat-tab-header-pagination-before,.mat-tab-header-pagination-after{padding-right:4px}.mat-tab-header-rtl .mat-tab-header-pagination-before .mat-tab-header-pagination-chevron,.mat-tab-header-pagination-after .mat-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px}.mat-tab-header-pagination-disabled{box-shadow:none;cursor:default}.mat-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-ink-bar{position:absolute;bottom:0;height:2px;transition:500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-ink-bar._mat-animation-noopable{transition:none !important;animation:none !important}.mat-tab-group-inverted-header .mat-ink-bar{bottom:auto;top:0}.cdk-high-contrast-active .mat-ink-bar{outline:solid 2px;height:0}.mat-tab-labels{display:flex}[mat-align-tabs=center]>.mat-tab-header .mat-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-tab-header .mat-tab-labels{justify-content:flex-end}.mat-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1}.mat-tab-list._mat-animation-noopable{transition:none !important;animation:none !important}.mat-tab-label{height:48px;padding:0 24px;cursor:pointer;box-sizing:border-box;opacity:.6;min-width:160px;text-align:center;display:inline-flex;justify-content:center;align-items:center;white-space:nowrap;position:relative}.mat-tab-label:focus{outline:none}.mat-tab-label:focus:not(.mat-tab-disabled){opacity:1}.mat-tab-label.mat-tab-disabled{cursor:default}.cdk-high-contrast-active .mat-tab-label.mat-tab-disabled{opacity:.5}.mat-tab-label .mat-tab-label-content{display:inline-flex;justify-content:center;align-items:center;white-space:nowrap}.cdk-high-contrast-active .mat-tab-label{opacity:1}.mat-tab-label::before{margin:5px}@media(max-width: 599px){.mat-tab-label{min-width:72px}}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1$1.ViewportRuler\n    }, {\n      type: i1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i3.Platform\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    _items: [{\n      type: ContentChildren,\n      args: [MatTabLabelWrapper, {\n        descendants: false\n      }]\n    }],\n    _inkBar: [{\n      type: ViewChild,\n      args: [MatInkBar, {\n        static: true\n      }]\n    }],\n    _tabListContainer: [{\n      type: ViewChild,\n      args: ['tabListContainer', {\n        static: true\n      }]\n    }],\n    _tabList: [{\n      type: ViewChild,\n      args: ['tabList', {\n        static: true\n      }]\n    }],\n    _tabListInner: [{\n      type: ViewChild,\n      args: ['tabListInner', {\n        static: true\n      }]\n    }],\n    _nextPaginator: [{\n      type: ViewChild,\n      args: ['nextPaginator']\n    }],\n    _previousPaginator: [{\n      type: ViewChild,\n      args: ['previousPaginator']\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Used to generate unique ID's for each tab component */\n\n\nlet nextId = 0;\n/** A simple change event emitted on focus or selection changes. */\n\nclass MatTabChangeEvent {} // Boilerplate for applying mixins to MatTabGroup.\n\n/** @docs-private */\n\n\nconst _MatTabGroupMixinBase = mixinColor(mixinDisableRipple(class {\n  constructor(_elementRef) {\n    this._elementRef = _elementRef;\n  }\n\n}), 'primary');\n/**\n * Base class with all of the `MatTabGroupBase` functionality.\n * @docs-private\n */\n\n\nclass _MatTabGroupBase extends _MatTabGroupMixinBase {\n  constructor(elementRef, _changeDetectorRef, defaultConfig, _animationMode) {\n    super(elementRef);\n    this._changeDetectorRef = _changeDetectorRef;\n    this._animationMode = _animationMode;\n    /** All of the tabs that belong to the group. */\n\n    this._tabs = new QueryList();\n    /** The tab index that should be selected after the content has been checked. */\n\n    this._indexToSelect = 0;\n    /** Index of the tab that was focused last. */\n\n    this._lastFocusedTabIndex = null;\n    /** Snapshot of the height of the tab body wrapper before another tab is activated. */\n\n    this._tabBodyWrapperHeight = 0;\n    /** Subscription to tabs being added/removed. */\n\n    this._tabsSubscription = Subscription.EMPTY;\n    /** Subscription to changes in the tab labels. */\n\n    this._tabLabelSubscription = Subscription.EMPTY;\n    this._dynamicHeight = false;\n    this._selectedIndex = null;\n    /** Position of the tab header. */\n\n    this.headerPosition = 'above';\n    this._disablePagination = false;\n    this._preserveContent = false;\n    /** Output to enable support for two-way binding on `[(selectedIndex)]` */\n\n    this.selectedIndexChange = new EventEmitter();\n    /** Event emitted when focus has changed within a tab group. */\n\n    this.focusChange = new EventEmitter();\n    /** Event emitted when the body animation has completed */\n\n    this.animationDone = new EventEmitter();\n    /** Event emitted when the tab selection has changed. */\n\n    this.selectedTabChange = new EventEmitter(true);\n    this._groupId = nextId++;\n    this.animationDuration = defaultConfig && defaultConfig.animationDuration ? defaultConfig.animationDuration : '500ms';\n    this.disablePagination = defaultConfig && defaultConfig.disablePagination != null ? defaultConfig.disablePagination : false;\n    this.dynamicHeight = defaultConfig && defaultConfig.dynamicHeight != null ? defaultConfig.dynamicHeight : false;\n    this.contentTabIndex = defaultConfig?.contentTabIndex ?? null;\n    this.preserveContent = !!defaultConfig?.preserveContent;\n  }\n  /** Whether the tab group should grow to the size of the active tab. */\n\n\n  get dynamicHeight() {\n    return this._dynamicHeight;\n  }\n\n  set dynamicHeight(value) {\n    this._dynamicHeight = coerceBooleanProperty(value);\n  }\n  /** The index of the active tab. */\n\n\n  get selectedIndex() {\n    return this._selectedIndex;\n  }\n\n  set selectedIndex(value) {\n    this._indexToSelect = coerceNumberProperty(value, null);\n  }\n  /** Duration for the tab animation. Will be normalized to milliseconds if no units are set. */\n\n\n  get animationDuration() {\n    return this._animationDuration;\n  }\n\n  set animationDuration(value) {\n    this._animationDuration = /^\\d+$/.test(value + '') ? value + 'ms' : value;\n  }\n  /**\n   * `tabindex` to be set on the inner element that wraps the tab content. Can be used for improved\n   * accessibility when the tab does not have focusable elements or if it has scrollable content.\n   * The `tabindex` will be removed automatically for inactive tabs.\n   * Read more at https://www.w3.org/TR/wai-aria-practices/examples/tabs/tabs-2/tabs.html\n   */\n\n\n  get contentTabIndex() {\n    return this._contentTabIndex;\n  }\n\n  set contentTabIndex(value) {\n    this._contentTabIndex = coerceNumberProperty(value, null);\n  }\n  /**\n   * Whether pagination should be disabled. This can be used to avoid unnecessary\n   * layout recalculations if it's known that pagination won't be required.\n   */\n\n\n  get disablePagination() {\n    return this._disablePagination;\n  }\n\n  set disablePagination(value) {\n    this._disablePagination = coerceBooleanProperty(value);\n  }\n  /**\n   * By default tabs remove their content from the DOM while it's off-screen.\n   * Setting this to `true` will keep it in the DOM which will prevent elements\n   * like iframes and videos from reloading next time it comes back into the view.\n   */\n\n\n  get preserveContent() {\n    return this._preserveContent;\n  }\n\n  set preserveContent(value) {\n    this._preserveContent = coerceBooleanProperty(value);\n  }\n  /** Background color of the tab group. */\n\n\n  get backgroundColor() {\n    return this._backgroundColor;\n  }\n\n  set backgroundColor(value) {\n    const nativeElement = this._elementRef.nativeElement;\n    nativeElement.classList.remove(`mat-background-${this.backgroundColor}`);\n\n    if (value) {\n      nativeElement.classList.add(`mat-background-${value}`);\n    }\n\n    this._backgroundColor = value;\n  }\n  /**\n   * After the content is checked, this component knows what tabs have been defined\n   * and what the selected index should be. This is where we can know exactly what position\n   * each tab should be in according to the new selected index, and additionally we know how\n   * a new selected tab should transition in (from the left or right).\n   */\n\n\n  ngAfterContentChecked() {\n    // Don't clamp the `indexToSelect` immediately in the setter because it can happen that\n    // the amount of tabs changes before the actual change detection runs.\n    const indexToSelect = this._indexToSelect = this._clampTabIndex(this._indexToSelect); // If there is a change in selected index, emit a change event. Should not trigger if\n    // the selected index has not yet been initialized.\n\n\n    if (this._selectedIndex != indexToSelect) {\n      const isFirstRun = this._selectedIndex == null;\n\n      if (!isFirstRun) {\n        this.selectedTabChange.emit(this._createChangeEvent(indexToSelect)); // Preserve the height so page doesn't scroll up during tab change.\n        // Fixes https://stackblitz.com/edit/mat-tabs-scroll-page-top-on-tab-change\n\n        const wrapper = this._tabBodyWrapper.nativeElement;\n        wrapper.style.minHeight = wrapper.clientHeight + 'px';\n      } // Changing these values after change detection has run\n      // since the checked content may contain references to them.\n\n\n      Promise.resolve().then(() => {\n        this._tabs.forEach((tab, index) => tab.isActive = index === indexToSelect);\n\n        if (!isFirstRun) {\n          this.selectedIndexChange.emit(indexToSelect); // Clear the min-height, this was needed during tab change to avoid\n          // unnecessary scrolling.\n\n          this._tabBodyWrapper.nativeElement.style.minHeight = '';\n        }\n      });\n    } // Setup the position for each tab and optionally setup an origin on the next selected tab.\n\n\n    this._tabs.forEach((tab, index) => {\n      tab.position = index - indexToSelect; // If there is already a selected tab, then set up an origin for the next selected tab\n      // if it doesn't have one already.\n\n      if (this._selectedIndex != null && tab.position == 0 && !tab.origin) {\n        tab.origin = indexToSelect - this._selectedIndex;\n      }\n    });\n\n    if (this._selectedIndex !== indexToSelect) {\n      this._selectedIndex = indexToSelect;\n      this._lastFocusedTabIndex = null;\n\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n\n  ngAfterContentInit() {\n    this._subscribeToAllTabChanges();\n\n    this._subscribeToTabLabels(); // Subscribe to changes in the amount of tabs, in order to be\n    // able to re-render the content as new tabs are added or removed.\n\n\n    this._tabsSubscription = this._tabs.changes.subscribe(() => {\n      const indexToSelect = this._clampTabIndex(this._indexToSelect); // Maintain the previously-selected tab if a new tab is added or removed and there is no\n      // explicit change that selects a different tab.\n\n\n      if (indexToSelect === this._selectedIndex) {\n        const tabs = this._tabs.toArray();\n\n        let selectedTab;\n\n        for (let i = 0; i < tabs.length; i++) {\n          if (tabs[i].isActive) {\n            // Assign both to the `_indexToSelect` and `_selectedIndex` so we don't fire a changed\n            // event, otherwise the consumer may end up in an infinite loop in some edge cases like\n            // adding a tab within the `selectedIndexChange` event.\n            this._indexToSelect = this._selectedIndex = i;\n            this._lastFocusedTabIndex = null;\n            selectedTab = tabs[i];\n            break;\n          }\n        } // If we haven't found an active tab and a tab exists at the selected index, it means\n        // that the active tab was swapped out. Since this won't be picked up by the rendering\n        // loop in `ngAfterContentChecked`, we need to sync it up manually.\n\n\n        if (!selectedTab && tabs[indexToSelect]) {\n          Promise.resolve().then(() => {\n            tabs[indexToSelect].isActive = true;\n            this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n          });\n        }\n      }\n\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n  /** Listens to changes in all of the tabs. */\n\n\n  _subscribeToAllTabChanges() {\n    // Since we use a query with `descendants: true` to pick up the tabs, we may end up catching\n    // some that are inside of nested tab groups. We filter them out manually by checking that\n    // the closest group to the tab is the current one.\n    this._allTabs.changes.pipe(startWith(this._allTabs)).subscribe(tabs => {\n      this._tabs.reset(tabs.filter(tab => {\n        return tab._closestTabGroup === this || !tab._closestTabGroup;\n      }));\n\n      this._tabs.notifyOnChanges();\n    });\n  }\n\n  ngOnDestroy() {\n    this._tabs.destroy();\n\n    this._tabsSubscription.unsubscribe();\n\n    this._tabLabelSubscription.unsubscribe();\n  }\n  /** Re-aligns the ink bar to the selected tab element. */\n\n\n  realignInkBar() {\n    if (this._tabHeader) {\n      this._tabHeader._alignInkBarToSelectedTab();\n    }\n  }\n  /**\n   * Recalculates the tab group's pagination dimensions.\n   *\n   * WARNING: Calling this method can be very costly in terms of performance. It should be called\n   * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n   * page.\n   */\n\n\n  updatePagination() {\n    if (this._tabHeader) {\n      this._tabHeader.updatePagination();\n    }\n  }\n  /**\n   * Sets focus to a particular tab.\n   * @param index Index of the tab to be focused.\n   */\n\n\n  focusTab(index) {\n    const header = this._tabHeader;\n\n    if (header) {\n      header.focusIndex = index;\n    }\n  }\n\n  _focusChanged(index) {\n    this._lastFocusedTabIndex = index;\n    this.focusChange.emit(this._createChangeEvent(index));\n  }\n\n  _createChangeEvent(index) {\n    const event = new MatTabChangeEvent();\n    event.index = index;\n\n    if (this._tabs && this._tabs.length) {\n      event.tab = this._tabs.toArray()[index];\n    }\n\n    return event;\n  }\n  /**\n   * Subscribes to changes in the tab labels. This is needed, because the @Input for the label is\n   * on the MatTab component, whereas the data binding is inside the MatTabGroup. In order for the\n   * binding to be updated, we need to subscribe to changes in it and trigger change detection\n   * manually.\n   */\n\n\n  _subscribeToTabLabels() {\n    if (this._tabLabelSubscription) {\n      this._tabLabelSubscription.unsubscribe();\n    }\n\n    this._tabLabelSubscription = merge(...this._tabs.map(tab => tab._stateChanges)).subscribe(() => this._changeDetectorRef.markForCheck());\n  }\n  /** Clamps the given index to the bounds of 0 and the tabs length. */\n\n\n  _clampTabIndex(index) {\n    // Note the `|| 0`, which ensures that values like NaN can't get through\n    // and which would otherwise throw the component into an infinite loop\n    // (since Math.max(NaN, 0) === NaN).\n    return Math.min(this._tabs.length - 1, Math.max(index || 0, 0));\n  }\n  /** Returns a unique id for each tab label element */\n\n\n  _getTabLabelId(i) {\n    return `mat-tab-label-${this._groupId}-${i}`;\n  }\n  /** Returns a unique id for each tab content element */\n\n\n  _getTabContentId(i) {\n    return `mat-tab-content-${this._groupId}-${i}`;\n  }\n  /**\n   * Sets the height of the body wrapper to the height of the activating tab if dynamic\n   * height property is true.\n   */\n\n\n  _setTabBodyWrapperHeight(tabHeight) {\n    if (!this._dynamicHeight || !this._tabBodyWrapperHeight) {\n      return;\n    }\n\n    const wrapper = this._tabBodyWrapper.nativeElement;\n    wrapper.style.height = this._tabBodyWrapperHeight + 'px'; // This conditional forces the browser to paint the height so that\n    // the animation to the new height can have an origin.\n\n    if (this._tabBodyWrapper.nativeElement.offsetHeight) {\n      wrapper.style.height = tabHeight + 'px';\n    }\n  }\n  /** Removes the height of the tab body wrapper. */\n\n\n  _removeTabBodyWrapperHeight() {\n    const wrapper = this._tabBodyWrapper.nativeElement;\n    this._tabBodyWrapperHeight = wrapper.clientHeight;\n    wrapper.style.height = '';\n    this.animationDone.emit();\n  }\n  /** Handle click events, setting new selected index if appropriate. */\n\n\n  _handleClick(tab, tabHeader, index) {\n    if (!tab.disabled) {\n      this.selectedIndex = tabHeader.focusIndex = index;\n    }\n  }\n  /** Retrieves the tabindex for the tab. */\n\n\n  _getTabIndex(tab, index) {\n    if (tab.disabled) {\n      return null;\n    }\n\n    const targetIndex = this._lastFocusedTabIndex ?? this.selectedIndex;\n    return index === targetIndex ? 0 : -1;\n  }\n  /** Callback for when the focused state of a tab has changed. */\n\n\n  _tabFocusChanged(focusOrigin, index) {\n    // Mouse/touch focus happens during the `mousedown`/`touchstart` phase which\n    // can cause the tab to be moved out from under the pointer, interrupting the\n    // click sequence (see #21898). We don't need to scroll the tab into view for\n    // such cases anyway, because it will be done when the tab becomes selected.\n    if (focusOrigin && focusOrigin !== 'mouse' && focusOrigin !== 'touch') {\n      this._tabHeader.focusIndex = index;\n    }\n  }\n\n}\n\n_MatTabGroupBase.ɵfac = function _MatTabGroupBase_Factory(t) {\n  return new (t || _MatTabGroupBase)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_TABS_CONFIG, 8), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n};\n\n_MatTabGroupBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatTabGroupBase,\n  inputs: {\n    dynamicHeight: \"dynamicHeight\",\n    selectedIndex: \"selectedIndex\",\n    headerPosition: \"headerPosition\",\n    animationDuration: \"animationDuration\",\n    contentTabIndex: \"contentTabIndex\",\n    disablePagination: \"disablePagination\",\n    preserveContent: \"preserveContent\",\n    backgroundColor: \"backgroundColor\"\n  },\n  outputs: {\n    selectedIndexChange: \"selectedIndexChange\",\n    focusChange: \"focusChange\",\n    animationDone: \"animationDone\",\n    selectedTabChange: \"selectedTabChange\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatTabGroupBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_TABS_CONFIG]\n      }, {\n        type: Optional\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    dynamicHeight: [{\n      type: Input\n    }],\n    selectedIndex: [{\n      type: Input\n    }],\n    headerPosition: [{\n      type: Input\n    }],\n    animationDuration: [{\n      type: Input\n    }],\n    contentTabIndex: [{\n      type: Input\n    }],\n    disablePagination: [{\n      type: Input\n    }],\n    preserveContent: [{\n      type: Input\n    }],\n    backgroundColor: [{\n      type: Input\n    }],\n    selectedIndexChange: [{\n      type: Output\n    }],\n    focusChange: [{\n      type: Output\n    }],\n    animationDone: [{\n      type: Output\n    }],\n    selectedTabChange: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * Material design tab-group component. Supports basic tab pairs (label + content) and includes\n * animated ink-bar, keyboard navigation, and screen reader.\n * See: https://material.io/design/components/tabs.html\n */\n\n\nclass MatTabGroup extends _MatTabGroupBase {\n  constructor(elementRef, changeDetectorRef, defaultConfig, animationMode) {\n    super(elementRef, changeDetectorRef, defaultConfig, animationMode);\n  }\n\n}\n\nMatTabGroup.ɵfac = function MatTabGroup_Factory(t) {\n  return new (t || MatTabGroup)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_TABS_CONFIG, 8), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n};\n\nMatTabGroup.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatTabGroup,\n  selectors: [[\"mat-tab-group\"]],\n  contentQueries: function MatTabGroup_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MatTab, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allTabs = _t);\n    }\n  },\n  viewQuery: function MatTabGroup_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c8, 5);\n      i0.ɵɵviewQuery(_c9, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabBodyWrapper = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabHeader = _t.first);\n    }\n  },\n  hostAttrs: [1, \"mat-tab-group\"],\n  hostVars: 4,\n  hostBindings: function MatTabGroup_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-tab-group-dynamic-height\", ctx.dynamicHeight)(\"mat-tab-group-inverted-header\", ctx.headerPosition === \"below\");\n    }\n  },\n  inputs: {\n    color: \"color\",\n    disableRipple: \"disableRipple\"\n  },\n  exportAs: [\"matTabGroup\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_TAB_GROUP,\n    useExisting: MatTabGroup\n  }]), i0.ɵɵInheritDefinitionFeature],\n  decls: 6,\n  vars: 7,\n  consts: [[3, \"selectedIndex\", \"disableRipple\", \"disablePagination\", \"indexFocused\", \"selectFocusedIndex\"], [\"tabHeader\", \"\"], [\"class\", \"mat-tab-label mat-focus-indicator\", \"role\", \"tab\", \"matTabLabelWrapper\", \"\", \"mat-ripple\", \"\", \"cdkMonitorElementFocus\", \"\", 3, \"id\", \"mat-tab-label-active\", \"ngClass\", \"disabled\", \"matRippleDisabled\", \"click\", \"cdkFocusChange\", 4, \"ngFor\", \"ngForOf\"], [1, \"mat-tab-body-wrapper\"], [\"tabBodyWrapper\", \"\"], [\"role\", \"tabpanel\", 3, \"id\", \"mat-tab-body-active\", \"ngClass\", \"content\", \"position\", \"origin\", \"animationDuration\", \"preserveContent\", \"_onCentered\", \"_onCentering\", 4, \"ngFor\", \"ngForOf\"], [\"role\", \"tab\", \"matTabLabelWrapper\", \"\", \"mat-ripple\", \"\", \"cdkMonitorElementFocus\", \"\", 1, \"mat-tab-label\", \"mat-focus-indicator\", 3, \"id\", \"ngClass\", \"disabled\", \"matRippleDisabled\", \"click\", \"cdkFocusChange\"], [1, \"mat-tab-label-content\"], [3, \"ngIf\", \"ngIfElse\"], [\"tabTextLabel\", \"\"], [3, \"cdkPortalOutlet\"], [\"role\", \"tabpanel\", 3, \"id\", \"ngClass\", \"content\", \"position\", \"origin\", \"animationDuration\", \"preserveContent\", \"_onCentered\", \"_onCentering\"]],\n  template: function MatTabGroup_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"mat-tab-header\", 0, 1);\n      i0.ɵɵlistener(\"indexFocused\", function MatTabGroup_Template_mat_tab_header_indexFocused_0_listener($event) {\n        return ctx._focusChanged($event);\n      })(\"selectFocusedIndex\", function MatTabGroup_Template_mat_tab_header_selectFocusedIndex_0_listener($event) {\n        return ctx.selectedIndex = $event;\n      });\n      i0.ɵɵtemplate(2, MatTabGroup_div_2_Template, 5, 15, \"div\", 2);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(3, \"div\", 3, 4);\n      i0.ɵɵtemplate(5, MatTabGroup_mat_tab_body_5_Template, 1, 11, \"mat-tab-body\", 5);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"selectedIndex\", ctx.selectedIndex || 0)(\"disableRipple\", ctx.disableRipple)(\"disablePagination\", ctx.disablePagination);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngForOf\", ctx._tabs);\n      i0.ɵɵadvance(1);\n      i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngForOf\", ctx._tabs);\n    }\n  },\n  dependencies: [i1$2.NgClass, i1$2.NgForOf, i1$2.NgIf, i2.CdkPortalOutlet, i4.MatRipple, i7.CdkMonitorFocus, MatTabLabelWrapper, MatTabBody, MatTabHeader],\n  styles: [\".mat-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-tab-group.mat-tab-group-inverted-header{flex-direction:column-reverse}.mat-tab-label{height:48px;padding:0 24px;cursor:pointer;box-sizing:border-box;opacity:.6;min-width:160px;text-align:center;display:inline-flex;justify-content:center;align-items:center;white-space:nowrap;position:relative}.mat-tab-label:focus{outline:none}.mat-tab-label:focus:not(.mat-tab-disabled){opacity:1}.mat-tab-label.mat-tab-disabled{cursor:default}.cdk-high-contrast-active .mat-tab-label.mat-tab-disabled{opacity:.5}.mat-tab-label .mat-tab-label-content{display:inline-flex;justify-content:center;align-items:center;white-space:nowrap}.cdk-high-contrast-active .mat-tab-label{opacity:1}@media(max-width: 599px){.mat-tab-label{padding:0 12px}}@media(max-width: 959px){.mat-tab-label{padding:0 12px}}.mat-tab-group[mat-stretch-tabs]>.mat-tab-header .mat-tab-label{flex-basis:0;flex-grow:1}.mat-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}.mat-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-tab-body.mat-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-tab-group.mat-tab-group-dynamic-height .mat-tab-body.mat-tab-body-active{overflow-y:hidden}\"],\n  encapsulation: 2\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabGroup, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab-group',\n      exportAs: 'matTabGroup',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      inputs: ['color', 'disableRipple'],\n      providers: [{\n        provide: MAT_TAB_GROUP,\n        useExisting: MatTabGroup\n      }],\n      host: {\n        'class': 'mat-tab-group',\n        '[class.mat-tab-group-dynamic-height]': 'dynamicHeight',\n        '[class.mat-tab-group-inverted-header]': 'headerPosition === \"below\"'\n      },\n      template: \"<mat-tab-header #tabHeader\\n               [selectedIndex]=\\\"selectedIndex || 0\\\"\\n               [disableRipple]=\\\"disableRipple\\\"\\n               [disablePagination]=\\\"disablePagination\\\"\\n               (indexFocused)=\\\"_focusChanged($event)\\\"\\n               (selectFocusedIndex)=\\\"selectedIndex = $event\\\">\\n  <div class=\\\"mat-tab-label mat-focus-indicator\\\" role=\\\"tab\\\" matTabLabelWrapper mat-ripple\\n       cdkMonitorElementFocus\\n       *ngFor=\\\"let tab of _tabs; let i = index\\\"\\n       [id]=\\\"_getTabLabelId(i)\\\"\\n       [attr.tabIndex]=\\\"_getTabIndex(tab, i)\\\"\\n       [attr.aria-posinset]=\\\"i + 1\\\"\\n       [attr.aria-setsize]=\\\"_tabs.length\\\"\\n       [attr.aria-controls]=\\\"_getTabContentId(i)\\\"\\n       [attr.aria-selected]=\\\"selectedIndex === i\\\"\\n       [attr.aria-label]=\\\"tab.ariaLabel || null\\\"\\n       [attr.aria-labelledby]=\\\"(!tab.ariaLabel && tab.ariaLabelledby) ? tab.ariaLabelledby : null\\\"\\n       [class.mat-tab-label-active]=\\\"selectedIndex === i\\\"\\n       [ngClass]=\\\"tab.labelClass\\\"\\n       [disabled]=\\\"tab.disabled\\\"\\n       [matRippleDisabled]=\\\"tab.disabled || disableRipple\\\"\\n       (click)=\\\"_handleClick(tab, tabHeader, i)\\\"\\n       (cdkFocusChange)=\\\"_tabFocusChanged($event, i)\\\">\\n\\n\\n    <div class=\\\"mat-tab-label-content\\\">\\n      <!-- If there is a label template, use it. -->\\n      <ng-template [ngIf]=\\\"tab.templateLabel\\\" [ngIfElse]=\\\"tabTextLabel\\\">\\n        <ng-template [cdkPortalOutlet]=\\\"tab.templateLabel\\\"></ng-template>\\n      </ng-template>\\n\\n      <!-- If there is not a label template, fall back to the text label. -->\\n      <ng-template #tabTextLabel>{{tab.textLabel}}</ng-template>\\n    </div>\\n  </div>\\n</mat-tab-header>\\n\\n<div\\n  class=\\\"mat-tab-body-wrapper\\\"\\n  [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\"\\n  #tabBodyWrapper>\\n  <mat-tab-body role=\\\"tabpanel\\\"\\n               *ngFor=\\\"let tab of _tabs; let i = index\\\"\\n               [id]=\\\"_getTabContentId(i)\\\"\\n               [attr.tabindex]=\\\"(contentTabIndex != null && selectedIndex === i) ? contentTabIndex : null\\\"\\n               [attr.aria-labelledby]=\\\"_getTabLabelId(i)\\\"\\n               [class.mat-tab-body-active]=\\\"selectedIndex === i\\\"\\n               [ngClass]=\\\"tab.bodyClass\\\"\\n               [content]=\\\"tab.content!\\\"\\n               [position]=\\\"tab.position!\\\"\\n               [origin]=\\\"tab.origin\\\"\\n               [animationDuration]=\\\"animationDuration\\\"\\n               [preserveContent]=\\\"preserveContent\\\"\\n               (_onCentered)=\\\"_removeTabBodyWrapperHeight()\\\"\\n               (_onCentering)=\\\"_setTabBodyWrapperHeight($event)\\\">\\n  </mat-tab-body>\\n</div>\\n\",\n      styles: [\".mat-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-tab-group.mat-tab-group-inverted-header{flex-direction:column-reverse}.mat-tab-label{height:48px;padding:0 24px;cursor:pointer;box-sizing:border-box;opacity:.6;min-width:160px;text-align:center;display:inline-flex;justify-content:center;align-items:center;white-space:nowrap;position:relative}.mat-tab-label:focus{outline:none}.mat-tab-label:focus:not(.mat-tab-disabled){opacity:1}.mat-tab-label.mat-tab-disabled{cursor:default}.cdk-high-contrast-active .mat-tab-label.mat-tab-disabled{opacity:.5}.mat-tab-label .mat-tab-label-content{display:inline-flex;justify-content:center;align-items:center;white-space:nowrap}.cdk-high-contrast-active .mat-tab-label{opacity:1}@media(max-width: 599px){.mat-tab-label{padding:0 12px}}@media(max-width: 959px){.mat-tab-label{padding:0 12px}}.mat-tab-group[mat-stretch-tabs]>.mat-tab-header .mat-tab-label{flex-basis:0;flex-grow:1}.mat-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}.mat-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-tab-body.mat-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-tab-group.mat-tab-group-dynamic-height .mat-tab-body.mat-tab-body-active{overflow-y:hidden}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_TABS_CONFIG]\n      }, {\n        type: Optional\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    _allTabs: [{\n      type: ContentChildren,\n      args: [MatTab, {\n        descendants: true\n      }]\n    }],\n    _tabBodyWrapper: [{\n      type: ViewChild,\n      args: ['tabBodyWrapper']\n    }],\n    _tabHeader: [{\n      type: ViewChild,\n      args: ['tabHeader']\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Increasing integer for generating unique ids for tab nav components.\n\n\nlet nextUniqueId = 0;\n/**\n * Base class with all of the `MatTabNav` functionality.\n * @docs-private\n */\n\nclass _MatTabNavBase extends MatPaginatedTabHeader {\n  constructor(elementRef, dir, ngZone, changeDetectorRef, viewportRuler, platform, animationMode) {\n    super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n    this._disableRipple = false;\n    /** Theme color of the nav bar. */\n\n    this.color = 'primary';\n  }\n  /** Background color of the tab nav. */\n\n\n  get backgroundColor() {\n    return this._backgroundColor;\n  }\n\n  set backgroundColor(value) {\n    const classList = this._elementRef.nativeElement.classList;\n    classList.remove(`mat-background-${this.backgroundColor}`);\n\n    if (value) {\n      classList.add(`mat-background-${value}`);\n    }\n\n    this._backgroundColor = value;\n  }\n  /** Whether the ripple effect is disabled or not. */\n\n\n  get disableRipple() {\n    return this._disableRipple;\n  }\n\n  set disableRipple(value) {\n    this._disableRipple = coerceBooleanProperty(value);\n  }\n\n  _itemSelected() {// noop\n  }\n\n  ngAfterContentInit() {\n    // We need this to run before the `changes` subscription in parent to ensure that the\n    // selectedIndex is up-to-date by the time the super class starts looking for it.\n    this._items.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n      this.updateActiveLink();\n    });\n\n    super.ngAfterContentInit();\n  }\n  /** Notifies the component that the active link has been changed. */\n\n\n  updateActiveLink() {\n    if (!this._items) {\n      return;\n    }\n\n    const items = this._items.toArray();\n\n    for (let i = 0; i < items.length; i++) {\n      if (items[i].active) {\n        this.selectedIndex = i;\n\n        this._changeDetectorRef.markForCheck();\n\n        if (this.tabPanel) {\n          this.tabPanel._activeTabId = items[i].id;\n        }\n\n        return;\n      }\n    } // The ink bar should hide itself if no items are active.\n\n\n    this.selectedIndex = -1;\n\n    this._inkBar.hide();\n  }\n\n  _getRole() {\n    return this.tabPanel ? 'tablist' : this._elementRef.nativeElement.getAttribute('role');\n  }\n\n}\n\n_MatTabNavBase.ɵfac = function _MatTabNavBase_Factory(t) {\n  return new (t || _MatTabNavBase)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.ViewportRuler), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n};\n\n_MatTabNavBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatTabNavBase,\n  inputs: {\n    backgroundColor: \"backgroundColor\",\n    disableRipple: \"disableRipple\",\n    color: \"color\",\n    tabPanel: \"tabPanel\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatTabNavBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1$1.ViewportRuler\n    }, {\n      type: i3.Platform\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    backgroundColor: [{\n      type: Input\n    }],\n    disableRipple: [{\n      type: Input\n    }],\n    color: [{\n      type: Input\n    }],\n    tabPanel: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Navigation component matching the styles of the tab group header.\n * Provides anchored navigation with animated ink bar.\n */\n\n\nclass MatTabNav extends _MatTabNavBase {\n  constructor(elementRef, dir, ngZone, changeDetectorRef, viewportRuler, platform, animationMode) {\n    super(elementRef, dir, ngZone, changeDetectorRef, viewportRuler, platform, animationMode);\n  }\n\n}\n\nMatTabNav.ɵfac = function MatTabNav_Factory(t) {\n  return new (t || MatTabNav)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.ViewportRuler), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n};\n\nMatTabNav.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatTabNav,\n  selectors: [[\"\", \"mat-tab-nav-bar\", \"\"]],\n  contentQueries: function MatTabNav_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MatTabLink, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._items = _t);\n    }\n  },\n  viewQuery: function MatTabNav_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(MatInkBar, 7);\n      i0.ɵɵviewQuery(_c3, 7);\n      i0.ɵɵviewQuery(_c4, 7);\n      i0.ɵɵviewQuery(_c5, 7);\n      i0.ɵɵviewQuery(_c6, 5);\n      i0.ɵɵviewQuery(_c7, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._inkBar = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListContainer = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabList = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListInner = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nextPaginator = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._previousPaginator = _t.first);\n    }\n  },\n  hostAttrs: [1, \"mat-tab-nav-bar\", \"mat-tab-header\"],\n  hostVars: 11,\n  hostBindings: function MatTabNav_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"role\", ctx._getRole());\n      i0.ɵɵclassProp(\"mat-tab-header-pagination-controls-enabled\", ctx._showPaginationControls)(\"mat-tab-header-rtl\", ctx._getLayoutDirection() == \"rtl\")(\"mat-primary\", ctx.color !== \"warn\" && ctx.color !== \"accent\")(\"mat-accent\", ctx.color === \"accent\")(\"mat-warn\", ctx.color === \"warn\");\n    }\n  },\n  inputs: {\n    color: \"color\"\n  },\n  exportAs: [\"matTabNavBar\", \"matTabNav\"],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  attrs: _c10,\n  ngContentSelectors: _c0,\n  decls: 14,\n  vars: 10,\n  consts: [[\"aria-hidden\", \"true\", \"type\", \"button\", \"mat-ripple\", \"\", \"tabindex\", \"-1\", 1, \"mat-tab-header-pagination\", \"mat-tab-header-pagination-before\", \"mat-elevation-z4\", 3, \"matRippleDisabled\", \"disabled\", \"click\", \"mousedown\", \"touchend\"], [\"previousPaginator\", \"\"], [1, \"mat-tab-header-pagination-chevron\"], [1, \"mat-tab-link-container\", 3, \"keydown\"], [\"tabListContainer\", \"\"], [1, \"mat-tab-list\", 3, \"cdkObserveContent\"], [\"tabList\", \"\"], [1, \"mat-tab-links\"], [\"tabListInner\", \"\"], [\"aria-hidden\", \"true\", \"type\", \"button\", \"mat-ripple\", \"\", \"tabindex\", \"-1\", 1, \"mat-tab-header-pagination\", \"mat-tab-header-pagination-after\", \"mat-elevation-z4\", 3, \"matRippleDisabled\", \"disabled\", \"mousedown\", \"click\", \"touchend\"], [\"nextPaginator\", \"\"]],\n  template: function MatTabNav_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"button\", 0, 1);\n      i0.ɵɵlistener(\"click\", function MatTabNav_Template_button_click_0_listener() {\n        return ctx._handlePaginatorClick(\"before\");\n      })(\"mousedown\", function MatTabNav_Template_button_mousedown_0_listener($event) {\n        return ctx._handlePaginatorPress(\"before\", $event);\n      })(\"touchend\", function MatTabNav_Template_button_touchend_0_listener() {\n        return ctx._stopInterval();\n      });\n      i0.ɵɵelement(2, \"div\", 2);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(3, \"div\", 3, 4);\n      i0.ɵɵlistener(\"keydown\", function MatTabNav_Template_div_keydown_3_listener($event) {\n        return ctx._handleKeydown($event);\n      });\n      i0.ɵɵelementStart(5, \"div\", 5, 6);\n      i0.ɵɵlistener(\"cdkObserveContent\", function MatTabNav_Template_div_cdkObserveContent_5_listener() {\n        return ctx._onContentChanges();\n      });\n      i0.ɵɵelementStart(7, \"div\", 7, 8);\n      i0.ɵɵprojection(9);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(10, \"mat-ink-bar\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(11, \"button\", 9, 10);\n      i0.ɵɵlistener(\"mousedown\", function MatTabNav_Template_button_mousedown_11_listener($event) {\n        return ctx._handlePaginatorPress(\"after\", $event);\n      })(\"click\", function MatTabNav_Template_button_click_11_listener() {\n        return ctx._handlePaginatorClick(\"after\");\n      })(\"touchend\", function MatTabNav_Template_button_touchend_11_listener() {\n        return ctx._stopInterval();\n      });\n      i0.ɵɵelement(13, \"div\", 2);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-tab-header-pagination-disabled\", ctx._disableScrollBefore);\n      i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollBefore || ctx.disableRipple)(\"disabled\", ctx._disableScrollBefore || null);\n      i0.ɵɵadvance(5);\n      i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n      i0.ɵɵadvance(6);\n      i0.ɵɵclassProp(\"mat-tab-header-pagination-disabled\", ctx._disableScrollAfter);\n      i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollAfter || ctx.disableRipple)(\"disabled\", ctx._disableScrollAfter || null);\n    }\n  },\n  dependencies: [i4.MatRipple, i5.CdkObserveContent, MatInkBar],\n  styles: [\".mat-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mat-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-tab-header-pagination::-moz-focus-inner{border:0}.mat-tab-header-pagination-controls-enabled .mat-tab-header-pagination{display:flex}.mat-tab-header-pagination-before,.mat-tab-header-rtl .mat-tab-header-pagination-after{padding-left:4px}.mat-tab-header-pagination-before .mat-tab-header-pagination-chevron,.mat-tab-header-rtl .mat-tab-header-pagination-after .mat-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-tab-header-rtl .mat-tab-header-pagination-before,.mat-tab-header-pagination-after{padding-right:4px}.mat-tab-header-rtl .mat-tab-header-pagination-before .mat-tab-header-pagination-chevron,.mat-tab-header-pagination-after .mat-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px}.mat-tab-header-pagination-disabled{box-shadow:none;cursor:default}.mat-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-tab-links{display:flex}[mat-align-tabs=center]>.mat-tab-link-container .mat-tab-links{justify-content:center}[mat-align-tabs=end]>.mat-tab-link-container .mat-tab-links{justify-content:flex-end}.mat-ink-bar{position:absolute;bottom:0;height:2px;transition:500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-ink-bar._mat-animation-noopable{transition:none !important;animation:none !important}.mat-tab-group-inverted-header .mat-ink-bar{bottom:auto;top:0}.cdk-high-contrast-active .mat-ink-bar{outline:solid 2px;height:0}.mat-tab-link-container{display:flex;flex-grow:1;overflow:hidden;z-index:1}.mat-tab-link{height:48px;padding:0 24px;cursor:pointer;box-sizing:border-box;opacity:.6;min-width:160px;text-align:center;display:inline-flex;justify-content:center;align-items:center;white-space:nowrap;vertical-align:top;text-decoration:none;position:relative;overflow:hidden;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-tab-link:focus{outline:none}.mat-tab-link:focus:not(.mat-tab-disabled){opacity:1}.mat-tab-link.mat-tab-disabled{cursor:default}.cdk-high-contrast-active .mat-tab-link.mat-tab-disabled{opacity:.5}.mat-tab-link .mat-tab-label-content{display:inline-flex;justify-content:center;align-items:center;white-space:nowrap}.cdk-high-contrast-active .mat-tab-link{opacity:1}[mat-stretch-tabs] .mat-tab-link{flex-basis:0;flex-grow:1}.mat-tab-link.mat-tab-disabled{pointer-events:none}.mat-tab-link::before{margin:5px}@media(max-width: 599px){.mat-tab-link{min-width:72px}}\"],\n  encapsulation: 2\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabNav, [{\n    type: Component,\n    args: [{\n      selector: '[mat-tab-nav-bar]',\n      exportAs: 'matTabNavBar, matTabNav',\n      inputs: ['color'],\n      host: {\n        '[attr.role]': '_getRole()',\n        'class': 'mat-tab-nav-bar mat-tab-header',\n        '[class.mat-tab-header-pagination-controls-enabled]': '_showPaginationControls',\n        '[class.mat-tab-header-rtl]': \"_getLayoutDirection() == 'rtl'\",\n        '[class.mat-primary]': 'color !== \"warn\" && color !== \"accent\"',\n        '[class.mat-accent]': 'color === \"accent\"',\n        '[class.mat-warn]': 'color === \"warn\"'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      template: \"<button class=\\\"mat-tab-header-pagination mat-tab-header-pagination-before mat-elevation-z4\\\"\\n     #previousPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     tabindex=\\\"-1\\\"\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     [disabled]=\\\"_disableScrollBefore || null\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\\n<div class=\\\"mat-tab-link-container\\\" #tabListContainer (keydown)=\\\"_handleKeydown($event)\\\">\\n  <div\\n    class=\\\"mat-tab-list\\\"\\n    [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\"\\n    #tabList\\n    (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-tab-links\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n    <mat-ink-bar></mat-ink-bar>\\n  </div>\\n</div>\\n\\n<button class=\\\"mat-tab-header-pagination mat-tab-header-pagination-after mat-elevation-z4\\\"\\n     #nextPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     [disabled]=\\\"_disableScrollAfter || null\\\"\\n     tabindex=\\\"-1\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\",\n      styles: [\".mat-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mat-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-tab-header-pagination::-moz-focus-inner{border:0}.mat-tab-header-pagination-controls-enabled .mat-tab-header-pagination{display:flex}.mat-tab-header-pagination-before,.mat-tab-header-rtl .mat-tab-header-pagination-after{padding-left:4px}.mat-tab-header-pagination-before .mat-tab-header-pagination-chevron,.mat-tab-header-rtl .mat-tab-header-pagination-after .mat-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-tab-header-rtl .mat-tab-header-pagination-before,.mat-tab-header-pagination-after{padding-right:4px}.mat-tab-header-rtl .mat-tab-header-pagination-before .mat-tab-header-pagination-chevron,.mat-tab-header-pagination-after .mat-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px}.mat-tab-header-pagination-disabled{box-shadow:none;cursor:default}.mat-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-tab-links{display:flex}[mat-align-tabs=center]>.mat-tab-link-container .mat-tab-links{justify-content:center}[mat-align-tabs=end]>.mat-tab-link-container .mat-tab-links{justify-content:flex-end}.mat-ink-bar{position:absolute;bottom:0;height:2px;transition:500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-ink-bar._mat-animation-noopable{transition:none !important;animation:none !important}.mat-tab-group-inverted-header .mat-ink-bar{bottom:auto;top:0}.cdk-high-contrast-active .mat-ink-bar{outline:solid 2px;height:0}.mat-tab-link-container{display:flex;flex-grow:1;overflow:hidden;z-index:1}.mat-tab-link{height:48px;padding:0 24px;cursor:pointer;box-sizing:border-box;opacity:.6;min-width:160px;text-align:center;display:inline-flex;justify-content:center;align-items:center;white-space:nowrap;vertical-align:top;text-decoration:none;position:relative;overflow:hidden;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-tab-link:focus{outline:none}.mat-tab-link:focus:not(.mat-tab-disabled){opacity:1}.mat-tab-link.mat-tab-disabled{cursor:default}.cdk-high-contrast-active .mat-tab-link.mat-tab-disabled{opacity:.5}.mat-tab-link .mat-tab-label-content{display:inline-flex;justify-content:center;align-items:center;white-space:nowrap}.cdk-high-contrast-active .mat-tab-link{opacity:1}[mat-stretch-tabs] .mat-tab-link{flex-basis:0;flex-grow:1}.mat-tab-link.mat-tab-disabled{pointer-events:none}.mat-tab-link::before{margin:5px}@media(max-width: 599px){.mat-tab-link{min-width:72px}}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1$1.ViewportRuler\n    }, {\n      type: i3.Platform\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    _items: [{\n      type: ContentChildren,\n      args: [forwardRef(() => MatTabLink), {\n        descendants: true\n      }]\n    }],\n    _inkBar: [{\n      type: ViewChild,\n      args: [MatInkBar, {\n        static: true\n      }]\n    }],\n    _tabListContainer: [{\n      type: ViewChild,\n      args: ['tabListContainer', {\n        static: true\n      }]\n    }],\n    _tabList: [{\n      type: ViewChild,\n      args: ['tabList', {\n        static: true\n      }]\n    }],\n    _tabListInner: [{\n      type: ViewChild,\n      args: ['tabListInner', {\n        static: true\n      }]\n    }],\n    _nextPaginator: [{\n      type: ViewChild,\n      args: ['nextPaginator']\n    }],\n    _previousPaginator: [{\n      type: ViewChild,\n      args: ['previousPaginator']\n    }]\n  });\n})(); // Boilerplate for applying mixins to MatTabLink.\n\n\nconst _MatTabLinkMixinBase = mixinTabIndex(mixinDisableRipple(mixinDisabled(class {})));\n/** Base class with all of the `MatTabLink` functionality. */\n\n\nclass _MatTabLinkBase extends _MatTabLinkMixinBase {\n  constructor(_tabNavBar,\n  /** @docs-private */\n  elementRef, globalRippleOptions, tabIndex, _focusMonitor, animationMode) {\n    super();\n    this._tabNavBar = _tabNavBar;\n    this.elementRef = elementRef;\n    this._focusMonitor = _focusMonitor;\n    /** Whether the tab link is active or not. */\n\n    this._isActive = false;\n    /** Unique id for the tab. */\n\n    this.id = `mat-tab-link-${nextUniqueId++}`;\n    this.rippleConfig = globalRippleOptions || {};\n    this.tabIndex = parseInt(tabIndex) || 0;\n\n    if (animationMode === 'NoopAnimations') {\n      this.rippleConfig.animation = {\n        enterDuration: 0,\n        exitDuration: 0\n      };\n    }\n  }\n  /** Whether the link is active. */\n\n\n  get active() {\n    return this._isActive;\n  }\n\n  set active(value) {\n    const newValue = coerceBooleanProperty(value);\n\n    if (newValue !== this._isActive) {\n      this._isActive = newValue;\n\n      this._tabNavBar.updateActiveLink();\n    }\n  }\n  /**\n   * Whether ripples are disabled on interaction.\n   * @docs-private\n   */\n\n\n  get rippleDisabled() {\n    return this.disabled || this.disableRipple || this._tabNavBar.disableRipple || !!this.rippleConfig.disabled;\n  }\n  /** Focuses the tab link. */\n\n\n  focus() {\n    this.elementRef.nativeElement.focus();\n  }\n\n  ngAfterViewInit() {\n    this._focusMonitor.monitor(this.elementRef);\n  }\n\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this.elementRef);\n  }\n\n  _handleFocus() {\n    // Since we allow navigation through tabbing in the nav bar, we\n    // have to update the focused index whenever the link receives focus.\n    this._tabNavBar.focusIndex = this._tabNavBar._items.toArray().indexOf(this);\n  }\n\n  _handleKeydown(event) {\n    if (this._tabNavBar.tabPanel && event.keyCode === SPACE) {\n      this.elementRef.nativeElement.click();\n    }\n  }\n\n  _getAriaControls() {\n    return this._tabNavBar.tabPanel ? this._tabNavBar.tabPanel?.id : this.elementRef.nativeElement.getAttribute('aria-controls');\n  }\n\n  _getAriaSelected() {\n    if (this._tabNavBar.tabPanel) {\n      return this.active ? 'true' : 'false';\n    } else {\n      return this.elementRef.nativeElement.getAttribute('aria-selected');\n    }\n  }\n\n  _getAriaCurrent() {\n    return this.active && !this._tabNavBar.tabPanel ? 'page' : null;\n  }\n\n  _getRole() {\n    return this._tabNavBar.tabPanel ? 'tab' : this.elementRef.nativeElement.getAttribute('role');\n  }\n\n  _getTabIndex() {\n    if (this._tabNavBar.tabPanel) {\n      return this._isActive && !this.disabled ? 0 : -1;\n    } else {\n      return this.tabIndex;\n    }\n  }\n\n}\n\n_MatTabLinkBase.ɵfac = function _MatTabLinkBase_Factory(t) {\n  return new (t || _MatTabLinkBase)(i0.ɵɵdirectiveInject(_MatTabNavBase), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MAT_RIPPLE_GLOBAL_OPTIONS, 8), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(i7.FocusMonitor), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n};\n\n_MatTabLinkBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatTabLinkBase,\n  inputs: {\n    active: \"active\",\n    id: \"id\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatTabLinkBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: _MatTabNavBase\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_RIPPLE_GLOBAL_OPTIONS]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Attribute,\n        args: ['tabindex']\n      }]\n    }, {\n      type: i7.FocusMonitor\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    active: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Link inside of a `mat-tab-nav-bar`.\n */\n\n\nclass MatTabLink extends _MatTabLinkBase {\n  constructor(tabNavBar, elementRef, ngZone, platform, globalRippleOptions, tabIndex, focusMonitor, animationMode) {\n    super(tabNavBar, elementRef, globalRippleOptions, tabIndex, focusMonitor, animationMode);\n    this._tabLinkRipple = new RippleRenderer(this, ngZone, elementRef, platform);\n\n    this._tabLinkRipple.setupTriggerEvents(elementRef.nativeElement);\n  }\n\n  ngOnDestroy() {\n    super.ngOnDestroy();\n\n    this._tabLinkRipple._removeTriggerEvents();\n  }\n\n}\n\nMatTabLink.ɵfac = function MatTabLink_Factory(t) {\n  return new (t || MatTabLink)(i0.ɵɵdirectiveInject(MatTabNav), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(MAT_RIPPLE_GLOBAL_OPTIONS, 8), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(i7.FocusMonitor), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n};\n\nMatTabLink.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatTabLink,\n  selectors: [[\"\", \"mat-tab-link\", \"\"], [\"\", \"matTabLink\", \"\"]],\n  hostAttrs: [1, \"mat-tab-link\", \"mat-focus-indicator\"],\n  hostVars: 11,\n  hostBindings: function MatTabLink_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"focus\", function MatTabLink_focus_HostBindingHandler() {\n        return ctx._handleFocus();\n      })(\"keydown\", function MatTabLink_keydown_HostBindingHandler($event) {\n        return ctx._handleKeydown($event);\n      });\n    }\n\n    if (rf & 2) {\n      i0.ɵɵattribute(\"aria-controls\", ctx._getAriaControls())(\"aria-current\", ctx._getAriaCurrent())(\"aria-disabled\", ctx.disabled)(\"aria-selected\", ctx._getAriaSelected())(\"id\", ctx.id)(\"tabIndex\", ctx._getTabIndex())(\"role\", ctx._getRole());\n      i0.ɵɵclassProp(\"mat-tab-disabled\", ctx.disabled)(\"mat-tab-label-active\", ctx.active);\n    }\n  },\n  inputs: {\n    disabled: \"disabled\",\n    disableRipple: \"disableRipple\",\n    tabIndex: \"tabIndex\"\n  },\n  exportAs: [\"matTabLink\"],\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabLink, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-tab-link], [matTabLink]',\n      exportAs: 'matTabLink',\n      inputs: ['disabled', 'disableRipple', 'tabIndex'],\n      host: {\n        'class': 'mat-tab-link mat-focus-indicator',\n        '[attr.aria-controls]': '_getAriaControls()',\n        '[attr.aria-current]': '_getAriaCurrent()',\n        '[attr.aria-disabled]': 'disabled',\n        '[attr.aria-selected]': '_getAriaSelected()',\n        '[attr.id]': 'id',\n        '[attr.tabIndex]': '_getTabIndex()',\n        '[attr.role]': '_getRole()',\n        '[class.mat-tab-disabled]': 'disabled',\n        '[class.mat-tab-label-active]': 'active',\n        '(focus)': '_handleFocus()',\n        '(keydown)': '_handleKeydown($event)'\n      }\n    }]\n  }], function () {\n    return [{\n      type: MatTabNav\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i3.Platform\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_RIPPLE_GLOBAL_OPTIONS]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Attribute,\n        args: ['tabindex']\n      }]\n    }, {\n      type: i7.FocusMonitor\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Tab panel component associated with MatTabNav.\n */\n\n\nclass MatTabNavPanel {\n  constructor() {\n    /** Unique id for the tab panel. */\n    this.id = `mat-tab-nav-panel-${nextUniqueId++}`;\n  }\n\n}\n\nMatTabNavPanel.ɵfac = function MatTabNavPanel_Factory(t) {\n  return new (t || MatTabNavPanel)();\n};\n\nMatTabNavPanel.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatTabNavPanel,\n  selectors: [[\"mat-tab-nav-panel\"]],\n  hostAttrs: [\"role\", \"tabpanel\", 1, \"mat-tab-nav-panel\"],\n  hostVars: 2,\n  hostBindings: function MatTabNavPanel_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"aria-labelledby\", ctx._activeTabId)(\"id\", ctx.id);\n    }\n  },\n  inputs: {\n    id: \"id\"\n  },\n  exportAs: [\"matTabNavPanel\"],\n  ngContentSelectors: _c0,\n  decls: 1,\n  vars: 0,\n  template: function MatTabNavPanel_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵprojection(0);\n    }\n  },\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabNavPanel, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab-nav-panel',\n      exportAs: 'matTabNavPanel',\n      template: '<ng-content></ng-content>',\n      host: {\n        '[attr.aria-labelledby]': '_activeTabId',\n        '[attr.id]': 'id',\n        'class': 'mat-tab-nav-panel',\n        'role': 'tabpanel'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, {\n    id: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass MatTabsModule {}\n\nMatTabsModule.ɵfac = function MatTabsModule_Factory(t) {\n  return new (t || MatTabsModule)();\n};\n\nMatTabsModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatTabsModule\n});\nMatTabsModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, MatCommonModule, PortalModule, MatRippleModule, ObserversModule, A11yModule, MatCommonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabsModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, MatCommonModule, PortalModule, MatRippleModule, ObserversModule, A11yModule],\n      // Don't export all components because some are only to be used internally.\n      exports: [MatCommonModule, MatTabGroup, MatTabLabel, MatTab, MatTabNav, MatTabNavPanel, MatTabLink, MatTabContent],\n      declarations: [MatTabGroup, MatTabLabel, MatTab, MatInkBar, MatTabLabelWrapper, MatTabNav, MatTabNavPanel, MatTabLink, MatTabBody, MatTabBodyPortal, MatTabHeader, MatTabContent]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { MAT_TAB, MAT_TABS_CONFIG, MAT_TAB_GROUP, MatInkBar, MatTab, MatTabBody, MatTabBodyPortal, MatTabChangeEvent, MatTabContent, MatTabGroup, MatTabHeader, MatTabLabel, MatTabLabelWrapper, MatTabLink, MatTabNav, MatTabNavPanel, MatTabsModule, _MAT_INK_BAR_POSITIONER, _MatTabBodyBase, _MatTabGroupBase, _MatTabHeaderBase, _MatTabLinkBase, _MatTabNavBase, matTabsAnimations };", "map": {"version": 3, "names": ["i7", "FocusKeyManager", "A11yModule", "i5", "ObserversModule", "i2", "CdkPortal", "TemplatePortal", "CdkPortalOutlet", "PortalModule", "i1$2", "DOCUMENT", "CommonModule", "i0", "InjectionToken", "Directive", "Inject", "Optional", "TemplateRef", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "ContentChild", "ViewChild", "Input", "forwardRef", "EventEmitter", "Output", "ContentChildren", "QueryList", "Attribute", "NgModule", "i4", "mixinDisabled", "mixinColor", "mixinDisableRipple", "mixinTabIndex", "MAT_RIPPLE_GLOBAL_OPTIONS", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MatCommonModule", "MatRippleModule", "ANIMATION_MODULE_TYPE", "take", "startWith", "distinctUntilChanged", "takeUntil", "switchMap", "skip", "filter", "Subject", "Subscription", "fromEvent", "of", "merge", "EMPTY", "Observable", "timer", "i1", "trigger", "state", "style", "transition", "animate", "coerceBooleanProperty", "coerceNumberProperty", "i1$1", "i3", "normalizePassiveListenerOptions", "hasModifierKey", "SPACE", "ENTER", "_MAT_INK_BAR_POSITIONER", "providedIn", "factory", "_MAT_INK_BAR_POSITIONER_FACTORY", "method", "element", "left", "offsetLeft", "width", "offsetWidth", "MatInkBar", "constructor", "_elementRef", "_ngZone", "_inkBarPositioner", "_animationMode", "alignToElement", "show", "run", "onStable", "pipe", "subscribe", "positions", "inkBar", "nativeElement", "visibility", "hide", "ɵfac", "ElementRef", "NgZone", "ɵdir", "type", "args", "selector", "host", "undefined", "decorators", "MAT_TAB_CONTENT", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "template", "provide", "useExisting", "providers", "MAT_TAB_LABEL", "MAT_TAB", "MatTab<PERSON><PERSON><PERSON>", "templateRef", "viewContainerRef", "_closestTab", "ViewContainerRef", "_MatTabBase", "MAT_TAB_GROUP", "Mat<PERSON><PERSON>", "_viewContainerRef", "_closestTabGroup", "textLabel", "_contentPortal", "_stateChanges", "position", "origin", "isActive", "templateLabel", "_templateLabel", "value", "_setTemplateLabelInput", "content", "ngOnChanges", "changes", "hasOwnProperty", "next", "ngOnDestroy", "complete", "ngOnInit", "_explicitContent", "_implicitContent", "ɵcmp", "inputs", "changeDetection", "<PERSON><PERSON><PERSON>", "encapsulation", "None", "exportAs", "read", "static", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "labelClass", "bodyClass", "matTabsAnimations", "translateTab", "transform", "minHeight", "MatTabBodyPortal", "componentFactoryResolver", "_host", "_document", "_centeringSub", "_leavingSub", "_beforeCentering", "_isCenterPosition", "_position", "isCentering", "has<PERSON>tta<PERSON>", "attach", "_content", "_afterLeavingCenter", "preserve<PERSON><PERSON>nt", "detach", "unsubscribe", "ComponentFactoryResolver", "MatTabBody", "_MatTabBodyBase", "_dir", "changeDetectorRef", "_dirChangeSubscription", "_translateTabComplete", "_onCentering", "_onCentered", "animationDuration", "change", "dir", "_computePositionAnimationState", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "x", "y", "fromState", "toState", "event", "emit", "_positionIndex", "_computePositionFromO<PERSON>in", "_onTranslateTabStarted", "clientHeight", "_getLayoutDirection", "Directionality", "ChangeDetectorRef", "elementRef", "animations", "styles", "_portalHost", "MAT_TABS_CONFIG", "_MatTabLabelWrapperBase", "MatTabLabelWrapper", "focus", "getOffsetLeft", "getOffsetWidth", "passiveEventListenerOptions", "passive", "EXAGGERATED_OVERSCROLL", "HEADER_SCROLL_DELAY", "HEADER_SCROLL_INTERVAL", "MatPaginatedTabHeader", "_changeDetectorRef", "_viewportRuler", "_platform", "_scrollDistance", "_selectedIndexChanged", "_destroyed", "_showPaginationControls", "_disableScrollAfter", "_disableScrollBefore", "_stopScrolling", "_disablePagination", "_selectedIndex", "selectFocusedIndex", "indexFocused", "runOutsideAngular", "_stopInterval", "disablePagination", "selectedIndex", "_keyManager", "updateActiveItem", "ngAfterViewInit", "_previousPaginator", "_handlePaginatorPress", "_nextPaginator", "ngAfterContentInit", "<PERSON><PERSON><PERSON><PERSON>", "resize", "realign", "updatePagination", "_alignInkBarToSelectedTab", "_items", "withHorizontalOrientation", "withHomeAndEnd", "withWrap", "_itemsResized", "Promise", "resolve", "then", "Math", "max", "min", "_getMaxScrollDistance", "newFocusIndex", "_setTabFocus", "ResizeObserver", "tabItems", "observer", "resizeObserver", "entries", "for<PERSON>ach", "item", "observe", "disconnect", "some", "e", "contentRect", "height", "ngAfterContentChecked", "_tabLabelCount", "length", "_scrollToLabel", "_checkScrollingControls", "_scrollDistanceChanged", "_updateTabScrollPosition", "_handleKeydown", "keyCode", "focusIndex", "_itemSelected", "onKeydown", "_onContentChanges", "textContent", "_currentTextContent", "_checkPaginationEnabled", "activeItemIndex", "_isValidIndex", "setActiveItem", "index", "tab", "toArray", "disabled", "tabIndex", "containerEl", "_tabListContainer", "scrollLeft", "scrollWidth", "scrollDistance", "translateX", "_tabList", "round", "TRIDENT", "EDGE", "_scrollTo", "_scrollHeader", "direction", "viewLength", "scrollAmount", "_handlePaginatorClick", "labelIndex", "<PERSON><PERSON><PERSON><PERSON>", "labelBeforePos", "labelAfterPos", "_tabListInner", "beforeVisiblePos", "afterVisiblePos", "isEnabled", "lengthOfTabList", "selectedItem", "<PERSON><PERSON><PERSON><PERSON>W<PERSON><PERSON>", "_inkBar", "mouseEvent", "button", "maxScrollDistance", "distance", "ViewportRuler", "Platform", "_MatTabHeaderBase", "viewportRuler", "ngZone", "platform", "animationMode", "_disableRipple", "disable<PERSON><PERSON><PERSON>", "preventDefault", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CdkObserveContent", "outputs", "descendants", "nextId", "MatTabChangeEvent", "_MatTabGroupMixinBase", "_MatTabGroupBase", "defaultConfig", "_tabs", "_indexToSelect", "_lastFocusedTabIndex", "_tabBodyWrapperHeight", "_tabsSubscription", "_tabLabelSubscription", "_dynamicHeight", "headerPosition", "_preserveContent", "selectedIndexChange", "focusChange", "animationDone", "selectedTabChange", "_groupId", "dynamicHeight", "contentTabIndex", "_animationDuration", "test", "_contentTabIndex", "backgroundColor", "_backgroundColor", "classList", "remove", "add", "indexToSelect", "_clampTabIndex", "isFirstRun", "_createChangeEvent", "wrapper", "_tabBodyWrapper", "_subscribeToAllTabChanges", "_subscribeToTabLabels", "tabs", "selectedTab", "i", "_allTabs", "reset", "notifyOn<PERSON><PERSON>es", "destroy", "realignInkBar", "_tabHeader", "focusTab", "header", "_focusChanged", "map", "_getTabLabelId", "_getTabContentId", "_setTabBodyWrapperHeight", "tabHeight", "offsetHeight", "_removeTabBodyWrapperHeight", "_handleClick", "tabHeader", "_getTabIndex", "targetIndex", "_tabFocusChanged", "<PERSON><PERSON><PERSON><PERSON>", "MatTabGroup", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "CdkMonitorFocus", "nextUniqueId", "_MatTabNavBase", "color", "updateActiveLink", "items", "active", "tabPanel", "_activeTabId", "id", "_getRole", "getAttribute", "MatTabNav", "MatTabLink", "_MatTabLinkMixinBase", "_MatTabLinkBase", "_tabNavBar", "globalRippleOptions", "_focusMonitor", "_isActive", "rippleConfig", "parseInt", "animation", "enterDuration", "exitDuration", "newValue", "rippleDisabled", "monitor", "stopMonitoring", "_handleFocus", "indexOf", "click", "_getAriaControls", "_getAriaSelected", "_getAriaCurrent", "FocusMonitor", "tabNavBar", "focusMonitor", "_tabLinkRipple", "setupTriggerEvents", "_removeTriggerEvents", "MatTabNavPanel", "OnPush", "MatTabsModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["R:/chateye/FrontendAngular/node_modules/@angular/material/fesm2020/tabs.mjs"], "sourcesContent": ["import * as i7 from '@angular/cdk/a11y';\nimport { FocusKeyManager, A11yModule } from '@angular/cdk/a11y';\nimport * as i5 from '@angular/cdk/observers';\nimport { ObserversModule } from '@angular/cdk/observers';\nimport * as i2 from '@angular/cdk/portal';\nimport { CdkPortal, TemplatePortal, CdkPortalOutlet, PortalModule } from '@angular/cdk/portal';\nimport * as i1$2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Inject, Optional, TemplateRef, Component, ChangeDetectionStrategy, ViewEncapsulation, ContentChild, ViewChild, Input, forwardRef, EventEmitter, Output, ContentChildren, QueryList, Attribute, NgModule } from '@angular/core';\nimport * as i4 from '@angular/material/core';\nimport { mixinDisabled, mixinColor, mixinDisableRipple, mixinTabIndex, MAT_RIPPLE_GLOBAL_OPTIONS, RippleRenderer, MatCommonModule, MatRippleModule } from '@angular/material/core';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { take, startWith, distinctUntilChanged, takeUntil, switchMap, skip, filter } from 'rxjs/operators';\nimport { Subject, Subscription, fromEvent, of, merge, EMPTY, Observable, timer } from 'rxjs';\nimport * as i1 from '@angular/cdk/bidi';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i1$1 from '@angular/cdk/scrolling';\nimport * as i3 from '@angular/cdk/platform';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport { hasModifierKey, SPACE, ENTER } from '@angular/cdk/keycodes';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Injection token for the MatInkBar's Positioner. */\nconst _MAT_INK_BAR_POSITIONER = new InjectionToken('MatInkBarPositioner', {\n    providedIn: 'root',\n    factory: _MAT_INK_BAR_POSITIONER_FACTORY,\n});\n/**\n * The default positioner function for the MatInkBar.\n * @docs-private\n */\nfunction _MAT_INK_BAR_POSITIONER_FACTORY() {\n    const method = (element) => ({\n        left: element ? (element.offsetLeft || 0) + 'px' : '0',\n        width: element ? (element.offsetWidth || 0) + 'px' : '0',\n    });\n    return method;\n}\n/**\n * The ink-bar is used to display and animate the line underneath the current active tab label.\n * @docs-private\n */\nclass MatInkBar {\n    constructor(_elementRef, _ngZone, _inkBarPositioner, _animationMode) {\n        this._elementRef = _elementRef;\n        this._ngZone = _ngZone;\n        this._inkBarPositioner = _inkBarPositioner;\n        this._animationMode = _animationMode;\n    }\n    /**\n     * Calculates the styles from the provided element in order to align the ink-bar to that element.\n     * Shows the ink bar if previously set as hidden.\n     * @param element\n     */\n    alignToElement(element) {\n        this.show();\n        // `onStable` might not run for a while if the zone has already stabilized.\n        // Wrap the call in `NgZone.run` to ensure that it runs relatively soon.\n        this._ngZone.run(() => {\n            this._ngZone.onStable.pipe(take(1)).subscribe(() => {\n                const positions = this._inkBarPositioner(element);\n                const inkBar = this._elementRef.nativeElement;\n                inkBar.style.left = positions.left;\n                inkBar.style.width = positions.width;\n            });\n        });\n    }\n    /** Shows the ink bar. */\n    show() {\n        this._elementRef.nativeElement.style.visibility = 'visible';\n    }\n    /** Hides the ink bar. */\n    hide() {\n        this._elementRef.nativeElement.style.visibility = 'hidden';\n    }\n}\nMatInkBar.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatInkBar, deps: [{ token: i0.ElementRef }, { token: i0.NgZone }, { token: _MAT_INK_BAR_POSITIONER }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\nMatInkBar.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatInkBar, selector: \"mat-ink-bar\", host: { properties: { \"class._mat-animation-noopable\": \"_animationMode === 'NoopAnimations'\" }, classAttribute: \"mat-ink-bar\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatInkBar, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-ink-bar',\n                    host: {\n                        'class': 'mat-ink-bar',\n                        '[class._mat-animation-noopable]': `_animationMode === 'NoopAnimations'`,\n                    },\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [_MAT_INK_BAR_POSITIONER]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token that can be used to reference instances of `MatTabContent`. It serves as\n * alternative token to the actual `MatTabContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_TAB_CONTENT = new InjectionToken('MatTabContent');\n/** Decorates the `ng-template` tags and reads out the template from it. */\nclass MatTabContent {\n    constructor(/** Content for the tab. */ template) {\n        this.template = template;\n    }\n}\nMatTabContent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatTabContent, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive });\nMatTabContent.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatTabContent, selector: \"[matTabContent]\", providers: [{ provide: MAT_TAB_CONTENT, useExisting: MatTabContent }], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatTabContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matTabContent]',\n                    providers: [{ provide: MAT_TAB_CONTENT, useExisting: MatTabContent }],\n                }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token that can be used to reference instances of `MatTabLabel`. It serves as\n * alternative token to the actual `MatTabLabel` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_TAB_LABEL = new InjectionToken('MatTabLabel');\n/**\n * Used to provide a tab label to a tab without causing a circular dependency.\n * @docs-private\n */\nconst MAT_TAB = new InjectionToken('MAT_TAB');\n/** Used to flag tab labels for use with the portal directive */\nclass MatTabLabel extends CdkPortal {\n    constructor(templateRef, viewContainerRef, _closestTab) {\n        super(templateRef, viewContainerRef);\n        this._closestTab = _closestTab;\n    }\n}\nMatTabLabel.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatTabLabel, deps: [{ token: i0.TemplateRef }, { token: i0.ViewContainerRef }, { token: MAT_TAB, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\nMatTabLabel.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatTabLabel, selector: \"[mat-tab-label], [matTabLabel]\", providers: [{ provide: MAT_TAB_LABEL, useExisting: MatTabLabel }], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatTabLabel, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-tab-label], [matTabLabel]',\n                    providers: [{ provide: MAT_TAB_LABEL, useExisting: MatTabLabel }],\n                }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }, { type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_TAB]\n                }, {\n                    type: Optional\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to MatTab.\n/** @docs-private */\nconst _MatTabBase = mixinDisabled(class {\n});\n/**\n * Used to provide a tab group to a tab without causing a circular dependency.\n * @docs-private\n */\nconst MAT_TAB_GROUP = new InjectionToken('MAT_TAB_GROUP');\nclass MatTab extends _MatTabBase {\n    constructor(_viewContainerRef, _closestTabGroup) {\n        super();\n        this._viewContainerRef = _viewContainerRef;\n        this._closestTabGroup = _closestTabGroup;\n        /** Plain text label for the tab, used when there is no template label. */\n        this.textLabel = '';\n        /** Portal that will be the hosted content of the tab */\n        this._contentPortal = null;\n        /** Emits whenever the internal state of the tab changes. */\n        this._stateChanges = new Subject();\n        /**\n         * The relatively indexed position where 0 represents the center, negative is left, and positive\n         * represents the right.\n         */\n        this.position = null;\n        /**\n         * The initial relatively index origin of the tab if it was created and selected after there\n         * was already a selected tab. Provides context of what position the tab should originate from.\n         */\n        this.origin = null;\n        /**\n         * Whether the tab is currently active.\n         */\n        this.isActive = false;\n    }\n    /** Content for the tab label given by `<ng-template mat-tab-label>`. */\n    get templateLabel() {\n        return this._templateLabel;\n    }\n    set templateLabel(value) {\n        this._setTemplateLabelInput(value);\n    }\n    /** @docs-private */\n    get content() {\n        return this._contentPortal;\n    }\n    ngOnChanges(changes) {\n        if (changes.hasOwnProperty('textLabel') || changes.hasOwnProperty('disabled')) {\n            this._stateChanges.next();\n        }\n    }\n    ngOnDestroy() {\n        this._stateChanges.complete();\n    }\n    ngOnInit() {\n        this._contentPortal = new TemplatePortal(this._explicitContent || this._implicitContent, this._viewContainerRef);\n    }\n    /**\n     * This has been extracted to a util because of TS 4 and VE.\n     * View Engine doesn't support property rename inheritance.\n     * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n     * @docs-private\n     */\n    _setTemplateLabelInput(value) {\n        // Only update the label if the query managed to find one. This works around an issue where a\n        // user may have manually set `templateLabel` during creation mode, which would then get\n        // clobbered by `undefined` when the query resolves. Also note that we check that the closest\n        // tab matches the current one so that we don't pick up labels from nested tabs.\n        if (value && value._closestTab === this) {\n            this._templateLabel = value;\n        }\n    }\n}\nMatTab.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatTab, deps: [{ token: i0.ViewContainerRef }, { token: MAT_TAB_GROUP, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nMatTab.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatTab, selector: \"mat-tab\", inputs: { disabled: \"disabled\", textLabel: [\"label\", \"textLabel\"], ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], labelClass: \"labelClass\", bodyClass: \"bodyClass\" }, providers: [{ provide: MAT_TAB, useExisting: MatTab }], queries: [{ propertyName: \"templateLabel\", first: true, predicate: MAT_TAB_LABEL, descendants: true }, { propertyName: \"_explicitContent\", first: true, predicate: MAT_TAB_CONTENT, descendants: true, read: TemplateRef, static: true }], viewQueries: [{ propertyName: \"_implicitContent\", first: true, predicate: TemplateRef, descendants: true, static: true }], exportAs: [\"matTab\"], usesInheritance: true, usesOnChanges: true, ngImport: i0, template: \"<!-- Create a template for the content of the <mat-tab> so that we can grab a reference to this\\n    TemplateRef and use it in a Portal to render the tab content in the appropriate place in the\\n    tab-group. -->\\n<ng-template><ng-content></ng-content></ng-template>\\n\", changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatTab, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tab', inputs: ['disabled'], changeDetection: ChangeDetectionStrategy.Default, encapsulation: ViewEncapsulation.None, exportAs: 'matTab', providers: [{ provide: MAT_TAB, useExisting: MatTab }], template: \"<!-- Create a template for the content of the <mat-tab> so that we can grab a reference to this\\n    TemplateRef and use it in a Portal to render the tab content in the appropriate place in the\\n    tab-group. -->\\n<ng-template><ng-content></ng-content></ng-template>\\n\" }]\n        }], ctorParameters: function () { return [{ type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_TAB_GROUP]\n                }, {\n                    type: Optional\n                }] }]; }, propDecorators: { templateLabel: [{\n                type: ContentChild,\n                args: [MAT_TAB_LABEL]\n            }], _explicitContent: [{\n                type: ContentChild,\n                args: [MAT_TAB_CONTENT, { read: TemplateRef, static: true }]\n            }], _implicitContent: [{\n                type: ViewChild,\n                args: [TemplateRef, { static: true }]\n            }], textLabel: [{\n                type: Input,\n                args: ['label']\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], labelClass: [{\n                type: Input\n            }], bodyClass: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Animations used by the Material tabs.\n * @docs-private\n */\nconst matTabsAnimations = {\n    /** Animation translates a tab along the X axis. */\n    translateTab: trigger('translateTab', [\n        // Transitions to `none` instead of 0, because some browsers might blur the content.\n        state('center, void, left-origin-center, right-origin-center', style({ transform: 'none' })),\n        // If the tab is either on the left or right, we additionally add a `min-height` of 1px\n        // in order to ensure that the element has a height before its state changes. This is\n        // necessary because Chrome does seem to skip the transition in RTL mode if the element does\n        // not have a static height and is not rendered. See related issue: #9465\n        state('left', style({\n            transform: 'translate3d(-100%, 0, 0)',\n            minHeight: '1px',\n            // Normally this is redundant since we detach the content from the DOM, but if the user\n            // opted into keeping the content in the DOM, we have to hide it so it isn't focusable.\n            visibility: 'hidden',\n        })),\n        state('right', style({\n            transform: 'translate3d(100%, 0, 0)',\n            minHeight: '1px',\n            visibility: 'hidden',\n        })),\n        transition('* => left, * => right, left => center, right => center', animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)')),\n        transition('void => left-origin-center', [\n            style({ transform: 'translate3d(-100%, 0, 0)', visibility: 'hidden' }),\n            animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n        ]),\n        transition('void => right-origin-center', [\n            style({ transform: 'translate3d(100%, 0, 0)', visibility: 'hidden' }),\n            animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n        ]),\n    ]),\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * The portal host directive for the contents of the tab.\n * @docs-private\n */\nclass MatTabBodyPortal extends CdkPortalOutlet {\n    constructor(componentFactoryResolver, viewContainerRef, _host, _document) {\n        super(componentFactoryResolver, viewContainerRef, _document);\n        this._host = _host;\n        /** Subscription to events for when the tab body begins centering. */\n        this._centeringSub = Subscription.EMPTY;\n        /** Subscription to events for when the tab body finishes leaving from center position. */\n        this._leavingSub = Subscription.EMPTY;\n    }\n    /** Set initial visibility or set up subscription for changing visibility. */\n    ngOnInit() {\n        super.ngOnInit();\n        this._centeringSub = this._host._beforeCentering\n            .pipe(startWith(this._host._isCenterPosition(this._host._position)))\n            .subscribe((isCentering) => {\n            if (isCentering && !this.hasAttached()) {\n                this.attach(this._host._content);\n            }\n        });\n        this._leavingSub = this._host._afterLeavingCenter.subscribe(() => {\n            if (!this._host.preserveContent) {\n                this.detach();\n            }\n        });\n    }\n    /** Clean up centering subscription. */\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        this._centeringSub.unsubscribe();\n        this._leavingSub.unsubscribe();\n    }\n}\nMatTabBodyPortal.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatTabBodyPortal, deps: [{ token: i0.ComponentFactoryResolver }, { token: i0.ViewContainerRef }, { token: forwardRef(() => MatTabBody) }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Directive });\nMatTabBodyPortal.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatTabBodyPortal, selector: \"[matTabBodyHost]\", usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatTabBodyPortal, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matTabBodyHost]',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ComponentFactoryResolver }, { type: i0.ViewContainerRef }, { type: MatTabBody, decorators: [{\n                    type: Inject,\n                    args: [forwardRef(() => MatTabBody)]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n/**\n * Base class with all of the `MatTabBody` functionality.\n * @docs-private\n */\nclass _MatTabBodyBase {\n    constructor(_elementRef, _dir, changeDetectorRef) {\n        this._elementRef = _elementRef;\n        this._dir = _dir;\n        /** Subscription to the directionality change observable. */\n        this._dirChangeSubscription = Subscription.EMPTY;\n        /** Emits when an animation on the tab is complete. */\n        this._translateTabComplete = new Subject();\n        /** Event emitted when the tab begins to animate towards the center as the active tab. */\n        this._onCentering = new EventEmitter();\n        /** Event emitted before the centering of the tab begins. */\n        this._beforeCentering = new EventEmitter();\n        /** Event emitted before the centering of the tab begins. */\n        this._afterLeavingCenter = new EventEmitter();\n        /** Event emitted when the tab completes its animation towards the center. */\n        this._onCentered = new EventEmitter(true);\n        // Note that the default value will always be overwritten by `MatTabBody`, but we need one\n        // anyway to prevent the animations module from throwing an error if the body is used on its own.\n        /** Duration for the tab's animation. */\n        this.animationDuration = '500ms';\n        /** Whether the tab's content should be kept in the DOM while it's off-screen. */\n        this.preserveContent = false;\n        if (_dir) {\n            this._dirChangeSubscription = _dir.change.subscribe((dir) => {\n                this._computePositionAnimationState(dir);\n                changeDetectorRef.markForCheck();\n            });\n        }\n        // Ensure that we get unique animation events, because the `.done` callback can get\n        // invoked twice in some browsers. See https://github.com/angular/angular/issues/24084.\n        this._translateTabComplete\n            .pipe(distinctUntilChanged((x, y) => {\n            return x.fromState === y.fromState && x.toState === y.toState;\n        }))\n            .subscribe(event => {\n            // If the transition to the center is complete, emit an event.\n            if (this._isCenterPosition(event.toState) && this._isCenterPosition(this._position)) {\n                this._onCentered.emit();\n            }\n            if (this._isCenterPosition(event.fromState) && !this._isCenterPosition(this._position)) {\n                this._afterLeavingCenter.emit();\n            }\n        });\n    }\n    /** The shifted index position of the tab body, where zero represents the active center tab. */\n    set position(position) {\n        this._positionIndex = position;\n        this._computePositionAnimationState();\n    }\n    /**\n     * After initialized, check if the content is centered and has an origin. If so, set the\n     * special position states that transition the tab from the left or right before centering.\n     */\n    ngOnInit() {\n        if (this._position == 'center' && this.origin != null) {\n            this._position = this._computePositionFromOrigin(this.origin);\n        }\n    }\n    ngOnDestroy() {\n        this._dirChangeSubscription.unsubscribe();\n        this._translateTabComplete.complete();\n    }\n    _onTranslateTabStarted(event) {\n        const isCentering = this._isCenterPosition(event.toState);\n        this._beforeCentering.emit(isCentering);\n        if (isCentering) {\n            this._onCentering.emit(this._elementRef.nativeElement.clientHeight);\n        }\n    }\n    /** The text direction of the containing app. */\n    _getLayoutDirection() {\n        return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Whether the provided position state is considered center, regardless of origin. */\n    _isCenterPosition(position) {\n        return (position == 'center' || position == 'left-origin-center' || position == 'right-origin-center');\n    }\n    /** Computes the position state that will be used for the tab-body animation trigger. */\n    _computePositionAnimationState(dir = this._getLayoutDirection()) {\n        if (this._positionIndex < 0) {\n            this._position = dir == 'ltr' ? 'left' : 'right';\n        }\n        else if (this._positionIndex > 0) {\n            this._position = dir == 'ltr' ? 'right' : 'left';\n        }\n        else {\n            this._position = 'center';\n        }\n    }\n    /**\n     * Computes the position state based on the specified origin position. This is used if the\n     * tab is becoming visible immediately after creation.\n     */\n    _computePositionFromOrigin(origin) {\n        const dir = this._getLayoutDirection();\n        if ((dir == 'ltr' && origin <= 0) || (dir == 'rtl' && origin > 0)) {\n            return 'left-origin-center';\n        }\n        return 'right-origin-center';\n    }\n}\n_MatTabBodyBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: _MatTabBodyBase, deps: [{ token: i0.ElementRef }, { token: i1.Directionality, optional: true }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Directive });\n_MatTabBodyBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: _MatTabBodyBase, inputs: { _content: [\"content\", \"_content\"], origin: \"origin\", animationDuration: \"animationDuration\", preserveContent: \"preserveContent\", position: \"position\" }, outputs: { _onCentering: \"_onCentering\", _beforeCentering: \"_beforeCentering\", _afterLeavingCenter: \"_afterLeavingCenter\", _onCentered: \"_onCentered\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: _MatTabBodyBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { _onCentering: [{\n                type: Output\n            }], _beforeCentering: [{\n                type: Output\n            }], _afterLeavingCenter: [{\n                type: Output\n            }], _onCentered: [{\n                type: Output\n            }], _content: [{\n                type: Input,\n                args: ['content']\n            }], origin: [{\n                type: Input\n            }], animationDuration: [{\n                type: Input\n            }], preserveContent: [{\n                type: Input\n            }], position: [{\n                type: Input\n            }] } });\n/**\n * Wrapper for the contents of a tab.\n * @docs-private\n */\nclass MatTabBody extends _MatTabBodyBase {\n    constructor(elementRef, dir, changeDetectorRef) {\n        super(elementRef, dir, changeDetectorRef);\n    }\n}\nMatTabBody.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatTabBody, deps: [{ token: i0.ElementRef }, { token: i1.Directionality, optional: true }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\nMatTabBody.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatTabBody, selector: \"mat-tab-body\", host: { classAttribute: \"mat-tab-body\" }, viewQueries: [{ propertyName: \"_portalHost\", first: true, predicate: CdkPortalOutlet, descendants: true }], usesInheritance: true, ngImport: i0, template: \"<div class=\\\"mat-tab-body-content\\\" #content\\n     [@translateTab]=\\\"{\\n        value: _position,\\n        params: {animationDuration: animationDuration}\\n     }\\\"\\n     (@translateTab.start)=\\\"_onTranslateTabStarted($event)\\\"\\n     (@translateTab.done)=\\\"_translateTabComplete.next($event)\\\"\\n     cdkScrollable>\\n  <ng-template matTabBodyHost></ng-template>\\n</div>\\n\", styles: [\".mat-tab-body-content{height:100%;overflow:auto}.mat-tab-group-dynamic-height .mat-tab-body-content{overflow:hidden}.mat-tab-body-content[style*=\\\"visibility: hidden\\\"]{display:none}\"], dependencies: [{ kind: \"directive\", type: MatTabBodyPortal, selector: \"[matTabBodyHost]\" }], animations: [matTabsAnimations.translateTab], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatTabBody, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tab-body', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, animations: [matTabsAnimations.translateTab], host: {\n                        'class': 'mat-tab-body',\n                    }, template: \"<div class=\\\"mat-tab-body-content\\\" #content\\n     [@translateTab]=\\\"{\\n        value: _position,\\n        params: {animationDuration: animationDuration}\\n     }\\\"\\n     (@translateTab.start)=\\\"_onTranslateTabStarted($event)\\\"\\n     (@translateTab.done)=\\\"_translateTabComplete.next($event)\\\"\\n     cdkScrollable>\\n  <ng-template matTabBodyHost></ng-template>\\n</div>\\n\", styles: [\".mat-tab-body-content{height:100%;overflow:auto}.mat-tab-group-dynamic-height .mat-tab-body-content{overflow:hidden}.mat-tab-body-content[style*=\\\"visibility: hidden\\\"]{display:none}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { _portalHost: [{\n                type: ViewChild,\n                args: [CdkPortalOutlet]\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Injection token that can be used to provide the default options the tabs module. */\nconst MAT_TABS_CONFIG = new InjectionToken('MAT_TABS_CONFIG');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to MatTabLabelWrapper.\n/** @docs-private */\nconst _MatTabLabelWrapperBase = mixinDisabled(class {\n});\n/**\n * Used in the `mat-tab-group` view to display tab labels.\n * @docs-private\n */\nclass MatTabLabelWrapper extends _MatTabLabelWrapperBase {\n    constructor(elementRef) {\n        super();\n        this.elementRef = elementRef;\n    }\n    /** Sets focus on the wrapper element */\n    focus() {\n        this.elementRef.nativeElement.focus();\n    }\n    getOffsetLeft() {\n        return this.elementRef.nativeElement.offsetLeft;\n    }\n    getOffsetWidth() {\n        return this.elementRef.nativeElement.offsetWidth;\n    }\n}\nMatTabLabelWrapper.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatTabLabelWrapper, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive });\nMatTabLabelWrapper.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatTabLabelWrapper, selector: \"[matTabLabelWrapper]\", inputs: { disabled: \"disabled\" }, host: { properties: { \"class.mat-tab-disabled\": \"disabled\", \"attr.aria-disabled\": \"!!disabled\" } }, usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatTabLabelWrapper, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matTabLabelWrapper]',\n                    inputs: ['disabled'],\n                    host: {\n                        '[class.mat-tab-disabled]': 'disabled',\n                        '[attr.aria-disabled]': '!!disabled',\n                    },\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Config used to bind passive event listeners */\nconst passiveEventListenerOptions = normalizePassiveListenerOptions({\n    passive: true,\n});\n/**\n * The distance in pixels that will be overshot when scrolling a tab label into view. This helps\n * provide a small affordance to the label next to it.\n */\nconst EXAGGERATED_OVERSCROLL = 60;\n/**\n * Amount of milliseconds to wait before starting to scroll the header automatically.\n * Set a little conservatively in order to handle fake events dispatched on touch devices.\n */\nconst HEADER_SCROLL_DELAY = 650;\n/**\n * Interval in milliseconds at which to scroll the header\n * while the user is holding their pointer.\n */\nconst HEADER_SCROLL_INTERVAL = 100;\n/**\n * Base class for a tab header that supported pagination.\n * @docs-private\n */\nclass MatPaginatedTabHeader {\n    constructor(_elementRef, _changeDetectorRef, _viewportRuler, _dir, _ngZone, _platform, _animationMode) {\n        this._elementRef = _elementRef;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._viewportRuler = _viewportRuler;\n        this._dir = _dir;\n        this._ngZone = _ngZone;\n        this._platform = _platform;\n        this._animationMode = _animationMode;\n        /** The distance in pixels that the tab labels should be translated to the left. */\n        this._scrollDistance = 0;\n        /** Whether the header should scroll to the selected index after the view has been checked. */\n        this._selectedIndexChanged = false;\n        /** Emits when the component is destroyed. */\n        this._destroyed = new Subject();\n        /** Whether the controls for pagination should be displayed */\n        this._showPaginationControls = false;\n        /** Whether the tab list can be scrolled more towards the end of the tab label list. */\n        this._disableScrollAfter = true;\n        /** Whether the tab list can be scrolled more towards the beginning of the tab label list. */\n        this._disableScrollBefore = true;\n        /** Stream that will stop the automated scrolling. */\n        this._stopScrolling = new Subject();\n        this._disablePagination = false;\n        this._selectedIndex = 0;\n        /** Event emitted when the option is selected. */\n        this.selectFocusedIndex = new EventEmitter();\n        /** Event emitted when a label is focused. */\n        this.indexFocused = new EventEmitter();\n        // Bind the `mouseleave` event on the outside since it doesn't change anything in the view.\n        _ngZone.runOutsideAngular(() => {\n            fromEvent(_elementRef.nativeElement, 'mouseleave')\n                .pipe(takeUntil(this._destroyed))\n                .subscribe(() => {\n                this._stopInterval();\n            });\n        });\n    }\n    /**\n     * Whether pagination should be disabled. This can be used to avoid unnecessary\n     * layout recalculations if it's known that pagination won't be required.\n     */\n    get disablePagination() {\n        return this._disablePagination;\n    }\n    set disablePagination(value) {\n        this._disablePagination = coerceBooleanProperty(value);\n    }\n    /** The index of the active tab. */\n    get selectedIndex() {\n        return this._selectedIndex;\n    }\n    set selectedIndex(value) {\n        value = coerceNumberProperty(value);\n        if (this._selectedIndex != value) {\n            this._selectedIndexChanged = true;\n            this._selectedIndex = value;\n            if (this._keyManager) {\n                this._keyManager.updateActiveItem(value);\n            }\n        }\n    }\n    ngAfterViewInit() {\n        // We need to handle these events manually, because we want to bind passive event listeners.\n        fromEvent(this._previousPaginator.nativeElement, 'touchstart', passiveEventListenerOptions)\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => {\n            this._handlePaginatorPress('before');\n        });\n        fromEvent(this._nextPaginator.nativeElement, 'touchstart', passiveEventListenerOptions)\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => {\n            this._handlePaginatorPress('after');\n        });\n    }\n    ngAfterContentInit() {\n        const dirChange = this._dir ? this._dir.change : of('ltr');\n        const resize = this._viewportRuler.change(150);\n        const realign = () => {\n            this.updatePagination();\n            this._alignInkBarToSelectedTab();\n        };\n        this._keyManager = new FocusKeyManager(this._items)\n            .withHorizontalOrientation(this._getLayoutDirection())\n            .withHomeAndEnd()\n            .withWrap();\n        this._keyManager.updateActiveItem(this._selectedIndex);\n        // Defer the first call in order to allow for slower browsers to lay out the elements.\n        // This helps in cases where the user lands directly on a page with paginated tabs.\n        // Note that we use `onStable` instead of `requestAnimationFrame`, because the latter\n        // can hold up tests that are in a background tab.\n        this._ngZone.onStable.pipe(take(1)).subscribe(realign);\n        // On dir change or window resize, realign the ink bar and update the orientation of\n        // the key manager if the direction has changed.\n        merge(dirChange, resize, this._items.changes, this._itemsResized())\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => {\n            // We need to defer this to give the browser some time to recalculate\n            // the element dimensions. The call has to be wrapped in `NgZone.run`,\n            // because the viewport change handler runs outside of Angular.\n            this._ngZone.run(() => {\n                Promise.resolve().then(() => {\n                    // Clamp the scroll distance, because it can change with the number of tabs.\n                    this._scrollDistance = Math.max(0, Math.min(this._getMaxScrollDistance(), this._scrollDistance));\n                    realign();\n                });\n            });\n            this._keyManager.withHorizontalOrientation(this._getLayoutDirection());\n        });\n        // If there is a change in the focus key manager we need to emit the `indexFocused`\n        // event in order to provide a public event that notifies about focus changes. Also we realign\n        // the tabs container by scrolling the new focused tab into the visible section.\n        this._keyManager.change.pipe(takeUntil(this._destroyed)).subscribe(newFocusIndex => {\n            this.indexFocused.emit(newFocusIndex);\n            this._setTabFocus(newFocusIndex);\n        });\n    }\n    /** Sends any changes that could affect the layout of the items. */\n    _itemsResized() {\n        if (typeof ResizeObserver !== 'function') {\n            return EMPTY;\n        }\n        return this._items.changes.pipe(startWith(this._items), switchMap((tabItems) => new Observable((observer) => this._ngZone.runOutsideAngular(() => {\n            const resizeObserver = new ResizeObserver(entries => observer.next(entries));\n            tabItems.forEach(item => resizeObserver.observe(item.elementRef.nativeElement));\n            return () => {\n                resizeObserver.disconnect();\n            };\n        }))), \n        // Skip the first emit since the resize observer emits when an item\n        // is observed for new items when the tab is already inserted\n        skip(1), \n        // Skip emissions where all the elements are invisible since we don't want\n        // the header to try and re-render with invalid measurements. See #25574.\n        filter(entries => entries.some(e => e.contentRect.width > 0 && e.contentRect.height > 0)));\n    }\n    ngAfterContentChecked() {\n        // If the number of tab labels have changed, check if scrolling should be enabled\n        if (this._tabLabelCount != this._items.length) {\n            this.updatePagination();\n            this._tabLabelCount = this._items.length;\n            this._changeDetectorRef.markForCheck();\n        }\n        // If the selected index has changed, scroll to the label and check if the scrolling controls\n        // should be disabled.\n        if (this._selectedIndexChanged) {\n            this._scrollToLabel(this._selectedIndex);\n            this._checkScrollingControls();\n            this._alignInkBarToSelectedTab();\n            this._selectedIndexChanged = false;\n            this._changeDetectorRef.markForCheck();\n        }\n        // If the scroll distance has been changed (tab selected, focused, scroll controls activated),\n        // then translate the header to reflect this.\n        if (this._scrollDistanceChanged) {\n            this._updateTabScrollPosition();\n            this._scrollDistanceChanged = false;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    ngOnDestroy() {\n        this._destroyed.next();\n        this._destroyed.complete();\n        this._stopScrolling.complete();\n    }\n    /** Handles keyboard events on the header. */\n    _handleKeydown(event) {\n        // We don't handle any key bindings with a modifier key.\n        if (hasModifierKey(event)) {\n            return;\n        }\n        switch (event.keyCode) {\n            case ENTER:\n            case SPACE:\n                if (this.focusIndex !== this.selectedIndex) {\n                    this.selectFocusedIndex.emit(this.focusIndex);\n                    this._itemSelected(event);\n                }\n                break;\n            default:\n                this._keyManager.onKeydown(event);\n        }\n    }\n    /**\n     * Callback for when the MutationObserver detects that the content has changed.\n     */\n    _onContentChanges() {\n        const textContent = this._elementRef.nativeElement.textContent;\n        // We need to diff the text content of the header, because the MutationObserver callback\n        // will fire even if the text content didn't change which is inefficient and is prone\n        // to infinite loops if a poorly constructed expression is passed in (see #14249).\n        if (textContent !== this._currentTextContent) {\n            this._currentTextContent = textContent || '';\n            // The content observer runs outside the `NgZone` by default, which\n            // means that we need to bring the callback back in ourselves.\n            this._ngZone.run(() => {\n                this.updatePagination();\n                this._alignInkBarToSelectedTab();\n                this._changeDetectorRef.markForCheck();\n            });\n        }\n    }\n    /**\n     * Updates the view whether pagination should be enabled or not.\n     *\n     * WARNING: Calling this method can be very costly in terms of performance. It should be called\n     * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n     * page.\n     */\n    updatePagination() {\n        this._checkPaginationEnabled();\n        this._checkScrollingControls();\n        this._updateTabScrollPosition();\n    }\n    /** Tracks which element has focus; used for keyboard navigation */\n    get focusIndex() {\n        return this._keyManager ? this._keyManager.activeItemIndex : 0;\n    }\n    /** When the focus index is set, we must manually send focus to the correct label */\n    set focusIndex(value) {\n        if (!this._isValidIndex(value) || this.focusIndex === value || !this._keyManager) {\n            return;\n        }\n        this._keyManager.setActiveItem(value);\n    }\n    /**\n     * Determines if an index is valid.  If the tabs are not ready yet, we assume that the user is\n     * providing a valid index and return true.\n     */\n    _isValidIndex(index) {\n        if (!this._items) {\n            return true;\n        }\n        const tab = this._items ? this._items.toArray()[index] : null;\n        return !!tab && !tab.disabled;\n    }\n    /**\n     * Sets focus on the HTML element for the label wrapper and scrolls it into the view if\n     * scrolling is enabled.\n     */\n    _setTabFocus(tabIndex) {\n        if (this._showPaginationControls) {\n            this._scrollToLabel(tabIndex);\n        }\n        if (this._items && this._items.length) {\n            this._items.toArray()[tabIndex].focus();\n            // Do not let the browser manage scrolling to focus the element, this will be handled\n            // by using translation. In LTR, the scroll left should be 0. In RTL, the scroll width\n            // should be the full width minus the offset width.\n            const containerEl = this._tabListContainer.nativeElement;\n            const dir = this._getLayoutDirection();\n            if (dir == 'ltr') {\n                containerEl.scrollLeft = 0;\n            }\n            else {\n                containerEl.scrollLeft = containerEl.scrollWidth - containerEl.offsetWidth;\n            }\n        }\n    }\n    /** The layout direction of the containing app. */\n    _getLayoutDirection() {\n        return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Performs the CSS transformation on the tab list that will cause the list to scroll. */\n    _updateTabScrollPosition() {\n        if (this.disablePagination) {\n            return;\n        }\n        const scrollDistance = this.scrollDistance;\n        const translateX = this._getLayoutDirection() === 'ltr' ? -scrollDistance : scrollDistance;\n        // Don't use `translate3d` here because we don't want to create a new layer. A new layer\n        // seems to cause flickering and overflow in Internet Explorer. For example, the ink bar\n        // and ripples will exceed the boundaries of the visible tab bar.\n        // See: https://github.com/angular/components/issues/10276\n        // We round the `transform` here, because transforms with sub-pixel precision cause some\n        // browsers to blur the content of the element.\n        this._tabList.nativeElement.style.transform = `translateX(${Math.round(translateX)}px)`;\n        // Setting the `transform` on IE will change the scroll offset of the parent, causing the\n        // position to be thrown off in some cases. We have to reset it ourselves to ensure that\n        // it doesn't get thrown off. Note that we scope it only to IE and Edge, because messing\n        // with the scroll position throws off Chrome 71+ in RTL mode (see #14689).\n        if (this._platform.TRIDENT || this._platform.EDGE) {\n            this._tabListContainer.nativeElement.scrollLeft = 0;\n        }\n    }\n    /** Sets the distance in pixels that the tab header should be transformed in the X-axis. */\n    get scrollDistance() {\n        return this._scrollDistance;\n    }\n    set scrollDistance(value) {\n        this._scrollTo(value);\n    }\n    /**\n     * Moves the tab list in the 'before' or 'after' direction (towards the beginning of the list or\n     * the end of the list, respectively). The distance to scroll is computed to be a third of the\n     * length of the tab list view window.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _scrollHeader(direction) {\n        const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n        // Move the scroll distance one-third the length of the tab list's viewport.\n        const scrollAmount = ((direction == 'before' ? -1 : 1) * viewLength) / 3;\n        return this._scrollTo(this._scrollDistance + scrollAmount);\n    }\n    /** Handles click events on the pagination arrows. */\n    _handlePaginatorClick(direction) {\n        this._stopInterval();\n        this._scrollHeader(direction);\n    }\n    /**\n     * Moves the tab list such that the desired tab label (marked by index) is moved into view.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _scrollToLabel(labelIndex) {\n        if (this.disablePagination) {\n            return;\n        }\n        const selectedLabel = this._items ? this._items.toArray()[labelIndex] : null;\n        if (!selectedLabel) {\n            return;\n        }\n        // The view length is the visible width of the tab labels.\n        const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n        const { offsetLeft, offsetWidth } = selectedLabel.elementRef.nativeElement;\n        let labelBeforePos, labelAfterPos;\n        if (this._getLayoutDirection() == 'ltr') {\n            labelBeforePos = offsetLeft;\n            labelAfterPos = labelBeforePos + offsetWidth;\n        }\n        else {\n            labelAfterPos = this._tabListInner.nativeElement.offsetWidth - offsetLeft;\n            labelBeforePos = labelAfterPos - offsetWidth;\n        }\n        const beforeVisiblePos = this.scrollDistance;\n        const afterVisiblePos = this.scrollDistance + viewLength;\n        if (labelBeforePos < beforeVisiblePos) {\n            // Scroll header to move label to the before direction\n            this.scrollDistance -= beforeVisiblePos - labelBeforePos + EXAGGERATED_OVERSCROLL;\n        }\n        else if (labelAfterPos > afterVisiblePos) {\n            // Scroll header to move label to the after direction\n            this.scrollDistance += labelAfterPos - afterVisiblePos + EXAGGERATED_OVERSCROLL;\n        }\n    }\n    /**\n     * Evaluate whether the pagination controls should be displayed. If the scroll width of the\n     * tab list is wider than the size of the header container, then the pagination controls should\n     * be shown.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _checkPaginationEnabled() {\n        if (this.disablePagination) {\n            this._showPaginationControls = false;\n        }\n        else {\n            const isEnabled = this._tabListInner.nativeElement.scrollWidth > this._elementRef.nativeElement.offsetWidth;\n            if (!isEnabled) {\n                this.scrollDistance = 0;\n            }\n            if (isEnabled !== this._showPaginationControls) {\n                this._changeDetectorRef.markForCheck();\n            }\n            this._showPaginationControls = isEnabled;\n        }\n    }\n    /**\n     * Evaluate whether the before and after controls should be enabled or disabled.\n     * If the header is at the beginning of the list (scroll distance is equal to 0) then disable the\n     * before button. If the header is at the end of the list (scroll distance is equal to the\n     * maximum distance we can scroll), then disable the after button.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _checkScrollingControls() {\n        if (this.disablePagination) {\n            this._disableScrollAfter = this._disableScrollBefore = true;\n        }\n        else {\n            // Check if the pagination arrows should be activated.\n            this._disableScrollBefore = this.scrollDistance == 0;\n            this._disableScrollAfter = this.scrollDistance == this._getMaxScrollDistance();\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /**\n     * Determines what is the maximum length in pixels that can be set for the scroll distance. This\n     * is equal to the difference in width between the tab list container and tab header container.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _getMaxScrollDistance() {\n        const lengthOfTabList = this._tabListInner.nativeElement.scrollWidth;\n        const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n        return lengthOfTabList - viewLength || 0;\n    }\n    /** Tells the ink-bar to align itself to the current label wrapper */\n    _alignInkBarToSelectedTab() {\n        const selectedItem = this._items && this._items.length ? this._items.toArray()[this.selectedIndex] : null;\n        const selectedLabelWrapper = selectedItem ? selectedItem.elementRef.nativeElement : null;\n        if (selectedLabelWrapper) {\n            this._inkBar.alignToElement(selectedLabelWrapper);\n        }\n        else {\n            this._inkBar.hide();\n        }\n    }\n    /** Stops the currently-running paginator interval.  */\n    _stopInterval() {\n        this._stopScrolling.next();\n    }\n    /**\n     * Handles the user pressing down on one of the paginators.\n     * Starts scrolling the header after a certain amount of time.\n     * @param direction In which direction the paginator should be scrolled.\n     */\n    _handlePaginatorPress(direction, mouseEvent) {\n        // Don't start auto scrolling for right mouse button clicks. Note that we shouldn't have to\n        // null check the `button`, but we do it so we don't break tests that use fake events.\n        if (mouseEvent && mouseEvent.button != null && mouseEvent.button !== 0) {\n            return;\n        }\n        // Avoid overlapping timers.\n        this._stopInterval();\n        // Start a timer after the delay and keep firing based on the interval.\n        timer(HEADER_SCROLL_DELAY, HEADER_SCROLL_INTERVAL)\n            // Keep the timer going until something tells it to stop or the component is destroyed.\n            .pipe(takeUntil(merge(this._stopScrolling, this._destroyed)))\n            .subscribe(() => {\n            const { maxScrollDistance, distance } = this._scrollHeader(direction);\n            // Stop the timer if we've reached the start or the end.\n            if (distance === 0 || distance >= maxScrollDistance) {\n                this._stopInterval();\n            }\n        });\n    }\n    /**\n     * Scrolls the header to a given position.\n     * @param position Position to which to scroll.\n     * @returns Information on the current scroll distance and the maximum.\n     */\n    _scrollTo(position) {\n        if (this.disablePagination) {\n            return { maxScrollDistance: 0, distance: 0 };\n        }\n        const maxScrollDistance = this._getMaxScrollDistance();\n        this._scrollDistance = Math.max(0, Math.min(maxScrollDistance, position));\n        // Mark that the scroll distance has changed so that after the view is checked, the CSS\n        // transformation can move the header.\n        this._scrollDistanceChanged = true;\n        this._checkScrollingControls();\n        return { maxScrollDistance, distance: this._scrollDistance };\n    }\n}\nMatPaginatedTabHeader.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatPaginatedTabHeader, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i1$1.ViewportRuler }, { token: i1.Directionality, optional: true }, { token: i0.NgZone }, { token: i3.Platform }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\nMatPaginatedTabHeader.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatPaginatedTabHeader, inputs: { disablePagination: \"disablePagination\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatPaginatedTabHeader, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i1$1.ViewportRuler }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.NgZone }, { type: i3.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { disablePagination: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Base class with all of the `MatTabHeader` functionality.\n * @docs-private\n */\nclass _MatTabHeaderBase extends MatPaginatedTabHeader {\n    constructor(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode) {\n        super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n        this._disableRipple = false;\n    }\n    /** Whether the ripple effect is disabled or not. */\n    get disableRipple() {\n        return this._disableRipple;\n    }\n    set disableRipple(value) {\n        this._disableRipple = coerceBooleanProperty(value);\n    }\n    _itemSelected(event) {\n        event.preventDefault();\n    }\n}\n_MatTabHeaderBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: _MatTabHeaderBase, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i1$1.ViewportRuler }, { token: i1.Directionality, optional: true }, { token: i0.NgZone }, { token: i3.Platform }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\n_MatTabHeaderBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: _MatTabHeaderBase, inputs: { disableRipple: \"disableRipple\" }, usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: _MatTabHeaderBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i1$1.ViewportRuler }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.NgZone }, { type: i3.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { disableRipple: [{\n                type: Input\n            }] } });\n/**\n * The header of the tab group which displays a list of all the tabs in the tab group. Includes\n * an ink bar that follows the currently selected tab. When the tabs list's width exceeds the\n * width of the header container, then arrows will be displayed to allow the user to scroll\n * left and right across the header.\n * @docs-private\n */\nclass MatTabHeader extends _MatTabHeaderBase {\n    constructor(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode) {\n        super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n    }\n}\nMatTabHeader.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatTabHeader, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i1$1.ViewportRuler }, { token: i1.Directionality, optional: true }, { token: i0.NgZone }, { token: i3.Platform }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nMatTabHeader.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatTabHeader, selector: \"mat-tab-header\", inputs: { selectedIndex: \"selectedIndex\" }, outputs: { selectFocusedIndex: \"selectFocusedIndex\", indexFocused: \"indexFocused\" }, host: { properties: { \"class.mat-tab-header-pagination-controls-enabled\": \"_showPaginationControls\", \"class.mat-tab-header-rtl\": \"_getLayoutDirection() == 'rtl'\" }, classAttribute: \"mat-tab-header\" }, queries: [{ propertyName: \"_items\", predicate: MatTabLabelWrapper }], viewQueries: [{ propertyName: \"_inkBar\", first: true, predicate: MatInkBar, descendants: true, static: true }, { propertyName: \"_tabListContainer\", first: true, predicate: [\"tabListContainer\"], descendants: true, static: true }, { propertyName: \"_tabList\", first: true, predicate: [\"tabList\"], descendants: true, static: true }, { propertyName: \"_tabListInner\", first: true, predicate: [\"tabListInner\"], descendants: true, static: true }, { propertyName: \"_nextPaginator\", first: true, predicate: [\"nextPaginator\"], descendants: true }, { propertyName: \"_previousPaginator\", first: true, predicate: [\"previousPaginator\"], descendants: true }], usesInheritance: true, ngImport: i0, template: \"<button class=\\\"mat-tab-header-pagination mat-tab-header-pagination-before mat-elevation-z4\\\"\\n     #previousPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     tabindex=\\\"-1\\\"\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     [disabled]=\\\"_disableScrollBefore || null\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\\n<div class=\\\"mat-tab-label-container\\\" #tabListContainer (keydown)=\\\"_handleKeydown($event)\\\">\\n  <div\\n    #tabList\\n    class=\\\"mat-tab-list\\\"\\n    [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\"\\n    role=\\\"tablist\\\"\\n    (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-tab-labels\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n    <mat-ink-bar></mat-ink-bar>\\n  </div>\\n</div>\\n\\n<button class=\\\"mat-tab-header-pagination mat-tab-header-pagination-after mat-elevation-z4\\\"\\n     #nextPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     [disabled]=\\\"_disableScrollAfter || null\\\"\\n     tabindex=\\\"-1\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\", styles: [\".mat-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mat-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-tab-header-pagination::-moz-focus-inner{border:0}.mat-tab-header-pagination-controls-enabled .mat-tab-header-pagination{display:flex}.mat-tab-header-pagination-before,.mat-tab-header-rtl .mat-tab-header-pagination-after{padding-left:4px}.mat-tab-header-pagination-before .mat-tab-header-pagination-chevron,.mat-tab-header-rtl .mat-tab-header-pagination-after .mat-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-tab-header-rtl .mat-tab-header-pagination-before,.mat-tab-header-pagination-after{padding-right:4px}.mat-tab-header-rtl .mat-tab-header-pagination-before .mat-tab-header-pagination-chevron,.mat-tab-header-pagination-after .mat-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px}.mat-tab-header-pagination-disabled{box-shadow:none;cursor:default}.mat-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-ink-bar{position:absolute;bottom:0;height:2px;transition:500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-ink-bar._mat-animation-noopable{transition:none !important;animation:none !important}.mat-tab-group-inverted-header .mat-ink-bar{bottom:auto;top:0}.cdk-high-contrast-active .mat-ink-bar{outline:solid 2px;height:0}.mat-tab-labels{display:flex}[mat-align-tabs=center]>.mat-tab-header .mat-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-tab-header .mat-tab-labels{justify-content:flex-end}.mat-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1}.mat-tab-list._mat-animation-noopable{transition:none !important;animation:none !important}.mat-tab-label{height:48px;padding:0 24px;cursor:pointer;box-sizing:border-box;opacity:.6;min-width:160px;text-align:center;display:inline-flex;justify-content:center;align-items:center;white-space:nowrap;position:relative}.mat-tab-label:focus{outline:none}.mat-tab-label:focus:not(.mat-tab-disabled){opacity:1}.mat-tab-label.mat-tab-disabled{cursor:default}.cdk-high-contrast-active .mat-tab-label.mat-tab-disabled{opacity:.5}.mat-tab-label .mat-tab-label-content{display:inline-flex;justify-content:center;align-items:center;white-space:nowrap}.cdk-high-contrast-active .mat-tab-label{opacity:1}.mat-tab-label::before{margin:5px}@media(max-width: 599px){.mat-tab-label{min-width:72px}}\"], dependencies: [{ kind: \"directive\", type: i4.MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"directive\", type: i5.CdkObserveContent, selector: \"[cdkObserveContent]\", inputs: [\"cdkObserveContentDisabled\", \"debounce\"], outputs: [\"cdkObserveContent\"], exportAs: [\"cdkObserveContent\"] }, { kind: \"directive\", type: MatInkBar, selector: \"mat-ink-bar\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatTabHeader, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tab-header', inputs: ['selectedIndex'], outputs: ['selectFocusedIndex', 'indexFocused'], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, host: {\n                        'class': 'mat-tab-header',\n                        '[class.mat-tab-header-pagination-controls-enabled]': '_showPaginationControls',\n                        '[class.mat-tab-header-rtl]': \"_getLayoutDirection() == 'rtl'\",\n                    }, template: \"<button class=\\\"mat-tab-header-pagination mat-tab-header-pagination-before mat-elevation-z4\\\"\\n     #previousPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     tabindex=\\\"-1\\\"\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     [disabled]=\\\"_disableScrollBefore || null\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\\n<div class=\\\"mat-tab-label-container\\\" #tabListContainer (keydown)=\\\"_handleKeydown($event)\\\">\\n  <div\\n    #tabList\\n    class=\\\"mat-tab-list\\\"\\n    [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\"\\n    role=\\\"tablist\\\"\\n    (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-tab-labels\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n    <mat-ink-bar></mat-ink-bar>\\n  </div>\\n</div>\\n\\n<button class=\\\"mat-tab-header-pagination mat-tab-header-pagination-after mat-elevation-z4\\\"\\n     #nextPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     [disabled]=\\\"_disableScrollAfter || null\\\"\\n     tabindex=\\\"-1\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\", styles: [\".mat-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mat-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-tab-header-pagination::-moz-focus-inner{border:0}.mat-tab-header-pagination-controls-enabled .mat-tab-header-pagination{display:flex}.mat-tab-header-pagination-before,.mat-tab-header-rtl .mat-tab-header-pagination-after{padding-left:4px}.mat-tab-header-pagination-before .mat-tab-header-pagination-chevron,.mat-tab-header-rtl .mat-tab-header-pagination-after .mat-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-tab-header-rtl .mat-tab-header-pagination-before,.mat-tab-header-pagination-after{padding-right:4px}.mat-tab-header-rtl .mat-tab-header-pagination-before .mat-tab-header-pagination-chevron,.mat-tab-header-pagination-after .mat-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px}.mat-tab-header-pagination-disabled{box-shadow:none;cursor:default}.mat-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-ink-bar{position:absolute;bottom:0;height:2px;transition:500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-ink-bar._mat-animation-noopable{transition:none !important;animation:none !important}.mat-tab-group-inverted-header .mat-ink-bar{bottom:auto;top:0}.cdk-high-contrast-active .mat-ink-bar{outline:solid 2px;height:0}.mat-tab-labels{display:flex}[mat-align-tabs=center]>.mat-tab-header .mat-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-tab-header .mat-tab-labels{justify-content:flex-end}.mat-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1}.mat-tab-list._mat-animation-noopable{transition:none !important;animation:none !important}.mat-tab-label{height:48px;padding:0 24px;cursor:pointer;box-sizing:border-box;opacity:.6;min-width:160px;text-align:center;display:inline-flex;justify-content:center;align-items:center;white-space:nowrap;position:relative}.mat-tab-label:focus{outline:none}.mat-tab-label:focus:not(.mat-tab-disabled){opacity:1}.mat-tab-label.mat-tab-disabled{cursor:default}.cdk-high-contrast-active .mat-tab-label.mat-tab-disabled{opacity:.5}.mat-tab-label .mat-tab-label-content{display:inline-flex;justify-content:center;align-items:center;white-space:nowrap}.cdk-high-contrast-active .mat-tab-label{opacity:1}.mat-tab-label::before{margin:5px}@media(max-width: 599px){.mat-tab-label{min-width:72px}}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i1$1.ViewportRuler }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.NgZone }, { type: i3.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { _items: [{\n                type: ContentChildren,\n                args: [MatTabLabelWrapper, { descendants: false }]\n            }], _inkBar: [{\n                type: ViewChild,\n                args: [MatInkBar, { static: true }]\n            }], _tabListContainer: [{\n                type: ViewChild,\n                args: ['tabListContainer', { static: true }]\n            }], _tabList: [{\n                type: ViewChild,\n                args: ['tabList', { static: true }]\n            }], _tabListInner: [{\n                type: ViewChild,\n                args: ['tabListInner', { static: true }]\n            }], _nextPaginator: [{\n                type: ViewChild,\n                args: ['nextPaginator']\n            }], _previousPaginator: [{\n                type: ViewChild,\n                args: ['previousPaginator']\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Used to generate unique ID's for each tab component */\nlet nextId = 0;\n/** A simple change event emitted on focus or selection changes. */\nclass MatTabChangeEvent {\n}\n// Boilerplate for applying mixins to MatTabGroup.\n/** @docs-private */\nconst _MatTabGroupMixinBase = mixinColor(mixinDisableRipple(class {\n    constructor(_elementRef) {\n        this._elementRef = _elementRef;\n    }\n}), 'primary');\n/**\n * Base class with all of the `MatTabGroupBase` functionality.\n * @docs-private\n */\nclass _MatTabGroupBase extends _MatTabGroupMixinBase {\n    constructor(elementRef, _changeDetectorRef, defaultConfig, _animationMode) {\n        super(elementRef);\n        this._changeDetectorRef = _changeDetectorRef;\n        this._animationMode = _animationMode;\n        /** All of the tabs that belong to the group. */\n        this._tabs = new QueryList();\n        /** The tab index that should be selected after the content has been checked. */\n        this._indexToSelect = 0;\n        /** Index of the tab that was focused last. */\n        this._lastFocusedTabIndex = null;\n        /** Snapshot of the height of the tab body wrapper before another tab is activated. */\n        this._tabBodyWrapperHeight = 0;\n        /** Subscription to tabs being added/removed. */\n        this._tabsSubscription = Subscription.EMPTY;\n        /** Subscription to changes in the tab labels. */\n        this._tabLabelSubscription = Subscription.EMPTY;\n        this._dynamicHeight = false;\n        this._selectedIndex = null;\n        /** Position of the tab header. */\n        this.headerPosition = 'above';\n        this._disablePagination = false;\n        this._preserveContent = false;\n        /** Output to enable support for two-way binding on `[(selectedIndex)]` */\n        this.selectedIndexChange = new EventEmitter();\n        /** Event emitted when focus has changed within a tab group. */\n        this.focusChange = new EventEmitter();\n        /** Event emitted when the body animation has completed */\n        this.animationDone = new EventEmitter();\n        /** Event emitted when the tab selection has changed. */\n        this.selectedTabChange = new EventEmitter(true);\n        this._groupId = nextId++;\n        this.animationDuration =\n            defaultConfig && defaultConfig.animationDuration ? defaultConfig.animationDuration : '500ms';\n        this.disablePagination =\n            defaultConfig && defaultConfig.disablePagination != null\n                ? defaultConfig.disablePagination\n                : false;\n        this.dynamicHeight =\n            defaultConfig && defaultConfig.dynamicHeight != null ? defaultConfig.dynamicHeight : false;\n        this.contentTabIndex = defaultConfig?.contentTabIndex ?? null;\n        this.preserveContent = !!defaultConfig?.preserveContent;\n    }\n    /** Whether the tab group should grow to the size of the active tab. */\n    get dynamicHeight() {\n        return this._dynamicHeight;\n    }\n    set dynamicHeight(value) {\n        this._dynamicHeight = coerceBooleanProperty(value);\n    }\n    /** The index of the active tab. */\n    get selectedIndex() {\n        return this._selectedIndex;\n    }\n    set selectedIndex(value) {\n        this._indexToSelect = coerceNumberProperty(value, null);\n    }\n    /** Duration for the tab animation. Will be normalized to milliseconds if no units are set. */\n    get animationDuration() {\n        return this._animationDuration;\n    }\n    set animationDuration(value) {\n        this._animationDuration = /^\\d+$/.test(value + '') ? value + 'ms' : value;\n    }\n    /**\n     * `tabindex` to be set on the inner element that wraps the tab content. Can be used for improved\n     * accessibility when the tab does not have focusable elements or if it has scrollable content.\n     * The `tabindex` will be removed automatically for inactive tabs.\n     * Read more at https://www.w3.org/TR/wai-aria-practices/examples/tabs/tabs-2/tabs.html\n     */\n    get contentTabIndex() {\n        return this._contentTabIndex;\n    }\n    set contentTabIndex(value) {\n        this._contentTabIndex = coerceNumberProperty(value, null);\n    }\n    /**\n     * Whether pagination should be disabled. This can be used to avoid unnecessary\n     * layout recalculations if it's known that pagination won't be required.\n     */\n    get disablePagination() {\n        return this._disablePagination;\n    }\n    set disablePagination(value) {\n        this._disablePagination = coerceBooleanProperty(value);\n    }\n    /**\n     * By default tabs remove their content from the DOM while it's off-screen.\n     * Setting this to `true` will keep it in the DOM which will prevent elements\n     * like iframes and videos from reloading next time it comes back into the view.\n     */\n    get preserveContent() {\n        return this._preserveContent;\n    }\n    set preserveContent(value) {\n        this._preserveContent = coerceBooleanProperty(value);\n    }\n    /** Background color of the tab group. */\n    get backgroundColor() {\n        return this._backgroundColor;\n    }\n    set backgroundColor(value) {\n        const nativeElement = this._elementRef.nativeElement;\n        nativeElement.classList.remove(`mat-background-${this.backgroundColor}`);\n        if (value) {\n            nativeElement.classList.add(`mat-background-${value}`);\n        }\n        this._backgroundColor = value;\n    }\n    /**\n     * After the content is checked, this component knows what tabs have been defined\n     * and what the selected index should be. This is where we can know exactly what position\n     * each tab should be in according to the new selected index, and additionally we know how\n     * a new selected tab should transition in (from the left or right).\n     */\n    ngAfterContentChecked() {\n        // Don't clamp the `indexToSelect` immediately in the setter because it can happen that\n        // the amount of tabs changes before the actual change detection runs.\n        const indexToSelect = (this._indexToSelect = this._clampTabIndex(this._indexToSelect));\n        // If there is a change in selected index, emit a change event. Should not trigger if\n        // the selected index has not yet been initialized.\n        if (this._selectedIndex != indexToSelect) {\n            const isFirstRun = this._selectedIndex == null;\n            if (!isFirstRun) {\n                this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n                // Preserve the height so page doesn't scroll up during tab change.\n                // Fixes https://stackblitz.com/edit/mat-tabs-scroll-page-top-on-tab-change\n                const wrapper = this._tabBodyWrapper.nativeElement;\n                wrapper.style.minHeight = wrapper.clientHeight + 'px';\n            }\n            // Changing these values after change detection has run\n            // since the checked content may contain references to them.\n            Promise.resolve().then(() => {\n                this._tabs.forEach((tab, index) => (tab.isActive = index === indexToSelect));\n                if (!isFirstRun) {\n                    this.selectedIndexChange.emit(indexToSelect);\n                    // Clear the min-height, this was needed during tab change to avoid\n                    // unnecessary scrolling.\n                    this._tabBodyWrapper.nativeElement.style.minHeight = '';\n                }\n            });\n        }\n        // Setup the position for each tab and optionally setup an origin on the next selected tab.\n        this._tabs.forEach((tab, index) => {\n            tab.position = index - indexToSelect;\n            // If there is already a selected tab, then set up an origin for the next selected tab\n            // if it doesn't have one already.\n            if (this._selectedIndex != null && tab.position == 0 && !tab.origin) {\n                tab.origin = indexToSelect - this._selectedIndex;\n            }\n        });\n        if (this._selectedIndex !== indexToSelect) {\n            this._selectedIndex = indexToSelect;\n            this._lastFocusedTabIndex = null;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    ngAfterContentInit() {\n        this._subscribeToAllTabChanges();\n        this._subscribeToTabLabels();\n        // Subscribe to changes in the amount of tabs, in order to be\n        // able to re-render the content as new tabs are added or removed.\n        this._tabsSubscription = this._tabs.changes.subscribe(() => {\n            const indexToSelect = this._clampTabIndex(this._indexToSelect);\n            // Maintain the previously-selected tab if a new tab is added or removed and there is no\n            // explicit change that selects a different tab.\n            if (indexToSelect === this._selectedIndex) {\n                const tabs = this._tabs.toArray();\n                let selectedTab;\n                for (let i = 0; i < tabs.length; i++) {\n                    if (tabs[i].isActive) {\n                        // Assign both to the `_indexToSelect` and `_selectedIndex` so we don't fire a changed\n                        // event, otherwise the consumer may end up in an infinite loop in some edge cases like\n                        // adding a tab within the `selectedIndexChange` event.\n                        this._indexToSelect = this._selectedIndex = i;\n                        this._lastFocusedTabIndex = null;\n                        selectedTab = tabs[i];\n                        break;\n                    }\n                }\n                // If we haven't found an active tab and a tab exists at the selected index, it means\n                // that the active tab was swapped out. Since this won't be picked up by the rendering\n                // loop in `ngAfterContentChecked`, we need to sync it up manually.\n                if (!selectedTab && tabs[indexToSelect]) {\n                    Promise.resolve().then(() => {\n                        tabs[indexToSelect].isActive = true;\n                        this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n                    });\n                }\n            }\n            this._changeDetectorRef.markForCheck();\n        });\n    }\n    /** Listens to changes in all of the tabs. */\n    _subscribeToAllTabChanges() {\n        // Since we use a query with `descendants: true` to pick up the tabs, we may end up catching\n        // some that are inside of nested tab groups. We filter them out manually by checking that\n        // the closest group to the tab is the current one.\n        this._allTabs.changes.pipe(startWith(this._allTabs)).subscribe((tabs) => {\n            this._tabs.reset(tabs.filter(tab => {\n                return tab._closestTabGroup === this || !tab._closestTabGroup;\n            }));\n            this._tabs.notifyOnChanges();\n        });\n    }\n    ngOnDestroy() {\n        this._tabs.destroy();\n        this._tabsSubscription.unsubscribe();\n        this._tabLabelSubscription.unsubscribe();\n    }\n    /** Re-aligns the ink bar to the selected tab element. */\n    realignInkBar() {\n        if (this._tabHeader) {\n            this._tabHeader._alignInkBarToSelectedTab();\n        }\n    }\n    /**\n     * Recalculates the tab group's pagination dimensions.\n     *\n     * WARNING: Calling this method can be very costly in terms of performance. It should be called\n     * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n     * page.\n     */\n    updatePagination() {\n        if (this._tabHeader) {\n            this._tabHeader.updatePagination();\n        }\n    }\n    /**\n     * Sets focus to a particular tab.\n     * @param index Index of the tab to be focused.\n     */\n    focusTab(index) {\n        const header = this._tabHeader;\n        if (header) {\n            header.focusIndex = index;\n        }\n    }\n    _focusChanged(index) {\n        this._lastFocusedTabIndex = index;\n        this.focusChange.emit(this._createChangeEvent(index));\n    }\n    _createChangeEvent(index) {\n        const event = new MatTabChangeEvent();\n        event.index = index;\n        if (this._tabs && this._tabs.length) {\n            event.tab = this._tabs.toArray()[index];\n        }\n        return event;\n    }\n    /**\n     * Subscribes to changes in the tab labels. This is needed, because the @Input for the label is\n     * on the MatTab component, whereas the data binding is inside the MatTabGroup. In order for the\n     * binding to be updated, we need to subscribe to changes in it and trigger change detection\n     * manually.\n     */\n    _subscribeToTabLabels() {\n        if (this._tabLabelSubscription) {\n            this._tabLabelSubscription.unsubscribe();\n        }\n        this._tabLabelSubscription = merge(...this._tabs.map(tab => tab._stateChanges)).subscribe(() => this._changeDetectorRef.markForCheck());\n    }\n    /** Clamps the given index to the bounds of 0 and the tabs length. */\n    _clampTabIndex(index) {\n        // Note the `|| 0`, which ensures that values like NaN can't get through\n        // and which would otherwise throw the component into an infinite loop\n        // (since Math.max(NaN, 0) === NaN).\n        return Math.min(this._tabs.length - 1, Math.max(index || 0, 0));\n    }\n    /** Returns a unique id for each tab label element */\n    _getTabLabelId(i) {\n        return `mat-tab-label-${this._groupId}-${i}`;\n    }\n    /** Returns a unique id for each tab content element */\n    _getTabContentId(i) {\n        return `mat-tab-content-${this._groupId}-${i}`;\n    }\n    /**\n     * Sets the height of the body wrapper to the height of the activating tab if dynamic\n     * height property is true.\n     */\n    _setTabBodyWrapperHeight(tabHeight) {\n        if (!this._dynamicHeight || !this._tabBodyWrapperHeight) {\n            return;\n        }\n        const wrapper = this._tabBodyWrapper.nativeElement;\n        wrapper.style.height = this._tabBodyWrapperHeight + 'px';\n        // This conditional forces the browser to paint the height so that\n        // the animation to the new height can have an origin.\n        if (this._tabBodyWrapper.nativeElement.offsetHeight) {\n            wrapper.style.height = tabHeight + 'px';\n        }\n    }\n    /** Removes the height of the tab body wrapper. */\n    _removeTabBodyWrapperHeight() {\n        const wrapper = this._tabBodyWrapper.nativeElement;\n        this._tabBodyWrapperHeight = wrapper.clientHeight;\n        wrapper.style.height = '';\n        this.animationDone.emit();\n    }\n    /** Handle click events, setting new selected index if appropriate. */\n    _handleClick(tab, tabHeader, index) {\n        if (!tab.disabled) {\n            this.selectedIndex = tabHeader.focusIndex = index;\n        }\n    }\n    /** Retrieves the tabindex for the tab. */\n    _getTabIndex(tab, index) {\n        if (tab.disabled) {\n            return null;\n        }\n        const targetIndex = this._lastFocusedTabIndex ?? this.selectedIndex;\n        return index === targetIndex ? 0 : -1;\n    }\n    /** Callback for when the focused state of a tab has changed. */\n    _tabFocusChanged(focusOrigin, index) {\n        // Mouse/touch focus happens during the `mousedown`/`touchstart` phase which\n        // can cause the tab to be moved out from under the pointer, interrupting the\n        // click sequence (see #21898). We don't need to scroll the tab into view for\n        // such cases anyway, because it will be done when the tab becomes selected.\n        if (focusOrigin && focusOrigin !== 'mouse' && focusOrigin !== 'touch') {\n            this._tabHeader.focusIndex = index;\n        }\n    }\n}\n_MatTabGroupBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: _MatTabGroupBase, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: MAT_TABS_CONFIG, optional: true }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\n_MatTabGroupBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: _MatTabGroupBase, inputs: { dynamicHeight: \"dynamicHeight\", selectedIndex: \"selectedIndex\", headerPosition: \"headerPosition\", animationDuration: \"animationDuration\", contentTabIndex: \"contentTabIndex\", disablePagination: \"disablePagination\", preserveContent: \"preserveContent\", backgroundColor: \"backgroundColor\" }, outputs: { selectedIndexChange: \"selectedIndexChange\", focusChange: \"focusChange\", animationDone: \"animationDone\", selectedTabChange: \"selectedTabChange\" }, usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: _MatTabGroupBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_TABS_CONFIG]\n                }, {\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { dynamicHeight: [{\n                type: Input\n            }], selectedIndex: [{\n                type: Input\n            }], headerPosition: [{\n                type: Input\n            }], animationDuration: [{\n                type: Input\n            }], contentTabIndex: [{\n                type: Input\n            }], disablePagination: [{\n                type: Input\n            }], preserveContent: [{\n                type: Input\n            }], backgroundColor: [{\n                type: Input\n            }], selectedIndexChange: [{\n                type: Output\n            }], focusChange: [{\n                type: Output\n            }], animationDone: [{\n                type: Output\n            }], selectedTabChange: [{\n                type: Output\n            }] } });\n/**\n * Material design tab-group component. Supports basic tab pairs (label + content) and includes\n * animated ink-bar, keyboard navigation, and screen reader.\n * See: https://material.io/design/components/tabs.html\n */\nclass MatTabGroup extends _MatTabGroupBase {\n    constructor(elementRef, changeDetectorRef, defaultConfig, animationMode) {\n        super(elementRef, changeDetectorRef, defaultConfig, animationMode);\n    }\n}\nMatTabGroup.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatTabGroup, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: MAT_TABS_CONFIG, optional: true }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nMatTabGroup.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatTabGroup, selector: \"mat-tab-group\", inputs: { color: \"color\", disableRipple: \"disableRipple\" }, host: { properties: { \"class.mat-tab-group-dynamic-height\": \"dynamicHeight\", \"class.mat-tab-group-inverted-header\": \"headerPosition === \\\"below\\\"\" }, classAttribute: \"mat-tab-group\" }, providers: [\n        {\n            provide: MAT_TAB_GROUP,\n            useExisting: MatTabGroup,\n        },\n    ], queries: [{ propertyName: \"_allTabs\", predicate: MatTab, descendants: true }], viewQueries: [{ propertyName: \"_tabBodyWrapper\", first: true, predicate: [\"tabBodyWrapper\"], descendants: true }, { propertyName: \"_tabHeader\", first: true, predicate: [\"tabHeader\"], descendants: true }], exportAs: [\"matTabGroup\"], usesInheritance: true, ngImport: i0, template: \"<mat-tab-header #tabHeader\\n               [selectedIndex]=\\\"selectedIndex || 0\\\"\\n               [disableRipple]=\\\"disableRipple\\\"\\n               [disablePagination]=\\\"disablePagination\\\"\\n               (indexFocused)=\\\"_focusChanged($event)\\\"\\n               (selectFocusedIndex)=\\\"selectedIndex = $event\\\">\\n  <div class=\\\"mat-tab-label mat-focus-indicator\\\" role=\\\"tab\\\" matTabLabelWrapper mat-ripple\\n       cdkMonitorElementFocus\\n       *ngFor=\\\"let tab of _tabs; let i = index\\\"\\n       [id]=\\\"_getTabLabelId(i)\\\"\\n       [attr.tabIndex]=\\\"_getTabIndex(tab, i)\\\"\\n       [attr.aria-posinset]=\\\"i + 1\\\"\\n       [attr.aria-setsize]=\\\"_tabs.length\\\"\\n       [attr.aria-controls]=\\\"_getTabContentId(i)\\\"\\n       [attr.aria-selected]=\\\"selectedIndex === i\\\"\\n       [attr.aria-label]=\\\"tab.ariaLabel || null\\\"\\n       [attr.aria-labelledby]=\\\"(!tab.ariaLabel && tab.ariaLabelledby) ? tab.ariaLabelledby : null\\\"\\n       [class.mat-tab-label-active]=\\\"selectedIndex === i\\\"\\n       [ngClass]=\\\"tab.labelClass\\\"\\n       [disabled]=\\\"tab.disabled\\\"\\n       [matRippleDisabled]=\\\"tab.disabled || disableRipple\\\"\\n       (click)=\\\"_handleClick(tab, tabHeader, i)\\\"\\n       (cdkFocusChange)=\\\"_tabFocusChanged($event, i)\\\">\\n\\n\\n    <div class=\\\"mat-tab-label-content\\\">\\n      <!-- If there is a label template, use it. -->\\n      <ng-template [ngIf]=\\\"tab.templateLabel\\\" [ngIfElse]=\\\"tabTextLabel\\\">\\n        <ng-template [cdkPortalOutlet]=\\\"tab.templateLabel\\\"></ng-template>\\n      </ng-template>\\n\\n      <!-- If there is not a label template, fall back to the text label. -->\\n      <ng-template #tabTextLabel>{{tab.textLabel}}</ng-template>\\n    </div>\\n  </div>\\n</mat-tab-header>\\n\\n<div\\n  class=\\\"mat-tab-body-wrapper\\\"\\n  [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\"\\n  #tabBodyWrapper>\\n  <mat-tab-body role=\\\"tabpanel\\\"\\n               *ngFor=\\\"let tab of _tabs; let i = index\\\"\\n               [id]=\\\"_getTabContentId(i)\\\"\\n               [attr.tabindex]=\\\"(contentTabIndex != null && selectedIndex === i) ? contentTabIndex : null\\\"\\n               [attr.aria-labelledby]=\\\"_getTabLabelId(i)\\\"\\n               [class.mat-tab-body-active]=\\\"selectedIndex === i\\\"\\n               [ngClass]=\\\"tab.bodyClass\\\"\\n               [content]=\\\"tab.content!\\\"\\n               [position]=\\\"tab.position!\\\"\\n               [origin]=\\\"tab.origin\\\"\\n               [animationDuration]=\\\"animationDuration\\\"\\n               [preserveContent]=\\\"preserveContent\\\"\\n               (_onCentered)=\\\"_removeTabBodyWrapperHeight()\\\"\\n               (_onCentering)=\\\"_setTabBodyWrapperHeight($event)\\\">\\n  </mat-tab-body>\\n</div>\\n\", styles: [\".mat-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-tab-group.mat-tab-group-inverted-header{flex-direction:column-reverse}.mat-tab-label{height:48px;padding:0 24px;cursor:pointer;box-sizing:border-box;opacity:.6;min-width:160px;text-align:center;display:inline-flex;justify-content:center;align-items:center;white-space:nowrap;position:relative}.mat-tab-label:focus{outline:none}.mat-tab-label:focus:not(.mat-tab-disabled){opacity:1}.mat-tab-label.mat-tab-disabled{cursor:default}.cdk-high-contrast-active .mat-tab-label.mat-tab-disabled{opacity:.5}.mat-tab-label .mat-tab-label-content{display:inline-flex;justify-content:center;align-items:center;white-space:nowrap}.cdk-high-contrast-active .mat-tab-label{opacity:1}@media(max-width: 599px){.mat-tab-label{padding:0 12px}}@media(max-width: 959px){.mat-tab-label{padding:0 12px}}.mat-tab-group[mat-stretch-tabs]>.mat-tab-header .mat-tab-label{flex-basis:0;flex-grow:1}.mat-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}.mat-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-tab-body.mat-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-tab-group.mat-tab-group-dynamic-height .mat-tab-body.mat-tab-body-active{overflow-y:hidden}\"], dependencies: [{ kind: \"directive\", type: i1$2.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1$2.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i1$2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i2.CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }, { kind: \"directive\", type: i4.MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"directive\", type: i7.CdkMonitorFocus, selector: \"[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]\", outputs: [\"cdkFocusChange\"], exportAs: [\"cdkMonitorFocus\"] }, { kind: \"directive\", type: MatTabLabelWrapper, selector: \"[matTabLabelWrapper]\", inputs: [\"disabled\"] }, { kind: \"component\", type: MatTabBody, selector: \"mat-tab-body\" }, { kind: \"component\", type: MatTabHeader, selector: \"mat-tab-header\", inputs: [\"selectedIndex\"], outputs: [\"selectFocusedIndex\", \"indexFocused\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatTabGroup, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tab-group', exportAs: 'matTabGroup', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, inputs: ['color', 'disableRipple'], providers: [\n                        {\n                            provide: MAT_TAB_GROUP,\n                            useExisting: MatTabGroup,\n                        },\n                    ], host: {\n                        'class': 'mat-tab-group',\n                        '[class.mat-tab-group-dynamic-height]': 'dynamicHeight',\n                        '[class.mat-tab-group-inverted-header]': 'headerPosition === \"below\"',\n                    }, template: \"<mat-tab-header #tabHeader\\n               [selectedIndex]=\\\"selectedIndex || 0\\\"\\n               [disableRipple]=\\\"disableRipple\\\"\\n               [disablePagination]=\\\"disablePagination\\\"\\n               (indexFocused)=\\\"_focusChanged($event)\\\"\\n               (selectFocusedIndex)=\\\"selectedIndex = $event\\\">\\n  <div class=\\\"mat-tab-label mat-focus-indicator\\\" role=\\\"tab\\\" matTabLabelWrapper mat-ripple\\n       cdkMonitorElementFocus\\n       *ngFor=\\\"let tab of _tabs; let i = index\\\"\\n       [id]=\\\"_getTabLabelId(i)\\\"\\n       [attr.tabIndex]=\\\"_getTabIndex(tab, i)\\\"\\n       [attr.aria-posinset]=\\\"i + 1\\\"\\n       [attr.aria-setsize]=\\\"_tabs.length\\\"\\n       [attr.aria-controls]=\\\"_getTabContentId(i)\\\"\\n       [attr.aria-selected]=\\\"selectedIndex === i\\\"\\n       [attr.aria-label]=\\\"tab.ariaLabel || null\\\"\\n       [attr.aria-labelledby]=\\\"(!tab.ariaLabel && tab.ariaLabelledby) ? tab.ariaLabelledby : null\\\"\\n       [class.mat-tab-label-active]=\\\"selectedIndex === i\\\"\\n       [ngClass]=\\\"tab.labelClass\\\"\\n       [disabled]=\\\"tab.disabled\\\"\\n       [matRippleDisabled]=\\\"tab.disabled || disableRipple\\\"\\n       (click)=\\\"_handleClick(tab, tabHeader, i)\\\"\\n       (cdkFocusChange)=\\\"_tabFocusChanged($event, i)\\\">\\n\\n\\n    <div class=\\\"mat-tab-label-content\\\">\\n      <!-- If there is a label template, use it. -->\\n      <ng-template [ngIf]=\\\"tab.templateLabel\\\" [ngIfElse]=\\\"tabTextLabel\\\">\\n        <ng-template [cdkPortalOutlet]=\\\"tab.templateLabel\\\"></ng-template>\\n      </ng-template>\\n\\n      <!-- If there is not a label template, fall back to the text label. -->\\n      <ng-template #tabTextLabel>{{tab.textLabel}}</ng-template>\\n    </div>\\n  </div>\\n</mat-tab-header>\\n\\n<div\\n  class=\\\"mat-tab-body-wrapper\\\"\\n  [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\"\\n  #tabBodyWrapper>\\n  <mat-tab-body role=\\\"tabpanel\\\"\\n               *ngFor=\\\"let tab of _tabs; let i = index\\\"\\n               [id]=\\\"_getTabContentId(i)\\\"\\n               [attr.tabindex]=\\\"(contentTabIndex != null && selectedIndex === i) ? contentTabIndex : null\\\"\\n               [attr.aria-labelledby]=\\\"_getTabLabelId(i)\\\"\\n               [class.mat-tab-body-active]=\\\"selectedIndex === i\\\"\\n               [ngClass]=\\\"tab.bodyClass\\\"\\n               [content]=\\\"tab.content!\\\"\\n               [position]=\\\"tab.position!\\\"\\n               [origin]=\\\"tab.origin\\\"\\n               [animationDuration]=\\\"animationDuration\\\"\\n               [preserveContent]=\\\"preserveContent\\\"\\n               (_onCentered)=\\\"_removeTabBodyWrapperHeight()\\\"\\n               (_onCentering)=\\\"_setTabBodyWrapperHeight($event)\\\">\\n  </mat-tab-body>\\n</div>\\n\", styles: [\".mat-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-tab-group.mat-tab-group-inverted-header{flex-direction:column-reverse}.mat-tab-label{height:48px;padding:0 24px;cursor:pointer;box-sizing:border-box;opacity:.6;min-width:160px;text-align:center;display:inline-flex;justify-content:center;align-items:center;white-space:nowrap;position:relative}.mat-tab-label:focus{outline:none}.mat-tab-label:focus:not(.mat-tab-disabled){opacity:1}.mat-tab-label.mat-tab-disabled{cursor:default}.cdk-high-contrast-active .mat-tab-label.mat-tab-disabled{opacity:.5}.mat-tab-label .mat-tab-label-content{display:inline-flex;justify-content:center;align-items:center;white-space:nowrap}.cdk-high-contrast-active .mat-tab-label{opacity:1}@media(max-width: 599px){.mat-tab-label{padding:0 12px}}@media(max-width: 959px){.mat-tab-label{padding:0 12px}}.mat-tab-group[mat-stretch-tabs]>.mat-tab-header .mat-tab-label{flex-basis:0;flex-grow:1}.mat-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}.mat-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-tab-body.mat-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-tab-group.mat-tab-group-dynamic-height .mat-tab-body.mat-tab-body-active{overflow-y:hidden}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_TABS_CONFIG]\n                }, {\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { _allTabs: [{\n                type: ContentChildren,\n                args: [MatTab, { descendants: true }]\n            }], _tabBodyWrapper: [{\n                type: ViewChild,\n                args: ['tabBodyWrapper']\n            }], _tabHeader: [{\n                type: ViewChild,\n                args: ['tabHeader']\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Increasing integer for generating unique ids for tab nav components.\nlet nextUniqueId = 0;\n/**\n * Base class with all of the `MatTabNav` functionality.\n * @docs-private\n */\nclass _MatTabNavBase extends MatPaginatedTabHeader {\n    constructor(elementRef, dir, ngZone, changeDetectorRef, viewportRuler, platform, animationMode) {\n        super(elementRef, changeDetectorRef, viewportRuler, dir, ngZone, platform, animationMode);\n        this._disableRipple = false;\n        /** Theme color of the nav bar. */\n        this.color = 'primary';\n    }\n    /** Background color of the tab nav. */\n    get backgroundColor() {\n        return this._backgroundColor;\n    }\n    set backgroundColor(value) {\n        const classList = this._elementRef.nativeElement.classList;\n        classList.remove(`mat-background-${this.backgroundColor}`);\n        if (value) {\n            classList.add(`mat-background-${value}`);\n        }\n        this._backgroundColor = value;\n    }\n    /** Whether the ripple effect is disabled or not. */\n    get disableRipple() {\n        return this._disableRipple;\n    }\n    set disableRipple(value) {\n        this._disableRipple = coerceBooleanProperty(value);\n    }\n    _itemSelected() {\n        // noop\n    }\n    ngAfterContentInit() {\n        // We need this to run before the `changes` subscription in parent to ensure that the\n        // selectedIndex is up-to-date by the time the super class starts looking for it.\n        this._items.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n            this.updateActiveLink();\n        });\n        super.ngAfterContentInit();\n    }\n    /** Notifies the component that the active link has been changed. */\n    updateActiveLink() {\n        if (!this._items) {\n            return;\n        }\n        const items = this._items.toArray();\n        for (let i = 0; i < items.length; i++) {\n            if (items[i].active) {\n                this.selectedIndex = i;\n                this._changeDetectorRef.markForCheck();\n                if (this.tabPanel) {\n                    this.tabPanel._activeTabId = items[i].id;\n                }\n                return;\n            }\n        }\n        // The ink bar should hide itself if no items are active.\n        this.selectedIndex = -1;\n        this._inkBar.hide();\n    }\n    _getRole() {\n        return this.tabPanel ? 'tablist' : this._elementRef.nativeElement.getAttribute('role');\n    }\n}\n_MatTabNavBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: _MatTabNavBase, deps: [{ token: i0.ElementRef }, { token: i1.Directionality, optional: true }, { token: i0.NgZone }, { token: i0.ChangeDetectorRef }, { token: i1$1.ViewportRuler }, { token: i3.Platform }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\n_MatTabNavBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: _MatTabNavBase, inputs: { backgroundColor: \"backgroundColor\", disableRipple: \"disableRipple\", color: \"color\", tabPanel: \"tabPanel\" }, usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: _MatTabNavBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.NgZone }, { type: i0.ChangeDetectorRef }, { type: i1$1.ViewportRuler }, { type: i3.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { backgroundColor: [{\n                type: Input\n            }], disableRipple: [{\n                type: Input\n            }], color: [{\n                type: Input\n            }], tabPanel: [{\n                type: Input\n            }] } });\n/**\n * Navigation component matching the styles of the tab group header.\n * Provides anchored navigation with animated ink bar.\n */\nclass MatTabNav extends _MatTabNavBase {\n    constructor(elementRef, dir, ngZone, changeDetectorRef, viewportRuler, platform, animationMode) {\n        super(elementRef, dir, ngZone, changeDetectorRef, viewportRuler, platform, animationMode);\n    }\n}\nMatTabNav.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatTabNav, deps: [{ token: i0.ElementRef }, { token: i1.Directionality, optional: true }, { token: i0.NgZone }, { token: i0.ChangeDetectorRef }, { token: i1$1.ViewportRuler }, { token: i3.Platform }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nMatTabNav.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatTabNav, selector: \"[mat-tab-nav-bar]\", inputs: { color: \"color\" }, host: { properties: { \"attr.role\": \"_getRole()\", \"class.mat-tab-header-pagination-controls-enabled\": \"_showPaginationControls\", \"class.mat-tab-header-rtl\": \"_getLayoutDirection() == 'rtl'\", \"class.mat-primary\": \"color !== \\\"warn\\\" && color !== \\\"accent\\\"\", \"class.mat-accent\": \"color === \\\"accent\\\"\", \"class.mat-warn\": \"color === \\\"warn\\\"\" }, classAttribute: \"mat-tab-nav-bar mat-tab-header\" }, queries: [{ propertyName: \"_items\", predicate: i0.forwardRef(function () { return MatTabLink; }), descendants: true }], viewQueries: [{ propertyName: \"_inkBar\", first: true, predicate: MatInkBar, descendants: true, static: true }, { propertyName: \"_tabListContainer\", first: true, predicate: [\"tabListContainer\"], descendants: true, static: true }, { propertyName: \"_tabList\", first: true, predicate: [\"tabList\"], descendants: true, static: true }, { propertyName: \"_tabListInner\", first: true, predicate: [\"tabListInner\"], descendants: true, static: true }, { propertyName: \"_nextPaginator\", first: true, predicate: [\"nextPaginator\"], descendants: true }, { propertyName: \"_previousPaginator\", first: true, predicate: [\"previousPaginator\"], descendants: true }], exportAs: [\"matTabNavBar\", \"matTabNav\"], usesInheritance: true, ngImport: i0, template: \"<button class=\\\"mat-tab-header-pagination mat-tab-header-pagination-before mat-elevation-z4\\\"\\n     #previousPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     tabindex=\\\"-1\\\"\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     [disabled]=\\\"_disableScrollBefore || null\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\\n<div class=\\\"mat-tab-link-container\\\" #tabListContainer (keydown)=\\\"_handleKeydown($event)\\\">\\n  <div\\n    class=\\\"mat-tab-list\\\"\\n    [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\"\\n    #tabList\\n    (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-tab-links\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n    <mat-ink-bar></mat-ink-bar>\\n  </div>\\n</div>\\n\\n<button class=\\\"mat-tab-header-pagination mat-tab-header-pagination-after mat-elevation-z4\\\"\\n     #nextPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     [disabled]=\\\"_disableScrollAfter || null\\\"\\n     tabindex=\\\"-1\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\", styles: [\".mat-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mat-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-tab-header-pagination::-moz-focus-inner{border:0}.mat-tab-header-pagination-controls-enabled .mat-tab-header-pagination{display:flex}.mat-tab-header-pagination-before,.mat-tab-header-rtl .mat-tab-header-pagination-after{padding-left:4px}.mat-tab-header-pagination-before .mat-tab-header-pagination-chevron,.mat-tab-header-rtl .mat-tab-header-pagination-after .mat-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-tab-header-rtl .mat-tab-header-pagination-before,.mat-tab-header-pagination-after{padding-right:4px}.mat-tab-header-rtl .mat-tab-header-pagination-before .mat-tab-header-pagination-chevron,.mat-tab-header-pagination-after .mat-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px}.mat-tab-header-pagination-disabled{box-shadow:none;cursor:default}.mat-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-tab-links{display:flex}[mat-align-tabs=center]>.mat-tab-link-container .mat-tab-links{justify-content:center}[mat-align-tabs=end]>.mat-tab-link-container .mat-tab-links{justify-content:flex-end}.mat-ink-bar{position:absolute;bottom:0;height:2px;transition:500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-ink-bar._mat-animation-noopable{transition:none !important;animation:none !important}.mat-tab-group-inverted-header .mat-ink-bar{bottom:auto;top:0}.cdk-high-contrast-active .mat-ink-bar{outline:solid 2px;height:0}.mat-tab-link-container{display:flex;flex-grow:1;overflow:hidden;z-index:1}.mat-tab-link{height:48px;padding:0 24px;cursor:pointer;box-sizing:border-box;opacity:.6;min-width:160px;text-align:center;display:inline-flex;justify-content:center;align-items:center;white-space:nowrap;vertical-align:top;text-decoration:none;position:relative;overflow:hidden;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-tab-link:focus{outline:none}.mat-tab-link:focus:not(.mat-tab-disabled){opacity:1}.mat-tab-link.mat-tab-disabled{cursor:default}.cdk-high-contrast-active .mat-tab-link.mat-tab-disabled{opacity:.5}.mat-tab-link .mat-tab-label-content{display:inline-flex;justify-content:center;align-items:center;white-space:nowrap}.cdk-high-contrast-active .mat-tab-link{opacity:1}[mat-stretch-tabs] .mat-tab-link{flex-basis:0;flex-grow:1}.mat-tab-link.mat-tab-disabled{pointer-events:none}.mat-tab-link::before{margin:5px}@media(max-width: 599px){.mat-tab-link{min-width:72px}}\"], dependencies: [{ kind: \"directive\", type: i4.MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"directive\", type: i5.CdkObserveContent, selector: \"[cdkObserveContent]\", inputs: [\"cdkObserveContentDisabled\", \"debounce\"], outputs: [\"cdkObserveContent\"], exportAs: [\"cdkObserveContent\"] }, { kind: \"directive\", type: MatInkBar, selector: \"mat-ink-bar\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatTabNav, decorators: [{\n            type: Component,\n            args: [{ selector: '[mat-tab-nav-bar]', exportAs: 'matTabNavBar, matTabNav', inputs: ['color'], host: {\n                        '[attr.role]': '_getRole()',\n                        'class': 'mat-tab-nav-bar mat-tab-header',\n                        '[class.mat-tab-header-pagination-controls-enabled]': '_showPaginationControls',\n                        '[class.mat-tab-header-rtl]': \"_getLayoutDirection() == 'rtl'\",\n                        '[class.mat-primary]': 'color !== \"warn\" && color !== \"accent\"',\n                        '[class.mat-accent]': 'color === \"accent\"',\n                        '[class.mat-warn]': 'color === \"warn\"',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, template: \"<button class=\\\"mat-tab-header-pagination mat-tab-header-pagination-before mat-elevation-z4\\\"\\n     #previousPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     tabindex=\\\"-1\\\"\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     [disabled]=\\\"_disableScrollBefore || null\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\\n<div class=\\\"mat-tab-link-container\\\" #tabListContainer (keydown)=\\\"_handleKeydown($event)\\\">\\n  <div\\n    class=\\\"mat-tab-list\\\"\\n    [class._mat-animation-noopable]=\\\"_animationMode === 'NoopAnimations'\\\"\\n    #tabList\\n    (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-tab-links\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n    <mat-ink-bar></mat-ink-bar>\\n  </div>\\n</div>\\n\\n<button class=\\\"mat-tab-header-pagination mat-tab-header-pagination-after mat-elevation-z4\\\"\\n     #nextPaginator\\n     aria-hidden=\\\"true\\\"\\n     type=\\\"button\\\"\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     [disabled]=\\\"_disableScrollAfter || null\\\"\\n     tabindex=\\\"-1\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-tab-header-pagination-chevron\\\"></div>\\n</button>\\n\", styles: [\".mat-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mat-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-tab-header-pagination::-moz-focus-inner{border:0}.mat-tab-header-pagination-controls-enabled .mat-tab-header-pagination{display:flex}.mat-tab-header-pagination-before,.mat-tab-header-rtl .mat-tab-header-pagination-after{padding-left:4px}.mat-tab-header-pagination-before .mat-tab-header-pagination-chevron,.mat-tab-header-rtl .mat-tab-header-pagination-after .mat-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-tab-header-rtl .mat-tab-header-pagination-before,.mat-tab-header-pagination-after{padding-right:4px}.mat-tab-header-rtl .mat-tab-header-pagination-before .mat-tab-header-pagination-chevron,.mat-tab-header-pagination-after .mat-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px}.mat-tab-header-pagination-disabled{box-shadow:none;cursor:default}.mat-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-tab-links{display:flex}[mat-align-tabs=center]>.mat-tab-link-container .mat-tab-links{justify-content:center}[mat-align-tabs=end]>.mat-tab-link-container .mat-tab-links{justify-content:flex-end}.mat-ink-bar{position:absolute;bottom:0;height:2px;transition:500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-ink-bar._mat-animation-noopable{transition:none !important;animation:none !important}.mat-tab-group-inverted-header .mat-ink-bar{bottom:auto;top:0}.cdk-high-contrast-active .mat-ink-bar{outline:solid 2px;height:0}.mat-tab-link-container{display:flex;flex-grow:1;overflow:hidden;z-index:1}.mat-tab-link{height:48px;padding:0 24px;cursor:pointer;box-sizing:border-box;opacity:.6;min-width:160px;text-align:center;display:inline-flex;justify-content:center;align-items:center;white-space:nowrap;vertical-align:top;text-decoration:none;position:relative;overflow:hidden;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-tab-link:focus{outline:none}.mat-tab-link:focus:not(.mat-tab-disabled){opacity:1}.mat-tab-link.mat-tab-disabled{cursor:default}.cdk-high-contrast-active .mat-tab-link.mat-tab-disabled{opacity:.5}.mat-tab-link .mat-tab-label-content{display:inline-flex;justify-content:center;align-items:center;white-space:nowrap}.cdk-high-contrast-active .mat-tab-link{opacity:1}[mat-stretch-tabs] .mat-tab-link{flex-basis:0;flex-grow:1}.mat-tab-link.mat-tab-disabled{pointer-events:none}.mat-tab-link::before{margin:5px}@media(max-width: 599px){.mat-tab-link{min-width:72px}}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.NgZone }, { type: i0.ChangeDetectorRef }, { type: i1$1.ViewportRuler }, { type: i3.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { _items: [{\n                type: ContentChildren,\n                args: [forwardRef(() => MatTabLink), { descendants: true }]\n            }], _inkBar: [{\n                type: ViewChild,\n                args: [MatInkBar, { static: true }]\n            }], _tabListContainer: [{\n                type: ViewChild,\n                args: ['tabListContainer', { static: true }]\n            }], _tabList: [{\n                type: ViewChild,\n                args: ['tabList', { static: true }]\n            }], _tabListInner: [{\n                type: ViewChild,\n                args: ['tabListInner', { static: true }]\n            }], _nextPaginator: [{\n                type: ViewChild,\n                args: ['nextPaginator']\n            }], _previousPaginator: [{\n                type: ViewChild,\n                args: ['previousPaginator']\n            }] } });\n// Boilerplate for applying mixins to MatTabLink.\nconst _MatTabLinkMixinBase = mixinTabIndex(mixinDisableRipple(mixinDisabled(class {\n})));\n/** Base class with all of the `MatTabLink` functionality. */\nclass _MatTabLinkBase extends _MatTabLinkMixinBase {\n    constructor(_tabNavBar, \n    /** @docs-private */ elementRef, globalRippleOptions, tabIndex, _focusMonitor, animationMode) {\n        super();\n        this._tabNavBar = _tabNavBar;\n        this.elementRef = elementRef;\n        this._focusMonitor = _focusMonitor;\n        /** Whether the tab link is active or not. */\n        this._isActive = false;\n        /** Unique id for the tab. */\n        this.id = `mat-tab-link-${nextUniqueId++}`;\n        this.rippleConfig = globalRippleOptions || {};\n        this.tabIndex = parseInt(tabIndex) || 0;\n        if (animationMode === 'NoopAnimations') {\n            this.rippleConfig.animation = { enterDuration: 0, exitDuration: 0 };\n        }\n    }\n    /** Whether the link is active. */\n    get active() {\n        return this._isActive;\n    }\n    set active(value) {\n        const newValue = coerceBooleanProperty(value);\n        if (newValue !== this._isActive) {\n            this._isActive = newValue;\n            this._tabNavBar.updateActiveLink();\n        }\n    }\n    /**\n     * Whether ripples are disabled on interaction.\n     * @docs-private\n     */\n    get rippleDisabled() {\n        return (this.disabled ||\n            this.disableRipple ||\n            this._tabNavBar.disableRipple ||\n            !!this.rippleConfig.disabled);\n    }\n    /** Focuses the tab link. */\n    focus() {\n        this.elementRef.nativeElement.focus();\n    }\n    ngAfterViewInit() {\n        this._focusMonitor.monitor(this.elementRef);\n    }\n    ngOnDestroy() {\n        this._focusMonitor.stopMonitoring(this.elementRef);\n    }\n    _handleFocus() {\n        // Since we allow navigation through tabbing in the nav bar, we\n        // have to update the focused index whenever the link receives focus.\n        this._tabNavBar.focusIndex = this._tabNavBar._items.toArray().indexOf(this);\n    }\n    _handleKeydown(event) {\n        if (this._tabNavBar.tabPanel && event.keyCode === SPACE) {\n            this.elementRef.nativeElement.click();\n        }\n    }\n    _getAriaControls() {\n        return this._tabNavBar.tabPanel\n            ? this._tabNavBar.tabPanel?.id\n            : this.elementRef.nativeElement.getAttribute('aria-controls');\n    }\n    _getAriaSelected() {\n        if (this._tabNavBar.tabPanel) {\n            return this.active ? 'true' : 'false';\n        }\n        else {\n            return this.elementRef.nativeElement.getAttribute('aria-selected');\n        }\n    }\n    _getAriaCurrent() {\n        return this.active && !this._tabNavBar.tabPanel ? 'page' : null;\n    }\n    _getRole() {\n        return this._tabNavBar.tabPanel ? 'tab' : this.elementRef.nativeElement.getAttribute('role');\n    }\n    _getTabIndex() {\n        if (this._tabNavBar.tabPanel) {\n            return this._isActive && !this.disabled ? 0 : -1;\n        }\n        else {\n            return this.tabIndex;\n        }\n    }\n}\n_MatTabLinkBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: _MatTabLinkBase, deps: [{ token: _MatTabNavBase }, { token: i0.ElementRef }, { token: MAT_RIPPLE_GLOBAL_OPTIONS, optional: true }, { token: 'tabindex', attribute: true }, { token: i7.FocusMonitor }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\n_MatTabLinkBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: _MatTabLinkBase, inputs: { active: \"active\", id: \"id\" }, usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: _MatTabLinkBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: _MatTabNavBase }, { type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_RIPPLE_GLOBAL_OPTIONS]\n                }] }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['tabindex']\n                }] }, { type: i7.FocusMonitor }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { active: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }] } });\n/**\n * Link inside of a `mat-tab-nav-bar`.\n */\nclass MatTabLink extends _MatTabLinkBase {\n    constructor(tabNavBar, elementRef, ngZone, platform, globalRippleOptions, tabIndex, focusMonitor, animationMode) {\n        super(tabNavBar, elementRef, globalRippleOptions, tabIndex, focusMonitor, animationMode);\n        this._tabLinkRipple = new RippleRenderer(this, ngZone, elementRef, platform);\n        this._tabLinkRipple.setupTriggerEvents(elementRef.nativeElement);\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        this._tabLinkRipple._removeTriggerEvents();\n    }\n}\nMatTabLink.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatTabLink, deps: [{ token: MatTabNav }, { token: i0.ElementRef }, { token: i0.NgZone }, { token: i3.Platform }, { token: MAT_RIPPLE_GLOBAL_OPTIONS, optional: true }, { token: 'tabindex', attribute: true }, { token: i7.FocusMonitor }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\nMatTabLink.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatTabLink, selector: \"[mat-tab-link], [matTabLink]\", inputs: { disabled: \"disabled\", disableRipple: \"disableRipple\", tabIndex: \"tabIndex\" }, host: { listeners: { \"focus\": \"_handleFocus()\", \"keydown\": \"_handleKeydown($event)\" }, properties: { \"attr.aria-controls\": \"_getAriaControls()\", \"attr.aria-current\": \"_getAriaCurrent()\", \"attr.aria-disabled\": \"disabled\", \"attr.aria-selected\": \"_getAriaSelected()\", \"attr.id\": \"id\", \"attr.tabIndex\": \"_getTabIndex()\", \"attr.role\": \"_getRole()\", \"class.mat-tab-disabled\": \"disabled\", \"class.mat-tab-label-active\": \"active\" }, classAttribute: \"mat-tab-link mat-focus-indicator\" }, exportAs: [\"matTabLink\"], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatTabLink, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-tab-link], [matTabLink]',\n                    exportAs: 'matTabLink',\n                    inputs: ['disabled', 'disableRipple', 'tabIndex'],\n                    host: {\n                        'class': 'mat-tab-link mat-focus-indicator',\n                        '[attr.aria-controls]': '_getAriaControls()',\n                        '[attr.aria-current]': '_getAriaCurrent()',\n                        '[attr.aria-disabled]': 'disabled',\n                        '[attr.aria-selected]': '_getAriaSelected()',\n                        '[attr.id]': 'id',\n                        '[attr.tabIndex]': '_getTabIndex()',\n                        '[attr.role]': '_getRole()',\n                        '[class.mat-tab-disabled]': 'disabled',\n                        '[class.mat-tab-label-active]': 'active',\n                        '(focus)': '_handleFocus()',\n                        '(keydown)': '_handleKeydown($event)',\n                    },\n                }]\n        }], ctorParameters: function () { return [{ type: MatTabNav }, { type: i0.ElementRef }, { type: i0.NgZone }, { type: i3.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_RIPPLE_GLOBAL_OPTIONS]\n                }] }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['tabindex']\n                }] }, { type: i7.FocusMonitor }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; } });\n/**\n * Tab panel component associated with MatTabNav.\n */\nclass MatTabNavPanel {\n    constructor() {\n        /** Unique id for the tab panel. */\n        this.id = `mat-tab-nav-panel-${nextUniqueId++}`;\n    }\n}\nMatTabNavPanel.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatTabNavPanel, deps: [], target: i0.ɵɵFactoryTarget.Component });\nMatTabNavPanel.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.2.0\", type: MatTabNavPanel, selector: \"mat-tab-nav-panel\", inputs: { id: \"id\" }, host: { attributes: { \"role\": \"tabpanel\" }, properties: { \"attr.aria-labelledby\": \"_activeTabId\", \"attr.id\": \"id\" }, classAttribute: \"mat-tab-nav-panel\" }, exportAs: [\"matTabNavPanel\"], ngImport: i0, template: '<ng-content></ng-content>', isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatTabNavPanel, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'mat-tab-nav-panel',\n                    exportAs: 'matTabNavPanel',\n                    template: '<ng-content></ng-content>',\n                    host: {\n                        '[attr.aria-labelledby]': '_activeTabId',\n                        '[attr.id]': 'id',\n                        'class': 'mat-tab-nav-panel',\n                        'role': 'tabpanel',\n                    },\n                    encapsulation: ViewEncapsulation.None,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                }]\n        }], propDecorators: { id: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatTabsModule {\n}\nMatTabsModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatTabsModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatTabsModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.2.0\", ngImport: i0, type: MatTabsModule, declarations: [MatTabGroup,\n        MatTabLabel,\n        MatTab,\n        MatInkBar,\n        MatTabLabelWrapper,\n        MatTabNav,\n        MatTabNavPanel,\n        MatTabLink,\n        MatTabBody,\n        MatTabBodyPortal,\n        MatTabHeader,\n        MatTabContent], imports: [CommonModule,\n        MatCommonModule,\n        PortalModule,\n        MatRippleModule,\n        ObserversModule,\n        A11yModule], exports: [MatCommonModule,\n        MatTabGroup,\n        MatTabLabel,\n        MatTab,\n        MatTabNav,\n        MatTabNavPanel,\n        MatTabLink,\n        MatTabContent] });\nMatTabsModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatTabsModule, imports: [CommonModule,\n        MatCommonModule,\n        PortalModule,\n        MatRippleModule,\n        ObserversModule,\n        A11yModule, MatCommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.0\", ngImport: i0, type: MatTabsModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        CommonModule,\n                        MatCommonModule,\n                        PortalModule,\n                        MatRippleModule,\n                        ObserversModule,\n                        A11yModule,\n                    ],\n                    // Don't export all components because some are only to be used internally.\n                    exports: [\n                        MatCommonModule,\n                        MatTabGroup,\n                        MatTabLabel,\n                        MatTab,\n                        MatTabNav,\n                        MatTabNavPanel,\n                        MatTabLink,\n                        MatTabContent,\n                    ],\n                    declarations: [\n                        MatTabGroup,\n                        MatTabLabel,\n                        MatTab,\n                        MatInkBar,\n                        MatTabLabelWrapper,\n                        MatTabNav,\n                        MatTabNavPanel,\n                        MatTabLink,\n                        MatTabBody,\n                        MatTabBodyPortal,\n                        MatTabHeader,\n                        MatTabContent,\n                    ],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_TAB, MAT_TABS_CONFIG, MAT_TAB_GROUP, MatInkBar, MatTab, MatTabBody, MatTabBodyPortal, MatTabChangeEvent, MatTabContent, MatTabGroup, MatTabHeader, MatTabLabel, MatTabLabelWrapper, MatTabLink, MatTabNav, MatTabNavPanel, MatTabsModule, _MAT_INK_BAR_POSITIONER, _MatTabBodyBase, _MatTabGroupBase, _MatTabHeaderBase, _MatTabLinkBase, _MatTabNavBase, matTabsAnimations };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,mBAApB;AACA,SAASC,eAAT,EAA0BC,UAA1B,QAA4C,mBAA5C;AACA,OAAO,KAAKC,EAAZ,MAAoB,wBAApB;AACA,SAASC,eAAT,QAAgC,wBAAhC;AACA,OAAO,KAAKC,EAAZ,MAAoB,qBAApB;AACA,SAASC,SAAT,EAAoBC,cAApB,EAAoCC,eAApC,EAAqDC,YAArD,QAAyE,qBAAzE;AACA,OAAO,KAAKC,IAAZ,MAAsB,iBAAtB;AACA,SAASC,QAAT,EAAmBC,YAAnB,QAAuC,iBAAvC;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,SAASC,cAAT,EAAyBC,SAAzB,EAAoCC,MAApC,EAA4CC,QAA5C,EAAsDC,WAAtD,EAAmEC,SAAnE,EAA8EC,uBAA9E,EAAuGC,iBAAvG,EAA0HC,YAA1H,EAAwIC,SAAxI,EAAmJC,KAAnJ,EAA0JC,UAA1J,EAAsKC,YAAtK,EAAoLC,MAApL,EAA4LC,eAA5L,EAA6MC,SAA7M,EAAwNC,SAAxN,EAAmOC,QAAnO,QAAmP,eAAnP;AACA,OAAO,KAAKC,EAAZ,MAAoB,wBAApB;AACA,SAASC,aAAT,EAAwBC,UAAxB,EAAoCC,kBAApC,EAAwDC,aAAxD,EAAuEC,yBAAvE,EAAkGC,cAAlG,EAAkHC,eAAlH,EAAmIC,eAAnI,QAA0J,wBAA1J;AACA,SAASC,qBAAT,QAAsC,sCAAtC;AACA,SAASC,IAAT,EAAeC,SAAf,EAA0BC,oBAA1B,EAAgDC,SAAhD,EAA2DC,SAA3D,EAAsEC,IAAtE,EAA4EC,MAA5E,QAA0F,gBAA1F;AACA,SAASC,OAAT,EAAkBC,YAAlB,EAAgCC,SAAhC,EAA2CC,EAA3C,EAA+CC,KAA/C,EAAsDC,KAAtD,EAA6DC,UAA7D,EAAyEC,KAAzE,QAAsF,MAAtF;AACA,OAAO,KAAKC,EAAZ,MAAoB,mBAApB;AACA,SAASC,OAAT,EAAkBC,KAAlB,EAAyBC,KAAzB,EAAgCC,UAAhC,EAA4CC,OAA5C,QAA2D,qBAA3D;AACA,SAASC,qBAAT,EAAgCC,oBAAhC,QAA4D,uBAA5D;AACA,OAAO,KAAKC,IAAZ,MAAsB,wBAAtB;AACA,OAAO,KAAKC,EAAZ,MAAoB,uBAApB;AACA,SAASC,+BAAT,QAAgD,uBAAhD;AACA,SAASC,cAAT,EAAyBC,KAAzB,EAAgCC,KAAhC,QAA6C,uBAA7C;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;;IAsD4FzD,EA2K28B,gB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA3K38BA,EA2+CqpD,+F;;;;mBA3+CrpDA,E;IAAAA,EA2+CkqD,oD;;;;;;IA3+ClqDA,EA2+Ck2D,U;;;;mBA3+Cl2DA,E;IAAAA,EA2+Ck2D,oC;;;;;;iBA3+Cl2DA,E;;IAAAA,EA2+C6kB,4B;IA3+C7kBA,EA2+Cm3C;MAAA,oBA3+Cn3CA,EA2+Cm3C;MAAA;MAAA;MAAA,gBA3+Cn3CA,EA2+Cm3C;;MAAA,YA3+Cn3CA,EA2+Cm3C;;MAAA,OA3+Cn3CA,EA2+C63C,qDAAV;IAAA;MAAA,oBA3+Cn3CA,EA2+Cm3C;MAAA;MAAA,gBA3+Cn3CA,EA2+Cm3C;MAAA,OA3+Cn3CA,EA2+C07C,oDAAvE;IAAA,E;IA3+Cn3CA,EA2+Ck+C,4B;IA3+Cl+CA,EA2+CqkD,gF;IA3+CrkDA,EA2+Cu0D,uFA3+Cv0DA,EA2+Cu0D,wB;IA3+Cv0DA,EA2+Cu4D,iB;;;;;;;gBA3+Cv4DA,E;;mBAAAA,E;IAAAA,EA2+C+qC,mE;IA3+C/qCA,EA2+CmwB,uK;IA3+CnwBA,EA2+CsyB,iW;IA3+CtyBA,EA2+CklD,a;IA3+CllDA,EA2+CklD,0D;;;;;;iBA3+CllDA,E;;IAAAA,EA2+CujE,sC;IA3+CvjEA,EA2+C0tF;MA3+C1tFA,EA2+C0tF;MAAA,gBA3+C1tFA,EA2+C0tF;MAAA,OA3+C1tFA,EA2+C0uF,mDAAhB;IAAA;MA3+C1tFA,EA2+C0tF;MAAA,gBA3+C1tFA,EA2+C0tF;MAAA,OA3+C1tFA,EA2+C2yF,sDAAjF;IAAA,E;IA3+C1tFA,EA2+Ck1F,e;;;;;;mBA3+Cl1FA,E;IAAAA,EA2+C03E,mE;IA3+C13EA,EA2+CkqE,6P;IA3+ClqEA,EA2+C+sE,2K;;;;;;AAhiD3yE,MAAM0D,uBAAuB,GAAG,IAAIzD,cAAJ,CAAmB,qBAAnB,EAA0C;EACtE0D,UAAU,EAAE,MAD0D;EAEtEC,OAAO,EAAEC;AAF6D,CAA1C,CAAhC;AAIA;AACA;AACA;AACA;;;AACA,SAASA,+BAAT,GAA2C;EACvC,MAAMC,MAAM,GAAIC,OAAD,KAAc;IACzBC,IAAI,EAAED,OAAO,GAAG,CAACA,OAAO,CAACE,UAAR,IAAsB,CAAvB,IAA4B,IAA/B,GAAsC,GAD1B;IAEzBC,KAAK,EAAEH,OAAO,GAAG,CAACA,OAAO,CAACI,WAAR,IAAuB,CAAxB,IAA6B,IAAhC,GAAuC;EAF5B,CAAd,CAAf;;EAIA,OAAOL,MAAP;AACH;AACD;AACA;AACA;AACA;;;AACA,MAAMM,SAAN,CAAgB;EACZC,WAAW,CAACC,WAAD,EAAcC,OAAd,EAAuBC,iBAAvB,EAA0CC,cAA1C,EAA0D;IACjE,KAAKH,WAAL,GAAmBA,WAAnB;IACA,KAAKC,OAAL,GAAeA,OAAf;IACA,KAAKC,iBAAL,GAAyBA,iBAAzB;IACA,KAAKC,cAAL,GAAsBA,cAAtB;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIC,cAAc,CAACX,OAAD,EAAU;IACpB,KAAKY,IAAL,GADoB,CAEpB;IACA;;IACA,KAAKJ,OAAL,CAAaK,GAAb,CAAiB,MAAM;MACnB,KAAKL,OAAL,CAAaM,QAAb,CAAsBC,IAAtB,CAA2BjD,IAAI,CAAC,CAAD,CAA/B,EAAoCkD,SAApC,CAA8C,MAAM;QAChD,MAAMC,SAAS,GAAG,KAAKR,iBAAL,CAAuBT,OAAvB,CAAlB;;QACA,MAAMkB,MAAM,GAAG,KAAKX,WAAL,CAAiBY,aAAhC;QACAD,MAAM,CAAClC,KAAP,CAAaiB,IAAb,GAAoBgB,SAAS,CAAChB,IAA9B;QACAiB,MAAM,CAAClC,KAAP,CAAamB,KAAb,GAAqBc,SAAS,CAACd,KAA/B;MACH,CALD;IAMH,CAPD;EAQH;EACD;;;EACAS,IAAI,GAAG;IACH,KAAKL,WAAL,CAAiBY,aAAjB,CAA+BnC,KAA/B,CAAqCoC,UAArC,GAAkD,SAAlD;EACH;EACD;;;EACAC,IAAI,GAAG;IACH,KAAKd,WAAL,CAAiBY,aAAjB,CAA+BnC,KAA/B,CAAqCoC,UAArC,GAAkD,QAAlD;EACH;;AAhCW;;AAkChBf,SAAS,CAACiB,IAAV;EAAA,iBAAsGjB,SAAtG,EAA4FpE,EAA5F,mBAAiIA,EAAE,CAACsF,UAApI,GAA4FtF,EAA5F,mBAA2JA,EAAE,CAACuF,MAA9J,GAA4FvF,EAA5F,mBAAiL0D,uBAAjL,GAA4F1D,EAA5F,mBAAqN4B,qBAArN;AAAA;;AACAwC,SAAS,CAACoB,IAAV,kBAD4FxF,EAC5F;EAAA,MAA0FoE,SAA1F;EAAA;EAAA;EAAA;EAAA;IAAA;MAD4FpE,EAC5F;IAAA;EAAA;AAAA;;AACA;EAAA,mDAF4FA,EAE5F,mBAA2FoE,SAA3F,EAAkH,CAAC;IACvGqB,IAAI,EAAEvF,SADiG;IAEvGwF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,aADX;MAECC,IAAI,EAAE;QACF,SAAS,aADP;QAEF,mCAAoC;MAFlC;IAFP,CAAD;EAFiG,CAAD,CAAlH,EAS4B,YAAY;IAAE,OAAO,CAAC;MAAEH,IAAI,EAAEzF,EAAE,CAACsF;IAAX,CAAD,EAA0B;MAAEG,IAAI,EAAEzF,EAAE,CAACuF;IAAX,CAA1B,EAA+C;MAAEE,IAAI,EAAEI,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC5GL,IAAI,EAAEtF,MADsG;QAE5GuF,IAAI,EAAE,CAAChC,uBAAD;MAFsG,CAAD;IAA/B,CAA/C,EAG3B;MAAE+B,IAAI,EAAEI,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAClCL,IAAI,EAAErF;MAD4B,CAAD,EAElC;QACCqF,IAAI,EAAEtF,MADP;QAECuF,IAAI,EAAE,CAAC9D,qBAAD;MAFP,CAFkC;IAA/B,CAH2B,CAAP;EAQlB,CAjBxB;AAAA;AAmBA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMmE,eAAe,GAAG,IAAI9F,cAAJ,CAAmB,eAAnB,CAAxB;AACA;;AACA,MAAM+F,aAAN,CAAoB;EAChB3B,WAAW;EAAC;EAA4B4B,QAA7B,EAAuC;IAC9C,KAAKA,QAAL,GAAgBA,QAAhB;EACH;;AAHe;;AAKpBD,aAAa,CAACX,IAAd;EAAA,iBAA0GW,aAA1G,EAxC4FhG,EAwC5F,mBAAyIA,EAAE,CAACK,WAA5I;AAAA;;AACA2F,aAAa,CAACR,IAAd,kBAzC4FxF,EAyC5F;EAAA,MAA8FgG,aAA9F;EAAA;EAAA,WAzC4FhG,EAyC5F,oBAAqJ,CAAC;IAAEkG,OAAO,EAAEH,eAAX;IAA4BI,WAAW,EAAEH;EAAzC,CAAD,CAArJ;AAAA;;AACA;EAAA,mDA1C4FhG,EA0C5F,mBAA2FgG,aAA3F,EAAsH,CAAC;IAC3GP,IAAI,EAAEvF,SADqG;IAE3GwF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBADX;MAECS,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAEH,eAAX;QAA4BI,WAAW,EAAEH;MAAzC,CAAD;IAFZ,CAAD;EAFqG,CAAD,CAAtH,EAM4B,YAAY;IAAE,OAAO,CAAC;MAAEP,IAAI,EAAEzF,EAAE,CAACK;IAAX,CAAD,CAAP;EAAoC,CAN9E;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMgG,aAAa,GAAG,IAAIpG,cAAJ,CAAmB,aAAnB,CAAtB;AACA;AACA;AACA;AACA;;AACA,MAAMqG,OAAO,GAAG,IAAIrG,cAAJ,CAAmB,SAAnB,CAAhB;AACA;;AACA,MAAMsG,WAAN,SAA0B9G,SAA1B,CAAoC;EAChC4E,WAAW,CAACmC,WAAD,EAAcC,gBAAd,EAAgCC,WAAhC,EAA6C;IACpD,MAAMF,WAAN,EAAmBC,gBAAnB;IACA,KAAKC,WAAL,GAAmBA,WAAnB;EACH;;AAJ+B;;AAMpCH,WAAW,CAAClB,IAAZ;EAAA,iBAAwGkB,WAAxG,EA3E4FvG,EA2E5F,mBAAqIA,EAAE,CAACK,WAAxI,GA3E4FL,EA2E5F,mBAAgKA,EAAE,CAAC2G,gBAAnK,GA3E4F3G,EA2E5F,mBAAgMsG,OAAhM;AAAA;;AACAC,WAAW,CAACf,IAAZ,kBA5E4FxF,EA4E5F;EAAA,MAA4FuG,WAA5F;EAAA;EAAA,WA5E4FvG,EA4E5F,oBAAgK,CAAC;IAAEkG,OAAO,EAAEG,aAAX;IAA0BF,WAAW,EAAEI;EAAvC,CAAD,CAAhK,GA5E4FvG,EA4E5F;AAAA;;AACA;EAAA,mDA7E4FA,EA6E5F,mBAA2FuG,WAA3F,EAAoH,CAAC;IACzGd,IAAI,EAAEvF,SADmG;IAEzGwF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gCADX;MAECS,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAEG,aAAX;QAA0BF,WAAW,EAAEI;MAAvC,CAAD;IAFZ,CAAD;EAFmG,CAAD,CAApH,EAM4B,YAAY;IAAE,OAAO,CAAC;MAAEd,IAAI,EAAEzF,EAAE,CAACK;IAAX,CAAD,EAA2B;MAAEoF,IAAI,EAAEzF,EAAE,CAAC2G;IAAX,CAA3B,EAA0D;MAAElB,IAAI,EAAEI,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACvHL,IAAI,EAAEtF,MADiH;QAEvHuF,IAAI,EAAE,CAACY,OAAD;MAFiH,CAAD,EAGvH;QACCb,IAAI,EAAErF;MADP,CAHuH;IAA/B,CAA1D,CAAP;EAKlB,CAXxB;AAAA;AAaA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMwG,WAAW,GAAGxF,aAAa,CAAC,MAAM,EAAP,CAAjC;AAEA;AACA;AACA;AACA;;;AACA,MAAMyF,aAAa,GAAG,IAAI5G,cAAJ,CAAmB,eAAnB,CAAtB;;AACA,MAAM6G,MAAN,SAAqBF,WAArB,CAAiC;EAC7BvC,WAAW,CAAC0C,iBAAD,EAAoBC,gBAApB,EAAsC;IAC7C;IACA,KAAKD,iBAAL,GAAyBA,iBAAzB;IACA,KAAKC,gBAAL,GAAwBA,gBAAxB;IACA;;IACA,KAAKC,SAAL,GAAiB,EAAjB;IACA;;IACA,KAAKC,cAAL,GAAsB,IAAtB;IACA;;IACA,KAAKC,aAAL,GAAqB,IAAI/E,OAAJ,EAArB;IACA;AACR;AACA;AACA;;IACQ,KAAKgF,QAAL,GAAgB,IAAhB;IACA;AACR;AACA;AACA;;IACQ,KAAKC,MAAL,GAAc,IAAd;IACA;AACR;AACA;;IACQ,KAAKC,QAAL,GAAgB,KAAhB;EACH;EACD;;;EACiB,IAAbC,aAAa,GAAG;IAChB,OAAO,KAAKC,cAAZ;EACH;;EACgB,IAAbD,aAAa,CAACE,KAAD,EAAQ;IACrB,KAAKC,sBAAL,CAA4BD,KAA5B;EACH;EACD;;;EACW,IAAPE,OAAO,GAAG;IACV,OAAO,KAAKT,cAAZ;EACH;;EACDU,WAAW,CAACC,OAAD,EAAU;IACjB,IAAIA,OAAO,CAACC,cAAR,CAAuB,WAAvB,KAAuCD,OAAO,CAACC,cAAR,CAAuB,UAAvB,CAA3C,EAA+E;MAC3E,KAAKX,aAAL,CAAmBY,IAAnB;IACH;EACJ;;EACDC,WAAW,GAAG;IACV,KAAKb,aAAL,CAAmBc,QAAnB;EACH;;EACDC,QAAQ,GAAG;IACP,KAAKhB,cAAL,GAAsB,IAAIxH,cAAJ,CAAmB,KAAKyI,gBAAL,IAAyB,KAAKC,gBAAjD,EAAmE,KAAKrB,iBAAxE,CAAtB;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIW,sBAAsB,CAACD,KAAD,EAAQ;IAC1B;IACA;IACA;IACA;IACA,IAAIA,KAAK,IAAIA,KAAK,CAACf,WAAN,KAAsB,IAAnC,EAAyC;MACrC,KAAKc,cAAL,GAAsBC,KAAtB;IACH;EACJ;;AA9D4B;;AAgEjCX,MAAM,CAACzB,IAAP;EAAA,iBAAmGyB,MAAnG,EA1K4F9G,EA0K5F,mBAA2HA,EAAE,CAAC2G,gBAA9H,GA1K4F3G,EA0K5F,mBAA2J6G,aAA3J;AAAA;;AACAC,MAAM,CAACuB,IAAP,kBA3K4FrI,EA2K5F;EAAA,MAAuF8G,MAAvF;EAAA;EAAA;IAAA;MA3K4F9G,EA2K5F,0BAAqcqG,aAArc;MA3K4FrG,EA2K5F,0BAAqiB+F,eAAriB,KAA+kB1F,WAA/kB;IAAA;;IAAA;MAAA;;MA3K4FL,EA2K5F,qBA3K4FA,EA2K5F;MA3K4FA,EA2K5F,qBA3K4FA,EA2K5F;IAAA;EAAA;EAAA;IAAA;MA3K4FA,EA2K5F,aAAurBK,WAAvrB;IAAA;;IAAA;MAAA;;MA3K4FL,EA2K5F,qBA3K4FA,EA2K5F;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WA3K4FA,EA2K5F,oBAAqV,CAAC;IAAEkG,OAAO,EAAEI,OAAX;IAAoBH,WAAW,EAAEW;EAAjC,CAAD,CAArV,GA3K4F9G,EA2K5F,6BA3K4FA,EA2K5F;EAAA;EAAA;EAAA;EAAA;IAAA;MA3K4FA,EA2K5F;MA3K4FA,EA2K87B,kEAA1hC;IAAA;EAAA;EAAA;AAAA;;AACA;EAAA,mDA5K4FA,EA4K5F,mBAA2F8G,MAA3F,EAA+G,CAAC;IACpGrB,IAAI,EAAEnF,SAD8F;IAEpGoF,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,SAAZ;MAAuB2C,MAAM,EAAE,CAAC,UAAD,CAA/B;MAA6CC,eAAe,EAAEhI,uBAAuB,CAACiI,OAAtF;MAA+FC,aAAa,EAAEjI,iBAAiB,CAACkI,IAAhI;MAAsIC,QAAQ,EAAE,QAAhJ;MAA0JvC,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAEI,OAAX;QAAoBH,WAAW,EAAEW;MAAjC,CAAD,CAArK;MAAkNb,QAAQ,EAAE;IAA5N,CAAD;EAF8F,CAAD,CAA/G,EAG4B,YAAY;IAAE,OAAO,CAAC;MAAER,IAAI,EAAEzF,EAAE,CAAC2G;IAAX,CAAD,EAAgC;MAAElB,IAAI,EAAEI,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC7FL,IAAI,EAAEtF,MADuF;QAE7FuF,IAAI,EAAE,CAACmB,aAAD;MAFuF,CAAD,EAG7F;QACCpB,IAAI,EAAErF;MADP,CAH6F;IAA/B,CAAhC,CAAP;EAKlB,CARxB,EAQ0C;IAAEmH,aAAa,EAAE,CAAC;MAC5C9B,IAAI,EAAEhF,YADsC;MAE5CiF,IAAI,EAAE,CAACW,aAAD;IAFsC,CAAD,CAAjB;IAG1B8B,gBAAgB,EAAE,CAAC;MACnB1C,IAAI,EAAEhF,YADa;MAEnBiF,IAAI,EAAE,CAACK,eAAD,EAAkB;QAAE6C,IAAI,EAAEvI,WAAR;QAAqBwI,MAAM,EAAE;MAA7B,CAAlB;IAFa,CAAD,CAHQ;IAM1BT,gBAAgB,EAAE,CAAC;MACnB3C,IAAI,EAAE/E,SADa;MAEnBgF,IAAI,EAAE,CAACrF,WAAD,EAAc;QAAEwI,MAAM,EAAE;MAAV,CAAd;IAFa,CAAD,CANQ;IAS1B5B,SAAS,EAAE,CAAC;MACZxB,IAAI,EAAE9E,KADM;MAEZ+E,IAAI,EAAE,CAAC,OAAD;IAFM,CAAD,CATe;IAY1BoD,SAAS,EAAE,CAAC;MACZrD,IAAI,EAAE9E,KADM;MAEZ+E,IAAI,EAAE,CAAC,YAAD;IAFM,CAAD,CAZe;IAe1BqD,cAAc,EAAE,CAAC;MACjBtD,IAAI,EAAE9E,KADW;MAEjB+E,IAAI,EAAE,CAAC,iBAAD;IAFW,CAAD,CAfU;IAkB1BsD,UAAU,EAAE,CAAC;MACbvD,IAAI,EAAE9E;IADO,CAAD,CAlBc;IAoB1BsI,SAAS,EAAE,CAAC;MACZxD,IAAI,EAAE9E;IADM,CAAD;EApBe,CAR1C;AAAA;AAgCA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;;AACA,MAAMuI,iBAAiB,GAAG;EACtB;EACAC,YAAY,EAAEtG,OAAO,CAAC,cAAD,EAAiB,CAClC;EACAC,KAAK,CAAC,uDAAD,EAA0DC,KAAK,CAAC;IAAEqG,SAAS,EAAE;EAAb,CAAD,CAA/D,CAF6B,EAGlC;EACA;EACA;EACA;EACAtG,KAAK,CAAC,MAAD,EAASC,KAAK,CAAC;IAChBqG,SAAS,EAAE,0BADK;IAEhBC,SAAS,EAAE,KAFK;IAGhB;IACA;IACAlE,UAAU,EAAE;EALI,CAAD,CAAd,CAP6B,EAclCrC,KAAK,CAAC,OAAD,EAAUC,KAAK,CAAC;IACjBqG,SAAS,EAAE,yBADM;IAEjBC,SAAS,EAAE,KAFM;IAGjBlE,UAAU,EAAE;EAHK,CAAD,CAAf,CAd6B,EAmBlCnC,UAAU,CAAC,wDAAD,EAA2DC,OAAO,CAAC,sDAAD,CAAlE,CAnBwB,EAoBlCD,UAAU,CAAC,4BAAD,EAA+B,CACrCD,KAAK,CAAC;IAAEqG,SAAS,EAAE,0BAAb;IAAyCjE,UAAU,EAAE;EAArD,CAAD,CADgC,EAErClC,OAAO,CAAC,sDAAD,CAF8B,CAA/B,CApBwB,EAwBlCD,UAAU,CAAC,6BAAD,EAAgC,CACtCD,KAAK,CAAC;IAAEqG,SAAS,EAAE,yBAAb;IAAwCjE,UAAU,EAAE;EAApD,CAAD,CADiC,EAEtClC,OAAO,CAAC,sDAAD,CAF+B,CAAhC,CAxBwB,CAAjB;AAFC,CAA1B;AAiCA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;AACA,MAAMqG,gBAAN,SAA+B3J,eAA/B,CAA+C;EAC3C0E,WAAW,CAACkF,wBAAD,EAA2B9C,gBAA3B,EAA6C+C,KAA7C,EAAoDC,SAApD,EAA+D;IACtE,MAAMF,wBAAN,EAAgC9C,gBAAhC,EAAkDgD,SAAlD;IACA,KAAKD,KAAL,GAAaA,KAAb;IACA;;IACA,KAAKE,aAAL,GAAqBrH,YAAY,CAACI,KAAlC;IACA;;IACA,KAAKkH,WAAL,GAAmBtH,YAAY,CAACI,KAAhC;EACH;EACD;;;EACAyF,QAAQ,GAAG;IACP,MAAMA,QAAN;IACA,KAAKwB,aAAL,GAAqB,KAAKF,KAAL,CAAWI,gBAAX,CAChB9E,IADgB,CACXhD,SAAS,CAAC,KAAK0H,KAAL,CAAWK,iBAAX,CAA6B,KAAKL,KAAL,CAAWM,SAAxC,CAAD,CADE,EAEhB/E,SAFgB,CAELgF,WAAD,IAAiB;MAC5B,IAAIA,WAAW,IAAI,CAAC,KAAKC,WAAL,EAApB,EAAwC;QACpC,KAAKC,MAAL,CAAY,KAAKT,KAAL,CAAWU,QAAvB;MACH;IACJ,CANoB,CAArB;IAOA,KAAKP,WAAL,GAAmB,KAAKH,KAAL,CAAWW,mBAAX,CAA+BpF,SAA/B,CAAyC,MAAM;MAC9D,IAAI,CAAC,KAAKyE,KAAL,CAAWY,eAAhB,EAAiC;QAC7B,KAAKC,MAAL;MACH;IACJ,CAJkB,CAAnB;EAKH;EACD;;;EACArC,WAAW,GAAG;IACV,MAAMA,WAAN;;IACA,KAAK0B,aAAL,CAAmBY,WAAnB;;IACA,KAAKX,WAAL,CAAiBW,WAAjB;EACH;;AA9B0C;;AAgC/ChB,gBAAgB,CAACjE,IAAjB;EAAA,iBAA6GiE,gBAA7G,EAnS4FtJ,EAmS5F,mBAA+IA,EAAE,CAACuK,wBAAlJ,GAnS4FvK,EAmS5F,mBAAuLA,EAAE,CAAC2G,gBAA1L,GAnS4F3G,EAmS5F,mBAAuNY,UAAU,CAAC,MAAM4J,UAAP,CAAjO,GAnS4FxK,EAmS5F,mBAAgQF,QAAhQ;AAAA;;AACAwJ,gBAAgB,CAAC9D,IAAjB,kBApS4FxF,EAoS5F;EAAA,MAAiGsJ,gBAAjG;EAAA;EAAA,WApS4FtJ,EAoS5F;AAAA;;AACA;EAAA,mDArS4FA,EAqS5F,mBAA2FsJ,gBAA3F,EAAyH,CAAC;IAC9G7D,IAAI,EAAEvF,SADwG;IAE9GwF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IADX,CAAD;EAFwG,CAAD,CAAzH,EAK4B,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAEzF,EAAE,CAACuK;IAAX,CAAD,EAAwC;MAAE9E,IAAI,EAAEzF,EAAE,CAAC2G;IAAX,CAAxC,EAAuE;MAAElB,IAAI,EAAE+E,UAAR;MAAoB1E,UAAU,EAAE,CAAC;QACrIL,IAAI,EAAEtF,MAD+H;QAErIuF,IAAI,EAAE,CAAC9E,UAAU,CAAC,MAAM4J,UAAP,CAAX;MAF+H,CAAD;IAAhC,CAAvE,EAG3B;MAAE/E,IAAI,EAAEI,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAClCL,IAAI,EAAEtF,MAD4B;QAElCuF,IAAI,EAAE,CAAC5F,QAAD;MAF4B,CAAD;IAA/B,CAH2B,CAAP;EAMlB,CAXxB;AAAA;AAYA;AACA;AACA;AACA;;;AACA,MAAM2K,eAAN,CAAsB;EAClBpG,WAAW,CAACC,WAAD,EAAcoG,IAAd,EAAoBC,iBAApB,EAAuC;IAC9C,KAAKrG,WAAL,GAAmBA,WAAnB;IACA,KAAKoG,IAAL,GAAYA,IAAZ;IACA;;IACA,KAAKE,sBAAL,GAA8BvI,YAAY,CAACI,KAA3C;IACA;;IACA,KAAKoI,qBAAL,GAA6B,IAAIzI,OAAJ,EAA7B;IACA;;IACA,KAAK0I,YAAL,GAAoB,IAAIjK,YAAJ,EAApB;IACA;;IACA,KAAK+I,gBAAL,GAAwB,IAAI/I,YAAJ,EAAxB;IACA;;IACA,KAAKsJ,mBAAL,GAA2B,IAAItJ,YAAJ,EAA3B;IACA;;IACA,KAAKkK,WAAL,GAAmB,IAAIlK,YAAJ,CAAiB,IAAjB,CAAnB,CAd8C,CAe9C;IACA;;IACA;;IACA,KAAKmK,iBAAL,GAAyB,OAAzB;IACA;;IACA,KAAKZ,eAAL,GAAuB,KAAvB;;IACA,IAAIM,IAAJ,EAAU;MACN,KAAKE,sBAAL,GAA8BF,IAAI,CAACO,MAAL,CAAYlG,SAAZ,CAAuBmG,GAAD,IAAS;QACzD,KAAKC,8BAAL,CAAoCD,GAApC;;QACAP,iBAAiB,CAACS,YAAlB;MACH,CAH6B,CAA9B;IAIH,CA1B6C,CA2B9C;IACA;;;IACA,KAAKP,qBAAL,CACK/F,IADL,CACU/C,oBAAoB,CAAC,CAACsJ,CAAD,EAAIC,CAAJ,KAAU;MACrC,OAAOD,CAAC,CAACE,SAAF,KAAgBD,CAAC,CAACC,SAAlB,IAA+BF,CAAC,CAACG,OAAF,KAAcF,CAAC,CAACE,OAAtD;IACH,CAF6B,CAD9B,EAIKzG,SAJL,CAIe0G,KAAK,IAAI;MACpB;MACA,IAAI,KAAK5B,iBAAL,CAAuB4B,KAAK,CAACD,OAA7B,KAAyC,KAAK3B,iBAAL,CAAuB,KAAKC,SAA5B,CAA7C,EAAqF;QACjF,KAAKiB,WAAL,CAAiBW,IAAjB;MACH;;MACD,IAAI,KAAK7B,iBAAL,CAAuB4B,KAAK,CAACF,SAA7B,KAA2C,CAAC,KAAK1B,iBAAL,CAAuB,KAAKC,SAA5B,CAAhD,EAAwF;QACpF,KAAKK,mBAAL,CAAyBuB,IAAzB;MACH;IACJ,CAZD;EAaH;EACD;;;EACY,IAARtE,QAAQ,CAACA,QAAD,EAAW;IACnB,KAAKuE,cAAL,GAAsBvE,QAAtB;;IACA,KAAK+D,8BAAL;EACH;EACD;AACJ;AACA;AACA;;;EACIjD,QAAQ,GAAG;IACP,IAAI,KAAK4B,SAAL,IAAkB,QAAlB,IAA8B,KAAKzC,MAAL,IAAe,IAAjD,EAAuD;MACnD,KAAKyC,SAAL,GAAiB,KAAK8B,0BAAL,CAAgC,KAAKvE,MAArC,CAAjB;IACH;EACJ;;EACDW,WAAW,GAAG;IACV,KAAK4C,sBAAL,CAA4BN,WAA5B;;IACA,KAAKO,qBAAL,CAA2B5C,QAA3B;EACH;;EACD4D,sBAAsB,CAACJ,KAAD,EAAQ;IAC1B,MAAM1B,WAAW,GAAG,KAAKF,iBAAL,CAAuB4B,KAAK,CAACD,OAA7B,CAApB;;IACA,KAAK5B,gBAAL,CAAsB8B,IAAtB,CAA2B3B,WAA3B;;IACA,IAAIA,WAAJ,EAAiB;MACb,KAAKe,YAAL,CAAkBY,IAAlB,CAAuB,KAAKpH,WAAL,CAAiBY,aAAjB,CAA+B4G,YAAtD;IACH;EACJ;EACD;;;EACAC,mBAAmB,GAAG;IAClB,OAAO,KAAKrB,IAAL,IAAa,KAAKA,IAAL,CAAUjD,KAAV,KAAoB,KAAjC,GAAyC,KAAzC,GAAiD,KAAxD;EACH;EACD;;;EACAoC,iBAAiB,CAACzC,QAAD,EAAW;IACxB,OAAQA,QAAQ,IAAI,QAAZ,IAAwBA,QAAQ,IAAI,oBAApC,IAA4DA,QAAQ,IAAI,qBAAhF;EACH;EACD;;;EACA+D,8BAA8B,CAACD,GAAG,GAAG,KAAKa,mBAAL,EAAP,EAAmC;IAC7D,IAAI,KAAKJ,cAAL,GAAsB,CAA1B,EAA6B;MACzB,KAAK7B,SAAL,GAAiBoB,GAAG,IAAI,KAAP,GAAe,MAAf,GAAwB,OAAzC;IACH,CAFD,MAGK,IAAI,KAAKS,cAAL,GAAsB,CAA1B,EAA6B;MAC9B,KAAK7B,SAAL,GAAiBoB,GAAG,IAAI,KAAP,GAAe,OAAf,GAAyB,MAA1C;IACH,CAFI,MAGA;MACD,KAAKpB,SAAL,GAAiB,QAAjB;IACH;EACJ;EACD;AACJ;AACA;AACA;;;EACI8B,0BAA0B,CAACvE,MAAD,EAAS;IAC/B,MAAM6D,GAAG,GAAG,KAAKa,mBAAL,EAAZ;;IACA,IAAKb,GAAG,IAAI,KAAP,IAAgB7D,MAAM,IAAI,CAA3B,IAAkC6D,GAAG,IAAI,KAAP,IAAgB7D,MAAM,GAAG,CAA/D,EAAmE;MAC/D,OAAO,oBAAP;IACH;;IACD,OAAO,qBAAP;EACH;;AAnGiB;;AAqGtBoD,eAAe,CAACpF,IAAhB;EAAA,iBAA4GoF,eAA5G,EA1Z4FzK,EA0Z5F,mBAA6IA,EAAE,CAACsF,UAAhJ,GA1Z4FtF,EA0Z5F,mBAAuK4C,EAAE,CAACoJ,cAA1K,MA1Z4FhM,EA0Z5F,mBAAqNA,EAAE,CAACiM,iBAAxN;AAAA;;AACAxB,eAAe,CAACjF,IAAhB,kBA3Z4FxF,EA2Z5F;EAAA,MAAgGyK,eAAhG;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;EAAA;AAAA;;AACA;EAAA,mDA5Z4FzK,EA4Z5F,mBAA2FyK,eAA3F,EAAwH,CAAC;IAC7GhF,IAAI,EAAEvF;EADuG,CAAD,CAAxH,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAEuF,IAAI,EAAEzF,EAAE,CAACsF;IAAX,CAAD,EAA0B;MAAEG,IAAI,EAAE7C,EAAE,CAACoJ,cAAX;MAA2BlG,UAAU,EAAE,CAAC;QAC/FL,IAAI,EAAErF;MADyF,CAAD;IAAvC,CAA1B,EAE3B;MAAEqF,IAAI,EAAEzF,EAAE,CAACiM;IAAX,CAF2B,CAAP;EAEc,CAJxD,EAI0E;IAAEnB,YAAY,EAAE,CAAC;MAC3ErF,IAAI,EAAE3E;IADqE,CAAD,CAAhB;IAE1D8I,gBAAgB,EAAE,CAAC;MACnBnE,IAAI,EAAE3E;IADa,CAAD,CAFwC;IAI1DqJ,mBAAmB,EAAE,CAAC;MACtB1E,IAAI,EAAE3E;IADgB,CAAD,CAJqC;IAM1DiK,WAAW,EAAE,CAAC;MACdtF,IAAI,EAAE3E;IADQ,CAAD,CAN6C;IAQ1DoJ,QAAQ,EAAE,CAAC;MACXzE,IAAI,EAAE9E,KADK;MAEX+E,IAAI,EAAE,CAAC,SAAD;IAFK,CAAD,CARgD;IAW1D2B,MAAM,EAAE,CAAC;MACT5B,IAAI,EAAE9E;IADG,CAAD,CAXkD;IAa1DqK,iBAAiB,EAAE,CAAC;MACpBvF,IAAI,EAAE9E;IADc,CAAD,CAbuC;IAe1DyJ,eAAe,EAAE,CAAC;MAClB3E,IAAI,EAAE9E;IADY,CAAD,CAfyC;IAiB1DyG,QAAQ,EAAE,CAAC;MACX3B,IAAI,EAAE9E;IADK,CAAD;EAjBgD,CAJ1E;AAAA;AAwBA;AACA;AACA;AACA;;;AACA,MAAM6J,UAAN,SAAyBC,eAAzB,CAAyC;EACrCpG,WAAW,CAAC6H,UAAD,EAAahB,GAAb,EAAkBP,iBAAlB,EAAqC;IAC5C,MAAMuB,UAAN,EAAkBhB,GAAlB,EAAuBP,iBAAvB;EACH;;AAHoC;;AAKzCH,UAAU,CAACnF,IAAX;EAAA,iBAAuGmF,UAAvG,EA7b4FxK,EA6b5F,mBAAmIA,EAAE,CAACsF,UAAtI,GA7b4FtF,EA6b5F,mBAA6J4C,EAAE,CAACoJ,cAAhK,MA7b4FhM,EA6b5F,mBAA2MA,EAAE,CAACiM,iBAA9M;AAAA;;AACAzB,UAAU,CAACnC,IAAX,kBA9b4FrI,EA8b5F;EAAA,MAA2FwK,UAA3F;EAAA;EAAA;IAAA;MA9b4FxK,EA8b5F,aAAgPL,eAAhP;IAAA;;IAAA;MAAA;;MA9b4FK,EA8b5F,qBA9b4FA,EA8b5F;IAAA;EAAA;EAAA;EAAA,WA9b4FA,EA8b5F;EAAA;EAAA;EAAA;EAAA;IAAA;MA9b4FA,EA8b2O,+BAAvU;MA9b4FA,EA8bqZ;QAAA,OAAwB,kCAAxB;MAAA;QAAA,OAAsF,sCAAtF;MAAA,EAAjf;MA9b4FA,EA8bwiB,yEAApoB;MA9b4FA,EA8bolB,eAAhrB;IAAA;;IAAA;MA9b4FA,EA8b8R,6BA9b9RA,EA8b8R,wCA9b9RA,EA8b8R,iDAA1X;IAAA;EAAA;EAAA,eAAy6BsJ,gBAAz6B;EAAA;EAAA;EAAA;IAAA,WAAw+B,CAACJ,iBAAiB,CAACC,YAAnB;EAAx+B;AAAA;;AACA;EAAA,mDA/b4FnJ,EA+b5F,mBAA2FwK,UAA3F,EAAmH,CAAC;IACxG/E,IAAI,EAAEnF,SADkG;IAExGoF,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,cAAZ;MAA4B8C,aAAa,EAAEjI,iBAAiB,CAACkI,IAA7D;MAAmEH,eAAe,EAAEhI,uBAAuB,CAACiI,OAA5G;MAAqH2D,UAAU,EAAE,CAACjD,iBAAiB,CAACC,YAAnB,CAAjI;MAAmKvD,IAAI,EAAE;QACpK,SAAS;MAD2J,CAAzK;MAEIK,QAAQ,EAAE,mXAFd;MAEmYmG,MAAM,EAAE,CAAC,wLAAD;IAF3Y,CAAD;EAFkG,CAAD,CAAnH,EAK4B,YAAY;IAAE,OAAO,CAAC;MAAE3G,IAAI,EAAEzF,EAAE,CAACsF;IAAX,CAAD,EAA0B;MAAEG,IAAI,EAAE7C,EAAE,CAACoJ,cAAX;MAA2BlG,UAAU,EAAE,CAAC;QAC/FL,IAAI,EAAErF;MADyF,CAAD;IAAvC,CAA1B,EAE3B;MAAEqF,IAAI,EAAEzF,EAAE,CAACiM;IAAX,CAF2B,CAAP;EAEc,CAPxD,EAO0E;IAAEI,WAAW,EAAE,CAAC;MAC1E5G,IAAI,EAAE/E,SADoE;MAE1EgF,IAAI,EAAE,CAAC/F,eAAD;IAFoE,CAAD;EAAf,CAP1E;AAAA;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAM2M,eAAe,GAAG,IAAIrM,cAAJ,CAAmB,iBAAnB,CAAxB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;AACA,MAAMsM,uBAAuB,GAAGnL,aAAa,CAAC,MAAM,EAAP,CAA7C;AAEA;AACA;AACA;AACA;;;AACA,MAAMoL,kBAAN,SAAiCD,uBAAjC,CAAyD;EACrDlI,WAAW,CAAC6H,UAAD,EAAa;IACpB;IACA,KAAKA,UAAL,GAAkBA,UAAlB;EACH;EACD;;;EACAO,KAAK,GAAG;IACJ,KAAKP,UAAL,CAAgBhH,aAAhB,CAA8BuH,KAA9B;EACH;;EACDC,aAAa,GAAG;IACZ,OAAO,KAAKR,UAAL,CAAgBhH,aAAhB,CAA8BjB,UAArC;EACH;;EACD0I,cAAc,GAAG;IACb,OAAO,KAAKT,UAAL,CAAgBhH,aAAhB,CAA8Bf,WAArC;EACH;;AAdoD;;AAgBzDqI,kBAAkB,CAACnH,IAAnB;EAAA,iBAA+GmH,kBAA/G,EApf4FxM,EAof5F,mBAAmJA,EAAE,CAACsF,UAAtJ;AAAA;;AACAkH,kBAAkB,CAAChH,IAAnB,kBArf4FxF,EAqf5F;EAAA,MAAmGwM,kBAAnG;EAAA;EAAA;EAAA;IAAA;MArf4FxM,EAqf5F;MArf4FA,EAqf5F;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA,WArf4FA,EAqf5F;AAAA;;AACA;EAAA,mDAtf4FA,EAsf5F,mBAA2FwM,kBAA3F,EAA2H,CAAC;IAChH/G,IAAI,EAAEvF,SAD0G;IAEhHwF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBADX;MAEC2C,MAAM,EAAE,CAAC,UAAD,CAFT;MAGC1C,IAAI,EAAE;QACF,4BAA4B,UAD1B;QAEF,wBAAwB;MAFtB;IAHP,CAAD;EAF0G,CAAD,CAA3H,EAU4B,YAAY;IAAE,OAAO,CAAC;MAAEH,IAAI,EAAEzF,EAAE,CAACsF;IAAX,CAAD,CAAP;EAAmC,CAV7E;AAAA;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMsH,2BAA2B,GAAGtJ,+BAA+B,CAAC;EAChEuJ,OAAO,EAAE;AADuD,CAAD,CAAnE;AAGA;AACA;AACA;AACA;;AACA,MAAMC,sBAAsB,GAAG,EAA/B;AACA;AACA;AACA;AACA;;AACA,MAAMC,mBAAmB,GAAG,GAA5B;AACA;AACA;AACA;AACA;;AACA,MAAMC,sBAAsB,GAAG,GAA/B;AACA;AACA;AACA;AACA;;AACA,MAAMC,qBAAN,CAA4B;EACxB5I,WAAW,CAACC,WAAD,EAAc4I,kBAAd,EAAkCC,cAAlC,EAAkDzC,IAAlD,EAAwDnG,OAAxD,EAAiE6I,SAAjE,EAA4E3I,cAA5E,EAA4F;IACnG,KAAKH,WAAL,GAAmBA,WAAnB;IACA,KAAK4I,kBAAL,GAA0BA,kBAA1B;IACA,KAAKC,cAAL,GAAsBA,cAAtB;IACA,KAAKzC,IAAL,GAAYA,IAAZ;IACA,KAAKnG,OAAL,GAAeA,OAAf;IACA,KAAK6I,SAAL,GAAiBA,SAAjB;IACA,KAAK3I,cAAL,GAAsBA,cAAtB;IACA;;IACA,KAAK4I,eAAL,GAAuB,CAAvB;IACA;;IACA,KAAKC,qBAAL,GAA6B,KAA7B;IACA;;IACA,KAAKC,UAAL,GAAkB,IAAInL,OAAJ,EAAlB;IACA;;IACA,KAAKoL,uBAAL,GAA+B,KAA/B;IACA;;IACA,KAAKC,mBAAL,GAA2B,IAA3B;IACA;;IACA,KAAKC,oBAAL,GAA4B,IAA5B;IACA;;IACA,KAAKC,cAAL,GAAsB,IAAIvL,OAAJ,EAAtB;IACA,KAAKwL,kBAAL,GAA0B,KAA1B;IACA,KAAKC,cAAL,GAAsB,CAAtB;IACA;;IACA,KAAKC,kBAAL,GAA0B,IAAIjN,YAAJ,EAA1B;IACA;;IACA,KAAKkN,YAAL,GAAoB,IAAIlN,YAAJ,EAApB,CA3BmG,CA4BnG;;IACA0D,OAAO,CAACyJ,iBAAR,CAA0B,MAAM;MAC5B1L,SAAS,CAACgC,WAAW,CAACY,aAAb,EAA4B,YAA5B,CAAT,CACKJ,IADL,CACU9C,SAAS,CAAC,KAAKuL,UAAN,CADnB,EAEKxI,SAFL,CAEe,MAAM;QACjB,KAAKkJ,aAAL;MACH,CAJD;IAKH,CAND;EAOH;EACD;AACJ;AACA;AACA;;;EACyB,IAAjBC,iBAAiB,GAAG;IACpB,OAAO,KAAKN,kBAAZ;EACH;;EACoB,IAAjBM,iBAAiB,CAACzG,KAAD,EAAQ;IACzB,KAAKmG,kBAAL,GAA0B1K,qBAAqB,CAACuE,KAAD,CAA/C;EACH;EACD;;;EACiB,IAAb0G,aAAa,GAAG;IAChB,OAAO,KAAKN,cAAZ;EACH;;EACgB,IAAbM,aAAa,CAAC1G,KAAD,EAAQ;IACrBA,KAAK,GAAGtE,oBAAoB,CAACsE,KAAD,CAA5B;;IACA,IAAI,KAAKoG,cAAL,IAAuBpG,KAA3B,EAAkC;MAC9B,KAAK6F,qBAAL,GAA6B,IAA7B;MACA,KAAKO,cAAL,GAAsBpG,KAAtB;;MACA,IAAI,KAAK2G,WAAT,EAAsB;QAClB,KAAKA,WAAL,CAAiBC,gBAAjB,CAAkC5G,KAAlC;MACH;IACJ;EACJ;;EACD6G,eAAe,GAAG;IACd;IACAhM,SAAS,CAAC,KAAKiM,kBAAL,CAAwBrJ,aAAzB,EAAwC,YAAxC,EAAsD0H,2BAAtD,CAAT,CACK9H,IADL,CACU9C,SAAS,CAAC,KAAKuL,UAAN,CADnB,EAEKxI,SAFL,CAEe,MAAM;MACjB,KAAKyJ,qBAAL,CAA2B,QAA3B;IACH,CAJD;IAKAlM,SAAS,CAAC,KAAKmM,cAAL,CAAoBvJ,aAArB,EAAoC,YAApC,EAAkD0H,2BAAlD,CAAT,CACK9H,IADL,CACU9C,SAAS,CAAC,KAAKuL,UAAN,CADnB,EAEKxI,SAFL,CAEe,MAAM;MACjB,KAAKyJ,qBAAL,CAA2B,OAA3B;IACH,CAJD;EAKH;;EACDE,kBAAkB,GAAG;IACjB,MAAMC,SAAS,GAAG,KAAKjE,IAAL,GAAY,KAAKA,IAAL,CAAUO,MAAtB,GAA+B1I,EAAE,CAAC,KAAD,CAAnD;;IACA,MAAMqM,MAAM,GAAG,KAAKzB,cAAL,CAAoBlC,MAApB,CAA2B,GAA3B,CAAf;;IACA,MAAM4D,OAAO,GAAG,MAAM;MAClB,KAAKC,gBAAL;;MACA,KAAKC,yBAAL;IACH,CAHD;;IAIA,KAAKX,WAAL,GAAmB,IAAIhP,eAAJ,CAAoB,KAAK4P,MAAzB,EACdC,yBADc,CACY,KAAKlD,mBAAL,EADZ,EAEdmD,cAFc,GAGdC,QAHc,EAAnB;;IAIA,KAAKf,WAAL,CAAiBC,gBAAjB,CAAkC,KAAKR,cAAvC,EAXiB,CAYjB;IACA;IACA;IACA;;;IACA,KAAKtJ,OAAL,CAAaM,QAAb,CAAsBC,IAAtB,CAA2BjD,IAAI,CAAC,CAAD,CAA/B,EAAoCkD,SAApC,CAA8C8J,OAA9C,EAhBiB,CAiBjB;IACA;;;IACArM,KAAK,CAACmM,SAAD,EAAYC,MAAZ,EAAoB,KAAKI,MAAL,CAAYnH,OAAhC,EAAyC,KAAKuH,aAAL,EAAzC,CAAL,CACKtK,IADL,CACU9C,SAAS,CAAC,KAAKuL,UAAN,CADnB,EAEKxI,SAFL,CAEe,MAAM;MACjB;MACA;MACA;MACA,KAAKR,OAAL,CAAaK,GAAb,CAAiB,MAAM;QACnByK,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuB,MAAM;UACzB;UACA,KAAKlC,eAAL,GAAuBmC,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYD,IAAI,CAACE,GAAL,CAAS,KAAKC,qBAAL,EAAT,EAAuC,KAAKtC,eAA5C,CAAZ,CAAvB;UACAwB,OAAO;QACV,CAJD;MAKH,CAND;;MAOA,KAAKT,WAAL,CAAiBa,yBAAjB,CAA2C,KAAKlD,mBAAL,EAA3C;IACH,CAdD,EAnBiB,CAkCjB;IACA;IACA;;IACA,KAAKqC,WAAL,CAAiBnD,MAAjB,CAAwBnG,IAAxB,CAA6B9C,SAAS,CAAC,KAAKuL,UAAN,CAAtC,EAAyDxI,SAAzD,CAAmE6K,aAAa,IAAI;MAChF,KAAK7B,YAAL,CAAkBrC,IAAlB,CAAuBkE,aAAvB;;MACA,KAAKC,YAAL,CAAkBD,aAAlB;IACH,CAHD;EAIH;EACD;;;EACAR,aAAa,GAAG;IACZ,IAAI,OAAOU,cAAP,KAA0B,UAA9B,EAA0C;MACtC,OAAOrN,KAAP;IACH;;IACD,OAAO,KAAKuM,MAAL,CAAYnH,OAAZ,CAAoB/C,IAApB,CAAyBhD,SAAS,CAAC,KAAKkN,MAAN,CAAlC,EAAiD/M,SAAS,CAAE8N,QAAD,IAAc,IAAIrN,UAAJ,CAAgBsN,QAAD,IAAc,KAAKzL,OAAL,CAAayJ,iBAAb,CAA+B,MAAM;MAC9I,MAAMiC,cAAc,GAAG,IAAIH,cAAJ,CAAmBI,OAAO,IAAIF,QAAQ,CAACjI,IAAT,CAAcmI,OAAd,CAA9B,CAAvB;MACAH,QAAQ,CAACI,OAAT,CAAiBC,IAAI,IAAIH,cAAc,CAACI,OAAf,CAAuBD,IAAI,CAAClE,UAAL,CAAgBhH,aAAvC,CAAzB;MACA,OAAO,MAAM;QACT+K,cAAc,CAACK,UAAf;MACH,CAFD;IAGH,CAN4G,CAA7B,CAAf,CAA1D,EAOP;IACA;IACApO,IAAI,CAAC,CAAD,CATG,EAUP;IACA;IACAC,MAAM,CAAC+N,OAAO,IAAIA,OAAO,CAACK,IAAR,CAAaC,CAAC,IAAIA,CAAC,CAACC,WAAF,CAAcvM,KAAd,GAAsB,CAAtB,IAA2BsM,CAAC,CAACC,WAAF,CAAcC,MAAd,GAAuB,CAApE,CAAZ,CAZC,CAAP;EAaH;;EACDC,qBAAqB,GAAG;IACpB;IACA,IAAI,KAAKC,cAAL,IAAuB,KAAK5B,MAAL,CAAY6B,MAAvC,EAA+C;MAC3C,KAAK/B,gBAAL;MACA,KAAK8B,cAAL,GAAsB,KAAK5B,MAAL,CAAY6B,MAAlC;;MACA,KAAK3D,kBAAL,CAAwB9B,YAAxB;IACH,CANmB,CAOpB;IACA;;;IACA,IAAI,KAAKkC,qBAAT,EAAgC;MAC5B,KAAKwD,cAAL,CAAoB,KAAKjD,cAAzB;;MACA,KAAKkD,uBAAL;;MACA,KAAKhC,yBAAL;;MACA,KAAKzB,qBAAL,GAA6B,KAA7B;;MACA,KAAKJ,kBAAL,CAAwB9B,YAAxB;IACH,CAfmB,CAgBpB;IACA;;;IACA,IAAI,KAAK4F,sBAAT,EAAiC;MAC7B,KAAKC,wBAAL;;MACA,KAAKD,sBAAL,GAA8B,KAA9B;;MACA,KAAK9D,kBAAL,CAAwB9B,YAAxB;IACH;EACJ;;EACDpD,WAAW,GAAG;IACV,KAAKuF,UAAL,CAAgBxF,IAAhB;;IACA,KAAKwF,UAAL,CAAgBtF,QAAhB;;IACA,KAAK0F,cAAL,CAAoB1F,QAApB;EACH;EACD;;;EACAiJ,cAAc,CAACzF,KAAD,EAAQ;IAClB;IACA,IAAIlI,cAAc,CAACkI,KAAD,CAAlB,EAA2B;MACvB;IACH;;IACD,QAAQA,KAAK,CAAC0F,OAAd;MACI,KAAK1N,KAAL;MACA,KAAKD,KAAL;QACI,IAAI,KAAK4N,UAAL,KAAoB,KAAKjD,aAA7B,EAA4C;UACxC,KAAKL,kBAAL,CAAwBpC,IAAxB,CAA6B,KAAK0F,UAAlC;;UACA,KAAKC,aAAL,CAAmB5F,KAAnB;QACH;;QACD;;MACJ;QACI,KAAK2C,WAAL,CAAiBkD,SAAjB,CAA2B7F,KAA3B;;IATR;EAWH;EACD;AACJ;AACA;;;EACI8F,iBAAiB,GAAG;IAChB,MAAMC,WAAW,GAAG,KAAKlN,WAAL,CAAiBY,aAAjB,CAA+BsM,WAAnD,CADgB,CAEhB;IACA;IACA;;IACA,IAAIA,WAAW,KAAK,KAAKC,mBAAzB,EAA8C;MAC1C,KAAKA,mBAAL,GAA2BD,WAAW,IAAI,EAA1C,CAD0C,CAE1C;MACA;;MACA,KAAKjN,OAAL,CAAaK,GAAb,CAAiB,MAAM;QACnB,KAAKkK,gBAAL;;QACA,KAAKC,yBAAL;;QACA,KAAK7B,kBAAL,CAAwB9B,YAAxB;MACH,CAJD;IAKH;EACJ;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACI0D,gBAAgB,GAAG;IACf,KAAK4C,uBAAL;;IACA,KAAKX,uBAAL;;IACA,KAAKE,wBAAL;EACH;EACD;;;EACc,IAAVG,UAAU,GAAG;IACb,OAAO,KAAKhD,WAAL,GAAmB,KAAKA,WAAL,CAAiBuD,eAApC,GAAsD,CAA7D;EACH;EACD;;;EACc,IAAVP,UAAU,CAAC3J,KAAD,EAAQ;IAClB,IAAI,CAAC,KAAKmK,aAAL,CAAmBnK,KAAnB,CAAD,IAA8B,KAAK2J,UAAL,KAAoB3J,KAAlD,IAA2D,CAAC,KAAK2G,WAArE,EAAkF;MAC9E;IACH;;IACD,KAAKA,WAAL,CAAiByD,aAAjB,CAA+BpK,KAA/B;EACH;EACD;AACJ;AACA;AACA;;;EACImK,aAAa,CAACE,KAAD,EAAQ;IACjB,IAAI,CAAC,KAAK9C,MAAV,EAAkB;MACd,OAAO,IAAP;IACH;;IACD,MAAM+C,GAAG,GAAG,KAAK/C,MAAL,GAAc,KAAKA,MAAL,CAAYgD,OAAZ,GAAsBF,KAAtB,CAAd,GAA6C,IAAzD;IACA,OAAO,CAAC,CAACC,GAAF,IAAS,CAACA,GAAG,CAACE,QAArB;EACH;EACD;AACJ;AACA;AACA;;;EACIpC,YAAY,CAACqC,QAAD,EAAW;IACnB,IAAI,KAAK1E,uBAAT,EAAkC;MAC9B,KAAKsD,cAAL,CAAoBoB,QAApB;IACH;;IACD,IAAI,KAAKlD,MAAL,IAAe,KAAKA,MAAL,CAAY6B,MAA/B,EAAuC;MACnC,KAAK7B,MAAL,CAAYgD,OAAZ,GAAsBE,QAAtB,EAAgCzF,KAAhC,GADmC,CAEnC;MACA;MACA;;;MACA,MAAM0F,WAAW,GAAG,KAAKC,iBAAL,CAAuBlN,aAA3C;;MACA,MAAMgG,GAAG,GAAG,KAAKa,mBAAL,EAAZ;;MACA,IAAIb,GAAG,IAAI,KAAX,EAAkB;QACdiH,WAAW,CAACE,UAAZ,GAAyB,CAAzB;MACH,CAFD,MAGK;QACDF,WAAW,CAACE,UAAZ,GAAyBF,WAAW,CAACG,WAAZ,GAA0BH,WAAW,CAAChO,WAA/D;MACH;IACJ;EACJ;EACD;;;EACA4H,mBAAmB,GAAG;IAClB,OAAO,KAAKrB,IAAL,IAAa,KAAKA,IAAL,CAAUjD,KAAV,KAAoB,KAAjC,GAAyC,KAAzC,GAAiD,KAAxD;EACH;EACD;;;EACAwJ,wBAAwB,GAAG;IACvB,IAAI,KAAK/C,iBAAT,EAA4B;MACxB;IACH;;IACD,MAAMqE,cAAc,GAAG,KAAKA,cAA5B;IACA,MAAMC,UAAU,GAAG,KAAKzG,mBAAL,OAA+B,KAA/B,GAAuC,CAACwG,cAAxC,GAAyDA,cAA5E,CALuB,CAMvB;IACA;IACA;IACA;IACA;IACA;;IACA,KAAKE,QAAL,CAAcvN,aAAd,CAA4BnC,KAA5B,CAAkCqG,SAAlC,GAA+C,cAAaoG,IAAI,CAACkD,KAAL,CAAWF,UAAX,CAAuB,KAAnF,CAZuB,CAavB;IACA;IACA;IACA;;IACA,IAAI,KAAKpF,SAAL,CAAeuF,OAAf,IAA0B,KAAKvF,SAAL,CAAewF,IAA7C,EAAmD;MAC/C,KAAKR,iBAAL,CAAuBlN,aAAvB,CAAqCmN,UAArC,GAAkD,CAAlD;IACH;EACJ;EACD;;;EACkB,IAAdE,cAAc,GAAG;IACjB,OAAO,KAAKlF,eAAZ;EACH;;EACiB,IAAdkF,cAAc,CAAC9K,KAAD,EAAQ;IACtB,KAAKoL,SAAL,CAAepL,KAAf;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIqL,aAAa,CAACC,SAAD,EAAY;IACrB,MAAMC,UAAU,GAAG,KAAKZ,iBAAL,CAAuBlN,aAAvB,CAAqCf,WAAxD,CADqB,CAErB;;IACA,MAAM8O,YAAY,GAAI,CAACF,SAAS,IAAI,QAAb,GAAwB,CAAC,CAAzB,GAA6B,CAA9B,IAAmCC,UAApC,GAAkD,CAAvE;IACA,OAAO,KAAKH,SAAL,CAAe,KAAKxF,eAAL,GAAuB4F,YAAtC,CAAP;EACH;EACD;;;EACAC,qBAAqB,CAACH,SAAD,EAAY;IAC7B,KAAK9E,aAAL;;IACA,KAAK6E,aAAL,CAAmBC,SAAnB;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIjC,cAAc,CAACqC,UAAD,EAAa;IACvB,IAAI,KAAKjF,iBAAT,EAA4B;MACxB;IACH;;IACD,MAAMkF,aAAa,GAAG,KAAKpE,MAAL,GAAc,KAAKA,MAAL,CAAYgD,OAAZ,GAAsBmB,UAAtB,CAAd,GAAkD,IAAxE;;IACA,IAAI,CAACC,aAAL,EAAoB;MAChB;IACH,CAPsB,CAQvB;;;IACA,MAAMJ,UAAU,GAAG,KAAKZ,iBAAL,CAAuBlN,aAAvB,CAAqCf,WAAxD;IACA,MAAM;MAAEF,UAAF;MAAcE;IAAd,IAA8BiP,aAAa,CAAClH,UAAd,CAAyBhH,aAA7D;IACA,IAAImO,cAAJ,EAAoBC,aAApB;;IACA,IAAI,KAAKvH,mBAAL,MAA8B,KAAlC,EAAyC;MACrCsH,cAAc,GAAGpP,UAAjB;MACAqP,aAAa,GAAGD,cAAc,GAAGlP,WAAjC;IACH,CAHD,MAIK;MACDmP,aAAa,GAAG,KAAKC,aAAL,CAAmBrO,aAAnB,CAAiCf,WAAjC,GAA+CF,UAA/D;MACAoP,cAAc,GAAGC,aAAa,GAAGnP,WAAjC;IACH;;IACD,MAAMqP,gBAAgB,GAAG,KAAKjB,cAA9B;IACA,MAAMkB,eAAe,GAAG,KAAKlB,cAAL,GAAsBS,UAA9C;;IACA,IAAIK,cAAc,GAAGG,gBAArB,EAAuC;MACnC;MACA,KAAKjB,cAAL,IAAuBiB,gBAAgB,GAAGH,cAAnB,GAAoCvG,sBAA3D;IACH,CAHD,MAIK,IAAIwG,aAAa,GAAGG,eAApB,EAAqC;MACtC;MACA,KAAKlB,cAAL,IAAuBe,aAAa,GAAGG,eAAhB,GAAkC3G,sBAAzD;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;EACI4E,uBAAuB,GAAG;IACtB,IAAI,KAAKxD,iBAAT,EAA4B;MACxB,KAAKV,uBAAL,GAA+B,KAA/B;IACH,CAFD,MAGK;MACD,MAAMkG,SAAS,GAAG,KAAKH,aAAL,CAAmBrO,aAAnB,CAAiCoN,WAAjC,GAA+C,KAAKhO,WAAL,CAAiBY,aAAjB,CAA+Bf,WAAhG;;MACA,IAAI,CAACuP,SAAL,EAAgB;QACZ,KAAKnB,cAAL,GAAsB,CAAtB;MACH;;MACD,IAAImB,SAAS,KAAK,KAAKlG,uBAAvB,EAAgD;QAC5C,KAAKN,kBAAL,CAAwB9B,YAAxB;MACH;;MACD,KAAKoC,uBAAL,GAA+BkG,SAA/B;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACI3C,uBAAuB,GAAG;IACtB,IAAI,KAAK7C,iBAAT,EAA4B;MACxB,KAAKT,mBAAL,GAA2B,KAAKC,oBAAL,GAA4B,IAAvD;IACH,CAFD,MAGK;MACD;MACA,KAAKA,oBAAL,GAA4B,KAAK6E,cAAL,IAAuB,CAAnD;MACA,KAAK9E,mBAAL,GAA2B,KAAK8E,cAAL,IAAuB,KAAK5C,qBAAL,EAAlD;;MACA,KAAKzC,kBAAL,CAAwB9B,YAAxB;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACIuE,qBAAqB,GAAG;IACpB,MAAMgE,eAAe,GAAG,KAAKJ,aAAL,CAAmBrO,aAAnB,CAAiCoN,WAAzD;IACA,MAAMU,UAAU,GAAG,KAAKZ,iBAAL,CAAuBlN,aAAvB,CAAqCf,WAAxD;IACA,OAAOwP,eAAe,GAAGX,UAAlB,IAAgC,CAAvC;EACH;EACD;;;EACAjE,yBAAyB,GAAG;IACxB,MAAM6E,YAAY,GAAG,KAAK5E,MAAL,IAAe,KAAKA,MAAL,CAAY6B,MAA3B,GAAoC,KAAK7B,MAAL,CAAYgD,OAAZ,GAAsB,KAAK7D,aAA3B,CAApC,GAAgF,IAArG;IACA,MAAM0F,oBAAoB,GAAGD,YAAY,GAAGA,YAAY,CAAC1H,UAAb,CAAwBhH,aAA3B,GAA2C,IAApF;;IACA,IAAI2O,oBAAJ,EAA0B;MACtB,KAAKC,OAAL,CAAapP,cAAb,CAA4BmP,oBAA5B;IACH,CAFD,MAGK;MACD,KAAKC,OAAL,CAAa1O,IAAb;IACH;EACJ;EACD;;;EACA6I,aAAa,GAAG;IACZ,KAAKN,cAAL,CAAoB5F,IAApB;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIyG,qBAAqB,CAACuE,SAAD,EAAYgB,UAAZ,EAAwB;IACzC;IACA;IACA,IAAIA,UAAU,IAAIA,UAAU,CAACC,MAAX,IAAqB,IAAnC,IAA2CD,UAAU,CAACC,MAAX,KAAsB,CAArE,EAAwE;MACpE;IACH,CALwC,CAMzC;;;IACA,KAAK/F,aAAL,GAPyC,CAQzC;;;IACAtL,KAAK,CAACoK,mBAAD,EAAsBC,sBAAtB,CAAL,CACI;IADJ,CAEKlI,IAFL,CAEU9C,SAAS,CAACQ,KAAK,CAAC,KAAKmL,cAAN,EAAsB,KAAKJ,UAA3B,CAAN,CAFnB,EAGKxI,SAHL,CAGe,MAAM;MACjB,MAAM;QAAEkP,iBAAF;QAAqBC;MAArB,IAAkC,KAAKpB,aAAL,CAAmBC,SAAnB,CAAxC,CADiB,CAEjB;;;MACA,IAAImB,QAAQ,KAAK,CAAb,IAAkBA,QAAQ,IAAID,iBAAlC,EAAqD;QACjD,KAAKhG,aAAL;MACH;IACJ,CATD;EAUH;EACD;AACJ;AACA;AACA;AACA;;;EACI4E,SAAS,CAACzL,QAAD,EAAW;IAChB,IAAI,KAAK8G,iBAAT,EAA4B;MACxB,OAAO;QAAE+F,iBAAiB,EAAE,CAArB;QAAwBC,QAAQ,EAAE;MAAlC,CAAP;IACH;;IACD,MAAMD,iBAAiB,GAAG,KAAKtE,qBAAL,EAA1B;;IACA,KAAKtC,eAAL,GAAuBmC,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYD,IAAI,CAACE,GAAL,CAASuE,iBAAT,EAA4B7M,QAA5B,CAAZ,CAAvB,CALgB,CAMhB;IACA;;IACA,KAAK4J,sBAAL,GAA8B,IAA9B;;IACA,KAAKD,uBAAL;;IACA,OAAO;MAAEkD,iBAAF;MAAqBC,QAAQ,EAAE,KAAK7G;IAApC,CAAP;EACH;;AA3cuB;;AA6c5BJ,qBAAqB,CAAC5H,IAAtB;EAAA,iBAAkH4H,qBAAlH,EA7+B4FjN,EA6+B5F,mBAAyJA,EAAE,CAACsF,UAA5J,GA7+B4FtF,EA6+B5F,mBAAmLA,EAAE,CAACiM,iBAAtL,GA7+B4FjM,EA6+B5F,mBAAoNoD,IAAI,CAAC+Q,aAAzN,GA7+B4FnU,EA6+B5F,mBAAmP4C,EAAE,CAACoJ,cAAtP,MA7+B4FhM,EA6+B5F,mBAAiSA,EAAE,CAACuF,MAApS,GA7+B4FvF,EA6+B5F,mBAAuTqD,EAAE,CAAC+Q,QAA1T,GA7+B4FpU,EA6+B5F,mBAA+U4B,qBAA/U;AAAA;;AACAqL,qBAAqB,CAACzH,IAAtB,kBA9+B4FxF,EA8+B5F;EAAA,MAAsGiN,qBAAtG;EAAA;IAAA;EAAA;AAAA;;AACA;EAAA,mDA/+B4FjN,EA++B5F,mBAA2FiN,qBAA3F,EAA8H,CAAC;IACnHxH,IAAI,EAAEvF;EAD6G,CAAD,CAA9H,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAEuF,IAAI,EAAEzF,EAAE,CAACsF;IAAX,CAAD,EAA0B;MAAEG,IAAI,EAAEzF,EAAE,CAACiM;IAAX,CAA1B,EAA0D;MAAExG,IAAI,EAAErC,IAAI,CAAC+Q;IAAb,CAA1D,EAAwF;MAAE1O,IAAI,EAAE7C,EAAE,CAACoJ,cAAX;MAA2BlG,UAAU,EAAE,CAAC;QAC7JL,IAAI,EAAErF;MADuJ,CAAD;IAAvC,CAAxF,EAE3B;MAAEqF,IAAI,EAAEzF,EAAE,CAACuF;IAAX,CAF2B,EAEN;MAAEE,IAAI,EAAEpC,EAAE,CAAC+Q;IAAX,CAFM,EAEiB;MAAE3O,IAAI,EAAEI,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC9EL,IAAI,EAAErF;MADwE,CAAD,EAE9E;QACCqF,IAAI,EAAEtF,MADP;QAECuF,IAAI,EAAE,CAAC9D,qBAAD;MAFP,CAF8E;IAA/B,CAFjB,CAAP;EAOlB,CATxB,EAS0C;IAAEsM,iBAAiB,EAAE,CAAC;MAChDzI,IAAI,EAAE9E;IAD0C,CAAD;EAArB,CAT1C;AAAA;AAaA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;;AACA,MAAM0T,iBAAN,SAAgCpH,qBAAhC,CAAsD;EAClD5I,WAAW,CAAC6H,UAAD,EAAavB,iBAAb,EAAgC2J,aAAhC,EAA+CpJ,GAA/C,EAAoDqJ,MAApD,EAA4DC,QAA5D,EAAsEC,aAAtE,EAAqF;IAC5F,MAAMvI,UAAN,EAAkBvB,iBAAlB,EAAqC2J,aAArC,EAAoDpJ,GAApD,EAAyDqJ,MAAzD,EAAiEC,QAAjE,EAA2EC,aAA3E;IACA,KAAKC,cAAL,GAAsB,KAAtB;EACH;EACD;;;EACiB,IAAbC,aAAa,GAAG;IAChB,OAAO,KAAKD,cAAZ;EACH;;EACgB,IAAbC,aAAa,CAAClN,KAAD,EAAQ;IACrB,KAAKiN,cAAL,GAAsBxR,qBAAqB,CAACuE,KAAD,CAA3C;EACH;;EACD4J,aAAa,CAAC5F,KAAD,EAAQ;IACjBA,KAAK,CAACmJ,cAAN;EACH;;AAdiD;;AAgBtDP,iBAAiB,CAAChP,IAAlB;EAAA,iBAA8GgP,iBAA9G,EAvhC4FrU,EAuhC5F,mBAAiJA,EAAE,CAACsF,UAApJ,GAvhC4FtF,EAuhC5F,mBAA2KA,EAAE,CAACiM,iBAA9K,GAvhC4FjM,EAuhC5F,mBAA4MoD,IAAI,CAAC+Q,aAAjN,GAvhC4FnU,EAuhC5F,mBAA2O4C,EAAE,CAACoJ,cAA9O,MAvhC4FhM,EAuhC5F,mBAAyRA,EAAE,CAACuF,MAA5R,GAvhC4FvF,EAuhC5F,mBAA+SqD,EAAE,CAAC+Q,QAAlT,GAvhC4FpU,EAuhC5F,mBAAuU4B,qBAAvU;AAAA;;AACAyS,iBAAiB,CAAC7O,IAAlB,kBAxhC4FxF,EAwhC5F;EAAA,MAAkGqU,iBAAlG;EAAA;IAAA;EAAA;EAAA,WAxhC4FrU,EAwhC5F;AAAA;;AACA;EAAA,mDAzhC4FA,EAyhC5F,mBAA2FqU,iBAA3F,EAA0H,CAAC;IAC/G5O,IAAI,EAAEvF;EADyG,CAAD,CAA1H,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAEuF,IAAI,EAAEzF,EAAE,CAACsF;IAAX,CAAD,EAA0B;MAAEG,IAAI,EAAEzF,EAAE,CAACiM;IAAX,CAA1B,EAA0D;MAAExG,IAAI,EAAErC,IAAI,CAAC+Q;IAAb,CAA1D,EAAwF;MAAE1O,IAAI,EAAE7C,EAAE,CAACoJ,cAAX;MAA2BlG,UAAU,EAAE,CAAC;QAC7JL,IAAI,EAAErF;MADuJ,CAAD;IAAvC,CAAxF,EAE3B;MAAEqF,IAAI,EAAEzF,EAAE,CAACuF;IAAX,CAF2B,EAEN;MAAEE,IAAI,EAAEpC,EAAE,CAAC+Q;IAAX,CAFM,EAEiB;MAAE3O,IAAI,EAAEI,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC9EL,IAAI,EAAErF;MADwE,CAAD,EAE9E;QACCqF,IAAI,EAAEtF,MADP;QAECuF,IAAI,EAAE,CAAC9D,qBAAD;MAFP,CAF8E;IAA/B,CAFjB,CAAP;EAOlB,CATxB,EAS0C;IAAE+S,aAAa,EAAE,CAAC;MAC5ClP,IAAI,EAAE9E;IADsC,CAAD;EAAjB,CAT1C;AAAA;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMkU,YAAN,SAA2BR,iBAA3B,CAA6C;EACzChQ,WAAW,CAAC6H,UAAD,EAAavB,iBAAb,EAAgC2J,aAAhC,EAA+CpJ,GAA/C,EAAoDqJ,MAApD,EAA4DC,QAA5D,EAAsEC,aAAtE,EAAqF;IAC5F,MAAMvI,UAAN,EAAkBvB,iBAAlB,EAAqC2J,aAArC,EAAoDpJ,GAApD,EAAyDqJ,MAAzD,EAAiEC,QAAjE,EAA2EC,aAA3E;EACH;;AAHwC;;AAK7CI,YAAY,CAACxP,IAAb;EAAA,iBAAyGwP,YAAzG,EAjjC4F7U,EAijC5F,mBAAuIA,EAAE,CAACsF,UAA1I,GAjjC4FtF,EAijC5F,mBAAiKA,EAAE,CAACiM,iBAApK,GAjjC4FjM,EAijC5F,mBAAkMoD,IAAI,CAAC+Q,aAAvM,GAjjC4FnU,EAijC5F,mBAAiO4C,EAAE,CAACoJ,cAApO,MAjjC4FhM,EAijC5F,mBAA+QA,EAAE,CAACuF,MAAlR,GAjjC4FvF,EAijC5F,mBAAqSqD,EAAE,CAAC+Q,QAAxS,GAjjC4FpU,EAijC5F,mBAA6T4B,qBAA7T;AAAA;;AACAiT,YAAY,CAACxM,IAAb,kBAljC4FrI,EAkjC5F;EAAA,MAA6F6U,YAA7F;EAAA;EAAA;IAAA;MAljC4F7U,EAkjC5F,0BAAggBwM,kBAAhgB;IAAA;;IAAA;MAAA;;MAljC4FxM,EAkjC5F,qBAljC4FA,EAkjC5F;IAAA;EAAA;EAAA;IAAA;MAljC4FA,EAkjC5F,aAAwlBoE,SAAxlB;MAljC4FpE,EAkjC5F;MAljC4FA,EAkjC5F;MAljC4FA,EAkjC5F;MAljC4FA,EAkjC5F;MAljC4FA,EAkjC5F;IAAA;;IAAA;MAAA;;MAljC4FA,EAkjC5F,qBAljC4FA,EAkjC5F;MAljC4FA,EAkjC5F,qBAljC4FA,EAkjC5F;MAljC4FA,EAkjC5F,qBAljC4FA,EAkjC5F;MAljC4FA,EAkjC5F,qBAljC4FA,EAkjC5F;MAljC4FA,EAkjC5F,qBAljC4FA,EAkjC5F;MAljC4FA,EAkjC5F,qBAljC4FA,EAkjC5F;IAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAljC4FA,EAkjC5F;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA,WAljC4FA,EAkjC5F;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAljC4FA,EAkjC5F;MAljC4FA,EAkjC+mC,kCAA3sC;MAljC4FA,EAkjCogD;QAAA,OAAU,0BAAsB,QAAtB,CAAV;MAAA;QAAA,OAAgE,0BAAsB,QAAtB,SAAhE;MAAA;QAAA,OAA6H,mBAA7H;MAAA,EAAhmD;MAljC4FA,EAkjCupD,uBAAnvD;MAljC4FA,EAkjCgtD,eAA5yD;MAljC4FA,EAkjC6tD,+BAAzzD;MAljC4FA,EAkjCsxD;QAAA,OAAY,0BAAZ;MAAA,EAAl3D;MAljC4FA,EAkjC+zD,+BAA35D;MAljC4FA,EAkjCs9D;QAAA,OAAsB,uBAAtB;MAAA,EAAljE;MAljC4FA,EAkjCwgE,+BAApmE;MAljC4FA,EAkjC4jE,gBAAxpE;MAljC4FA,EAkjC2lE,eAAvrE;MAljC4FA,EAkjCumE,6BAAnsE;MAljC4FA,EAkjCsoE,iBAAluE;MAljC4FA,EAkjCwpE,oCAApvE;MAljC4FA,EAkjCqiF;QAAA,OAAc,0BAAsB,OAAtB,SAAd;MAAA;QAAA,OAAuE,0BAAsB,OAAtB,CAAvE;MAAA;QAAA,OAA2H,mBAA3H;MAAA,EAAjoF;MAljC4FA,EAkjCsrF,wBAAlxF;MAljC4FA,EAkjC+uF,eAA30F;IAAA;;IAAA;MAljC4FA,EAkjCw4C,4EAAp+C;MAljC4FA,EAkjCo0C,6HAAh6C;MAljC4FA,EAkjCm3D,aAA/8D;MAljC4FA,EAkjCm3D,gFAA/8D;MAljC4FA,EAkjCq5E,aAAj/E;MAljC4FA,EAkjCq5E,2EAAj/E;MAljC4FA,EAkjCk1E,2HAA96E;IAAA;EAAA;EAAA,eAAwjLmB,EAAE,CAAC2T,SAA3jL,EAA4zLxV,EAAE,CAACyV,iBAA/zL,EAAogM3Q,SAApgM;EAAA;EAAA;AAAA;;AACA;EAAA,mDAnjC4FpE,EAmjC5F,mBAA2F6U,YAA3F,EAAqH,CAAC;IAC1GpP,IAAI,EAAEnF,SADoG;IAE1GoF,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,gBAAZ;MAA8B2C,MAAM,EAAE,CAAC,eAAD,CAAtC;MAAyD0M,OAAO,EAAE,CAAC,oBAAD,EAAuB,cAAvB,CAAlE;MAA0GvM,aAAa,EAAEjI,iBAAiB,CAACkI,IAA3I;MAAiJH,eAAe,EAAEhI,uBAAuB,CAACiI,OAA1L;MAAmM5C,IAAI,EAAE;QACpM,SAAS,gBAD2L;QAEpM,sDAAsD,yBAF8I;QAGpM,8BAA8B;MAHsK,CAAzM;MAIIK,QAAQ,EAAE,6oDAJd;MAI6pDmG,MAAM,EAAE,CAAC,yqFAAD;IAJrqD,CAAD;EAFoG,CAAD,CAArH,EAO4B,YAAY;IAAE,OAAO,CAAC;MAAE3G,IAAI,EAAEzF,EAAE,CAACsF;IAAX,CAAD,EAA0B;MAAEG,IAAI,EAAEzF,EAAE,CAACiM;IAAX,CAA1B,EAA0D;MAAExG,IAAI,EAAErC,IAAI,CAAC+Q;IAAb,CAA1D,EAAwF;MAAE1O,IAAI,EAAE7C,EAAE,CAACoJ,cAAX;MAA2BlG,UAAU,EAAE,CAAC;QAC7JL,IAAI,EAAErF;MADuJ,CAAD;IAAvC,CAAxF,EAE3B;MAAEqF,IAAI,EAAEzF,EAAE,CAACuF;IAAX,CAF2B,EAEN;MAAEE,IAAI,EAAEpC,EAAE,CAAC+Q;IAAX,CAFM,EAEiB;MAAE3O,IAAI,EAAEI,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC9EL,IAAI,EAAErF;MADwE,CAAD,EAE9E;QACCqF,IAAI,EAAEtF,MADP;QAECuF,IAAI,EAAE,CAAC9D,qBAAD;MAFP,CAF8E;IAA/B,CAFjB,CAAP;EAOlB,CAdxB,EAc0C;IAAEoN,MAAM,EAAE,CAAC;MACrCvJ,IAAI,EAAE1E,eAD+B;MAErC2E,IAAI,EAAE,CAAC8G,kBAAD,EAAqB;QAAEyI,WAAW,EAAE;MAAf,CAArB;IAF+B,CAAD,CAAV;IAG1BnB,OAAO,EAAE,CAAC;MACVrO,IAAI,EAAE/E,SADI;MAEVgF,IAAI,EAAE,CAACtB,SAAD,EAAY;QAAEyE,MAAM,EAAE;MAAV,CAAZ;IAFI,CAAD,CAHiB;IAM1BuJ,iBAAiB,EAAE,CAAC;MACpB3M,IAAI,EAAE/E,SADc;MAEpBgF,IAAI,EAAE,CAAC,kBAAD,EAAqB;QAAEmD,MAAM,EAAE;MAAV,CAArB;IAFc,CAAD,CANO;IAS1B4J,QAAQ,EAAE,CAAC;MACXhN,IAAI,EAAE/E,SADK;MAEXgF,IAAI,EAAE,CAAC,SAAD,EAAY;QAAEmD,MAAM,EAAE;MAAV,CAAZ;IAFK,CAAD,CATgB;IAY1B0K,aAAa,EAAE,CAAC;MAChB9N,IAAI,EAAE/E,SADU;MAEhBgF,IAAI,EAAE,CAAC,cAAD,EAAiB;QAAEmD,MAAM,EAAE;MAAV,CAAjB;IAFU,CAAD,CAZW;IAe1B4F,cAAc,EAAE,CAAC;MACjBhJ,IAAI,EAAE/E,SADW;MAEjBgF,IAAI,EAAE,CAAC,eAAD;IAFW,CAAD,CAfU;IAkB1B6I,kBAAkB,EAAE,CAAC;MACrB9I,IAAI,EAAE/E,SADe;MAErBgF,IAAI,EAAE,CAAC,mBAAD;IAFe,CAAD;EAlBM,CAd1C;AAAA;AAqCA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,IAAIwP,MAAM,GAAG,CAAb;AACA;;AACA,MAAMC,iBAAN,CAAwB,E,CAExB;;AACA;;;AACA,MAAMC,qBAAqB,GAAG/T,UAAU,CAACC,kBAAkB,CAAC,MAAM;EAC9D+C,WAAW,CAACC,WAAD,EAAc;IACrB,KAAKA,WAAL,GAAmBA,WAAnB;EACH;;AAH6D,CAAP,CAAnB,EAIpC,SAJoC,CAAxC;AAKA;AACA;AACA;AACA;;;AACA,MAAM+Q,gBAAN,SAA+BD,qBAA/B,CAAqD;EACjD/Q,WAAW,CAAC6H,UAAD,EAAagB,kBAAb,EAAiCoI,aAAjC,EAAgD7Q,cAAhD,EAAgE;IACvE,MAAMyH,UAAN;IACA,KAAKgB,kBAAL,GAA0BA,kBAA1B;IACA,KAAKzI,cAAL,GAAsBA,cAAtB;IACA;;IACA,KAAK8Q,KAAL,GAAa,IAAIvU,SAAJ,EAAb;IACA;;IACA,KAAKwU,cAAL,GAAsB,CAAtB;IACA;;IACA,KAAKC,oBAAL,GAA4B,IAA5B;IACA;;IACA,KAAKC,qBAAL,GAA6B,CAA7B;IACA;;IACA,KAAKC,iBAAL,GAAyBtT,YAAY,CAACI,KAAtC;IACA;;IACA,KAAKmT,qBAAL,GAA6BvT,YAAY,CAACI,KAA1C;IACA,KAAKoT,cAAL,GAAsB,KAAtB;IACA,KAAKhI,cAAL,GAAsB,IAAtB;IACA;;IACA,KAAKiI,cAAL,GAAsB,OAAtB;IACA,KAAKlI,kBAAL,GAA0B,KAA1B;IACA,KAAKmI,gBAAL,GAAwB,KAAxB;IACA;;IACA,KAAKC,mBAAL,GAA2B,IAAInV,YAAJ,EAA3B;IACA;;IACA,KAAKoV,WAAL,GAAmB,IAAIpV,YAAJ,EAAnB;IACA;;IACA,KAAKqV,aAAL,GAAqB,IAAIrV,YAAJ,EAArB;IACA;;IACA,KAAKsV,iBAAL,GAAyB,IAAItV,YAAJ,CAAiB,IAAjB,CAAzB;IACA,KAAKuV,QAAL,GAAgBlB,MAAM,EAAtB;IACA,KAAKlK,iBAAL,GACIsK,aAAa,IAAIA,aAAa,CAACtK,iBAA/B,GAAmDsK,aAAa,CAACtK,iBAAjE,GAAqF,OADzF;IAEA,KAAKkD,iBAAL,GACIoH,aAAa,IAAIA,aAAa,CAACpH,iBAAd,IAAmC,IAApD,GACMoH,aAAa,CAACpH,iBADpB,GAEM,KAHV;IAIA,KAAKmI,aAAL,GACIf,aAAa,IAAIA,aAAa,CAACe,aAAd,IAA+B,IAAhD,GAAuDf,aAAa,CAACe,aAArE,GAAqF,KADzF;IAEA,KAAKC,eAAL,GAAuBhB,aAAa,EAAEgB,eAAf,IAAkC,IAAzD;IACA,KAAKlM,eAAL,GAAuB,CAAC,CAACkL,aAAa,EAAElL,eAAxC;EACH;EACD;;;EACiB,IAAbiM,aAAa,GAAG;IAChB,OAAO,KAAKR,cAAZ;EACH;;EACgB,IAAbQ,aAAa,CAAC5O,KAAD,EAAQ;IACrB,KAAKoO,cAAL,GAAsB3S,qBAAqB,CAACuE,KAAD,CAA3C;EACH;EACD;;;EACiB,IAAb0G,aAAa,GAAG;IAChB,OAAO,KAAKN,cAAZ;EACH;;EACgB,IAAbM,aAAa,CAAC1G,KAAD,EAAQ;IACrB,KAAK+N,cAAL,GAAsBrS,oBAAoB,CAACsE,KAAD,EAAQ,IAAR,CAA1C;EACH;EACD;;;EACqB,IAAjBuD,iBAAiB,GAAG;IACpB,OAAO,KAAKuL,kBAAZ;EACH;;EACoB,IAAjBvL,iBAAiB,CAACvD,KAAD,EAAQ;IACzB,KAAK8O,kBAAL,GAA0B,QAAQC,IAAR,CAAa/O,KAAK,GAAG,EAArB,IAA2BA,KAAK,GAAG,IAAnC,GAA0CA,KAApE;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACuB,IAAf6O,eAAe,GAAG;IAClB,OAAO,KAAKG,gBAAZ;EACH;;EACkB,IAAfH,eAAe,CAAC7O,KAAD,EAAQ;IACvB,KAAKgP,gBAAL,GAAwBtT,oBAAoB,CAACsE,KAAD,EAAQ,IAAR,CAA5C;EACH;EACD;AACJ;AACA;AACA;;;EACyB,IAAjByG,iBAAiB,GAAG;IACpB,OAAO,KAAKN,kBAAZ;EACH;;EACoB,IAAjBM,iBAAiB,CAACzG,KAAD,EAAQ;IACzB,KAAKmG,kBAAL,GAA0B1K,qBAAqB,CAACuE,KAAD,CAA/C;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACuB,IAAf2C,eAAe,GAAG;IAClB,OAAO,KAAK2L,gBAAZ;EACH;;EACkB,IAAf3L,eAAe,CAAC3C,KAAD,EAAQ;IACvB,KAAKsO,gBAAL,GAAwB7S,qBAAqB,CAACuE,KAAD,CAA7C;EACH;EACD;;;EACmB,IAAfiP,eAAe,GAAG;IAClB,OAAO,KAAKC,gBAAZ;EACH;;EACkB,IAAfD,eAAe,CAACjP,KAAD,EAAQ;IACvB,MAAMvC,aAAa,GAAG,KAAKZ,WAAL,CAAiBY,aAAvC;IACAA,aAAa,CAAC0R,SAAd,CAAwBC,MAAxB,CAAgC,kBAAiB,KAAKH,eAAgB,EAAtE;;IACA,IAAIjP,KAAJ,EAAW;MACPvC,aAAa,CAAC0R,SAAd,CAAwBE,GAAxB,CAA6B,kBAAiBrP,KAAM,EAApD;IACH;;IACD,KAAKkP,gBAAL,GAAwBlP,KAAxB;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIkJ,qBAAqB,GAAG;IACpB;IACA;IACA,MAAMoG,aAAa,GAAI,KAAKvB,cAAL,GAAsB,KAAKwB,cAAL,CAAoB,KAAKxB,cAAzB,CAA7C,CAHoB,CAIpB;IACA;;;IACA,IAAI,KAAK3H,cAAL,IAAuBkJ,aAA3B,EAA0C;MACtC,MAAME,UAAU,GAAG,KAAKpJ,cAAL,IAAuB,IAA1C;;MACA,IAAI,CAACoJ,UAAL,EAAiB;QACb,KAAKd,iBAAL,CAAuBzK,IAAvB,CAA4B,KAAKwL,kBAAL,CAAwBH,aAAxB,CAA5B,EADa,CAEb;QACA;;QACA,MAAMI,OAAO,GAAG,KAAKC,eAAL,CAAqBlS,aAArC;QACAiS,OAAO,CAACpU,KAAR,CAAcsG,SAAd,GAA0B8N,OAAO,CAACrL,YAAR,GAAuB,IAAjD;MACH,CARqC,CAStC;MACA;;;MACAuD,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuB,MAAM;QACzB,KAAKgG,KAAL,CAAWpF,OAAX,CAAmB,CAAC4B,GAAD,EAAMD,KAAN,KAAiBC,GAAG,CAACzK,QAAJ,GAAewK,KAAK,KAAKiF,aAA7D;;QACA,IAAI,CAACE,UAAL,EAAiB;UACb,KAAKjB,mBAAL,CAAyBtK,IAAzB,CAA8BqL,aAA9B,EADa,CAEb;UACA;;UACA,KAAKK,eAAL,CAAqBlS,aAArB,CAAmCnC,KAAnC,CAAyCsG,SAAzC,GAAqD,EAArD;QACH;MACJ,CARD;IASH,CA1BmB,CA2BpB;;;IACA,KAAKkM,KAAL,CAAWpF,OAAX,CAAmB,CAAC4B,GAAD,EAAMD,KAAN,KAAgB;MAC/BC,GAAG,CAAC3K,QAAJ,GAAe0K,KAAK,GAAGiF,aAAvB,CAD+B,CAE/B;MACA;;MACA,IAAI,KAAKlJ,cAAL,IAAuB,IAAvB,IAA+BkE,GAAG,CAAC3K,QAAJ,IAAgB,CAA/C,IAAoD,CAAC2K,GAAG,CAAC1K,MAA7D,EAAqE;QACjE0K,GAAG,CAAC1K,MAAJ,GAAa0P,aAAa,GAAG,KAAKlJ,cAAlC;MACH;IACJ,CAPD;;IAQA,IAAI,KAAKA,cAAL,KAAwBkJ,aAA5B,EAA2C;MACvC,KAAKlJ,cAAL,GAAsBkJ,aAAtB;MACA,KAAKtB,oBAAL,GAA4B,IAA5B;;MACA,KAAKvI,kBAAL,CAAwB9B,YAAxB;IACH;EACJ;;EACDsD,kBAAkB,GAAG;IACjB,KAAK2I,yBAAL;;IACA,KAAKC,qBAAL,GAFiB,CAGjB;IACA;;;IACA,KAAK3B,iBAAL,GAAyB,KAAKJ,KAAL,CAAW1N,OAAX,CAAmB9C,SAAnB,CAA6B,MAAM;MACxD,MAAMgS,aAAa,GAAG,KAAKC,cAAL,CAAoB,KAAKxB,cAAzB,CAAtB,CADwD,CAExD;MACA;;;MACA,IAAIuB,aAAa,KAAK,KAAKlJ,cAA3B,EAA2C;QACvC,MAAM0J,IAAI,GAAG,KAAKhC,KAAL,CAAWvD,OAAX,EAAb;;QACA,IAAIwF,WAAJ;;QACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,IAAI,CAAC1G,MAAzB,EAAiC4G,CAAC,EAAlC,EAAsC;UAClC,IAAIF,IAAI,CAACE,CAAD,CAAJ,CAAQnQ,QAAZ,EAAsB;YAClB;YACA;YACA;YACA,KAAKkO,cAAL,GAAsB,KAAK3H,cAAL,GAAsB4J,CAA5C;YACA,KAAKhC,oBAAL,GAA4B,IAA5B;YACA+B,WAAW,GAAGD,IAAI,CAACE,CAAD,CAAlB;YACA;UACH;QACJ,CAbsC,CAcvC;QACA;QACA;;;QACA,IAAI,CAACD,WAAD,IAAgBD,IAAI,CAACR,aAAD,CAAxB,EAAyC;UACrC1H,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuB,MAAM;YACzBgI,IAAI,CAACR,aAAD,CAAJ,CAAoBzP,QAApB,GAA+B,IAA/B;YACA,KAAK6O,iBAAL,CAAuBzK,IAAvB,CAA4B,KAAKwL,kBAAL,CAAwBH,aAAxB,CAA5B;UACH,CAHD;QAIH;MACJ;;MACD,KAAK7J,kBAAL,CAAwB9B,YAAxB;IACH,CA7BwB,CAAzB;EA8BH;EACD;;;EACAiM,yBAAyB,GAAG;IACxB;IACA;IACA;IACA,KAAKK,QAAL,CAAc7P,OAAd,CAAsB/C,IAAtB,CAA2BhD,SAAS,CAAC,KAAK4V,QAAN,CAApC,EAAqD3S,SAArD,CAAgEwS,IAAD,IAAU;MACrE,KAAKhC,KAAL,CAAWoC,KAAX,CAAiBJ,IAAI,CAACpV,MAAL,CAAY4P,GAAG,IAAI;QAChC,OAAOA,GAAG,CAAC/K,gBAAJ,KAAyB,IAAzB,IAAiC,CAAC+K,GAAG,CAAC/K,gBAA7C;MACH,CAFgB,CAAjB;;MAGA,KAAKuO,KAAL,CAAWqC,eAAX;IACH,CALD;EAMH;;EACD5P,WAAW,GAAG;IACV,KAAKuN,KAAL,CAAWsC,OAAX;;IACA,KAAKlC,iBAAL,CAAuBrL,WAAvB;;IACA,KAAKsL,qBAAL,CAA2BtL,WAA3B;EACH;EACD;;;EACAwN,aAAa,GAAG;IACZ,IAAI,KAAKC,UAAT,EAAqB;MACjB,KAAKA,UAAL,CAAgBhJ,yBAAhB;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACID,gBAAgB,GAAG;IACf,IAAI,KAAKiJ,UAAT,EAAqB;MACjB,KAAKA,UAAL,CAAgBjJ,gBAAhB;IACH;EACJ;EACD;AACJ;AACA;AACA;;;EACIkJ,QAAQ,CAAClG,KAAD,EAAQ;IACZ,MAAMmG,MAAM,GAAG,KAAKF,UAApB;;IACA,IAAIE,MAAJ,EAAY;MACRA,MAAM,CAAC7G,UAAP,GAAoBU,KAApB;IACH;EACJ;;EACDoG,aAAa,CAACpG,KAAD,EAAQ;IACjB,KAAK2D,oBAAL,GAA4B3D,KAA5B;IACA,KAAKmE,WAAL,CAAiBvK,IAAjB,CAAsB,KAAKwL,kBAAL,CAAwBpF,KAAxB,CAAtB;EACH;;EACDoF,kBAAkB,CAACpF,KAAD,EAAQ;IACtB,MAAMrG,KAAK,GAAG,IAAI0J,iBAAJ,EAAd;IACA1J,KAAK,CAACqG,KAAN,GAAcA,KAAd;;IACA,IAAI,KAAKyD,KAAL,IAAc,KAAKA,KAAL,CAAW1E,MAA7B,EAAqC;MACjCpF,KAAK,CAACsG,GAAN,GAAY,KAAKwD,KAAL,CAAWvD,OAAX,GAAqBF,KAArB,CAAZ;IACH;;IACD,OAAOrG,KAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACI6L,qBAAqB,GAAG;IACpB,IAAI,KAAK1B,qBAAT,EAAgC;MAC5B,KAAKA,qBAAL,CAA2BtL,WAA3B;IACH;;IACD,KAAKsL,qBAAL,GAA6BpT,KAAK,CAAC,GAAG,KAAK+S,KAAL,CAAW4C,GAAX,CAAepG,GAAG,IAAIA,GAAG,CAAC5K,aAA1B,CAAJ,CAAL,CAAmDpC,SAAnD,CAA6D,MAAM,KAAKmI,kBAAL,CAAwB9B,YAAxB,EAAnE,CAA7B;EACH;EACD;;;EACA4L,cAAc,CAAClF,KAAD,EAAQ;IAClB;IACA;IACA;IACA,OAAOtC,IAAI,CAACE,GAAL,CAAS,KAAK6F,KAAL,CAAW1E,MAAX,GAAoB,CAA7B,EAAgCrB,IAAI,CAACC,GAAL,CAASqC,KAAK,IAAI,CAAlB,EAAqB,CAArB,CAAhC,CAAP;EACH;EACD;;;EACAsG,cAAc,CAACX,CAAD,EAAI;IACd,OAAQ,iBAAgB,KAAKrB,QAAS,IAAGqB,CAAE,EAA3C;EACH;EACD;;;EACAY,gBAAgB,CAACZ,CAAD,EAAI;IAChB,OAAQ,mBAAkB,KAAKrB,QAAS,IAAGqB,CAAE,EAA7C;EACH;EACD;AACJ;AACA;AACA;;;EACIa,wBAAwB,CAACC,SAAD,EAAY;IAChC,IAAI,CAAC,KAAK1C,cAAN,IAAwB,CAAC,KAAKH,qBAAlC,EAAyD;MACrD;IACH;;IACD,MAAMyB,OAAO,GAAG,KAAKC,eAAL,CAAqBlS,aAArC;IACAiS,OAAO,CAACpU,KAAR,CAAc2N,MAAd,GAAuB,KAAKgF,qBAAL,GAA6B,IAApD,CALgC,CAMhC;IACA;;IACA,IAAI,KAAK0B,eAAL,CAAqBlS,aAArB,CAAmCsT,YAAvC,EAAqD;MACjDrB,OAAO,CAACpU,KAAR,CAAc2N,MAAd,GAAuB6H,SAAS,GAAG,IAAnC;IACH;EACJ;EACD;;;EACAE,2BAA2B,GAAG;IAC1B,MAAMtB,OAAO,GAAG,KAAKC,eAAL,CAAqBlS,aAArC;IACA,KAAKwQ,qBAAL,GAA6ByB,OAAO,CAACrL,YAArC;IACAqL,OAAO,CAACpU,KAAR,CAAc2N,MAAd,GAAuB,EAAvB;IACA,KAAKwF,aAAL,CAAmBxK,IAAnB;EACH;EACD;;;EACAgN,YAAY,CAAC3G,GAAD,EAAM4G,SAAN,EAAiB7G,KAAjB,EAAwB;IAChC,IAAI,CAACC,GAAG,CAACE,QAAT,EAAmB;MACf,KAAK9D,aAAL,GAAqBwK,SAAS,CAACvH,UAAV,GAAuBU,KAA5C;IACH;EACJ;EACD;;;EACA8G,YAAY,CAAC7G,GAAD,EAAMD,KAAN,EAAa;IACrB,IAAIC,GAAG,CAACE,QAAR,EAAkB;MACd,OAAO,IAAP;IACH;;IACD,MAAM4G,WAAW,GAAG,KAAKpD,oBAAL,IAA6B,KAAKtH,aAAtD;IACA,OAAO2D,KAAK,KAAK+G,WAAV,GAAwB,CAAxB,GAA4B,CAAC,CAApC;EACH;EACD;;;EACAC,gBAAgB,CAACC,WAAD,EAAcjH,KAAd,EAAqB;IACjC;IACA;IACA;IACA;IACA,IAAIiH,WAAW,IAAIA,WAAW,KAAK,OAA/B,IAA0CA,WAAW,KAAK,OAA9D,EAAuE;MACnE,KAAKhB,UAAL,CAAgB3G,UAAhB,GAA6BU,KAA7B;IACH;EACJ;;AAnUgD;;AAqUrDuD,gBAAgB,CAAChQ,IAAjB;EAAA,iBAA6GgQ,gBAA7G,EAp7C4FrV,EAo7C5F,mBAA+IA,EAAE,CAACsF,UAAlJ,GAp7C4FtF,EAo7C5F,mBAAyKA,EAAE,CAACiM,iBAA5K,GAp7C4FjM,EAo7C5F,mBAA0MsM,eAA1M,MAp7C4FtM,EAo7C5F,mBAAsP4B,qBAAtP;AAAA;;AACAyT,gBAAgB,CAAC7P,IAAjB,kBAr7C4FxF,EAq7C5F;EAAA,MAAiGqV,gBAAjG;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA,WAr7C4FrV,EAq7C5F;AAAA;;AACA;EAAA,mDAt7C4FA,EAs7C5F,mBAA2FqV,gBAA3F,EAAyH,CAAC;IAC9G5P,IAAI,EAAEvF;EADwG,CAAD,CAAzH,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAEuF,IAAI,EAAEzF,EAAE,CAACsF;IAAX,CAAD,EAA0B;MAAEG,IAAI,EAAEzF,EAAE,CAACiM;IAAX,CAA1B,EAA0D;MAAExG,IAAI,EAAEI,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACvHL,IAAI,EAAEtF,MADiH;QAEvHuF,IAAI,EAAE,CAAC4G,eAAD;MAFiH,CAAD,EAGvH;QACC7G,IAAI,EAAErF;MADP,CAHuH;IAA/B,CAA1D,EAK3B;MAAEqF,IAAI,EAAEI,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAClCL,IAAI,EAAErF;MAD4B,CAAD,EAElC;QACCqF,IAAI,EAAEtF,MADP;QAECuF,IAAI,EAAE,CAAC9D,qBAAD;MAFP,CAFkC;IAA/B,CAL2B,CAAP;EAUlB,CAZxB,EAY0C;IAAEyU,aAAa,EAAE,CAAC;MAC5C5Q,IAAI,EAAE9E;IADsC,CAAD,CAAjB;IAE1BwN,aAAa,EAAE,CAAC;MAChB1I,IAAI,EAAE9E;IADU,CAAD,CAFW;IAI1BmV,cAAc,EAAE,CAAC;MACjBrQ,IAAI,EAAE9E;IADW,CAAD,CAJU;IAM1BqK,iBAAiB,EAAE,CAAC;MACpBvF,IAAI,EAAE9E;IADc,CAAD,CANO;IAQ1B2V,eAAe,EAAE,CAAC;MAClB7Q,IAAI,EAAE9E;IADY,CAAD,CARS;IAU1BuN,iBAAiB,EAAE,CAAC;MACpBzI,IAAI,EAAE9E;IADc,CAAD,CAVO;IAY1ByJ,eAAe,EAAE,CAAC;MAClB3E,IAAI,EAAE9E;IADY,CAAD,CAZS;IAc1B+V,eAAe,EAAE,CAAC;MAClBjR,IAAI,EAAE9E;IADY,CAAD,CAdS;IAgB1BqV,mBAAmB,EAAE,CAAC;MACtBvQ,IAAI,EAAE3E;IADgB,CAAD,CAhBK;IAkB1BmV,WAAW,EAAE,CAAC;MACdxQ,IAAI,EAAE3E;IADQ,CAAD,CAlBa;IAoB1BoV,aAAa,EAAE,CAAC;MAChBzQ,IAAI,EAAE3E;IADU,CAAD,CApBW;IAsB1BqV,iBAAiB,EAAE,CAAC;MACpB1Q,IAAI,EAAE3E;IADc,CAAD;EAtBO,CAZ1C;AAAA;AAqCA;AACA;AACA;AACA;AACA;;;AACA,MAAMkY,WAAN,SAA0B3D,gBAA1B,CAA2C;EACvChR,WAAW,CAAC6H,UAAD,EAAavB,iBAAb,EAAgC2K,aAAhC,EAA+Cb,aAA/C,EAA8D;IACrE,MAAMvI,UAAN,EAAkBvB,iBAAlB,EAAqC2K,aAArC,EAAoDb,aAApD;EACH;;AAHsC;;AAK3CuE,WAAW,CAAC3T,IAAZ;EAAA,iBAAwG2T,WAAxG,EAr+C4FhZ,EAq+C5F,mBAAqIA,EAAE,CAACsF,UAAxI,GAr+C4FtF,EAq+C5F,mBAA+JA,EAAE,CAACiM,iBAAlK,GAr+C4FjM,EAq+C5F,mBAAgMsM,eAAhM,MAr+C4FtM,EAq+C5F,mBAA4O4B,qBAA5O;AAAA;;AACAoX,WAAW,CAAC3Q,IAAZ,kBAt+C4FrI,EAs+C5F;EAAA,MAA4FgZ,WAA5F;EAAA;EAAA;IAAA;MAt+C4FhZ,EAs+C5F,0BAKwD8G,MALxD;IAAA;;IAAA;MAAA;;MAt+C4F9G,EAs+C5F,qBAt+C4FA,EAs+C5F;IAAA;EAAA;EAAA;IAAA;MAt+C4FA,EAs+C5F;MAt+C4FA,EAs+C5F;IAAA;;IAAA;MAAA;;MAt+C4FA,EAs+C5F,qBAt+C4FA,EAs+C5F;MAt+C4FA,EAs+C5F,qBAt+C4FA,EAs+C5F;IAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAt+C4FA,EAs+C5F;IAAA;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WAt+C4FA,EAs+C5F,oBAAoY,CAC5X;IACIkG,OAAO,EAAEW,aADb;IAEIV,WAAW,EAAE6S;EAFjB,CAD4X,CAApY,GAt+C4FhZ,EAs+C5F;EAAA;EAAA;EAAA;EAAA;IAAA;MAt+C4FA,EA2+CkR,0CAL9W;MAt+C4FA,EA2+Cge;QAAA,OAAiB,yBAAjB;MAAA;QAAA;MAAA,EAL5jB;MAt+C4FA,EA2+C6kB,2DALzqB;MAt+C4FA,EA2+Cy5D,eALr/D;MAt+C4FA,EA2+C86D,+BAL1gE;MAt+C4FA,EA2+CujE,6EALnpE;MAt+C4FA,EA2+Cm2F,eAL/7F;IAAA;;IAAA;MAt+C4FA,EA2+C6T,oIALzZ;MAt+C4FA,EA2+CiuB,aAL7zB;MAt+C4FA,EA2+CiuB,iCAL7zB;MAt+C4FA,EA2+Cw9D,aALpjE;MAt+C4FA,EA2+Cw9D,gFALpjE;MAt+C4FA,EA2+CwnE,aALptE;MAt+C4FA,EA2+CwnE,iCALptE;IAAA;EAAA;EAAA,eAK68IH,IAAI,CAACoZ,OALl9I,EAK6iJpZ,IAAI,CAACqZ,OALljJ,EAK4qJrZ,IAAI,CAACsZ,IALjrJ,EAKkxJ3Z,EAAE,CAACG,eALrxJ,EAKq7JwB,EAAE,CAAC2T,SALx7J,EAKyrK3V,EAAE,CAACia,eAL5rK,EAKs2K5M,kBALt2K,EAK+8KhC,UAL/8K,EAKkhLqK,YALlhL;EAAA;EAAA;AAAA;;AAMA;EAAA,mDA5+C4F7U,EA4+C5F,mBAA2FgZ,WAA3F,EAAoH,CAAC;IACzGvT,IAAI,EAAEnF,SADmG;IAEzGoF,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,eAAZ;MAA6BgD,QAAQ,EAAE,aAAvC;MAAsDF,aAAa,EAAEjI,iBAAiB,CAACkI,IAAvF;MAA6FH,eAAe,EAAEhI,uBAAuB,CAACiI,OAAtI;MAA+IF,MAAM,EAAE,CAAC,OAAD,EAAU,eAAV,CAAvJ;MAAmLlC,SAAS,EAAE,CACzL;QACIF,OAAO,EAAEW,aADb;QAEIV,WAAW,EAAE6S;MAFjB,CADyL,CAA9L;MAKIpT,IAAI,EAAE;QACL,SAAS,eADJ;QAEL,wCAAwC,eAFnC;QAGL,yCAAyC;MAHpC,CALV;MASIK,QAAQ,EAAE,2lFATd;MAS2mFmG,MAAM,EAAE,CAAC,68CAAD;IATnnF,CAAD;EAFmG,CAAD,CAApH,EAY4B,YAAY;IAAE,OAAO,CAAC;MAAE3G,IAAI,EAAEzF,EAAE,CAACsF;IAAX,CAAD,EAA0B;MAAEG,IAAI,EAAEzF,EAAE,CAACiM;IAAX,CAA1B,EAA0D;MAAExG,IAAI,EAAEI,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACvHL,IAAI,EAAEtF,MADiH;QAEvHuF,IAAI,EAAE,CAAC4G,eAAD;MAFiH,CAAD,EAGvH;QACC7G,IAAI,EAAErF;MADP,CAHuH;IAA/B,CAA1D,EAK3B;MAAEqF,IAAI,EAAEI,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAClCL,IAAI,EAAErF;MAD4B,CAAD,EAElC;QACCqF,IAAI,EAAEtF,MADP;QAECuF,IAAI,EAAE,CAAC9D,qBAAD;MAFP,CAFkC;IAA/B,CAL2B,CAAP;EAUlB,CAtBxB,EAsB0C;IAAE8V,QAAQ,EAAE,CAAC;MACvCjS,IAAI,EAAE1E,eADiC;MAEvC2E,IAAI,EAAE,CAACoB,MAAD,EAAS;QAAEmO,WAAW,EAAE;MAAf,CAAT;IAFiC,CAAD,CAAZ;IAG1BmC,eAAe,EAAE,CAAC;MAClB3R,IAAI,EAAE/E,SADY;MAElBgF,IAAI,EAAE,CAAC,gBAAD;IAFY,CAAD,CAHS;IAM1BqS,UAAU,EAAE,CAAC;MACbtS,IAAI,EAAE/E,SADO;MAEbgF,IAAI,EAAE,CAAC,WAAD;IAFO,CAAD;EANc,CAtB1C;AAAA;AAiCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAI2T,YAAY,GAAG,CAAnB;AACA;AACA;AACA;AACA;;AACA,MAAMC,cAAN,SAA6BrM,qBAA7B,CAAmD;EAC/C5I,WAAW,CAAC6H,UAAD,EAAahB,GAAb,EAAkBqJ,MAAlB,EAA0B5J,iBAA1B,EAA6C2J,aAA7C,EAA4DE,QAA5D,EAAsEC,aAAtE,EAAqF;IAC5F,MAAMvI,UAAN,EAAkBvB,iBAAlB,EAAqC2J,aAArC,EAAoDpJ,GAApD,EAAyDqJ,MAAzD,EAAiEC,QAAjE,EAA2EC,aAA3E;IACA,KAAKC,cAAL,GAAsB,KAAtB;IACA;;IACA,KAAK6E,KAAL,GAAa,SAAb;EACH;EACD;;;EACmB,IAAf7C,eAAe,GAAG;IAClB,OAAO,KAAKC,gBAAZ;EACH;;EACkB,IAAfD,eAAe,CAACjP,KAAD,EAAQ;IACvB,MAAMmP,SAAS,GAAG,KAAKtS,WAAL,CAAiBY,aAAjB,CAA+B0R,SAAjD;IACAA,SAAS,CAACC,MAAV,CAAkB,kBAAiB,KAAKH,eAAgB,EAAxD;;IACA,IAAIjP,KAAJ,EAAW;MACPmP,SAAS,CAACE,GAAV,CAAe,kBAAiBrP,KAAM,EAAtC;IACH;;IACD,KAAKkP,gBAAL,GAAwBlP,KAAxB;EACH;EACD;;;EACiB,IAAbkN,aAAa,GAAG;IAChB,OAAO,KAAKD,cAAZ;EACH;;EACgB,IAAbC,aAAa,CAAClN,KAAD,EAAQ;IACrB,KAAKiN,cAAL,GAAsBxR,qBAAqB,CAACuE,KAAD,CAA3C;EACH;;EACD4J,aAAa,GAAG,CACZ;EACH;;EACD3C,kBAAkB,GAAG;IACjB;IACA;IACA,KAAKM,MAAL,CAAYnH,OAAZ,CAAoB/C,IAApB,CAAyBhD,SAAS,CAAC,IAAD,CAAlC,EAA0CE,SAAS,CAAC,KAAKuL,UAAN,CAAnD,EAAsExI,SAAtE,CAAgF,MAAM;MAClF,KAAKyU,gBAAL;IACH,CAFD;;IAGA,MAAM9K,kBAAN;EACH;EACD;;;EACA8K,gBAAgB,GAAG;IACf,IAAI,CAAC,KAAKxK,MAAV,EAAkB;MACd;IACH;;IACD,MAAMyK,KAAK,GAAG,KAAKzK,MAAL,CAAYgD,OAAZ,EAAd;;IACA,KAAK,IAAIyF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGgC,KAAK,CAAC5I,MAA1B,EAAkC4G,CAAC,EAAnC,EAAuC;MACnC,IAAIgC,KAAK,CAAChC,CAAD,CAAL,CAASiC,MAAb,EAAqB;QACjB,KAAKvL,aAAL,GAAqBsJ,CAArB;;QACA,KAAKvK,kBAAL,CAAwB9B,YAAxB;;QACA,IAAI,KAAKuO,QAAT,EAAmB;UACf,KAAKA,QAAL,CAAcC,YAAd,GAA6BH,KAAK,CAAChC,CAAD,CAAL,CAASoC,EAAtC;QACH;;QACD;MACH;IACJ,CAdc,CAef;;;IACA,KAAK1L,aAAL,GAAqB,CAAC,CAAtB;;IACA,KAAK2F,OAAL,CAAa1O,IAAb;EACH;;EACD0U,QAAQ,GAAG;IACP,OAAO,KAAKH,QAAL,GAAgB,SAAhB,GAA4B,KAAKrV,WAAL,CAAiBY,aAAjB,CAA+B6U,YAA/B,CAA4C,MAA5C,CAAnC;EACH;;AA3D8C;;AA6DnDT,cAAc,CAACjU,IAAf;EAAA,iBAA2GiU,cAA3G,EAvlD4FtZ,EAulD5F,mBAA2IA,EAAE,CAACsF,UAA9I,GAvlD4FtF,EAulD5F,mBAAqK4C,EAAE,CAACoJ,cAAxK,MAvlD4FhM,EAulD5F,mBAAmNA,EAAE,CAACuF,MAAtN,GAvlD4FvF,EAulD5F,mBAAyOA,EAAE,CAACiM,iBAA5O,GAvlD4FjM,EAulD5F,mBAA0QoD,IAAI,CAAC+Q,aAA/Q,GAvlD4FnU,EAulD5F,mBAAySqD,EAAE,CAAC+Q,QAA5S,GAvlD4FpU,EAulD5F,mBAAiU4B,qBAAjU;AAAA;;AACA0X,cAAc,CAAC9T,IAAf,kBAxlD4FxF,EAwlD5F;EAAA,MAA+FsZ,cAA/F;EAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA,WAxlD4FtZ,EAwlD5F;AAAA;;AACA;EAAA,mDAzlD4FA,EAylD5F,mBAA2FsZ,cAA3F,EAAuH,CAAC;IAC5G7T,IAAI,EAAEvF;EADsG,CAAD,CAAvH,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAEuF,IAAI,EAAEzF,EAAE,CAACsF;IAAX,CAAD,EAA0B;MAAEG,IAAI,EAAE7C,EAAE,CAACoJ,cAAX;MAA2BlG,UAAU,EAAE,CAAC;QAC/FL,IAAI,EAAErF;MADyF,CAAD;IAAvC,CAA1B,EAE3B;MAAEqF,IAAI,EAAEzF,EAAE,CAACuF;IAAX,CAF2B,EAEN;MAAEE,IAAI,EAAEzF,EAAE,CAACiM;IAAX,CAFM,EAE0B;MAAExG,IAAI,EAAErC,IAAI,CAAC+Q;IAAb,CAF1B,EAEwD;MAAE1O,IAAI,EAAEpC,EAAE,CAAC+Q;IAAX,CAFxD,EAE+E;MAAE3O,IAAI,EAAEI,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC5IL,IAAI,EAAErF;MADsI,CAAD,EAE5I;QACCqF,IAAI,EAAEtF,MADP;QAECuF,IAAI,EAAE,CAAC9D,qBAAD;MAFP,CAF4I;IAA/B,CAF/E,CAAP;EAOlB,CATxB,EAS0C;IAAE8U,eAAe,EAAE,CAAC;MAC9CjR,IAAI,EAAE9E;IADwC,CAAD,CAAnB;IAE1BgU,aAAa,EAAE,CAAC;MAChBlP,IAAI,EAAE9E;IADU,CAAD,CAFW;IAI1B4Y,KAAK,EAAE,CAAC;MACR9T,IAAI,EAAE9E;IADE,CAAD,CAJmB;IAM1BgZ,QAAQ,EAAE,CAAC;MACXlU,IAAI,EAAE9E;IADK,CAAD;EANgB,CAT1C;AAAA;AAkBA;AACA;AACA;AACA;;;AACA,MAAMqZ,SAAN,SAAwBV,cAAxB,CAAuC;EACnCjV,WAAW,CAAC6H,UAAD,EAAahB,GAAb,EAAkBqJ,MAAlB,EAA0B5J,iBAA1B,EAA6C2J,aAA7C,EAA4DE,QAA5D,EAAsEC,aAAtE,EAAqF;IAC5F,MAAMvI,UAAN,EAAkBhB,GAAlB,EAAuBqJ,MAAvB,EAA+B5J,iBAA/B,EAAkD2J,aAAlD,EAAiEE,QAAjE,EAA2EC,aAA3E;EACH;;AAHkC;;AAKvCuF,SAAS,CAAC3U,IAAV;EAAA,iBAAsG2U,SAAtG,EApnD4Fha,EAonD5F,mBAAiIA,EAAE,CAACsF,UAApI,GApnD4FtF,EAonD5F,mBAA2J4C,EAAE,CAACoJ,cAA9J,MApnD4FhM,EAonD5F,mBAAyMA,EAAE,CAACuF,MAA5M,GApnD4FvF,EAonD5F,mBAA+NA,EAAE,CAACiM,iBAAlO,GApnD4FjM,EAonD5F,mBAAgQoD,IAAI,CAAC+Q,aAArQ,GApnD4FnU,EAonD5F,mBAA+RqD,EAAE,CAAC+Q,QAAlS,GApnD4FpU,EAonD5F,mBAAuT4B,qBAAvT;AAAA;;AACAoY,SAAS,CAAC3R,IAAV,kBArnD4FrI,EAqnD5F;EAAA,MAA0Fga,SAA1F;EAAA;EAAA;IAAA;MArnD4Fha,EAqnD5F,0BAA6nBia,UAA7nB;IAAA;;IAAA;MAAA;;MArnD4Fja,EAqnD5F,qBArnD4FA,EAqnD5F;IAAA;EAAA;EAAA;IAAA;MArnD4FA,EAqnD5F,aAAouBoE,SAApuB;MArnD4FpE,EAqnD5F;MArnD4FA,EAqnD5F;MArnD4FA,EAqnD5F;MArnD4FA,EAqnD5F;MArnD4FA,EAqnD5F;IAAA;;IAAA;MAAA;;MArnD4FA,EAqnD5F,qBArnD4FA,EAqnD5F;MArnD4FA,EAqnD5F,qBArnD4FA,EAqnD5F;MArnD4FA,EAqnD5F,qBArnD4FA,EAqnD5F;MArnD4FA,EAqnD5F,qBArnD4FA,EAqnD5F;MArnD4FA,EAqnD5F,qBArnD4FA,EAqnD5F;MArnD4FA,EAqnD5F,qBArnD4FA,EAqnD5F;IAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MArnD4FA,EAqnD5F;MArnD4FA,EAqnD5F;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA;EAAA,WArnD4FA,EAqnD5F;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MArnD4FA,EAqnD5F;MArnD4FA,EAqnDoyC,kCAAh4C;MArnD4FA,EAqnDyrD;QAAA,OAAU,0BAAsB,QAAtB,CAAV;MAAA;QAAA,OAAgE,0BAAsB,QAAtB,SAAhE;MAAA;QAAA,OAA6H,mBAA7H;MAAA,EAArxD;MArnD4FA,EAqnD40D,uBAAx6D;MArnD4FA,EAqnDq4D,eAAj+D;MArnD4FA,EAqnDk5D,+BAA9+D;MArnD4FA,EAqnD08D;QAAA,OAAY,0BAAZ;MAAA,EAAtiE;MArnD4FA,EAqnDm/D,+BAA/kE;MArnD4FA,EAqnDonE;QAAA,OAAsB,uBAAtB;MAAA,EAAhtE;MArnD4FA,EAqnDsqE,+BAAlwE;MArnD4FA,EAqnDytE,gBAArzE;MArnD4FA,EAqnDwvE,eAAp1E;MArnD4FA,EAqnDowE,6BAAh2E;MArnD4FA,EAqnDmyE,iBAA/3E;MArnD4FA,EAqnDqzE,oCAAj5E;MArnD4FA,EAqnDksF;QAAA,OAAc,0BAAsB,OAAtB,SAAd;MAAA;QAAA,OAAuE,0BAAsB,OAAtB,CAAvE;MAAA;QAAA,OAA2H,mBAA3H;MAAA,EAA9xF;MArnD4FA,EAqnDm1F,wBAA/6F;MArnD4FA,EAqnD44F,eAAx+F;IAAA;;IAAA;MArnD4FA,EAqnD6jD,4EAAzpD;MArnD4FA,EAqnDy/C,6HAArlD;MArnD4FA,EAqnDyhE,aAArnE;MArnD4FA,EAqnDyhE,gFAArnE;MArnD4FA,EAqnDkjF,aAA9oF;MArnD4FA,EAqnDkjF,2EAA9oF;MArnD4FA,EAqnD++E,2HAA3kF;IAAA;EAAA;EAAA,eAA40LmB,EAAE,CAAC2T,SAA/0L,EAAglMxV,EAAE,CAACyV,iBAAnlM,EAAwxM3Q,SAAxxM;EAAA;EAAA;AAAA;;AACA;EAAA,mDAtnD4FpE,EAsnD5F,mBAA2Fga,SAA3F,EAAkH,CAAC;IACvGvU,IAAI,EAAEnF,SADiG;IAEvGoF,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,mBAAZ;MAAiCgD,QAAQ,EAAE,yBAA3C;MAAsEL,MAAM,EAAE,CAAC,OAAD,CAA9E;MAAyF1C,IAAI,EAAE;QAC1F,eAAe,YAD2E;QAE1F,SAAS,gCAFiF;QAG1F,sDAAsD,yBAHoC;QAI1F,8BAA8B,gCAJ4D;QAK1F,uBAAuB,wCALmE;QAM1F,sBAAsB,oBANoE;QAO1F,oBAAoB;MAPsE,CAA/F;MAQI6C,aAAa,EAAEjI,iBAAiB,CAACkI,IARrC;MAQ2CH,eAAe,EAAEhI,uBAAuB,CAACiI,OARpF;MAQ6FvC,QAAQ,EAAE,qnDARvG;MAQ8tDmG,MAAM,EAAE,CAAC,gyFAAD;IARtuD,CAAD;EAFiG,CAAD,CAAlH,EAW4B,YAAY;IAAE,OAAO,CAAC;MAAE3G,IAAI,EAAEzF,EAAE,CAACsF;IAAX,CAAD,EAA0B;MAAEG,IAAI,EAAE7C,EAAE,CAACoJ,cAAX;MAA2BlG,UAAU,EAAE,CAAC;QAC/FL,IAAI,EAAErF;MADyF,CAAD;IAAvC,CAA1B,EAE3B;MAAEqF,IAAI,EAAEzF,EAAE,CAACuF;IAAX,CAF2B,EAEN;MAAEE,IAAI,EAAEzF,EAAE,CAACiM;IAAX,CAFM,EAE0B;MAAExG,IAAI,EAAErC,IAAI,CAAC+Q;IAAb,CAF1B,EAEwD;MAAE1O,IAAI,EAAEpC,EAAE,CAAC+Q;IAAX,CAFxD,EAE+E;MAAE3O,IAAI,EAAEI,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC5IL,IAAI,EAAErF;MADsI,CAAD,EAE5I;QACCqF,IAAI,EAAEtF,MADP;QAECuF,IAAI,EAAE,CAAC9D,qBAAD;MAFP,CAF4I;IAA/B,CAF/E,CAAP;EAOlB,CAlBxB,EAkB0C;IAAEoN,MAAM,EAAE,CAAC;MACrCvJ,IAAI,EAAE1E,eAD+B;MAErC2E,IAAI,EAAE,CAAC9E,UAAU,CAAC,MAAMqZ,UAAP,CAAX,EAA+B;QAAEhF,WAAW,EAAE;MAAf,CAA/B;IAF+B,CAAD,CAAV;IAG1BnB,OAAO,EAAE,CAAC;MACVrO,IAAI,EAAE/E,SADI;MAEVgF,IAAI,EAAE,CAACtB,SAAD,EAAY;QAAEyE,MAAM,EAAE;MAAV,CAAZ;IAFI,CAAD,CAHiB;IAM1BuJ,iBAAiB,EAAE,CAAC;MACpB3M,IAAI,EAAE/E,SADc;MAEpBgF,IAAI,EAAE,CAAC,kBAAD,EAAqB;QAAEmD,MAAM,EAAE;MAAV,CAArB;IAFc,CAAD,CANO;IAS1B4J,QAAQ,EAAE,CAAC;MACXhN,IAAI,EAAE/E,SADK;MAEXgF,IAAI,EAAE,CAAC,SAAD,EAAY;QAAEmD,MAAM,EAAE;MAAV,CAAZ;IAFK,CAAD,CATgB;IAY1B0K,aAAa,EAAE,CAAC;MAChB9N,IAAI,EAAE/E,SADU;MAEhBgF,IAAI,EAAE,CAAC,cAAD,EAAiB;QAAEmD,MAAM,EAAE;MAAV,CAAjB;IAFU,CAAD,CAZW;IAe1B4F,cAAc,EAAE,CAAC;MACjBhJ,IAAI,EAAE/E,SADW;MAEjBgF,IAAI,EAAE,CAAC,eAAD;IAFW,CAAD,CAfU;IAkB1B6I,kBAAkB,EAAE,CAAC;MACrB9I,IAAI,EAAE/E,SADe;MAErBgF,IAAI,EAAE,CAAC,mBAAD;IAFe,CAAD;EAlBM,CAlB1C;AAAA,K,CAwCA;;;AACA,MAAMwU,oBAAoB,GAAG3Y,aAAa,CAACD,kBAAkB,CAACF,aAAa,CAAC,MAAM,EAAP,CAAd,CAAnB,CAA1C;AAEA;;;AACA,MAAM+Y,eAAN,SAA8BD,oBAA9B,CAAmD;EAC/C7V,WAAW,CAAC+V,UAAD;EACX;EAAqBlO,UADV,EACsBmO,mBADtB,EAC2CnI,QAD3C,EACqDoI,aADrD,EACoE7F,aADpE,EACmF;IAC1F;IACA,KAAK2F,UAAL,GAAkBA,UAAlB;IACA,KAAKlO,UAAL,GAAkBA,UAAlB;IACA,KAAKoO,aAAL,GAAqBA,aAArB;IACA;;IACA,KAAKC,SAAL,GAAiB,KAAjB;IACA;;IACA,KAAKV,EAAL,GAAW,gBAAeR,YAAY,EAAG,EAAzC;IACA,KAAKmB,YAAL,GAAoBH,mBAAmB,IAAI,EAA3C;IACA,KAAKnI,QAAL,GAAgBuI,QAAQ,CAACvI,QAAD,CAAR,IAAsB,CAAtC;;IACA,IAAIuC,aAAa,KAAK,gBAAtB,EAAwC;MACpC,KAAK+F,YAAL,CAAkBE,SAAlB,GAA8B;QAAEC,aAAa,EAAE,CAAjB;QAAoBC,YAAY,EAAE;MAAlC,CAA9B;IACH;EACJ;EACD;;;EACU,IAANlB,MAAM,GAAG;IACT,OAAO,KAAKa,SAAZ;EACH;;EACS,IAANb,MAAM,CAACjS,KAAD,EAAQ;IACd,MAAMoT,QAAQ,GAAG3X,qBAAqB,CAACuE,KAAD,CAAtC;;IACA,IAAIoT,QAAQ,KAAK,KAAKN,SAAtB,EAAiC;MAC7B,KAAKA,SAAL,GAAiBM,QAAjB;;MACA,KAAKT,UAAL,CAAgBZ,gBAAhB;IACH;EACJ;EACD;AACJ;AACA;AACA;;;EACsB,IAAdsB,cAAc,GAAG;IACjB,OAAQ,KAAK7I,QAAL,IACJ,KAAK0C,aADD,IAEJ,KAAKyF,UAAL,CAAgBzF,aAFZ,IAGJ,CAAC,CAAC,KAAK6F,YAAL,CAAkBvI,QAHxB;EAIH;EACD;;;EACAxF,KAAK,GAAG;IACJ,KAAKP,UAAL,CAAgBhH,aAAhB,CAA8BuH,KAA9B;EACH;;EACD6B,eAAe,GAAG;IACd,KAAKgM,aAAL,CAAmBS,OAAnB,CAA2B,KAAK7O,UAAhC;EACH;;EACDlE,WAAW,GAAG;IACV,KAAKsS,aAAL,CAAmBU,cAAnB,CAAkC,KAAK9O,UAAvC;EACH;;EACD+O,YAAY,GAAG;IACX;IACA;IACA,KAAKb,UAAL,CAAgBhJ,UAAhB,GAA6B,KAAKgJ,UAAL,CAAgBpL,MAAhB,CAAuBgD,OAAvB,GAAiCkJ,OAAjC,CAAyC,IAAzC,CAA7B;EACH;;EACDhK,cAAc,CAACzF,KAAD,EAAQ;IAClB,IAAI,KAAK2O,UAAL,CAAgBT,QAAhB,IAA4BlO,KAAK,CAAC0F,OAAN,KAAkB3N,KAAlD,EAAyD;MACrD,KAAK0I,UAAL,CAAgBhH,aAAhB,CAA8BiW,KAA9B;IACH;EACJ;;EACDC,gBAAgB,GAAG;IACf,OAAO,KAAKhB,UAAL,CAAgBT,QAAhB,GACD,KAAKS,UAAL,CAAgBT,QAAhB,EAA0BE,EADzB,GAED,KAAK3N,UAAL,CAAgBhH,aAAhB,CAA8B6U,YAA9B,CAA2C,eAA3C,CAFN;EAGH;;EACDsB,gBAAgB,GAAG;IACf,IAAI,KAAKjB,UAAL,CAAgBT,QAApB,EAA8B;MAC1B,OAAO,KAAKD,MAAL,GAAc,MAAd,GAAuB,OAA9B;IACH,CAFD,MAGK;MACD,OAAO,KAAKxN,UAAL,CAAgBhH,aAAhB,CAA8B6U,YAA9B,CAA2C,eAA3C,CAAP;IACH;EACJ;;EACDuB,eAAe,GAAG;IACd,OAAO,KAAK5B,MAAL,IAAe,CAAC,KAAKU,UAAL,CAAgBT,QAAhC,GAA2C,MAA3C,GAAoD,IAA3D;EACH;;EACDG,QAAQ,GAAG;IACP,OAAO,KAAKM,UAAL,CAAgBT,QAAhB,GAA2B,KAA3B,GAAmC,KAAKzN,UAAL,CAAgBhH,aAAhB,CAA8B6U,YAA9B,CAA2C,MAA3C,CAA1C;EACH;;EACDnB,YAAY,GAAG;IACX,IAAI,KAAKwB,UAAL,CAAgBT,QAApB,EAA8B;MAC1B,OAAO,KAAKY,SAAL,IAAkB,CAAC,KAAKtI,QAAxB,GAAmC,CAAnC,GAAuC,CAAC,CAA/C;IACH,CAFD,MAGK;MACD,OAAO,KAAKC,QAAZ;IACH;EACJ;;AApF8C;;AAsFnDiI,eAAe,CAAC9U,IAAhB;EAAA,iBAA4G8U,eAA5G,EAxvD4Fna,EAwvD5F,mBAA6IsZ,cAA7I,GAxvD4FtZ,EAwvD5F,mBAAwKA,EAAE,CAACsF,UAA3K,GAxvD4FtF,EAwvD5F,mBAAkMwB,yBAAlM,MAxvD4FxB,EAwvD5F,mBAAwP,UAAxP,GAxvD4FA,EAwvD5F,mBAAgSb,EAAE,CAACoc,YAAnS,GAxvD4Fvb,EAwvD5F,mBAA4T4B,qBAA5T;AAAA;;AACAuY,eAAe,CAAC3U,IAAhB,kBAzvD4FxF,EAyvD5F;EAAA,MAAgGma,eAAhG;EAAA;IAAA;IAAA;EAAA;EAAA,WAzvD4Fna,EAyvD5F;AAAA;;AACA;EAAA,mDA1vD4FA,EA0vD5F,mBAA2Fma,eAA3F,EAAwH,CAAC;IAC7G1U,IAAI,EAAEvF;EADuG,CAAD,CAAxH,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAEuF,IAAI,EAAE6T;IAAR,CAAD,EAA2B;MAAE7T,IAAI,EAAEzF,EAAE,CAACsF;IAAX,CAA3B,EAAoD;MAAEG,IAAI,EAAEI,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACjHL,IAAI,EAAErF;MAD2G,CAAD,EAEjH;QACCqF,IAAI,EAAEtF,MADP;QAECuF,IAAI,EAAE,CAAClE,yBAAD;MAFP,CAFiH;IAA/B,CAApD,EAK3B;MAAEiE,IAAI,EAAEI,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAClCL,IAAI,EAAExE,SAD4B;QAElCyE,IAAI,EAAE,CAAC,UAAD;MAF4B,CAAD;IAA/B,CAL2B,EAQ3B;MAAED,IAAI,EAAEtG,EAAE,CAACoc;IAAX,CAR2B,EAQA;MAAE9V,IAAI,EAAEI,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC7DL,IAAI,EAAErF;MADuD,CAAD,EAE7D;QACCqF,IAAI,EAAEtF,MADP;QAECuF,IAAI,EAAE,CAAC9D,qBAAD;MAFP,CAF6D;IAA/B,CARA,CAAP;EAalB,CAfxB,EAe0C;IAAE8X,MAAM,EAAE,CAAC;MACrCjU,IAAI,EAAE9E;IAD+B,CAAD,CAAV;IAE1BkZ,EAAE,EAAE,CAAC;MACLpU,IAAI,EAAE9E;IADD,CAAD;EAFsB,CAf1C;AAAA;AAoBA;AACA;AACA;;;AACA,MAAMsZ,UAAN,SAAyBE,eAAzB,CAAyC;EACrC9V,WAAW,CAACmX,SAAD,EAAYtP,UAAZ,EAAwBqI,MAAxB,EAAgCC,QAAhC,EAA0C6F,mBAA1C,EAA+DnI,QAA/D,EAAyEuJ,YAAzE,EAAuFhH,aAAvF,EAAsG;IAC7G,MAAM+G,SAAN,EAAiBtP,UAAjB,EAA6BmO,mBAA7B,EAAkDnI,QAAlD,EAA4DuJ,YAA5D,EAA0EhH,aAA1E;IACA,KAAKiH,cAAL,GAAsB,IAAIja,cAAJ,CAAmB,IAAnB,EAAyB8S,MAAzB,EAAiCrI,UAAjC,EAA6CsI,QAA7C,CAAtB;;IACA,KAAKkH,cAAL,CAAoBC,kBAApB,CAAuCzP,UAAU,CAAChH,aAAlD;EACH;;EACD8C,WAAW,GAAG;IACV,MAAMA,WAAN;;IACA,KAAK0T,cAAL,CAAoBE,oBAApB;EACH;;AAToC;;AAWzC3B,UAAU,CAAC5U,IAAX;EAAA,iBAAuG4U,UAAvG,EA5xD4Fja,EA4xD5F,mBAAmIga,SAAnI,GA5xD4Fha,EA4xD5F,mBAAyJA,EAAE,CAACsF,UAA5J,GA5xD4FtF,EA4xD5F,mBAAmLA,EAAE,CAACuF,MAAtL,GA5xD4FvF,EA4xD5F,mBAAyMqD,EAAE,CAAC+Q,QAA5M,GA5xD4FpU,EA4xD5F,mBAAiOwB,yBAAjO,MA5xD4FxB,EA4xD5F,mBAAuR,UAAvR,GA5xD4FA,EA4xD5F,mBAA+Tb,EAAE,CAACoc,YAAlU,GA5xD4Fvb,EA4xD5F,mBAA2V4B,qBAA3V;AAAA;;AACAqY,UAAU,CAACzU,IAAX,kBA7xD4FxF,EA6xD5F;EAAA,MAA2Fia,UAA3F;EAAA;EAAA;EAAA;EAAA;IAAA;MA7xD4Fja,EA6xD5F;QAAA,OAA2F,kBAA3F;MAAA;QAAA,OAA2F,0BAA3F;MAAA;IAAA;;IAAA;MA7xD4FA,EA6xD5F;MA7xD4FA,EA6xD5F;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WA7xD4FA,EA6xD5F;AAAA;;AACA;EAAA,mDA9xD4FA,EA8xD5F,mBAA2Fia,UAA3F,EAAmH,CAAC;IACxGxU,IAAI,EAAEvF,SADkG;IAExGwF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,8BADX;MAECgD,QAAQ,EAAE,YAFX;MAGCL,MAAM,EAAE,CAAC,UAAD,EAAa,eAAb,EAA8B,UAA9B,CAHT;MAIC1C,IAAI,EAAE;QACF,SAAS,kCADP;QAEF,wBAAwB,oBAFtB;QAGF,uBAAuB,mBAHrB;QAIF,wBAAwB,UAJtB;QAKF,wBAAwB,oBALtB;QAMF,aAAa,IANX;QAOF,mBAAmB,gBAPjB;QAQF,eAAe,YARb;QASF,4BAA4B,UAT1B;QAUF,gCAAgC,QAV9B;QAWF,WAAW,gBAXT;QAYF,aAAa;MAZX;IAJP,CAAD;EAFkG,CAAD,CAAnH,EAqB4B,YAAY;IAAE,OAAO,CAAC;MAAEH,IAAI,EAAEuU;IAAR,CAAD,EAAsB;MAAEvU,IAAI,EAAEzF,EAAE,CAACsF;IAAX,CAAtB,EAA+C;MAAEG,IAAI,EAAEzF,EAAE,CAACuF;IAAX,CAA/C,EAAoE;MAAEE,IAAI,EAAEpC,EAAE,CAAC+Q;IAAX,CAApE,EAA2F;MAAE3O,IAAI,EAAEI,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACxJL,IAAI,EAAErF;MADkJ,CAAD,EAExJ;QACCqF,IAAI,EAAEtF,MADP;QAECuF,IAAI,EAAE,CAAClE,yBAAD;MAFP,CAFwJ;IAA/B,CAA3F,EAK3B;MAAEiE,IAAI,EAAEI,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAClCL,IAAI,EAAExE,SAD4B;QAElCyE,IAAI,EAAE,CAAC,UAAD;MAF4B,CAAD;IAA/B,CAL2B,EAQ3B;MAAED,IAAI,EAAEtG,EAAE,CAACoc;IAAX,CAR2B,EAQA;MAAE9V,IAAI,EAAEI,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC7DL,IAAI,EAAErF;MADuD,CAAD,EAE7D;QACCqF,IAAI,EAAEtF,MADP;QAECuF,IAAI,EAAE,CAAC9D,qBAAD;MAFP,CAF6D;IAA/B,CARA,CAAP;EAalB,CAlCxB;AAAA;AAmCA;AACA;AACA;;;AACA,MAAMia,cAAN,CAAqB;EACjBxX,WAAW,GAAG;IACV;IACA,KAAKwV,EAAL,GAAW,qBAAoBR,YAAY,EAAG,EAA9C;EACH;;AAJgB;;AAMrBwC,cAAc,CAACxW,IAAf;EAAA,iBAA2GwW,cAA3G;AAAA;;AACAA,cAAc,CAACxT,IAAf,kBA30D4FrI,EA20D5F;EAAA,MAA+F6b,cAA/F;EAAA;EAAA,oBAAkM,UAAlM;EAAA;EAAA;IAAA;MA30D4F7b,EA20D5F;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MA30D4FA,EA20D5F;MA30D4FA,EA20D2R,gBAAvX;IAAA;EAAA;EAAA;EAAA;AAAA;;AACA;EAAA,mDA50D4FA,EA40D5F,mBAA2F6b,cAA3F,EAAuH,CAAC;IAC5GpW,IAAI,EAAEnF,SADsG;IAE5GoF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBADX;MAECgD,QAAQ,EAAE,gBAFX;MAGC1C,QAAQ,EAAE,2BAHX;MAICL,IAAI,EAAE;QACF,0BAA0B,cADxB;QAEF,aAAa,IAFX;QAGF,SAAS,mBAHP;QAIF,QAAQ;MAJN,CAJP;MAUC6C,aAAa,EAAEjI,iBAAiB,CAACkI,IAVlC;MAWCH,eAAe,EAAEhI,uBAAuB,CAACub;IAX1C,CAAD;EAFsG,CAAD,CAAvH,QAe4B;IAAEjC,EAAE,EAAE,CAAC;MACnBpU,IAAI,EAAE9E;IADa,CAAD;EAAN,CAf5B;AAAA;AAmBA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMob,aAAN,CAAoB;;AAEpBA,aAAa,CAAC1W,IAAd;EAAA,iBAA0G0W,aAA1G;AAAA;;AACAA,aAAa,CAACC,IAAd,kBAz2D4Fhc,EAy2D5F;EAAA,MAA2G+b;AAA3G;AAwBAA,aAAa,CAACE,IAAd,kBAj4D4Fjc,EAi4D5F;EAAA,UAAoID,YAApI,EACQ2B,eADR,EAEQ9B,YAFR,EAGQ+B,eAHR,EAIQpC,eAJR,EAKQF,UALR,EAKoBqC,eALpB;AAAA;;AAMA;EAAA,mDAv4D4F1B,EAu4D5F,mBAA2F+b,aAA3F,EAAsH,CAAC;IAC3GtW,IAAI,EAAEvE,QADqG;IAE3GwE,IAAI,EAAE,CAAC;MACCwW,OAAO,EAAE,CACLnc,YADK,EAEL2B,eAFK,EAGL9B,YAHK,EAIL+B,eAJK,EAKLpC,eALK,EAMLF,UANK,CADV;MASC;MACA8c,OAAO,EAAE,CACLza,eADK,EAELsX,WAFK,EAGLzS,WAHK,EAILO,MAJK,EAKLkT,SALK,EAML6B,cANK,EAOL5B,UAPK,EAQLjU,aARK,CAVV;MAoBCoW,YAAY,EAAE,CACVpD,WADU,EAEVzS,WAFU,EAGVO,MAHU,EAIV1C,SAJU,EAKVoI,kBALU,EAMVwN,SANU,EAOV6B,cAPU,EAQV5B,UARU,EASVzP,UATU,EAUVlB,gBAVU,EAWVuL,YAXU,EAYV7O,aAZU;IApBf,CAAD;EAFqG,CAAD,CAAtH;AAAA;AAuCA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAASM,OAAT,EAAkBgG,eAAlB,EAAmCzF,aAAnC,EAAkDzC,SAAlD,EAA6D0C,MAA7D,EAAqE0D,UAArE,EAAiFlB,gBAAjF,EAAmG6L,iBAAnG,EAAsHnP,aAAtH,EAAqIgT,WAArI,EAAkJnE,YAAlJ,EAAgKtO,WAAhK,EAA6KiG,kBAA7K,EAAiMyN,UAAjM,EAA6MD,SAA7M,EAAwN6B,cAAxN,EAAwOE,aAAxO,EAAuPrY,uBAAvP,EAAgR+G,eAAhR,EAAiS4K,gBAAjS,EAAmThB,iBAAnT,EAAsU8F,eAAtU,EAAuVb,cAAvV,EAAuWpQ,iBAAvW", "ignoreList": []}, "metadata": {}, "sourceType": "module"}