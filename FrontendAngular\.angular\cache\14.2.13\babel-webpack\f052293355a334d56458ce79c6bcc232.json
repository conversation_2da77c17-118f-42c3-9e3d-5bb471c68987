{"ast": null, "code": "import _asyncToGenerator from \"R:/chateye/FrontendAngular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { ChangeDetectorRef } from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { PERFORMANCE_CONFIG } from './config/performance.config';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./services/chat.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/toolbar\";\nimport * as i4 from \"@angular/material/sidenav\";\nimport * as i5 from \"@angular/material/card\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/chips\";\nimport * as i10 from \"@angular/material/tooltip\";\nimport * as i11 from \"@angular/material/select\";\nimport * as i12 from \"@angular/material/core\";\nimport * as i13 from \"./components/login-form/login-form.component\";\nimport * as i14 from \"./components/sidebar/sidebar.component\";\nimport * as i15 from \"./components/message-list/message-list.component\";\nimport * as i16 from \"./components/message-list/message-list-virtual.component\";\nimport * as i17 from \"./components/message-input/message-input.component\";\nimport * as i18 from \"./components/admin-panel/admin-panel.component\";\nimport * as i19 from \"./components/password-change/password-change.component\";\nimport * as i20 from \"./components/message-edit/message-edit.component\";\n\nfunction AppComponent_app_login_form_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-login-form\");\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_1_p_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 29);\n    i0.ɵɵtext(1, \" Please select a group to start chatting \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_1_p_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.currentGroup == null ? null : ctx_r7.currentGroup.description, \" \");\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_1_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function AppComponent_mat_sidenav_container_1_button_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.onShowAdminPanel());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"admin_panel_settings\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_1_app_message_list_virtual_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"app-message-list-virtual\", 32);\n    i0.ɵɵlistener(\"onReply\", function AppComponent_mat_sidenav_container_1_app_message_list_virtual_26_Template_app_message_list_virtual_onReply_0_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.onReply($event));\n    })(\"onAddReaction\", function AppComponent_mat_sidenav_container_1_app_message_list_virtual_26_Template_app_message_list_virtual_onAddReaction_0_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r18 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r18.onAddReaction($event));\n    })(\"onRemoveReaction\", function AppComponent_mat_sidenav_container_1_app_message_list_virtual_26_Template_app_message_list_virtual_onRemoveReaction_0_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r19 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r19.onRemoveReaction($event));\n    })(\"onEdit\", function AppComponent_mat_sidenav_container_1_app_message_list_virtual_26_Template_app_message_list_virtual_onEdit_0_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r20 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r20.onEdit($event));\n    })(\"onDelete\", function AppComponent_mat_sidenav_container_1_app_message_list_virtual_26_Template_app_message_list_virtual_onDelete_0_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r21 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r21.onDelete($event));\n    })(\"onMessageClick\", function AppComponent_mat_sidenav_container_1_app_message_list_virtual_26_Template_app_message_list_virtual_onMessageClick_0_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r22 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r22.onMessageClick($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"messages\", ctx_r9.messages)(\"currentUser\", ctx_r9.user)(\"loading\", ctx_r9.loading)(\"highlightedMessageId\", ctx_r9.highlightedMessageId);\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_1_app_message_list_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"app-message-list\", 32);\n    i0.ɵɵlistener(\"onReply\", function AppComponent_mat_sidenav_container_1_app_message_list_27_Template_app_message_list_onReply_0_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r23.onReply($event));\n    })(\"onAddReaction\", function AppComponent_mat_sidenav_container_1_app_message_list_27_Template_app_message_list_onAddReaction_0_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r25 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r25.onAddReaction($event));\n    })(\"onRemoveReaction\", function AppComponent_mat_sidenav_container_1_app_message_list_27_Template_app_message_list_onRemoveReaction_0_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r26 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r26.onRemoveReaction($event));\n    })(\"onEdit\", function AppComponent_mat_sidenav_container_1_app_message_list_27_Template_app_message_list_onEdit_0_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r27 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r27.onEdit($event));\n    })(\"onDelete\", function AppComponent_mat_sidenav_container_1_app_message_list_27_Template_app_message_list_onDelete_0_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.onDelete($event));\n    })(\"onMessageClick\", function AppComponent_mat_sidenav_container_1_app_message_list_27_Template_app_message_list_onMessageClick_0_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r29 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r29.onMessageClick($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"messages\", ctx_r10.messages)(\"currentUser\", ctx_r10.user)(\"loading\", ctx_r10.loading)(\"highlightedMessageId\", ctx_r10.highlightedMessageId);\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_1_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"mat-card\", 34)(2, \"mat-card-content\")(3, \"div\", 35)(4, \"div\", 36);\n    i0.ɵɵelement(5, \"span\")(6, \"span\")(7, \"span\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 37);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.getTypingText(ctx_r11.typingUsers), \" \");\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_1_app_message_input_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"app-message-input\", 38);\n    i0.ɵɵlistener(\"onSendMessage\", function AppComponent_mat_sidenav_container_1_app_message_input_30_Template_app_message_input_onSendMessage_0_listener($event) {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r30 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r30.onSendMessage($event));\n    })(\"onCancelReply\", function AppComponent_mat_sidenav_container_1_app_message_input_30_Template_app_message_input_onCancelReply_0_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r32 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r32.onCancelReply());\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"replyTo\", ctx_r12.replyTo);\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_1_div_31_mat_option_10_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const group_r35 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" - \", group_r35.description, \"\");\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_1_div_31_mat_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, AppComponent_mat_sidenav_container_1_div_31_mat_option_10_span_2_Template, 2, 1, \"span\", 46);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const group_r35 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", group_r35.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", group_r35.name, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", group_r35.description);\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_1_div_31_p_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 48);\n    i0.ɵɵtext(1, \" No groups available. Contact an administrator to get access to groups. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_1_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"mat-card\")(2, \"mat-card-content\")(3, \"div\", 40)(4, \"h3\");\n    i0.ɵɵtext(5, \"Select a group to start chatting\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-form-field\", 41)(7, \"mat-label\");\n    i0.ɵɵtext(8, \"Choose a group\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-select\", 42);\n    i0.ɵɵlistener(\"selectionChange\", function AppComponent_mat_sidenav_container_1_div_31_Template_mat_select_selectionChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r38 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r38.onJoinGroup($event.value));\n    });\n    i0.ɵɵtemplate(10, AppComponent_mat_sidenav_container_1_div_31_mat_option_10_Template, 3, 3, \"mat-option\", 43);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, AppComponent_mat_sidenav_container_1_div_31_p_11_Template, 2, 0, \"p\", 44);\n    i0.ɵɵelementEnd()()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"value\", null);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r13.groups);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r13.groups == null ? null : ctx_r13.groups.length) === 0);\n  }\n}\n\nfunction AppComponent_mat_sidenav_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r41 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"mat-sidenav-container\", 5)(1, \"mat-sidenav\", 6, 7)(3, \"app-sidebar\", 8);\n    i0.ɵɵlistener(\"onPasswordChange\", function AppComponent_mat_sidenav_container_1_Template_app_sidebar_onPasswordChange_3_listener() {\n      i0.ɵɵrestoreView(_r41);\n      const ctx_r40 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r40.onPasswordChange());\n    })(\"onLogout\", function AppComponent_mat_sidenav_container_1_Template_app_sidebar_onLogout_3_listener() {\n      i0.ɵɵrestoreView(_r41);\n      const ctx_r42 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r42.onLogout());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"mat-sidenav-content\", 9)(5, \"mat-toolbar\", 10)(6, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function AppComponent_mat_sidenav_container_1_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r41);\n\n      const _r5 = i0.ɵɵreference(2);\n\n      return i0.ɵɵresetView(_r5.toggle());\n    });\n    i0.ɵɵelementStart(7, \"mat-icon\");\n    i0.ɵɵtext(8, \"menu\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 12)(10, \"div\", 13)(11, \"h1\", 14);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, AppComponent_mat_sidenav_container_1_p_13_Template, 2, 0, \"p\", 15);\n    i0.ɵɵtemplate(14, AppComponent_mat_sidenav_container_1_p_14_Template, 2, 1, \"p\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 17)(16, \"div\", 18)(17, \"mat-icon\", 19);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\", 20);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"mat-chip-list\")(22, \"mat-chip\", 21);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(24, AppComponent_mat_sidenav_container_1_button_24_Template, 3, 0, \"button\", 22);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 23);\n    i0.ɵɵtemplate(26, AppComponent_mat_sidenav_container_1_app_message_list_virtual_26_Template, 1, 4, \"app-message-list-virtual\", 24);\n    i0.ɵɵtemplate(27, AppComponent_mat_sidenav_container_1_app_message_list_27_Template, 1, 4, \"app-message-list\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(28, AppComponent_mat_sidenav_container_1_div_28_Template, 10, 1, \"div\", 25);\n    i0.ɵɵelementStart(29, \"div\", 26);\n    i0.ɵɵtemplate(30, AppComponent_mat_sidenav_container_1_app_message_input_30_Template, 1, 1, \"app-message-input\", 27);\n    i0.ɵɵtemplate(31, AppComponent_mat_sidenav_container_1_div_31_Template, 12, 3, \"div\", 28);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"onlineUsers\", ctx_r1.onlineUsers)(\"currentUser\", ctx_r1.user)(\"isAdmin\", ctx_r1.isAdmin)(\"groups\", ctx_r1.groups)(\"currentGroup\", ctx_r1.currentGroup)(\"onJoinGroup\", ctx_r1.onJoinGroup.bind(ctx_r1));\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r1.currentGroup == null ? null : ctx_r1.currentGroup.name) || \"Select a Group\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.currentGroup);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentGroup == null ? null : ctx_r1.currentGroup.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"connected\", ctx_r1.connected);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.connected ? \"wifi\" : \"wifi_off\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.connected ? \"Connected\" : \"Disconnected\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.onlineUsers.length || 0, \" online \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isAdmin);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.useVirtualScrolling);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.useVirtualScrolling);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.typingUsers && ctx_r1.typingUsers.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentGroup);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.currentGroup);\n  }\n}\n\nfunction AppComponent_app_admin_panel_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"app-admin-panel\", 49);\n    i0.ɵɵlistener(\"onClose\", function AppComponent_app_admin_panel_2_Template_app_admin_panel_onClose_0_listener() {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r44 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r44.onHideAdminPanel());\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"currentUser\", ctx_r2.user);\n  }\n}\n\nfunction AppComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r47 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"mat-card\", 51)(2, \"mat-card-header\")(3, \"mat-card-title\");\n    i0.ɵɵtext(4, \"Change Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function AppComponent_div_3_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r47);\n      const ctx_r46 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r46.onClosePasswordChange());\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"close\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"mat-card-content\");\n    i0.ɵɵelement(9, \"app-password-change\");\n    i0.ɵɵelementEnd()()();\n  }\n}\n\nfunction AppComponent_app_message_edit_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r49 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"app-message-edit\", 53);\n    i0.ɵɵlistener(\"onSave\", function AppComponent_app_message_edit_4_Template_app_message_edit_onSave_0_listener($event) {\n      i0.ɵɵrestoreView(_r49);\n      const ctx_r48 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r48.onSaveEdit($event));\n    })(\"onCancel\", function AppComponent_app_message_edit_4_Template_app_message_edit_onCancel_0_listener() {\n      i0.ɵɵrestoreView(_r49);\n      const ctx_r50 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r50.onCancelEdit());\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"message\", ctx_r4.editingMessage);\n  }\n}\n\nexport let AppComponent = /*#__PURE__*/(() => {\n  class AppComponent {\n    constructor(chatService, cdr) {\n      this.chatService = chatService;\n      this.cdr = cdr; // Cached values to reduce async pipe usage\n\n      this.user = null;\n      this.messages = [];\n      this.onlineUsers = [];\n      this.groups = [];\n      this.currentGroup = null;\n      this.replyTo = null;\n      this.loading = false;\n      this.isAdmin = false;\n      this.showAdminPanel = false;\n      this.connected = false;\n      this.isLoggedIn = false;\n      this.showPasswordChange = false;\n      this.editingMessage = null;\n      this.highlightedMessageId = null;\n      this.typingUsers = []; // Performance configuration\n\n      this.useVirtualScrolling = PERFORMANCE_CONFIG.enableVirtualScrolling;\n      this.subscriptions = [];\n    }\n\n    ngOnInit() {\n      // Subscribe to all observables and cache values to reduce async pipe usage\n      this.subscriptions.push(this.chatService.user$.subscribe(user => {\n        this.user = user;\n        this.cdr.markForCheck();\n      }), this.chatService.messages$.subscribe(messages => {\n        this.messages = messages;\n        this.cdr.markForCheck();\n      }), this.chatService.onlineUsers$.subscribe(users => {\n        this.onlineUsers = users;\n        this.cdr.markForCheck();\n      }), this.chatService.groups$.subscribe(groups => {\n        this.groups = groups;\n        this.cdr.markForCheck();\n      }), this.chatService.currentGroup$.subscribe(group => {\n        this.currentGroup = group;\n        this.cdr.markForCheck();\n      }), this.chatService.replyTo$.subscribe(replyTo => {\n        this.replyTo = replyTo;\n        this.cdr.markForCheck();\n      }), this.chatService.loading$.subscribe(loading => {\n        this.loading = loading;\n        this.cdr.markForCheck();\n      }), this.chatService.isAdmin$.subscribe(isAdmin => {\n        this.isAdmin = isAdmin;\n        this.cdr.markForCheck();\n      }), this.chatService.showAdminPanel$.subscribe(showPanel => {\n        this.showAdminPanel = showPanel;\n        this.cdr.markForCheck();\n      }), this.chatService.connected$.subscribe(connected => {\n        this.connected = connected;\n        this.cdr.markForCheck();\n      }), this.chatService.isLoggedIn$.subscribe(isLoggedIn => {\n        this.isLoggedIn = isLoggedIn;\n        this.cdr.markForCheck();\n      }), this.chatService.editingMessage$.subscribe(editingMessage => {\n        this.editingMessage = editingMessage;\n        this.cdr.markForCheck();\n      }), this.chatService.typingUsers$.subscribe(typingUsers => {\n        this.typingUsers = typingUsers;\n        this.cdr.markForCheck();\n      }));\n    }\n\n    ngOnDestroy() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n      this.chatService.logout();\n    }\n\n    onJoinGroup(groupId) {\n      this.chatService.joinGroup(groupId);\n    }\n\n    onSendMessage(event) {\n      this.chatService.sendMessage(event.text, event.replyToId);\n    }\n\n    onReply(message) {\n      this.chatService.replyToMessage(message); // Highlight the replied-to message\n\n      this.highlightedMessageId = message.id; // Clear highlight after 3 seconds\n\n      setTimeout(() => {\n        this.highlightedMessageId = null;\n      }, 3000);\n    }\n\n    onCancelReply() {\n      this.chatService.cancelReply();\n    }\n\n    onAddReaction(event) {\n      this.chatService.addReaction(event.messageId, event.emoji);\n    }\n\n    onRemoveReaction(data) {\n      this.chatService.removeReaction(data);\n    }\n\n    onEdit(message) {\n      this.chatService.startEditingMessage(message);\n    }\n\n    onSaveEdit(newText) {\n      var _this = this;\n\n      return _asyncToGenerator(function* () {\n        try {\n          if (_this.editingMessage) {\n            yield _this.chatService.updateMessage(_this.editingMessage.id, newText);\n\n            _this.chatService.cancelEditingMessage();\n          }\n        } catch (error) {\n          console.error('Failed to edit message:', error);\n          alert('Failed to edit message. Please try again.');\n        }\n      })();\n    }\n\n    onCancelEdit() {\n      this.chatService.cancelEditingMessage();\n    }\n\n    onDelete(message) {\n      var _this2 = this;\n\n      return _asyncToGenerator(function* () {\n        try {\n          if (confirm('Are you sure you want to delete this message?')) {\n            yield _this2.chatService.deleteMessage(message.id);\n          }\n        } catch (error) {\n          console.error('Failed to delete message:', error);\n          alert('Failed to delete message. Please try again.');\n        }\n      })();\n    }\n\n    onShowAdminPanel() {\n      this.chatService.showAdminPanel();\n    }\n\n    onHideAdminPanel() {\n      this.chatService.hideAdminPanel();\n    }\n\n    onPasswordChange() {\n      this.showPasswordChange = true;\n    }\n\n    onClosePasswordChange() {\n      this.showPasswordChange = false;\n    }\n\n    onLogout() {\n      this.chatService.logout();\n    }\n\n    onMessageClick(message) {\n      this.highlightedMessageId = message.id; // Clear highlight after 3 seconds\n\n      setTimeout(() => {\n        this.highlightedMessageId = null;\n      }, 3000);\n    }\n\n    getTypingText(typingUsers) {\n      if (typingUsers.length === 0) return '';\n\n      if (typingUsers.length === 1) {\n        return `${typingUsers[0]} is typing...`;\n      } else if (typingUsers.length === 2) {\n        return `${typingUsers[0]} and ${typingUsers[1]} are typing...`;\n      } else {\n        return `${typingUsers[0]} and ${typingUsers.length - 1} others are typing...`;\n      }\n    }\n\n  }\n\n  AppComponent.ɵfac = function AppComponent_Factory(t) {\n    return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.ChatService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n\n  AppComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppComponent,\n    selectors: [[\"app-root\"]],\n    decls: 5,\n    vars: 5,\n    consts: [[4, \"ngIf\"], [\"class\", \"chat-container\", 4, \"ngIf\"], [3, \"currentUser\", \"onClose\", 4, \"ngIf\"], [\"class\", \"modal-overlay\", 4, \"ngIf\"], [3, \"message\", \"onSave\", \"onCancel\", 4, \"ngIf\"], [1, \"chat-container\"], [\"mode\", \"side\", \"opened\", \"true\", 1, \"chat-sidebar\"], [\"sidenav\", \"\"], [3, \"onlineUsers\", \"currentUser\", \"isAdmin\", \"groups\", \"currentGroup\", \"onJoinGroup\", \"onPasswordChange\", \"onLogout\"], [1, \"chat-main-content\"], [1, \"chat-header\"], [\"mat-icon-button\", \"\", 1, \"menu-button\", 3, \"click\"], [1, \"header-content\"], [1, \"group-info\"], [1, \"group-name\"], [\"class\", \"no-group-message\", 4, \"ngIf\"], [\"class\", \"group-description\", 4, \"ngIf\"], [1, \"header-actions\"], [1, \"connection-status\"], [1, \"status-icon\"], [1, \"status-text\"], [1, \"online-count\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Admin Panel\", 3, \"click\", 4, \"ngIf\"], [1, \"messages-container\"], [3, \"messages\", \"currentUser\", \"loading\", \"highlightedMessageId\", \"onReply\", \"onAddReaction\", \"onRemoveReaction\", \"onEdit\", \"onDelete\", \"onMessageClick\", 4, \"ngIf\"], [\"class\", \"typing-indicator\", 4, \"ngIf\"], [1, \"input-container\"], [3, \"replyTo\", \"onSendMessage\", \"onCancelReply\", 4, \"ngIf\"], [\"class\", \"no-group-input\", 4, \"ngIf\"], [1, \"no-group-message\"], [1, \"group-description\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Admin Panel\", 3, \"click\"], [3, \"messages\", \"currentUser\", \"loading\", \"highlightedMessageId\", \"onReply\", \"onAddReaction\", \"onRemoveReaction\", \"onEdit\", \"onDelete\", \"onMessageClick\"], [1, \"typing-indicator\"], [1, \"typing-card\"], [1, \"typing-content\"], [1, \"typing-dots\"], [1, \"typing-text\"], [3, \"replyTo\", \"onSendMessage\", \"onCancelReply\"], [1, \"no-group-input\"], [1, \"group-selection\"], [\"appearance\", \"outline\", 1, \"group-selector\"], [3, \"value\", \"selectionChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"no-groups-message\", 4, \"ngIf\"], [3, \"value\"], [\"class\", \"group-description-small\", 4, \"ngIf\"], [1, \"group-description-small\"], [1, \"no-groups-message\"], [3, \"currentUser\", \"onClose\"], [1, \"modal-overlay\"], [1, \"modal-card\"], [\"mat-icon-button\", \"\", 1, \"close-button\", 3, \"click\"], [3, \"message\", \"onSave\", \"onCancel\"]],\n    template: function AppComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, AppComponent_app_login_form_0_Template, 1, 0, \"app-login-form\", 0);\n        i0.ɵɵtemplate(1, AppComponent_mat_sidenav_container_1_Template, 32, 20, \"mat-sidenav-container\", 1);\n        i0.ɵɵtemplate(2, AppComponent_app_admin_panel_2_Template, 1, 1, \"app-admin-panel\", 2);\n        i0.ɵɵtemplate(3, AppComponent_div_3_Template, 10, 0, \"div\", 3);\n        i0.ɵɵtemplate(4, AppComponent_app_message_edit_4_Template, 1, 1, \"app-message-edit\", 4);\n      }\n\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLoggedIn);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoggedIn);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.showAdminPanel);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.showPasswordChange);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.editingMessage);\n      }\n    },\n    dependencies: [i2.NgForOf, i2.NgIf, i3.MatToolbar, i4.MatSidenav, i4.MatSidenavContainer, i4.MatSidenavContent, i5.MatCard, i5.MatCardHeader, i5.MatCardContent, i5.MatCardTitle, i6.MatFormField, i6.MatLabel, i7.MatButton, i8.MatIcon, i9.MatChipList, i9.MatChip, i10.MatTooltip, i11.MatSelect, i12.MatOption, i13.LoginFormComponent, i14.SidebarComponent, i15.MessageListComponent, i16.MessageListVirtualComponent, i17.MessageInputComponent, i18.AdminPanelComponent, i19.PasswordChangeComponent, i20.MessageEditComponent],\n    styles: [\".chat-container[_ngcontent-%COMP%]{height:100vh;width:100vw;background:#f5f5f5}.chat-sidebar[_ngcontent-%COMP%]{width:280px;min-width:280px;background:white;border-right:1px solid #e0e0e0;box-shadow:2px 0 8px #0000001a}.chat-main-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:100vh;background:white}.chat-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#3f51b5 0%,#5c6bc0 100%);color:#fff;padding:0 16px;box-shadow:0 2px 8px #0000001a;z-index:10}.menu-button[_ngcontent-%COMP%]{margin-right:16px}.header-content[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;flex:1;min-width:0}.group-info[_ngcontent-%COMP%]{flex:1;min-width:0;margin-right:16px}.group-name[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:500;margin:0 0 4px;line-height:1.2}.no-group-message[_ngcontent-%COMP%]{color:#fffc;font-size:.875rem;margin:0;font-weight:400}.group-description[_ngcontent-%COMP%]{color:#ffffffe6;font-size:.75rem;margin:0;opacity:.9}.header-actions[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px;flex-shrink:0}.connection-status[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;background:rgba(255,255,255,.1);padding:8px 12px;border-radius:16px;backdrop-filter:blur(10px)}.status-icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px;color:#ef4444;transition:all .3s ease}.status-icon.connected[_ngcontent-%COMP%]{color:#10b981}.status-text[_ngcontent-%COMP%]{font-size:.75rem;font-weight:500}.online-count[_ngcontent-%COMP%]{background:rgba(255,255,255,.1);color:#fff;font-size:.75rem;font-weight:500}.messages-container[_ngcontent-%COMP%]{flex:1;min-height:0;background:#f8f9fa;overflow:hidden;position:relative}.input-container[_ngcontent-%COMP%]{background:white;border-top:1px solid #e0e0e0;padding:16px}.no-group-input[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;height:60px}.group-selection[_ngcontent-%COMP%]{text-align:center;padding:20px}.group-selection[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 16px;color:#333;font-weight:500}.group-selector[_ngcontent-%COMP%]{width:100%;max-width:300px;margin-bottom:16px}.group-description-small[_ngcontent-%COMP%]{color:#666;font-size:.8rem}.no-groups-message[_ngcontent-%COMP%]{color:#666;font-style:italic;margin:0 0 16px}.retry-button[_ngcontent-%COMP%]{margin-top:8px}.modal-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background:rgba(0,0,0,.5);display:flex;align-items:center;justify-content:center;z-index:1000}.modal-card[_ngcontent-%COMP%]{width:90%;max-width:500px;max-height:90vh;overflow-y:auto}.close-button[_ngcontent-%COMP%]{position:absolute;top:8px;right:8px}@media (max-width: 1024px){.chat-sidebar[_ngcontent-%COMP%]{width:260px;min-width:260px}.group-name[_ngcontent-%COMP%]{font-size:1.125rem}.header-actions[_ngcontent-%COMP%]{gap:12px}}@media (max-width: 768px){.chat-sidebar[_ngcontent-%COMP%]{width:100%;min-width:100%}.chat-main-content[_ngcontent-%COMP%]{height:calc(100vh - 60px)}.group-name[_ngcontent-%COMP%]{font-size:1rem}.header-actions[_ngcontent-%COMP%]{gap:8px}.connection-status[_ngcontent-%COMP%]{padding:6px 10px}.status-text[_ngcontent-%COMP%], .online-count[_ngcontent-%COMP%]{font-size:.7rem}}@media (max-width: 480px){.header-content[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:8px}.header-actions[_ngcontent-%COMP%]{width:100%;justify-content:space-between}.group-name[_ngcontent-%COMP%]{font-size:.9rem}.chat-header[_ngcontent-%COMP%]{padding:0 8px}.input-container[_ngcontent-%COMP%]{padding:12px}}*[_ngcontent-%COMP%]{transition:all .3s cubic-bezier(.4,0,.2,1)}[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px}[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:transparent}[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:rgba(0,0,0,.2);border-radius:3px}[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:rgba(0,0,0,.3)}.typing-indicator[_ngcontent-%COMP%]{padding:8px 16px;background:#f8f9fa;border-top:1px solid #e0e0e0}.typing-card[_ngcontent-%COMP%]{margin:0;box-shadow:none;background:transparent}.typing-card[_ngcontent-%COMP%]   .mat-card-content[_ngcontent-%COMP%]{padding:8px 12px}.typing-content[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.typing-dots[_ngcontent-%COMP%]{display:flex;gap:3px}.typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{width:6px;height:6px;background:#666;border-radius:50%;animation:typing-bounce 1.4s infinite ease-in-out}.typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1){animation-delay:-.32s}.typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2){animation-delay:-.16s}.typing-text[_ngcontent-%COMP%]{font-size:.875rem;color:#666;font-style:italic}@keyframes typing-bounce{0%,80%,to{transform:scale(.8);opacity:.5}40%{transform:scale(1);opacity:1}}\"],\n    changeDetection: 0\n  });\n  return AppComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}