-- Migration script to add password_hash column to users table
-- Run this script to update existing databases

-- Add password_hash column to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS password_hash VARCHAR(255);

-- Update existing users to have a default password (they'll need to reset)
-- This is optional - you might want to handle this differently
-- UPDATE users SET password_hash = '$2b$10$default.hash.here' WHERE password_hash IS NULL;
