const fs = require('fs');
const path = require('path');
const { pool } = require('../database/db');
const AllowedUser = require('../models/AllowedUser');

async function updateDatabase() {
  try {
    console.log('Updating database schema...');
    
    // Read and execute the update schema
    const updateSchemaPath = path.join(__dirname, 'update_schema.sql');
    const updateSchema = fs.readFileSync(updateSchemaPath, 'utf8');
    
    await pool.query(updateSchema);
    console.log('Schema update completed successfully!');
    
    // Initialize admin users
    await AllowedUser.initializeDefaults();
    console.log('Admin users initialized');
    
    // Check current admin users
    const adminUsers = process.env.ADMIN_USERS ? process.env.ADMIN_USERS.split(',') : ['admin'];
    console.log('Admin users configured:', adminUsers);
    
    // Set admin status for configured users
    for (const adminUser of adminUsers) {
      try {
        const result = await pool.query(
          'UPDATE users SET is_admin = true WHERE username = $1',
          [adminUser.trim()]
        );
        if (result.rowCount > 0) {
          console.log(`Set admin status for: ${adminUser}`);
        } else {
          console.log(`User not found: ${adminUser}`);
        }
      } catch (error) {
        console.log(`Error updating ${adminUser}:`, error.message);
      }
    }
    
    console.log('Database update completed!');
    process.exit(0);
  } catch (error) {
    console.error('Database update failed:', error);
    process.exit(1);
  }
}

updateDatabase();
