import { Component, OnInit } from '@angular/core';
import { ChatService } from '../../services/chat.service';
import { ApiService, SecurityInfo } from '../../services/api.service';

@Component({
  selector: 'app-login-form',
  templateUrl: './login-form.component.html',
  styleUrls: ['./login-form.component.css']
})
export class LoginFormComponent implements OnInit {
  username = 'testuser';
  password = '';
  inviteCode = '';
  isLoading = false;
  securityInfo: SecurityInfo | null = null;
  error = '';
  requiresPassword = false;
  private hasAutoLoggedIn = false; // Prevent multiple auto-login attempts

  constructor(
    private chatService: ChatService,
    private apiService: ApiService
  ) {}

  ngOnInit(): void {
    this.fetchSecurityInfo();

    // Subscribe to security info changes from ChatService
    this.chatService.securityInfo$.subscribe(securityInfo => {
      if (securityInfo) {
        this.securityInfo = securityInfo;
        console.log('Security info updated from ChatService:', securityInfo);

        // Auto-login for testing - only once and if not already logged in
        if (!this.hasAutoLoggedIn && !this.chatService.getCurrentUser()) {
          this.hasAutoLoggedIn = true;
          setTimeout(() => {
            if (this.username && !this.isLoading && !this.chatService.getCurrentUser()) {
              console.log('Auto-submitting login form for testing...');
              this.onSubmit();
            }
          }, 1000);
        }
      }
    });

    // Check if already logged in
    this.chatService.user$.subscribe(user => {
      if (user) {
        this.hasAutoLoggedIn = true; // Prevent auto-login if already logged in
      }
    });
  }

  private fetchSecurityInfo(): void {
    this.apiService.getSecurityInfo().subscribe({
      next: (data) => {
        this.securityInfo = data;
        console.log('Security info updated:', data);
      },
      error: (error) => {
        console.error('Failed to fetch security info:', error);
        this.securityInfo = { mode: 'open', requiresInvite: false, requiresWhitelist: false };
      }
    });
  }

  refreshSecurityInfo(): void {
    this.fetchSecurityInfo();
  }

  async onSubmit(): Promise<void> {
    if (!this.username.trim()) return;
    
    // Validate invite code if required
    if (this.securityInfo?.requiresInvite && !this.inviteCode.trim()) {
      this.error = 'Invite code is required';
      return;
    }

    this.isLoading = true;
    this.error = '';
    
    try {
      await this.chatService.login(
        this.username.trim(), 
        this.password.trim() || undefined, 
        this.inviteCode.trim() || null
      );
    } catch (error: any) {
      console.error('Login failed:', error);
      this.error = error.error?.error || error.message || 'Login failed';
      
      // Check if password is required
      if (error.error?.requiresPassword) {
        this.requiresPassword = true;
      }
    } finally {
      this.isLoading = false;
    }
  }

  onInviteCodeInput(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.inviteCode = target.value.toUpperCase();
  }
}
