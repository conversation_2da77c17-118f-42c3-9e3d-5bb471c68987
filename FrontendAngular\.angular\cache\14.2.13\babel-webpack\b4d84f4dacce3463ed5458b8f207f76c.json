{"ast": null, "code": "import _asyncToGenerator from \"R:/chateye/FrontendAngular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/api.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/material/card\";\nimport * as i5 from \"@angular/material/form-field\";\nimport * as i6 from \"@angular/material/input\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/chips\";\nimport * as i10 from \"@angular/material/tooltip\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nimport * as i12 from \"@angular/material/tabs\";\n\nfunction AdminPanelComponent_div_25_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵelement(1, \"mat-spinner\", 26);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AdminPanelComponent_div_25_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"people_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No users in whitelist\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AdminPanelComponent_div_25_div_22_div_1_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_25_div_22_div_1_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const user_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r11 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r11.removeUserFromWhitelist(user_r9.username));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"delete\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AdminPanelComponent_div_25_div_22_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 31)(2, \"span\", 32);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 33);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 34)(7, \"mat-chip\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, AdminPanelComponent_div_25_div_22_div_1_button_9_Template, 3, 0, \"button\", 35);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const user_r9 = ctx.$implicit;\n    const ctx_r8 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(user_r9.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" Added by \", user_r9.added_by, \" on \", ctx_r8.formatDate(user_r9.added_at), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(user_r9.is_active ? \"active-chip\" : \"inactive-chip\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", user_r9.is_active ? \"Active\" : \"Inactive\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", user_r9.is_active);\n  }\n}\n\nfunction AdminPanelComponent_div_25_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵtemplate(1, AdminPanelComponent_div_25_div_22_div_1_Template, 10, 7, \"div\", 29);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.allowedUsers);\n  }\n}\n\nfunction AdminPanelComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"mat-card\", 16)(2, \"mat-card-header\")(3, \"mat-card-title\");\n    i0.ɵɵtext(4, \"Add User to Whitelist\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"mat-card-content\")(6, \"div\", 17)(7, \"mat-form-field\", 18)(8, \"mat-label\");\n    i0.ɵɵtext(9, \"Username\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"input\", 19);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminPanelComponent_div_25_Template_input_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.newUsername = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_25_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.addUserToWhitelist());\n    });\n    i0.ɵɵelementStart(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(14, \" Add User \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(15, \"mat-card\", 21)(16, \"mat-card-header\")(17, \"mat-card-title\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"mat-card-content\");\n    i0.ɵɵtemplate(20, AdminPanelComponent_div_25_div_20_Template, 4, 0, \"div\", 22);\n    i0.ɵɵtemplate(21, AdminPanelComponent_div_25_div_21_Template, 5, 0, \"div\", 23);\n    i0.ɵɵtemplate(22, AdminPanelComponent_div_25_div_22_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.newUsername);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.newUsername.trim());\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"Allowed Users (\", ctx_r0.allowedUsers.length, \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.loading && ctx_r0.allowedUsers.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.loading && ctx_r0.allowedUsers.length > 0);\n  }\n}\n\nfunction AdminPanelComponent_div_26_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵelement(1, \"mat-spinner\", 26);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AdminPanelComponent_div_26_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"vpn_key\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No invite codes\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AdminPanelComponent_div_26_div_30_div_1_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const code_r21 = i0.ɵɵnextContext().$implicit;\n    const ctx_r22 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" Expires: \", ctx_r22.formatDate(code_r21.expires_at), \" \");\n  }\n}\n\nfunction AdminPanelComponent_div_26_div_30_div_1_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_26_div_30_div_1_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const code_r21 = i0.ɵɵnextContext().$implicit;\n      const ctx_r25 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r25.deactivateInviteCode(code_r21.code));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"block\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AdminPanelComponent_div_26_div_30_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 46)(2, \"span\", 47);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 48);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, AdminPanelComponent_div_26_div_30_div_1_span_6_Template, 2, 1, \"span\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 50)(8, \"mat-chip\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_26_div_30_div_1_Template_button_click_10_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r29);\n      const code_r21 = restoredCtx.$implicit;\n      const ctx_r28 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r28.copyToClipboard(code_r21.code));\n    });\n    i0.ɵɵelementStart(11, \"mat-icon\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, AdminPanelComponent_div_26_div_30_div_1_button_13_Template, 3, 0, \"button\", 52);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const code_r21 = ctx.$implicit;\n    const ctx_r20 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(code_r21.code);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate4(\" Uses: \", code_r21.current_uses, \"/\", code_r21.max_uses, \" | Created by \", code_r21.created_by, \" on \", ctx_r20.formatDate(code_r21.created_at), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", code_r21.expires_at);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(code_r21.status === \"active\" ? \"active-chip\" : \"inactive-chip\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", code_r21.status, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r20.copiedCode === code_r21.code ? \"check\" : \"content_copy\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", code_r21.status === \"active\");\n  }\n}\n\nfunction AdminPanelComponent_div_26_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵtemplate(1, AdminPanelComponent_div_26_div_30_div_1_Template, 14, 11, \"div\", 44);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r19.inviteCodes);\n  }\n}\n\nfunction AdminPanelComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"mat-card\", 16)(2, \"mat-card-header\")(3, \"mat-card-title\");\n    i0.ɵɵtext(4, \"Create Invite Code\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"mat-card-content\")(6, \"div\", 17)(7, \"mat-form-field\", 37)(8, \"mat-label\");\n    i0.ɵɵtext(9, \"Max Uses\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"input\", 38);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminPanelComponent_div_26_Template_input_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.newInviteConfig.maxUses = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"mat-form-field\", 37)(12, \"mat-label\");\n    i0.ɵɵtext(13, \"Expires In (Hours)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"input\", 39);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminPanelComponent_div_26_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r32 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r32.newInviteConfig.expiresInHours = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"mat-form-field\", 37)(16, \"mat-label\");\n    i0.ɵɵtext(17, \"Custom Code (Optional)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"input\", 40);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminPanelComponent_div_26_Template_input_ngModelChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r33 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r33.newInviteConfig.customCode = $event);\n    })(\"input\", function AdminPanelComponent_div_26_Template_input_input_18_listener($event) {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r34.onCustomCodeInput($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_26_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.createInviteCode());\n    });\n    i0.ɵɵelementStart(20, \"mat-icon\");\n    i0.ɵɵtext(21, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(22, \" Create Code \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(23, \"mat-card\", 21)(24, \"mat-card-header\")(25, \"mat-card-title\");\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"mat-card-content\");\n    i0.ɵɵtemplate(28, AdminPanelComponent_div_26_div_28_Template, 4, 0, \"div\", 22);\n    i0.ɵɵtemplate(29, AdminPanelComponent_div_26_div_29_Template, 5, 0, \"div\", 23);\n    i0.ɵɵtemplate(30, AdminPanelComponent_div_26_div_30_Template, 2, 1, \"div\", 42);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.newInviteConfig.maxUses);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.newInviteConfig.expiresInHours);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.newInviteConfig.customCode);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"Invite Codes (\", ctx_r1.inviteCodes.length, \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading && ctx_r1.inviteCodes.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading && ctx_r1.inviteCodes.length > 0);\n  }\n}\n\nfunction AdminPanelComponent_div_27_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵelement(1, \"mat-spinner\", 26);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AdminPanelComponent_div_27_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"group\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No groups\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AdminPanelComponent_div_27_div_26_div_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const group_r41 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(group_r41.description);\n  }\n}\n\nfunction AdminPanelComponent_div_27_div_26_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 62);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_27_div_26_div_1_Template_div_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r45);\n      const group_r41 = restoredCtx.$implicit;\n      const ctx_r44 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r44.onGroupSelect(group_r41));\n    });\n    i0.ɵɵelementStart(2, \"span\", 63);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AdminPanelComponent_div_27_div_26_div_1_span_4_Template, 2, 1, \"span\", 64);\n    i0.ɵɵelementStart(5, \"span\", 65);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 66)(8, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_27_div_26_div_1_Template_button_click_8_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r45);\n      const group_r41 = restoredCtx.$implicit;\n      const ctx_r46 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r46.deleteGroup(group_r41.id));\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"delete\");\n    i0.ɵɵelementEnd()()()();\n  }\n\n  if (rf & 2) {\n    const group_r41 = ctx.$implicit;\n    const ctx_r40 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"selected\", (ctx_r40.selectedGroup == null ? null : ctx_r40.selectedGroup.id) === group_r41.id);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(group_r41.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", group_r41.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Created by \", group_r41.created_by_username || \"Unknown\", \" \");\n  }\n}\n\nfunction AdminPanelComponent_div_27_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtemplate(1, AdminPanelComponent_div_27_div_26_div_1_Template, 11, 5, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r38 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r38.groups);\n  }\n}\n\nfunction AdminPanelComponent_div_27_mat_card_27_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵelement(1, \"mat-spinner\", 26);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AdminPanelComponent_div_27_mat_card_27_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"person_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No users in this group\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AdminPanelComponent_div_27_mat_card_27_div_7_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r53 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 31)(2, \"span\", 32);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 33);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 34)(7, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_27_mat_card_27_div_7_div_1_Template_button_click_7_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r53);\n      const user_r51 = restoredCtx.$implicit;\n      const ctx_r52 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r52.revokeUserAccess(user_r51.username));\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"remove_circle\");\n    i0.ɵɵelementEnd()()()();\n  }\n\n  if (rf & 2) {\n    const user_r51 = ctx.$implicit;\n    const ctx_r50 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(user_r51.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Granted access on \", ctx_r50.formatDate(user_r51.granted_at), \" \");\n  }\n}\n\nfunction AdminPanelComponent_div_27_mat_card_27_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵtemplate(1, AdminPanelComponent_div_27_mat_card_27_div_7_div_1_Template, 10, 2, \"div\", 29);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r49 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r49.groupUsers);\n  }\n}\n\nfunction AdminPanelComponent_div_27_mat_card_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 21)(1, \"mat-card-header\")(2, \"mat-card-title\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"mat-card-content\");\n    i0.ɵɵtemplate(5, AdminPanelComponent_div_27_mat_card_27_div_5_Template, 4, 0, \"div\", 22);\n    i0.ɵɵtemplate(6, AdminPanelComponent_div_27_mat_card_27_div_6_Template, 5, 0, \"div\", 23);\n    i0.ɵɵtemplate(7, AdminPanelComponent_div_27_mat_card_27_div_7_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r39 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Group Users - \", ctx_r39.selectedGroup.name, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r39.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r39.loading && ctx_r39.groupUsers.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r39.loading && ctx_r39.groupUsers.length > 0);\n  }\n}\n\nfunction AdminPanelComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r55 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"mat-card\", 16)(2, \"mat-card-header\")(3, \"mat-card-title\");\n    i0.ɵɵtext(4, \"Create Group\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"mat-card-content\")(6, \"div\", 17)(7, \"mat-form-field\", 37)(8, \"mat-label\");\n    i0.ɵɵtext(9, \"Group Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"input\", 55);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminPanelComponent_div_27_Template_input_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r54 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r54.newGroupName = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"mat-form-field\", 37)(12, \"mat-label\");\n    i0.ɵɵtext(13, \"Description (Optional)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"input\", 56);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminPanelComponent_div_27_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r56 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r56.newGroupDescription = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_27_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r57 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r57.createGroup());\n    });\n    i0.ɵɵelementStart(16, \"mat-icon\");\n    i0.ɵɵtext(17, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(18, \" Create Group \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(19, \"mat-card\", 21)(20, \"mat-card-header\")(21, \"mat-card-title\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"mat-card-content\");\n    i0.ɵɵtemplate(24, AdminPanelComponent_div_27_div_24_Template, 4, 0, \"div\", 22);\n    i0.ɵɵtemplate(25, AdminPanelComponent_div_27_div_25_Template, 5, 0, \"div\", 23);\n    i0.ɵɵtemplate(26, AdminPanelComponent_div_27_div_26_Template, 2, 1, \"div\", 57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(27, AdminPanelComponent_div_27_mat_card_27_Template, 8, 4, \"mat-card\", 58);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.newGroupName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.newGroupDescription);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.newGroupName.trim());\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"Groups (\", ctx_r2.groups.length, \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.loading && ctx_r2.groups.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.loading && ctx_r2.groups.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedGroup);\n  }\n}\n\nfunction AdminPanelComponent_div_28_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵelement(1, \"mat-spinner\", 26);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AdminPanelComponent_div_28_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No users\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction AdminPanelComponent_div_28_div_26_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r64 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 31)(2, \"span\", 32);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 33);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 34)(7, \"mat-chip\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_28_div_26_div_1_Template_button_click_9_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r64);\n      const user_r62 = restoredCtx.$implicit;\n      const ctx_r63 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r63.openUserModal(user_r62));\n    });\n    i0.ɵɵelementStart(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"settings\");\n    i0.ɵɵelementEnd()()()();\n  }\n\n  if (rf & 2) {\n    const user_r62 = ctx.$implicit;\n    const ctx_r61 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(user_r62.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" Last seen: \", ctx_r61.formatDate(user_r62.last_seen), \" | Status: \", user_r62.online_status ? \"Online\" : \"Offline\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(user_r62.hasPassword ? \"active-chip\" : \"inactive-chip\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", user_r62.hasPassword ? \"Has Password\" : \"No Password\", \" \");\n  }\n}\n\nfunction AdminPanelComponent_div_28_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵtemplate(1, AdminPanelComponent_div_28_div_26_div_1_Template, 12, 6, \"div\", 29);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r60 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r60.users);\n  }\n}\n\nfunction AdminPanelComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r66 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"mat-card\", 16)(2, \"mat-card-header\")(3, \"mat-card-title\");\n    i0.ɵɵtext(4, \"Create User\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"mat-card-content\")(6, \"div\", 17)(7, \"mat-form-field\", 37)(8, \"mat-label\");\n    i0.ɵɵtext(9, \"Username\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"input\", 70);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminPanelComponent_div_28_Template_input_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r66);\n      const ctx_r65 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r65.newUsername = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"mat-form-field\", 37)(12, \"mat-label\");\n    i0.ɵɵtext(13, \"Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"input\", 71);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminPanelComponent_div_28_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r66);\n      const ctx_r67 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r67.newUserPassword = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_28_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r66);\n      const ctx_r68 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r68.createUser());\n    });\n    i0.ɵɵelementStart(16, \"mat-icon\");\n    i0.ɵɵtext(17, \"add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(18, \" Create User \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(19, \"mat-card\", 21)(20, \"mat-card-header\")(21, \"mat-card-title\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"mat-card-content\");\n    i0.ɵɵtemplate(24, AdminPanelComponent_div_28_div_24_Template, 4, 0, \"div\", 22);\n    i0.ɵɵtemplate(25, AdminPanelComponent_div_28_div_25_Template, 5, 0, \"div\", 23);\n    i0.ɵɵtemplate(26, AdminPanelComponent_div_28_div_26_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.newUsername);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.newUserPassword);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", !ctx_r3.newUsername.trim() || !ctx_r3.newUserPassword.trim());\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"Users (\", ctx_r3.users.length, \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.loading && ctx_r3.users.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.loading && ctx_r3.users.length > 0);\n  }\n}\n\nfunction AdminPanelComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r70 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_29_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r69 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r69.closeUserModal());\n    });\n    i0.ɵɵelementStart(1, \"mat-card\", 74);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_29_Template_mat_card_click_1_listener($event) {\n      return $event.stopPropagation();\n    });\n    i0.ɵɵelementStart(2, \"mat-card-header\", 75)(3, \"div\", 3)(4, \"mat-icon\", 76);\n    i0.ɵɵtext(5, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-card-title\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_29_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r72 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r72.closeUserModal());\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"close\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"mat-card-content\", 77)(12, \"div\", 78)(13, \"div\", 79)(14, \"span\", 80);\n    i0.ɵɵtext(15, \"Username:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 81);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 79)(19, \"span\", 80);\n    i0.ɵɵtext(20, \"Status:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 81);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 79)(24, \"span\", 80);\n    i0.ɵɵtext(25, \"Last Seen:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 81);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 79)(29, \"span\", 80);\n    i0.ɵɵtext(30, \"Password Status:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"mat-chip\");\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"div\", 82)(34, \"mat-card\", 83)(35, \"mat-card-header\")(36, \"mat-card-title\");\n    i0.ɵɵtext(37, \"Set Password\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"mat-card-content\")(39, \"mat-form-field\", 84)(40, \"mat-label\");\n    i0.ɵɵtext(41, \"New Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"input\", 85);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminPanelComponent_div_29_Template_input_ngModelChange_42_listener($event) {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r73 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r73.newPassword = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_29_Template_button_click_43_listener() {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r74 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r74.setUserPassword());\n    });\n    i0.ɵɵelementStart(44, \"mat-icon\");\n    i0.ɵɵtext(45, \"lock\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(46, \" Set Password \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(47, \"mat-card\", 83)(48, \"mat-card-header\")(49, \"mat-card-title\");\n    i0.ɵɵtext(50, \"Reset Password\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(51, \"mat-card-content\")(52, \"mat-form-field\", 84)(53, \"mat-label\");\n    i0.ɵɵtext(54, \"New Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"input\", 85);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminPanelComponent_div_29_Template_input_ngModelChange_55_listener($event) {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r75 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r75.resetPassword = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(56, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function AdminPanelComponent_div_29_Template_button_click_56_listener() {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r76 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r76.resetUserPassword());\n    });\n    i0.ɵɵelementStart(57, \"mat-icon\");\n    i0.ɵɵtext(58, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(59, \" Reset Password \");\n    i0.ɵɵelementEnd()()()()()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"Manage User: \", ctx_r4.selectedUser == null ? null : ctx_r4.selectedUser.username, \"\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r4.selectedUser == null ? null : ctx_r4.selectedUser.username);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r4.selectedUser == null ? null : ctx_r4.selectedUser.online_status) ? \"Online\" : \"Offline\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r4.formatDate((ctx_r4.selectedUser == null ? null : ctx_r4.selectedUser.last_seen) || \"\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap((ctx_r4.selectedUser == null ? null : ctx_r4.selectedUser.hasPassword) ? \"active-chip\" : \"inactive-chip\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r4.selectedUser == null ? null : ctx_r4.selectedUser.hasPassword) ? \"Has Password\" : \"No Password\", \" \");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.newPassword);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", !ctx_r4.newPassword.trim());\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.resetPassword);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", !ctx_r4.resetPassword.trim());\n  }\n}\n\nexport class AdminPanelComponent {\n  constructor(apiService) {\n    this.apiService = apiService;\n    this.currentUser = null;\n    this.onClose = new EventEmitter();\n    this.activeTab = 'whitelist';\n    this.activeTabIndex = 0;\n    this.allowedUsers = [];\n    this.inviteCodes = [];\n    this.groups = [];\n    this.selectedGroup = null;\n    this.groupUsers = [];\n    this.users = [];\n    this.newUsername = '';\n    this.newUserPassword = '';\n    this.newGroupName = '';\n    this.newGroupDescription = '';\n    this.newInviteConfig = {\n      maxUses: 1,\n      expiresInHours: 24,\n      customCode: ''\n    };\n    this.loading = false;\n    this.copiedCode = '';\n    this.showUserModal = false;\n    this.selectedUser = null;\n    this.newPassword = '';\n    this.resetPassword = '';\n  }\n\n  ngOnInit() {\n    this.loadData();\n  }\n\n  ngOnDestroy() {// Cleanup if needed\n  }\n\n  loadData() {\n    if (this.activeTab === 'whitelist') {\n      this.fetchAllowedUsers();\n    } else if (this.activeTab === 'invites') {\n      this.fetchInviteCodes();\n    } else if (this.activeTab === 'groups') {\n      this.fetchGroups();\n    } else if (this.activeTab === 'users') {\n      this.fetchUsers();\n    }\n  }\n\n  onTabChange(tabIndex) {\n    const tabs = ['whitelist', 'invites', 'groups', 'users'];\n    this.activeTab = tabs[tabIndex];\n    this.activeTabIndex = tabIndex;\n    this.loadData();\n  }\n\n  onGroupSelect(group) {\n    this.selectedGroup = group;\n    this.fetchGroupUsers(group.id);\n  }\n\n  fetchAllowedUsers() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this.currentUser) return;\n\n      try {\n        _this.loading = true;\n        _this.allowedUsers = (yield _this.apiService.getAllowedUsers(_this.currentUser).toPromise()) || [];\n      } catch (error) {\n        console.error('Failed to fetch allowed users:', error);\n      } finally {\n        _this.loading = false;\n      }\n    })();\n  }\n\n  fetchInviteCodes() {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this2.currentUser) return;\n\n      try {\n        _this2.loading = true;\n        _this2.inviteCodes = (yield _this2.apiService.getInviteCodes(_this2.currentUser).toPromise()) || [];\n      } catch (error) {\n        console.error('Failed to fetch invite codes:', error);\n      } finally {\n        _this2.loading = false;\n      }\n    })();\n  }\n\n  fetchGroups() {\n    var _this3 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this3.currentUser) return;\n\n      try {\n        _this3.loading = true;\n        _this3.groups = (yield _this3.apiService.getGroups(_this3.currentUser).toPromise()) || [];\n      } catch (error) {\n        console.error('Failed to fetch groups:', error);\n      } finally {\n        _this3.loading = false;\n      }\n    })();\n  }\n\n  fetchGroupUsers(groupId) {\n    var _this4 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this4.currentUser) return;\n\n      try {\n        _this4.groupUsers = (yield _this4.apiService.getGroupUsers(_this4.currentUser, groupId).toPromise()) || [];\n      } catch (error) {\n        console.error('Failed to fetch group users:', error);\n      }\n    })();\n  }\n\n  addUserToWhitelist() {\n    var _this5 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this5.newUsername.trim() || !_this5.currentUser) return;\n\n      try {\n        yield _this5.apiService.addAllowedUser(_this5.currentUser, _this5.newUsername.trim()).toPromise();\n        _this5.newUsername = '';\n\n        _this5.fetchAllowedUsers();\n      } catch (error) {\n        alert(error.error?.error || error.message || 'Failed to add user');\n      }\n    })();\n  }\n\n  removeUserFromWhitelist(username) {\n    var _this6 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this6.currentUser) return;\n\n      try {\n        yield _this6.apiService.removeAllowedUser(_this6.currentUser, username).toPromise();\n\n        _this6.fetchAllowedUsers();\n      } catch (error) {\n        alert(error.error?.error || error.message || 'Failed to remove user');\n      }\n    })();\n  }\n\n  createInviteCode() {\n    var _this7 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this7.currentUser) return;\n\n      try {\n        const result = yield _this7.apiService.createInviteCode(_this7.currentUser, _this7.newInviteConfig).toPromise();\n        _this7.newInviteConfig = {\n          maxUses: 1,\n          expiresInHours: 24,\n          customCode: ''\n        };\n\n        _this7.fetchInviteCodes(); // Auto-copy the new code\n\n\n        if (result?.code) {\n          navigator.clipboard.writeText(result.code);\n          _this7.copiedCode = result.code;\n          setTimeout(() => _this7.copiedCode = '', 3000);\n        }\n      } catch (error) {\n        alert(error.error?.error || error.message || 'Failed to create invite code');\n      }\n    })();\n  }\n\n  deactivateInviteCode(code) {\n    var _this8 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this8.currentUser) return;\n\n      try {\n        yield _this8.apiService.deactivateInviteCode(_this8.currentUser, code).toPromise();\n\n        _this8.fetchInviteCodes();\n      } catch (error) {\n        alert(error.error?.error || error.message || 'Failed to deactivate invite code');\n      }\n    })();\n  }\n\n  createGroup() {\n    var _this9 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this9.newGroupName.trim() || !_this9.currentUser) return;\n\n      try {\n        yield _this9.apiService.createGroup(_this9.currentUser, _this9.newGroupName.trim(), _this9.newGroupDescription.trim() || undefined).toPromise();\n        _this9.newGroupName = '';\n        _this9.newGroupDescription = '';\n\n        _this9.fetchGroups();\n      } catch (error) {\n        alert(error.error?.error || error.message || 'Failed to create group');\n      }\n    })();\n  }\n\n  deleteGroup(groupId) {\n    var _this0 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!confirm('Are you sure you want to delete this group?') || !_this0.currentUser) return;\n\n      try {\n        yield _this0.apiService.deleteGroup(_this0.currentUser, groupId).toPromise();\n\n        _this0.fetchGroups();\n\n        if (_this0.selectedGroup && _this0.selectedGroup.id === groupId) {\n          _this0.selectedGroup = null;\n        }\n      } catch (error) {\n        alert(error.error?.error || error.message || 'Failed to delete group');\n      }\n    })();\n  }\n\n  grantUserAccess(username) {\n    var _this1 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this1.selectedGroup || !_this1.currentUser) return;\n\n      try {\n        yield _this1.apiService.grantUserAccess(_this1.currentUser, _this1.selectedGroup.id, username).toPromise();\n\n        _this1.fetchGroupUsers(_this1.selectedGroup.id);\n      } catch (error) {\n        alert(error.error?.error || error.message || 'Failed to grant user access');\n      }\n    })();\n  }\n\n  revokeUserAccess(username) {\n    var _this10 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this10.selectedGroup || !_this10.currentUser) return;\n\n      try {\n        yield _this10.apiService.revokeUserAccess(_this10.currentUser, _this10.selectedGroup.id, username).toPromise();\n\n        _this10.fetchGroupUsers(_this10.selectedGroup.id);\n      } catch (error) {\n        alert(error.error?.error || error.message || 'Failed to revoke user access');\n      }\n    })();\n  }\n\n  copyToClipboard(text) {\n    navigator.clipboard.writeText(text);\n    this.copiedCode = text;\n    setTimeout(() => this.copiedCode = '', 3000);\n  }\n\n  formatDate(dateString) {\n    return new Date(dateString).toLocaleString();\n  }\n\n  onCloseClick() {\n    this.onClose.emit();\n  }\n\n  onCustomCodeInput(event) {\n    const target = event.target;\n    this.newInviteConfig.customCode = target.value.toUpperCase();\n  } // User management methods\n\n\n  fetchUsers() {\n    var _this11 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this11.currentUser) return;\n\n      try {\n        _this11.loading = true;\n        _this11.users = (yield _this11.apiService.getUsersWithPasswordStatus(_this11.currentUser).toPromise()) || [];\n      } catch (error) {\n        console.error('Failed to fetch users:', error);\n      } finally {\n        _this11.loading = false;\n      }\n    })();\n  }\n\n  createUser() {\n    var _this12 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this12.newUsername.trim() || !_this12.newUserPassword.trim() || !_this12.currentUser) return;\n\n      if (_this12.newUserPassword.length < 6) {\n        alert('Password must be at least 6 characters long');\n        return;\n      }\n\n      try {\n        yield _this12.apiService.createUser(_this12.currentUser, _this12.newUsername.trim(), _this12.newUserPassword.trim()).toPromise();\n        _this12.newUsername = '';\n        _this12.newUserPassword = '';\n\n        _this12.fetchUsers();\n      } catch (error) {\n        alert(error.error?.error || error.message || 'Failed to create user');\n      }\n    })();\n  }\n\n  openUserModal(user) {\n    this.selectedUser = user;\n    this.newPassword = '';\n    this.resetPassword = '';\n    this.showUserModal = true;\n  }\n\n  closeUserModal() {\n    this.showUserModal = false;\n    this.selectedUser = null;\n    this.newPassword = '';\n    this.resetPassword = '';\n  }\n\n  setUserPassword() {\n    var _this13 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this13.selectedUser || !_this13.newPassword.trim() || !_this13.currentUser) return;\n\n      if (_this13.newPassword.length < 6) {\n        alert('Password must be at least 6 characters long');\n        return;\n      }\n\n      try {\n        yield _this13.apiService.setUserPassword(_this13.currentUser, _this13.selectedUser.username, _this13.newPassword.trim()).toPromise();\n        alert('Password set successfully');\n\n        _this13.closeUserModal();\n\n        _this13.fetchUsers();\n      } catch (error) {\n        alert(error.error?.error || error.message || 'Failed to set password');\n      }\n    })();\n  }\n\n  resetUserPassword() {\n    var _this14 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this14.selectedUser || !_this14.resetPassword.trim() || !_this14.currentUser) return;\n\n      if (_this14.resetPassword.length < 6) {\n        alert('Password must be at least 6 characters long');\n        return;\n      }\n\n      try {\n        yield _this14.apiService.resetUserPassword(_this14.currentUser, _this14.selectedUser.username, _this14.resetPassword.trim()).toPromise();\n        alert('Password reset successfully');\n\n        _this14.closeUserModal();\n\n        _this14.fetchUsers();\n      } catch (error) {\n        alert(error.error?.error || error.message || 'Failed to reset password');\n      }\n    })();\n  }\n\n}\n\nAdminPanelComponent.ɵfac = function AdminPanelComponent_Factory(t) {\n  return new (t || AdminPanelComponent)(i0.ɵɵdirectiveInject(i1.ApiService));\n};\n\nAdminPanelComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: AdminPanelComponent,\n  selectors: [[\"app-admin-panel\"]],\n  inputs: {\n    currentUser: \"currentUser\"\n  },\n  outputs: {\n    onClose: \"onClose\"\n  },\n  decls: 30,\n  vars: 6,\n  consts: [[1, \"admin-panel-overlay\", 3, \"click\"], [1, \"admin-panel-container\", 3, \"click\"], [1, \"admin-panel-header\"], [1, \"header-content\"], [1, \"admin-icon\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Close\", 1, \"close-button\", 3, \"click\"], [1, \"admin-panel-content\"], [3, \"selectedIndex\", \"selectedIndexChange\"], [\"label\", \"Allowed Users\"], [\"matPrefix\", \"\"], [\"label\", \"Invite Codes\"], [\"label\", \"Groups\"], [\"label\", \"Users\"], [\"class\", \"tab-content\", 4, \"ngIf\"], [\"class\", \"modal-overlay\", 3, \"click\", 4, \"ngIf\"], [1, \"tab-content\"], [1, \"form-card\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"username-field\"], [\"matInput\", \"\", \"type\", \"text\", \"name\", \"newUsername\", \"placeholder\", \"Enter username\", 3, \"ngModel\", \"ngModelChange\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"add-button\", 3, \"disabled\", \"click\"], [1, \"list-card\"], [\"class\", \"loading-state\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"user-list\", 4, \"ngIf\"], [1, \"loading-state\"], [\"diameter\", \"40\"], [1, \"empty-state\"], [1, \"user-list\"], [\"class\", \"user-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"user-item\"], [1, \"user-info\"], [1, \"user-name\"], [1, \"user-details\"], [1, \"user-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Remove User\", \"class\", \"remove-button\", 3, \"click\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Remove User\", 1, \"remove-button\", 3, \"click\"], [\"appearance\", \"outline\", 1, \"field\"], [\"matInput\", \"\", \"type\", \"number\", \"min\", \"1\", \"placeholder\", \"1\", 3, \"ngModel\", \"ngModelChange\"], [\"matInput\", \"\", \"type\", \"number\", \"min\", \"1\", \"placeholder\", \"24\", 3, \"ngModel\", \"ngModelChange\"], [\"matInput\", \"\", \"type\", \"text\", \"placeholder\", \"Leave empty for auto-generated\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"add-button\", 3, \"click\"], [\"class\", \"invite-list\", 4, \"ngIf\"], [1, \"invite-list\"], [\"class\", \"invite-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"invite-item\"], [1, \"invite-info\"], [1, \"invite-code\"], [1, \"invite-details\"], [\"class\", \"invite-expiry\", 4, \"ngIf\"], [1, \"invite-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Copy Code\", 1, \"copy-button\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Deactivate\", \"class\", \"remove-button\", 3, \"click\", 4, \"ngIf\"], [1, \"invite-expiry\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Deactivate\", 1, \"remove-button\", 3, \"click\"], [\"matInput\", \"\", \"type\", \"text\", \"placeholder\", \"Enter group name\", 3, \"ngModel\", \"ngModelChange\"], [\"matInput\", \"\", \"type\", \"text\", \"placeholder\", \"Enter group description\", 3, \"ngModel\", \"ngModelChange\"], [\"class\", \"group-list\", 4, \"ngIf\"], [\"class\", \"list-card\", 4, \"ngIf\"], [1, \"group-list\"], [\"class\", \"group-item\", 3, \"selected\", 4, \"ngFor\", \"ngForOf\"], [1, \"group-item\"], [1, \"group-info\", 3, \"click\"], [1, \"group-name\"], [\"class\", \"group-description\", 4, \"ngIf\"], [1, \"group-details\"], [1, \"group-actions\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Delete Group\", 1, \"remove-button\", 3, \"click\"], [1, \"group-description\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Revoke Access\", 1, \"remove-button\", 3, \"click\"], [\"matInput\", \"\", \"type\", \"text\", \"placeholder\", \"Enter username\", 3, \"ngModel\", \"ngModelChange\"], [\"matInput\", \"\", \"type\", \"password\", \"placeholder\", \"Enter password\", 3, \"ngModel\", \"ngModelChange\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Manage User\", 1, \"manage-button\", 3, \"click\"], [1, \"modal-overlay\", 3, \"click\"], [1, \"modal-container\", 3, \"click\"], [1, \"modal-header\"], [1, \"modal-icon\"], [1, \"modal-content\"], [1, \"user-info-section\"], [1, \"info-item\"], [1, \"info-label\"], [1, \"info-value\"], [1, \"password-section\"], [1, \"password-card\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"password\", \"placeholder\", \"Enter new password\", 3, \"ngModel\", \"ngModelChange\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"full-width\", 3, \"disabled\", \"click\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", 1, \"full-width\", 3, \"disabled\", \"click\"]],\n  template: function AdminPanelComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵlistener(\"click\", function AdminPanelComponent_Template_div_click_0_listener() {\n        return ctx.onCloseClick();\n      });\n      i0.ɵɵelementStart(1, \"mat-card\", 1);\n      i0.ɵɵlistener(\"click\", function AdminPanelComponent_Template_mat_card_click_1_listener($event) {\n        return $event.stopPropagation();\n      });\n      i0.ɵɵelementStart(2, \"mat-card-header\", 2)(3, \"div\", 3)(4, \"mat-icon\", 4);\n      i0.ɵɵtext(5, \"admin_panel_settings\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(6, \"mat-card-title\");\n      i0.ɵɵtext(7, \"Admin Panel\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(8, \"button\", 5);\n      i0.ɵɵlistener(\"click\", function AdminPanelComponent_Template_button_click_8_listener() {\n        return ctx.onCloseClick();\n      });\n      i0.ɵɵelementStart(9, \"mat-icon\");\n      i0.ɵɵtext(10, \"close\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(11, \"mat-card-content\", 6)(12, \"mat-tab-group\", 7);\n      i0.ɵɵlistener(\"selectedIndexChange\", function AdminPanelComponent_Template_mat_tab_group_selectedIndexChange_12_listener($event) {\n        return ctx.activeTabIndex = $event;\n      })(\"selectedIndexChange\", function AdminPanelComponent_Template_mat_tab_group_selectedIndexChange_12_listener($event) {\n        return ctx.onTabChange($event);\n      });\n      i0.ɵɵelementStart(13, \"mat-tab\", 8)(14, \"mat-icon\", 9);\n      i0.ɵɵtext(15, \"people\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(16, \"mat-tab\", 10)(17, \"mat-icon\", 9);\n      i0.ɵɵtext(18, \"vpn_key\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(19, \"mat-tab\", 11)(20, \"mat-icon\", 9);\n      i0.ɵɵtext(21, \"group\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(22, \"mat-tab\", 12)(23, \"mat-icon\", 9);\n      i0.ɵɵtext(24, \"person\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵtemplate(25, AdminPanelComponent_div_25_Template, 23, 6, \"div\", 13);\n      i0.ɵɵtemplate(26, AdminPanelComponent_div_26_Template, 31, 7, \"div\", 13);\n      i0.ɵɵtemplate(27, AdminPanelComponent_div_27_Template, 28, 8, \"div\", 13);\n      i0.ɵɵtemplate(28, AdminPanelComponent_div_28_Template, 27, 7, \"div\", 13);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵtemplate(29, AdminPanelComponent_div_29_Template, 60, 11, \"div\", 14);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(12);\n      i0.ɵɵproperty(\"selectedIndex\", ctx.activeTabIndex);\n      i0.ɵɵadvance(13);\n      i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"whitelist\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"invites\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"groups\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"users\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showUserModal);\n    }\n  },\n  dependencies: [i2.NgForOf, i2.NgIf, i3.DefaultValueAccessor, i3.NumberValueAccessor, i3.NgControlStatus, i3.MinValidator, i3.NgModel, i4.MatCard, i4.MatCardHeader, i4.MatCardContent, i4.MatCardTitle, i5.MatFormField, i5.MatLabel, i5.MatPrefix, i6.MatInput, i7.MatButton, i8.MatIcon, i9.MatChip, i10.MatTooltip, i11.MatProgressSpinner, i12.MatTabGroup, i12.MatTab],\n  styles: [\".admin-panel-overlay[_ngcontent-%COMP%] {\\r\\n  position: fixed;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  right: 0;\\r\\n  bottom: 0;\\r\\n  background: rgba(0, 0, 0, 0.5);\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  z-index: 1000;\\r\\n}\\r\\n.admin-panel-container[_ngcontent-%COMP%] {\\r\\n  width: 90%;\\r\\n  max-width: 1200px;\\r\\n  max-height: 90vh;\\r\\n  overflow: hidden;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n}\\r\\n.admin-panel-header[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: space-between;\\r\\n  align-items: center;\\r\\n  padding: 16px;\\r\\n  border-bottom: 1px solid #e0e0e0;\\r\\n  background: #f8f9fa;\\r\\n}\\r\\n.header-content[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 12px;\\r\\n}\\r\\n.admin-icon[_ngcontent-%COMP%] {\\r\\n  color: #3f51b5;\\r\\n  font-size: 24px;\\r\\n  width: 24px;\\r\\n  height: 24px;\\r\\n}\\r\\n.close-button[_ngcontent-%COMP%] {\\r\\n  color: #666;\\r\\n  transition: all 0.2s ease;\\r\\n}\\r\\n.close-button[_ngcontent-%COMP%]:hover {\\r\\n  background: #e0e0e0;\\r\\n  color: #333;\\r\\n}\\r\\n.admin-panel-content[_ngcontent-%COMP%] {\\r\\n  flex: 1;\\r\\n  overflow-y: auto;\\r\\n  padding: 16px;\\r\\n}\\r\\n.tab-content[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 16px;\\r\\n}\\r\\n.form-card[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 16px;\\r\\n}\\r\\n.form-row[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: flex-end;\\r\\n  gap: 16px;\\r\\n}\\r\\n.username-field[_ngcontent-%COMP%] {\\r\\n  flex: 1;\\r\\n}\\r\\n.add-button[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 8px;\\r\\n}\\r\\n.list-card[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 16px;\\r\\n}\\r\\n.loading-state[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  padding: 32px;\\r\\n  color: #666;\\r\\n}\\r\\n.empty-state[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  padding: 32px;\\r\\n  color: #666;\\r\\n}\\r\\n.empty-state[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\r\\n  font-size: 48px;\\r\\n  width: 48px;\\r\\n  height: 48px;\\r\\n  margin-bottom: 16px;\\r\\n  opacity: 0.6;\\r\\n}\\r\\n.user-list[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 8px;\\r\\n}\\r\\n.user-item[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  justify-content: space-between;\\r\\n  align-items: center;\\r\\n  padding: 12px;\\r\\n  border: 1px solid #e0e0e0;\\r\\n  border-radius: 8px;\\r\\n  transition: all 0.2s ease;\\r\\n}\\r\\n.user-item[_ngcontent-%COMP%]:hover {\\r\\n  background: #f8f9fa;\\r\\n  border-color: #3f51b5;\\r\\n}\\r\\n.user-info[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  gap: 4px;\\r\\n}\\r\\n.user-name[_ngcontent-%COMP%] {\\r\\n  font-weight: 500;\\r\\n  font-size: 0.9rem;\\r\\n  color: #333;\\r\\n}\\r\\n.user-details[_ngcontent-%COMP%] {\\r\\n  font-size: 0.8rem;\\r\\n  color: #666;\\r\\n}\\r\\n.user-actions[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  gap: 8px;\\r\\n}\\r\\n.active-chip[_ngcontent-%COMP%] {\\r\\n  background: #4caf50;\\r\\n  color: white;\\r\\n}\\r\\n.inactive-chip[_ngcontent-%COMP%] {\\r\\n  background: #f44336;\\r\\n  color: white;\\r\\n}\\r\\n.remove-button[_ngcontent-%COMP%] {\\r\\n  color: #f44336;\\r\\n}\\r\\n.remove-button[_ngcontent-%COMP%]:hover {\\r\\n  background: rgba(244, 67, 54, 0.1);\\r\\n}\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  .admin-panel-container[_ngcontent-%COMP%] {\\r\\n    width: 95%;\\r\\n    max-height: 95vh;\\r\\n  }\\r\\n  \\r\\n  .form-row[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n    align-items: stretch;\\r\\n  }\\r\\n  \\r\\n  .add-button[_ngcontent-%COMP%] {\\r\\n    margin-bottom: 0;\\r\\n  }\\r\\n  \\r\\n  .user-item[_ngcontent-%COMP%] {\\r\\n    flex-direction: column;\\r\\n    align-items: flex-start;\\r\\n    gap: 8px;\\r\\n  }\\r\\n  \\r\\n  .user-actions[_ngcontent-%COMP%] {\\r\\n    width: 100%;\\r\\n    justify-content: space-between;\\r\\n  }\\r\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\"]\n});", "map": {"version": 3, "mappings": ";AAAA,SAAmCA,YAAnC,QAA0E,eAA1E;;;;;;;;;;;;;;;;;IC2EYC;IACEA;IACAA;IAAGA;IAAUA;;;;;;IAEfA,gCAAuE,CAAvE,EAAuE,UAAvE;IACYA;IAAcA;IACxBA;IAAGA;IAAqBA;;;;;;;;IAcpBA;IAEEA;MAAAA;MAAA;MAAA;MAAA,OAASA,iEAAT;IAA+C,CAA/C;IAKAA;IAAUA;IAAMA;;;;;;IAlBtBA,gCAAyD,CAAzD,EAAyD,KAAzD,EAAyD,EAAzD,EAAyD,CAAzD,EAAyD,MAAzD,EAAyD,EAAzD;IAE4BA;IAAmBA;IAC3CA;IACEA;IACFA;IAEFA,gCAA0B,CAA1B,EAA0B,UAA1B;IAEIA;IACFA;IACAA;IASFA;;;;;;IAlB0BA;IAAAA;IAEtBA;IAAAA;IAIQA;IAAAA;IACRA;IAAAA;IAGCA;IAAAA;;;;;;IAbTA;IACEA;IAsBFA;;;;;IAtBwBA;IAAAA;;;;;;;;IA/C9BA,gCAA2D,CAA3D,EAA2D,UAA3D,EAA2D,EAA3D,EAA2D,CAA3D,EAA2D,iBAA3D,EAA2D,CAA3D,EAA2D,gBAA3D;IAIsBA;IAAqBA;IAEvCA,yCAAkB,CAAlB,EAAkB,KAAlB,EAAkB,EAAlB,EAAkB,CAAlB,EAAkB,gBAAlB,EAAkB,EAAlB,EAAkB,CAAlB,EAAkB,WAAlB;IAGiBA;IAAQA;IACnBA;IAIEA;MAAAA;MAAA;MAAA;IAAA;IAJFA;IAQFA;IACEA;MAAAA;MAAA;MAAA,OAASA,4CAAT;IAA6B,CAA7B;IAMAA;IAAUA;IAAGA;IACbA;IACFA;IAMNA,sCAA4B,EAA5B,EAA4B,iBAA5B,EAA4B,EAA5B,EAA4B,gBAA5B;IAEoBA;IAAyCA;IAE3DA;IACEA;IAIAA;IAIAA;IAwBFA;;;;;IAxDQA;IAAAA;IAMFA;IAAAA;IAeYA;IAAAA;IAGVA;IAAAA;IAIAA;IAAAA;IAIAA;IAAAA;;;;;;IAsFNA;IACEA;IACAA;IAAGA;IAAUA;;;;;;IAEfA,gCAAsE,CAAtE,EAAsE,UAAtE;IACYA;IAAOA;IACjBA;IAAGA;IAAeA;;;;;;IAUdA;IACEA;IACFA;;;;;;IADEA;IAAAA;;;;;;;;IAeFA;IAEEA;MAAAA;MAAA;MAAA;MAAA,OAASA,2DAAT;IAAwC,CAAxC;IAKAA;IAAUA;IAAKA;;;;;;;;IA9BrBA,gCAA0D,CAA1D,EAA0D,KAA1D,EAA0D,EAA1D,EAA0D,CAA1D,EAA0D,MAA1D,EAA0D,EAA1D;IAE8BA;IAAeA;IACzCA;IACEA;IAEFA;IACAA;IAGFA;IACAA,gCAA4B,CAA5B,EAA4B,UAA5B;IAEIA;IACFA;IACAA;IACEA;MAAA;MAAA;MAAA;MAAA,OAASA,sDAAT;IAAmC,CAAnC;IAKAA;IAAUA;IAAyDA;IAErEA;IASFA;;;;;;IA9B4BA;IAAAA;IAExBA;IAAAA;IAGKA;IAAAA;IAKGA;IAAAA;IACRA;IAAAA;IAQUA;IAAAA;IAGTA;IAAAA;;;;;;IAzBTA;IACEA;IAkCFA;;;;;IAlCwBA;IAAAA;;;;;;;;IAlE9BA,gCAAyD,CAAzD,EAAyD,UAAzD,EAAyD,EAAzD,EAAyD,CAAzD,EAAyD,iBAAzD,EAAyD,CAAzD,EAAyD,gBAAzD;IAIsBA;IAAkBA;IAEpCA,yCAAkB,CAAlB,EAAkB,KAAlB,EAAkB,EAAlB,EAAkB,CAAlB,EAAkB,gBAAlB,EAAkB,EAAlB,EAAkB,CAAlB,EAAkB,WAAlB;IAGiBA;IAAQA;IACnBA;IAGEA;MAAAA;MAAA;MAAA,OAAaA,wDAAb;IACb,CADa;IAHFA;IAQFA,4CAAmD,EAAnD,EAAmD,WAAnD;IACaA;IAAkBA;IAC7BA;IAGEA;MAAAA;MAAA;MAAA,OAAaA,+DAAb;IACb,CADa;IAHFA;IAQFA,4CAAmD,EAAnD,EAAmD,WAAnD;IACaA;IAAsBA;IACjCA;IAGEA;MAAAA;MAAA;MAAA,OAAaA,2DAAb;IACb,CADa,EAAwC,OAAxC,EAAwC;MAAAA;MAAA;MAAA,OAC/BA,iDAD+B;IACN,CADlC;IAHFA;IAQFA;IACEA;MAAAA;MAAA;MAAA,OAASA,0CAAT;IAA2B,CAA3B;IAKAA;IAAUA;IAAGA;IACbA;IACFA;IAMNA,sCAA4B,EAA5B,EAA4B,iBAA5B,EAA4B,EAA5B,EAA4B,gBAA5B;IAEoBA;IAAuCA;IAEzDA;IACEA;IAIAA;IAIAA;IAoCFA;;;;;IAxFQA;IAAAA;IAUAA;IAAAA;IAUAA;IAAAA;IAqBUA;IAAAA;IAGVA;IAAAA;IAIAA;IAAAA;IAIAA;IAAAA;;;;;;IAuFNA;IACEA;IACAA;IAAGA;IAAUA;;;;;;IAEfA,gCAAiE,CAAjE,EAAiE,UAAjE;IACYA;IAAKA;IACfA;IAAGA;IAASA;;;;;;IAMRA;IAA0DA;IAAuBA;;;;;IAAvBA;IAAAA;;;;;;;;IAH9DA,gCAAuG,CAAvG,EAAuG,KAAvG,EAAuG,EAAvG;IAC0BA;MAAA;MAAA;MAAA;MAAA,OAASA,gDAAT;IAA6B,CAA7B;IACtBA;IAAyBA;IAAgBA;IACzCA;IACAA;IACEA;IACFA;IAEFA,gCAA2B,CAA3B,EAA2B,QAA3B,EAA2B,EAA3B;IAEIA;MAAA;MAAA;MAAA;MAAA,OAASA,iDAAT;IAA8B,CAA9B;IAKAA;IAAUA;IAAMA;;;;;;IAf+BA;IAExBA;IAAAA;IAClBA;IAAAA;IAELA;IAAAA;;;;;;IANRA;IACEA;IAmBFA;;;;;IAnByBA;IAAAA;;;;;;IA6BzBA;IACEA;IACAA;IAAGA;IAAUA;;;;;;IAEfA,gCAAqE,CAArE,EAAqE,UAArE;IACYA;IAAcA;IACxBA;IAAGA;IAAsBA;;;;;;;;IAGzBA,gCAAuD,CAAvD,EAAuD,KAAvD,EAAuD,EAAvD,EAAuD,CAAvD,EAAuD,MAAvD,EAAuD,EAAvD;IAE4BA;IAAmBA;IAC3CA;IACEA;IACFA;IAEFA,gCAA0B,CAA1B,EAA0B,QAA1B,EAA0B,EAA1B;IAEIA;MAAA;MAAA;MAAA;MAAA,OAASA,2DAAT;IAAwC,CAAxC;IAKAA;IAAUA;IAAaA;;;;;;IAZDA;IAAAA;IAEtBA;IAAAA;;;;;;IALRA;IACEA;IAkBFA;;;;;IAlBwBA;IAAAA;;;;;;IAd5BA,qCAAkD,CAAlD,EAAkD,iBAAlD,EAAkD,CAAlD,EAAkD,gBAAlD;IAEoBA;IAAsCA;IAExDA;IACEA;IAIAA;IAIAA;IAoBFA;;;;;IA/BkBA;IAAAA;IAGVA;IAAAA;IAIAA;IAAAA;IAIAA;IAAAA;;;;;;;;IA5FZA,gCAAwD,CAAxD,EAAwD,UAAxD,EAAwD,EAAxD,EAAwD,CAAxD,EAAwD,iBAAxD,EAAwD,CAAxD,EAAwD,gBAAxD;IAIsBA;IAAYA;IAE9BA,yCAAkB,CAAlB,EAAkB,KAAlB,EAAkB,EAAlB,EAAkB,CAAlB,EAAkB,gBAAlB,EAAkB,EAAlB,EAAkB,CAAlB,EAAkB,WAAlB;IAGiBA;IAAUA;IACrBA;IAGEA;MAAAA;MAAA;MAAA;IAAA;IAHFA;IAOFA,4CAAmD,EAAnD,EAAmD,WAAnD;IACaA;IAAsBA;IACjCA;IAGEA;MAAAA;MAAA;MAAA;IAAA;IAHFA;IAOFA;IACEA;MAAAA;MAAA;MAAA,OAASA,qCAAT;IAAsB,CAAtB;IAMAA;IAAUA;IAAGA;IACbA;IACFA;IAMNA,sCAA4B,EAA5B,EAA4B,iBAA5B,EAA4B,EAA5B,EAA4B,gBAA5B;IAEoBA;IAA4BA;IAE9CA;IACEA;IAIAA;IAIAA;IAqBFA;IAIFA;IAmCFA;;;;;IArGYA;IAAAA;IASAA;IAAAA;IAMFA;IAAAA;IAeYA;IAAAA;IAGVA;IAAAA;IAIAA;IAAAA;IAIAA;IAAAA;IAyBCA;IAAAA;;;;;;IAoFPA;IACEA;IACAA;IAAGA;IAAUA;;;;;;IAEfA,gCAAgE,CAAhE,EAAgE,UAAhE;IACYA;IAAMA;IAChBA;IAAGA;IAAQA;;;;;;;;IAGXA,gCAAkD,CAAlD,EAAkD,KAAlD,EAAkD,EAAlD,EAAkD,CAAlD,EAAkD,MAAlD,EAAkD,EAAlD;IAE4BA;IAAmBA;IAC3CA;IACEA;IAEFA;IAEFA,gCAA0B,CAA1B,EAA0B,UAA1B;IAEIA;IACFA;IACAA;IACEA;MAAA;MAAA;MAAA;MAAA,OAASA,+CAAT;IAA4B,CAA5B;IAKAA;IAAUA;IAAQA;;;;;;IAhBIA;IAAAA;IAEtBA;IAAAA;IAKQA;IAAAA;IACRA;IAAAA;;;;;;IAXRA;IACEA;IAsBFA;;;;;IAtBwBA;IAAAA;;;;;;;;IAvD9BA,gCAAuD,CAAvD,EAAuD,UAAvD,EAAuD,EAAvD,EAAuD,CAAvD,EAAuD,iBAAvD,EAAuD,CAAvD,EAAuD,gBAAvD;IAIsBA;IAAWA;IAE7BA,yCAAkB,CAAlB,EAAkB,KAAlB,EAAkB,EAAlB,EAAkB,CAAlB,EAAkB,gBAAlB,EAAkB,EAAlB,EAAkB,CAAlB,EAAkB,WAAlB;IAGiBA;IAAQA;IACnBA;IAGEA;MAAAA;MAAA;MAAA;IAAA;IAHFA;IAOFA,4CAAmD,EAAnD,EAAmD,WAAnD;IACaA;IAAQA;IACnBA;IAGEA;MAAAA;MAAA;MAAA;IAAA;IAHFA;IAOFA;IACEA;MAAAA;MAAA;MAAA,OAASA,oCAAT;IAAqB,CAArB;IAMAA;IAAUA;IAAGA;IACbA;IACFA;IAMNA,sCAA4B,EAA5B,EAA4B,iBAA5B,EAA4B,EAA5B,EAA4B,gBAA5B;IAEoBA;IAA0BA;IAE5CA;IACEA;IAIAA;IAIAA;IAwBFA;;;;;IAjEQA;IAAAA;IASAA;IAAAA;IAMFA;IAAAA;IAeYA;IAAAA;IAGVA;IAAAA;IAIAA;IAAAA;IAIAA;IAAAA;;;;;;;;IAgClBA;IAAiDA;MAAAA;MAAA;MAAA,OAASA,wCAAT;IAAyB,CAAzB;IAC/CA;IAAkCA;MAAA,OAASC,wBAAT;IAAiC,CAAjC;IAChCD,4CAAsC,CAAtC,EAAsC,KAAtC,EAAsC,CAAtC,EAAsC,CAAtC,EAAsC,UAAtC,EAAsC,EAAtC;IAEiCA;IAAMA;IACnCA;IAAgBA;IAAyCA;IAE3DA;IACEA;MAAAA;MAAA;MAAA,OAASA,wCAAT;IAAyB,CAAzB;IAKAA;IAAUA;IAAKA;IAInBA,8CAAwC,EAAxC,EAAwC,KAAxC,EAAwC,EAAxC,EAAwC,EAAxC,EAAwC,KAAxC,EAAwC,EAAxC,EAAwC,EAAxC,EAAwC,MAAxC,EAAwC,EAAxC;IAG+BA;IAASA;IAClCA;IAAyBA;IAA4BA;IAEvDA,iCAAuB,EAAvB,EAAuB,MAAvB,EAAuB,EAAvB;IAC2BA;IAAOA;IAChCA;IAAyBA;IAAwDA;IAEnFA,iCAAuB,EAAvB,EAAuB,MAAvB,EAAuB,EAAvB;IAC2BA;IAAUA;IACnCA;IAAyBA;IAA+CA;IAE1EA,iCAAuB,EAAvB,EAAuB,MAAvB,EAAuB,EAAvB;IAC2BA;IAAgBA;IACzCA;IACEA;IACFA;IAIJA,iCAA8B,EAA9B,EAA8B,UAA9B,EAA8B,EAA9B,EAA8B,EAA9B,EAA8B,iBAA9B,EAA8B,EAA9B,EAA8B,gBAA9B;IAGsBA;IAAYA;IAE9BA,0CAAkB,EAAlB,EAAkB,gBAAlB,EAAkB,EAAlB,EAAkB,EAAlB,EAAkB,WAAlB;IAEeA;IAAYA;IACvBA;IAGEA;MAAAA;MAAA;MAAA;IAAA;IAHFA;IAOFA;IACEA;MAAAA;MAAA;MAAA,OAASA,yCAAT;IAA0B,CAA1B;IAMAA;IAAUA;IAAIA;IACdA;IACFA;IAIJA,sCAAgC,EAAhC,EAAgC,iBAAhC,EAAgC,EAAhC,EAAgC,gBAAhC;IAEoBA;IAAcA;IAEhCA,0CAAkB,EAAlB,EAAkB,gBAAlB,EAAkB,EAAlB,EAAkB,EAAlB,EAAkB,WAAlB;IAEeA;IAAYA;IACvBA;IAGEA;MAAAA;MAAA;MAAA;IAAA;IAHFA;IAOFA;IACEA;MAAAA;MAAA;MAAA,OAASA,2CAAT;IAA4B,CAA5B;IAMAA;IAAUA;IAAOA;IACjBA;IACFA;;;;;IArFYA;IAAAA;IAgBWA;IAAAA;IAIAA;IAAAA;IAIAA;IAAAA;IAIfA;IAAAA;IACRA;IAAAA;IAgBIA;IAAAA;IAMFA;IAAAA;IAqBEA;IAAAA;IAMFA;IAAAA;;;;ADhfd,OAAM,MAAOE,mBAAP,CAA0B;EA4B9BC,YAAoBC,UAApB,EAA0C;IAAtB;IA3BX,mBAA6B,IAA7B;IACC,eAAU,IAAIL,YAAJ,EAAV;IAEV,iBAAY,WAAZ;IACA,sBAAiB,CAAjB;IACA,oBAA8B,EAA9B;IACA,mBAA4B,EAA5B;IACA,cAAkB,EAAlB;IACA,qBAA8B,IAA9B;IACA,kBAA0B,EAA1B;IACA,aAA4B,EAA5B;IACA,mBAAc,EAAd;IACA,uBAAkB,EAAlB;IACA,oBAAe,EAAf;IACA,2BAAsB,EAAtB;IACA,uBAAkB;MAChBM,OAAO,EAAE,CADO;MAEhBC,cAAc,EAAE,EAFA;MAGhBC,UAAU,EAAE;IAHI,CAAlB;IAKA,eAAU,KAAV;IACA,kBAAa,EAAb;IACA,qBAAgB,KAAhB;IACA,oBAAwC,IAAxC;IACA,mBAAc,EAAd;IACA,qBAAgB,EAAhB;EAE8C;;EAE9CC,QAAQ;IACN,KAAKC,QAAL;EACD;;EAEDC,WAAW,IACT;EACD;;EAEDD,QAAQ;IACN,IAAI,KAAKE,SAAL,KAAmB,WAAvB,EAAoC;MAClC,KAAKC,iBAAL;IACD,CAFD,MAEO,IAAI,KAAKD,SAAL,KAAmB,SAAvB,EAAkC;MACvC,KAAKE,gBAAL;IACD,CAFM,MAEA,IAAI,KAAKF,SAAL,KAAmB,QAAvB,EAAiC;MACtC,KAAKG,WAAL;IACD,CAFM,MAEA,IAAI,KAAKH,SAAL,KAAmB,OAAvB,EAAgC;MACrC,KAAKI,UAAL;IACD;EACF;;EAEDC,WAAW,CAACC,QAAD,EAAiB;IAC1B,MAAMC,IAAI,GAAG,CAAC,WAAD,EAAc,SAAd,EAAyB,QAAzB,EAAmC,OAAnC,CAAb;IACA,KAAKP,SAAL,GAAiBO,IAAI,CAACD,QAAD,CAArB;IACA,KAAKE,cAAL,GAAsBF,QAAtB;IACA,KAAKR,QAAL;EACD;;EAEDW,aAAa,CAACC,KAAD,EAAa;IACxB,KAAKC,aAAL,GAAqBD,KAArB;IACA,KAAKE,eAAL,CAAqBF,KAAK,CAACG,EAA3B;EACD;;EAEaZ,iBAAiB;IAAA;;IAAA;MAC7B,IAAI,CAAC,KAAI,CAACa,WAAV,EAAuB;;MAEvB,IAAI;QACF,KAAI,CAACC,OAAL,GAAe,IAAf;QACA,KAAI,CAACC,YAAL,GAAoB,OAAM,KAAI,CAACvB,UAAL,CAAgBwB,eAAhB,CAAgC,KAAI,CAACH,WAArC,EAAkDI,SAAlD,EAAN,KAAuE,EAA3F;MACD,CAHD,CAGE,OAAOC,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,gCAAd,EAAgDA,KAAhD;MACD,CALD,SAKU;QACR,KAAI,CAACJ,OAAL,GAAe,KAAf;MACD;IAV4B;EAW9B;;EAEab,gBAAgB;IAAA;;IAAA;MAC5B,IAAI,CAAC,MAAI,CAACY,WAAV,EAAuB;;MAEvB,IAAI;QACF,MAAI,CAACC,OAAL,GAAe,IAAf;QACA,MAAI,CAACM,WAAL,GAAmB,OAAM,MAAI,CAAC5B,UAAL,CAAgB6B,cAAhB,CAA+B,MAAI,CAACR,WAApC,EAAiDI,SAAjD,EAAN,KAAsE,EAAzF;MACD,CAHD,CAGE,OAAOC,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,+BAAd,EAA+CA,KAA/C;MACD,CALD,SAKU;QACR,MAAI,CAACJ,OAAL,GAAe,KAAf;MACD;IAV2B;EAW7B;;EAEaZ,WAAW;IAAA;;IAAA;MACvB,IAAI,CAAC,MAAI,CAACW,WAAV,EAAuB;;MAEvB,IAAI;QACF,MAAI,CAACC,OAAL,GAAe,IAAf;QACA,MAAI,CAACQ,MAAL,GAAc,OAAM,MAAI,CAAC9B,UAAL,CAAgB+B,SAAhB,CAA0B,MAAI,CAACV,WAA/B,EAA4CI,SAA5C,EAAN,KAAiE,EAA/E;MACD,CAHD,CAGE,OAAOC,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,yBAAd,EAAyCA,KAAzC;MACD,CALD,SAKU;QACR,MAAI,CAACJ,OAAL,GAAe,KAAf;MACD;IAVsB;EAWxB;;EAEaH,eAAe,CAACa,OAAD,EAAgB;IAAA;;IAAA;MAC3C,IAAI,CAAC,MAAI,CAACX,WAAV,EAAuB;;MAEvB,IAAI;QACF,MAAI,CAACY,UAAL,GAAkB,OAAM,MAAI,CAACjC,UAAL,CAAgBkC,aAAhB,CAA8B,MAAI,CAACb,WAAnC,EAAgDW,OAAhD,EAAyDP,SAAzD,EAAN,KAA8E,EAAhG;MACD,CAFD,CAEE,OAAOC,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,8BAAd,EAA8CA,KAA9C;MACD;IAP0C;EAQ5C;;EAEKS,kBAAkB;IAAA;;IAAA;MACtB,IAAI,CAAC,MAAI,CAACC,WAAL,CAAiBC,IAAjB,EAAD,IAA4B,CAAC,MAAI,CAAChB,WAAtC,EAAmD;;MAEnD,IAAI;QACF,MAAM,MAAI,CAACrB,UAAL,CAAgBsC,cAAhB,CAA+B,MAAI,CAACjB,WAApC,EAAiD,MAAI,CAACe,WAAL,CAAiBC,IAAjB,EAAjD,EAA0EZ,SAA1E,EAAN;QACA,MAAI,CAACW,WAAL,GAAmB,EAAnB;;QACA,MAAI,CAAC5B,iBAAL;MACD,CAJD,CAIE,OAAOkB,KAAP,EAAmB;QACnBa,KAAK,CAACb,KAAK,CAACA,KAAN,EAAaA,KAAb,IAAsBA,KAAK,CAACc,OAA5B,IAAuC,oBAAxC,CAAL;MACD;IATqB;EAUvB;;EAEKC,uBAAuB,CAACC,QAAD,EAAiB;IAAA;;IAAA;MAC5C,IAAI,CAAC,MAAI,CAACrB,WAAV,EAAuB;;MAEvB,IAAI;QACF,MAAM,MAAI,CAACrB,UAAL,CAAgB2C,iBAAhB,CAAkC,MAAI,CAACtB,WAAvC,EAAoDqB,QAApD,EAA8DjB,SAA9D,EAAN;;QACA,MAAI,CAACjB,iBAAL;MACD,CAHD,CAGE,OAAOkB,KAAP,EAAmB;QACnBa,KAAK,CAACb,KAAK,CAACA,KAAN,EAAaA,KAAb,IAAsBA,KAAK,CAACc,OAA5B,IAAuC,uBAAxC,CAAL;MACD;IAR2C;EAS7C;;EAEKI,gBAAgB;IAAA;;IAAA;MACpB,IAAI,CAAC,MAAI,CAACvB,WAAV,EAAuB;;MAEvB,IAAI;QACF,MAAMwB,MAAM,SAAS,MAAI,CAAC7C,UAAL,CAAgB4C,gBAAhB,CAAiC,MAAI,CAACvB,WAAtC,EAAmD,MAAI,CAACyB,eAAxD,EAAyErB,SAAzE,EAArB;QACA,MAAI,CAACqB,eAAL,GAAuB;UACrB7C,OAAO,EAAE,CADY;UAErBC,cAAc,EAAE,EAFK;UAGrBC,UAAU,EAAE;QAHS,CAAvB;;QAKA,MAAI,CAACM,gBAAL,GAPE,CASF;;;QACA,IAAIoC,MAAM,EAAEE,IAAZ,EAAkB;UAChBC,SAAS,CAACC,SAAV,CAAoBC,SAApB,CAA8BL,MAAM,CAACE,IAArC;UACA,MAAI,CAACI,UAAL,GAAkBN,MAAM,CAACE,IAAzB;UACAK,UAAU,CAAC,MAAM,MAAI,CAACD,UAAL,GAAkB,EAAzB,EAA6B,IAA7B,CAAV;QACD;MACF,CAfD,CAeE,OAAOzB,KAAP,EAAmB;QACnBa,KAAK,CAACb,KAAK,CAACA,KAAN,EAAaA,KAAb,IAAsBA,KAAK,CAACc,OAA5B,IAAuC,8BAAxC,CAAL;MACD;IApBmB;EAqBrB;;EAEKa,oBAAoB,CAACN,IAAD,EAAa;IAAA;;IAAA;MACrC,IAAI,CAAC,MAAI,CAAC1B,WAAV,EAAuB;;MAEvB,IAAI;QACF,MAAM,MAAI,CAACrB,UAAL,CAAgBqD,oBAAhB,CAAqC,MAAI,CAAChC,WAA1C,EAAuD0B,IAAvD,EAA6DtB,SAA7D,EAAN;;QACA,MAAI,CAAChB,gBAAL;MACD,CAHD,CAGE,OAAOiB,KAAP,EAAmB;QACnBa,KAAK,CAACb,KAAK,CAACA,KAAN,EAAaA,KAAb,IAAsBA,KAAK,CAACc,OAA5B,IAAuC,kCAAxC,CAAL;MACD;IARoC;EAStC;;EAEKc,WAAW;IAAA;;IAAA;MACf,IAAI,CAAC,MAAI,CAACC,YAAL,CAAkBlB,IAAlB,EAAD,IAA6B,CAAC,MAAI,CAAChB,WAAvC,EAAoD;;MAEpD,IAAI;QACF,MAAM,MAAI,CAACrB,UAAL,CAAgBsD,WAAhB,CACJ,MAAI,CAACjC,WADD,EAEJ,MAAI,CAACkC,YAAL,CAAkBlB,IAAlB,EAFI,EAGJ,MAAI,CAACmB,mBAAL,CAAyBnB,IAAzB,MAAmCoB,SAH/B,EAIJhC,SAJI,EAAN;QAKA,MAAI,CAAC8B,YAAL,GAAoB,EAApB;QACA,MAAI,CAACC,mBAAL,GAA2B,EAA3B;;QACA,MAAI,CAAC9C,WAAL;MACD,CATD,CASE,OAAOgB,KAAP,EAAmB;QACnBa,KAAK,CAACb,KAAK,CAACA,KAAN,EAAaA,KAAb,IAAsBA,KAAK,CAACc,OAA5B,IAAuC,wBAAxC,CAAL;MACD;IAdc;EAehB;;EAEKkB,WAAW,CAAC1B,OAAD,EAAgB;IAAA;;IAAA;MAC/B,IAAI,CAAC2B,OAAO,CAAC,6CAAD,CAAR,IAA2D,CAAC,MAAI,CAACtC,WAArE,EAAkF;;MAElF,IAAI;QACF,MAAM,MAAI,CAACrB,UAAL,CAAgB0D,WAAhB,CAA4B,MAAI,CAACrC,WAAjC,EAA8CW,OAA9C,EAAuDP,SAAvD,EAAN;;QACA,MAAI,CAACf,WAAL;;QACA,IAAI,MAAI,CAACQ,aAAL,IAAsB,MAAI,CAACA,aAAL,CAAmBE,EAAnB,KAA0BY,OAApD,EAA6D;UAC3D,MAAI,CAACd,aAAL,GAAqB,IAArB;QACD;MACF,CAND,CAME,OAAOQ,KAAP,EAAmB;QACnBa,KAAK,CAACb,KAAK,CAACA,KAAN,EAAaA,KAAb,IAAsBA,KAAK,CAACc,OAA5B,IAAuC,wBAAxC,CAAL;MACD;IAX8B;EAYhC;;EAEKoB,eAAe,CAAClB,QAAD,EAAiB;IAAA;;IAAA;MACpC,IAAI,CAAC,MAAI,CAACxB,aAAN,IAAuB,CAAC,MAAI,CAACG,WAAjC,EAA8C;;MAE9C,IAAI;QACF,MAAM,MAAI,CAACrB,UAAL,CAAgB4D,eAAhB,CAAgC,MAAI,CAACvC,WAArC,EAAkD,MAAI,CAACH,aAAL,CAAmBE,EAArE,EAAyEsB,QAAzE,EAAmFjB,SAAnF,EAAN;;QACA,MAAI,CAACN,eAAL,CAAqB,MAAI,CAACD,aAAL,CAAmBE,EAAxC;MACD,CAHD,CAGE,OAAOM,KAAP,EAAmB;QACnBa,KAAK,CAACb,KAAK,CAACA,KAAN,EAAaA,KAAb,IAAsBA,KAAK,CAACc,OAA5B,IAAuC,6BAAxC,CAAL;MACD;IARmC;EASrC;;EAEKqB,gBAAgB,CAACnB,QAAD,EAAiB;IAAA;;IAAA;MACrC,IAAI,CAAC,OAAI,CAACxB,aAAN,IAAuB,CAAC,OAAI,CAACG,WAAjC,EAA8C;;MAE9C,IAAI;QACF,MAAM,OAAI,CAACrB,UAAL,CAAgB6D,gBAAhB,CAAiC,OAAI,CAACxC,WAAtC,EAAmD,OAAI,CAACH,aAAL,CAAmBE,EAAtE,EAA0EsB,QAA1E,EAAoFjB,SAApF,EAAN;;QACA,OAAI,CAACN,eAAL,CAAqB,OAAI,CAACD,aAAL,CAAmBE,EAAxC;MACD,CAHD,CAGE,OAAOM,KAAP,EAAmB;QACnBa,KAAK,CAACb,KAAK,CAACA,KAAN,EAAaA,KAAb,IAAsBA,KAAK,CAACc,OAA5B,IAAuC,8BAAxC,CAAL;MACD;IARoC;EAStC;;EAEDsB,eAAe,CAACC,IAAD,EAAa;IAC1Bf,SAAS,CAACC,SAAV,CAAoBC,SAApB,CAA8Ba,IAA9B;IACA,KAAKZ,UAAL,GAAkBY,IAAlB;IACAX,UAAU,CAAC,MAAM,KAAKD,UAAL,GAAkB,EAAzB,EAA6B,IAA7B,CAAV;EACD;;EAEDa,UAAU,CAACC,UAAD,EAAmB;IAC3B,OAAO,IAAIC,IAAJ,CAASD,UAAT,EAAqBE,cAArB,EAAP;EACD;;EAEDC,YAAY;IACV,KAAKC,OAAL,CAAaC,IAAb;EACD;;EAEDC,iBAAiB,CAACC,KAAD,EAAa;IAC5B,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAArB;IACA,KAAK3B,eAAL,CAAqB3C,UAArB,GAAkCsE,MAAM,CAACC,KAAP,CAAaC,WAAb,EAAlC;EACD,CA9O6B,CAgP9B;;;EACchE,UAAU;IAAA;;IAAA;MACtB,IAAI,CAAC,OAAI,CAACU,WAAV,EAAuB;;MAEvB,IAAI;QACF,OAAI,CAACC,OAAL,GAAe,IAAf;QACA,OAAI,CAACsD,KAAL,GAAa,OAAM,OAAI,CAAC5E,UAAL,CAAgB6E,0BAAhB,CAA2C,OAAI,CAACxD,WAAhD,EAA6DI,SAA7D,EAAN,KAAkF,EAA/F;MACD,CAHD,CAGE,OAAOC,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,wBAAd,EAAwCA,KAAxC;MACD,CALD,SAKU;QACR,OAAI,CAACJ,OAAL,GAAe,KAAf;MACD;IAVqB;EAWvB;;EAEKwD,UAAU;IAAA;;IAAA;MACd,IAAI,CAAC,OAAI,CAAC1C,WAAL,CAAiBC,IAAjB,EAAD,IAA4B,CAAC,OAAI,CAAC0C,eAAL,CAAqB1C,IAArB,EAA7B,IAA4D,CAAC,OAAI,CAAChB,WAAtE,EAAmF;;MAEnF,IAAI,OAAI,CAAC0D,eAAL,CAAqBC,MAArB,GAA8B,CAAlC,EAAqC;QACnCzC,KAAK,CAAC,6CAAD,CAAL;QACA;MACD;;MAED,IAAI;QACF,MAAM,OAAI,CAACvC,UAAL,CAAgB8E,UAAhB,CAA2B,OAAI,CAACzD,WAAhC,EAA6C,OAAI,CAACe,WAAL,CAAiBC,IAAjB,EAA7C,EAAsE,OAAI,CAAC0C,eAAL,CAAqB1C,IAArB,EAAtE,EAAmGZ,SAAnG,EAAN;QACA,OAAI,CAACW,WAAL,GAAmB,EAAnB;QACA,OAAI,CAAC2C,eAAL,GAAuB,EAAvB;;QACA,OAAI,CAACpE,UAAL;MACD,CALD,CAKE,OAAOe,KAAP,EAAmB;QACnBa,KAAK,CAACb,KAAK,CAACA,KAAN,EAAaA,KAAb,IAAsBA,KAAK,CAACc,OAA5B,IAAuC,uBAAxC,CAAL;MACD;IAfa;EAgBf;;EAEDyC,aAAa,CAACC,IAAD,EAAuB;IAClC,KAAKC,YAAL,GAAoBD,IAApB;IACA,KAAKE,WAAL,GAAmB,EAAnB;IACA,KAAKC,aAAL,GAAqB,EAArB;IACA,KAAKC,aAAL,GAAqB,IAArB;EACD;;EAEDC,cAAc;IACZ,KAAKD,aAAL,GAAqB,KAArB;IACA,KAAKH,YAAL,GAAoB,IAApB;IACA,KAAKC,WAAL,GAAmB,EAAnB;IACA,KAAKC,aAAL,GAAqB,EAArB;EACD;;EAEKG,eAAe;IAAA;;IAAA;MACnB,IAAI,CAAC,OAAI,CAACL,YAAN,IAAsB,CAAC,OAAI,CAACC,WAAL,CAAiB/C,IAAjB,EAAvB,IAAkD,CAAC,OAAI,CAAChB,WAA5D,EAAyE;;MAEzE,IAAI,OAAI,CAAC+D,WAAL,CAAiBJ,MAAjB,GAA0B,CAA9B,EAAiC;QAC/BzC,KAAK,CAAC,6CAAD,CAAL;QACA;MACD;;MAED,IAAI;QACF,MAAM,OAAI,CAACvC,UAAL,CAAgBwF,eAAhB,CAAgC,OAAI,CAACnE,WAArC,EAAkD,OAAI,CAAC8D,YAAL,CAAkBzC,QAApE,EAA8E,OAAI,CAAC0C,WAAL,CAAiB/C,IAAjB,EAA9E,EAAuGZ,SAAvG,EAAN;QACAc,KAAK,CAAC,2BAAD,CAAL;;QACA,OAAI,CAACgD,cAAL;;QACA,OAAI,CAAC5E,UAAL;MACD,CALD,CAKE,OAAOe,KAAP,EAAmB;QACnBa,KAAK,CAACb,KAAK,CAACA,KAAN,EAAaA,KAAb,IAAsBA,KAAK,CAACc,OAA5B,IAAuC,wBAAxC,CAAL;MACD;IAfkB;EAgBpB;;EAEKiD,iBAAiB;IAAA;;IAAA;MACrB,IAAI,CAAC,OAAI,CAACN,YAAN,IAAsB,CAAC,OAAI,CAACE,aAAL,CAAmBhD,IAAnB,EAAvB,IAAoD,CAAC,OAAI,CAAChB,WAA9D,EAA2E;;MAE3E,IAAI,OAAI,CAACgE,aAAL,CAAmBL,MAAnB,GAA4B,CAAhC,EAAmC;QACjCzC,KAAK,CAAC,6CAAD,CAAL;QACA;MACD;;MAED,IAAI;QACF,MAAM,OAAI,CAACvC,UAAL,CAAgByF,iBAAhB,CAAkC,OAAI,CAACpE,WAAvC,EAAoD,OAAI,CAAC8D,YAAL,CAAkBzC,QAAtE,EAAgF,OAAI,CAAC2C,aAAL,CAAmBhD,IAAnB,EAAhF,EAA2GZ,SAA3G,EAAN;QACAc,KAAK,CAAC,6BAAD,CAAL;;QACA,OAAI,CAACgD,cAAL;;QACA,OAAI,CAAC5E,UAAL;MACD,CALD,CAKE,OAAOe,KAAP,EAAmB;QACnBa,KAAK,CAACb,KAAK,CAACA,KAAN,EAAaA,KAAb,IAAsBA,KAAK,CAACc,OAA5B,IAAuC,0BAAxC,CAAL;MACD;IAfoB;EAgBtB;;AAhU6B;;;mBAAnB1C,qBAAmBF;AAAA;;;QAAnBE;EAAmB4F;EAAAC;IAAAtE;EAAA;EAAAuE;IAAAvB;EAAA;EAAAwB;EAAAC;EAAAC;EAAAC;IAAA;MCPhCpG;MAAiCA;QAAA,OAASqG,kBAAT;MAAuB,CAAvB;MAC/BrG;MAAwCA;QAAA,OAASC,wBAAT;MAAiC,CAAjC;MACtCD,2CAA4C,CAA5C,EAA4C,KAA5C,EAA4C,CAA5C,EAA4C,CAA5C,EAA4C,UAA5C,EAA4C,CAA5C;MAEiCA;MAAoBA;MACjDA;MAAgBA;MAAWA;MAE7BA;MACEA;QAAA,OAASqG,kBAAT;MAAuB,CAAvB;MAKArG;MAAUA;MAAKA;MAKnBA,6CAA8C,EAA9C,EAA8C,eAA9C,EAA8C,CAA9C;MAEiBA;QAAA;MAAA,GAAkC,qBAAlC,EAAkC;QAAA,OAAwBqG,uBAAxB;MAA2C,CAA7E;MACbrG,oCAA+B,EAA/B,EAA+B,UAA/B,EAA+B,CAA/B;MACsBA;MAAMA;MAE5BA,qCAA8B,EAA9B,EAA8B,UAA9B,EAA8B,CAA9B;MACsBA;MAAOA;MAE7BA,qCAAwB,EAAxB,EAAwB,UAAxB,EAAwB,CAAxB;MACsBA;MAAKA;MAE3BA,qCAAuB,EAAvB,EAAuB,UAAvB,EAAuB,CAAvB;MACsBA;MAAMA;MAK9BA;MA2EAA;MA0GAA;MAqHAA;MAiFFA;MAKJA;;;;MAhZqBA;MAAAA;MAgBTA;MAAAA;MA2EAA;MAAAA;MA0GAA;MAAAA;MAqHAA;MAAAA;MAsFNA;MAAAA", "names": ["EventEmitter", "i0", "$event", "AdminPanelComponent", "constructor", "apiService", "maxUses", "expiresInHours", "customCode", "ngOnInit", "loadData", "ngOnDestroy", "activeTab", "fetchAllowedUsers", "fetchInviteCodes", "fetchGroups", "fetchUsers", "onTabChange", "tabIndex", "tabs", "activeTabIndex", "onGroupSelect", "group", "selectedGroup", "fetchGroupUsers", "id", "currentUser", "loading", "allowedUsers", "getAllowedUsers", "to<PERSON>romise", "error", "console", "inviteCodes", "getInviteCodes", "groups", "getGroups", "groupId", "groupUsers", "getGroupUsers", "addUser<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "newUsername", "trim", "addAllowedUser", "alert", "message", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "username", "removeAllowedUser", "createInviteCode", "result", "newInviteConfig", "code", "navigator", "clipboard", "writeText", "copiedCode", "setTimeout", "deactivateInviteCode", "createGroup", "newGroupName", "newGroupDescription", "undefined", "deleteGroup", "confirm", "grantUserAccess", "revokeUserAccess", "copyToClipboard", "text", "formatDate", "dateString", "Date", "toLocaleString", "onCloseClick", "onClose", "emit", "onCustomCodeInput", "event", "target", "value", "toUpperCase", "users", "getUsersWithPasswordStatus", "createUser", "newUserPassword", "length", "openUserModal", "user", "selected<PERSON>ser", "newPassword", "resetPassword", "showUserModal", "closeUserModal", "setUserPassword", "resetUserPassword", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["R:\\chateye\\FrontendAngular\\src\\app\\components\\admin-panel\\admin-panel.component.ts", "R:\\chateye\\FrontendAngular\\src\\app\\components\\admin-panel\\admin-panel.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';\r\nimport { ApiService, AllowedUser, InviteCode, Group, GroupUser, UserWithPassword } from '../../services/api.service';\r\n\r\n@Component({\r\n  selector: 'app-admin-panel',\r\n  templateUrl: './admin-panel.component.html',\r\n  styleUrls: ['./admin-panel.component.css']\r\n})\r\nexport class AdminPanelComponent implements OnInit, OnDestroy {\r\n  @Input() currentUser: string | null = null;\r\n  @Output() onClose = new EventEmitter<void>();\r\n\r\n  activeTab = 'whitelist';\r\n  activeTabIndex = 0;\r\n  allowedUsers: AllowedUser[] = [];\r\n  inviteCodes: InviteCode[] = [];\r\n  groups: Group[] = [];\r\n  selectedGroup: Group | null = null;\r\n  groupUsers: GroupUser[] = [];\r\n  users: UserWithPassword[] = [];\r\n  newUsername = '';\r\n  newUserPassword = '';\r\n  newGroupName = '';\r\n  newGroupDescription = '';\r\n  newInviteConfig = {\r\n    maxUses: 1,\r\n    expiresInHours: 24,\r\n    customCode: ''\r\n  };\r\n  loading = false;\r\n  copiedCode = '';\r\n  showUserModal = false;\r\n  selectedUser: UserWithPassword | null = null;\r\n  newPassword = '';\r\n  resetPassword = '';\r\n\r\n  constructor(private apiService: ApiService) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loadData();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Cleanup if needed\r\n  }\r\n\r\n  loadData(): void {\r\n    if (this.activeTab === 'whitelist') {\r\n      this.fetchAllowedUsers();\r\n    } else if (this.activeTab === 'invites') {\r\n      this.fetchInviteCodes();\r\n    } else if (this.activeTab === 'groups') {\r\n      this.fetchGroups();\r\n    } else if (this.activeTab === 'users') {\r\n      this.fetchUsers();\r\n    }\r\n  }\r\n\r\n  onTabChange(tabIndex: number): void {\r\n    const tabs = ['whitelist', 'invites', 'groups', 'users'];\r\n    this.activeTab = tabs[tabIndex];\r\n    this.activeTabIndex = tabIndex;\r\n    this.loadData();\r\n  }\r\n\r\n  onGroupSelect(group: Group): void {\r\n    this.selectedGroup = group;\r\n    this.fetchGroupUsers(group.id);\r\n  }\r\n\r\n  private async fetchAllowedUsers(): Promise<void> {\r\n    if (!this.currentUser) return;\r\n    \r\n    try {\r\n      this.loading = true;\r\n      this.allowedUsers = await this.apiService.getAllowedUsers(this.currentUser).toPromise() || [];\r\n    } catch (error) {\r\n      console.error('Failed to fetch allowed users:', error);\r\n    } finally {\r\n      this.loading = false;\r\n    }\r\n  }\r\n\r\n  private async fetchInviteCodes(): Promise<void> {\r\n    if (!this.currentUser) return;\r\n    \r\n    try {\r\n      this.loading = true;\r\n      this.inviteCodes = await this.apiService.getInviteCodes(this.currentUser).toPromise() || [];\r\n    } catch (error) {\r\n      console.error('Failed to fetch invite codes:', error);\r\n    } finally {\r\n      this.loading = false;\r\n    }\r\n  }\r\n\r\n  private async fetchGroups(): Promise<void> {\r\n    if (!this.currentUser) return;\r\n    \r\n    try {\r\n      this.loading = true;\r\n      this.groups = await this.apiService.getGroups(this.currentUser).toPromise() || [];\r\n    } catch (error) {\r\n      console.error('Failed to fetch groups:', error);\r\n    } finally {\r\n      this.loading = false;\r\n    }\r\n  }\r\n\r\n  private async fetchGroupUsers(groupId: string): Promise<void> {\r\n    if (!this.currentUser) return;\r\n    \r\n    try {\r\n      this.groupUsers = await this.apiService.getGroupUsers(this.currentUser, groupId).toPromise() || [];\r\n    } catch (error) {\r\n      console.error('Failed to fetch group users:', error);\r\n    }\r\n  }\r\n\r\n  async addUserToWhitelist(): Promise<void> {\r\n    if (!this.newUsername.trim() || !this.currentUser) return;\r\n    \r\n    try {\r\n      await this.apiService.addAllowedUser(this.currentUser, this.newUsername.trim()).toPromise();\r\n      this.newUsername = '';\r\n      this.fetchAllowedUsers();\r\n    } catch (error: any) {\r\n      alert(error.error?.error || error.message || 'Failed to add user');\r\n    }\r\n  }\r\n\r\n  async removeUserFromWhitelist(username: string): Promise<void> {\r\n    if (!this.currentUser) return;\r\n    \r\n    try {\r\n      await this.apiService.removeAllowedUser(this.currentUser, username).toPromise();\r\n      this.fetchAllowedUsers();\r\n    } catch (error: any) {\r\n      alert(error.error?.error || error.message || 'Failed to remove user');\r\n    }\r\n  }\r\n\r\n  async createInviteCode(): Promise<void> {\r\n    if (!this.currentUser) return;\r\n    \r\n    try {\r\n      const result = await this.apiService.createInviteCode(this.currentUser, this.newInviteConfig).toPromise();\r\n      this.newInviteConfig = {\r\n        maxUses: 1,\r\n        expiresInHours: 24,\r\n        customCode: ''\r\n      };\r\n      this.fetchInviteCodes();\r\n      \r\n      // Auto-copy the new code\r\n      if (result?.code) {\r\n        navigator.clipboard.writeText(result.code);\r\n        this.copiedCode = result.code;\r\n        setTimeout(() => this.copiedCode = '', 3000);\r\n      }\r\n    } catch (error: any) {\r\n      alert(error.error?.error || error.message || 'Failed to create invite code');\r\n    }\r\n  }\r\n\r\n  async deactivateInviteCode(code: string): Promise<void> {\r\n    if (!this.currentUser) return;\r\n    \r\n    try {\r\n      await this.apiService.deactivateInviteCode(this.currentUser, code).toPromise();\r\n      this.fetchInviteCodes();\r\n    } catch (error: any) {\r\n      alert(error.error?.error || error.message || 'Failed to deactivate invite code');\r\n    }\r\n  }\r\n\r\n  async createGroup(): Promise<void> {\r\n    if (!this.newGroupName.trim() || !this.currentUser) return;\r\n    \r\n    try {\r\n      await this.apiService.createGroup(\r\n        this.currentUser, \r\n        this.newGroupName.trim(), \r\n        this.newGroupDescription.trim() || undefined\r\n      ).toPromise();\r\n      this.newGroupName = '';\r\n      this.newGroupDescription = '';\r\n      this.fetchGroups();\r\n    } catch (error: any) {\r\n      alert(error.error?.error || error.message || 'Failed to create group');\r\n    }\r\n  }\r\n\r\n  async deleteGroup(groupId: string): Promise<void> {\r\n    if (!confirm('Are you sure you want to delete this group?') || !this.currentUser) return;\r\n    \r\n    try {\r\n      await this.apiService.deleteGroup(this.currentUser, groupId).toPromise();\r\n      this.fetchGroups();\r\n      if (this.selectedGroup && this.selectedGroup.id === groupId) {\r\n        this.selectedGroup = null;\r\n      }\r\n    } catch (error: any) {\r\n      alert(error.error?.error || error.message || 'Failed to delete group');\r\n    }\r\n  }\r\n\r\n  async grantUserAccess(username: string): Promise<void> {\r\n    if (!this.selectedGroup || !this.currentUser) return;\r\n    \r\n    try {\r\n      await this.apiService.grantUserAccess(this.currentUser, this.selectedGroup.id, username).toPromise();\r\n      this.fetchGroupUsers(this.selectedGroup.id);\r\n    } catch (error: any) {\r\n      alert(error.error?.error || error.message || 'Failed to grant user access');\r\n    }\r\n  }\r\n\r\n  async revokeUserAccess(username: string): Promise<void> {\r\n    if (!this.selectedGroup || !this.currentUser) return;\r\n    \r\n    try {\r\n      await this.apiService.revokeUserAccess(this.currentUser, this.selectedGroup.id, username).toPromise();\r\n      this.fetchGroupUsers(this.selectedGroup.id);\r\n    } catch (error: any) {\r\n      alert(error.error?.error || error.message || 'Failed to revoke user access');\r\n    }\r\n  }\r\n\r\n  copyToClipboard(text: string): void {\r\n    navigator.clipboard.writeText(text);\r\n    this.copiedCode = text;\r\n    setTimeout(() => this.copiedCode = '', 3000);\r\n  }\r\n\r\n  formatDate(dateString: string): string {\r\n    return new Date(dateString).toLocaleString();\r\n  }\r\n\r\n  onCloseClick(): void {\r\n    this.onClose.emit();\r\n  }\r\n\r\n  onCustomCodeInput(event: Event): void {\r\n    const target = event.target as HTMLInputElement;\r\n    this.newInviteConfig.customCode = target.value.toUpperCase();\r\n  }\r\n\r\n  // User management methods\r\n  private async fetchUsers(): Promise<void> {\r\n    if (!this.currentUser) return;\r\n    \r\n    try {\r\n      this.loading = true;\r\n      this.users = await this.apiService.getUsersWithPasswordStatus(this.currentUser).toPromise() || [];\r\n    } catch (error) {\r\n      console.error('Failed to fetch users:', error);\r\n    } finally {\r\n      this.loading = false;\r\n    }\r\n  }\r\n\r\n  async createUser(): Promise<void> {\r\n    if (!this.newUsername.trim() || !this.newUserPassword.trim() || !this.currentUser) return;\r\n    \r\n    if (this.newUserPassword.length < 6) {\r\n      alert('Password must be at least 6 characters long');\r\n      return;\r\n    }\r\n    \r\n    try {\r\n      await this.apiService.createUser(this.currentUser, this.newUsername.trim(), this.newUserPassword.trim()).toPromise();\r\n      this.newUsername = '';\r\n      this.newUserPassword = '';\r\n      this.fetchUsers();\r\n    } catch (error: any) {\r\n      alert(error.error?.error || error.message || 'Failed to create user');\r\n    }\r\n  }\r\n\r\n  openUserModal(user: UserWithPassword): void {\r\n    this.selectedUser = user;\r\n    this.newPassword = '';\r\n    this.resetPassword = '';\r\n    this.showUserModal = true;\r\n  }\r\n\r\n  closeUserModal(): void {\r\n    this.showUserModal = false;\r\n    this.selectedUser = null;\r\n    this.newPassword = '';\r\n    this.resetPassword = '';\r\n  }\r\n\r\n  async setUserPassword(): Promise<void> {\r\n    if (!this.selectedUser || !this.newPassword.trim() || !this.currentUser) return;\r\n    \r\n    if (this.newPassword.length < 6) {\r\n      alert('Password must be at least 6 characters long');\r\n      return;\r\n    }\r\n    \r\n    try {\r\n      await this.apiService.setUserPassword(this.currentUser, this.selectedUser.username, this.newPassword.trim()).toPromise();\r\n      alert('Password set successfully');\r\n      this.closeUserModal();\r\n      this.fetchUsers();\r\n    } catch (error: any) {\r\n      alert(error.error?.error || error.message || 'Failed to set password');\r\n    }\r\n  }\r\n\r\n  async resetUserPassword(): Promise<void> {\r\n    if (!this.selectedUser || !this.resetPassword.trim() || !this.currentUser) return;\r\n    \r\n    if (this.resetPassword.length < 6) {\r\n      alert('Password must be at least 6 characters long');\r\n      return;\r\n    }\r\n    \r\n    try {\r\n      await this.apiService.resetUserPassword(this.currentUser, this.selectedUser.username, this.resetPassword.trim()).toPromise();\r\n      alert('Password reset successfully');\r\n      this.closeUserModal();\r\n      this.fetchUsers();\r\n    } catch (error: any) {\r\n      alert(error.error?.error || error.message || 'Failed to reset password');\r\n    }\r\n  }\r\n}\r\n", "<!-- Material Design Admin Panel Modal -->\r\n<div class=\"admin-panel-overlay\" (click)=\"onCloseClick()\">\r\n  <mat-card class=\"admin-panel-container\" (click)=\"$event.stopPropagation()\">\r\n    <mat-card-header class=\"admin-panel-header\">\r\n      <div class=\"header-content\">\r\n        <mat-icon class=\"admin-icon\">admin_panel_settings</mat-icon>\r\n        <mat-card-title>Admin Panel</mat-card-title>\r\n      </div>\r\n      <button\r\n        (click)=\"onCloseClick()\"\r\n        mat-icon-button\r\n        matTooltip=\"Close\"\r\n        class=\"close-button\"\r\n      >\r\n        <mat-icon>close</mat-icon>\r\n      </button>\r\n    </mat-card-header>\r\n\r\n    <!-- Content -->\r\n    <mat-card-content class=\"admin-panel-content\">\r\n      <!-- Material Design Tabs -->\r\n      <mat-tab-group [(selectedIndex)]=\"activeTabIndex\" (selectedIndexChange)=\"onTabChange($event)\">\r\n        <mat-tab label=\"Allowed Users\">\r\n          <mat-icon matPrefix>people</mat-icon>\r\n        </mat-tab>\r\n        <mat-tab label=\"Invite Codes\">\r\n          <mat-icon matPrefix>vpn_key</mat-icon>\r\n        </mat-tab>\r\n        <mat-tab label=\"Groups\">\r\n          <mat-icon matPrefix>group</mat-icon>\r\n        </mat-tab>\r\n        <mat-tab label=\"Users\">\r\n          <mat-icon matPrefix>person</mat-icon>\r\n        </mat-tab>\r\n      </mat-tab-group>\r\n\r\n      <!-- Whitelist Tab -->\r\n      <div *ngIf=\"activeTab === 'whitelist'\" class=\"tab-content\">\r\n        <!-- Add User Form -->\r\n        <mat-card class=\"form-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Add User to Whitelist</mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div class=\"form-row\">\r\n              <mat-form-field appearance=\"outline\" class=\"username-field\">\r\n                <mat-label>Username</mat-label>\r\n                <input\r\n                  matInput\r\n                  type=\"text\"\r\n                  name=\"newUsername\"\r\n                  [(ngModel)]=\"newUsername\"\r\n                  placeholder=\"Enter username\"\r\n                />\r\n              </mat-form-field>\r\n              <button\r\n                (click)=\"addUserToWhitelist()\"\r\n                [disabled]=\"!newUsername.trim()\"\r\n                mat-raised-button\r\n                color=\"primary\"\r\n                class=\"add-button\"\r\n              >\r\n                <mat-icon>add</mat-icon>\r\n                Add User\r\n              </button>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n\r\n        <!-- Users List -->\r\n        <mat-card class=\"list-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Allowed Users ({{ allowedUsers.length }})</mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div *ngIf=\"loading\" class=\"loading-state\">\r\n              <mat-spinner diameter=\"40\"></mat-spinner>\r\n              <p>Loading...</p>\r\n            </div>\r\n            <div *ngIf=\"!loading && allowedUsers.length === 0\" class=\"empty-state\">\r\n              <mat-icon>people_outline</mat-icon>\r\n              <p>No users in whitelist</p>\r\n            </div>\r\n            <div *ngIf=\"!loading && allowedUsers.length > 0\" class=\"user-list\">\r\n              <div *ngFor=\"let user of allowedUsers\" class=\"user-item\">\r\n                <div class=\"user-info\">\r\n                  <span class=\"user-name\">{{ user.username }}</span>\r\n                  <span class=\"user-details\">\r\n                    Added by {{ user.added_by }} on {{ formatDate(user.added_at) }}\r\n                  </span>\r\n                </div>\r\n                <div class=\"user-actions\">\r\n                  <mat-chip [class]=\"user.is_active ? 'active-chip' : 'inactive-chip'\">\r\n                    {{ user.is_active ? 'Active' : 'Inactive' }}\r\n                  </mat-chip>\r\n                  <button\r\n                    *ngIf=\"user.is_active\"\r\n                    (click)=\"removeUserFromWhitelist(user.username)\"\r\n                    mat-icon-button\r\n                    matTooltip=\"Remove User\"\r\n                    class=\"remove-button\"\r\n                  >\r\n                    <mat-icon>delete</mat-icon>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n      </div>\r\n\r\n      <!-- Invite Codes Tab -->\r\n      <div *ngIf=\"activeTab === 'invites'\" class=\"tab-content\">\r\n        <!-- Create Invite Code Form -->\r\n        <mat-card class=\"form-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Create Invite Code</mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div class=\"form-row\">\r\n              <mat-form-field appearance=\"outline\" class=\"field\">\r\n                <mat-label>Max Uses</mat-label>\r\n                <input\r\n                  matInput\r\n                  type=\"number\"\r\n                  [(ngModel)]=\"newInviteConfig.maxUses\"\r\n                  min=\"1\"\r\n                  placeholder=\"1\"\r\n                />\r\n              </mat-form-field>\r\n              <mat-form-field appearance=\"outline\" class=\"field\">\r\n                <mat-label>Expires In (Hours)</mat-label>\r\n                <input\r\n                  matInput\r\n                  type=\"number\"\r\n                  [(ngModel)]=\"newInviteConfig.expiresInHours\"\r\n                  min=\"1\"\r\n                  placeholder=\"24\"\r\n                />\r\n              </mat-form-field>\r\n              <mat-form-field appearance=\"outline\" class=\"field\">\r\n                <mat-label>Custom Code (Optional)</mat-label>\r\n                <input\r\n                  matInput\r\n                  type=\"text\"\r\n                  [(ngModel)]=\"newInviteConfig.customCode\"\r\n                  (input)=\"onCustomCodeInput($event)\"\r\n                  placeholder=\"Leave empty for auto-generated\"\r\n                />\r\n              </mat-form-field>\r\n              <button\r\n                (click)=\"createInviteCode()\"\r\n                mat-raised-button\r\n                color=\"primary\"\r\n                class=\"add-button\"\r\n              >\r\n                <mat-icon>add</mat-icon>\r\n                Create Code\r\n              </button>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n\r\n        <!-- Invite Codes List -->\r\n        <mat-card class=\"list-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Invite Codes ({{ inviteCodes.length }})</mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div *ngIf=\"loading\" class=\"loading-state\">\r\n              <mat-spinner diameter=\"40\"></mat-spinner>\r\n              <p>Loading...</p>\r\n            </div>\r\n            <div *ngIf=\"!loading && inviteCodes.length === 0\" class=\"empty-state\">\r\n              <mat-icon>vpn_key</mat-icon>\r\n              <p>No invite codes</p>\r\n            </div>\r\n            <div *ngIf=\"!loading && inviteCodes.length > 0\" class=\"invite-list\">\r\n              <div *ngFor=\"let code of inviteCodes\" class=\"invite-item\">\r\n                <div class=\"invite-info\">\r\n                  <span class=\"invite-code\">{{ code.code }}</span>\r\n                  <span class=\"invite-details\">\r\n                    Uses: {{ code.current_uses }}/{{ code.max_uses }} | \r\n                    Created by {{ code.created_by }} on {{ formatDate(code.created_at) }}\r\n                  </span>\r\n                  <span *ngIf=\"code.expires_at\" class=\"invite-expiry\">\r\n                    Expires: {{ formatDate(code.expires_at) }}\r\n                  </span>\r\n                </div>\r\n                <div class=\"invite-actions\">\r\n                  <mat-chip [class]=\"code.status === 'active' ? 'active-chip' : 'inactive-chip'\">\r\n                    {{ code.status }}\r\n                  </mat-chip>\r\n                  <button\r\n                    (click)=\"copyToClipboard(code.code)\"\r\n                    mat-icon-button\r\n                    matTooltip=\"Copy Code\"\r\n                    class=\"copy-button\"\r\n                  >\r\n                    <mat-icon>{{ copiedCode === code.code ? 'check' : 'content_copy' }}</mat-icon>\r\n                  </button>\r\n                  <button\r\n                    *ngIf=\"code.status === 'active'\"\r\n                    (click)=\"deactivateInviteCode(code.code)\"\r\n                    mat-icon-button\r\n                    matTooltip=\"Deactivate\"\r\n                    class=\"remove-button\"\r\n                  >\r\n                    <mat-icon>block</mat-icon>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n      </div>\r\n\r\n      <!-- Groups Tab -->\r\n      <div *ngIf=\"activeTab === 'groups'\" class=\"tab-content\">\r\n        <!-- Create Group Form -->\r\n        <mat-card class=\"form-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Create Group</mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div class=\"form-row\">\r\n              <mat-form-field appearance=\"outline\" class=\"field\">\r\n                <mat-label>Group Name</mat-label>\r\n                <input\r\n                  matInput\r\n                  type=\"text\"\r\n                  [(ngModel)]=\"newGroupName\"\r\n                  placeholder=\"Enter group name\"\r\n                />\r\n              </mat-form-field>\r\n              <mat-form-field appearance=\"outline\" class=\"field\">\r\n                <mat-label>Description (Optional)</mat-label>\r\n                <input\r\n                  matInput\r\n                  type=\"text\"\r\n                  [(ngModel)]=\"newGroupDescription\"\r\n                  placeholder=\"Enter group description\"\r\n                />\r\n              </mat-form-field>\r\n              <button\r\n                (click)=\"createGroup()\"\r\n                [disabled]=\"!newGroupName.trim()\"\r\n                mat-raised-button\r\n                color=\"primary\"\r\n                class=\"add-button\"\r\n              >\r\n                <mat-icon>add</mat-icon>\r\n                Create Group\r\n              </button>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n\r\n        <!-- Groups List -->\r\n        <mat-card class=\"list-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Groups ({{ groups.length }})</mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div *ngIf=\"loading\" class=\"loading-state\">\r\n              <mat-spinner diameter=\"40\"></mat-spinner>\r\n              <p>Loading...</p>\r\n            </div>\r\n            <div *ngIf=\"!loading && groups.length === 0\" class=\"empty-state\">\r\n              <mat-icon>group</mat-icon>\r\n              <p>No groups</p>\r\n            </div>\r\n            <div *ngIf=\"!loading && groups.length > 0\" class=\"group-list\">\r\n              <div *ngFor=\"let group of groups\" class=\"group-item\" [class.selected]=\"selectedGroup?.id === group.id\">\r\n                <div class=\"group-info\" (click)=\"onGroupSelect(group)\">\r\n                  <span class=\"group-name\">{{ group.name }}</span>\r\n                  <span *ngIf=\"group.description\" class=\"group-description\">{{ group.description }}</span>\r\n                  <span class=\"group-details\">\r\n                    Created by {{ group.created_by_username || 'Unknown' }}\r\n                  </span>\r\n                </div>\r\n                <div class=\"group-actions\">\r\n                  <button\r\n                    (click)=\"deleteGroup(group.id)\"\r\n                    mat-icon-button\r\n                    matTooltip=\"Delete Group\"\r\n                    class=\"remove-button\"\r\n                  >\r\n                    <mat-icon>delete</mat-icon>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n\r\n        <!-- Group Users Management -->\r\n        <mat-card *ngIf=\"selectedGroup\" class=\"list-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Group Users - {{ selectedGroup.name }}</mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div *ngIf=\"loading\" class=\"loading-state\">\r\n              <mat-spinner diameter=\"40\"></mat-spinner>\r\n              <p>Loading...</p>\r\n            </div>\r\n            <div *ngIf=\"!loading && groupUsers.length === 0\" class=\"empty-state\">\r\n              <mat-icon>person_outline</mat-icon>\r\n              <p>No users in this group</p>\r\n            </div>\r\n            <div *ngIf=\"!loading && groupUsers.length > 0\" class=\"user-list\">\r\n              <div *ngFor=\"let user of groupUsers\" class=\"user-item\">\r\n                <div class=\"user-info\">\r\n                  <span class=\"user-name\">{{ user.username }}</span>\r\n                  <span class=\"user-details\">\r\n                    Granted access on {{ formatDate(user.granted_at) }}\r\n                  </span>\r\n                </div>\r\n                <div class=\"user-actions\">\r\n                  <button\r\n                    (click)=\"revokeUserAccess(user.username)\"\r\n                    mat-icon-button\r\n                    matTooltip=\"Revoke Access\"\r\n                    class=\"remove-button\"\r\n                  >\r\n                    <mat-icon>remove_circle</mat-icon>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n      </div>\r\n\r\n      <!-- Users Tab -->\r\n      <div *ngIf=\"activeTab === 'users'\" class=\"tab-content\">\r\n        <!-- Create User Form -->\r\n        <mat-card class=\"form-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Create User</mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div class=\"form-row\">\r\n              <mat-form-field appearance=\"outline\" class=\"field\">\r\n                <mat-label>Username</mat-label>\r\n                <input\r\n                  matInput\r\n                  type=\"text\"\r\n                  [(ngModel)]=\"newUsername\"\r\n                  placeholder=\"Enter username\"\r\n                />\r\n              </mat-form-field>\r\n              <mat-form-field appearance=\"outline\" class=\"field\">\r\n                <mat-label>Password</mat-label>\r\n                <input\r\n                  matInput\r\n                  type=\"password\"\r\n                  [(ngModel)]=\"newUserPassword\"\r\n                  placeholder=\"Enter password\"\r\n                />\r\n              </mat-form-field>\r\n              <button\r\n                (click)=\"createUser()\"\r\n                [disabled]=\"!newUsername.trim() || !newUserPassword.trim()\"\r\n                mat-raised-button\r\n                color=\"primary\"\r\n                class=\"add-button\"\r\n              >\r\n                <mat-icon>add</mat-icon>\r\n                Create User\r\n              </button>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n\r\n        <!-- Users List -->\r\n        <mat-card class=\"list-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Users ({{ users.length }})</mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <div *ngIf=\"loading\" class=\"loading-state\">\r\n              <mat-spinner diameter=\"40\"></mat-spinner>\r\n              <p>Loading...</p>\r\n            </div>\r\n            <div *ngIf=\"!loading && users.length === 0\" class=\"empty-state\">\r\n              <mat-icon>person</mat-icon>\r\n              <p>No users</p>\r\n            </div>\r\n            <div *ngIf=\"!loading && users.length > 0\" class=\"user-list\">\r\n              <div *ngFor=\"let user of users\" class=\"user-item\">\r\n                <div class=\"user-info\">\r\n                  <span class=\"user-name\">{{ user.username }}</span>\r\n                  <span class=\"user-details\">\r\n                    Last seen: {{ formatDate(user.last_seen) }} | \r\n                    Status: {{ user.online_status ? 'Online' : 'Offline' }}\r\n                  </span>\r\n                </div>\r\n                <div class=\"user-actions\">\r\n                  <mat-chip [class]=\"user.hasPassword ? 'active-chip' : 'inactive-chip'\">\r\n                    {{ user.hasPassword ? 'Has Password' : 'No Password' }}\r\n                  </mat-chip>\r\n                  <button\r\n                    (click)=\"openUserModal(user)\"\r\n                    mat-icon-button\r\n                    matTooltip=\"Manage User\"\r\n                    class=\"manage-button\"\r\n                  >\r\n                    <mat-icon>settings</mat-icon>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n      </div>\r\n    </mat-card-content>\r\n  </mat-card>\r\n</div>\r\n\r\n<!-- User Management Modal -->\r\n<div *ngIf=\"showUserModal\" class=\"modal-overlay\" (click)=\"closeUserModal()\">\r\n  <mat-card class=\"modal-container\" (click)=\"$event.stopPropagation()\">\r\n    <mat-card-header class=\"modal-header\">\r\n      <div class=\"header-content\">\r\n        <mat-icon class=\"modal-icon\">person</mat-icon>\r\n        <mat-card-title>Manage User: {{ selectedUser?.username }}</mat-card-title>\r\n      </div>\r\n      <button\r\n        (click)=\"closeUserModal()\"\r\n        mat-icon-button\r\n        matTooltip=\"Close\"\r\n        class=\"close-button\"\r\n      >\r\n        <mat-icon>close</mat-icon>\r\n      </button>\r\n    </mat-card-header>\r\n\r\n    <mat-card-content class=\"modal-content\">\r\n      <div class=\"user-info-section\">\r\n        <div class=\"info-item\">\r\n          <span class=\"info-label\">Username:</span>\r\n          <span class=\"info-value\">{{ selectedUser?.username }}</span>\r\n        </div>\r\n        <div class=\"info-item\">\r\n          <span class=\"info-label\">Status:</span>\r\n          <span class=\"info-value\">{{ selectedUser?.online_status ? 'Online' : 'Offline' }}</span>\r\n        </div>\r\n        <div class=\"info-item\">\r\n          <span class=\"info-label\">Last Seen:</span>\r\n          <span class=\"info-value\">{{ formatDate(selectedUser?.last_seen || '') }}</span>\r\n        </div>\r\n        <div class=\"info-item\">\r\n          <span class=\"info-label\">Password Status:</span>\r\n          <mat-chip [class]=\"selectedUser?.hasPassword ? 'active-chip' : 'inactive-chip'\">\r\n            {{ selectedUser?.hasPassword ? 'Has Password' : 'No Password' }}\r\n          </mat-chip>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"password-section\">\r\n        <mat-card class=\"password-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Set Password</mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n              <mat-label>New Password</mat-label>\r\n              <input\r\n                matInput\r\n                type=\"password\"\r\n                [(ngModel)]=\"newPassword\"\r\n                placeholder=\"Enter new password\"\r\n              />\r\n            </mat-form-field>\r\n            <button\r\n              (click)=\"setUserPassword()\"\r\n              [disabled]=\"!newPassword.trim()\"\r\n              mat-raised-button\r\n              color=\"primary\"\r\n              class=\"full-width\"\r\n            >\r\n              <mat-icon>lock</mat-icon>\r\n              Set Password\r\n            </button>\r\n          </mat-card-content>\r\n        </mat-card>\r\n\r\n        <mat-card class=\"password-card\">\r\n          <mat-card-header>\r\n            <mat-card-title>Reset Password</mat-card-title>\r\n          </mat-card-header>\r\n          <mat-card-content>\r\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n              <mat-label>New Password</mat-label>\r\n              <input\r\n                matInput\r\n                type=\"password\"\r\n                [(ngModel)]=\"resetPassword\"\r\n                placeholder=\"Enter new password\"\r\n              />\r\n            </mat-form-field>\r\n            <button\r\n              (click)=\"resetUserPassword()\"\r\n              [disabled]=\"!resetPassword.trim()\"\r\n              mat-raised-button\r\n              color=\"warn\"\r\n              class=\"full-width\"\r\n            >\r\n              <mat-icon>refresh</mat-icon>\r\n              Reset Password\r\n            </button>\r\n          </mat-card-content>\r\n        </mat-card>\r\n      </div>\r\n    </mat-card-content>\r\n  </mat-card>\r\n</div>"]}, "metadata": {}, "sourceType": "module"}